package com.gtech.gvcore.web.giftcard;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.core.task.SyncTaskExecutor;

import java.util.concurrent.Executor;

@TestConfiguration
public class SyncTaskExecutorConfig {
    @Bean
    public Executor asyncExecutor() {
        // 使用同步执行器替代异步线程池
        return new SyncTaskExecutor();
    }
}
