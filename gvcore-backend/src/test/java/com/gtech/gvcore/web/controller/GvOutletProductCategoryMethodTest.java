package com.gtech.gvcore.web.controller;

import com.gtech.gvcore.common.request.outletproductcategory.*;
import com.gtech.gvcore.service.OutletProductCategoryService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 * @Date 2022/2/22 18:05
 */
@RunWith(MockitoJUnitRunner.class)
public class GvOutletProductCategoryMethodTest {



    @InjectMocks
    private GvOutletProductCategoryMethodController gvOutletProductCategoryController;

    @Mock
    private OutletProductCategoryService outletService;


    @Test
    public void createOutletProductCategoryMethod(){

        CreateOutletProductCategoryRequest request = new CreateOutletProductCategoryRequest();
        request.setProductCategoryCode("B000A70B9I");
        request.setCreateUser("1");
        request.setOutletCode("123");
        gvOutletProductCategoryController.createOutletProductCategory(request);

    }

    @Test
    public void updateOutletProductCategory(){

        UpdateOutletProductCategoryRequest request = new UpdateOutletProductCategoryRequest();
        request.setOutletProductCategoryCode("2");
        request.setProductCategoryCode("1");
        request.setStatus(0);
        request.setUpdateUser("1");
        gvOutletProductCategoryController.updateOutletProductCategory(request);

    }


    @Test
    public void deleteOutletProductCategory(){
        DeleteOutletProductCategoryRequest request = new DeleteOutletProductCategoryRequest();
        request.setOutletProductCategoryCode("1");
        gvOutletProductCategoryController.deleteOutletProductCategory(request);
    }


    @Test
    public void queryOutletProductCategoryList(){
        QueryOutletProductCategoryRequest request = new QueryOutletProductCategoryRequest();
        request.setOutletProductCategoryCode("");
        request.setStatus(0);
        request.setPageSize(0);
        request.setPageNum(0);

        gvOutletProductCategoryController.queryOutletProductCategoryList(request);


    }


    @Test
    public void getOutletProductCategory(){

        GetOutletProductCategoryRequest request = new GetOutletProductCategoryRequest();
        request.setOutletProductCategoryCode("1");

        gvOutletProductCategoryController.getOutletProductCategory(request);

    }






}
