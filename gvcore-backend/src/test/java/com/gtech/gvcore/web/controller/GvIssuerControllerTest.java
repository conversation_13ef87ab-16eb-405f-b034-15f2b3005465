package com.gtech.gvcore.web.controller;

import com.gtech.gvcore.common.request.issuer.*;
import com.gtech.gvcore.service.IssuerService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 * @Date 2022/2/22 18:05
 */
@RunWith(MockitoJUnitRunner.class)
public class GvIssuerControllerTest {



    @InjectMocks
    private GvIssuerController gvIssuerController;

    @Mock
    private IssuerService outletService;


    @Test
    public void createIssuer(){

        CreateIssuerRequest request = new CreateIssuerRequest();
        request.setIssuerName("Erwin Cummings");
        request.setCreateUser("1");
        gvIssuerController.createIssuer(request);

    }

    @Test
    public void updateIssuer(){

        UpdateIssuerRequest request = new UpdateIssuerRequest();
        request.setIssuerCode("2");
        request.setIssuerName("1");
        request.setStatus(0);
        request.setUpdateUser("1");
        gvIssuerController.updateIssuer(request);

    }

    @Test
    public void updateIssuerStatus(){

        UpdateIssuerStatusRequest request = new UpdateIssuerStatusRequest();
        request.setIssuerCode("2");
        request.setStatus(0);
        request.setUpdateUser("1");
        gvIssuerController.updateIssuerStatus(request);

    }


    @Test
    public void deleteIssuer(){
        DeleteIssuerRequest request = new DeleteIssuerRequest();
        request.setIssuerCode("1");
        gvIssuerController.deleteIssuer(request);
    }


    @Test
    public void queryIssuerList(){
        QueryIssuerRequest request = new QueryIssuerRequest();
        request.setIssuerName("");
        request.setPageSize(0);
        request.setPageNum(0);

        gvIssuerController.queryIssuerList(request);


    }


    @Test
    public void getIssuer(){

        GetIssuerRequest request = new GetIssuerRequest();
        request.setIssuerCode("1");

        gvIssuerController.getIssuer(request);

    }






}
