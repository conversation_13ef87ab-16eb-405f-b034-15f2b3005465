package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.schedulerreport.CreateSchedulerReportRequest;
import com.gtech.gvcore.common.request.schedulerreport.DeleteSchedulerReportRequest;
import com.gtech.gvcore.common.request.schedulerreport.QuerySchedulerReportRequest;
import com.gtech.gvcore.common.request.schedulerreport.UpdateSchedulerReportRequest;
import com.gtech.gvcore.common.request.schedulerreport.UpdateSchedulerReportsStatusRequest;
import com.gtech.gvcore.service.report.base.SchedulerReportServiceImpl;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class GvSchedulerReportControllerTest {

    @InjectMocks
    private GvSchedulerReportController controller;
    @Mock
    private SchedulerReportServiceImpl schedulerReportService;

    @Test
    public void createSchedulerReport() {
        Mockito.when(schedulerReportService.createSchedulerReport(Mockito.any())).thenReturn(new Result<>());
        Assert.assertNotNull(controller.createSchedulerReport(new CreateSchedulerReportRequest()));
    }

    @Test
    public void updateSchedulerReport() {
        Mockito.when(schedulerReportService.updateSchedulerReport(Mockito.any())).thenReturn(new Result<>());
        Assert.assertNotNull(controller.updateSchedulerReport(new UpdateSchedulerReportRequest()));
    }

    @Test
    public void querySchedulerReport() {
        Mockito.when(schedulerReportService.querySchedulerReport(Mockito.any())).thenReturn(new PageResult<>());
        Assert.assertNotNull(controller.querySchedulerReport(new QuerySchedulerReportRequest()));
    }

    @Test
    public void updateStatusSchedulerReport() {
        Mockito.when(schedulerReportService.updateSchedulerReportStatus(Mockito.any())).thenReturn(new Result<>());
        Assert.assertNotNull(controller.updateSchedulerReportStatus(new UpdateSchedulerReportsStatusRequest()));
    }

    @Test
    public void deleteSchedulerReport() {
        Mockito.when(schedulerReportService.deleteSchedulerReport(Mockito.any())).thenReturn(new Result<>());
        Assert.assertNotNull(controller.deleteSchedulerReport(new DeleteSchedulerReportRequest()));
    }
}