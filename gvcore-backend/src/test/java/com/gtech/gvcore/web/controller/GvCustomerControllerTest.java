package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.customer.CheckCustomerInfoRequest;
import com.gtech.gvcore.common.request.customer.CreateCustomerRequest;
import com.gtech.gvcore.common.request.customer.CreateIncompleteCustomerRequest;
import com.gtech.gvcore.common.request.customer.DeleteCustomerRequest;
import com.gtech.gvcore.common.request.customer.GetCustomerRequest;
import com.gtech.gvcore.common.request.customer.GetEmailValidateCodeRequest;
import com.gtech.gvcore.common.request.customer.QueryCustomerRequest;
import com.gtech.gvcore.common.request.customer.UpdateCustomerRequest;
import com.gtech.gvcore.common.request.customer.UpdateCustomerStatusRequest;
import com.gtech.gvcore.common.request.customer.ValidateEmailRequest;
import com.gtech.gvcore.service.CustomerService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 * @Date 2022/2/22 18:05
 */
@RunWith(MockitoJUnitRunner.class)
public class GvCustomerControllerTest {


    @InjectMocks
    private GvCustomerController gvCustomerController;

    @Mock
    private CustomerService outletService;


    @Test
    public void createCustomer() {

        CreateCustomerRequest request = new CreateCustomerRequest();
        request.setCustomerName("1");
        request.setCustomerType("1");
        request.setCompanyName("1");
        request.setContactFirstName("1");
        request.setContactLastName("1");
        request.setContactDivision("1");
        request.setContactPhone("1");
        request.setContactEmail("1");
        request.setShippingAddress1("1");
        request.setShippingAddress2("1");
        request.setTransferAccount("1");
        request.setBankCardIssuer("1");
        request.setNote("1");
        request.setCreateUser("1");

        request.setCustomerName("Erwin Cummings");
        request.setCreateUser("1");

        gvCustomerController.createCustomer(request);

    }

    @Test
    public void updateCustomer() {

        UpdateCustomerRequest request = new UpdateCustomerRequest();
        request.setCustomerCode("2");
        request.setCustomerName("1");

        request.setStatus(0);
        request.setUpdateUser("1");
        gvCustomerController.updateCustomer(request);

    }


    @Test
    public void updateCustomerStatus() {

        UpdateCustomerStatusRequest request = new UpdateCustomerStatusRequest();
        request.setCustomerCode("2");
        request.setStatus(0);
        request.setUpdateUser("1");
        gvCustomerController.updateCustomerStatus(request);

    }


    @Test
    public void deleteCustomer() {
        DeleteCustomerRequest request = new DeleteCustomerRequest();
        request.setCustomerCode("1");
        gvCustomerController.deleteCustomer(request);
    }


    @Test
    public void queryCustomerList() {
        QueryCustomerRequest request = new QueryCustomerRequest();

        request.setPageSize(0);
        request.setPageNum(0);

        gvCustomerController.queryCustomerList(request);


    }


    @Test
    public void getCustomer() {

        GetCustomerRequest request = new GetCustomerRequest();
        request.setCustomerCode("1");

        gvCustomerController.getCustomer(request);

    }


    @Test
    public void getEmailValidateCode() {
        Mockito.when(outletService.getEmailValidateCode(Mockito.any(),Mockito.any())).thenReturn(new Result<>());
        Assert.assertNotNull(gvCustomerController.getEmailValidateCode(new GetEmailValidateCodeRequest()));
    }

    @Test
    public void validatedEmail() {
        Mockito.when(outletService.validateEmail(Mockito.any())).thenReturn(new Result<>());
        Assert.assertNotNull(gvCustomerController.validatedEmail(new ValidateEmailRequest()));
    }

    @Test
    public void createIncompleteCustomer() {
        Mockito.when(outletService.createIncompleteCustomer(Mockito.any())).thenReturn(new Result<>());
        Assert.assertNotNull(gvCustomerController.createIncompleteCustomer(new CreateIncompleteCustomerRequest()));
    }

    @Test
    public void checkCustomerInfo() {
        Mockito.when(outletService.checkCustomerInfo(Mockito.any())).thenReturn(new Result<>());
        Assert.assertNotNull(gvCustomerController.checkCustomerInfo(new CheckCustomerInfoRequest()));
    }

    @Test
    public void queryCustomerByEmail() {
        Mockito.when(outletService.queryCustomerByEmail(Mockito.any())).thenReturn(new Result<>());
        Assert.assertNotNull(gvCustomerController.queryCustomerByEmail(new GetEmailValidateCodeRequest()));
    }


}
