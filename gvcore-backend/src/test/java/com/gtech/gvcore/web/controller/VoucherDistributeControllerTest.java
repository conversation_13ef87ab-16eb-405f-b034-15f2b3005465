package com.gtech.gvcore.web.controller;

import com.gtech.gvcore.common.request.printer.CreatePrinterRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> Gao.Yuhua
 * @date : 2022-03-08 15:48
 **/


@RunWith(MockitoJUnitRunner.class)
@Slf4j
public class VoucherDistributeControllerTest {


    @Autowired
    private GvPrinterController gvPrinterController;

    @Autowired
    private VoucherDistributeController voucherDistributeController;

    @Test
    public void testDistribution() {
        //新增
        CreatePrinterRequest createPrinterRequest = getCreatePrinterRequest();

        /*Result<Object> result = gvPrinterController.createPrinter(createPrinterRequest);
        log.info("result:{}", result);

        Assert.assertTrue(result.isSuccess());*/


//        CreateVoucherDistributeRequest createVoucherDistributeRequest = new CreateVoucherDistributeRequest();
//
//        createVoucherDistributeRequest.setExcelFileUrl("https://static.gtech.asia/static/template/price.xlsx");
//        createVoucherDistributeRequest.setTargetFileName(System.currentTimeMillis() + ".xlsx");
//        createVoucherDistributeRequest.setPrinterCode(result.getData().toString());

        //Result<Object> voucherDistribute = voucherDistributeController.createVoucherDistribute(createVoucherDistributeRequest);
        //Assert.assertTrue(voucherDistribute.isSuccess());

    }


    private CreatePrinterRequest getCreatePrinterRequest() {

        CreatePrinterRequest createPrinterRequest = new CreatePrinterRequest();

        createPrinterRequest.setPrinterName("PrinterName".concat(System.currentTimeMillis() + ""));
        createPrinterRequest.setStateCode("001");
        //createPrinterRequest.setIssuerCode("MAP");
        createPrinterRequest.setCityCode("002");
        createPrinterRequest.setDistrictCode("003");
        createPrinterRequest.setAddress("Address");
        createPrinterRequest.setLongitude("Longitud");
        createPrinterRequest.setLatitude("latitude");
        createPrinterRequest.setFirstName("FirstName");
        createPrinterRequest.setLastName("LastName");
        createPrinterRequest.setMobile("+62-811109949");
        createPrinterRequest.setEmail("<EMAIL>");
        createPrinterRequest.setFtpAuthorizationType("PASSWORD");
        createPrinterRequest.setFtpUrl("ftp://************:21211/upload/Test");
        createPrinterRequest.setFtpUsername("titan_sta_map");
        createPrinterRequest.setFtpPassword("123titan_sta_map");
        createPrinterRequest.setCreateUser("gv");
        return createPrinterRequest;
    }


}
