package com.gtech.gvcore.web.controller;

import com.gtech.gvcore.common.request.customerproductcategory.*;
import com.gtech.gvcore.service.CustomerProductCategoryService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 * @Date 2022/2/22 18:05
 */
@RunWith(MockitoJUnitRunner.class)
public class GvCustomerProductCategoryControllerTest {



    @InjectMocks
    private GvCustomerProductCategoryController gvCustomerProductCategoryController;

    @Mock
    private CustomerProductCategoryService outletService;


    @Test
    public void createCustomerProductCategory(){

        CreateCustomerProductCategoryRequest request = new CreateCustomerProductCategoryRequest();
        request.setCustomerCode("test");
        request.setProductCategoryCode("test");
        request.setCreateUser("test");


        request.setProductCategoryCode("B000A70B9I");

        request.setCreateUser("1");
        gvCustomerProductCategoryController.createCustomerProductCategory(request);

    }

    @Test
    public void updateCustomerProductCategory(){

        UpdateCustomerProductCategoryRequest request = new UpdateCustomerProductCategoryRequest();
        request.setCustomerProductCategoryCode("test");
        request.setCustomerCode("test");
        request.setProductCategoryCode("test");
        request.setStatus(0);
        request.setUpdateUser("test");


        gvCustomerProductCategoryController.updateCustomerProductCategory(request);

    }


    @Test
    public void deleteCustomerProductCategory(){
        DeleteCustomerProductCategoryRequest request = new DeleteCustomerProductCategoryRequest();
        request.setCustomerProductCategoryCode("1");
        gvCustomerProductCategoryController.deleteCustomerProductCategory(request);
    }


    @Test
    public void queryCustomerProductCategoryList(){
        QueryCustomerProductCategoryRequest request = new QueryCustomerProductCategoryRequest();
        request.setCustomerProductCategoryCode("test");

        request.setStatus(0);
        request.setPageSize(0);
        request.setPageNum(0);

        gvCustomerProductCategoryController.queryCustomerProductCategoryList(request);


    }


    @Test
    public void getCustomerProductCategory(){

        GetCustomerProductCategoryRequest request = new GetCustomerProductCategoryRequest();
        request.setCustomerProductCategoryCode("1");

        gvCustomerProductCategoryController.getCustomerProductCategory(request);

    }






}
