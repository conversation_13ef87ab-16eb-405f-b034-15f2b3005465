package com.gtech.gvcore.web.controller;

import com.gtech.gvcore.common.request.customerorder.PoNumberRequest;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.customerorder.ApproveCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.CancelReleaseRequest;
import com.gtech.gvcore.common.request.customerorder.CreateCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.DelPaymentVoucherRequest;
import com.gtech.gvcore.common.request.customerorder.DeliverCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.GetCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.IssuanceRequest;
import com.gtech.gvcore.common.request.customerorder.NonSystemCreateCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.QueryCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.ReceiveRequest;
import com.gtech.gvcore.common.request.customerorder.ReleaseRequest;
import com.gtech.gvcore.common.request.customerorder.SendCustomerOrderEmailRequest;
import com.gtech.gvcore.common.request.customerorder.SubmitCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.UpdateCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.UploadPaymentVoucherRequest;
import com.gtech.gvcore.common.request.releaseapprove.ApproveNodeRecordRequest;
import com.gtech.gvcore.common.request.releaseapprove.ReleaseApproveAbleRequest;
import com.gtech.gvcore.service.FlowNoticeService;
import com.gtech.gvcore.service.impl.CustomerOrderServiceImpl;
import com.gtech.gvcore.service.impl.MessageComponent;

@RunWith(MockitoJUnitRunner.class)
public class GvCustomerOrderControllerTest {

    @InjectMocks
    private GvCustomerOrderController customerOrderController;
    @Mock
    private MessageComponent messageComponent;
    @Mock
    private CustomerOrderServiceImpl customerOrderService;
	@Mock
	private FlowNoticeService flowNoticeService;

    @Test
    public void createCustomerOrder() {
        CreateCustomerOrderRequest createCustomerOrderRequest = new CreateCustomerOrderRequest();
        createCustomerOrderRequest.setCustomerType("Individual");
        Mockito.when(customerOrderService.createCustomerOrder(createCustomerOrderRequest)).thenReturn(new Result<>());
        Assert.assertNotNull(customerOrderController.createCustomerOrder(createCustomerOrderRequest));
    }

    @Test
    public void deliverCustomerOrder() {
        Assert.assertNotNull(customerOrderController.deliverCustomerOrder(new DeliverCustomerOrderRequest()));
    }

    @Test
    public void queryCustomerOrder() {
        Mockito.when(customerOrderService.queryCustomerOrder(new QueryCustomerOrderRequest())).thenReturn(new PageResult<>());
        Assert.assertNotNull(customerOrderController.queryCustomerOrder(new QueryCustomerOrderRequest()));
    }

    @Test
    public void getCustomerOrder() {
        Mockito.when(customerOrderService.getCustomerOrder(new GetCustomerOrderRequest())).thenReturn(new Result<>());
        Assert.assertNotNull(customerOrderController.getCustomerOrder(new GetCustomerOrderRequest()));
    }

    @Test
    public void updateCustomerOrder() {
        Mockito.when(customerOrderService.updateCustomerOrder(new UpdateCustomerOrderRequest())).thenReturn(new Result<>());
        Assert.assertNotNull(customerOrderController.updateCustomerOrder(new UpdateCustomerOrderRequest()));
    }

    @Test
    public void generatePurchaseOrderNumber() {
        Mockito.when(customerOrderService.generatePurchaseOrderNumber(Mockito.anyString())).thenReturn(new Result<>());
        PoNumberRequest poNumberRequest = new PoNumberRequest();
        poNumberRequest.setPoNumber("123");
        poNumberRequest.setStoreCode("123");
        Assert.assertNotNull(customerOrderController.generatePurchaseOrderNumber(poNumberRequest));
    }

    @Test
    public void submitCustomerOrder() {
        Mockito.when(customerOrderService.submitCustomerOrder(new SubmitCustomerOrderRequest())).thenReturn(new Result<>());
        Assert.assertNotNull(customerOrderController.submitCustomerOrder(new SubmitCustomerOrderRequest()));
    }

    @Test
    public void approveCustomerOrder() {
        Mockito.when(customerOrderService.approveCustomerOrder(new ApproveCustomerOrderRequest())).thenReturn(new Result<>());
        Assert.assertNotNull(customerOrderController.approveCustomerOrder(new ApproveCustomerOrderRequest()));
    }

    @Test
    public void nonSystemCreateCustomerOrder() {

        NonSystemCreateCustomerOrderRequest request = new NonSystemCreateCustomerOrderRequest();
        request.setCustomerType("Individual");
		Mockito.when(customerOrderService.nonSystemCreateCustomerOrder(request)).thenReturn(Result.ok("123"));

        Assert.assertNotNull(customerOrderController.nonSystemCreateCustomerOrder(request));
    }

    @Test
    public void approveCustomerOrderAble() {
        Mockito.when(customerOrderService.approveCustomerOrderAble(new ReleaseApproveAbleRequest())).thenReturn(new Result<>());
        Assert.assertNotNull(customerOrderController.approveCustomerOrderAble(new ReleaseApproveAbleRequest()));
    }

    @Test
    public void issuance() {
        Mockito.when(customerOrderService.issuanceAndNoticeNextNode(new IssuanceRequest())).thenReturn(new Result<>());
        Assert.assertNotNull(customerOrderController.issuance(new IssuanceRequest()));
    }

    @Test
    public void approveCustomerOrderRelease() {
        Mockito.when(customerOrderService.approveCustomerOrderRelease(new ApproveNodeRecordRequest())).thenReturn(new Result<>());
        Assert.assertNotNull(customerOrderController.approveCustomerOrderRelease(new ApproveNodeRecordRequest()));
    }

    @Test
    public void release() {
        Mockito.when(customerOrderService.release(new ReleaseRequest())).thenReturn(new Result<>());
        Assert.assertNotNull(customerOrderController.release(new ReleaseRequest()));
    }

    @Test
    public void receive() {
        Mockito.when(customerOrderService.receive(new ReceiveRequest())).thenReturn(new Result<>());
        Assert.assertNotNull(customerOrderController.receive(new ReceiveRequest()));
    }

    @Test
    public void cancelRelease() {
        Mockito.when(customerOrderService.cancelRelease(new CancelReleaseRequest())).thenReturn(new Result<>());
        Assert.assertNotNull(customerOrderController.cancelRelease(new CancelReleaseRequest()));
    }

    @Test
    public void uploadPaymentVoucher() {
        Mockito.when(customerOrderService.uploadPaymentVoucher(new UploadPaymentVoucherRequest())).thenReturn(new Result<>());
        Assert.assertNotNull(customerOrderController.uploadPaymentVoucher(new UploadPaymentVoucherRequest()));
    }

    @Test
    public void delPaymentVoucher() {
        Mockito.when(customerOrderService.delPaymentVoucher(new DelPaymentVoucherRequest())).thenReturn(new Result<>());
        Assert.assertNotNull(customerOrderController.delPaymentVoucher(new DelPaymentVoucherRequest()));
    }

    @Test
    public void sendEmail() {
        Mockito.when(customerOrderService.sendEmail(new SendCustomerOrderEmailRequest())).thenReturn(new Result<>());
        Assert.assertNotNull(customerOrderController.sendEmail(new SendCustomerOrderEmailRequest()));

    }
}