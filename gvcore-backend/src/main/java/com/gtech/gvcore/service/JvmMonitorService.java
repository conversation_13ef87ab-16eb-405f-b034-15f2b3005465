package com.gtech.gvcore.service;

import com.gtech.gvcore.monitor.JvmObjectMonitor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * JVM监控服务
 * 提供JVM监控相关功能的服务层封装
 */
@Slf4j
@Service
public class JvmMonitorService {

    /**
     * 获取简化的对象统计信息
     */
    public String getSimpleObjectStatistics() {
        return JvmObjectMonitor.getSimpleObjectStatistics();
    }

    /**
     * 获取详细的对象统计信息
     */
    public String getDetailedObjectStatistics() {
        return JvmObjectMonitor.getDetailedObjectStatistics();
    }

    /**
     * 获取详细的对象实例信息
     */
    public String getDetailedObjectInstances() {
        return JvmObjectMonitor.getDetailedObjectInstances();
    }

    /**
     * 评估内存泄漏风险
     */
    public String assessMemoryLeakRisk() {
        JvmObjectMonitor.MemoryLeakRisk risk = JvmObjectMonitor.assessMemoryLeakRisk();
        return risk.toString();
    }

    /**
     * 跟踪对象实例
     */
    public void trackObject(Object obj, String identifier) {
        JvmObjectMonitor.trackObject(obj, identifier);
    }

    /**
     * 执行垃圾回收并返回前后对比
     */
    public String forceGarbageCollection() {
        try {
            // 记录GC前的内存状态
            String beforeStats = getSimpleObjectStatistics();
            log.info("GC前内存状态: {}", beforeStats);
            
            // 执行GC
            System.gc();
            
            // 等待一小段时间让GC完成
            Thread.sleep(1000);
            
            // 记录GC后的内存状态
            String afterStats = getSimpleObjectStatistics();
            log.info("GC后内存状态: {}", afterStats);
            
            return "GC执行完成。GC前: " + beforeStats + ", GC后: " + afterStats;
        } catch (Exception e) {
            log.error("执行GC失败", e);
            return "执行GC失败: " + e.getMessage();
        }
    }
}
