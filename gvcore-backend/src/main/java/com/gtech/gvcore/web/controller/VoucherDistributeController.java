package com.gtech.gvcore.web.controller;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.voucherdistribute.CreateVoucherDistributeRequest;
import com.gtech.gvcore.service.VoucherDistributeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Gao.Yuhua
 * @date : 2022-03-08 10:11
 **/

@Slf4j
@RestController
@RequestMapping(value = "/gv/distribute")
@Api(value = "Voucher Distribute", tags = {"Voucher Distribute Api"})
public class VoucherDistributeController {

    @Autowired
    private VoucherDistributeService voucherDistributeService;


    @ApiOperation(value = "create Voucher Distribute", notes = "create Voucher Distribute")
    @PostMapping(value = "/createVoucherDistribute")
    public Result<Object> createVoucherDistribute(@Validated @RequestBody CreateVoucherDistributeRequest distributeRequest) {
        log.info("CreateVoucherDistributeRequest:{}", JSON.toJSONString(distributeRequest));
        return voucherDistributeService.voucherDistributeByPrint(distributeRequest);
    }
}
