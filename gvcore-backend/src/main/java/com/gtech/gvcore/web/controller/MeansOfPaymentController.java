package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.meansofpayment.CreateMeansOfPaymentRequest;
import com.gtech.gvcore.common.request.meansofpayment.QueryMeansOfPaymentsByPageRequest;
import com.gtech.gvcore.common.request.meansofpayment.UpdateMeansOfPaymentRequest;
import com.gtech.gvcore.common.request.meansofpayment.UpdateMeansOfPaymentStatusRequest;
import com.gtech.gvcore.common.response.meansofpayment.QueryMeansOfPaymentsByPageResponse;
import com.gtech.gvcore.service.MeansOfPaymentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022年2月21日
 */
@RestController
@RequestMapping(value = "/gv/meansOfPayment")
@Api(value = "Means Of Payment maintenance.", tags = { "GV Means Of Payment Api" })
public class MeansOfPaymentController {

    @Autowired
    private MeansOfPaymentService meansOfPaymentService;

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月21日
     */
    @ApiOperation(value = "Create Means Of Payment", notes = "Create Means Of Payment")
    @PostMapping(value = "/createMeansOfPayment")
    public Result<Void> createMeansOfPayment(@RequestBody @Validated CreateMeansOfPaymentRequest request) {

        return meansOfPaymentService.createMeansOfPayment(request);
    }

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月21日
     */
    @ApiOperation(value = "Update Means Of Payment", notes = "Update Means Of Payment")
    @PostMapping(value = "/updateMeansOfPayment")
    public Result<Void> updateMeansOfPayment(@RequestBody @Validated UpdateMeansOfPaymentRequest request) {

        return meansOfPaymentService.updateMeansOfPayment(request);
    }

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月21日
     */
    @ApiOperation(value = "Update Means Of Payment status", notes = "Update Means Of Payment status")
    @PostMapping(value = "/updateMeansOfPaymentStatus")
    public Result<Void> updateMeansOfPaymentStatus(@RequestBody @Validated UpdateMeansOfPaymentStatusRequest request) {

        return meansOfPaymentService.updateMeansOfPaymentStatus(request);
    }

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月21日
     */
    @ApiOperation(value = "Query Means Of Payment By Page", notes = "Query Means Of Payment By Page")
    @PostMapping(value = "/queryMeansOfPaymentsByPage")
    public PageResult<QueryMeansOfPaymentsByPageResponse> queryMeansOfPaymentsByPage(
            @RequestBody @Validated QueryMeansOfPaymentsByPageRequest request) {

        return meansOfPaymentService.queryMeansOfPaymentsByPage(request);
    }

}
