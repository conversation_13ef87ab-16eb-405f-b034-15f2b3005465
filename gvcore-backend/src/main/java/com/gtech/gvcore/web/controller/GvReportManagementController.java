package com.gtech.gvcore.web.controller;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.reportmanagement.CreateReportManagementListRequest;
import com.gtech.gvcore.common.request.reportmanagement.DelReportManagementRequest;
import com.gtech.gvcore.common.request.reportmanagement.QueryReportManagementRequest;
import com.gtech.gvcore.common.request.reportmanagement.UpdateReportManagementRequest;
import com.gtech.gvcore.common.response.reportmanagement.QueryReportManagementResponse;
import com.gtech.gvcore.service.ReportManagementService;
import com.gtech.gvcore.service.report.impl.support.liability.LiabilityDataScript;
import com.gtech.gvcore.service.report.impl.support.sales.CancelSalesDataRefresh;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/11 16:46
 */


@Api(tags = "Report management")
@RestController
@RequestMapping("/gv/reportManagement")
@Slf4j
public class GvReportManagementController {

    @Autowired
    private ReportManagementService reportManagementService;

    @Autowired
    private CancelSalesDataRefresh cancelSalesDataRefresh;

    @PostMapping("/createReportManagement")
    @ApiOperation("Create report management notes")
    public Result<List<String>> createReportManagement(@RequestBody @Validated CreateReportManagementListRequest createReportManagementRequests) {
        log.info("createReportManagementRequests:{}", JSON.toJSONString(createReportManagementRequests));
        return reportManagementService.createReportManagements(createReportManagementRequests.getCreateReportManagementRequests());
    }

    @PostMapping("/updatePermissions")
    @ApiOperation("Modify the permission to export PDF and Excel files")
    public Result<Void> updatePermissions(@RequestBody @Validated UpdateReportManagementRequest updateReportManagementRequest) {
        log.info("updateReportManagementRequest:{}", JSON.toJSONString(updateReportManagementRequest));
        return reportManagementService.updatePermissions(updateReportManagementRequest);
    }


    @PostMapping("/delReportManagement")
    @ApiOperation("Delete a Report Management record")
    public Result<Void> delReportManagement(@RequestBody @Validated DelReportManagementRequest delReportManagementRequest) {
        log.info("delReportManagementRequest:{}", JSON.toJSONString(delReportManagementRequest));
        return reportManagementService.delReportManagement(delReportManagementRequest);
    }

    @PostMapping("/queryReportManagements")
    @ApiOperation(value = "Query report management notes")
    public PageResult<QueryReportManagementResponse> queryReportManagements(@RequestBody @Validated QueryReportManagementRequest reportManagementRequest) {
        log.info("reportManagementRequest:{}", JSON.toJSONString(reportManagementRequest));
        return reportManagementService.queryReportManagements(reportManagementRequest);
    }


    @Autowired
    LiabilityDataScript liabilityDataScript;

    @GetMapping("generateLiabilityDataScript")
    public void generateLiabilityDataScript(@RequestParam(name = "tableCode") String tableCode){
        liabilityDataScript.runner(tableCode);
    }


    @PostMapping("initCancelSalesData")
    public void initCancelSalesData(){
        cancelSalesDataRefresh.init(null);
    }

}
