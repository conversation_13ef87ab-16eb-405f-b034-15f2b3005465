package com.gtech.gvcore.web.controller;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.request.releaseapprove.ApproveNodeRecordRequest;
import com.gtech.gvcore.common.request.releaseapprove.ReleaseApproveAbleRequest;
import com.gtech.gvcore.common.request.voucherrequest.CreateVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.UpdateVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherreturnantransfer.CreateVoucherReturnOrTransferRequest;
import com.gtech.gvcore.common.request.voucherreturnantransfer.QueryVoucherReturnAndTransferRequest;
import com.gtech.gvcore.common.request.voucherreturnantransfer.ReturnAndTransferRequest;
import com.gtech.gvcore.common.response.voucherrequest.QueryVoucherRequestResponse;
import com.gtech.gvcore.service.VoucherReturnAndTransferService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/8 9:49
 */

@RestController
@RequestMapping("/gv/voucherReturnOrTransfer")
@Api(tags = "Voucher return or transfer")
@Slf4j
public class GvVoucherReturnAndTransferController {

    @Autowired
    private VoucherReturnAndTransferService voucherReturnAndTransferService;

    @ApiOperation("Create voucher return or transfer")
    @PostMapping("/createVoucherReturnOrTransfer")
    public Result<String> createVoucherReturnOrTransfer(@RequestBody @Validated CreateVoucherReturnOrTransferRequest createVoucherReturnOrTransferRequest) {
        log.info("createVoucherReturnOrTransferRequest:{}", JSON.toJSONString(createVoucherReturnOrTransferRequest));
        CreateVoucherRequestRequest createVoucherRequestRequest = BeanCopyUtils.jsonCopyBean(createVoucherReturnOrTransferRequest, CreateVoucherRequestRequest.class);
        return voucherReturnAndTransferService.addVoucherRequest(createVoucherRequestRequest);
    }

	@ApiOperation("Update voucher return or transfer")
	@PostMapping("/updateVoucherReturnOrTransfer")
	public Result<Void> updateVoucherReturnOrTransfer(@RequestBody @Validated UpdateVoucherRequestRequest updateVoucherRequestRequest) {
		log.info("updateVoucherReturnOrTransferRequest:{}", JSON.toJSONString(updateVoucherRequestRequest));
		return voucherReturnAndTransferService.updateVoucherRequest(updateVoucherRequestRequest);
	}

    @ApiOperation("Query voucher return and transfer list")
    @PostMapping("/queryVoucherReturnsOrTransfers")
    public PageResult<QueryVoucherRequestResponse> queryVoucherReturnsOrTransfers(@RequestBody @Validated QueryVoucherReturnAndTransferRequest queryVoucherReturnAndTransferRequest) {
        log.info("queryVoucherReturnAndTransferRequest:{}", JSON.toJSONString(queryVoucherReturnAndTransferRequest));
        return voucherReturnAndTransferService.queryVoucherRequest(queryVoucherReturnAndTransferRequest);
    }

    @PostMapping("/approveVoucherReturnAndTransferAble")
    @ApiOperation("Approve able of voucher return&transfer")
    public Result<Boolean> approveVoucherReturnAndTransferAble(@RequestBody @Validated ReleaseApproveAbleRequest releaseApproveAbleRequest) {
        log.info("releaseApproveAbleRequest:{}", JSON.toJSONString(releaseApproveAbleRequest));
        return voucherReturnAndTransferService.approveVoucherReturnAndTransferAble(releaseApproveAbleRequest);
    }

    @PostMapping("/approveVoucherReturnAndTransfer")
    @ApiOperation("Approve voucher return&transfer order")
    public Result<String> approveVoucherReturnAndTransfer(@RequestBody @Validated ApproveNodeRecordRequest approveNodeRecordRequest) {
        log.info("approveNodeRecordRequest:{}", JSON.toJSONString(approveNodeRecordRequest));
        return voucherReturnAndTransferService.approveVoucherReturnAndTransfer(approveNodeRecordRequest);
    }

    @PostMapping("/returnAndTransfer")
    @ApiOperation("Return and transfer voucher")
    public Result<Void> returnAndTransfer(@RequestBody @Validated ReturnAndTransferRequest returnAndTransferRequest) {
        log.info("returnAndTransferRequest:{}", JSON.toJSONString(returnAndTransferRequest));
        return voucherReturnAndTransferService.returnAndTransfer(returnAndTransferRequest);
    }

}
