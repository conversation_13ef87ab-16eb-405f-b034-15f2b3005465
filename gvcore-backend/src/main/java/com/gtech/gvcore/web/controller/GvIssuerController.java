package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.issuer.*;
import com.gtech.gvcore.common.response.issuer.IssuerResponse;
import com.gtech.gvcore.service.IssuerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2022/2/16 17:36
 */
@RestController
@RequestMapping(value = "/gv/issuer")
@Api(value = "Issuer data dictionary.", tags = { "GV issuer Api" })
public class GvIssuerController {

    @Autowired
    private IssuerService issuerService;
    
    @ApiOperation(value = "Create issuer ",notes = "Create issuer  information.")
    @PostMapping(value = "/createIssuer")
    public Result<String> createIssuer(@RequestBody @Validated CreateIssuerRequest param) {




        // Return result object
        return issuerService.createIssuer(param);
    }

    @ApiOperation(value = "Update issuer ",notes = "Update issuer  information.")
    @PostMapping(value = "/updateIssuer")
    public Result<Void> updateIssuer(@RequestBody @Validated UpdateIssuerRequest param) {


        // Return result object
        return issuerService.updateIssuer(param);
    }


    @ApiOperation(value = "Update issuer status",notes = "Update issuer status.")
    @PostMapping(value = "/updateIssuerStatus")
    public Result<Void> updateIssuerStatus(@RequestBody @Validated UpdateIssuerStatusRequest param) {

        // Return result object
        return issuerService.updateIssuerStatus(param);
    }

    @ApiOperation(value = "Delete issuer ",notes = "Delete issuer  information.")
    @PostMapping(value = "/deleteIssuer")
    public Result<Void> deleteIssuer(@RequestBody @Validated DeleteIssuerRequest param) {


        issuerService.deleteIssuer(param);

        // Return result object
        return Result.ok();
    }


    @ApiOperation(value = "Query issuer  list",notes = "Query issuer  information list.")
    @PostMapping(value = "/queryIssuerList")
    public PageResult<IssuerResponse> queryIssuerList(@RequestBody @Validated QueryIssuerRequest param) {

        // Return result object
        return issuerService.queryIssuerList(param);
    }


    @ApiOperation(value = "Get issuer ",notes = "Get issuer  information.")
    @PostMapping(value = "/getIssuer")
    public Result<IssuerResponse> getIssuer(@RequestBody @Validated GetIssuerRequest param) {


        IssuerResponse issuer = issuerService.getIssuer(param);

        // Return result object
        return Result.ok(issuer);
    }
    
    
    
}
