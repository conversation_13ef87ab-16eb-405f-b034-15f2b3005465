package com.gtech.gvcore.web.controller.gc;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.articlemop.CreateArticleMopRequest;
import com.gtech.gvcore.common.request.articlemop.QueryArticleMopsByPageRequest;
import com.gtech.gvcore.common.request.articlemop.UpdateArticleMopRequest;
import com.gtech.gvcore.common.request.articlemop.UpdateArticleMopStatusRequest;
import com.gtech.gvcore.common.response.articlemop.QueryArticleMopsByPageResponse;
import com.gtech.gvcore.service.GcArticleMopService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/gc/articleMop")
@RequiredArgsConstructor
@Api(value = "Gift card article code", tags = {"Gift card  article code controller"})
public class GcArticleCodeController {

    private final GcArticleMopService gcArticleCodeService;


    /**
     *
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月17日
     */
    @ApiOperation(value = "Create article code mop code bind data", notes = "Create article code mop code bind data")
    @PostMapping(value = "/createArticleMop")
    public Result<String> createArticleMop(@RequestBody @Validated CreateArticleMopRequest request) {

        return gcArticleCodeService.createArticleMop(request);
    }

    /**
     *
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月21日
     */
    @ApiOperation(value = "Update article code mop code bind data", notes = "Update article code mop code bind data")
    @PostMapping(value = "/updateArticleMop")
    public Result<Void> updateArticleMop(@RequestBody @Validated UpdateArticleMopRequest request) {

        return gcArticleCodeService.updateArticleMop(request);
    }

    /**
     *
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月21日
     */
    @ApiOperation(value = "Update article code mop code bind data status", notes = "Update article code mop code bind data status")
    @PostMapping(value = "/updateArticleMopStatus")
    public Result<Void> updateArticleMopStatus(@RequestBody @Validated UpdateArticleMopStatusRequest request) {

        return gcArticleCodeService.updateArticleMopStatus(request);
    }

    /**
     *
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月21日
     */
    @ApiOperation(value = "Query article code mop code bind data", notes = "Query article code mop code bind data")
    @PostMapping(value = "/queryArticleMopsByPage")
    public PageResult<QueryArticleMopsByPageResponse> queryArticleMopsByPage(
            @RequestBody @Validated QueryArticleMopsByPageRequest request) {

        return gcArticleCodeService.queryArticleMopsByPage(request);
    }



}
