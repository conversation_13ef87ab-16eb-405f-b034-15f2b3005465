package com.gtech.gvcore.web.controller;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.request.voucherrequest.*;
import com.gtech.gvcore.common.response.voucherrequest.GetVoucherRequestResponse;
import com.gtech.gvcore.common.response.voucherrequest.QueryHeadOfficeAndWareHouseResponse;
import com.gtech.gvcore.common.response.voucherrequest.QueryVoucherRequestResponse;
import com.gtech.gvcore.service.VoucherRequestService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/8 9:49
 */

@RestController
@RequestMapping("/gv/voucherRequest")
@Api(tags = "Voucher request")
@Slf4j
public class GvVoucherRequestController {

    @Autowired
    private VoucherRequestService voucherRequestService;

    @ApiOperation("Create voucher request")
    @PostMapping("/createVoucherRequest")
    public Result<String> createVoucherRequest(@RequestBody @Validated CreateVoucherRequestRequest createVoucherRequestRequest) {
        log.info("createVoucherRequestRequest:{}", JSON.toJSONString(createVoucherRequestRequest));
        return voucherRequestService.addVoucherRequest(createVoucherRequestRequest, "request");
    }

    @ApiOperation("update voucher request")
    @PostMapping("/updateVoucherRequest")
    public Result<Void> updateVoucherRequest(@RequestBody @Validated UpdateVoucherRequestRequest updateVoucherRequestRequest) {
        log.info("updateVoucherRequestRequest:{}", JSON.toJSONString(updateVoucherRequestRequest));
        return voucherRequestService.updateVoucherRequest(updateVoucherRequestRequest);
    }

    @ApiOperation("Approve voucher request")
    @PostMapping("/approveVoucherRequest")
    public Result<String> approveVoucherRequest(@RequestBody @Validated ApproveVoucherRequestRequest approveVoucherRequestRequest) {
        log.info("approveVoucherRequestRequest:{}", JSON.toJSONString(approveVoucherRequestRequest));
        return voucherRequestService.approveVoucherRequest(approveVoucherRequestRequest);
    }

    @ApiOperation("Bulk approve voucher request")
    @PostMapping("/bulkApproveVoucherRequest")
    public Result<Void> bulkApproveVoucherRequest(@RequestBody @Validated BulkApproveVoucherRequestRequest bulkApproveVoucherRequestRequest) {
        log.info("bulkApproveVoucherRequestRequest:{}", JSON.toJSONString(bulkApproveVoucherRequestRequest));
        return voucherRequestService.bulkApproveVoucherRequest(bulkApproveVoucherRequestRequest);
    }

    @ApiOperation("Query voucher request list")
    @PostMapping("/queryVoucherRequests")
    public PageResult<QueryVoucherRequestResponse> queryVoucherRequest(@RequestBody @Validated QueryVoucherRequestRequest queryVoucherRequestRequest) {
        queryVoucherRequestRequest.setBusinessType(GvcoreConstants.SALES);
        log.info("queryVoucherRequestRequest:{}", JSON.toJSONString(queryVoucherRequestRequest));
        return voucherRequestService.queryVoucherRequest(queryVoucherRequestRequest);
    }

    @ApiOperation("Bulk get voucher request list")
    @PostMapping("/bulkGetVoucherRequests")
    public Result<List<GetVoucherRequestResponse>> bulkGetVoucherRequests(@RequestBody @Validated BulkGetVoucherRequestRequest bulkGetVoucherRequestRequest) {
        log.info("bulkGetVoucherRequestRequest:{}", JSON.toJSONString(bulkGetVoucherRequestRequest));
        return voucherRequestService.bulkGetVoucherRequests(bulkGetVoucherRequestRequest.getVoucherRequestCodeList());
    }

    @ApiOperation("Get a voucher request ")
    @PostMapping("/getVoucherRequest")
    public Result<GetVoucherRequestResponse> getVoucherRequest(@RequestBody @Validated GetVoucherRequestRequest getVoucherRequestRequest) {
        log.info("getVoucherRequestRequest:{}", JSON.toJSONString(getVoucherRequestRequest));
		return voucherRequestService.getVoucherRequest(getVoucherRequestRequest);
    }

    @ApiOperation("Cancel a voucher request ")
    @PostMapping("/cancelVoucherRequest")
    public Result<Void> cancelVoucherRequest(@RequestBody @Validated CancelVoucherRequestRequest cancelVoucherRequestRequest) {
        log.info("cancelVoucherRequestRequest:{}", JSON.toJSONString(cancelVoucherRequestRequest));
        return voucherRequestService.cancelVoucherRequest(cancelVoucherRequestRequest);
    }


    @ApiOperation("Cancel a voucher request ")
    @PostMapping("/queryHeadOfficeAndWareHouse")
    public Result<QueryHeadOfficeAndWareHouseResponse> queryHeadOfficeAndWareHouse(@RequestBody @Validated QueryHeadOfficeAndWareHouseRequest cancelVoucherRequestRequest) {
        log.info("cancelVoucherRequestRequest:{}", JSON.toJSONString(cancelVoucherRequestRequest));
        return voucherRequestService.queryHeadOfficeAndWareHouse(cancelVoucherRequestRequest);
    }


}
