package com.gtech.gvcore.web.controller.gc;

import com.gtech.commons.result.Result;
import com.gtech.commons.page.PageData;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.request.cardnumberconfig.CreateCardNumberConfigRequest;
import com.gtech.gvcore.common.request.cardnumberconfig.GetCardNumberConfigRequest;
import com.gtech.gvcore.common.request.cardnumberconfig.QueryCardNumberConfigListRequest;
import com.gtech.gvcore.common.request.cardnumberconfig.UpdateCardNumberConfigRequest;
import com.gtech.gvcore.common.response.cardnumberconfig.CardNumberResponse;
import com.gtech.gvcore.dto.CardNumberConfigDto;
import com.gtech.gvcore.service.CardNumberConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/gc/cardNumberConfig")
@Api(value = "Card number config.", tags = {"Card number config Api"})
public class CardNumberConfigController {

    @Autowired
    private CardNumberConfigService cardNumberConfigService;


    @PostMapping(value = "/createCardNumberConfig")
    @ApiOperation(value = "Create card number config.", notes = "Create card number config.")
    public Result<String> createCardNumberConfig(@RequestBody CreateCardNumberConfigRequest request) {
        cardNumberConfigService .createCardNumberConfig(BeanCopyUtils.jsonCopyBean(request, CardNumberConfigDto.class));
        return Result.ok();
    }

    @PostMapping(value = "/updateCardNumberConfig")
    @ApiOperation(value = "Update card number config.", notes = "Update card number config.")
    public Result<String> updateCardNumberConfig(@RequestBody UpdateCardNumberConfigRequest request) {
        cardNumberConfigService .updateCardNumberConfig(BeanCopyUtils.jsonCopyBean(request, CardNumberConfigDto.class));
        return Result.ok();
    }

    @PostMapping(value = "/getCardNumberConfig")
    @ApiOperation(value = "Get card number config.", notes = "Get card number config.")
    public Result<CardNumberResponse> getCardNumberConfig(@RequestBody GetCardNumberConfigRequest request) {
        return Result.ok(BeanCopyUtils.jsonCopyBean(cardNumberConfigService.getCardNumberConfig(request.getConfigCode()), CardNumberResponse.class));
    }

    @PostMapping(value = "/queryCardNumberConfigList")
    @ApiOperation(value = "Query card number config list.", notes = "Query card number config list.")
    public PageResult<CardNumberResponse> queryCardNumberConfigList(@RequestBody QueryCardNumberConfigListRequest request) {
        PageData<CardNumberConfigDto> cardNumberConfigDtoPageData = cardNumberConfigService.queryCardNumberConfigList(BeanCopyUtils.jsonCopyBean(request, CardNumberConfigDto.class));
        return new PageResult<>(BeanCopyUtils.jsonCopyList(cardNumberConfigDtoPageData.getList(), CardNumberResponse.class), cardNumberConfigDtoPageData.getTotal());
    }






}


