package com.gtech.gvcore.web.controller;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.enums.FlowEnum;
import com.gtech.gvcore.common.enums.FlowNodeEnum;
import com.gtech.gvcore.common.request.customerorder.*;
import com.gtech.gvcore.common.request.flow.SendNoticeRequest;
import com.gtech.gvcore.common.request.releaseapprove.ApproveNodeRecordRequest;
import com.gtech.gvcore.common.request.releaseapprove.ReleaseApproveAbleRequest;
import com.gtech.gvcore.common.response.customerorder.GetCustomerOrderResponse;
import com.gtech.gvcore.common.response.customerorder.QueryCustomerOrderResponse;
import com.gtech.gvcore.service.CustomerOrderService;
import com.gtech.gvcore.service.FlowNoticeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/18 14:36
 */

@RestController
@RequestMapping("/gv/customerOrder")
@Slf4j
@Api(tags = "Customer order")
public class GvCustomerOrderController {

    @Autowired
    private CustomerOrderService customerOrderService;

	@Autowired
	private FlowNoticeService flowNoticeService;

    @Value("${customerOrder.limit:100000}")
    private Integer customerOrderLimit;

    @PostMapping("/createCustomerOrder")
    @ApiOperation(value = "Create customer order")
    public Result<String> createCustomerOrder(@RequestBody @Validated CreateCustomerOrderRequest createCustomerOrderRequest) {
        createCustomerOrderRequest.validation();
        log.info("createCustomerOrderRequest:{}", JSON.toJSONString(createCustomerOrderRequest));
        if (createCustomerOrderRequest.getVoucherNum()>customerOrderLimit){
            return Result.failed("Exceeding the maximum coupon quantity limit for a single order:"+customerOrderLimit);
        }
        return customerOrderService.createCustomerOrder(createCustomerOrderRequest);
    }

    @PostMapping("/nonSystemCreateCustomerOrder")
    @ApiOperation("Non-system user create customer order")
    public Result<String> nonSystemCreateCustomerOrder(@RequestBody @Validated NonSystemCreateCustomerOrderRequest nonSystemCreateCustomerOrderRequest) {
        nonSystemCreateCustomerOrderRequest.validation();
        log.info("nonSystemCreateCustomerOrderRequest:{}", JSON.toJSONString(nonSystemCreateCustomerOrderRequest));
        Result<String> result = customerOrderService.nonSystemCreateCustomerOrder(nonSystemCreateCustomerOrderRequest);
		if (result.isSuccess() && !StringUtil.isEmpty(result.getData())) {
			sendOrderEmail(result.getData());
		}
        return result;
    }

	private void sendOrderEmail(String customerOrderCode) {

		Map<String, Object> params = customerOrderService.getExtendsParams(customerOrderCode);
		SendNoticeRequest sendNoticeRequest = new SendNoticeRequest();
		sendNoticeRequest.setBusinessCode(customerOrderCode);
		sendNoticeRequest.setFlowCode(FlowEnum.CUSTOMER_ORDER_FLOW.getCode());
		sendNoticeRequest.setFlowNodeCode(FlowNodeEnum.CUSTOMER_CREATED.getCode());
		sendNoticeRequest.setExtendParams(params);
		flowNoticeService.send(sendNoticeRequest);
	}

    @PostMapping("/QueryCustomerOrders")
    @ApiOperation(value = "Query customer orders")
    public PageResult<QueryCustomerOrderResponse> queryCustomerOrder(@RequestBody @Validated QueryCustomerOrderRequest queryCustomerOrderRequest) {
        log.info("queryCustomerOrderRequest:{}", JSON.toJSONString(queryCustomerOrderRequest));
        return customerOrderService.queryCustomerOrder(queryCustomerOrderRequest);
    }

    @PostMapping("/getCustomerOrder")
    @ApiOperation("Get customer order")
    public Result<GetCustomerOrderResponse> getCustomerOrder(@RequestBody @Validated GetCustomerOrderRequest getCustomerOrderRequest) {
        log.info("getCustomerOrderRequest:{}", JSON.toJSONString(getCustomerOrderRequest));
        return customerOrderService.getCustomerOrder(getCustomerOrderRequest);
    }



    @PostMapping("/resendEmail")
    @ApiOperation("Resend email")
    public Result<Void> resendEmail(@RequestBody @Validated ResendEmailRequest request) {

        log.info("resendEmail:{}", JSON.toJSONString(request));

        customerOrderService.resendVoucherEmail(request.getEmailCode());

        return Result.ok();
    }

    @PostMapping("/addSendEmail")
    @ApiOperation("add send email")
    public Result<Void> addSendEmail(@RequestBody @Validated AddSendEmailRequest request) {

        log.info("addSendEmail:{}", JSON.toJSONString(request));

        customerOrderService.addSendVoucherEmail(request);

        return Result.ok();
    }


    @PostMapping("/updateCustomerOrder")
    @ApiOperation("Update customer order")
    public Result<Void> updateCustomerOrder(@RequestBody @Validated UpdateCustomerOrderRequest updateCustomerOrderRequest) {
        log.info("updateCustomerOrderRequest:{}", JSON.toJSONString(updateCustomerOrderRequest));
        return customerOrderService.updateCustomerOrder(updateCustomerOrderRequest);
    }

    @PostMapping("/submitCustomerOrder")
    @ApiOperation("Submit customer order")
    public Result<Void> submitCustomerOrder(@RequestBody @Validated SubmitCustomerOrderRequest submitCustomerOrderRequest) {
        log.info("submitCustomerOrderRequest:{}", JSON.toJSONString(submitCustomerOrderRequest));
        return customerOrderService.submitCustomerOrder(submitCustomerOrderRequest);
    }

    @PostMapping("/approveCustomerOrderAble")
    @ApiOperation("Approve able of customer order")
    public Result<Boolean> approveCustomerOrderAble(@RequestBody @Validated ReleaseApproveAbleRequest releaseApproveAbleRequest) {
        log.info("releaseApproveAbleRequest:{}", JSON.toJSONString(releaseApproveAbleRequest));
        return customerOrderService.approveCustomerOrderAble(releaseApproveAbleRequest);
    }

    @PostMapping("/deliverCustomerOrder")
    @ApiOperation("Deliver customer order")
    public Result<Void> deliverCustomerOrder(@RequestBody @Validated DeliverCustomerOrderRequest deliverCustomerOrderRequest) {
        log.info("checkCustomerInfo:{}", JSON.toJSONString(deliverCustomerOrderRequest));
        customerOrderService.deliverCustomerOrder(deliverCustomerOrderRequest);
        return Result.ok();
    }

    @PostMapping("/approveCustomerOrder")
    @ApiOperation("Approve customer order")
    public Result<String> approveCustomerOrder(@RequestBody @Validated ApproveCustomerOrderRequest approveCustomerOrderRequest) {
        log.info("approveCustomerOrderRequest:{}", JSON.toJSONString(approveCustomerOrderRequest));
        return customerOrderService.approveCustomerOrder(approveCustomerOrderRequest);
    }

    @PostMapping("/generatePurchaseOrderNumber")
    @ApiOperation("Generate purchase order number")
    public Result<String> generatePurchaseOrderNumber(@RequestBody @Validated PoNumberRequest request) {
        return customerOrderService.generatePurchaseOrderNumber(request.getStoreCode());
    }


    @PostMapping("/updateRedisOrderNumber")
    @ApiOperation("Update Redis Order Number")
    public Result<String> updateRedisOrderNumber(@RequestBody @Validated PoNumberRequest request) {
        return customerOrderService.updateRedisOrderNumber(request.getStoreCode(),request.getPoNumber());
    }

    /**
     * @param request
     * @return
     * <AUTHOR>
     * @date 2022年4月14日
     */
    @ApiOperation(value = "Issuance", notes = "Issuance")
    @PostMapping(value = "/issuance")
    public Result<String> issuance(@RequestBody @Validated IssuanceRequest request) {
		log.info("issuance controller:{}", JSON.toJSONString(request));
        return customerOrderService.issuance(request);
    }

    /**
     *
     * @param approveNodeRecordRequest
     * @return
     * <AUTHOR>
     * @date 2022年4月20日
     */
    @ApiOperation(value = "Approve customer order release", notes = "Approve customer order release")
    @PostMapping(value = "/approveCustomerOrderRelease")
    public Result<String> approveCustomerOrderRelease(
            @RequestBody @Validated ApproveNodeRecordRequest approveNodeRecordRequest) {
        log.info("approveCustomerOrderRelease request={}", JSON.toJSONString(approveNodeRecordRequest));
        return customerOrderService.approveCustomerOrderRelease(approveNodeRecordRequest);
    }

    /**
     * @param request
     * @return
     * <AUTHOR>
     * @date 2022年4月14日
     */
    @ApiOperation(value = "Release", notes = "Release")
    @PostMapping(value = "/release")
    public Result<String> release(@RequestBody @Validated ReleaseRequest request) {
        return customerOrderService.release(request);
    }


    @ApiOperation(value = "Delivery Reminder", notes = "Delivery Reminder")
    @PostMapping(value = "/deliveryReminder")
    public Result<String> delivery() {
        return customerOrderService.delivery();
    }

    @ApiOperation(value = "Receive", notes = "Receive")
    @PostMapping(value = "/receive")
    public Result<String> receive(@RequestBody @Validated ReceiveRequest request) {
        return customerOrderService.receive(request);
    }

    @ApiOperation(value = "CancelReceive", notes = "CancelReceive")
    @PostMapping(value = "/cancelReceive")
    public Result<String> cancelReceive(@RequestBody @Validated CancelReceiveRequest request) throws Exception {
        return customerOrderService.cancelReceive(request);
    }

    @ApiOperation(value = "Cancel release", notes = "Cancel release")
    @PostMapping(value = "/cancelRelease")
    public Result<Void> cancelRelease(@RequestBody @Validated CancelReleaseRequest cancelReleaseRequest) {
        return customerOrderService.cancelRelease(cancelReleaseRequest);
    }

    @ApiOperation(value = "Upload payment voucher", notes = "Upload payment voucher")
    @PostMapping(value = "/uploadPaymentVoucher")
    public Result<Void> uploadPaymentVoucher(@RequestBody @Validated UploadPaymentVoucherRequest uploadPaymentVoucherRequest) {
        return customerOrderService.uploadPaymentVoucher(uploadPaymentVoucherRequest);
    }

    @ApiOperation(value = "Del payment voucher", notes = "Del payment voucher")
    @PostMapping(value = "/delPaymentVoucher")
    public Result<Void> delPaymentVoucher(@RequestBody @Validated DelPaymentVoucherRequest delPaymentVoucherRequest) {
        return customerOrderService.delPaymentVoucher(delPaymentVoucherRequest);
    }

    @ApiOperation(value = "Send email", notes = "Send email")
    @PostMapping(value = "/sendEmail")
    public Result<Void> sendEmail(@RequestBody @Validated SendCustomerOrderEmailRequest sendCustomerOrderEmailRequest) {
        return customerOrderService.sendEmail(sendCustomerOrderEmailRequest);
    }

    @ApiOperation(value = "Update customer info in customer order")
    @PostMapping(value = "/updateCustomerInfoInCustomerOrder")
    public Result<Void> updateCustomerInfoInCustomerOrder(@RequestBody @Validated UpdateCustomerInfoInCustomerOrderRequest updateCustomerInfoInCustomerOrderRequest) {
        log.info("updateCustomerInfoInCustomerOrder:{}", updateCustomerInfoInCustomerOrderRequest);
        return customerOrderService.updateCustomerInfoInCustomerOrder(updateCustomerInfoInCustomerOrderRequest);
    }

}
