package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.merchant.*;
import com.gtech.gvcore.common.response.merchant.MerchantResponse;
import com.gtech.gvcore.service.MerchantService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2022/2/16 17:36
 */
@RestController
@RequestMapping(value = "/gv/merchant")
@Api(value = "Merchant data dictionary.", tags = { "GV merchant Api" })
public class GvMerchantController {


    @Autowired
    private MerchantService merchantService;
    
    @ApiOperation(value = "Create merchant ",notes = "Create merchant information.")
    @PostMapping(value = "/createMerchant")
    public Result<String> createMerchant(@RequestBody @Validated CreateMerchantRequest param) {

        // Return result object
        return merchantService.createMerchant(param);
    }

    @ApiOperation(value = "Update merchant ",notes = "Update merchant information.")
    @PostMapping(value = "/updateMerchant")
    public Result<Void> updateMerchant(@RequestBody @Validated UpdateMerchantRequest param) {
        // Return result object
        return merchantService.updateMerchant(param);
    }

    @ApiOperation(value = "Update merchant status",notes = "Update merchant status.")
    @PostMapping(value = "/updateMerchantStatus")
    public Result<Void> updateMerchantStatus(@RequestBody @Validated UpdateMerchantStatusRequest param) {
        // Return result object
        return merchantService.updateMerchantStatus(param);
    }

    @ApiOperation(value = "Delete merchant ",notes = "Delete merchant information.")
    @PostMapping(value = "/deleteMerchant")
    public Result<Void> deleteMerchant(@RequestBody @Validated DeleteMerchantRequest param) {




        // Return result object
        return merchantService.deleteMerchant(param);
    }


    @ApiOperation(value = "Query merchant  list",notes = "Query merchant information list.")
    @PostMapping(value = "/queryMerchantList")
    public PageResult<MerchantResponse> queryMerchantList(@RequestBody @Validated QueryMerchantRequest param) {

        // Return result object
        return merchantService.queryMerchantList(param);
    }


    @ApiOperation(value = "Get merchant ",notes = "Get merchant information.")
    @PostMapping(value = "/getMerchant")
    public Result<MerchantResponse> getMerchant(@RequestBody @Validated GetMerchantRequest param) {


        MerchantResponse merchant = merchantService.getMerchant(param);

        // Return result object
        return Result.ok(merchant);
    }



    @ApiOperation(value = "Query company by company codes",notes = "Query company information by company codes.")
    @PostMapping(value = "/queryMerchantByCompanyCodes")
    public PageResult<MerchantResponse> queryMerchantByCompanyCodes(@RequestBody @Validated QueryMerchantByCompanyCodesRequest param) {


        // Return result object
        return merchantService.queryMerchantByCompanyCodes(param);
    }
    
    
    
}
