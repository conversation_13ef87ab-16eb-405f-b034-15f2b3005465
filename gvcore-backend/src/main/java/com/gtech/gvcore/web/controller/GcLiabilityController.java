package com.gtech.gvcore.web.controller;


import com.gtech.gvcore.service.report.impl.support.liability.GcLiabilityDataScript;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Controller("gcLiability")
public class GcLiabilityController {

    private static final Logger logger = LoggerFactory.getLogger(GcLiabilityController.class);

    @Autowired
    GcLiabilityDataScript script;

    @PostMapping("execute")
    public void execute() {
        long startTime = System.currentTimeMillis();
        script.scheduled();
        long endTime = System.currentTimeMillis();
        logger.info("执行耗时: {} ms", (endTime - startTime));
    }
}
