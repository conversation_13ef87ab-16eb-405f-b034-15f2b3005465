package com.gtech.gvcore.web.controller.gc;


import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.cardprogress.GetCardProgressRequest;
import com.gtech.gvcore.common.utils.ProgressTracker;
import io.swagger.annotations.Api;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/gc/cardProgressTracker")
@Api(value = "Card progress tracker.", tags = {"Card progress tracker Api"})
public class GardProgressTrackerController {


    @Autowired
    private ProgressTracker progressTracker;


    /**
     * 获取进度
     */
    @PostMapping("/getBatchProgress")
    public Result<ProgressTracker.TaskProgress> getBatchProgress(@RequestBody GetCardProgressRequest customerOrder) {
        return Result.ok(progressTracker.getMainTaskProgress(customerOrder.getCustomerOrder()));
    }


}
