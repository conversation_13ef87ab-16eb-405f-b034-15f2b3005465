package com.gtech.gvcore.web.controller.gc;

import com.gtech.commons.page.PageData;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.request.cpg.GetCpgRequest;
import com.gtech.gvcore.giftcard.masterdata.gcpg.request.CreateGcCpgRequest;
import com.gtech.gvcore.giftcard.masterdata.gcpg.dto.GcCpgDTO;
import com.gtech.gvcore.giftcard.masterdata.gcpg.request.QueryCpgListRequest;
import com.gtech.gvcore.giftcard.masterdata.gcpg.request.UpdateGcCpgRequest;
import com.gtech.gvcore.giftcard.masterdata.gcpg.response.GcCpgResponse;
import com.gtech.gvcore.giftcard.masterdata.gcpg.service.GcCpgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/gc/cpg")
@RequiredArgsConstructor
@Api(value = "Gift card cpg controller", tags = {"Gift card cpg controller"})
public class GcCpgController {

    private final GcCpgService gcCpgService;

    @ApiOperation(value = "Create gc cpg bind data", notes = "Create gc cpg bind data")
    @PostMapping("/createCpg")
    public Result<String> createCpg(@RequestBody @Valid CreateGcCpgRequest request) {
        return Result.ok(gcCpgService.createCpg(request));
    }

    @ApiOperation(value = "Update gc cpg bind data", notes = "Update gc cpg bind data")
    @PostMapping("/updateCpg")
    public Result<GcCpgResponse> updateCpg(@RequestBody UpdateGcCpgRequest request) {
        GcCpgDTO dto = gcCpgService.updateCpg(request);
        return Result.ok(BeanCopyUtils.jsonCopyBean(dto, GcCpgResponse.class));
    }

    @ApiOperation(value = "Get gc cpg bind data", notes = "Get gc cpg bind data")
    @PostMapping("/getCpg")
    public Result<GcCpgResponse> getCpg(@RequestBody GetCpgRequest request) {
        GcCpgDTO dto = gcCpgService.getCpg(request.getCpgCode());
        return Result.ok(BeanCopyUtils.jsonCopyBean(dto, GcCpgResponse.class));
    }

    @ApiOperation(value = "Query gc cpg bind data", notes = "Query gc cpg bind data")
    @PostMapping("/queryCpgList")
    public PageResult<GcCpgResponse> queryCpgList(@RequestBody QueryCpgListRequest request) {
        PageData<GcCpgDTO> select = gcCpgService.select(request);
        return new PageResult<>(BeanCopyUtils.jsonCopyList(select.getList(),GcCpgResponse.class), select.getTotal());
    }


} 