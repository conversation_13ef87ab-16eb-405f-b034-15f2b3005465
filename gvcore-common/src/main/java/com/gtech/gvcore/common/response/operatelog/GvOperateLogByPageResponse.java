package com.gtech.gvcore.common.response.operatelog;

import com.gtech.gvcore.common.request.operatelog.GvOperateLogRemarkJson;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.io.Serializable;
import java.util.List;

/**
 * (GvOperateLog)实体类
 *
 * <AUTHOR>
 * @since 2022-02-28 10:20:22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "GvOperateLogByPageResponse")
public class GvOperateLogByPageResponse implements Serializable {

    @ApiModelProperty(value = "businessCode")
    private String businessCode;

    @ApiModelProperty(value = "input")
    private String input;

    @ApiModelProperty(value = "output")
    private String output;

    @ApiModelProperty(value = "method")
    private String method;

    @ApiModelProperty(value = "operateUser")
    private String operateUser;

    @ApiModelProperty(value = "firstName")
    private String firstName;

    @ApiModelProperty(value = "lastName")
    private String lastName;

    @ApiModelProperty(value = "success")
    private String success;

    @ApiModelProperty(value = "createTime")
    private Date createTime;

    @ApiModelProperty(value = "remarks")
    private List<GvOperateLogRemarkJson> remarks;
}

