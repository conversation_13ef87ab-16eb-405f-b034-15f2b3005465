package com.gtech.gvcore.common.request.voucherdistribute;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> Gao.Yuhua
 * @date : 2022-02-21 17:53
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CreateVoucherDistributeRequest")
public class CreateVoucherDistributeRequest {

    @ApiModelProperty(value = "excelFileUrl", example = "https://gtech.com/test/1.xlsx", required = true)
    @NotEmpty(message = "excelFileUrl can not be empty")
    private String excelFileUrl;

    @ApiModelProperty(value = "targetFileName", example = "test2.xlsx", required = true)
    @NotEmpty(message = "targetFileName can not be empty")
    private String targetFileName;

    @ApiModelProperty(value = "printerCode", example = "CC1000023232", required = true)
    @NotEmpty(message = "printerCode can not be empty")
    private String printerCode;
}
