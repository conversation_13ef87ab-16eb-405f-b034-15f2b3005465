package com.gtech.gvcore.common.request.transaction;

import java.math.BigDecimal;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class BatchcloseRequest {
	@JsonProperty(value="TransactionId")
	@ApiModelProperty(value = "Transaction ID. ", example = "SF00001", required = true, notes = "The transaction id passed in the request.")
	@NotNull(message = "Transaction ID not blank")
	private Integer transactionId;
	@JsonProperty(value="DateAtClient")
	@ApiModelProperty(value = "Date At Client. ", example = "2022-02-02 18:00:00", required = true, notes = "Timestamp of client application in YYYY-MM-DD HH:MM:SS format")
	private String dateAtClient;

	@JsonProperty(value="ReloadCount")
	@ApiModelProperty(value = "Reload Count. ", example = "0", required = true, notes = "Total number of reloads in current batch")
	private Integer reloadCount;
	
	@JsonProperty(value="ReloadAmount")
	@ApiModelProperty(value = "Reload Amount. ", example = "0", required = true, notes = "Total value of reloads")
	private BigDecimal reloadAmount;
	
	@JsonProperty(value="ActivationCount")
	@ApiModelProperty(value = "Activation Count. ", example = "0", required = true, notes = "Total number of activations in current batch")
	private Integer activationCount;
	
	@JsonProperty(value="ActivationAmount")
	@ApiModelProperty(value = "Activation Amount. ", example = "0", required = true, notes = "Total value of activations")
	private BigDecimal activationAmount;
	
	@JsonProperty(value="RedemptionCount")
	@ApiModelProperty(value = "Redemption Count. ", example = "0", required = true, notes = "Total number of redemptions in current batch")
	private Integer redemptionCount;

	@JsonProperty(value="RedemptionAmount")
	@ApiModelProperty(value = "Redemption Amount. ", example = "0", required = true, notes = "Total number of redemptions in current batch")
	private BigDecimal redemptionAmount;
	
	@JsonProperty(value="CancelRedeemCount")
	@ApiModelProperty(value = "Cancel Redeem Count. ", example = "0", required = true, notes = "Total number of cancel redeems in current batch")
	private Integer cancelRedeemCount;
	
	@JsonProperty(value="CancelRedeemAmount")
	@ApiModelProperty(value = "Cancel Redeem Amount. ", example = "0", required = true, notes = "Total value of redemptions")
	private BigDecimal cancelRedeemAmount;
	
	@JsonProperty(value="CancelLoadAmount")
	@ApiModelProperty(value = "Cancel Load Amount. ", example = "0", required = true, notes = "Total value of cancel reloads")
	private BigDecimal cancelLoadAmount;
	
	@JsonProperty(value="CancelLoadCount")
	@ApiModelProperty(value = "Cancel Load Count. ", example = "0", required = true, notes = "Total number of cancel reloads in current batch")
	private Integer cancelLoadCount;
	
	@JsonProperty(value="CancelActivationCount")
	@ApiModelProperty(value = "Cancel Activation Count. ", example = "0", required = true, notes = "Total number of cancel activations in current batch")
	private Integer cancelActivationCount;
	
	@JsonProperty(value="CancelActivationAmount")
	@ApiModelProperty(value = "Cancel Activation Amount. ", example = "0", required = true, notes = "Total value of cancel activations")
	private BigDecimal cancelActivationAmount;
	
	@JsonProperty(value="IsActivationCancelAmountsProvided")
	@ApiModelProperty(value = "Is Activation Cancel Amounts Provided. ", example = "false", required = true, notes = "default: false")
	private Boolean isActivationCancelAmountsProvided;
	
	@JsonProperty(value="IActivationCancelCountsProvided")
	@ApiModelProperty(value = "Is Activation Cancel Counts Provided. ", example = "false", notes = "default: false")
	private Boolean isActivationCancelCountsProvided;
	
	@JsonProperty(value="IsLoadCancelAmountsProvided")
	@ApiModelProperty(value = "Is Load Cancel Amounts Provided. ", example = "false", notes = "default: false")
	private Boolean isLoadCancelAmountsProvided;
	
	@JsonProperty(value="IsLoadCancelCountsProvided")
	@ApiModelProperty(value = "Is Load Cancel Counts Provided. ", example = "false",notes = "default: false")
	private Boolean isLoadCancelCountsProvided;
	
	@JsonProperty(value="IsRedeemCancelAmountsProvided")
	@ApiModelProperty(value = "Is Redeem Cancel Amounts Provided. ", example = "false", notes = "default: false")
	private Boolean isRedeemCancelAmountsProvided;
	
	@JsonProperty(value="IsRedeemCancelCountsProvided")
	@ApiModelProperty(value = "Is Redeem Cancel Counts Provided. ", example = "false",notes = "default: false")
	private Boolean isRedeemCancelCountsProvided;
	
	@JsonProperty(value="BatchValidationRequired")
	@ApiModelProperty(value = "Batch Validation Required. ", example = "false", required = true, notes = "set true if count & amount validations ae required against QC source.default: false")
	private Boolean batchValidationRequired;
	@JsonProperty(value="SettlementDate")
	@ApiModelProperty(value = "Settlement Date. ", example = "2022-02-02 18:00:00", required = true, notes = "")
	private String settlementDate;
	

}
