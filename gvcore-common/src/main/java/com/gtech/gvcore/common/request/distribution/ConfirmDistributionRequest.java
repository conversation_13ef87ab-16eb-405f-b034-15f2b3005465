package com.gtech.gvcore.common.request.distribution;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * @ClassName ConfirmDistributionRequest
 * @Description ConfirmDistributionRequest
 * <AUTHOR>
 * @Date 2022/8/11 11:10
 * @Version V1.0
 **/
@Getter
@Setter
public class ConfirmDistributionRequest {

    @ApiModelProperty(value = "Distribution Code", example = "CC001",required = true)
    @NotBlank
    private String distributionCode;

    /**
     * 客户编码
     */
    @ApiModelProperty(value = "Customer Code", example = "CC001", required = true)
    @NotBlank
    private String customerCode;

    /**
     * 当前登录用户编码
     */
    @ApiModelProperty(value = "User Code", example = "UC001", required = true)
    @NotBlank
    private String userCode;
}
