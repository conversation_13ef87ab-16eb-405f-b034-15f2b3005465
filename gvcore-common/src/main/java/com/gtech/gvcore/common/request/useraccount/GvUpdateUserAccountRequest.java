package com.gtech.gvcore.common.request.useraccount;

import java.util.List;

import com.gtech.basic.idm.web.vo.param.UpdateUserAccountParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "GvUpdateUserAccountRequest")
public class GvUpdateUserAccountRequest extends UpdateUserAccountParam {
	/**
	* 
	*/
	private static final long serialVersionUID = 9014461972347975377L;

	@ApiModelProperty(value = "If need double check when login.", example = "1")
	private Integer doubleCheck;

	private List<IssuerPermissionRequest> issuerPermissionList;

}
