package com.gtech.gvcore.common.externalmapping.response;

import com.gtech.gvcore.common.response.transaction.OnetimebarcodeResponse;
import lombok.Data;

@Data
public class OneTimeBarCodeResponse {
    private String voucherNumber;
//    @JsonProperty("CardPin")
    private String voucherPIN;

//    @JsonProperty("Barcode")

    private String barCode;

    private String transactionTimestamp;

    private String barcodeExpiry;

    private String notes;

    private String approvalCode;

    private Integer responseCode;

    private String responseMessage;

    private Integer transactionId;

    private String transactionType;

    private String errorCode;

    private String errorDescription;
//
//    private String apiWebProperties;

    public OneTimeBarCodeResponse setOneTimeBarCodeResponse(OnetimebarcodeResponse response) {
        if (response == null) {
            return this;
        }
        this.setVoucherNumber(response.getCardNumber());
        this.setVoucherPIN(response.getCardPin());
        this.setBarCode(response.getBarCode());
        this.setTransactionTimestamp(response.getTransactionDateTime());
        this.setBarcodeExpiry(response.getPinExpiry());
        this.setNotes(response.getNotes());
        this.setApprovalCode(response.getApprovalCode());
        this.setResponseCode(response.getResponseCode());
        this.setResponseMessage(response.getResponseMessage());
        this.setTransactionId(response.getTransactionId());
        this.setTransactionType(response.getTransactionType());
        this.setErrorCode(response.getErrorCode());
        this.setErrorDescription(response.getErrorDescription());
//        this.setApiWebProperties(response.getApiWebProperties());
        return this;
    }

}
