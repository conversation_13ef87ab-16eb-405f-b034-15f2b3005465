package com.gtech.gvcore.common.request.productcategory;

import java.math.BigDecimal;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年2月24日
 */
@Data
public class DisscountDetailParamter {

    @ApiModelProperty(value = "id", required = true)
    private Long id;

    @ApiModelProperty(value = "fromPurchaseValue", required = true)
    @NotNull(message = "fromPurchaseValue can not be null")
    @Range(min = 0)
    private BigDecimal fromPurchaseValue;

    @ApiModelProperty(value = "uptoPurchaseValue", required = true)
    @NotNull(message = "uptoPurchaseValue can not be null")
    @Range(min = 0)
    private BigDecimal uptoPurchaseValue;

    @ApiModelProperty(value = "discountType", required = true)
    @NotEmpty(message = "discountType can not be empty")
    @Length(max = 50)
    private String discountType;

    @ApiModelProperty(value = "discount", required = true)
    @NotNull(message = "discount can not be null")
    @Range(min = 0)
    private BigDecimal discount;

    @ApiModelProperty(value = "maximumDiscountValue", required = false)
    @Range(min = 0)
    private BigDecimal maximumDiscountValue;

    @ApiModelProperty(value = "status", required = true)
    @NotNull(message = "status can not be null")
    @Range(min = 0, max = 1)
    private Integer status;

}
