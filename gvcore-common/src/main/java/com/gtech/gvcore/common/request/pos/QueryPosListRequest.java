package com.gtech.gvcore.common.request.pos;

import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryPosListRequest")
public class QueryPosListRequest extends PageBean {


    @ApiModelProperty(value = "Pos code.", example = "2123123")
    private String posCode;

    @ApiModelProperty(value = "Pos name.", example = "21123")
    private String posName;

    @ApiModelProperty(value = "Outlet code.", example = "2123123")
    private List<String> outletCodes;

    /*@ApiModelProperty(value = "Issuer code.", example = "2123123")
    private String issuerCode;*/

    @ApiModelProperty(value = "Status.", example = "1")
    private String status;

    @ApiModelProperty(value = "Merchant code.", example = "1")
    private List<String> merchantCodes;


}
