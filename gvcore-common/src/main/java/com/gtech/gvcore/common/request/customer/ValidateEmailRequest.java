package com.gtech.gvcore.common.request.customer;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/23 17:22
 */

@Data
@NoArgsConstructor
public class ValidateEmailRequest implements Serializable {

    private static final long serialVersionUID = -6373743362268129517L;
    @Email(message = "Please enter the correct email address")
    @ApiModelProperty(value = "email", required = true)
    private String email;

    @ApiModelProperty(value = "Email validated code", required = true, example = "675378")
    @NotBlank(message = "validateCode can not be empty")
    private String validatedCode;
}
