package com.gtech.gvcore.common.request.productcategory;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年2月23日
 */
@Data
public class UpdateProductCategoryRequest {

    @ApiModelProperty(value = "id", required = true)
    @NotNull(message = "id can not be null")
    private Long id;

    @ApiModelProperty(value = "categoryName", required = true)
    @NotEmpty(message = "categoryName can not be empty")
    @Length(max = 200)
    private String categoryName;

    @ApiModelProperty(value = "remarks", required = false)
    @Length(max = 500)
    private String remarks;

    @ApiModelProperty(value = "updateUser", required = true)
    @NotEmpty(message = "updateUser can not be empty")
    @Length(max = 50)
    private String updateUser;

}
