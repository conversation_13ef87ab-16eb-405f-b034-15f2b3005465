package com.gtech.gvcore.common.request.outletproductcategory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "UpdateOutletProductCategoryRequest")
public class UpdateOutletProductCategoryRequest {

    @ApiModelProperty(value = "Outlet product category code.", example = "112233",required = true)
    @NotEmpty(message = "outletProductCategoryCode can not be empty")
    @Length(max = 100)
    private String outletProductCategoryCode;

    @ApiModelProperty(value = "Outlet code.", example = "1122333")
    @Length(max = 100)
    private String outletCode;

    @ApiModelProperty(value = "Product category code.", example = "2123123")
    @Length(max = 100)
    private String productCategoryCode;

    @ApiModelProperty(value = "Status.", example = "1")
    private Integer status;

    @ApiModelProperty( value="Update user.", example="user1")
    @Length(max = 100)
    private String updateUser;


}
