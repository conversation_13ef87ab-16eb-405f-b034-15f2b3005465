package com.gtech.gvcore.common.request.customer;

import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryCustomerRequest")
public class QueryCustomerByUserCodeRequest extends PageBean {

    @ApiModelProperty(value = "User Code.", example = "11222334")
    @Length(max = 100)
    private String userCode;




}
