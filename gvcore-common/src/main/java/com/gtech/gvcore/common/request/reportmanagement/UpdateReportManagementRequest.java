package com.gtech.gvcore.common.request.reportmanagement;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/11 18:24
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "UpdateReportManagementRequest", description = "Modify the permission to export PDF and Excel files")
public class UpdateReportManagementRequest {

    @ApiModelProperty(value = "Report management code", required = true, example = "RM102203081916000004")
    @NotBlank(message = "reportManagementCode cannot be empty")
    private String reportManagementCode;
    @ApiModelProperty(value = "export PDF", example = "true", required = true)
    @NotNull(message = "exportPdf cannot be empty")
    private Boolean exportPdf;
    @ApiModelProperty(value = "export Excel", required = true, example = "false")
    @NotNull(message = "exportExcel cannot be empty")
    private Boolean exportExcel;
    @ApiModelProperty(value = "export Excel", required = true, example = "false")
    @NotNull(message = "updateUser cannot be empty")
    private String updateUser;

}
