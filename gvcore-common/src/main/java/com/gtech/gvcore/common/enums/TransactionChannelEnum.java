package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @Date 2022/11/24 13:51
 */
public enum TransactionChannelEnum {


    SYSTEM("0","SYSTEM"),
    API("1","API");


    private final String code;

    private final String desc;

    TransactionChannelEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    public static String getTypeDesc(String code) {

        for (TransactionChannelEnum typeEnum : TransactionChannelEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum.getDesc();
            }
        }
        return null;
    }

}
