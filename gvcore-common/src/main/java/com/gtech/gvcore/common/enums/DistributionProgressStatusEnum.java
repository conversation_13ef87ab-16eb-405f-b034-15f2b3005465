package com.gtech.gvcore.common.enums;

/**
 * @ClassName DistributionStatusEnum
 * @Description 分发状态
 * <AUTHOR>
 * @Date 2022/8/9 11:02
 * @Version V1.0
 **/
public enum DistributionProgressStatusEnum implements IEnum<String> {

    UNCONFIRMED("Unconfirmed", "未提交"),
    SUBMITTED("Submitted", "已提交"),
    DISTRIBUTING("Distributing", "分发中"),
    DISTRIBUTED("Distributed", "分发完成"),
    ;

    // UNCONFIRMED -> SUBMITTED -> DISTRIBUTING -> DISTRIBUTED

    private final String code;
    private final String desc;

    DistributionProgressStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String code() {
        return this.code;
    }

    @Override
    public String desc() {
        return this.desc;
    }

    @Override
    public boolean equalsCode(final String code) {
        return this.code().equals(code);
    }

}
