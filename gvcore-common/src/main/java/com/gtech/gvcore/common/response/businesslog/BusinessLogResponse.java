package com.gtech.gvcore.common.response.businesslog;

import com.gtech.gvcore.common.response.businesslogdetail.BusinessLogDetailResponse;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/3/11 17:16
 */
@Data
public class BusinessLogResponse {

    private String businessCode;

    private Integer status;

    private Long success;

    private Long failed;

    private String contentCode;

    private String content;

    private List<BusinessLogDetailResponse> businessLogDetailList;

    private String createUser;

    private Date createTime;

    private String updateUser;

    private Date updateTime;





}
