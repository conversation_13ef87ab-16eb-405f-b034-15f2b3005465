package com.gtech.gvcore.common.enums;

public enum FlowNodeEnum {

	CUSTOMER_CREATED("customer_create", "Created new"),
	CREATED("created", "Created by op"),
    SUBMIT("submit", "Submit"),
	APPROVE("approve", "Approved"),
    ALLOCATION("allocation", "Allocation"),
	ISSUANCE("issuance", "Issuance"),
    RELEASE("release", "Release"),
    RECEIVE("receive", "Receive"),
	DELIVER("deliver", "Deliver"),
	EXECUTE("execute", "Execute"),
    COMPLETED("completed", "Completed"),
	REJECTED("rejected", "Rejected"),
    CANCELED("cancel", "Canceled"),
    ;


    private final String code;

    private final String desc;

    FlowNodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getByCode(String code) {
    	for(FlowNodeEnum flowNodeEunm : FlowNodeEnum.values()) {
    		if (flowNodeEunm.getCode().equals(code)) {
    			return flowNodeEunm.getDesc();
    		}
    	}
    	return null;
    }
}
