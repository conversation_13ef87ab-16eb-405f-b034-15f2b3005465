package com.gtech.gvcore.common.request.voucher;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/3/11 15:53
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "SendVoucherRequest")
public class SendVoucherRequest implements Serializable {
    private static final long serialVersionUID = -2497317088016217669L;


    @ApiModelProperty(value = "Voucher batch code.",required = true)
    @NotEmpty(message = "voucherBatchCode can not be empty")
    private String voucherBatchCode;

    @ApiModelProperty(value = "Create user.",required = true)
    @NotEmpty(message = "createUser can not be empty")
    private String createUser;

    @ApiModelProperty(value = "Send mail only.")
    private Boolean sendMailOnly = false;



}
