package com.gtech.gvcore.common.request.posaccount;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CreatePosAccountRequest")
public class CreatePosAccountRequest {



    @ApiModelProperty(value = "Outlet type code.", example = "2123123",required = true)
    @NotEmpty(message = "outletType can not be empty")
    @Length(max = 100)
    private String outletType;

    @ApiModelProperty(value = "Pos account code.", example = "2123123",required = true)
    @NotEmpty(message = "posAccount can not be empty")
    @Length(max = 100)
    private String posAccount;

    @ApiModelProperty(value = "Pos password code.", example = "2123123",required = true)
    @NotEmpty(message = "posPassword can not be empty")
    @Length(max = 100)
    private String posPassword;

    @ApiModelProperty(value = "Create user.", example = "user123",required = true)
    @NotEmpty(message = "createUser can not be empty")
    private String createUser;


    @ApiModelProperty(value = "Create time.", example = "2020-03-12 12:12:12")
    private Date createTime;



}
