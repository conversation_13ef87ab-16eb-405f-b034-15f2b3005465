package com.gtech.gvcore.common.enums;

/**
 **/
public enum GvPosCommonResponseCodesEnum {
	BATCH_CLOSE_FAILED(99, "Batch Closed",
			""),
	VOUCHER_NO_GR_OR_NO_ALLOCATED(10020, "Voucher has not been GR/Allocated to the store",
			""),
	AUTHORIZATION_FAILED(10008, "Authorization failed",
			"This error indicates that some of the mandatory input parameters are wrong or User performing the transaction does not have enough permission to perform the transaction."),
	SECURITY_CHECK_FAILED(10009, "Security check failed",
			"This error indicates that the User performing the transaction does not have enough permission to perform the transaction"),
	TRANSACTION_FAILED(10018, "Transaction failed",
			"This error indicates that the transaction failed due to some critical server error for the current set of input request."),
	TRANSACTION_FAILED_1(10012, "Transaction failed",
			"This error indicates that the transaction failed due to a business error"),
	TRANSACTION_ID_CHECK_FAILED(10019, "Transaction Id check failed.",
			"This error indicates that the Transaction ID that is passed with Every transaction is out of sequence for the current batch. When this error occurs, one is recommended to perform Batch close and restart a new Batch."),
	INVALID_BATCH(10064, "Invalid batch.",
			"This error will occur when same TerminalID is used to Initialize multiple instances of application."),
	INACTIVE_POS(10120, "Inactive pos.", "POS has been deactivated at server"),
	INVALID_TERMINALID(10121, "Invalid POS Id.",
			"POS Id passed in Authorization request does not exist in system"),
	INVALID_USERNAME_OR_PASSWORD(10123, "Invalid username or password.",
			"The user credentials passed in the transaction are invalid"),
	MERCHANT_OUTLET_AUTH_FAILED(10126, "Merchant Store Auth failed.",
			"User is not having permission to perform the transaction from this store"),
	MERCHANT_OUTLET_AUTH_FAILED_1(10138, "User not authorized to login to this store.",
			"User is not having permission to perform the transaction from this store"),
	ACCESS_DENIED_FOR_PERFORMING_THE_TRANSACTION(10128, "Access denied for performing the transaction.", null),
	MERCHANT_AUTH_FAILED(10129, "Merchant auth failed.",
			"Merchant is not allowed to perform the specific transaction based on server configuration"),
	USER_DOES_NOT_EXIST(10131, "User does not exist.", "Invalid user credentials"),
	THE_USER_HAS_BEEN_DISABLED(10136, "The user has been disabled.", null),
	USER_AUTHORIZATION_FOR_MERCHANT_FAILED(10137, "User authorization for merchant failed.", null),
	CARD_ALREADY_ACTIVE(10015, "Voucher already active.", null),
	CARD_ALREADY_REDEEMED(10251, "Voucher is already redeemed.", null),
	CARD_EXPIRED(10001, "Voucher expired.", "Applicable for all APIs except CreateAndIssue"),
	//CARD_IS_DEACTIVATED(10002, "Card is deactivated", "Card is already moved to deactivated state."),
	CARD_IS_DEACTIVATED(10027, "Voucher is deactivated.", "Voucher is deactivated."),
	CARD_NOT_ACTIVATED(10029, "Voucher not activated.", "Applicable for all APIs except issuance"),
	CARD_NUMBER_PASSED_IS_INVALID(10004, "Could not find a voucher",
			"Voucher number passed is invalid"),
	INVALID_PREAUTH_CODE(10344, "Invalid PreAuth Code", null),
	PREAUTH_ALREADY_COMPLETED(10345, "PreAuth already completed", null),
	PREAUTH_ALREADY_CANCELED(10346, "PreAuth already canceled", null),
	CANNOT_PROCESS_TRANSACTION(10347, "Cannot process transaction, input mismatch", null),
	TOTAL_BILL_AMOUNT_SHOULD_BE_PASSED_TO_PROCEED_WITH_THE_PREAUTH_TRANSACTION(10351,
			"Total Bill Amount should be passed to proceed with the preauth transaction", null),
	OUTLET_NAME_IS_MANDATORY(30001, "Outlet Name is mandatory.", null),
	OUTLET_ALREADY_EXISTS(30002, "Outlet already exists.", null),
	ADDRESSLINE1_IS_MANDATORY(30003, "AddressLine1 is mandatory.", null),
	CONTACT_FIRST_NAME_IS_MANDATORY(30004, "Contact First Name is mandatory.", null),
	CONTACT_LAST_NAME_IS_MANDATORY(30005, "Contact last Name is mandatory.", null),
	PHONE_NUMBER_IS_MANDATORY(30006, "Phone Number is mandatory.", null),
	CITY_IS_MANDATORY(30007, "City is mandatory.", null), STATE_IS_MANDATORY(30008, "State is mandatory.", null),
	COUNTRY_IS_MANDATORY(30009, "Country is mandatory.", null),
	PINCODE_IS_MANDATORY(30010, "PinCode is mandatory.", null), AREA_IS_MANDATORY(30011, "Area is mandatory.", null),
	INVOICENUMBERPREFIX_IS_MANDATORY(30012, "InvoiceNumberPrefix is mandatory.", null),
	TIMEZONE_DOES_NOT_EXISTS(30013, "Timezone does not exists.", null),
	MERCHANTOUTLETTYPE_DOES_NOT_EXISTS(30014, "MerchantOutletType does not exists.", null),
	MERCHANT_IS_MANDATORY(30015, "Merchant is mandatory.", null),
	MERCHANTOUTLETCODE_IS_MANDATORY(30016, "MerchantOutletCode is mandatory.", null),
	MERCHANTOUTLETCODE_ALREADY_EXISTS(30017, "MerchantOutletCode already exists.", null),
	REFERENCE_NUMBER_IS_MANDATORY(30018, "Reference Number is mandatory.", null),
	INVALID_MERCHANT(30019, "Invalid Merchant.", null),
	INVALID_OUTLET_STATUS(30020, "Invalid Outlet Status.", null),
	INVALID_BARCODE(10874, "Invalid Barcode.", null),

	VALIDATION_FAILED(10838, "Validation Failed.", null),
	INVALID_LINEITEM(10855, "Invalid number of vouchers per voucher item.", "Invalid number of vouchers per voucher item."),
	REDEMPTION_FAILED(10011, "Redemption failed.", null),
	TRANSACTION_SUCCESS(0, "Transaction successful.", null),
	EXCEEDING_THE_MAXIMUM_COUPON_QUANTITY_LIMIT(10745, "Number of voucher has reached Max limit", null),
	AUTHORIZATION_FAILED_10744(10744, "Authorization Token Expired.",
			"This error indicates authorization token expired, need refresh authorize token."),

	;

	private final int responseCode;

	private final String responseMessage;
	private final String comment;

	GvPosCommonResponseCodesEnum(int responseCode, String responseMessage, String comment) {
		this.responseCode = responseCode;
		this.responseMessage = responseMessage;
		this.comment = comment;
	}

	public int getResponseCode() {
		return responseCode;
	}

	public String getResponseMessage() {
		return responseMessage;
	}

	public String getComment() {
		return comment;
	}
}
