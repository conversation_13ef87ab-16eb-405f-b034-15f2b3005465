package com.gtech.gvcore.common.response.dashboard;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/3/10 13:53
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "RedemptionOtherDto")
public class RedemptionOtherDto {

    private BigDecimal denomination;
    private Date voucherEffectiveDate;
    private String transactionType;
    private String status;
    private String voucherStatus;



}
