package com.gtech.gvcore.common.response.orderreport;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/11 14:01
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "QueryReportFileUrlResponse", description = "Url list")
public class QueryReportFileUrlResponse implements Serializable {

    private static final long serialVersionUID = -3653236262983460415L;
    private String reportFileType;

    private String reportFileUrl;

    private String orderReportCode;

}
