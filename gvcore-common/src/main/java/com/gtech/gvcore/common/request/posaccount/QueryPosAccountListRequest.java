package com.gtech.gvcore.common.request.posaccount;


import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryPosAccountListRequest")
public class QueryPosAccountListRequest extends PageBean {


    @ApiModelProperty(value = "Outlet type.", example = "2123123")
    @Length(max = 100)
    private String outletType;

    @ApiModelProperty(value = "Pos account.", example = "2123123")
    @Length(max = 100)
    private String posAccount;

    @ApiModelProperty(value = "Pos password.", example = "2123123")
    @Length(max = 100)
    private String posPassword;





}
