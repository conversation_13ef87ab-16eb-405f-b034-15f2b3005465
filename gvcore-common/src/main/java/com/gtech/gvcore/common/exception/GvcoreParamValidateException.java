/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR <PERSON><PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.gvcore.common.exception;

import com.gtech.commons.exception.ErrorCode;
import com.gtech.commons.exception.GTechBaseException;


/**
 * ParamValidateException
 */
public class GvcoreParamValidateException extends GTechBaseException {

    // serialVersionUID
    private static final long serialVersionUID = 2067851577237203106L;

    /**
     * Constructor without parameters.
     */
    public GvcoreParamValidateException() {

        super();
    }

    /**
     * Constructor with Throwable.
     */
    public GvcoreParamValidateException(Throwable cause) {

        super(cause);
    }

    /**
     * Constructor with message and Throwable.
     */
    public GvcoreParamValidateException(String message, Throwable cause, Object...args) {

        super(message, cause, args);
    }

    /**
     *Constructor with code and message.
     */
    public GvcoreParamValidateException(String code, String message, Object...args) {

        super(code, message, args);
    }

    /**
     * Constructor with code, message and Throwable.
     */
    public GvcoreParamValidateException(String code, String message, Throwable cause, Object...args) {

        super(code, message, cause, args);
    }

    /**
     * Constructor with ErrorCode.
     */
    public GvcoreParamValidateException(ErrorCode errorInfo, Object...args) {

        super(errorInfo, args);
    }

    /**
     * Constructor with ErrorCode and Throwable.
     */
    public GvcoreParamValidateException(ErrorCode errorInfo, Throwable cause, Object...args) {

        super(errorInfo, cause, args);
    }

}
