package com.gtech.gvcore.common.request.voucherreturnantransfer;

import com.gtech.gvcore.common.request.voucherrequest.CreateVoucherRequestDetailsRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/25 13:05
 */

@Data
@ApiModel("Create voucher return or transfer")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CreateVoucherReturnOrTransferRequest implements Serializable {

    private static final long serialVersionUID = -2220954960360312482L;
    @ApiModelProperty(value = "If select HO vault input return or Store input transfer", required = true, example = "return")
    @NotBlank(message = "business type can not be empty")
    private String businessType;
    @NotBlank(message = "Issuer code can not be empty")
    @ApiModelProperty(value = "Issuer code", required = true, example = "IS12431242124")
    private String issuerCode;
    @NotBlank(message = "voucherOwnerCode cannot be empty")
    @ApiModelProperty(value = "voucherOwnerCode from Store Name code.", example = "1122333", required = true)
    private String voucherOwnerCode;
    @NotBlank(message = "voucherOwnerName cannot be empty")
    @ApiModelProperty(value = "voucherOwnerName from Store Name", example = "MAP", required = true)
    private String voucherOwnerName;
    @NotBlank(message = "receiverCode cannot be empty")
    @ApiModelProperty(value = "receiverCode from HO vault or Store name code", example = "OU102203071410000010", required = true)
    private String receiverCode;
    @NotBlank(message = "receiverName cannot be empty")
    @ApiModelProperty(value = "receiverName from HO vault or Store name", example = "MAP", required = true)
    private String receiverName;
    @ApiModelProperty(value = "stateCode")
    private String stateCode;
    @ApiModelProperty(value = "cityCode")
    private String cityCode;
    @ApiModelProperty(value = "districtCode")
    private String districtCode;
    @ApiModelProperty(value = "address1")
    private String address1;
    @ApiModelProperty(value = "email")
    private String email;
    @ApiModelProperty(value = "phone")
    private String phone;
    @ApiModelProperty(value = "mobile")
    private String mobile;
    @ApiModelProperty(value = "requestRemarks")
    private String requestRemarks;
    @NotNull(message = "Please enter the quantity")
    @ApiModelProperty(value = "Number of Vouchers Total", example = "4000")
    private Integer voucherNum;
    @NotNull(message = "Please enter the quantity")
    @ApiModelProperty(value = "Voucher Amount Total ", example = "1200000000")
    private BigDecimal voucherAmount;
    @ApiModelProperty(value = "Currency code")
    private String currencyCode;
    @ApiModelProperty(value = "Permission code.", example = "1122333")
    private String permissionCode;
    @NotBlank(message = "The creator cannot do without")
    @ApiModelProperty(value = "Create user")
    private String createUser;
    @Size(min = 1, message = "Pass at least one piece of data")
    @ApiModelProperty(value = "Create voucher request details requests", required = true)
    private List<CreateVoucherRequestDetailsRequest> detailsRequests;

}
