package com.gtech.gvcore.common.request.releaseapprove;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/6 18:02
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryLogByBusinessCodeRequest", description = "Query log by business code")
public class QueryLogByBusinessCodeRequest {
    @ApiModelProperty(name = "businessCode", value = "String", required = true, example = "REQ12132412")
    @NotBlank(message = "businessCode can not be empty")
    private String businessCode;
}
