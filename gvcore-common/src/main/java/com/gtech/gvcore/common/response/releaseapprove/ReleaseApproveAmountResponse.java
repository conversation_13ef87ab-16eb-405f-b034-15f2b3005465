package com.gtech.gvcore.common.response.releaseapprove;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/16 16:54
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("Release approve range list")
public class ReleaseApproveAmountResponse {
    @ApiModelProperty(value = "Range name ", example = "1")
    private Integer rangeName;
    @ApiModelProperty(value = "type ", example = "ReleaseApproveConfig")
    private String type;
    @ApiModelProperty(value = "Start num ", example = "1")
    private BigDecimal startNum;
    @ApiModelProperty(value = "End num ", example = "200")
    private BigDecimal endNum;
    @ApiModelProperty(value = "Release approve amount code ", example = "1")
    private String releaseApproveAmountCode;
    @ApiModelProperty(value = "Release approve node list ")
    private List<ReleaseApproveNodeResponse> releaseApproveNodeResponseList;

}
