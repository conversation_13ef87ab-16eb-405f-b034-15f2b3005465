package com.gtech.gvcore.common.response.pos;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "PosCpgResponse")
public class PosCpgResponse {



    @ApiModelProperty(value = "Cpg code.", example = "2123123")
    private String cpgCode;


    @ApiModelProperty(value = "Cpg name.", example = "2123123")
    private String cpgName;

}
