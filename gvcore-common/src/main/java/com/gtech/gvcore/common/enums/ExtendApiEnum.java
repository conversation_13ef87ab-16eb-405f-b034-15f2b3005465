package com.gtech.gvcore.common.enums;

import org.apache.commons.lang3.StringUtils;
public enum ExtendApiEnum {


	AUTHORIZE(1, "/gv/external/authorize"),
	BATCHCLOSE(2, "/gv/external/batchclose"),
	ACTIVATEONLY(3, "/gv/external/gc/activateonly"),
	TRANSACTION(4, "/gv/external/gc/bulk/transaction"),
	CANCELACTIVATE(5, "/gv/external/gc/cancelactivate"),
	CANCELCREATEANDISSUE(6, "/gv/external/gc/cancelcreateandissue"),
	CANCELREDEEM(7, "/gv/external/gc/cancelredeem"),
	CARDTRANSACTIONHISTORY(8, "/gv/external/gc/cardtransactionhistory"),
	CREATEANDISSUE(9, "/gv/external/gc/createandissue"),
	ONETIMEBARCODE(10, "/gv/external/gc/onetimebarcode"),
	GET_BASIC_VOUCHER_INFORMATION(11, "/gv/external/getVoucherInformation"),
	AUTHORIZE_V3(12, "/gv/external/v3/authorize"),
	TRANSACTION_V3(13, "/gv/external/v3/gc/transactions");

	private Integer type;
	private String url;

	ExtendApiEnum(Integer type, String url) {
		this.type = type;
		this.url = url;
    }

	public int getType() {
		return type;
    }

	public String getUrl() {
		return url;
    }


	public static Integer getType(String url) {

		if (StringUtils.isEmpty(url))
			return null;
        ExtendApiEnum[] values = ExtendApiEnum.values();
        for (ExtendApiEnum value : values) {
			if (String.valueOf(value.url).equals(url)) {
				return value.getType();
            }
        }
        return null;

    }

}
