package com.gtech.gvcore.common.request.customer;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "UpdateCustomerStatusRequest")
public class UpdateCustomerStatusRequest implements Serializable {

    private static final long serialVersionUID = -3803613938787564315L;

    @ApiModelProperty(value = "Customer code.", example = "11222334", required = true)
    @NotEmpty(message = "customerCode can not be empty")
    @Length(max = 100)
    private String customerCode;

    @ApiModelProperty(value = "Status.", example = "1")
    private Integer status;

    @ApiModelProperty( value="Update user.", example="user1")
    @Length(max = 100)
    private String updateUser;

}
