package com.gtech.gvcore.common.request.productcategory;

import javax.validation.constraints.NotEmpty;

import com.gtech.gvcore.common.request.base.PageBean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022年2月23日
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QueryProductCategoryByPageRequest extends PageBean {

    @ApiModelProperty(value = "categoryName", required = false)
    private String categoryName;

    @ApiModelProperty(value = "issuerCode", required = true)
    @NotEmpty(message = "issuerCode can not be empty")
    private String issuerCode;

}
