package com.gtech.gvcore.common.request.voucher;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "QueryCheckVoucherRequest")
public class GetStartAndEndVoucherRequest {
	
	@ApiModelProperty(value = "Booklet start code.", notes ="Booklet start code. Bookletstartno and booklet end code must have values at the same time", example = "9001220000000001")
    private String bookletStartNo;

    @ApiModelProperty(value = "Booklet end code.", notes ="Booklet end code. Bookletstartno and booklet end code must have values at the same time", example = "9001220000000100")
    private String bookletEndNo;

    @ApiModelProperty(value = "Voucher start code.",notes ="Voucher start code. Bookletstartno and booklet end code must have values at the same time", example = "1002222220000001")
    private String voucherStartNo;

    @ApiModelProperty(value = "Voucher end code.",notes ="Voucher end code. Bookletstartno and booklet end code must have values at the same time", example = "1002222220003000")
    private String voucherEndNo;
    
    private List<String> voucherCodeList;
    
    private List<String> bookletCodeList;

    private String checkType;

    private String voucherReceiveCode;
    private String customerOrderCode;
}
