package com.gtech.gvcore.common.enums;

import com.gtech.commons.code.IEnum;

/**
 * @ClassName RepeatEndEnum
 * @Description 定时任务截止方式枚举
 * <AUTHOR>
 * @Date 2022/8/18 10:56
 * @Version V1.0
 **/
public enum RepeatEndEnum implements IEnum {

    /**
     * 永不停止
     */
    NEVER("never", "never execute"),
    /**
     * 到某个日期之后
     */
    ON_DATE("onDate", "certain time to stop"),

    /**
     * 执行一定次数
     */
    AFTER("after", "after")
    ;
    public static final String REMARK = "";

    RepeatEndEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    //code
    private final String code;

    //desc
    private final String desc;

    public static RepeatEndEnum valueOfCode(String code) {
        for (RepeatEndEnum value : RepeatEndEnum.values()) {
            if (value.code().equals(code)) {
                return value;
            }
        }
        return null;
    }

    @Override
    public String code() {

        return String.valueOf(code);
    }

    @Override
    public String desc() {

        return desc;
    }

    @Override
    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

}
