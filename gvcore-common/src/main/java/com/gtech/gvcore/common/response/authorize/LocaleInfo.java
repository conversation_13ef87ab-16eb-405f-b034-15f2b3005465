package com.gtech.gvcore.common.response.authorize;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class LocaleInfo {

	@ApiModelProperty(value = "Culture.", required = true, notes = "configured culture.")
	private String culture;
	@ApiModelProperty(value = "Currency Symbol.", required = true, notes = "currency symbol configured")
	private String currency;
	@ApiModelProperty(value = "Currency Position.", notes = "currency position configured")
	private Integer currencyPosition;
	@ApiModelProperty(value = "Currency Decimal Digits.", notes = "configured currency decimal digits")
	private Integer currencyDecimalDigits;
	@ApiModelProperty(value = "Display Unit For Points.", notes = "configured display unit for points.")
	private String displayUnitForPoints;

}