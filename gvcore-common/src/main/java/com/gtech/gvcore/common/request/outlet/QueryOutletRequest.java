package com.gtech.gvcore.common.request.outlet;

import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryOutletRequest")
public class QueryOutletRequest extends PageBean {

    @ApiModelProperty(value = "Outlet Code.", example = "12341254")
    @Length(max = 100)
    private String outletCode;

    @ApiModelProperty(value = "Outlet Code list.")
    private List<String> outletCodeList;

    @ApiModelProperty(value = "Outlet name.", example = "MAP")
    @Length(max = 100)
    private String outletName;

    @ApiModelProperty(value = "Merchant code list.", example = "1231254125")
    private List<String> merchantCodeList;

    @ApiModelProperty(value = "Merchant code .", example = "1231254125")
    private String merchantCode;

    @ApiModelProperty(value = "Outlet type", example = "MVStore")
    private String outletType;

    @ApiModelProperty(value = "Issuer code.", example = "1231254125")
    private String issuerCode;

    @ApiModelProperty(value = "Status.", example = "1")
    private Integer status;


}
