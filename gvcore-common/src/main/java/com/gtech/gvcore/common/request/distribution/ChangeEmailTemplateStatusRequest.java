package com.gtech.gvcore.common.request.distribution;

import com.gtech.gvcore.common.enums.DisEmailTemplateStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @ClassName DistributionEmailTemplateCodeRequest
 * @Description 变更邮件模板状态参数
 * <AUTHOR>
 * @Date 2022/7/5 15:59
 * @Version V1.0
 **/
@Getter
@Setter
@ApiModel(value = "ChangeEmailTemplateStatusRequest")
public class ChangeEmailTemplateStatusRequest {

    @ApiModelProperty(value = "邮件编码", example = "ET001", required = true)
    @NotEmpty(message = "Template code can not be empty")
    private String templateCode;

    @ApiModelProperty(value = DisEmailTemplateStatusEnum.REMARK, example = "1", required = true)
    @NotNull(message = "Template code can not be null")
    private Integer status;

    @ApiModelProperty(value = "客户编码", example = "UC001", required = true)
    @NotEmpty(message = "Customer code can not be empty")
    private String customerCode;

    @ApiModelProperty(value = "用户编码", example = "UC001", required = true)
    @NotEmpty(message = "User code can not be empty")
    private String userCode;

}
