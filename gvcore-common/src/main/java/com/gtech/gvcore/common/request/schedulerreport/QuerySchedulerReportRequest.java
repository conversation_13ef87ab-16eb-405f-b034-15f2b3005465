package com.gtech.gvcore.common.request.schedulerreport;

import com.gtech.commons.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/2 18:06
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "Query scheduler report request")
public class QuerySchedulerReportRequest extends PageParam implements Serializable {

    private static final long serialVersionUID = 2647615398522643501L;

    @ApiModelProperty(value = "Scheduler name")
    private String schedulerName;

    @ApiModelProperty(value = "Report type")
    private String reportType;

    @ApiModelProperty(value = "frequency")
    private String frequency;

    @ApiModelProperty(value = "Status")
    private Boolean status;
}
