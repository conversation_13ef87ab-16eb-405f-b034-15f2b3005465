package com.gtech.gvcore.common.request.productcategory;

import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年2月24日
 */
@Data
public class QueryProductCategoryDisscountRequest {

    @ApiModelProperty(value = "productCategoryCode", required = true)
    @NotEmpty(message = "productCategoryCode can not be empty")
    private String productCategoryCode;

}
