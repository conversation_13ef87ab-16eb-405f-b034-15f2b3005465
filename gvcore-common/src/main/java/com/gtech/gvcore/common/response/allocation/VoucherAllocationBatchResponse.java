package com.gtech.gvcore.common.response.allocation;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

@Data
public class VoucherAllocationBatchResponse implements Serializable {

    /**
	 * 
	 */
	private static final long serialVersionUID = -8320148335300131015L;

	/**
	 * cpg code
	 */
    private String cpgCode;

	private String cpgName;

    /**
     * voucher start NO
     */
    private String voucherStartNo;

    /**
     * voucher end NO
     */
    private String voucherEndNo;

    /**
     * number of vouchers
     */
    private Integer voucherNum;

    /**
     * denomination
     */
    private BigDecimal denomination;

    /**
     * received number of vouchers
     */
    private Integer receivedNum;
    
    /**
     * booklet start NO
     */
    private String bookletStartNo;

    /**
     * booklet end NO
     */
    private String bookletEndNo;

    /**
     * create user
     */
    private String createUser;

    /**
     * create time
     */
    private Date createTime;

}