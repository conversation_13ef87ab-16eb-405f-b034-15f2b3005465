package com.gtech.gvcore.common.request.articlemop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 获取商品支付方式关联请求
 */
@Data
@ApiModel(value = "GetArticleMopRequest", description = "获取商品支付方式关联请求")
public class GetArticleMopRequest {
    /**
     * 商品支付方式关联编码
     */
    @ApiModelProperty(value = "商品支付方式关联编码", required = true)
    @NotBlank(message = "商品支付方式关联编码不能为空")
    @Size(max = 50)
    private String articleMopCode;
} 