package com.gtech.gvcore.common.request.transaction;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class PaymentInfo {
	@ApiModelProperty(value = "Payment Mode Id. ", required = true, example = "1", notes = "550 CHECK 551 CREDIT CARD 552 DEBIT CARD 553 CASH 554 DEMAND DRAFT 555 CREDIT SALE 556 OTHERS")
	private Integer paymentModeId;
	@ApiModelProperty(value = "Payment Details. ", required = true, example = "1", notes = "Payment details")
	@Length(max = 1000, message = "Payment Details maximum length 1000")
	private String paymentDetails;

}