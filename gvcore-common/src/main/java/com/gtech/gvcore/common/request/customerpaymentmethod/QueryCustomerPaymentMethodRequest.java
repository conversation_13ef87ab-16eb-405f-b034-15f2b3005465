package com.gtech.gvcore.common.request.customerpaymentmethod;

import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryCustomerPaymentMethodRequest")
public class QueryCustomerPaymentMethodRequest extends PageBean {

    @ApiModelProperty(value = "Customer payment method code", example = "112233")
    @Length(max = 100)
    private String customerPaymentMethodCode;

    @ApiModelProperty(value = "Customer code", example = "112233")
    @Length(max = 100)
    private String customerCode;

    @ApiModelProperty(value = "Mop group", example = "1")
    @Length(max = 100)
    private String mopGroup;

    @ApiModelProperty(value = "Status.", example = "1")
    private Integer status;


}
