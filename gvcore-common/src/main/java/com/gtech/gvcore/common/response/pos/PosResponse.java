package com.gtech.gvcore.common.response.pos;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "PosResponse")
public class PosResponse {


    @ApiModelProperty(value = "Pos code.", example = "2123123")
    private String posCode;


    @ApiModelProperty(value = "Issuer code.", example = "2123123")
    private String issuerCode;


    @ApiModelProperty(value = "Pos name.", example = "Pos1")
    private String posName;


    @ApiModelProperty(value = "Machine id", example = "2123123")
    private String machineId;

    @ApiModelProperty(value = "Pos entry mode id.", example = "2123123")
    private String posEntryModeId;


    @ApiModelProperty(value = "Outlet code.", example = "2123123")
    private String outletCode;

    @ApiModelProperty(value = "Outlet name.", example = "2123123")
    private String outletName;


    @ApiModelProperty(value = "Status.", example = "1")
    private Integer status;


    @ApiModelProperty(value = "Create user.", example = "user123")
    private String createUser;

    @ApiModelProperty(value = "Cpg list.", example = "user123")
    private List<PosCpgResponse> cpg;

    @ApiModelProperty(value = "Create time.", example = "2020-03-12 12:12:12")
    private Date createTime;

    @ApiModelProperty(value = "Update user.", example = "user22")
    private String updateUser;

    @ApiModelProperty(value = "Update time.", example = "2020-03-12 12:12:12")
    private Date updateTime;

    @ApiModelProperty(value = "Account.", example = "********")
    private String account;

    @ApiModelProperty(value = "Password.", example = "7457457")
    private String password;

    @ApiModelProperty( value = "Merchant name",example = "123")
    private String merchantName;


}
