package com.gtech.gvcore.common.request.voucher;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2022/7/29 10:09
 */
@Data
public class GetVoucherInformationRequest {

    @ApiModelProperty(name = "Voucher code",required = true)
    @NotNull(message = "The voucherCode cannot be empty")
    private String voucherNumber;


}
