package com.gtech.gvcore.common.request.releaseapprove;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/2 15:02
 */


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateLogRecode {

    private String businessCode;
    private String approveRoleCode;
    /**
     * Then get the data based on the type and businessCode
     * 后面根据这个type和businessCode获取数据
     * example：VoucherRequest-approve
     */
    private String approveType;
    private String note;
    private String approveUser;
    /**
     * Status:  true:agree  false:reject
     */
    private Boolean status;
}
