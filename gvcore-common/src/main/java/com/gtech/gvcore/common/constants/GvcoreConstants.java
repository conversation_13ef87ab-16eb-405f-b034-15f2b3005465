package com.gtech.gvcore.common.constants;

public class GvcoreConstants {


    private GvcoreConstants() {
        // Empty
    }

	public static final String EMAIL_DATE_FORMAT = "dd-MM-yyyy HH:mm";

	public static final String OPERATE_METHOD_ISSUANCE = "ISSUANCE";

	public static final String OPERATE_METHOD_SUBMIT = "SUBMIT";

    public static final String APP_CODE = "GV";

    public static final String TITAN_DOMAIN_CODE = "GV";

    public static final String SYSTEM_DEFAULT = "SYSTEM_DEFAULT";

    public static final Integer STATUS_DISABLE = 0;

    public static final Integer STATUS_ENABLE = 1;

    public static final Integer DELETE_STATUS_DISABLE = 0;

    public static final Integer DELETE_STATUS_ENABLE = 1;

    public static final String MAP_ISSUER_CODE_KEY = "MAP_ISSUER_CODE";

    public static final String BARCODE_EXPIRATION = "BARCODE_EXPIRATION";

    public static final String ISSUE_HANDLING_FILE_TYPE = "csv";

    /**
     * 实体券
     */
    public static final String MOP_CODE_VCR = "VCR";
    /**
     * 电子券
     */
    public static final String MOP_CODE_VCE = "VCE";
    /**
     * 礼品卡
     */
    public static final String MOP_CODE_GC = "GC";

    public static final String SALES = "sales";
    public static final String RETURN = "return";
    public static final String TRANSFER = "transfer";
    //Automatic approval in Approve setting
    public static final String AUTOMATIC_APPROVE = "AUTOMATIC_APPROVE";

    public static final String MESSAGE_URL = "/gvcore/message/engine/send";

    public static final String CUSTOMER_ORDER_STATUS = "CUSTOMER_ORDER_STATUS";
    public static final String PRODUCT_CATEGORY_DISCOUNT_TYPE = "product_category_discount_type";

    public static final String SUCCESS = "0";
    public static final String FAIL = "1";

}
