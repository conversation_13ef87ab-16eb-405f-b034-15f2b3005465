package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/17 17:00
 */

public enum CustomerOrderEnum {


    CREATED("Created", "Pending Submit",10),
    SUBMIT("Submit", "Pending Approve",20),
    APPROVAL("Approve", "Pending Issuance",30),
    ISSUANCE("Issuance", "Pending Release",40),
    RELEASE("Release", "Pending Receive",50),
    API("Source Api", "Source Api",51),
    RECEIVE("Receive", "Pending Deliver",60),
	DELIVER("Deliver", "Pending Completed",70),
    COMPLETED("Completed", "Completed",80),
    REJECTED("Rejected", "Rejected",90),
    CANCELED("Cancel", "Canceled",100),
    ;


    private final String status;

    private final String desc;

    private final int sort;

    CustomerOrderEnum(String status, String desc ,int sort) {
        this.status = status;
        this.desc = desc;
        this.sort = sort;
    }

    public String getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    public static CustomerOrderEnum valueOfCode(final String orderStats) {
        for (CustomerOrderEnum value : CustomerOrderEnum.values()) {
            if (value.status.equals(orderStats)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 判断当前枚举的排序是否在指定 status 之前或与之相等
     * @param status 用来比较的枚举
     * @return boolean
     * <AUTHOR>
     * @date   2022/7/12 13:44
     * @since  1.0.0
     */
    public boolean beforeOrEquals(final CustomerOrderEnum status) {
        return null != status && this.sort <= status.sort;
    }
}
