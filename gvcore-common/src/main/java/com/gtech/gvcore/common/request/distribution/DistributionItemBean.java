package com.gtech.gvcore.common.request.distribution;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * @ClassName SaveDistributionItemRequest
 * @Description 保存分发E-GV Distribution Information参数
 * <AUTHOR>
 * @Date 2022/8/9 11:22
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "DistributionItemBean")
public class DistributionItemBean {

    @ApiModelProperty(value = "Cpg Code", example = "CPG001", required = true)
    @NotBlank
    private String cpgCode;

    @ApiModelProperty(value = "Cpg Expiry Date.Format: yyyyMMddHHmmss", example = "CPG001", required = true)
    @NotBlank
    private String expiryDate;

    @ApiModelProperty(value = "Number of vouchers per email.Must be greater than or equal to 1", example = "1", required = true)
    @Min(value = 1L)
    private Integer vouchersPerEmail;

    @ApiModelProperty(value = "Recipient email address. multiple use ';' separated", example = "endCustomerName <<EMAIL>>;", required = true)
    @NotBlank
    private String recipients;

    @ApiModelProperty(value = "Voucher amount.Must be greater than or equal to 0", example = "1", required = true)
    @Min(value = 0L)
    private BigDecimal voucherAmount;

}
