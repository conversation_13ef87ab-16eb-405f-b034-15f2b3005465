package com.gtech.gvcore.common.response.gc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Query Card Statement Response
 */
@Data
@ApiModel(value = "QueryCardStatementResponse", description = "Query Card Statement Response")
public class QueryCardStatementResponse extends BaseApiResponse {


    @ApiModelProperty(value = "Card Program Group", notes = "Gift card program group", example = "Gift Cards", position = 2)
    private String cardProgramGroup;

    @ApiModelProperty(value = "Expiry Date", notes = "Gift card expiry date", example = "2023-12-31", position = 3)
    private Date expiryDate;

    @ApiModelProperty(value = "Gift Card Number", notes = "16 digit gift card number", example = "1234567890123456", position = 5)
    private String cardNumber;

    @ApiModelProperty(value = "Transaction List", notes = "List of gift card transaction records", position = 7)
    private List<Transaction> transactions;



} 