package com.gtech.gvcore.common.request.voucherrequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/9 10:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("Update voucher request details")
@Builder
public class UpdateVoucherRequestDetailsRequest implements Serializable {
    private static final long serialVersionUID = -8065923486200065233L;
    @ApiModelProperty(value = "voucherRequestDetailsCode of the request detail that you want to modify," +
            "If a new detail is added, the voucherRequestDetailsCode is not transmitted", example = "VRD102203081740000005")
    private String voucherRequestDetailsCode;
    @NotNull(message = "denomination can't be empty")
    @ApiModelProperty(required = true, value = "Denomination，entity voucher：50000/100000/500000/1000000", example = "50000")
    private BigDecimal denomination;
    @NotNull(message = "cpgCode can't be empty")
    @ApiModelProperty(required = true, value = "Denomination，entity voucher：50000/100000/500000/1000000", example = "50000")
    private String  cpgCode;
    @NotNull(message = "voucherNum can't be empty")
    @ApiModelProperty(value = "Number of Vouchers", required = true, example = "2000")
    private Integer voucherNum;
    @NotNull(message = "voucherAmount can't be empty")
    @ApiModelProperty(value = "Voucher Amount", required = true, example = "100000000")
    private BigDecimal voucherAmount;
}
