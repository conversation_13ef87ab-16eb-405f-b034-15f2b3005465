package com.gtech.gvcore.common.request.articlemop;

import javax.validation.constraints.NotEmpty;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年2月17日
 */
@Data
public class CreateArticleMopRequest {

    @ApiModelProperty(value = "articleCode")
    //@NotEmpty(message = "articleCode can not be empty")
    @Length(max = 100)
    private String articleCode;

    @ApiModelProperty(value = "articleCodeName", required = true)
    @NotEmpty(message = "articleCodeName can not be empty")
    @Length(max = 200)
    private String articleCodeName;
    
    @ApiModelProperty(value = "sapArticleCode", required = false)
    @Length(max = 100)
    private String sapArticleCode;

    @ApiModelProperty(value = "mopCode", required = true)
    @NotEmpty(message = "mopCode can not be empty")
    @Length(max = 50)
    private String mopCode;

    @ApiModelProperty(value = "createUser", required = true)
    @NotEmpty(message = "createUser can not be empty")
    @Length(max = 50)
    private String createUser;

}
