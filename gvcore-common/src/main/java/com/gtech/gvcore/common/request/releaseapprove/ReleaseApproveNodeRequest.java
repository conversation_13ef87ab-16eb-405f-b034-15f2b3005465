package com.gtech.gvcore.common.request.releaseapprove;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/15 16:45
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("Release approve node")
public class ReleaseApproveNodeRequest {

    @NotNull(message = "Node name can not be null")
    @ApiModelProperty(value = "Node name ", required = true, example = "1")
    private Integer nodeName;

    @NotNull(message = "roleCode can not be empty")
    @ApiModelProperty(value = "Role code ", required = true, example = "admin")
    private String roleCode;
}
