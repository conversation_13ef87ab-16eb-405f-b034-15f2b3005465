package com.gtech.gvcore.common.request.voucherrequest;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/10 17:43
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("Get voucher request request")
public class GetVoucherRequestRequest {

    @ApiModelProperty(value = "Voucher request code", required = true, example = "REQ2200006")
    @NotBlank(message = "VoucherRequestCode cannot be empty")
    private String voucherRequestCode;

	@ApiModelProperty(value = "Role list use ',' split")
	private String roleList;

	@ApiModelProperty(value = "User code")
	private String userCode;
}
