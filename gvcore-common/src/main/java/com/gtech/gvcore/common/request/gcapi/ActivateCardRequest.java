package com.gtech.gvcore.common.request.gcapi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * Gift Card Activation Request
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "ActivateCardRequest", description = "Gift Card Activation Request")
public class ActivateCardRequest {

    @ApiModelProperty(value = "Gift Card Number", notes = "16 digit gift card number", required = true, example = "1234567890123456", position = 2)
    @NotEmpty(message = "giftCardNumber cannot be null")
    private String giftCardNumber;

    @ApiModelProperty(value = "Gift Card PIN", notes = "6 digit PIN code", example = "123456", position = 3)
    private String giftCardPIN;

    @ApiModelProperty(value = "Gift Card Activation Expiry", notes = "Gift card activation expiry date in YYYY-MM-DD format", position = 4)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date giftCardActivationExpiry;

    @ApiModelProperty(value = "Activation Code", notes = "Activation code for the gift card (if required)", example = "ACT12345", position = 9)
    private String activationCode;

    @ApiModelProperty(value = "Denomination", notes = "Denomination when first time purchased gift card", position = 10)
    private BigDecimal denomination;

    @ApiModelProperty(value = "Notes", notes = "Additional notes or comments", example = "Card activated upon purchase", position = 12)
    private String notes;

    @ApiModelProperty(value = "Customer Information", notes = "Customer details for card activation", required = true, position = 13)
    @NotNull(message = "customer cannot be null")
    @Valid
    private CustomerInfo customer;


}