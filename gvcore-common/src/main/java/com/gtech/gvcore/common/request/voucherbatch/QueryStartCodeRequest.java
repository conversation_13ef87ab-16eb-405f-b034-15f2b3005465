package com.gtech.gvcore.common.request.voucherbatch;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2022/3/4 15:57
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryStartCodeRequest")
public class QueryStartCodeRequest {

    @ApiModelProperty(value = "Denomination.", example = "100")
    private BigDecimal denomination;

    @ApiModelProperty(value = "Cpgcode.", example = "100")
    private String cpgCode;


}
