package com.gtech.gvcore.common.request.outletcpg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "GetOutletCpgRequest")
public class GetOutletCpgRequest {


    @ApiModelProperty(value = "Outlet cpg code.", example = "112233",required = true)
    @NotEmpty(message = "outletCpgCode can not be empty")
    @Length(max = 100)
    private String outletCpgCode;





}
