package com.gtech.gvcore.common.response.transaction;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategy.UpperCamelCaseStrategy.class)
public class OnetimebarcodeResponse {


    private String cardNumber;
    @JsonProperty("CardPin")
    private String cardPin;

    @JsonProperty("Barcode")
    private String barCode;

    private String transactionDateTime;

    private String pinExpiry;

    private String notes;

    private String approvalCode;

    private Integer responseCode;

    private String responseMessage;

    private Integer transactionId;

    private String transactionType;

    private String errorCode;

    private String errorDescription;

    private String apiWebProperties;





}