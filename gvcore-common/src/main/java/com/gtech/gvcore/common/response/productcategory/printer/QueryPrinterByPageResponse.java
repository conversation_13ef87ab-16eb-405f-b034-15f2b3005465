package com.gtech.gvcore.common.response.productcategory.printer;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> Gao.Yuhua
 * @date : 2022-02-22 14:49
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryPrinterByPageResponse")
public class QueryPrinterByPageResponse {

    /**
     * printerCode
     */
    @ApiModelProperty(value = "printerCode")
    private String printerCode;

    /**
     * outlet name
     */
    @ApiModelProperty(value = "printerName")
    private String printerName;

    private String issuerCode;

    /**
     * stateCode
     */
    @ApiModelProperty(value = "stateCode")
    private String stateCode;

    /**
     * cityCode
     */
    @ApiModelProperty(value = "cityCode")
    private String cityCode;

    /**
     * districtCode
     */
    @ApiModelProperty(value = "districtCode")
    private String districtCode;

    /**
     * stateName
     */
    @ApiModelProperty(value = "stateName")
    private String stateName;

    /**
     * cityCode
     */
    @ApiModelProperty(value = "cityName")
    private String cityName;

    /**
     * districtCode
     */
    @ApiModelProperty(value = "districtName")
    private String districtName;

    /**
     * printer first name
     */
    @ApiModelProperty(value = "firstName")
    private String firstName;

    /**
     * printer last name
     */
    @ApiModelProperty(value = "lastName")
    private String lastName;

    /**
     * printer mobile
     */
    @ApiModelProperty(value = "mobile")
    private String mobile;

    /**
     * printer email
     */
    @ApiModelProperty(value = "email")
    private String email;

    @ApiModelProperty(value = "Receiving Method", example = "0",required = true)
    private String receivingMethod;

    /**
     * status,0:disable,1:enable
     */
    @ApiModelProperty(value = "status",example = "status:0:disable,1:enable")
    private Integer status;

    /**
     * update time
     */
    private Date updateTime;
}
