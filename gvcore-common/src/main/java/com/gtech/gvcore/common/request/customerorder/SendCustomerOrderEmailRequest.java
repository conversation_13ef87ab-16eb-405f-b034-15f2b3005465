package com.gtech.gvcore.common.request.customerorder;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/27 14:29
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "SendCustomerOrderEmailRequest")
public class SendCustomerOrderEmailRequest implements Serializable {
	private static final long serialVersionUID = -3875682061974722964L;

	private String customerOrderCode;

	private String fileName;

	private String fileUrl;
}
