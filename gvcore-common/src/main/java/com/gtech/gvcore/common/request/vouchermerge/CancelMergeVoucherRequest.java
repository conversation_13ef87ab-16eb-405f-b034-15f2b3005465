package com.gtech.gvcore.common.request.vouchermerge;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CancelMergeVoucherRequest")
public class CancelMergeVoucherRequest implements Serializable {


    private static final long serialVersionUID = 53157181676373018L;

	private String voucherCode;

	private String operateUser;

}
