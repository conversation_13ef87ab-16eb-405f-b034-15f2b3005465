package com.gtech.gvcore.common.externalmapping.request.mapgiftvoucher;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.gtech.gvcore.common.request.v3.Cards;
import lombok.Data;

@Data
public class Vouchers {
    private String vpgName;
    private String amount;
    private String currency;

    private String voucherNumber;
    private String voucherPIN;
    private String trackData;
    private String externalNumber;
    private String themeId;
    private String sequence;
    private String productCode;
    private String designCode;
    @JsonProperty(value = "extends")
    private String extendsField;


    public Cards toCards() {
        Cards cards = new Cards();
        cards.setCardProgramGroupName(this.vpgName);
        cards.setAmount(this.amount);
        cards.setCurrencyCode(this.currency);
        return cards;
    }

}
