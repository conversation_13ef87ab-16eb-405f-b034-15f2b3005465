package com.gtech.gvcore.common.enums;

public enum CategoryTypeEnum {

    VOUCHER("voucher", "voucher"),
    GC("gc", "gc");

    private final String code;
    private final String description;

    CategoryTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     */
    public static CategoryTypeEnum fromCode(String code) {
        for (CategoryTypeEnum category : values()) {
            if (category.getCode().equals(code)) {
                return category;
            }
        }
        throw new IllegalArgumentException("Unknown category type code: " + code);
    }
}
