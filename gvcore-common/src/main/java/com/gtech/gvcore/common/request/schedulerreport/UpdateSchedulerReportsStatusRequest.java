package com.gtech.gvcore.common.request.schedulerreport;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/2 20:08
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "Enable/Disable scheduler report list",value = "UpdateSchedulerReportsStatusRequest")
public class UpdateSchedulerReportsStatusRequest implements Serializable {

    private static final long serialVersionUID = -7272900557005668628L;
    @ApiModelProperty(value = "Scheduler report code list")
    @NotNull(message = "schedulerReportCodeList can not be empty")
    @Size(min = 1, message = "There must be at least one parameter")
    private String[] schedulerReportCodeList;
    @ApiModelProperty(value = "Status  Enable:true/Disable:false")
    @NotNull(message = "Status can not be empty")
    private Boolean status;
}
