package com.gtech.gvcore.common.utils;


import com.jcraft.jsch.*;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.util.Properties;

/**
 * <AUTHOR>
 */
@Slf4j
public class SftpUtil {

    /**
     * 连接超时时间
     */
    private static final int CONNECTION_TIMEOUT = 120 * 1000;

    /**
     * 密码认证
     */
    public static final String AUTH_TYPE_PAD = "AUTH_TYPE_PASSWORD";


    /**
     * 私钥路径认证
     */
    public static final String AUTH_TYPE_PRIVATE_KEY_PATH = "AUTH_TYPE_PRIVATE_KEY_PATH";


    /**
     * 私钥字节数组认证
     */
    public static final String AUTH_TYPE_PRIVATE_KEY_BYTE = "AUTH_TYPE_PRIVATE_KEY_BYTE";


    /**
     * FTP 登录用户名
     */
    private String userName;

    /**
     * FTP 登录密码
     */
    private String password;

    /**
     * 私钥文件的路径
     */
    private String priKeyFilePath;

    /**
     * 密钥的密码
     */
    private String passphrase;

    /**
     * FTP 服务器地址IP地址
     */
    private String host;

    /**
     * FTP私钥
     */
    private byte[] prvKeyByte;

    /**
     * FTP 端口
     */
    private int port;

    /**
     * ChannelSftp
     */
    private ChannelSftp sftp;

    /**
     * Session
     */
    private Session sshSession;


    /**
     * 认证方式
     */
    private String authType;


    /**
     * 构造基于“密码”认证的sftp对象
     *
     * @param userName 用户名
     * @param password 登陆密码
     * @param host     服务器ip
     * @param port     sftp端口
     * @param authType 认证方式
     */
    public SftpUtil(String userName, String password, String host, int port, String passphrase, String authType) {
        this.userName = userName;
        this.password = password;
        this.host = host;
        this.port = port;
        this.passphrase = passphrase;
        this.authType = authType;
    }

    /**
     * 私钥登陆（字节数组私钥）
     *
     * @param userName
     * @param host
     * @param port
     * @param prvKeyByte
     * @param passphrase 私密密码短语
     * @param authType   认证方式
     */
    public SftpUtil(String userName, String host, int port, byte[] prvKeyByte, String passphrase, String authType) {
        this.userName = userName;
        this.host = host;
        this.port = port;
        this.prvKeyByte = prvKeyByte;
        this.passphrase = passphrase;
        this.authType = authType;
    }

    /**
     * 连接sftp服务器
     * 如果connect过程出现：Kerberos username [xxx]   Kerberos password
     * 解决办法：移步https://blog.csdn.net/a718515028/article/details/80356337
     *
     * @throws Exception
     */
    public void connect() {

        try {
            JSch jsch = getJsch();

            log.info("SFTP Host:{}, UserName:{}", host, userName);
            sshSession = jsch.getSession(userName, host, port);
            log.debug("Session It has been established。");

            if (AUTH_TYPE_PAD.equals(authType)) {
                sshSession.setPassword(password);
            }
            Properties config = new Properties();
            config.put("StrictHostKeyChecking", "no");

            // 为Session对象设置properties
            sshSession.setConfig(config);
            // 设置timeout时间
            sshSession.setTimeout(CONNECTION_TIMEOUT);
            // 通过Session建立链接
            sshSession.connect();

            log.debug("Session connected.");
            Channel channel = sshSession.openChannel("sftp");
            channel.connect();
            sftp = (ChannelSftp) channel;
            log.info("sftp connection succeeded,Host:{}", host);

        } catch (Exception e) {
            log.error("sftp Connection failed，error:{}：", e);

        }
    }

    private JSch getJsch() throws JSchException {

        JSch jSch;

        log.info("authType:{}", authType);

        //密钥路径登陆
        if (AUTH_TYPE_PRIVATE_KEY_PATH.equals(authType)) {

            jSch = new JSch();

            log.info("connection sftp,priKeyFilePath:{}", priKeyFilePath);

            // 设置私钥及私钥密码
            jSch.addIdentity(priKeyFilePath, passphrase);
            return jSch;
        }
        //密钥字节数组登陆
        else if (AUTH_TYPE_PRIVATE_KEY_BYTE.equals(authType)) {

            jSch = new JSch();

            jSch.addIdentity(UUIDUtils.generateCode(), prvKeyByte, null, str2byte(passphrase, "UTF-8"));

            return jSch;
        }
        //密码登陆
        else {
            jSch = new JSch();
        }

        return jSch;
    }

    static byte[] str2byte(String str, String encoding) {

        if (str == null) {
            return new byte[0];
        }
        try {
            return str.getBytes(encoding);
        } catch (java.io.UnsupportedEncodingException e) {
            return str.getBytes();
        }
    }

    /**
     * 关闭连接 server
     */
    public void disconnect() {

        if (sftp != null) {
            if (sftp.isConnected()) {
                sftp.disconnect();
                sshSession.disconnect();
                log.info("SFTP Close connection success.");
            } else if (sftp.isClosed()) {
                log.warn("SFTP Already closed, no need to turn off.");
            }
        }
    }

    /**
     * 将输入流的数据上传到sftp作为文件
     *
     * @param directory    上传到该目录
     * @param sftpFileName sftp端文件名
     * @param input        输入流
     * @throws Exception
     */
    public void upload(String directory, String sftpFileName, InputStream input) throws SftpException {

        try {
            // 如果cd报异常，说明目录不存在，就创建目录
            sftp.cd(directory);
        } catch (Exception e) {
            sftp.mkdir(directory);
            sftp.cd(directory);
            throw e;
        }

        try {

            sftp.put(input, sftpFileName);
            log.info("SFTP Upload success! file name：{}", sftpFileName);
        } catch (Exception e) {
            log.error("SFTP upload failed，fileName:{}", sftpFileName, e);
            throw e;
        }
    }

    /**
     * 上传单个文件
     *
     * @param directory  上传到sftp目录
     * @param uploadFile 要上传的文件,包括路径
     * @throws Exception
     */
    public void upload(String directory, String uploadFile) throws IOException {

        File file = new File(uploadFile);
        FileInputStream in = null;
        try {
            in = new FileInputStream(file);
            upload(directory, file.getName(), in);
        } catch (Exception ex) {
            if (in != null) {
                in.close();
            }
            log.error("upload Exception:{}", ex.getMessage());
        }
    }

    /**
     * 将byte[]上传到sftp，作为文件。注意:从String生成byte[]是，要指定字符集。
     *
     * @param directory    上传到sftp目录
     * @param sftpFileName 文件在sftp端的命名
     * @param byteArr      要上传的字节数组
     * @throws Exception
     */
    public void upload(String directory, String sftpFileName, byte[] byteArr) throws SftpException {
        upload(directory, sftpFileName, new ByteArrayInputStream(byteArr));

    }

    /**
     * 下载文件
     *
     * @param directory    下载目录
     * @param downloadFile 下载的文件
     * @param saveFile     存在本地的路径
     * @throws Exception
     */
    public byte[] download2Byte(String directory, String downloadFile, String saveFile) throws SftpException, IOException {
        connect();
        byte[] fileData;


        try (ByteArrayOutputStream outSteam = new ByteArrayOutputStream();) {
            synchronized (this) {
                sftp.cd(directory);
                sftp.get(downloadFile, outSteam);
                fileData = outSteam.toByteArray();
            }
            log.info("SFTP Download file success！fileName：{}", downloadFile);
            return fileData;
        } catch (Exception e) {
            log.info("SFTP Download file failed！fileName：{}", downloadFile);
            throw e;
        } finally {
            disconnect();
        }
    }
}


