package com.gtech.gvcore.common.utils;

import com.gtech.commons.utils.ConvertUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;

/**
 * @ClassName AmountUtils
 * @Description 金额工具类
 * <AUTHOR>
 * @Date 2022/7/28 10:47
 * @Version V1.0
 **/
public class AmountUtils {

    private AmountUtils() {}

    /**
     * 印尼格式 无小数位,千位符为'.'
     */
    private static final DecimalFormat IDR_FORMAT;

    private static final DecimalFormat IDR_COMMA_FORMAT;


    static {
        DecimalFormatSymbols symbols = DecimalFormatSymbols.getInstance();
        // 千位符为'.'
        symbols.setGroupingSeparator('.');
        // 无小数位
        IDR_FORMAT = new DecimalFormat("#,###", symbols);

        DecimalFormatSymbols commaSymbols = DecimalFormatSymbols.getInstance();
        // 千位符为','
        commaSymbols.setGroupingSeparator(',');
        // 无小数位
        IDR_COMMA_FORMAT = new DecimalFormat("#,###", commaSymbols);
    }

    public static String idrFormat(final Number amount, String defaultValue) {
        if (null == amount) {
            return defaultValue;
        }

        return AmountUtils.IDR_FORMAT.format(amount);
    }

    public static String idrFormat(final Number amount) {
        if (null == amount) {
            return null;
        }

        return AmountUtils.IDR_FORMAT.format(amount);
    }

    public static String idrFormat(final String amount) {
        if (StringUtils.isBlank(amount)) {
            return null;
        }

        return AmountUtils.IDR_FORMAT.format(ConvertUtils.toBigDecimal(amount, BigDecimal.ZERO));
    }

    public static String idrCommaFormat(final Number amount, String defaultValue) {
        if (null == amount) {
            return defaultValue;
        }

        return AmountUtils.IDR_COMMA_FORMAT.format(amount);
    }

    public static String idrCommaFormat(final Number amount) {

        return idrCommaFormat(amount, null);
    }

    public static String idrCommaFormat(final String amount) {
        if (StringUtils.isBlank(amount)) {
            return null;
        }

        return AmountUtils.IDR_COMMA_FORMAT.format(ConvertUtils.toBigDecimal(amount, BigDecimal.ZERO));
    }

}
