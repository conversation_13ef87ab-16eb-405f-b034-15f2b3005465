package com.gtech.gvcore.common.request.issuehandling;

import javax.validation.constraints.NotEmpty;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.URL;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ValidateUploadedFileRequest {
	
	@ApiModelProperty(value = "issuerCode", required = true)
    @NotEmpty(message = "issuerCode can not be empty")
    @Length(max = 50)
	private String issuerCode;
	
	@ApiModelProperty(value = "issueType", required = true)
    @NotEmpty(message = "issueType can not be empty")
    @Length(max = 40)
	private String issueType;
	
	@ApiModelProperty(value = "uploadedFileName", required = true)
    @NotEmpty(message = "uploadedFileName can not be empty")
    @Length(max = 200)
	private String uploadedFileName;
	
	@ApiModelProperty(value = "uploadedFileUrl", required = true)
    @NotEmpty(message = "uploadedFileUrl can not be empty")
    @Length(max = 500)
	private String uploadedFileUrl;

}
