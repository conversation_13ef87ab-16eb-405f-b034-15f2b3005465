package com.gtech.gvcore.common.request.receive;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class QueryVoucherReceiveBatchRequest {

	private List<String> receiveCodeList;

	private String voucherReceiveCode;

    /**
     * voucher start NO
     */
	@ApiModelProperty(value = "Voucher start NO")
    private String voucherStartNo;

    /**
     * voucher end NO
     */
	@ApiModelProperty(value = "Voucher end NO")
    private String voucherEndNo;


    /**
     * booklet start NO
     */
	@ApiModelProperty(value = "Booklet start NO")
    private String bookletStartNo;

    /**
     * booklet end NO
     */
	@ApiModelProperty(value = "Booklet end NO")
    private String bookletEndNo;

	/**
	 * cpg code
	 */
	@ApiModelProperty(value = "Cpg code")
	private String cpgCode;

}