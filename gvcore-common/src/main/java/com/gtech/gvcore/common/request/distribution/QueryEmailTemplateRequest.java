package com.gtech.gvcore.common.request.distribution;

import com.gtech.gvcore.common.enums.DisEmailTemplateTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

/**
 * @ClassName QueryEmailTemplateRequest
 * @Description 获取邮件参数
 * <AUTHOR>
 * @Date 2022/7/5 16:11
 * @Version V1.0
 **/
@Getter
@Setter
@ApiModel(value = "QueryEmailTemplateRequest")
public class QueryEmailTemplateRequest {

    @ApiModelProperty(value = "客户编码", example = "UC001", required = true)
    @NotEmpty(message = "Customer code can not be empty")
    private String customerCode;

    /**
     * 邮件模板状态
     *
     * @see com.gtech.gvcore.common.enums.DisEmailTemplateStatusEnum
     */
    @ApiModelProperty(value = "Email template status, ignore status when empty.0-invalid ,1-valid", example = "0")
    private Integer status;

    /**
     * 邮件模板类型
     *
     * @see com.gtech.gvcore.common.enums.DisEmailTemplateTypeEnum
     */
    @ApiModelProperty(value = DisEmailTemplateTypeEnum.REMARK, example = "0")
    private Integer templateType;

}
