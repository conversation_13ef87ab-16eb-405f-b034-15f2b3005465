package com.gtech.gvcore.common.utils;

import com.gtech.commons.utils.StringUtil;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Collection;

/**
 * @ClassName ConvertUtils
 * @Description com.gtech.commons.utils.ConvertUtils 扩展
 * <AUTHOR>
 * @Date 2022/7/12 18:36
 * @Version V1.0
 **/
public class GvConvertUtils {

    private GvConvertUtils() {}

    public static <T> Collection<T> toCollection(final Collection<T> sources, final Collection<T> defaultValues) {
        return CollectionUtils.isEmpty(sources) ? defaultValues : sources;
    }

    public static Integer toInteger(final String text, final Integer defaultValue) {
        return StringUtil.isBlank(text) ? defaultValue : Integer.valueOf(text);
    }

    public static Integer toInteger(final Object obj) {
        return toInteger(obj, null);
    }

    public static Integer toInteger(final Object obj, final Integer defaultValue) {
        return obj == null ? defaultValue : toInteger(String.valueOf(obj), defaultValue);
    }

    public static Long toLong(final String text, final Long defaultValue) {
        return StringUtil.isBlank(text) ? defaultValue : Long.valueOf(text);
    }

    public static Long toLong(final Object obj) {
        return toLong(obj, null);
    }

    public static Long toLong(final Object obj, Long defaultValue) {
        return obj == null ? defaultValue : toLong(String.valueOf(obj), defaultValue);
    }

    public static Double toDouble(final String text, final Double defaultValue) {
        return StringUtil.isBlank(text) ? defaultValue : Double.valueOf(text);
    }

    public static Double toDouble(final Object obj) {
        return toDouble(obj, null);
    }

    public static Double toDouble(final Object obj, final Double defaultValue) {
        return obj == null ? defaultValue : toDouble(String.valueOf(obj), defaultValue);
    }

    public static BigDecimal toBigDecimal(final String text, final BigDecimal defaultValue) {
        return StringUtil.isBlank(text) ? defaultValue : new BigDecimal(text);
    }

    public static BigDecimal toBigDecimal(final Object obj) {
        return toBigDecimal(obj, null);
    }

    public static BigDecimal toBigDecimal(final Object obj, final BigDecimal defaultValue) {
        return obj == null ? defaultValue : toBigDecimal(String.valueOf(obj), defaultValue);
    }

    public static String toString(final String text, final String defaultValue) {
        return StringUtil.isBlank(text) ? defaultValue : text;
    }

    public static String toString(final Object obj) {
        return toString(obj, null);
    }

    public static String toString(final Object obj, final String defaultValue) {
        return obj == null ? defaultValue : toString(String.valueOf(obj), defaultValue);
    }

    public static Boolean toBoolean(final String text, final Boolean defaultValue) {
        return StringUtil.isBlank(text) ? defaultValue : Boolean.valueOf(text);
    }

    public static Boolean toBoolean(final Object obj) {
        return toBoolean(obj, null);
    }

    public static Boolean toBoolean(final Object obj, final Boolean defaultValue) {
        return obj == null ? defaultValue : toBoolean(String.valueOf(obj), defaultValue);
    }

    public static <T> T toObject (final T obj, final T defaultObj) {
        return null == obj ? defaultObj : obj;
    }

}
