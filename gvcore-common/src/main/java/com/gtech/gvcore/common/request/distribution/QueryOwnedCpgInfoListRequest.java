package com.gtech.gvcore.common.request.distribution;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

/**
 * @ClassName QueryInStockCpgRequest
 * @Description Query VPG information parameters with inventory
 * <AUTHOR>
 * @Date 2022/7/6 10:18
 * @Version V1.0
 **/
@Getter
@Setter
@ApiModel(value = "QueryReceivedCpgInfoListRequest")
public class QueryOwnedCpgInfoListRequest {

    @ApiModelProperty(value = "Customer Code", example = "UC001",required = true)
    @NotEmpty(message = "Customer code can not be empty")
    private String customerCode;

}
