package com.gtech.gvcore.common.response.transaction;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonNaming(PropertyNamingStrategy.UpperCamelCaseStrategy.class)
public class BatchcloseResponse {
	@JsonProperty(value="responseCode")
	@ApiModelProperty(value = "Response Code.", required = true, notes = "A 5 digit integer response code. A zero value indicates success and a non-zero value indicates failure.")
	private Integer responseCode;
	@JsonProperty(value="responseMessage")
	@ApiModelProperty(value = "Response Message.", required = true, notes = "Contains the response message or error message in case the transacƟ on has failed (response code is non-zero).")
	private String responseMessage;
	@ApiModelProperty(value = "Transaction Id.", required = true, notes = "The transaction id passed in the request.")
	private Integer transactionId;
	@ApiModelProperty(value = "Reload Count.", required = true, notes = "Total number of reloads in current batch")
	private Integer reloadCount;
	@ApiModelProperty(value = "Reload Amount.", required = true, notes = "Total value of reloads")
	private BigDecimal reloadAmount;
	@ApiModelProperty(value = "Activation Count.", required = true, notes = "Total number of activations in current batch")
	private Integer activationCount;
	@ApiModelProperty(value = "Activation Amount.", required = true, notes = "Total value of activations")
	private BigDecimal activationAmount;
	@ApiModelProperty(value = "Redemption Count.", required = true, notes = "Total number of redemptions in current batch")
	private Integer redemptionCount;
	@ApiModelProperty(value = "Cancel Redeem Count.", required = true, notes = "Total number of cancel redeems in current batch")
	private Integer cancelRedeemCount;
	@ApiModelProperty(value = "Cancel Redeem Amount.", required = true, notes = "Total value of redemptions")
	private BigDecimal cancelRedeemAmount;
	@ApiModelProperty(value = "Cancel Load Amount.", required = true, notes = "Total value of cancel reloads")
	private BigDecimal cancelLoadAmount;
	@ApiModelProperty(value = "Cancel Load Count.", required = true, notes = "Total number of cancel reloads in current batch")
	private Integer cancelLoadCount;
	@ApiModelProperty(value = "Cancel Activation Count.", required = true, notes = "Total number of cancel activations in current batch")
	private Integer cancelActivationCount;
	@ApiModelProperty(value = "Cancel Activation Amount.", required = true, notes = "Total value of cancel activations")
	private BigDecimal cancelActivationAmount;
	@ApiModelProperty(value = "New Batch Number.", required = true, notes = "New batch number")
	private Integer newBatchNumber;
	@ApiModelProperty(value = "New Auth Token.", required = true, notes = "A JWT token will be returned to be passed for subsequent transaction.")
	private String newAuthToken;

	private String approvalCode;
	private String notes;
	private String errorCode;
	private String errorDescription;
	private String transactionType;
	private BigDecimal redemptionAmount;


}
