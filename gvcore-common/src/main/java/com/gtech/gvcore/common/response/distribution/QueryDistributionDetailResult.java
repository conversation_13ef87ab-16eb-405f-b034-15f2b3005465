package com.gtech.gvcore.common.response.distribution;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gtech.commons.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @ClassName QueryDistributionDetailResult
 * @Description 分发详情列表
 * <AUTHOR>
 * @Date 2022/9/6 19:17
 * @Version V1.0
 **/
@Setter
@Getter
@Accessors(chain = true)
@ApiModel(description = "QueryDistributionDetailResult")
public class QueryDistributionDetailResult {

    @ExcelProperty(index = 0)
    @DateTimeFormat(DateUtil.FORMAT_YYYYMMDDHHMISS)
    @JsonFormat(pattern = DateUtil.FORMAT_YYYYMMDDHHMISS)
    private Date createTime;

    @ExcelProperty(index = 1)
    private String distributionCode;

    @ExcelProperty(index = 2)
    private String voucherCode;

    @ExcelProperty(index = 3)
    private String cpgName;

    @ExcelProperty(index = 4)
    private String emailEndCustomerName;

    @ExcelProperty(index = 5)
    private String emailAddress;

    @ExcelProperty(index = 6)
    private String voucherStatus;

    @ExcelProperty(index = 7)
    private String lastTransactionType;

    @ExcelProperty(index = 8)
    @DateTimeFormat(DateUtil.FORMAT_YYYYMMDDHHMISS)
    @JsonFormat(pattern = DateUtil.FORMAT_YYYYMMDDHHMISS)
    private Date lastActionDate;

    @ExcelProperty(index = 9)
    @DateTimeFormat(DateUtil.FORMAT_YYYYMMDDHHMISS)
    @JsonFormat(pattern = DateUtil.FORMAT_YYYYMMDDHHMISS)
    private Date expiryDate;

    @ExcelProperty(index = 10)
    private String merchantName;

    @ExcelProperty(index = 11)
    private String outletName;

    @ExcelProperty(index = 13)
    private String status;

    @ExcelProperty(index = -1)
    private String distributionItemCode;

    @ExcelProperty(index = -1)
    private String errorMsg;

}
