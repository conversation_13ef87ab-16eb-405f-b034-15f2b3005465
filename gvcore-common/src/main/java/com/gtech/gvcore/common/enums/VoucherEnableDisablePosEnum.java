package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @Date 2022/3/11 11:44
 */
public enum VoucherEnableDisablePosEnum {

    /**
     *  VOUCHER_NEWLY_GENERATED = 0;
     *  VOUCHER_ACTIVATED = 1;
     *  VOUCHER_USED = 2;
     *  VOUCHER_CANCELLED = 3;
     *  VOUCHER_EXPIRED = 4;
     */

    /**
     * PURCHASED( "9", "130"),
     *     ACTIVATED( "1", "140"),
     *     USED("2", "140"),
     *     CANCELLED( "3", "310"),
     *     EXPIRED("4", "120")
     *     ;
     */



//    VOUCHER_NEWLY_GENERATED(0, "CREATED"),
    DEACTIVATE(0, "DEACTIVATED","150");

    private final int code;

    private final String desc;

    private final String statusId;

    VoucherEnableDisablePosEnum(int code, String desc, String statusId) {
        this.code = code;
        this.desc = desc;
        this.statusId = statusId;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
    public String getStatusId() {
        return statusId;
    }

    public boolean equalsCode(Integer code) {

        return null != code && code.equals(this.code);
    }


    public static String getDescByCode(Integer code) {
        for(VoucherEnableDisablePosEnum flowNodeEunm : VoucherEnableDisablePosEnum.values()) {
            if (flowNodeEunm.getCode() == code) {
                return flowNodeEunm.getDesc();
            }
        }
        return null;
    }


    public static String getStatusIdByCode(Integer code) {
        for(VoucherEnableDisablePosEnum flowNodeEunm : VoucherEnableDisablePosEnum.values()) {
            if (flowNodeEunm.getCode() == code) {
                return flowNodeEunm.getStatusId();
            }
        }
        return null;
    }

}
