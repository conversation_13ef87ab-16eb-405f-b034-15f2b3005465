package com.gtech.gvcore.common.enums;

/**
 * 延长激活期限记录来源枚举
 */
public enum ExtensionSourceEnum {
    
    /**
     * API接口调用
     */
    API("API", "API接口调用"),
    
    /**
     * 手动操作
     */
    MANUAL("MANUAL", "手动操作"),
    
    /**
     * 系统自动
     */
    SYSTEM("SYSTEM", "系统自动"),
    
    /**
     * 批量处理
     */
    BATCH("BATCH", "批量处理");
    
    private final String code;
    private final String description;
    
    ExtensionSourceEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static ExtensionSourceEnum fromCode(String code) {
        for (ExtensionSourceEnum source : values()) {
            if (source.getCode().equals(code)) {
                return source;
            }
        }
        throw new IllegalArgumentException("未知的延长来源代码: " + code);
    }
}
