package com.gtech.gvcore.common.request.customerorder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/27 12:30
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "UploadPaymentVoucherRequest")
public class UploadPaymentVoucherRequest implements Serializable {
	private static final long serialVersionUID = 4211553684151720413L;

	@ApiModelProperty(value = "Customer order code", required = true)
	@NotBlank(message = "customerOrderCode can not be empty")
	private String customerOrderCode;

	@ApiModelProperty(value = "Payment voucher url", required = true)
	@NotBlank(message = "paymentVoucherUrl can not be empty")
	private String paymentVoucherUrl;

	@ApiModelProperty(value = "User code", required = true)
	@NotBlank(message = "userCode can not be empty")
	private String userCode;
}
