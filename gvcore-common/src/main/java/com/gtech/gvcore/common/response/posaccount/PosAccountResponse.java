package com.gtech.gvcore.common.response.posaccount;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "PosAccountResponse")
public class PosAccountResponse {

    @ApiModelProperty(value = "Pos accountCode code.", example = "2123123")
    private String posAccountCode;

    @ApiModelProperty(value = "Outlet type code.", example = "2123123")
    private String outletType;

    @ApiModelProperty(value = "Pos account code.", example = "2123123")
    private String posAccount;

    @ApiModelProperty(value = "Pos password code.", example = "2123123")
    private String posPassword;

    @ApiModelProperty(value = "Create user.", example = "user123")
    private String createUser;

    @ApiModelProperty(value = "Create time.", example = "2020-03-12 12:12:12")
    private Date createTime;

    @ApiModelProperty(value = "Create user.", example = "user123")
    private String updateUser;

    @ApiModelProperty(value = "Create time.", example = "2020-03-12 12:12:12")
    private Date updateTime;
    
}
