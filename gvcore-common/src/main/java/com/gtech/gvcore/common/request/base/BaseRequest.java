package com.gtech.gvcore.common.request.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Base request object for all API requests.
 */
@Data
public class BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "A unique key for retrying a request to avoid duplicate processing. " +
            "If provided, the system will cache the response. " +
            "Subsequent requests with the same retryKey will receive the cached response.",
            example = "c8a4b3f2-1e9a-4b9f-8e1a-9a8b7c6d5e4f")
    private String retryKey;

} 