package com.gtech.gvcore.common.request.flow;

import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class FlowNodeRequest {

	
	private String flowCode;
	
	@ApiModelProperty(value = "delete flag", notes = "delete flag, default false")
	private Boolean deleteFlag = false;
	
    /**
     * flow node code
     */
    @ApiModelProperty(value = "flow node code", required = true)
    @NotEmpty(message = "flow node code can't be empty")
    private String flowNodeCode;

    /**
     * flow node name
     */
    @ApiModelProperty(value = "flow node name", required = true)
    @NotEmpty(message = "flow node name can't be empty")
    private String flowNodeName;

    /**
     * remark
     */
    private String remark;

    /**
     * status
     */
    private Integer status;
    
}