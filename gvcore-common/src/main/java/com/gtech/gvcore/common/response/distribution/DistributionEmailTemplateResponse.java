package com.gtech.gvcore.common.response.distribution;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.DisEmailTemplateStatusEnum;
import com.gtech.gvcore.common.enums.DisEmailTemplateTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @ClassName DistributionEmailTemplateResponse
 * @Description 分发邮件模板信息
 * <AUTHOR>
 * @Date 2022/7/5 15:45
 * @Version V1.0
 **/
@Getter
@Setter
@ApiModel(value = "DistributionEmailTemplateResponse")
public class DistributionEmailTemplateResponse {

    @ApiModelProperty(value = "邮件模板编码", example = "ET001")
    private String templateCode;

    @ApiModelProperty(value = "邮件模板名称", example = "Untitled Single Send")
    private String templateName;

    @ApiModelProperty(value = DisEmailTemplateTypeEnum.REMARK, example = "0")
    private Integer templateType;

    @ApiModelProperty(value = "邮件主题", example = "Informasi Penukaran Map Gift Voucher")
    private String subject;

    @ApiModelProperty(value = "富文本内容", example = "context")
    private String richText;

    @ApiModelProperty(value = DisEmailTemplateStatusEnum.REMARK, example = "1")
    private Integer status;

    @JsonFormat(pattern = DateUtil.FORMAT_YYYYMMDDHHMISS)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

}
