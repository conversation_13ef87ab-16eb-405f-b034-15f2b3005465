package com.gtech.gvcore.common.request.cpg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> Gao.Yuhua
 * @date : 2022-02-21 17:53
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CreateCpgTypeRequest")
public class UpdateCpgTypeRequest {


    /**
     * id
     */
    @NotEmpty(message = "cpgTypeId can not be empty")
    @ApiModelProperty(value = "cpgTypeCode", example = "CPC102202241512000241", required = true)
    private String cpgTypeCode;

    /**
     * CPG type name
     */
    @NotEmpty(message = "cpgTypeName can not be empty")
    @Length(max = 100)
    @ApiModelProperty(value = "cpgTypeName", example = "xxx", required = true)
    private String cpgTypeName;

    @NotEmpty(message = "automaticActivate can not be empty")
    @Length(max = 10)
    @ApiModelProperty(value = "automaticActivate", example = "YES/NO", required = true)
    private String automaticActivate;

    /**
     * prefix
     */
    @NotEmpty(message = "prefix can not be empty")
    @Length(max = 3)
    @ApiModelProperty(value = "prefix", example = "100", required = true)
    private String prefix;

    /**
     * updateUser
     */
    @NotEmpty(message = "updateUser can not be empty")
    @Length(max = 50)
    @ApiModelProperty(value = "updateUser", example = "gv", required = true)
    private String updateUser;
}
