package com.gtech.gvcore.common.externalmapping.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.HashMap;
import java.util.Map;

@Data
public class CustomerInfo {


    @ApiModelProperty(value = "Salutation. ", example = "123456", notes = "Salutation")
    @Length(max = 20, message = "Salutation maximum length 20")
    private String customerName;
    @ApiModelProperty(value = "First Name. ", required = true, example = "123456", notes = "First name of the customer")
    @Length(max = 50, message = "First Name maximum length 50")
    private String firstName;
    @ApiModelProperty(value = "Last Name. ", required = true, example = "123456", notes = "Last name of the customer")
    @Length(max = 50, message = "Last Name maximum length 50")
    private String lastName;
    @ApiModelProperty(value = "Mobile. ", example = "123456", notes = "Mobile number")
    @Length(max = 50, message = "Mobile maximum length 50")
    private String phoneNumber;
    @ApiModelProperty(value = "Address Line 1. ", example = "123456", notes = "Address Line 1")
    @Length(max = 250, message = "Address Line 1 maximum length 250")
    private String addressLine1;
    @ApiModelProperty(value = "Address Line 2. ", example = "123456", notes = "Address Line 2")
    @Length(max = 250, message = "Address Line 2 maximum length 250")
    private String addressLine2;
    @ApiModelProperty(value = "Address Line 3. ", example = "123456", notes = "Address Line 3")
    @Length(max = 250, message = "Address Line 3 maximum length 250")
    private String addressLine3;
    @ApiModelProperty(value = "Date Of Birth. ", example = "2022-02-02", notes = "YYYY-MM-DD")
    private String birthdayDate;
    @ApiModelProperty(value = "Email. ", example = "123456", notes = "email id of the customer")
    @Length(max = 50, message = "Email maximum length 50")
    private String email;
    @ApiModelProperty(value = "City. ", example = "123456", notes = "city in which customer resides")
    @Length(max = 50, message = "City maximum length 50")
    private String city;
    @ApiModelProperty(value = "State. ", example = "123456", notes = "state in which customer resides")
    @Length(max = 50, message = "State maximum length 50")
    private String state;
    @ApiModelProperty(value = "Country. ", example = "123456", notes = "country in which customer resides")
    @Length(max = 50, message = "Country maximum length 50")
    private String country;
    @ApiModelProperty(value = "Empid. ", example = "123456", notes = "employee id")
    @Length(max = 50, message = "Empid maximum length 50")
    private String employeeId;
    @ApiModelProperty(value = "Corporate Name. ", example = "123456", notes = "corporate name")
    @Length(max = 50, message = "Corporate Name maximum length 50")
    private String corporateName;

    private String customerType;

    private String gender;

    private String anniversary;

    private String maritalStatus;

    private String alterPhone;

    private String zipCode;

    private String region;

    private String area;




    public Map<String,Object> toMap(){

        Map<String, Object> map = new HashMap<>();
        map.put("salutation", this.customerName);
        map.put("firstName", this.firstName);
        map.put("lastName", this.lastName);
        map.put("mobile", this.phoneNumber);
        map.put("address1", this.addressLine1);
        map.put("address2", this.addressLine2);
        map.put("address3", this.addressLine3);
        map.put("dateOfBirth", this.birthdayDate);
        map.put("email", this.email);
        map.put("city", this.city);
        map.put("state", this.state);
        map.put("country", this.country);
        map.put("empid", this.employeeId);
        map.put("corporatename", this.corporateName);
        map.put("customerType", this.customerType);
        return map;
    }

    public CustomerInfo toCustomerInfo(com.gtech.gvcore.common.request.transaction.CustomerInfo customerInfo){

        this.setCustomerName(customerInfo.getSalutation());
        this.setFirstName(customerInfo.getFirstName());
        this.setLastName(customerInfo.getLastName());
        this.setPhoneNumber(customerInfo.getMobile());
        this.setAddressLine1(customerInfo.getAddress1());
        this.setAddressLine2(customerInfo.getAddress2());
        this.setAddressLine3(customerInfo.getAddress3());
        this.setBirthdayDate(customerInfo.getDateOfBirth());
        this.setEmail(customerInfo.getEmail());
        this.setCity(customerInfo.getCity());
        this.setState(customerInfo.getState());
        this.setCountry(customerInfo.getCountry());
        this.setEmployeeId(customerInfo.getEmpid());
        this.setCorporateName(customerInfo.getCorporatename());
        this.setCustomerType(customerInfo.getCustomerType());
        return this;
    }



}
