package com.gtech.gvcore.common.response.outletcpg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "OutletCpgResponse")
public class OutletCpgResponse {

    @ApiModelProperty(value = "Outlet cpg code.", example = "112233")
    private String outletCpgCode;

    @ApiModelProperty(value = "Outlet code.", example = "1122333")
    private String outletCode;

    @ApiModelProperty(value = "Cpg code.", example = "2123123")
    private String cpgCode;

    @ApiModelProperty(value = "Cpg name.", example = "2123123")
    private String cpgName;

    @ApiModelProperty(value = "Cpg type code.", example = "2123123")
    private String cpgTypeCode;

    @ApiModelProperty(value = "Cpg type.", example = "voucher", notes = "Cpg type, possible values: voucher, gc")
    private String cpgType;

    @ApiModelProperty(value = "Mop code.", example = "2123123")
    private String mopCode;

    @ApiModelProperty(value = "Denomination.", example = "50000")
    private BigDecimal denomination;

    @ApiModelProperty(value = "Disabled generate.", example = "0")
    private String disableGeneration;

    @ApiModelProperty(value = "Status.", example = "1")
    private Integer status;

    @ApiModelProperty( value="Update user.", example="user1")
    private String updateUser;

    @ApiModelProperty(value = "Create user.", example = "user12")
    private String createUser;

    @ApiModelProperty(value = "Create time.", example = "2022-02-17 16:22")
    private Date createTime;

    @ApiModelProperty(value = "Update time.", example = "2022-02-17 16:22")
    private Date updateTime;

}
