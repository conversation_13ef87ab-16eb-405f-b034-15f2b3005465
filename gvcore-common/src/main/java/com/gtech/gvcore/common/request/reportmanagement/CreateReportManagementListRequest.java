package com.gtech.gvcore.common.request.reportmanagement;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/26 12:09
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "CreateReportManagementListRequest", description = "Click Assign Access to operate, and finally save an array")
public class CreateReportManagementListRequest {

    @ApiModelProperty(value = "Create report management request list", required = true)
    @NotNull(message = "createReportManagementRequests can not empty")
    @Size(min = 1)
    private List<CreateReportManagementRequest> createReportManagementRequests;
}
