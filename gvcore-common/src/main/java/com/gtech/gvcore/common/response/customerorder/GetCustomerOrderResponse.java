package com.gtech.gvcore.common.response.customerorder;

import com.gtech.gvcore.common.response.allocation.VoucherAllocationBatchResponse;
import com.gtech.gvcore.common.response.releaseapprove.ApproveNodeRecordResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/20 15:05
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "Get customer order Response")
public class GetCustomerOrderResponse implements Serializable {

    private static final long serialVersionUID = 4412435241296919505L;
    @ApiModelProperty(value = "Customer order code")
    private String customerOrderCode;
    @ApiModelProperty(value = "Issuer code", example = "IS102203031638001252")
    private String issuerCode;
    @ApiModelProperty(value = "Outlet code")
    private String outletCode;
    @ApiModelProperty(value = "Customer name")
    private String customerName;
    @ApiModelProperty(value = "Customer type")
    private String customerType;
    @ApiModelProperty(value = "Company name")
    private String companyName;
    @ApiModelProperty(value = "Contact first name")
    private String contactFirstName;
    @ApiModelProperty(value = "Contact last name")
    private String contactLastName;
    @ApiModelProperty(value = "Contact Phone")
    private String contactPhone;
    @ApiModelProperty(value = "Contact email")
    private String contactEmail;
    @ApiModelProperty(value = "Product category code")
    private String productCategoryCode;
    @ApiModelProperty(value = "Discount type")
    private String discountType;
    @ApiModelProperty(value = "Purchase Order Number", example = "1221 - 1364")
    private String purchaseOrderNo;
    @ApiModelProperty(value = "Voucher type code", example = "VCE")
    private String mopCode;
    @ApiModelProperty(value = "invoice number")
    private String invoiceNo;
    @ApiModelProperty(value = "MOP code", example = "85a4d8656d05442b982d328bc3f2c19b")
    private String meansOfPaymentCode;
    @ApiModelProperty(value = "Total voucher num", example = "4000")
    private Integer voucherNum;
    @ApiModelProperty(value = "Total voucher amount", example = "120000000")
    private BigDecimal voucherAmount;
    @ApiModelProperty(value = "Discount", example = "2")
    private BigDecimal discount;
    @ApiModelProperty(value = "Discount Amount", example = "2.500.000.000")
    private BigDecimal amount;
    @ApiModelProperty(value = "Customer code", example = "")
    private String customerCode;
    @ApiModelProperty(value = "Create user")
    private String createUser;
    @ApiModelProperty(value = "Voucher batch code")
    private String voucherBatchCode;
    @ApiModelProperty(value = "Shipping address")
    private String shippingAddress;
    @ApiModelProperty(value = "notes")
    private String customerRemarks;
    @ApiModelProperty(value = "quotationUrl")
    private String quotationUrl;
    @ApiModelProperty(value = "invoiceUrl")
    private String invoiceUrl;
    @ApiModelProperty(value = "salesOrderUrl")
    private String salesOrderUrl;
    @ApiModelProperty(value = "paymentVoucherUrl")
    private String paymentVoucherUrl;
    @ApiModelProperty(value = "salesNoteUrl")
    private String salesNoteUrl;
    @ApiModelProperty("Customer order details")
    private List<GetCustomerOrderDetailsResponse> getCustomerOrderDetailsResponses;
    @ApiModelProperty("notes")
    private List<ApproveNodeRecordResponse> notes;
    @ApiModelProperty(value = "Voucher allocation code")
    private String voucherAllocationCode;
    @ApiModelProperty(value = "Payment voucher uploader")
    private String paymentVoucherUploader;

	@ApiModelProperty(value = "Status")
	private String status;
	private Date createTime;
	private Date updateTime;
	@ApiModelProperty(value = "alloctionBatchList")
	private List<VoucherAllocationBatchResponse> alloctionBatchList;
	@ApiModelProperty(value = "Can I cancel it?")
	private Boolean cancelAble;
	@ApiModelProperty(value = "Can I release it?")
	private Boolean releaseAble;

    @ApiModelProperty(value = "delivery date")
    private String deliveryDate;

    @ApiModelProperty(value = "order address")
    private String orderAddress;


    @ApiModelProperty(value = "Delive type")
    private String deliveType;
    @ApiModelProperty(value = "Logistics name")
    private String logisticsName;
    @ApiModelProperty(value = "Track no")
    private String trackNo;

    @ApiModelProperty(value = "awb")
    private String awb;

    @ApiModelProperty(value = "scan_of_receipt")
    private String scanOfReceipt;


    @ApiModelProperty(value = "email response list")
    private List<GetCustomerOrderEmailResponse> sendEmailList;

}
