package com.gtech.gvcore.common.request.distribution;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @ClassName CreateDistributionRequest
 * @Description 创建分发参数
 * <AUTHOR>
 * @Date 2022/8/9 11:15
 * @Version V1.0
 **/
@Getter
@Setter
@ApiModel(value = "SaveDistributionRequest")
public class SaveDistributionRequest {

    @ApiModelProperty(value = "Distribution Code", notes = "为空时新增,不为空时更新", example = "CC001")
    private String distributionCode;

    /**
     * 客户编码
     */
    @ApiModelProperty(value = "Customer Code", example = "CC001", required = true)
    @NotBlank
    private String customerCode;

    /**
     * 当前登录用户编码
     */
    @ApiModelProperty(value = "User Code", example = "UC001", required = true)
    @NotBlank
    private String userCode;

    /**
     * 分发类型.Individual，Bulk
     *
     * @see com.gtech.gvcore.common.enums.DistributionTypeEnum
     */
    @ApiModelProperty(value = "Distribution Type.", example = "Bulk", required = true)
    @NotBlank
    private String distributionType;

    /**
     * E-GV Distribution Information
     */
    @ApiModelProperty(value = "E-GV Distribution Information", required = true)
    @Size(min = 1)
    private List<DistributionItemBean> itemList;

    /**
     * 邮件模板编码
     */
    @ApiModelProperty(value = "Email Template Code", example = "ETC001", required = true)
    @NotBlank
    private String emailTemplateCode;

    /**
     * 邮件主题
     */
    @ApiModelProperty(value = "Email Subject", example = "1", required = true)
    @NotBlank
    private String emailSubject;

    /**
     * 邮件模板内容
     */
    @ApiModelProperty(value = "Email template content", example = "1", required = true)
    @NotBlank
    private String emailRichText;

}
