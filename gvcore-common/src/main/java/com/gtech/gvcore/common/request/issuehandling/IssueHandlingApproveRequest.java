package com.gtech.gvcore.common.request.issuehandling;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 
 * <AUTHOR>
 * @date 2022年4月5日
 */
@Data
public class IssueHandlingApproveRequest {
	
	@ApiModelProperty(value = "issueHandlingCode", required = true)
    @NotEmpty(message = "issueHandlingCode can not be empty")
	private String issueHandlingCode;
	
	@ApiModelProperty(value = "Status:  true:agree  false:reject", required = true)
    @NotNull(message = "Status cannot be null")
    private Boolean status;
	
	@ApiModelProperty(value = "notes", required = true)
	@Length(max = 256)
	private String notes;
	
	@ApiModelProperty(value = "updateUser", required = true)
    @NotEmpty(message = "updateUser can not be empty")
    @Length(max = 50)
    private String updateUser;

}


