package com.gtech.gvcore.common.request.voucherbatch;

import com.gtech.gvcore.common.response.customerorder.GetCustomerOrderDetailsResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "ReGenerateDigitalVouchersRequest")
public class ReGenerateDigitalVouchersRequest {

    @ApiModelProperty(value = "Voucher batch code.", example = "1122333")
    @NotEmpty(message = "voucherBatchCode can not be empty")
    private String voucherBatchCode;

    @ApiModelProperty(value = "Customer order detail", example = "1122333")
    @NotNull(message = "customerOrderDetail can not be empty")
    GetCustomerOrderDetailsResponse customerOrderDetail;


}
