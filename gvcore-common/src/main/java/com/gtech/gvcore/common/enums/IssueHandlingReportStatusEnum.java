package com.gtech.gvcore.common.enums;

import java.util.Objects;

public enum IssueHandlingReportStatusEnum implements IEnum<Integer> {
    CREATED(0, "Pending Submit"),
    SUBMIT(1, "Pending Approval"),
    APPROVE(2, "Completed"),
    CANCEL(3, "Canceled"),
    REJECT(4, "Rejected"),
    EXECUTING(5, "Executing");

    private final Integer code;

    private final String desc;

    IssueHandlingReportStatusEnum(Integer code, String desc) {

        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer code() {
        return this.code;
    }

    @Override
    public String desc() {
        return this.desc;
    }

    @Override
    public boolean equalsCode(Integer code) {
        return this.code.equals(code);
    }

    public static IssueHandlingReportStatusEnum valueOfCode(final Integer code) {

        for (IssueHandlingReportStatusEnum value : IssueHandlingReportStatusEnum.values()) {
            if (Objects.equals(code, value.code)) {
                return value;
            }
        }
        return null;
    }
}
