package com.gtech.gvcore.common.enums;

/**
 * 
 * <AUTHOR>
 * @date 2022年4月27日
 */
public enum CpgTypeAutomaticActivateEnum implements IEnum<String> {

    YES("YES", "YES"),
    NO("NO", "NO");

    private final String code;

    private final String desc;

    CpgTypeAutomaticActivateEnum(String code, String desc) {

        this.code = code;
        this.desc = desc;
    }

    @Override
    public String code() {
        return this.code;
    }

    @Override
    public String desc() {
        return this.desc;
    }

    @Override
    public boolean equalsCode(String code) {
        return this.code.equals(code);
    }

}


