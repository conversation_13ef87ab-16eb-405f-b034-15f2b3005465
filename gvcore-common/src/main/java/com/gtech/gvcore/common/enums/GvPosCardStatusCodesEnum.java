package com.gtech.gvcore.common.enums;

/**
 **/
public enum GvPosCardStatusCodesEnum {

	CREATED("CREATED", 0, "CREATED"), 
	ACTIVATED("ACTIVATED", 1, "ACTIVATED"),
	USED("REDEEMED", 2, "REDEEMED"),
	CANCELLED("CANCELLED", 3, "CANCELLED"),
	EXPIRED("EXPIRED", 4, "EXPIRED"),

	PURCHASED("PURCHASED", 130, "PURCHASED"),//??
	DEACTIVATED("DEACTIVATED", 150, "DEACTIVATED"), //voucherStatus  启动禁用
	RESERVED("RESERVED", 160, "RESERVED"), // ??
	;

	private final String code;
	private final int voucherCode;

	private final String desc;

	GvPosCardStatusCodesEnum(String code, int voucherCode, String desc) {
		this.code = code;
		this.voucherCode = voucherCode;
		this.desc = desc;
	}

	public String getCode() {
		return code;
	}

	public int getVoucherCode() {
		return voucherCode;
	}

	public String getDesc() {
		return desc;
	}

	public static String getCodeByVoucherCode(int voucherCode) {
		for (GvPosCardStatusCodesEnum value : GvPosCardStatusCodesEnum.values()) {
			if (value.getVoucherCode() == voucherCode) {
				return value.getCode();
			}
		}
		return null;
	}

}
