package com.gtech.gvcore.common.request.useraccount;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DataPermissionRequest implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3564213313404393467L;

	@ApiModelProperty(value = "Data Permission Type. (1-Issuer, 2-Company, 3-Merchant, 4-Outlet)", example = "1")
	private Integer type;

	@ApiModelProperty(value = "Node is leaf.")
	private Boolean isLeaf;

	@ApiModelProperty(value = "Data Permission Code. ")
	private String code;
}
