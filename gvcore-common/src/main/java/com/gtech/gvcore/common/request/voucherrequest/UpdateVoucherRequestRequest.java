package com.gtech.gvcore.common.request.voucherrequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date /9 9:51
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("Update voucher request")
public class UpdateVoucherRequestRequest implements Serializable {
    private static final long serialVersionUID = -3231727560068073286L;
    @ApiModelProperty(value = "Business type", required = true, example = "return")
    @NotBlank(message = "business type can not be empty")
    private String businessType;
    @NotBlank(message = "issuerCode can not be empty")
    @ApiModelProperty(value = "Issuer code", required = true, example = "IS342323424")
    private String issuerCode;
    @NotNull(message = "The request voucherRequestCode cannot be empty")
    @ApiModelProperty(value = "voucherRequestCode of the request that you want to modify", example = "REQ2200006", required = true)
    private String voucherRequestCode;
    @NotBlank(message = "voucherOwnerCode cannot be empty")
    @ApiModelProperty(value = "voucherOwnerCode from Issuer code.", example = "1122333", required = true)
    private String voucherOwnerCode;
    @NotBlank(message = "voucherOwnerName cannot be empty")
    @ApiModelProperty(value = "voucherOwnerName from Issuer name", required = true, example = "MAP")
    private String voucherOwnerName;
    @NotBlank(message = "receiverCode cannot be empty")
    @ApiModelProperty(value = "receiverCode from Request Source", example = "OU102203071410000010", required = true)
    private String receiverCode;
    @NotBlank(message = "receiverName cannot be empty")
    @ApiModelProperty(value = "receiverName from Request Source", example = "MAP", required = true)
    private String receiverName;
    @ApiModelProperty(value = "stateCode")
    private String stateCode;
    @ApiModelProperty(value = "cityCode")
    private String cityCode;
    @ApiModelProperty(value = "districtCode")
    private String districtCode;
    @ApiModelProperty(value = "address1")
    private String address1;
    @ApiModelProperty(value = "email")
    private String email;
    @ApiModelProperty(value = "phone")
    private String phone;
    @ApiModelProperty(value = "mobile")
    private String mobile;
    @ApiModelProperty(value = "requestRemarks")
    private String requestRemarks;
    @NotNull(message = "Please enter the quantity")
    @ApiModelProperty(value = "Number of Vouchers Total", example = "4000")
    private Integer voucherNum;
    @NotNull(message = "Please enter the quantity")
    @ApiModelProperty(value = "Voucher Amount Total ", example = "1200000000")
    private BigDecimal voucherAmount;
    @ApiModelProperty(value = "Currency code")
    private String currencyCode;
    @ApiModelProperty(value = "Permission code.", example = "1122333")
    private String permissionCode;
    @NotBlank(message = "The updater cannot do without")
    @ApiModelProperty(value = "Update user")
    private String updateUser;
    @Size(min = 1, message = "Pass at least one piece of data")
    @ApiModelProperty(value = "Update voucher request details requests", required = true)
    private List<UpdateVoucherRequestDetailsRequest> updateVoucherRequestDetailsRequests;
}
