package com.gtech.gvcore.common.request.voucherrequest;

import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/10 9:53
 */


@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("Query voucher request")
@Builder
public class QueryVoucherRequestRequest extends PageBean implements Serializable {

    private static final long serialVersionUID = -906193550646847743L;

    @ApiModelProperty(value = "Issuer code", example = "MAP",required = true)
    @NotBlank(message = "IssuerCode is required")
    private String issuerCode;

    @ApiModelProperty(value = "Request source", example = "OU102203011336000005")
    private String outletCode;

    @ApiModelProperty(value = "Request code", example = "OU102203011336000005")
    private String requestCode;

    @ApiModelProperty(value = "Start date time", example = "2021-01-02 10:10:10")
    private Date startDateTime;

    @ApiModelProperty(value = "End date time", example = "2021-01-02 10:10:10")
    private Date endDateTime;

    @ApiModelProperty(required = true, value = "Denomination，entity voucher：50000/100000/500000/1000000", example = "50000")
    private BigDecimal denomination;

    private String businessType;
    @ApiModelProperty(value = "Query by request status (" +
            "1:Pending Approval,2:Canceled,3:Rejected,4:Pending Allocation,5:Pending Receipt,6:Completed" +
            ")", required = true, example = "4")
    @Max(value = 6, message = "It can only be 1 to 6")
    @Min(value = 1, message = "It can only be 1 to 6")
    private Integer status;

    @ApiModelProperty(value = "login user code", example = "UC0001",required = true)
    @NotBlank(message = "UserCode is required")
    private String userCode;

}
