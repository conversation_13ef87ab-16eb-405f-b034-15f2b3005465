package com.gtech.gvcore.common.response.productcategory;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022年2月23日
 */
@Data
public class ProductCategoryCpgVo {

    private Long id;

    private String productCategoryCpgCode;

    private String cpgCode;

    private String cpgTypeCode;

    private Integer status;

    private String cpgName;

    private BigDecimal denomination;

    private String mopCode;

    private String disableGeneration;
    
    private String cpgType;

}
