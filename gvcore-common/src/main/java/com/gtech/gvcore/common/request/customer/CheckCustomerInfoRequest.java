package com.gtech.gvcore.common.request.customer;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/22 15:37
 */

@Data
@NoArgsConstructor
@ApiModel("Check customer info request")
public class CheckCustomerInfoRequest {

    @ApiModelProperty(value = "Customer code for check info", required = true)
    @NotBlank(message = "Customer code cannot be empty")
    private String customerCode;
}
