package com.gtech.gvcore.common.response.articlemop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 创建商品支付方式关联响应
 */
@Data
@ApiModel(value = "CreateArticleMopResponse", description = "创建商品支付方式关联响应")
public class CreateArticleMopResponse {
    /**
     * 商品支付方式关联编码
     */
    @ApiModelProperty(value = "商品支付方式关联编码")
    private String articleMopCode;
} 