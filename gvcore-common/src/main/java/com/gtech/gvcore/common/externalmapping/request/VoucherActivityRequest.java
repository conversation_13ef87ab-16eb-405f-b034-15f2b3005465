package com.gtech.gvcore.common.externalmapping.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Data
public class VoucherActivityRequest {

    @ApiModelProperty(value = "Transaction ID. ", example = "00001", required = true, notes = "An integer value. This value has to be incremented with every transaction within the batch. For e.g, after the first time init is done, when doing a balance enquiry for the first time, the value for this can be 1, for the second transaction, this should be 2 and so on. Basically, a unique incrementing sequence within the current batch")
    private Integer transactionId;
    @ApiModelProperty(value = "Activation Code. ", example = "123456", notes = "Intermediate reference to EGV number & PIN generated by eGMS. The activation code is part of the URL used to retrieve EGV information.")
    @Length(max = 256, message = "Activation Code maximum length 256")
    private String activationCode;
    @ApiModelProperty(value = "Amount. ", example = "1", notes = "Contains the balance on the card, with last two or three digits as implied decimal.")
    private BigDecimal amount;
    @ApiModelProperty(value = "Card Number. ", example = "123456", notes = "If activation code is not provided, then this is a mandatory parameter.")
    @Length(max = 50, message = "Card Number maximum length 50")
    private String voucherNumber;
    @ApiModelProperty(value = "Card Pin. ", example = "123456", notes = "PIN associated with the card. Entered by the cardholder/Operator(optional)")
    @Length(max = 50, message = "Card Pin maximum length 50")
    private String voucherPIN;
    @ApiModelProperty(value = "Notes. ", example = "0", notes = "Any Reference text to be captured along with this transaction.")
    @Length(max = 512, message = "Notes maximum length 512")
    private String notes;
    @ApiModelProperty(value = "Date At Client. ", example = "2022-02-02 18:00:00", required = true, notes = "The datetime at the client machine in YYYY-MM-DD HH:MM:SS")
    private String clientTime;
    @ApiModelProperty(value = "Customer. ", notes = "Customer object.")
    private CustomerInfo customerInfo;

//    private String tId;
//
//    private String invoiceNumber;


    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("transactionId", transactionId);
        map.put("activationCode", activationCode);
        map.put("amount", amount);
        map.put("cardNumber", voucherNumber);
        map.put("cardPIN", voucherPIN);
        map.put("notes", notes);
        map.put("dateAtClient", clientTime);
        if (this.customerInfo != null) {
            map.put("customer", customerInfo.toMap());
        }
//        map.put("terminalId", tId);
//        map.put("invoiceNumber",invoiceNumber);
        return map;
    }


}
