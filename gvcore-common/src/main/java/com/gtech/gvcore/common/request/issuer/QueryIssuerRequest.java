package com.gtech.gvcore.common.request.issuer;

import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryIssuerRequest")
public class QueryIssuerRequest extends PageBean {

    /**
     * issuer name
     */
    @ApiModelProperty( value="Issuer name.", example="Starbucks")
    @Length(max = 100)
    private String issuerName;

    @ApiModelProperty( value="Issuer code.", example="ISO1214124")
    @Length(max = 100)
    private String issuerCode;

    @ApiModelProperty( value="Issuer code list.", example="[ISO1214124,ISO1548]")
    private List<String> issuerCodes;


    @ApiModelProperty( value="Status.", example="1")
    @Length(max = 100)
    private String status;




}
