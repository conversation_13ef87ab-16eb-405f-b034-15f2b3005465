package com.gtech.gvcore.common.request.flow;

import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SendNoticeRequest {
	

    /**
     * flow code
     */
    @ApiModelProperty(value = "flow code" ,required = true)
    @NotEmpty(message = "flow code can't be empty")
    private String flowCode;

    /**
     * flow node code
     */
    @ApiModelProperty(value = "flow node code" ,required = true)
    @NotEmpty(message = "flow node code can't be empty")
    private String flowNodeCode;
    
    private String businessCode;
    
    private List<String> emails;
    
    private Map<String, Object> extendParams;

}