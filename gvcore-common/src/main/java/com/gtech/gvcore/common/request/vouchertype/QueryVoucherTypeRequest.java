package com.gtech.gvcore.common.request.vouchertype;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryVoucherTypeRequest")
public class QueryVoucherTypeRequest {

    @ApiModelProperty(value = "Dd value.", example = "1122333")
    @Length(max = 100)
    //ddValue prefix
    private String ddValue;


    @ApiModelProperty(value = "Dd text.", example = "1122333")
    @Length(max = 100)
    //ddText voucherTypeName
    private String ddText;



}
