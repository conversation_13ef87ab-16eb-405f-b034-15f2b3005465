package com.gtech.gvcore.common.request.voucherreturnantransfer;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.gtech.commons.page.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/25 15:25
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("Query voucher return and transfer request")
@Builder
public class QueryVoucherReturnAndTransferRequest extends PageParam implements Serializable {

    private static final long serialVersionUID = 1138710392052419616L;

    @ApiModelProperty(value = "Issuer code", example = "MAP")
    @NotBlank(message = "IssueCode is required")
    private String issuerCode;

    @ApiModelProperty(value = "From store name", example = "OU102203011336000005")
    private String outletCode;

    @ApiModelProperty(required = true, value = "Denomination，entity voucher：50000/100000/500000/1000000", example = "50000")
    private BigDecimal denomination;

    @ApiModelProperty(value = "Show status", required = false)
    private String showStatus;

    @ApiModelProperty(value = "Role list use ',' split", required = true)
    @NotBlank(message = "Role list can not be empty")
    private String roleList;

    @ApiModelProperty(value = "User code", required = true)
    @NotBlank(message = "User code can not be empty")
    private String userCode;

    @ApiModelProperty(value = "Has approve permission",notes = "是否具体审批权限: true-有,false-无", required = true)
    @NotNull(message = "HasApprovePermission is required!")
    private Boolean hasApprovePermission;

}
