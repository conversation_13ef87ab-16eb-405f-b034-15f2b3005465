package com.gtech.gvcore.common.response.cpg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年3月7日
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetCpgResponse {

    private Long id;

    private String cpgCode;

    private String cpgName;

    private String issuerCode;

    private String cpgTypeCode;

    private Integer gracePeriods;

    private Integer effectiveYears;

    private Integer effectiveMonth;

    private Integer effectiveDay;

    private Integer effectiveHour;

    private String currencyCode;

    private BigDecimal denomination;

    private String articleMopCode;
    
    private String articleCode;
    
    private String mopCode;

    private Integer bookletVoucherNum;

    private Integer status;

    private String createUser;

    private Date createTime;

    private String updateUser;

    private String disableGeneration;

    private Date updateTime;

    private List<CpgPrinterVo> printerList;

}
