package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @Date 2022/7/14 16:00
 */
public enum IssuerHandingUpperLevelTransactionTypeEnum {

    CANCEL_SALES(IssueHandlingTypeEnum.CANCEL_SALES, TransactionTypeEnum.GIFT_CARD_SELL),
    CANCEL_REDEEM(IssueHandlingTypeEnum.CANCEL_REDEEM, TransactionTypeEnum.GIFT_CARD_REDEEM),
    BULK_ACTIVATION(IssueHandlingTypeEnum.BULK_ACTIVATION, TransactionTypeEnum.GIFT_CARD_NEW_GENERATE),
    BULK_REDEEM(IssueHandlingTypeEnum.BULK_REDEEM, TransactionTypeEnum.GIFT_CARD_ACTIVATE),
    BULK_REISSUE(IssueHandlingTypeEnum.BULK_REISSUE, null),
    BULK_DEACTIVATE(IssueHandlingTypeEnum.BULK_DEACTIVATE, null),
    BUL<PERSON>_REACTIVATE(IssueHandlingTypeEnum.BULK_REACTIVATE, null),
    CHANGE_EXPIRY(IssueHandlingTypeEnum.CHANGE_EXPIRY, null),
    RESET_PIN_ACTIVE(IssueHandlingTypeEnum.RESET_PIN_ACTIVE, null);

    ;
    private IssueHandlingTypeEnum issueHandlingTypeEnum;

    private TransactionTypeEnum transactionTypeEnum;


    IssuerHandingUpperLevelTransactionTypeEnum(IssueHandlingTypeEnum code, TransactionTypeEnum desc) {
        this.issueHandlingTypeEnum = code;
        this.transactionTypeEnum = desc;
    }

    public static IssuerHandingUpperLevelTransactionTypeEnum valueOfIssueHandlingType(final IssueHandlingTypeEnum issueHandlingType) {

        for (IssuerHandingUpperLevelTransactionTypeEnum value : IssuerHandingUpperLevelTransactionTypeEnum.values()) {
            if (value.getCode().equals(issueHandlingType)) {
                return value;
            }
        }

        return null;
    }

    public IssueHandlingTypeEnum getCode() {
        return issueHandlingTypeEnum;
    }

    public TransactionTypeEnum getDesc() {
        return transactionTypeEnum;
    }

}
