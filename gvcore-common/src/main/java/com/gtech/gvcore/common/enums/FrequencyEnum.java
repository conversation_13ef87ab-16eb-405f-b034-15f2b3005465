package com.gtech.gvcore.common.enums;

import com.gtech.commons.code.IEnum;

/**
 * @ClassName FrequencyEnum
 * @Description 定时报表执行频率
 * <AUTHOR>
 * @Date 2022/8/18 14:51
 * @Version V1.0
 **/
public enum FrequencyEnum implements IEnum {

    /**
     * 执行一次
     */
    ONCE("once", "Once"),

    /**
     * 间隔天
     */
    DAILY("daily", "Daily"),

    /**
     * 间隔周
     */
    WEEKLY("weekly", "Weekly"),

    /**
     * 间隔月
     */
    MONTHLY("monthly", "Monthly"),

    /**
     * 间隔年
     */
    YEARLY("yearly", "Yearly"),
    ;

    public static final String REMARK   = "";

    FrequencyEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    //code
    private final String code;

    //desc
    private final String desc;


    public static FrequencyEnum valueOfCode(String code) {
        for (FrequencyEnum value : FrequencyEnum.values()) {
            if (value.code().equals(code)) {
                return value;
            }
        }
        return null;
    }

    @Override
    public String code() {

        return String.valueOf(code);
    }

    @Override
    public String desc() {

        return desc;
    }

    @Override
    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

}
