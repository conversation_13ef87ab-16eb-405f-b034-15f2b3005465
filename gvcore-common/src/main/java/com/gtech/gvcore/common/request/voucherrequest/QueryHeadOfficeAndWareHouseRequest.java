package com.gtech.gvcore.common.request.voucherrequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2023/1/4 14:24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("Query HeadOffice And WareHouse Request")
public class QueryHeadOfficeAndWareHouseRequest {

    @ApiModelProperty(value = "Issuer code", example = "MAP")
    private String issuerCode;

}
