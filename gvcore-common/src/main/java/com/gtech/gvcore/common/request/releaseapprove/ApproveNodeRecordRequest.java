package com.gtech.gvcore.common.request.releaseapprove;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnore;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/22 11:17
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "Approve node record request")
public class ApproveNodeRecordRequest implements Serializable {
    private static final long serialVersionUID = -3188060552854282010L;
    @ApiModelProperty(value = "Business code", required = true)
    @NotBlank(message = "Business code cannot be empty")
    private String businessCode;
    @ApiModelProperty(value = "Role code", required = true)
    @NotBlank(message = "Role code cannot be empty")
    private String roleCode;
    @JsonIgnore
    private String releaseApproveAmountType;
    @ApiModelProperty(hidden = true)
    private String releaseType;
    @JsonIgnore
    private BigDecimal voucherAmount;
    @ApiModelProperty("Note")
    private String note;
    @ApiModelProperty(value = "Approve user", required = true)
    @NotBlank(message = "Approve user code cannot be empty")
    private String approveUser;
    @ApiModelProperty(value = "Status:  true:agree  false:reject", required = true)
    @NotNull(message = "Status cannot be empty")
    private Boolean status;
	@ApiModelProperty(hidden = true)
	private Map<String, Object> extendParams;
	@ApiModelProperty(value = "Issuer code", hidden = true)
	private String issuerCode;
}
