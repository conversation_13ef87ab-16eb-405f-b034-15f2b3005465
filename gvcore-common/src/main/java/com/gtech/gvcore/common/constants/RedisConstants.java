package com.gtech.gvcore.common.constants;

public class RedisConstants {
	
	private RedisConstants() {
	}
	
	public static final String APPKEY = "gv";
	
	public static final String CACHE_SPLIT = ":";
	
	/**
	 * time unit ms
	 */
	public static final Long CACHE_TIMEOUT = 60000L;

	/**
	 * time unit ms
	 */
	public static final Long TASK_CACHE_TIMEOUT = 10 * 60 * 1000L;
	
	public static final String LOCK_KEY = "LOCK_KEY" + CACHE_SPLIT;
	
	public static final String LOCK_KEY_VOUCHER_LOG_TASK = LOCK_KEY + "voucher_Log_task" ;
	
	public static final String LOCK_KEY_ISSUEHANDLING_FILE = LOCK_KEY + "issueHandling_file" + CACHE_SPLIT;

    public static final String LOCK_KEY_APPROVE = LOCK_KEY + "approve" + CACHE_SPLIT;

    public static final String LOCK_KEY_AUTOMATIC_APPROVE = LOCK_KEY + "AUTOMATIC_APPROVE" + CACHE_SPLIT;

}
