package com.gtech.gvcore.common.request.cpg;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2022年2月22日
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetCpgRequest {

    @ApiModelProperty(value = "cpgCode", required = true)
    @NotEmpty(message = "cpgCode can not be empty")
    private String cpgCode;

}
