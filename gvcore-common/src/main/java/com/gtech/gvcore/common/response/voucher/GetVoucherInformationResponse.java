package com.gtech.gvcore.common.response.voucher;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/7/29 10:11
 */
@Data
public class GetVoucherInformationResponse {


    private String issuerCode;

    private String voucherBatchCode;

    private String bookletCode;

    private String voucherNumber;

    private String cpgCode;

    private String mopCode;

    private BigDecimal denomination;

    private String voucherPin;

    private String voucherBarcode;

    private Date voucherEffectiveDate;

    private String status;

    private Integer voucherStatus;

    private Integer circulationStatus;

    private String voucherActiveCode;

    private String voucherActiveUrl;

    private Date voucherUsedTime;

    private String voucherOwnerCode;

    private String voucherOwnerType;

//    private String permissionCode;







}
