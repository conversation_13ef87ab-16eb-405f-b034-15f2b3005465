package com.gtech.gvcore.common.response.distribution;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName ReceivedCpgInfoResponse
 * @Description 已接收的CPG信息
 * <AUTHOR>
 * @Date 2022/7/6 10:33
 * @Version V1.0
 **/
@Getter
@Setter
@ApiModel(value = "ReceivedCpgInfoResponse")
public class OwnedCpgInfoResponse {

    @ApiModelProperty(value = "CPG编码", example = "CP001")
    private String cpgCode;

    @ApiModelProperty(value = "CPG名称", example = "QA_TEST-VCR-100K")
    private String cpgName;

}
