package com.gtech.gvcore.common.request.merchant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "UpdateMerchantStatusRequest")
public class UpdateMerchantStatusRequest {


    @ApiModelProperty( value = "Merchant code.", example = "1345566",required = true)
    @NotEmpty(message = "merchantCode can not be empty")
    @Length(max = 100)
    private String merchantCode;

    @ApiModelProperty( value = "Status.", example = "0")
    private Integer status;

    @ApiModelProperty( value="Update user.", example="user1")
    @Length(max = 100)
    private String updateUser;



}
