package com.gtech.gvcore.common.response.gc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Activate Gift Card Response
 */
@Data
@ApiModel(value = "ActivateGiftCardResponse", description = "Activate Gift Card Response")
public class ActivateGiftCardResponse extends BaseApiResponse {


    @ApiModelProperty(value = "Notes", notes = "Reference text information", example = "Card activated successfully", position = 3)
    private String notes;

    @ApiModelProperty(value = "Retry Key", notes = "Reference number for the request", example = "RETRY123456", position = 4)
    private String retryKey;

    @ApiModelProperty(value = "Source", notes = "Reference for outlet name/POS name/brand name/partner name", example = "MOBILE_APP", position = 5)
    private String source;

    @ApiModelProperty(value = "Gift Card Information", notes = "Gift card detailed information", position = 6)
    private GiftCardInfo giftCards;

    @ApiModelProperty(value = "Transaction Date", notes = "Transaction date", example = "2023-01-01T12:00:00", position = 7)
    private Date transactionDate;

    @ApiModelProperty(value = "Batch Number", notes = "Batch number generated from token", example = "BATCH001", position = 8)
    private String batchNumber;


} 