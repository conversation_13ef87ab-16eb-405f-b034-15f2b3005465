package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @Date 2022/11/2 16:28
 */
public enum QCTransactionTypeEnum {

    GIFT_CARD_REDEEM("1", "GIFT CARD REDEEM"),
    GIFT_CARD_CREATED("9", "GIFT CARD CREATED"),
    GIFT_CARD_ACTIVATE("5", "GIFT CARD ACTIVATE"),
    GIFT_CARD_ACTIVATE_ONLY("22", "GIFT CARD ACTIVATE ONLY"),
    GIFT_CARD_EXPIRY("15", "GIFT CARD EXPIRY"),
    GIFT_CARD_BULK_CANCEL_REDEEM("11","GIFT CARD CANCEL REDEEM"),
    GIFT_CARD_ONE_TIME_BARCODE("19","ONE TIME BARCODE"),
    GIFT_CARD_DEACTIVATE("13", "GIFT CARD DEACTIVATE"),
    GIFT_CARD_REACTIVATE("14", "GIFT CARD REACTIVATE"),
    GIFT_CARD_CANCEL_ACTIVITY("20", "CANCEL ACTIVATE"),

    ;

    private final String code;

    private final String desc;

    QCTransactionTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Boolean doesItExist(String code){

        QCTransactionTypeEnum[] values = QCTransactionTypeEnum.values();
        for (QCTransactionTypeEnum value : values) {
            if (code.equals(value.getCode())){
                return true;
            }
        }
        return false;
    }

    public static String getTypeDesc(String code) {

        for (QCTransactionTypeEnum typeEnum : QCTransactionTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum.getDesc();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
