package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @Date 2022/3/23 10:04
 */
public enum VerifyVoucherTypeEnum {


    VOUCHER_ACTIVATION("0", "Activation"),
    VOUCHER_REDEMPTION("1", "Redemption"),
    VOUCHER_VERIFY_ACTIVATION("2", "VerifyActivation"),
    VOUCHER_VERIFY_REDEMPTION("3", "VerifyRedemption");

    private final String code;

    private final String desc;

    VerifyVoucherTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
