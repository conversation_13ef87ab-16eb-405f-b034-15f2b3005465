package com.gtech.gvcore.common.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * <AUTHOR>
 * @Date 2021/7/13 14:09
 */
@Slf4j
@Component
public class TransactionDataShardingConfig implements PreciseShardingAlgorithm<String> {
    @Override
    public String doSharding(Collection<String> collection, PreciseShardingValue<String> preciseShardingValue) {
        String value = preciseShardingValue.getValue();
        if (StringUtils.isNotBlank(value)) {
            //不去除字母的情况下转为全数字
            value = value.replaceAll("[a-zA-Z]", "");

            Long x = Long.valueOf(value) % collection.size();
            int i = x.intValue(); // NOSONAR
            return collection.toArray()[i].toString();
        } else {

            throw new IllegalArgumentException();
        }
    }

}
