package com.gtech.gvcore.common.response.gc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * Base class for all API responses, containing common fields like responseCode and responseMessage.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public abstract class BaseApiResponse {

    @ApiModelProperty(value = "Response Code", notes = "API response code, 0 indicates success, non-0 indicates failure", example = "0", position = 1)
    private Integer responseCode;

    @ApiModelProperty(value = "Response Message", notes = "Detailed response message or error information", example = "Transaction successful", position = 2)
    private String responseMessage;


    // protected String notes;
    // protected String source;
    // protected String batchNumber;
}
