package com.gtech.gvcore.common.request.productcategory;

import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

/**
 * 分页查询商品分类请求
 */
@Data
@ApiModel(value = "QueryProductCategoriesByPageRequest", description = "分页查询商品分类请求")
public class QueryProductCategoriesByPageRequest extends PageBean {
    @ApiModelProperty(value = "categoryName", required = false)
    private String categoryName;

    @ApiModelProperty(value = "issuerCode", required = true)
    @NotEmpty(message = "issuerCode can not be empty")
    private String issuerCode;
} 