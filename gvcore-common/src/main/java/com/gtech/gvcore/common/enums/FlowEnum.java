package com.gtech.gvcore.common.enums;

public enum FlowEnum {

	CUSTOMER_ORDER_FLOW("customer_order", "Customer order"),
	RETURN_VOUCHER_FLOW("return_voucher", "Return"),
	TRANSFER_ORDER_FLOW("transfer_voucher", "Transfer"),
	SALES_VOUCHER_FLOW("sales", "Sales"),
	ISSUE_HANDLING("issue_handling", "Issue handling"),
	CANCEL_SALES(ISSUE_HANDLING.getCode() + IssueHandlingTypeEnum.CANCEL_SALES.code(), "Bulk Cancel Activation"),
	CANCEL_REDEEM(ISSUE_HANDLING.getCode() + IssueHandlingTypeEnum.CANCEL_REDEEM.code(), "Bulk Cancel Redeem"),
	BULK_ACTIVATION(ISSUE_HANDLING.getCode() + IssueHandlingTypeEnum.BULK_ACTIVATION.code(), "Bulk Activation"),
	BULK_REDEEM(ISSUE_HANDLING.getCode() + IssueHandlingTypeEnum.BULK_REDEEM.code(), "Bulk Redeem"),
	BULK_REISSUE(ISSUE_HANDLING.getCode() + IssueHandlingTypeEnum.BULK_REISSUE.code(), "Bulk Reissue"),
	BULK_DEACTIVATE(ISSUE_HANDLING.getCode() + IssueHandlingTypeEnum.BULK_DEACTIVATE.code(), "Bulk Deactivated"),
	BULK_REACTIVATE(ISSUE_HANDLING.getCode() + IssueHandlingTypeEnum.BULK_REACTIVATE.code(), "Bulk Reactivated")
    ;
    private final String code;

    private final String desc;

    FlowEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

	public static String getFlowDescByCode(String code) {

		for (FlowEnum flowEnum : FlowEnum.values()) {
			if (flowEnum.getCode().equals(code)) {
				return flowEnum.getDesc();
			}
		}
		return null;
	}

}
