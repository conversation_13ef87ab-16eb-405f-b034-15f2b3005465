package com.gtech.gvcore.common.request.voucher;

import com.gtech.gvcore.common.request.transaction.CustomerInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class VerifyVoucherRequest {


    private String machineId;

    @ApiModelProperty(value = "Purchaser Info. ", notes = "CustomerInfo, Please see below for details Applicable for Activation")
    private CustomerInfo purchaserInfo;
    @ApiModelProperty(value = "Cardholder Info. ", notes = "CustomerInfo, Please see below for details Applicable for redemption")
    private CustomerInfo cardholderInfo;

    private List<VerifyVoucherInfo> verifyVoucherInfo;

    private String transactionId;

    private String notes;






}
