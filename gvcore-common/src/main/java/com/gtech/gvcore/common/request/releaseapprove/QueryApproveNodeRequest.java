package com.gtech.gvcore.common.request.releaseapprove;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "QueryApproveNodeRequest")
public class QueryApproveNodeRequest implements Serializable {
	private static final long serialVersionUID = -1416133818515003856L;
	@ApiModelProperty(value = "Business code", required = true)
    @NotBlank(message = "Business code cannot be empty")
    private String businessCode;
	@ApiModelProperty(example = "ReleaseApproveConfig or Return&TransferApproveConfig")
	@NotBlank(message = "Config type cannot be empty")
	private String configType;
	@ApiModelProperty(value = "Voucher amount", hidden = true)
	private BigDecimal voucherAmount;
	@ApiModelProperty(value = "permission code", hidden = true)
	private String permissionCode;
	@ApiModelProperty(value = "Issuer code", hidden = true)
	private String issuerCode;
}
