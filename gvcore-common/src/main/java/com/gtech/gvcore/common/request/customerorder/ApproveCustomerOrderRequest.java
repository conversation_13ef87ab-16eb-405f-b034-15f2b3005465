package com.gtech.gvcore.common.request.customerorder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/14 20:26
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "ApproveCustomerOrderRequest")
public class ApproveCustomerOrderRequest implements Serializable {
    private static final long serialVersionUID = 5786108272554924261L;
    @ApiModelProperty(value = "CustomerOrderCode", required = true, example = "CO12313214241")
    @NotBlank(message = "customerOrderCode is required")
    private String customerOrderCode;
    @ApiModelProperty(value = "userCode", required = true)
    @NotBlank(message = "userCode is required")
    private String userCode;
    @ApiModelProperty(value = "note")
    private String note;
    @ApiModelProperty(value = "status", required = true)
    @NotNull(message = "status is required")
    private Boolean status;
}
