package com.gtech.gvcore.common.request.customerorder;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/20 15:57
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "Get customer order request")
public class GetCustomerOrderRequest {
    @ApiModelProperty(value = "Customer order code", required = true, example = "CO102203211147000007")
    @NotBlank(message = "Customer order code")
    private String customerOrderCode;

	@ApiModelProperty(value = "Role list use ',' split", required = true)
	@NotBlank(message = "Role list can not be empty")
	private String roleList;
	@ApiModelProperty(value = "User code", required = true)
	@NotBlank(message = "User code can not be empty")
	private String userCode;

}
