package com.gtech.gvcore.common.request.customerorder;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年3月23日
 */
@Data
public class IssuanceRequest {
	
	@ApiModelProperty(value = "customerOrderCode", required = true)
    @NotEmpty(message = "customerOrderCode can not be empty")
	private String customerOrderCode;
	
	@ApiModelProperty(value = "voucherBatchList", required = false)
    @Valid
    private List<IssuanceVoucherBatch> voucherBatchList;
	
	@ApiModelProperty(value = "updateUser", required = true)
    @NotEmpty(message = "updateUser can not be empty")
    @Length(max = 50)
    private String updateUser;

}
