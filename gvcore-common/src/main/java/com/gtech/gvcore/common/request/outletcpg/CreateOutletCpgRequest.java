package com.gtech.gvcore.common.request.outletcpg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CreateOutletCpgRequest")
public class CreateOutletCpgRequest {


    @ApiModelProperty(value = "Outlet code.", example = "1122333",required = true)
    @NotEmpty(message = "outletCode can not be empty")
    @Length(max = 100)
    private String outletCode;

    @ApiModelProperty(value = "Cpg code.", example = "2123123",required = true)
    @NotEmpty(message = "cpgCode can not be empty")
    @Length(max = 100)
    private String cpgCode;

    @ApiModelProperty( value="Create user.", example="user1",required = true)
    @NotEmpty(message = "createUser can not be empty")
    @Length(max = 100)
    private String createUser;








}
