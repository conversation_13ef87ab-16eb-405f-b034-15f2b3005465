package com.gtech.gvcore.common.request.distribution;

import com.gtech.commons.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

/**
 * @ClassName QueryCustomerDistributionParam
 * @Description 查询客户分发列表入参
 * <AUTHOR>
 * @Date 2022/9/2 14:51
 * @Version V1.0
 **/
@Setter
@Getter
@Accessors(chain = true)
@ApiModel(description = "QueryCustomerDistributionParam")
public class QueryCustomerDistributionRequest extends PageParam {

    @ApiModelProperty(value = "客户编码", example = "UC001", required = true)
    @NotEmpty(message = "Customer code can not be empty")
    private String customerCode;

    private String distributionCode;

    private Date createTimeBegin;

    private Date createTimeEnd;

    private String cpgCode;

    private String status;

}
