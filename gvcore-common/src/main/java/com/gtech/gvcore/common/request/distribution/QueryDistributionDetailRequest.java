package com.gtech.gvcore.common.request.distribution;

import com.gtech.commons.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

/**
 * @ClassName QueryDistributionDetailRequest
 * @Description 分发详情请求实体
 * <AUTHOR>
 * @Date 2022/9/6 19:23
 * @Version V1.0
 **/
@Setter
@Getter
@Accessors(chain = true)
@ApiModel(description = "QueryDistributionDetailRequest")
public class QueryDistributionDetailRequest extends PageParam {

    @ApiModelProperty(value = "客户编码", example = "UC001", required = true)
    @NotEmpty(message = "Customer code can not be empty")
    private String customerCode;

    private String distributionCode;

    private Date createTimeBegin;

    private Date createTimeEnd;

    private String cpgCode;

    private String status;

    private String voucherCode;

    private String emailEndCustomerName;

    private String emailAddress;
}
