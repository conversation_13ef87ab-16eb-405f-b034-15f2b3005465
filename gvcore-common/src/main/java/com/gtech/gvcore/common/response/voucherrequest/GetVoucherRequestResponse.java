package com.gtech.gvcore.common.response.voucherrequest;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.gtech.gvcore.common.request.voucherrequest.CreateVoucherRequestDetailsRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/10 13:52
 */

@Data
@ApiModel("get a request voucher")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GetVoucherRequestResponse implements Serializable {
    private static final long serialVersionUID = 7105546048248539655L;
    @ApiModelProperty(value = "voucherRequestCode")
    private String voucherRequestCode;
    @ApiModelProperty(value = "Request Source,This is To Store name for return&transfer")
    private String receiverName;
    @ApiModelProperty(value = "Request Source code,This is To Store code for return&transfer")
    private String receiverCode;
    @ApiModelProperty(value = "This is From Store code for return&transfer")
    private String voucherOwnerCode;
    @ApiModelProperty(value = "This is From Store name for return&transfer")
    private String voucherOwnerName;
    @ApiModelProperty(value = "address1")
    private String address1;
    @ApiModelProperty(value = "email")
    private String email;
    @ApiModelProperty(value = "phone")
    private String phone;
    @ApiModelProperty(value = "mobile")
    private String mobile;
    @ApiModelProperty(value = "Request note time")
    private Date requestTime;
    @ApiModelProperty(value = "status")
    private Integer status;
    @ApiModelProperty(value = "Business type")
    private String businessType;
	@ApiModelProperty(value = "Voucher amount")
	private BigDecimal voucherAmount;
    @ApiModelProperty(value = "Voucher information")
    private List<CreateVoucherRequestDetailsRequest> createVoucherRequestDetailsRequests;

	private String issuerCode;
	private String showStatus;
	@ApiModelProperty(value = "Can I approve it?")
	private Boolean approveAble;
}
