package com.gtech.gvcore.common.externalmapping.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Data
public class CancelRedeemRequest {
    @ApiModelProperty(value = "Transaction ID. ", example = "SF00001", required = true, notes = "The transacƟ on id passed in the request.")
    private Integer transactionId;
    @ApiModelProperty(value = "Original Approval Code. ", example = "0", notes = "The approval code that was sent in API response of original transaction being canceled, if available.")
    @Length(max = 50, message = "Original Approval Code maximum length 50")
    private String originalApprovalCode;
    @ApiModelProperty(value = "Original Invoice Number. ", example = "0", notes = "The invoice number that was sent in the original transaction that we are trying to cancel, if available.")
    @Length(max = 50, message = "Original Invoice Number maximum length 50")
    private String originalInvoiceNumber;
    @ApiModelProperty(value = "Original Batch Number. ", example = "0",  required = true, notes = "The batch number that was sent in the original transaction that we are trying to cancel")
    @Length(max = 50, message = "Original Batch Number maximum length 50")
    private Integer originalBatchNumber;
    @ApiModelProperty(value = "Original Transaction Id. ", example = "0", required = true, notes = "The TransactionId that was sent in the original transaction that we are trying to cancel")
    @Length(max = 50, message = "Original Transaction Id maximum length 50")
    private Integer originalTransactionId;
    @ApiModelProperty(value = "Amount. ", example = "0", required = true, notes = "Cancellation amount")
    private BigDecimal amount;

    private String voucherNumber;

    private String voucherPIN;

    private BigDecimal originalAmount;

    private String notes;

    private String clientTime;

    public Map<String,Object> toMap(){
        Map<String,Object> map = new HashMap<>();
        map.put("transactionId",transactionId);
        map.put("originalApprovalCode",originalApprovalCode);
        map.put("originalInvoiceNumber",originalInvoiceNumber);
        map.put("originalBatchNumber",originalBatchNumber);
        map.put("originalTransactionId",originalTransactionId);
        map.put("amount",amount);
        map.put("cardNumber", voucherNumber);
        return map;
    }


}
