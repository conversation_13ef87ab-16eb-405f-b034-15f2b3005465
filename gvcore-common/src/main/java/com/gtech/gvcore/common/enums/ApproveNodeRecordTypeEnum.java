package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/16 16:39
 */

public enum ApproveNodeRecordTypeEnum {
    SUBMIT("Submit Note", "Submit Note"),
	APPROVE("Approve Note", "Approve Note"),
    RELEASE("Release Note", "Release Note"),
	EDIT("Edit Note", "Edit Note");

    private final String type;
    private final String desc;

    ApproveNodeRecordTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }


    public String getDesc() {
        return desc;
    }

    public String getType() {
        return type;
    }
}
