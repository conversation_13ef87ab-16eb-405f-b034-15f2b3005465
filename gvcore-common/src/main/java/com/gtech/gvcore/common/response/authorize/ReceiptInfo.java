package com.gtech.gvcore.common.response.authorize;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ReceiptInfo {

	@ApiModelProperty(value = "Receipt Footer Line 1.", notes = "configured receipt footer line1 in qc server")
	private String receiptLine1;
	@ApiModelProperty(value = "Receipt Footer Line 2.", notes = "configured receipt footer line2 in qc server")
	private String receiptLine2;
	@ApiModelProperty(value = "Receipt Footer Line 3.", notes = "configured receipt footer line3 in qc server")
	private String receiptLine3;
	@ApiModelProperty(value = "Receipt Footer Line 4.", notes = "configured receipt footer line4 in qc server")
	private String receiptLine4;
}