package com.gtech.gvcore.common.response.gc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Query Customer Card List Response
 */
@Data
@ApiModel(value = "GiftCardResponseCodesEnumListResponse", description = "Query Customer Card List Response")
public class QueryCustomerCardListResponse extends BaseApiResponse {


    @ApiModelProperty(value = "Notes", notes = "Reference text information")
    private String notes;

    @ApiModelProperty(value = "Retry Key", notes = "Reference number for the request")
    private String retryKey;

    @ApiModelProperty(value = "Customer ID", notes = "Customer unique identifier")
    private String customerId;

    @ApiModelProperty(value = "Customer Card List", notes = "List of gift cards owned by the customer")
    private List<GiftCardInfo> cards;

    @ApiModelProperty(value = "Total Records", notes = "Total number of records")
    private Long total;

    @ApiModelProperty(value = "Page Number", notes = "Current page number")
    private Integer pageNum;

    @ApiModelProperty(value = "Page Size", notes = "Number of records per page")
    private Integer pageSize;

}