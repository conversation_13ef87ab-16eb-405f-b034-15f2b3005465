package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @Date 2022/3/23 10:04
 */
public enum RedemptionStatusEnum {


    REDEEMED("REDEEMED", "Redeem"),
    CANCELLED_REDEEMED("CANCELLED_REDEEM", "Redemption cancelled");

    private final String code;

    private final String desc;

    RedemptionStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
