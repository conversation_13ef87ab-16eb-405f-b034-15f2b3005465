package com.gtech.gvcore.common.enums;

import java.util.Objects;

public enum IssueHandlingStatusEnum implements IEnum<Integer> {
    CREATED(0, "Created"),
    SUBMIT(1, "SUBMIT"),
    APPROVE(2, "APPROVE"),
    CANCEL(3, "Cancel"),
    REJECT(4, "Reject"),
    EXECUTING(5, "Executing");

    private final Integer code;

    private final String desc;

    IssueHandlingStatusEnum(Integer code, String desc) {

        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer code() {
        return this.code;
    }

    @Override
    public String desc() {
        return this.desc;
    }

    @Override
    public boolean equalsCode(Integer code) {
        return this.code.equals(code);
    }

    public static IssueHandlingStatusEnum valueOfCode(final Integer code) {

        for (IssueHandlingStatusEnum value : IssueHandlingStatusEnum.values()) {
            if (Objects.equals(code, value.code)) {
                return value;
            }
        }
        return null;
    }
}
