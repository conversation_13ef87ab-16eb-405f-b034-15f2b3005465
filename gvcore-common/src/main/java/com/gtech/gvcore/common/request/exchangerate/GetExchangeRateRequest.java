package com.gtech.gvcore.common.request.exchangerate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> Gao.Yuhua
 * @date 2022-03-02
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "GetExchangeRateRequest")
public class GetExchangeRateRequest {

    /**
     * exchangeRateCode
     */
    @NotEmpty(message = "exchangeRateCode can not be empty")
    @Length(max = 50)
    @ApiModelProperty(value = "exchangeRateCode", example = "currencyCode")
    private String exchangeRateCode;

}
