package com.gtech.gvcore.common.request.receive;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CreateVoucherReceiveRequest {


    /**
     * source type:  generate, allocation
     */
	@ApiModelProperty(value = "Source type:  generate, sales, return, transfer")
    private String sourceType;

    /**
     * source data code
     */
	@ApiModelProperty(value = "Source data code")
    private String sourceDataCode;

    /**
     * issuer code
     */
	@ApiModelProperty(value = "Issuer code")
    private String issuerCode;

	@ApiModelProperty(value = "Receiver code")
	private String receiverCode;

    /**
     * outbound name
     */
	@ApiModelProperty(value = "Outbound name")
    private String outbound;

    /**
     * inbound name
     */
	@ApiModelProperty(value = "Inbound name")
    private String inbound;

    /**
     * outbound name
     */
    @ApiModelProperty(value = "Outbound name")
    private String outboundCode;

    /**
     * inbound name
     */
    @ApiModelProperty(value = "Inbound name")
    private String inboundCode;

    /**
     * Total number of vouchers
     */
	@ApiModelProperty(value = "Total number of vouchers")
    private Integer voucherNum;

    /**
     * received number of vouchers
     */
	@ApiModelProperty(value = "Received number of vouchers")
    private Integer receivedNum;

    /**
     * status, 0:Processing, 1:Completed
     */
	@ApiModelProperty(value = "Status, 0:Processing, 1:Completed")
    private Integer status;

    /**
     * create user
     */
	@ApiModelProperty(value = "Create user")
    private String createUser;

	List<VoucherReceiveBatchRequest> receiveBatchList;
}