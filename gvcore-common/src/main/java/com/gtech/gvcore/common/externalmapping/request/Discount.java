package com.gtech.gvcore.common.externalmapping.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

@Data
public class Discount {

    @ApiModelProperty(value = "Discount Percentage. ", example = "1", notes = "Discount in percentage")
    private Integer discountPercentage;
    //ETP 金额类型改为String
    @ApiModelProperty(value = "Discount Amount. ", example = "1", notes = "Discount in Amount")
    private String discountAmount;


    public Map<String,Object> toMap(){
        Map<String,Object> map = new java.util.HashMap<>();
        map.put("discountPercentage",discountPercentage);
        map.put("discountAmount",discountAmount);
        return map;
    }


}
