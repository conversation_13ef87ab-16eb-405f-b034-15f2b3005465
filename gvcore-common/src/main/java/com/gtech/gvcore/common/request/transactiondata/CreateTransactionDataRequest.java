package com.gtech.gvcore.common.request.transactiondata;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CreateTransactionDataRequest")
public class CreateTransactionDataRequest {


    private String transactionCode;

    private String transactionId;


    /**
     * Transaction type
     */
    private String transactionType;

    private String merchantCode;

    private String outletCode;
    private String issuerCode;


    private String cpgCode;
    /**
     * Transaction date
     */
    private Date transactionDate;

    private String voucherCode;
    private Long voucherCodeNum;
    /**
     * Initiators
     */
    private String initiatedBy;

    private String posCode;

    private String batchCode;
    private String batchId;
    /**
     * Login Source (WEBPOS)
     */
    private String loginSource;

    private BigDecimal denomination;
    /**
     * Merchant
     */
    private String actualOutlet;
    /**
     * Forwarding Entity Id
     */
    private String forwardingEntityId;

    private String responseMessage;
    /**
     * Transaction Mode (Online   )
     */
    private String transactionMode;

    private String customerCode;


    /**
     * Customer Salutation
     */
    private String customerSalutation;
    /**
     * Customer First Name
     */
    private String customerFirstName;
    /**
     * Customer Last Name
     */
    private String customerLastName;
    /**
     * Mobile
     */
    private String mobile;
    /**
     * Invoice Number
     */
    private String invoiceNumber;
    /**
     * Other Input Parameter
     */
//    private String otherInputParameter;
    /**
     * 0-failure  1-success
     */
    private String successOrFailure;
    /**
     * create user
     */
    private String createUser;
    /**
     * create time
     */
    private Date createTime;

    private String referenceNumber;

    private String corporateName;

    private String cardEntryMode;

    private Date voucherEffectiveDate;

    private String customerType;

    private String mopCode;

    private String notes;

    private String transactionChannel;

    private String email;

    private String approveCode;

}
