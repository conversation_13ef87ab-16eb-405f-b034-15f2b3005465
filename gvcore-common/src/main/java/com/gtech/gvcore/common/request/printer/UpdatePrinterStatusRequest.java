package com.gtech.gvcore.common.request.printer;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022年2月17日
 */
@Data
@ApiModel(value = "UpdatePrinterStatusRequest")
public class UpdatePrinterStatusRequest{


    /**
     * printerCode
     */
    @NotEmpty(message = "printerCode can not be empty")
    @ApiModelProperty(value = "printerCode", example = "CPC102202241512000241", required = true)
    private String printerCode;

    @ApiModelProperty(value = "status", required = true)
    @NotNull(message = "status can not be null")
    @Range(min = 0, max = 1, message = "status value range [0,1]")
    private Integer status;

    @ApiModelProperty(value = "updateUser", required = true)
    @NotEmpty(message = "updateUser can not be empty")
    @Length(max = 50)
    private String updateUser;

}
