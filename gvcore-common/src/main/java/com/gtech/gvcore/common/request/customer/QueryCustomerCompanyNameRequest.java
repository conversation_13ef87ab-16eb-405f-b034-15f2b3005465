package com.gtech.gvcore.common.request.customer;

import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

@Data
@NoArgsConstructor
@ApiModel("QueryCustomerCompanyNameRequest")
public class QueryCustomerCompanyNameRequest extends PageBean {


    @ApiModelProperty(value = "Issuer code.", example = "123124", required = true)
    @NotEmpty(message = "issuerCode can not be empty")
    @Length(max = 100)
    private String issuerCode;

}
