package com.gtech.gvcore.common.enums;


public enum ApproveTypeEnum {

	APPROVE_TYPE_01("ReleaseApproveConfig", "Release Note"),
    APPROVE_TYPE_02("Return&TransferApproveConfig", "Approve Note"),
    APPROVE_TYPE_03("IssueHandlingApproveConfig", "Release Note");

    private final String type;
	private final String desc;

	ApproveTypeEnum(String type, String desc) {
        this.type = type;
		this.desc = desc;
    }


	public String getDesc() {
		return desc;
    }

    public String getType() {
        return type;
    }

	public static String getByType(String type) {
		for (ApproveTypeEnum approveTypeEnum : ApproveTypeEnum.values()) {
			if (approveTypeEnum.getType().equals(type)) {
				return approveTypeEnum.getDesc();
			}
		}
		return null;
	}
}
