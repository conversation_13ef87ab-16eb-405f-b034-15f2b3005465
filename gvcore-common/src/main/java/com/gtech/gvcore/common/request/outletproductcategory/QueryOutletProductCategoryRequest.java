package com.gtech.gvcore.common.request.outletproductcategory;

import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;


/**
 * <AUTHOR>
 * @Date 2022/2/11 16:26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryOutletProductCategoryRequest")
public class QueryOutletProductCategoryRequest extends PageBean {

    @ApiModelProperty(value = "Outlet product category code.", example = "112233")
    @Length(max = 100)
    private String outletProductCategoryCode;

    @ApiModelProperty(value = "Outlet code.", example = "1122333")
    @Length(max = 100)
    private String outletCode;

    @ApiModelProperty(value = "Product category code.", example = "2123123")
    @Length(max = 100)
    private String productCategoryCode;

    @ApiModelProperty(value = "Status.", example = "1")
    private Integer status;



}
