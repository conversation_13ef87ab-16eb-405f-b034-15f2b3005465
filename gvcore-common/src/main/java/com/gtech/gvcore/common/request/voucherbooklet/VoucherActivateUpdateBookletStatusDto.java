package com.gtech.gvcore.common.request.voucherbooklet;

import com.gtech.gvcore.common.enums.BookletStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/11/1 14:12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VoucherActivateUpdateBookletStatusDto {

    private List<String> voucherCode;
    private List<String> bookletCodeList;

    private String bookletCode;

    private String voucherBatchCode;

    // voucher - 0    booklet - 1   vouchetBatch - 2
    private String type;

    private BookletStatusEnum statusEnum;


}
