package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR> @Date 2022/3/11 11:44
 */
public enum VoucherStatusResultEnum {

    VOUCHER_NEWLY_GENERATED(0, "PURCHASED","130"),
    VOUCHER_ACTIVATED(1, "ACTIVATED","140"),
    VOUCHER_USED(2, "USER","140"),
    VOUCHER_CANCELLED(3, "CANCELLED","310"),
    VOUCHER_EXPIRED(4, "EXPIRED","120");

    private final int code;

    private final String desc;

    private final String statusId;

    VoucherStatusResultEnum(int code, String desc, String statusId) {
        this.code = code;
        this.desc = desc;
        this.statusId = statusId;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
    public String getStatusId() {
        return statusId;
    }

    public boolean equalsCode(Integer code) {

        return null != code && code.equals(this.code);
    }


    public static String getDescByCode(Integer code) {
        if (null == code){
            return "";
        }
        for(VoucherStatusResultEnum flowNodeEunm : VoucherStatusResultEnum.values()) {
            if (flowNodeEunm.getCode() == code) {
                return flowNodeEunm.getDesc();
            }
        }
        return "";
    }


    public static String getStatusIdByCode(Integer code) {
        for(VoucherStatusResultEnum flowNodeEunm : VoucherStatusResultEnum.values()) {
            if (flowNodeEunm.getCode() == code) {
                return flowNodeEunm.getStatusId();
            }
        }
        return null;
    }

}
