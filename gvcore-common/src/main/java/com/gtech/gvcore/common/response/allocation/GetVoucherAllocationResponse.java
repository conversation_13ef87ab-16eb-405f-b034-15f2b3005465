package com.gtech.gvcore.common.response.allocation;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年3月11日
 */
@Data
public class GetVoucherAllocationResponse {

    private String voucherAllocationCode;

    private String voucherRequestCode;

    private String issuerCode;

    private String sourceDataCode;

    private String businessType;

    private String receiverCode;

    private String receiverName;

    private String stateCode;
    
    private String stateName;

    private String cityCode;
    
    private String cityName;

    private String districtCode;
    
    private String districtName;

    private String address1;

    private String email;

    private String phone;

    private String mobile;

    private String requestRemarks;

    private Integer status;

    private Integer voucherNum;

    private BigDecimal voucherAmount;

    private String currencyCode;

    private List<AllocationRequestDetails> detailsList;

}
