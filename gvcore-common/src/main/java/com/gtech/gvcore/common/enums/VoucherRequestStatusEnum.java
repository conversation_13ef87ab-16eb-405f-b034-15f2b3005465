package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/8 18:35
 */

public enum VoucherRequestStatusEnum {

    PENDING_APPROVAL(1, "Pending Approval"),
    CANCELED(2, "Canceled"),
    REJECTED(3, "Rejected"),
    PENDING_ALLOCATION(4, "Pending Allocation"),
    PENDING_RECEIPT(5, "Pending Receipt"),
    COMPLETED(6, "Completed")
    ;
    private final int code;

    private final String desc;

    VoucherRequestStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static VoucherRequestStatusEnum valueOfCode(String code) {

        return valueOfCode(Integer.parseInt(code));
    }

    public static VoucherRequestStatusEnum valueOfCode(int code) {
        for (VoucherRequestStatusEnum status : VoucherRequestStatusEnum.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    public boolean equalsCode(String status) {

        return equalsCode(Integer.parseInt(status));
    }

    public boolean equalsCode(int status) {

        return this.code == status;
    }
}
