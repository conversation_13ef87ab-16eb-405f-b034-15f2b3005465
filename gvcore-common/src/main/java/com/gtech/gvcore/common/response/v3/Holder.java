
package com.gtech.gvcore.common.response.v3;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**

 */
@Data
@JsonNaming(PropertyNamingStrategy.UpperCamelCaseStrategy.class)
@JsonPropertyOrder({"Salutation","FirstName","LastName"})
public class Holder implements Serializable {

    private static final long serialVersionUID = 8998240126255838645L;
    @JsonProperty("Salutation")
    private String Salutation;
    @JsonProperty("FirstName")
    private String FirstName;
    @JsonProperty("LastName")
    private String LastName;


}