package com.gtech.gvcore.common.request.customerproductcategory;

import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryCustomerProductCategoryRequest")
public class QueryCustomerProductCategoryRequest extends PageBean {

    @ApiModelProperty(value = "Customer product category.", example = "12341412")
    @Length(max = 100)
    private String customerProductCategoryCode;

    @ApiModelProperty(value = "Customer code.", example = "12312312")
    @Length(max = 100)
    private String customerCode;

    @ApiModelProperty(value = "Product category code.", example = "11221312")
    @Length(max = 100)
    private String productCategoryCode;

    @ApiModelProperty(value = "Category type.", example = "voucher", notes = "Category type, possible values: voucher, gc")
    @Length(max = 20)
    private String categoryType;

    @ApiModelProperty(value = "Status.", example = "1")
    private Integer status;
}
