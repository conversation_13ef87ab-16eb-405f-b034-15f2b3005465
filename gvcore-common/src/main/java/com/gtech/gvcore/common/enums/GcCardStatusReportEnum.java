package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @Date 2022/11/2 15:32
 */
public enum GcCardStatusReportEnum {
    PURCHASED("Purchased"),
    ACTIVATION_PERIOD_ENDED("Activation Period Ended"),
    ACTIVATION_PERIOD_EXTENDED("Activation Period Extended"),
    ACTIVATED("Activated"),
    ZERO_BALANCE("Zero Balance"),
    EXPIRED("Expired"),
    DEACTIVATED("Deactivated"),
    DESTROY("Destroy");

    String status;

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
       return this.status;
    }

    GcCardStatusReportEnum(String status) {
        this.status = status;
    }
}
