package com.gtech.gvcore.common.response.orderreport;

import com.gtech.commons.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/22 15:42
 */

@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "QueryConditions")
public class QueryConditions implements Serializable {
    private static final long serialVersionUID = 4765796525060494288L;

    @ApiModelProperty(value = "Company code")
    private String[] companyCodes;

    @ApiModelProperty(value = "Voucher program group code")
    private String[] cpgCodes;

    @ApiModelProperty(value = "Create user")
    private String createUser;

    @ApiModelProperty(value = "Create user email")
    private String createUserEmail;

    @ApiModelProperty(value = "Customer code")
    private String[] customerCodes;

    @ApiModelProperty(value = "Invoice number")
    private String invoiceNo;

    @ApiModelProperty(value = "Issuer code", example = "IS102203031638001252")
    private String issuerCode;

    @ApiModelProperty(value = "Merchant code")
    private String[] merchantCodes;

    @ApiModelProperty(value = "Order status")
    private String[] orderStatuses;

    @ApiModelProperty(value = "Outlet code")
    private String[] outletCodes;

    @ApiModelProperty(value = "Transaction type")
    private String[] transactionTypes;

    @ApiModelProperty(value = "Transaction status")
    private String transactionStatus;

    @ApiModelProperty(value = "Purchase order number")
    private String purchaseOrderNo;

    @ApiModelProperty(value = "Report type")
    private Integer reportType;

    @ApiModelProperty(value = "Customer type")
    private String customerType;

    @ApiModelProperty(value = "Transaction date start")
    private Date transactionDateStart;

    @ApiModelProperty(value = "Transaction date end")
    private Date transactionDateEnd;

    public Date getTransactionDateEnd() {
        return DateUtil.getEndOfDay(transactionDateEnd);
    }

    @ApiModelProperty(value = "Voucher code num start")
    private String voucherCodeNumStart;

    @ApiModelProperty(value = "Voucher code number end")
    private String voucherCodeNumEnd;

    @ApiModelProperty(value = "Voucher effective Date start")
    private Date voucherEffectiveDateStart;

    @ApiModelProperty(value = "Voucher effective date end")
    private Date voucherEffectiveDateEnd;

    public Date getVoucherEffectiveDateEnd() {
        return DateUtil.getEndOfDay(voucherEffectiveDateEnd);
    }

    @ApiModelProperty(value = "Voucher start number")
    private String voucherStartNo;

    @ApiModelProperty(value = "Voucher end number")
    private String voucherEndNo;

    @ApiModelProperty(value = "Booklet status")
    private String bookletStatus;

    @ApiModelProperty(value = "Booklet start")
    private String bookletStart;

    @ApiModelProperty(value = "Booklet end")
    private String bookletEnd;

    @ApiModelProperty(value = "voucherStatus")
    private String voucherStatus;


    @ApiModelProperty(value = "操作时间起始")
    private Date operateTimeBegin;

    @ApiModelProperty(value = "操作时间截止")
    private Date operateTimeEnd;

    public Date getOperateTimeEnd() {
        return DateUtil.getEndOfDay(operateTimeEnd);
    }

    @ApiModelProperty(value = "操作人编码")
    private String operateUserCode;

    @ApiModelProperty(value = "操作人编码")
    private String operateUserName;

    @ApiModelProperty(value = "请求ID (Audit 表示 loggerId / GoodsTransit 表示 RequestId)")
    private String requestId;

    @ApiModelProperty(value = "请求ID (Audit 表示 loggerId / GoodsTransit 表示 RequestId)")
    private List<String> requestIdList;

    @ApiModelProperty(value = "outbound Outlet code")
    private String[] outboundCodeList;

    @ApiModelProperty(value = "inbound Outlet code")
    private String[] inboundCodeList;

    @ApiModelProperty(value = "uploadedFileUrl")
    private String uploadedFileUrl;

    @ApiModelProperty(value = "uploadedFileName")
    private String uploadedFileName;

    @ApiModelProperty(name = "contact_division")
    private String contactDivision;

    @ApiModelProperty(value = "Transaction Corporate Name.")
    private String transactionCorporateName;
}
