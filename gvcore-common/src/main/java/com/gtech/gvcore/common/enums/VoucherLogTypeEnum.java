package com.gtech.gvcore.common.enums;

public enum VoucherLogTypeEnum implements IEnum<String> {
	//0-Activation  1-Redemption 2-VerifyActivation  3-VerifyRedemption
	ALLOCATION("allocation", "allocation"),
	ALLOCATION_CANCEL("allocation_cancel", "allocation cancel"),
	CUSTOMERORDER_RELEASE("customerorder_release", "customer order release"),
	RECEIVE("receive", "receive"),
	VOUCHER_TO_BE_RECEIVED("to be receive","to be receive"),
    PURCHASED("Purchased", "Purchased"),
	ACTIVATION("activation", "0"),
	REDEMPTION("redemption", "1"),
	VERIFY_ACTIVATION("verify activation", "2"),
	VERIFY_REDEMPTION("verify redemption", "3"),
	PHYSICAL_VOUCHER("physical voucher", "physical voucher"),
	DIGITAL_VOUCHER("digital voucher", "digital voucher"),
	ULTRA_DIGITAL_VOUCHER("ultra digital voucher", "ultra digital voucher"),
	BLOCK_VOUCHER("block voucher", "block voucher"),
	UN_BLOCK_VOUCHER("un block voucher", "un block voucher"),
	CANCEL_REDEMPTION("cancel redemption", "cancel redemption"),
	RESET_PIN_ACTIVE("reset_pin_active","Reset pin active"),
	CHANGE_EXPIRY("change_expiry","Change expiry"),
	REISSUE("reissue","Reissue"),
	MERGE("merge","Merge"),
	CANCEL_MERGE("cancel_merge","Cancel merge"),
	REGENERATE_ACTIVATION_CODE("regenerate_activation_code","Regenerate activation code"),
    ;



    private final String code;

    private final String desc;

    VoucherLogTypeEnum(String code, String desc) {

        this.code = code;
        this.desc = desc;
    }

	@Override
	public String code() {
		return this.code;
	}

	@Override
	public String desc() {
		return this.desc;
	}

	@Override
	public boolean equalsCode(String code) {
		return this.code.equals(code);
	}

	public static String getCodeByDesc(String desc) {
		for (VoucherLogTypeEnum value : VoucherLogTypeEnum.values()) {
			if (value.desc().equals(desc)) {
				return value.code();
			}
		}
		return null;
	}

}
