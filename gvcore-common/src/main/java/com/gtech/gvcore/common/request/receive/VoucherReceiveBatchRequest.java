package com.gtech.gvcore.common.request.receive;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class VoucherReceiveBatchRequest {

    /**
     * voucher receive batch code
     */
	@ApiModelProperty(value = "voucher receive batch code")
    private String voucherReceiveBatchCode;

    /**
     * voucher receive code
     */
	@ApiModelProperty(value = "voucher receive code")
    private String voucherReceiveCode;

    /**
     * cpg code
     */
	@ApiModelProperty(value = "cpg code")
    private String cpgCode;

    /**
     * voucher start NO
     */
	@ApiModelProperty(value = "voucher start no")
    private String voucherStartNo;

    /**
     * voucher end NO
     */
	@ApiModelProperty(value = "voucher end no")
    private String voucherEndNo;

    /**
     * number of vouchers
     */
	@ApiModelProperty(value = "voucher num")
    private Integer voucherNum;

    /**
     * denomination
     */
    private BigDecimal denomination;

    /**
     * received number of vouchers
     */
	@ApiModelProperty(value = "received num")
    private Integer receivedNum;

    /**
     * booklet start NO
     */
	@ApiModelProperty(value = "booklet start no")
    private String bookletStartNo;

    /**
     * booklet end NO
     */
	@ApiModelProperty(value = "booklet end no")
    private String bookletEndNo;

    /**
     * booklet number
     */
	@ApiModelProperty(value = "booklet num")
    private Integer bookletNum;

    /**
     * create user
     */
	@ApiModelProperty(value = "create user")
    private String createUser;


}