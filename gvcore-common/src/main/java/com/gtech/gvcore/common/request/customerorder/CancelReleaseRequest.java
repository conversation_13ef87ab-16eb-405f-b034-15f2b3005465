package com.gtech.gvcore.common.request.customerorder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/14 18:28
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "CancelReleaseRequest")
public class CancelReleaseRequest implements Serializable {
    private static final long serialVersionUID = -6238179224259194192L;

    @ApiModelProperty(value = "Customer order code", required = true, example = "CO1231412415251")
    @NotBlank(message = "Customer order code is required")
    private String customerOrderCode;

    @ApiModelProperty(value = "userCode", required = true, example = "userCode")
    @NotBlank(message = "userCode is required")
    private String userCode;
}
