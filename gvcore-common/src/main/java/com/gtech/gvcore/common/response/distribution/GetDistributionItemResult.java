package com.gtech.gvcore.common.response.distribution;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@ApiModel(description = "GetDistributionItemResult")
public class GetDistributionItemResult {

    @ApiModelProperty(value = "Cpg Code", example = "CPG001")
    private String cpgCode;

    @JsonFormat(pattern = DateUtil.FORMAT_YYYYMMDDHHMISS)
    @ApiModelProperty(value = "Cpg Expiry Date", example = "CPG001")
    private Date expiryDate;

    private Integer inventory;

    @ApiModelProperty(value = "Number of vouchers per email.Must be greater than or equal to 1", example = "1")
    private Integer vouchersPerEmail;

    @ApiModelProperty(value = "Recipient email address. multiple use ';' separated", example = "endCustomerName <<EMAIL>>;")
    private String recipients;

    @ApiModelProperty(value = "Voucher amount.Must be greater than or equal to 0", example = "1")
    private BigDecimal voucherAmount;

    @JsonFormat(pattern = DateUtil.FORMAT_YYYYMMDDHHMISS)
    @ApiModelProperty(value = "start distribute")
    private Date startDistribute;

    @ApiModelProperty(value = "vpg name")
    private String vpgName;

    @ApiModelProperty(value = "status")
    private String status;

    @ApiModelProperty(value = "failure")
    private Integer failure;

    @ApiModelProperty(value = "recipients num")
    private Integer recipientsNum;

    @ApiModelProperty(value = "failure array")
    private List<GetDistributionItemDetailResult> failureArray;

    @ApiModelProperty(value = "Success")
    public Integer getSuccess() {

        return null == recipientsNum ? 0 : (recipientsNum * ConvertUtils.toInteger(this.vouchersPerEmail, 1))
                - ConvertUtils.toInteger(failure, 0);
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ApiModel(description = "GetDistributionItemResult")
    public static class GetDistributionItemDetailResult {

        @ApiModelProperty(value = "email address")
        private String emailAddress;

        @ApiModelProperty(value = "error msg")
        private String errorMsg;

    }


}
