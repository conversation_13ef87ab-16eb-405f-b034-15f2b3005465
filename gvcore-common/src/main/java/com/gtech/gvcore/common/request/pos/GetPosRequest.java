package com.gtech.gvcore.common.request.pos;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "GetPosRequest")
public class GetPosRequest {


    @ApiModelProperty(value = "Pos code.", example = "2123123",required = true)
    @NotEmpty(message = "posCode can not be empty")
    @Length(max = 100)
    private String posCode;

    @ApiModelProperty(value = "Machine id.", example = "2123123",hidden = true)
    private String machineId;


}
