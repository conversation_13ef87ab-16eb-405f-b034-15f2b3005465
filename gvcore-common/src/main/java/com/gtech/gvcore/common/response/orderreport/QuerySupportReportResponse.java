package com.gtech.gvcore.common.response.orderreport;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName QuerySupportReportList
 * @Description
 * <AUTHOR>
 * @Date 2023/1/30 15:47
 * @Version V1.0
 **/
@Setter
@Getter
@Accessors(chain = true)
@ApiModel(description = "QuerySupportReportList")
public class QuerySupportReportResponse {

    @ApiModelProperty(value = "report code")
    private Integer reportCode;

    @ApiModelProperty(value = "report name")
    private String reportName;

}
