package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @Date 2022/3/11 11:44
 */
public enum VoucherCirculationStatusEnum {

    /**
     * 0-新增
     * 2-待接收
     * 3-已接收
     * 4-分发中
     * 5-已分发
     */

	VOUCHER_NEWLY_GENERATED(0, "New"),
    VOUCHER_TO_BE_RECEIVED(2, "To be received"),
    VOUCHER_RECEIVED(3, "Received"),
    VOUCHER_DISTRIBUTING(4, "Distributing"),
    VOUCHER_DISTRIBUTED(5, "Distributed"),
    ;

    private final int code;

    private final String desc;

    VoucherCirculationStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

	public static String getDesc(int code) {

		for (VoucherCirculationStatusEnum statusEnum : VoucherCirculationStatusEnum.values()) {
			if (statusEnum.getCode() == code) {
				return statusEnum.getDesc();
			}
		}
		return "";
	}

}
