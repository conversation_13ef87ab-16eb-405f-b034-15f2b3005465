package com.gtech.gvcore.common.response.exchangerate;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> Gao.Yuhua
 * @date : 2022-03-02
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "GetExchangeRateResponse")
public class GetExchangeRateResponse {

    /**
     * exchange rate code,data UNIQUE KEY
     */
    private String exchangeRateCode;
    /**
     * currency code
     */
    private String currencyCode;
    /**
     * exchange rate
     */
    private BigDecimal exchangeRate;
    /**
     * exchange currency code
     */
    private String exchangeCurrencyCode;

    /**
     * exchange exchange_rate_date
     */
    private Date exchangeRateDate;

    /**
     * status,0:disable,1:enable
     */
    private Integer status;
    /**
     * create user
     */
    private String createUser;
    /**
     * create time
     */
    private Date createTime;
}
