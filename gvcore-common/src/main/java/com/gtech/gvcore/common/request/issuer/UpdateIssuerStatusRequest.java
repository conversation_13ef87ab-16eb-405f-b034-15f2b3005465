package com.gtech.gvcore.common.request.issuer;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "UpdateIssuerStatusRequest")
public class UpdateIssuerStatusRequest {

    /**
     * issuer code
     */
    @ApiModelProperty( value="Issuer code.", example="1122334455",required=true)
    @NotEmpty(message = "issuerCode can not be empty")
    @Length(max = 100)
    private String issuerCode;

    /**
     * status,0:disable,1:enable
     */
    @ApiModelProperty( value="Status.", example="0")
    private Integer status;

    /**
     * update user
     */
    @ApiModelProperty( value="Create user.", example="user1")
    @Length(max = 100)
    private String updateUser;




}
