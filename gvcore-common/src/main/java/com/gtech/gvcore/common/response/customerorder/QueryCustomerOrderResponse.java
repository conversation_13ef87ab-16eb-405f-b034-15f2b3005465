package com.gtech.gvcore.common.response.customerorder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/19 19:32
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "QueryCustomerOrderResponse")
public class QueryCustomerOrderResponse implements Serializable {

    private static final long serialVersionUID = 662006513980047263L;
    @ApiModelProperty(value = "Customer order code")
    private String customerOrderCode;
    @ApiModelProperty(value = "Customer code")
    private String customerCode;
    @ApiModelProperty(value = "Company name")
    private String companyName;
    @ApiModelProperty(value = "voucher num")
    private Integer voucherNum;
    @ApiModelProperty(value = "Denomination")
    private String denomination;
	@ApiModelProperty(value = "Voucher amount")
    private BigDecimal voucherAmount;
    @ApiModelProperty(value = "Customer name")
    private String customerName;
    @ApiModelProperty(value = "Contact first name")
    private String contactFirstName;
    @ApiModelProperty(value = "Contact last name")
    private String contactLastName;
    @ApiModelProperty(value = "Contact phone")
    private String contactPhone;
    @ApiModelProperty(value = "Contact email")
    private String contactEmail;
    @ApiModelProperty(value = "status")
    private String status;
    @ApiModelProperty(value = "Create by")
    private String createUser;
    @ApiModelProperty(value = "Create on")
    private Date createTime;

    @ApiModelProperty(value = "Update on")
    private Date updateTime;
    @ApiModelProperty(value = "Can I cancel it?")
	private Boolean cancelAble;
    @ApiModelProperty(value = "Can I release it?")
    private Boolean releaseAble;
    @ApiModelProperty(value = "Voucher allocation code")
    private String voucherAllocationCode;
    @ApiModelProperty("Mop code")
    private String mopCode;
    @ApiModelProperty(value = "Voucher batch code")
    private String voucherBatchCode;

	private String purchaseOrderNo;
	private String invoiceNo;
    @ApiModelProperty(value = "deliveryType")
    private String deliveType;
    @ApiModelProperty(value = "deliveryDate")
    private String deliveryDate;
    @ApiModelProperty(value = "orderAddress")
    private String orderAddress;

}
