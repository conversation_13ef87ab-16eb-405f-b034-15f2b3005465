package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @date 2022年3月8日
 */
public enum VoucherAllocationStatusEnum implements IEnum<Integer> {
    PENDING_ALLOCATION(0, "Pending Allocation"),
    PENDING_RECEIPT(1, "Pending Receipt"),
    COMPLETED(2,"Completed"),
    REJECTED(3, "Rejected");

    private final Integer code;

    private final String desc;

    VoucherAllocationStatusEnum(Integer code, String desc) {

        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer code() {
        return this.code;
    }

    @Override
    public String desc() {
        return this.desc;
    }

    @Override
    public boolean equalsCode(Integer code) {
        return this.code.equals(code);
    }

}
