package com.gtech.gvcore.common.request.company;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CreateCompanyRequest")
public class CreateCompanyRequest implements Serializable {


    private static final long serialVersionUID = -574657944652380577L;
    @ApiModelProperty(value = "Company name.", example = "PT AGUNG MANDIRI LESTARI", required = true)
    @NotEmpty(message = "companyName can not be empty")
    @Length(max = 100)
    private String companyName;

    @ApiModelProperty(value = "Sbu.", example = "Digimap")
    @Length(max = 100)
    private String sbu;

    //登录用户所属issuer
    /*@ApiModelProperty(value = "Issuer code.", example = "112233")
    @Length(max = 100)
    private String issuerCode;
*/
    @ApiModelProperty( value="Create user.", example="user1",required = true)
    @NotEmpty(message = "createUser can not be empty")
    @Length(max = 100)
    private String createUser;




}
