package com.gtech.gvcore.common.response.customerpaymentmethod;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CustomerPaymentMethodResponse")
public class CustomerPaymentMethodResponse {

    @ApiModelProperty(value = "Customer payment method code", example = "112233")
    private String customerPaymentMethodCode;

    @ApiModelProperty(value = "Customer code", example = "112233")
    private String customerCode;

    @ApiModelProperty(value = "Mop group", example = "1")
    private String mopGroup;

    @ApiModelProperty(value = "Status.", example = "1")
    private Integer status;

    @ApiModelProperty(value = "Create user.", example = "user1")
    private String createUser;

    @ApiModelProperty( value="Update user.", example="user1")
    private String updateUser;

    @ApiModelProperty(value = "Create time.", example = "2022-02-17 16:27")
    private Date createTime;

    @ApiModelProperty(value = "Update time.", example = "2022-02-17 16:27")
    private Date updateTime;


}
