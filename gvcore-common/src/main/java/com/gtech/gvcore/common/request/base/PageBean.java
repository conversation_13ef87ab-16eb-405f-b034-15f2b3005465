package com.gtech.gvcore.common.request.base;

import javax.validation.constraints.Max;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分页bean
 */
@Data
public class PageBean {
    /**
     * 当前页
     */
	@ApiModelProperty(value = "page num")
    private Integer pageNum = 1;

    /**
     * 每页显示的总条数
     */
	@ApiModelProperty(value = "page size")
    @Max(value = 1000)
    private Integer pageSize = 10;
}
