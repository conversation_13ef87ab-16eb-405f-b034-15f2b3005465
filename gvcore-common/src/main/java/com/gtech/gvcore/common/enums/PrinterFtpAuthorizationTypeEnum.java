package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR> Gao.Yuhua
 * @date : 2022-02-22 09:23
 **/
public enum PrinterFtpAuthorizationTypeEnum {


    /**
     * PASSWORD，KEY_FILE
     */
    AUTHORIZATION_TYPE_PASSWORD("PASSWORD", "密码认证")
    , AUTHORIZATION_TYPE_KEY_FILE("KEY_FILE", "密钥认证");

    private final String code;

    private final String desc;

    PrinterFtpAuthorizationTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
