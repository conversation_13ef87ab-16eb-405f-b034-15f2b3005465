package com.gtech.gvcore.common.externalmapping.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Data
public class CancelVoucherCreationAndIssuanceRequest {


    @ApiModelProperty(value = "Transaction ID. ", example = "00001", required = true, notes = "An integer value. This value has to be incremented with every transaction within the batch. For e.g, after the first time init is done, when doing a balance enquiry for the first time, the value for this can be 1, for the second transaction, this should be 2 and so on. Basically, a unique incrementing sequence within the current batch")
    private Integer transactionId;

    @ApiModelProperty(value = "Card Number. ")
    private String voucherNumber;

    @ApiModelProperty(value = "Original Amount. ")
    private Integer originalAmount;

    @ApiModelProperty(value = "Original Approval Code. ")
    private String originalApprovalCode;

    @ApiModelProperty(value = "Original Transaction Id. ")
    private Integer originalTransactionId;

    @ApiModelProperty(value = "Original Batch Number. ")
    private Long originalBatchNumber;

    @ApiModelProperty(value = "Date At Client. ")
    private Date clientTime;

    private String voucherPIN;

    private String originalInvoiceNumber;

    private String notes;

    private String retryKey;

    public Map<String,Object> toMap(){
        Map<String,Object> map = new HashMap<>();
        map.put("transactionId",transactionId);
        map.put("cardNumber", voucherNumber);
        map.put("originalAmount",originalAmount);
        map.put("originalApprovalCode",originalApprovalCode);
        map.put("originalTransactionId",originalTransactionId);
        map.put("originalBatchNumber",originalBatchNumber);
        map.put("dateAtClient", clientTime);
//        map.put("terminalId", tId);
        return map;


    }




}
