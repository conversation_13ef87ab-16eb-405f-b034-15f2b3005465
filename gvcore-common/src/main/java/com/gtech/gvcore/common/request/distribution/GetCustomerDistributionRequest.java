package com.gtech.gvcore.common.request.distribution;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName GetCustomerDistributionRequest
 * @Description 获得客户分发详情key
 * <AUTHOR>
 * @Date 2022/9/5 21:07
 * @Version V1.0
 **/
@Setter
@Getter
@Accessors(chain = true)
@ApiModel(description = "GetCustomerDistributionRequest")
public class GetCustomerDistributionRequest {

    @ApiModelProperty(value = "distribution code")
    private String distributionCode;

}
