package com.gtech.gvcore.common.response.dashboard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/9/7 15:16
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "VcrInStockResponse")
public class VcrInStockResponse {

    @ApiModelProperty(value = "data")
    private Map<String, String> data;


}
