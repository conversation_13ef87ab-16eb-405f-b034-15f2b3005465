package com.gtech.gvcore.common.response.dashboard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2022/8/31 16:42
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "TopTenResponse")
public class TopTenResponse {

    @ApiModelProperty(value = "Name")
    private String name;

    @ApiModelProperty(value = "Amount")
    private BigDecimal amount;



}
