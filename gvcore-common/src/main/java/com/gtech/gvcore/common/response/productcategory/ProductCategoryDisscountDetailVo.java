package com.gtech.gvcore.common.response.productcategory;

import java.math.BigDecimal;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年2月24日
 */
@Data
public class ProductCategoryDisscountDetailVo {

    private Long id;

    private String productCategoryDisscountDetailsCode;

    private String productCategoryDisscountCode;

    private BigDecimal fromPurchaseValue;

    private BigDecimal uptoPurchaseValue;

    private String discountType;

    private BigDecimal discount;

    private BigDecimal maximumDiscountValue;

    private Integer status;

}
