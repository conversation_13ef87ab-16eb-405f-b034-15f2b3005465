package com.gtech.gvcore.common.request.articlemop;

import com.gtech.gvcore.common.request.base.PageBean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022年2月17日
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QueryArticleMopsByPageRequest extends PageBean {

    @ApiModelProperty(value = "articleCodeName", required = false)
    private String articleCodeName;

}
