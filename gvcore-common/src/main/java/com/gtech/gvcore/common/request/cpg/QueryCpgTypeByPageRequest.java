package com.gtech.gvcore.common.request.cpg;

import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022年2月17日
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class QueryCpgTypeByPageRequest extends PageBean {

    @ApiModelProperty(value = "cpgTypeName")
    private String cpgTypeName;

    /**
     * status,0:disable,1:enable
     */
    @ApiModelProperty(value = "status",example ="0:disable;1:enable" )
    private Integer status;
}
