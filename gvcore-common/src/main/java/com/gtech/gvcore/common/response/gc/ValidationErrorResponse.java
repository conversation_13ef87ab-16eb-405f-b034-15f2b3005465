package com.gtech.gvcore.common.response.gc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 参数校验错误响应类
 * 专门用于处理请求头缺失、参数验证失败等场景
 * 只包含基础的错误码和错误信息，不包含业务数据
 */
@Data
@NoArgsConstructor
@ApiModel(value = "ValidationErrorResponse", description = "Parameter validation error response")
public class ValidationErrorResponse extends BaseApiResponse {

    /**
     * 构造函数 - 用于创建错误响应
     * @param responseCode 错误码
     * @param responseMessage 错误信息
     */
    public ValidationErrorResponse(Integer responseCode, String responseMessage) {
        super(responseCode, responseMessage);
    }
}