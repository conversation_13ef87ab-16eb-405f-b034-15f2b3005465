package com.gtech.gvcore.common.response.voucher;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CheckDynamicBarCodeResponse {

    /**
     * After validation is completed, the Redeem needs to store the barCode in the database
     *
     */



    //true-success  false-failure
    private Boolean isSuccess;


}
