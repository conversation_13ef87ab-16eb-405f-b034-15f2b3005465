package com.gtech.gvcore.common.request.transaction;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class DiscountInfo {

	@ApiModelProperty(value = "Discount Percentage. ", example = "1", notes = "Discount in percentage")
	private Integer discountPercentage;
	//ETP 金额类型改为String
	@ApiModelProperty(value = "Discount Amount. ", example = "1", notes = "Discount in Amount")
	private String discountAmount;
}
