package com.gtech.gvcore.common.enums;

public enum MessageEnventEnum {

	// Generate Physical Vouchers
	SEND_PHYSICAL_VOUCHER("GV0101", "Map Mercury physical voucher printing request"),
	SEND_PHYSICAL_PASSWORD("GV0102", "Map Mercury physical voucher file password"),
	//Voucher Request&Allocate 
	CREATE_VOUCHER_REQUEST("GV0201", "Map Mercury Voucher Request Pending Approval"),
	REJECT_VOUCHER_REQUEST("GV0202", "Voucher request {approved | rejected}"),
    APPROVE_VOUCHER_REQUEST("GV0203","Map Mercury Voucher Request Pending Allocation"),
    ALLOCATE_VOUCHER_REQUEST("GV0204","Map Mercury Voucher Request Pending Receipt"),
    RECEIVE_VOUCHER_REQUEST("GV0205", "Map Mercury Voucher Request Completed"),
    // Voucher Return&Transfer
    CREATE_VOUCHER_RETURN_TRANSFER("GV0301","Map Mercury Voucher {Return/Transfer} Request Pending Approval"),
	APPROVE_VOUCHER_RETURN_TRANSFER("GV0302", "Map Mercury Voucher Request Pending Transfer}"),
	REJECT_VOUCHER_RETURN_TRANSFER("GV0303", "Return/transfer rejected or return approved"),
    RETURN_TRANSFER_VOUCHER_REQUEST("GV0304","Map Mercury Voucher Request Pending Pending Receipt"),
    RECEIVE_VOUCHER_RETURN_TRANSFER("GV0305", "Map Mercury Voucher {Return/Transfer} Request Completed"),
    // Sales & Issuance
	CUSTOMER_VERIFICATION_CODE("GV0401", "Map Mercury verification code"),
	CUSTOMER_ORDER_CREATE("GV0402", "Map Mercury Customer Voucher"),
	CUSTOMER_ORDER_CREATE_BY_OP("GV0403", "New Purchase Request on MAP Gift Voucher Mercury Dashboard"),
    SUBMIT_CUSTOMER_ORDER("GV0404","Map Mercury Customer Voucher Order Pending  Appoval"),
    APPROVE_CUSTOMER_ORDER("GV0405","Map Mercury Customer Voucher Order Pending  Issuance"),
    REJECT_CUSTOMER_ORDER("GV0406", "Map Mercury Customer Voucher Order Approval Reject"),
    ISSUANCE_CUSTOMER_ORDER("GV0407", "Map Mercury Customer Voucher Order Pending  Release"),
    RELEASE_CUSTOMER_ORDER("GV0408", "Map Mercury Customer Voucher Order Pengding Receive"),
    RECEIVE_CUSTOMER_ORDER("GV0409", "Map Mercury Customer Voucher Order Pengding Deliver"),
    CUSTOMER_ORDER_DELIVERY_REMINDER("GV0416", "MAP Gift Voucher Purchase - Delivery Reminder"),
    DELIVER_CUSTOMER_ORDER("GV0410", "Map Mercury Customer Voucher Order Completed"),
    SEND_E_VOUCHER_FILE_PWD("GV0411", "Map Mercury e-voucher file password"),
    CUSTOMER_SEND_QUOTATION("GV0412", "Map Mercury Customer Voucher Order’s Quotation"),
    CUSTOMER_SEND_INVOICE("GV0413", "Map Mercury Customer Voucher Order’s Invoice"),
    CUSTOMER_ORDER_RELEASE_GO_ON("GV0414","Map Mercury Customer Voucher Order Pending  Release"),
	EGV_CUSTOMER_ORDER_COMPLETED("GV0415", "Map Mercury Customer Voucher Order Completed"),
    // Issue Handling
	SUBMIT_ISSUE_HANDLING("GV0501", "{issue handling operation name} MAP Gift Voucher Request - Approval Needed"),
	APPROVE_ISSUE_HANDLING("GV0502", "The {issue handling operation name} MAP Gift Voucher Request has been {Approved | Rejected}"),
    REJECT_ISSUE_HANDLING("GV0503", "Map Mercury Issue Handling - {Cancel sales} Request Approval Reject"),
    EXECUTE_ISSUE_HANDLING("GV0504", "Map Mercury Issue Handling - {Cancel sales} Request Completed"),
    SEND_REISSUE_E_VOUCHER("GV0505", "Map Mercury Issue Handling - Reissue Request Completed"),
	SEND_REISSUE_E_PWD("GV0506", "Decryption password of your replacement MAP e-Gift voucher(s)"),
    // Report
    SCHEDULER_REPORT("GV0601", "Map Mercury {Bulk Order Report} query completed"),
    SUBMIT_QUERY_REPORT("GV0602", "Map Mercury {Bulk Order Report} querying"),

    // Create b customer
    CREATE_B_CUSTOMER("GV0701", "Your account of Mercury Distribution system"),

    DISTRIBUTION_EMAIL("GV0801", "distribution email"),
    REGENERATE_ACTIVATION_CODE("GV0802", "RegenerateActivationCode"),
    REGENERATE_ACTIVATION_CODE_PWD("GV0803", "RegenerateActivationCode pwd"),

    ;

    private final String code;

    private final String title;

    MessageEnventEnum(String code, String title) {
        this.code = code;
        this.title = title;
    }

    public String getCode() {
        return code;
    }

    public String getTitle() {
        return title;
    }

}
