package com.gtech.gvcore.common.request.outlet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "UpdateOutletStatusRequest")
public class UpdateOutletStatusRequest {

    @ApiModelProperty(value = "Outlet code.", example = "1231245125",required = true)
    @NotEmpty(message = "outletCode can not be empty")
    @Length(max = 100)
    private String outletCode;

    @ApiModelProperty(value = "Status.", example = "0")
    private Integer status;

    @ApiModelProperty(value = "Update user.", example = "user1")
    @Length(max = 100)
    private String updateUser;



}
