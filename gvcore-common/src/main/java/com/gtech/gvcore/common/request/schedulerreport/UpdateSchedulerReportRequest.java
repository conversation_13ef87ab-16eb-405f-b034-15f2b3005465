package com.gtech.gvcore.common.request.schedulerreport;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/2 13:38
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "UpdateSchedulerReportRequest", description = "Update scheduler report request")
public class UpdateSchedulerReportRequest implements Serializable {

    private static final long serialVersionUID = -369313643973032726L;
    @ApiModelProperty(value = "Scheduler report code", required = true)
    @NotBlank(message = "Scheduler report code can not be empty")
    private String schedulerReportCode;

    @ApiModelProperty(value = "Scheduler name", required = true)
    @NotBlank(message = "Scheduler name can not be empty")
    private String schedulerName;

    @ApiModelProperty(value = "Execution time", required = true)
    @NotNull(message = "Execution time can not be empty")
    private Date executionTime;

    @ApiModelProperty(value = "Execution frequency,optional value is once/daily/weekly/monthly/or/yearly", required = true, example = "daily")
    @NotBlank(message = "Frequency can not be empty")
    private String frequency;

    @ApiModelProperty(value = "every", required = true)
    @NotNull(message = "every can not be empty")
    private Integer every;

    @ApiModelProperty(value = "cron expression，The schedulerCron of frequency is not once")
    private String schedulerCron;

    @ApiModelProperty(value = "Repeat End,The value can be never, after, or on date. The default value is never")
    private String repeatEnd;

    @ApiModelProperty(value = "repeat end after")
    private Integer repeatEndAfter;

    @ApiModelProperty(value = "repeat end On date")
    private Date repeatEndTime;

    @ApiModelProperty(value = "Report name", required = true)
    @NotBlank(message = "Report name can not be empty")
    private String reportName;

    @ApiModelProperty(value = "Report type", required = true)
    @NotNull(message = "Report type can not be empty")
    private Integer reportType;

    @ApiModelProperty(value = "Issuer code", required = true)
    @NotBlank(message = "Issuer code can not be empty")
    private String issuerCode;

    @ApiModelProperty(value = "Merchant code")
    private String merchantCode;

    @ApiModelProperty(value = "Merchant outlet code")
    private String merchantOutletCode;

    @ApiModelProperty(value = "Transaction type")
    private String transactionType;

    @ApiModelProperty(value = "Data range", required = true, example = "yesterday(1), last 7 days(7), last 30 days(30)")
    @NotNull(message = "Data range")
    private Integer dataRange;

    @ApiModelProperty(value = "Transaction Status")
    private String transactionStatus;

    @ApiModelProperty(value = "Voucher status")
    private Integer voucherStatus;

    @ApiModelProperty(value = "Voucher program group code")
    private String vpgCode;

    @ApiModelProperty(value = "Email(s),you can enter multiple use ','", example = "<EMAIL>,<EMAIL>")
    private String emails;

    @ApiModelProperty(value = "Email subject")
    private String emailSubject;

    @ApiModelProperty(value = "FTP address")
    private String ftpAddress;

    @ApiModelProperty(value = "Login method")
    private String loginMethod;

    @ApiModelProperty(value = "Encryption key")
    private String encryptionKey;

    @ApiModelProperty(value = "Update user", required = true)
    @NotBlank(message = "Update user can not be empty")
    private String updateUser;
}
