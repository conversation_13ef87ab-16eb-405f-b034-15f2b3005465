package com.gtech.gvcore.common.request.issuehandling;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

@Data
public class ProofFile {

    @ApiModelProperty(value = "proofType", required = true)
    @NotEmpty(message = "proofType can not be empty")
    @Length(max = 200)
    private String proofType;
	
	@ApiModelProperty(value = "proofFileName", required = true)
    @NotEmpty(message = "proofFileName can not be empty")
    @Length(max = 200)
	private String proofFileName;

	@ApiModelProperty(value = "proofFileUrl", required = true)
    @NotEmpty(message = "proofFileUrl can not be empty")
    @Length(max = 500)
    private String proofFileUrl;

}
