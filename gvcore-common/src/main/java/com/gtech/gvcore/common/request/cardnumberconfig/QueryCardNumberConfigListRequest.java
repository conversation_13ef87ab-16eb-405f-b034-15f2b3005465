package com.gtech.gvcore.common.request.cardnumberconfig;

import com.gtech.commons.page.PageData;
import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class QueryCardNumberConfigListRequest extends PageBean {

    @ApiModelProperty(value = "type")
    private String type;

    @ApiModelProperty(value = "status")
    private String status;

    @ApiModelProperty(value = "description")
    private String description;


}
