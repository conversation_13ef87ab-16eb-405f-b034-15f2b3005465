package com.gtech.gvcore.common.request.outlet;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@Builder
public class ReplaceOutletCodeRequest implements Serializable {
    private static final long serialVersionUID = -752809134443293094L;

    @ApiModelProperty(value = "oldOutletCode", required = true)
    @NotBlank(message = "oldOutletCode can not be empty")
    private String oldOutletCode;

    @ApiModelProperty(value = "newOutletCode", required = true)
    @NotBlank(message = "newOutletCode can not be empty")
    private String newOutletCode;


}
