package com.gtech.gvcore.common.request.outlet;

import java.util.List;

import javax.validation.constraints.NotEmpty;

import com.gtech.gvcore.common.request.base.PageBean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryOutletByMerchantCodesRequest")
public class QueryOutletByMerchantCodesRequest extends PageBean {


    @ApiModelProperty( value = "Merchant Codes.", example = "12341254",required = true)
    @NotEmpty(message = "merchantCodes can not be empty")
    private List<String> merchantCodes;







}
