package com.gtech.gvcore.common.request.company;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/2/24 16:56
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "UpdateCompanyStatusRequest")
public class UpdateCompanyStatusRequest implements Serializable {

    private static final long serialVersionUID = -8349200214289593696L;
    @ApiModelProperty( value = "Company code.", example = "1345566",required = true)
    @NotEmpty(message = "companyCode can not be empty")
    @Length(max = 100)
    private String companyCode;

    @ApiModelProperty( value="Status.", example="0")
    private Integer status;

    @ApiModelProperty( value="Update user.", example="user1")
    @Length(max = 50)
    private String updateUser;


}
