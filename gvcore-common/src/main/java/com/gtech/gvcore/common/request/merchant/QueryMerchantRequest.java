package com.gtech.gvcore.common.request.merchant;

import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryMerchantRequest")
public class QueryMerchantRequest extends PageBean {

    @ApiModelProperty( value = "Merchant code.", example = "1345566")
    @Length(max = 100)
    private String merchantCode;
    
    @ApiModelProperty( value = "Merchant code list.")
    private List<String> merchantCodeList;

    @ApiModelProperty( value = "Merchant name.", example = "MAP")
    @Length(max = 100)
    private String merchantName;

    @ApiModelProperty( value = "Company code.", example = "11223344")
    @Length(max = 100)
    private String companyCode;

    @ApiModelProperty( value = "Issuer code.", example = "11223344")
    @Length(max = 100)
    private String issuerCode;

    @ApiModelProperty( value = "Status.", example = "1")
    @Length(max = 10)
    private String status;



}
