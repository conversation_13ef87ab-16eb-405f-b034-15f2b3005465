package com.gtech.gvcore.common.request.gcapi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Query Card Statement Request
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryCardStatementRequest", description = "Query Card Statement Request")
public class QueryCardStatementRequest {

    @ApiModelProperty(value = "Gift Card Number", notes = "16 digit gift card number", required = true, example = "1234567890123456", position = 1)
    private String giftCardNumber;
    

}