package com.gtech.gvcore.common.response.productcategory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询商品分类响应
 */
@Data
@ApiModel(value = "QueryProductCategoryResponse", description = "查询商品分类响应")
public class QueryProductCategoryResponse {
    /**
     * 分类编码
     */
    @ApiModelProperty(value = "分类编码")
    private String categoryCode;

    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    /**
     * 父分类编码
     */
    @ApiModelProperty(value = "父分类编码")
    private String parentCategoryCode;

    /**
     * 父分类名称
     */
    @ApiModelProperty(value = "父分类名称")
    private String parentCategoryName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private String createTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private String updateTime;
} 