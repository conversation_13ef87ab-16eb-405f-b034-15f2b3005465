package com.gtech.gvcore.common.response.cpg;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022年2月22日
 */
@Data
public class QueryCpgsByPageResponse {

    private Long id;

    private String cpgCode;

    private String cpgName;

    private String issuerCode;

    private String cpgTypeCode;

    private String cpgTypeName;
    
    private String prefix;

    private Integer gracePeriods;

    private Integer effectiveYears;

    private Integer effectiveMonth;

    private Integer effectiveDay;

    private Integer effectiveHour;

    private String currencyCode;

    private BigDecimal denomination;

    private String articleMopCode;
    
    private String articleCode;
    
    private String mopCode;

    private Integer bookletVoucherNum;

    private Integer status;

    private String createUser;

    private Date createTime;

    private String updateUser;

    private Date updateTime;

    private String disableGeneration;

}
