package com.gtech.gvcore.common.request.allocation;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年3月21日
 */
@Data
public class CustomerOrderAllocateRequest {

    @ApiModelProperty(value = "voucherAllocationCode", required = true)
    @NotEmpty(message = "voucherAllocationCode can not be empty")
    private String voucherAllocationCode;

    @ApiModelProperty(value = "orderDetailList", required = true)
    @NotEmpty(message = "orderDetailList can not be empty")
    @Valid
    private List<CustomerOrderDetail> orderDetailList;

    @ApiModelProperty(value = "updateUser", required = true)
    @NotEmpty(message = "updateUser can not be empty")
    @Length(max = 50)
    private String updateUser;

}
