package com.gtech.gvcore.common.request.gcapi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Buy Gift Card Request
 * 购买礼品卡请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "ByGiftCardRequest", description = "Buy Gift Card Request")
public class ByGiftCardRequest {


    @ApiModelProperty(value = "GCPG", notes = "GCPG Name (Configurable Product Group for gift cards)", required = true, example = "STANDARD_GIFT_CARD", position = 2)
    private String gcpg;

    @ApiModelProperty(value = "Quantity", notes = "Number of gift cards to purchase", required = true, example = "1", position = 3)
    private Integer quantity;
    
    @ApiModelProperty(value = "Denomination", notes = "Card value/denomination amount", required = true, example = "100.00", position = 4)
    private BigDecimal denomination;
    
    @ApiModelProperty(value = "Currency", notes = "Currency code", required = true, example = "IDR", position = 5)
    private String currency;
    
    @ApiModelProperty(value = "Invoice Number", notes = "Invoice number from e-commerce system", required = true, example = "INV-12345", position = 6)
    private String invoiceNumber;
    
    @ApiModelProperty(value = "Invoice Date", notes = "Invoice date", example = "2023-01-01T12:00:00", position = 7)
    private Date invoiceDate;
    
    @ApiModelProperty(value = "Customer Information", notes = "Buyer information", required = true, position = 8)
    private CustomerInfo customer;
    
    @ApiModelProperty(value = "Notes", notes = "Reference text", position = 10)
    private String notes;
    
    @ApiModelProperty(value = "Source", notes = "Reference for outletname/POSname/brandname/partnername", example = "MAPCLUB.COM", position = 11)
    private String source;
    

    

}