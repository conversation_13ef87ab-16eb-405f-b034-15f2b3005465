package com.gtech.gvcore.common.externalmapping.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.gtech.gvcore.common.response.transaction.CardResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class VoucherResponse {

    @ApiModelProperty(value = "Card Number.", required = true, notes = "End card number in the lineitem")
    private String voucherNumber;
//    @JsonProperty("cardPin")
    @ApiModelProperty(value = "Card Pin.", notes = "Card Pin returned in case of EGV line item")
    private String voucherPIN;
    @ApiModelProperty(value = "Activation Code.", notes = "Depends on program setting")
    private String activationCode;
    @ApiModelProperty(value = "Card Creation Type.", notes = "Virtual or Physical")
    private String voucherCreationType;
    @ApiModelProperty(value = "Activation URL.", notes = "Depends on program setting")
    private String activationURL;
    @ApiModelProperty(value = "Card Balance.", notes = "")
    private BigDecimal voucherBalance;
    @ApiModelProperty(value = "Card Expiry.", notes = "card expiry")
    private String voucherExpiryDate;
    @ApiModelProperty(value = "Transaction Amount.", notes = "transacted amount @ card level")
    private BigDecimal transactionAmount;
    @ApiModelProperty(value = "Card Status.", notes = "Please refer lookup in intro section")
    private String voucherStatus;
    @ApiModelProperty(value = "Approval Code.", notes = "only for activate and redeem")
    private String approvalCode;
    @ApiModelProperty(value = "Card Program Group Name.", notes = "Card program group")
    private String vpgName;
    @ApiModelProperty(value = "Sequence No.", notes = "Sequence number associated to the card")
    private Long sequenceNo;
    @ApiModelProperty(value = "Product Code.", notes = "Product code")
    private String articleCode;
    @ApiModelProperty(value = "Design Code.", notes = "Design code")
    private String mopCode;
    @ApiModelProperty(value = "Track Data.", notes = "trackData")
    private String trackData;
    @ApiModelProperty(value = "barcode.", notes = "barcode")
    private String barcode;
    @ApiModelProperty(value = "Response Code.", required = true, notes = "A 5 digit integer response code. A zero value indicates success and a non-zero value indicates failure.")
    private Integer responseCode;
    @ApiModelProperty(value = "Response Message.", notes = "Contains the response message or error message in case the transacƟ on has failed (response code is non-zero).")
    private String responseMessage;


    public VoucherResponse setVoucherResponse(CardResponse cardResponse){
        if(cardResponse == null){
            return null;
        }
        this.setVoucherNumber(cardResponse.getCardNumber());
        this.setVoucherPIN(cardResponse.getCardPin());
        this.setActivationCode(cardResponse.getActivationCode());
        this.setVoucherCreationType(cardResponse.getCardCreationType());
        this.setActivationURL(cardResponse.getActivationURL());
        this.setVoucherBalance(cardResponse.getCardBalance());
        this.setVoucherExpiryDate(cardResponse.getCardExpiry());
        this.setTransactionAmount(cardResponse.getTransactionAmount());
        this.setVoucherStatus(cardResponse.getCardStatus());
        this.setApprovalCode(cardResponse.getApprovalCode());
        this.setVpgName(cardResponse.getCardProgramGroupName());
        this.setSequenceNo(cardResponse.getSequenceNo());
        this.setArticleCode(cardResponse.getProductCode());
        this.setMopCode(cardResponse.getDesignCode());
        this.setTrackData(cardResponse.getTrackData());
        this.setBarcode(cardResponse.getBarcode());
        this.setResponseCode(cardResponse.getResponseCode());
        this.setResponseMessage(cardResponse.getResponseMessage());
        return this;
    }


}
