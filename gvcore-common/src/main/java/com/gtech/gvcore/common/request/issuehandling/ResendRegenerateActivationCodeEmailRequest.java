package com.gtech.gvcore.common.request.issuehandling;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ResendRegenerateActivationCodeEmailRequest{
    @ApiModelProperty(value = "issueHandlingCode", required = true)
    String issueHandlingCode;
    @ApiModelProperty(value = "email", required = true)
    String email;
    @ApiModelProperty(value = "voucherCode", required = true)
    String voucherCode;
    @ApiModelProperty(value = "updateUser", required = true)
    String updateUser;
}
