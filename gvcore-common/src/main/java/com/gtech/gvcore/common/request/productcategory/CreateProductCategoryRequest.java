package com.gtech.gvcore.common.request.productcategory;

import javax.validation.constraints.NotEmpty;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年2月23日
 */
@Data
public class CreateProductCategoryRequest {

    @ApiModelProperty(value = "categoryName", required = true)
    @NotEmpty(message = "categoryName can not be empty")
    @Length(max = 200)
    private String categoryName;

    @ApiModelProperty(value = "issuerCode", required = true)
    @NotEmpty(message = "issuerCode can not be empty")
    @Length(max = 50)
    private String issuerCode;

    @ApiModelProperty(value = "remarks", required = false)
    @Length(max = 500)
    private String remarks;

    @ApiModelProperty(value = "createUser", required = true)
    @NotEmpty(message = "createUser can not be empty")
    @Length(max = 50)
    private String createUser;

}
