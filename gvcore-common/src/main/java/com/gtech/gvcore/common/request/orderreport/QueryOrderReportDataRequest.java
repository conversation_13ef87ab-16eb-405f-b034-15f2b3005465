package com.gtech.gvcore.common.request.orderreport;

import com.gtech.commons.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * @ClassName QueryOrderReportDataRequest
 * @Description 查询报表结果入参
 * <AUTHOR>
 * @Date 2022/8/23 14:54
 * @Version V1.0
 **/
@Setter
@Getter
@ToString
@Accessors(chain = true)
@ApiModel(description = "QueryOrderReportDataRequest")
public class QueryOrderReportDataRequest extends PageParam {

    @NotBlank(message = "orderReportCode is required")
    @ApiModelProperty(value = "Order report code", required = true)
    private String orderReportCode;

    @ApiModelProperty(value = "reportType")
    private Integer reportType;

}
