package com.gtech.gvcore.common.response.dashboard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2022/8/31 18:23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "TotalSalesResponse")
public class TotalSalesResponse {


    @ApiModelProperty(value = "Amount")
    private BigDecimal amount;


    @ApiModelProperty(value = "Type",example = "VCE")
    private String type;


}
