package com.gtech.gvcore.common.request.customerorder;

import com.gtech.basic.idm.common.exceptions.ErrorCodes;
import com.gtech.commons.utils.CheckUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/24 17:38
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "Non-system create customer order request")
@Builder
public class NonSystemCreateCustomerOrderRequest implements Serializable {
    private static final long serialVersionUID = 8999949174867166223L;


    public static final String CORPORATE = "Corporate";
    public static final String INDIVIDUAL = "Individual";


    @ApiModelProperty(value = "Voucher type code", required = true, example = "VCE")
    @NotBlank(message = "mopCode cannot be empty")
    private String mopCode;
    @ApiModelProperty(value = "Issuer code", required = true)
    @NotBlank(message = "issuerCode must required")
    private String issuerCode;
    @ApiModelProperty(value = "Total voucher num", example = "4000")
    private Integer voucherNum;
    @ApiModelProperty(value = "Total voucher amount", required = true, example = "120000000")
    @NotNull(message = "Voucher amount cannot be empty")
    private BigDecimal voucherAmount;
    @ApiModelProperty(value = "Customer code", example = "")
    private String customerCode;
    @ApiModelProperty(value = "Customer name", required = true)
    @NotBlank(message = "Customer name cannot be empty")
    private String customerName;
    @ApiModelProperty(value = "Customer type", required = true)
    @NotBlank(message = "Customer type cannot be empty")
    private String customerType;
    @ApiModelProperty(value = "Company name")
    private String companyName;
    @ApiModelProperty(value = "Contact first name")
    private String contactFirstName;
    @ApiModelProperty(value = "Contact last name")
    private String contactLastName;
    @ApiModelProperty(value = "Contact Phone", required = true)
    @NotBlank(message = "Contact phone cannot be empty")
    private String contactPhone;
    @ApiModelProperty(value = "Contact email", required = true)
    @NotBlank(message = "Contact email cannot empty")
    private String contactEmail;
    @NotBlank(message = "Shipping address cannot be empty")
    @ApiModelProperty(value = "Shipping address", required = true)
    private String shippingAddress;
    @ApiModelProperty(value = "notes")
    private String customerRemarks;
    @NotNull(message = "createCustomerOrderDetails cannot empty")
    @Size(min = 1, message = "At least one piece of data")
    private List<CreateCustomerOrderDetailsRequest> createCustomerOrderDetailsRequests;


    public void validation(){

        if (customerType.equals(CORPORATE)){

            CheckUtils.isNotBlank(this.contactFirstName, ErrorCodes.PARAM_EMPTY, "contactFirstName");
            CheckUtils.isNotBlank(this.contactLastName, ErrorCodes.PARAM_EMPTY, "contactLastName");

        }



    }




}
