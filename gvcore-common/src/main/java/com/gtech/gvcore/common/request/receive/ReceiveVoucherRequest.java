package com.gtech.gvcore.common.request.receive;

import java.util.List;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReceiveVoucherRequest {

    /**
     * voucher receive code
     */
	@ApiModelProperty(value = "Voucher receive code")
	@NotEmpty(message = "Voucher receive code can't be empty")
    private String voucherReceiveCode;


	/**
	 * create user
	 */
	@ApiModelProperty(value = "Create user")
	@NotEmpty(message = "Create user can't be empty")
	private String createUser;

	@NotNull(message = "receiveRecordList can't be null")
	List<VoucherReceiveRecordRequest> receiveRecordList;
}