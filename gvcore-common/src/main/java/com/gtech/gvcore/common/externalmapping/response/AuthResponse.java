package com.gtech.gvcore.common.externalmapping.response;

import com.gtech.gvcore.common.response.authorize.AuthorizeResponse;
import com.gtech.gvcore.common.response.authorize.LocaleInfo;
import com.gtech.gvcore.common.response.authorize.MerchantOutletInfo;
import com.gtech.gvcore.common.response.authorize.ReceiptInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AuthResponse {


    @ApiModelProperty(value = "Auth Token.", example = "XXXXXXXXX", required = true, notes = "JWT token (encoded Base64 format)")
    private String token;
    @ApiModelProperty(value = "Auth Token.", required = true, notes = "Please see below under MerchantOutletInfo")
    private MerchantOutletInfo merchantStore;
    @ApiModelProperty(value = "Locale Info.", required = true, notes = "Please see below under LocaleInfo")
    private LocaleInfo locale;
    @ApiModelProperty(value = "Receipt Info.", required = true, notes = "Configured receipt Info if any. Please see below for details under ReceiptInfo")
    private ReceiptInfo receipt;
    @ApiModelProperty(value = "Response Code.", required = true, notes = "A 5 digit integer response code. A zero value indicates success and a non-zero value indicates failure.")
    private Integer responseCode;
    @ApiModelProperty(value = "Response Message.", required = true, notes = "Contains the response message or error message in case the transacƟ on has failed (response code is non-zero).")
    private String responseMessage;
    @ApiModelProperty(value = "Batch Id.", required = true, notes = "This needs to be persisted to be passed as a OriginalBatchId for cancellaƟ on")
    private Integer batchId;


    public AuthResponse setAuthResponse(AuthorizeResponse authorizeResponse) {
        if (authorizeResponse == null) {
            return null;
        }
        this.setToken(authorizeResponse.getAuthToken());
        this.setMerchantStore(authorizeResponse.getMerchantOutletInfo());
        this.setLocale(authorizeResponse.getLocaleInfo());
        this.setReceipt(authorizeResponse.getReceiptInfo());
        this.setResponseCode(authorizeResponse.getResponseCode());
        this.setResponseMessage(authorizeResponse.getResponseMessage());
        this.setBatchId(authorizeResponse.getBatchId());
        return this;
    }




}
