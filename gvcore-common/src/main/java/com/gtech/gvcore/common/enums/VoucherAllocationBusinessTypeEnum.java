package com.gtech.gvcore.common.enums;


/**
 * <AUTHOR>
 * @date 2022年3月21日
 */
public enum VoucherAllocationBusinessTypeEnum implements IEnum<String> {
    
    SALES("sales", "sales"),
    RETURN("return", "return"),
    TRANSFER("transfer", "transfer"),
    CUSTOMERORDER("customerorder", "customerOrder")
    ;

    private final String code;

    private final String desc;

    VoucherAllocationBusinessTypeEnum(String code, String desc) {

        this.code = code;
        this.desc = desc;
    }

    @Override
    public String code() {
        return this.code;
    }

    @Override
    public String desc() {
        return this.desc;
    }

    @Override
    public boolean equalsCode(String code) {
        return this.code.equals(code);
    }

}
