package com.gtech.gvcore.common.request.meansofpayment;

import java.util.List;

import javax.validation.constraints.NotEmpty;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年2月21日
 */
@Data
public class CreateMeansOfPaymentRequest {

    @ApiModelProperty(value = "mopName", required = true)
    @NotEmpty(message = "mopName can not be empty")
    @Length(max = 100)
    private String mopName;

    @ApiModelProperty(value = "mopGroup", required = true)
    @NotEmpty(message = "mopGroup can not be empty")
    @Length(max = 100)
    private String mopGroup;

    @ApiModelProperty(value = "remarks", required = false)
    @Length(max = 500)
    private String remarks;

    @ApiModelProperty(value = "createUser", required = true)
    @NotEmpty(message = "createUser can not be empty")
    @Length(max = 50)
    private String createUser;

    @ApiModelProperty(value = "outletCodeList", required = true)
    @NotEmpty(message = "outletCodeList can not be empty")
    private List<String> outletCodeList;

}
