package com.gtech.gvcore.common.response.distribution;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @ClassName CpgInventoryResponse
 * @Description CpgInventoryResponse
 * <AUTHOR>
 * @Date 2022/7/6 10:43
 * @Version V1.0
 **/
@Getter
@Setter
@ApiModel(value = "CpgInventoryResponse")
public class CpgInventoryResponse {

    @ApiModelProperty(value = "CPG编码", example = "CP001")
    private String cpgCode;

    @ApiModelProperty(value = "CPG名称", example = "QA_TEST-VCR-100K")
    private String cpgName;

    @ApiModelProperty(value = "总金额", example = "100")
    private BigDecimal amount;

    @ApiModelProperty(value = "面额", example = "1")
    private BigDecimal denomination;

    @ApiModelProperty(value = "总库存数", example = "100")
    private Integer quantity;

    @ApiModelProperty(value = "可用库存数", example = "80")
    private Integer available;

    @ApiModelProperty(value = "已分发数", example = "20")
    private Integer distributed;

}
