package com.gtech.gvcore.common.externalmapping.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
public class OneTimeBarCodeRequest {

    @ApiModelProperty(value = "Transaction ID. ", example = "00001", required = true, notes = "An integer value. This value has to be incremented with every transaction within the batch.")
    private Integer transactionId;
    @ApiModelProperty(value = "Cards. ", example = "123456", required = true, notes = "Array of Object APICard • Only Card Number in this Object is mandatory. • Card Number: Contains the Virtual CardNumber for which OneTimeBarcode has to be generated. • As of now only one card is supported. If more than one card is passed “Invalid request” error will be thrown.")
    private List<VoucherInfo> vouchers;
    @ApiModelProperty(value = "Merchant Name. ", example = "123456", notes = "Merchant Name Note: As of now this field is just for capture only, and no validation is being performed.")
    @Length(max = 256, message = "Merchant Name maximum length 256")
    private String merchantName;
    @ApiModelProperty(value = "Outlet Name. ", example = "123456", notes = "A unique identifier which represents the name of the outlet. This is unique for the merchant")
    @Length(max = 100, message = "Outlet Name maximum length 100")
    private String storeName;
    @ApiModelProperty(value = "Notes. ", example = "0", notes = "Any Reference text to be captured along with this transaction.")
    @Length(max = 512, message = "Notes maximum length 512")
    private String notes;
    @ApiModelProperty(value = "Date At Client. ", example = "2022-02-02 18:00:00", required = true, notes = "The datetime at the client machine in YYYY-MM-DD HH:MM:SS")
    private String clientTime;


    private String tId;

    public Map<String,Object> toMap(){
        Map<String,Object> map = new HashMap<>();
        map.put("transactionId",transactionId);
        map.put("cards", vouchers.stream().map(VoucherInfo::toMap).collect(Collectors.toList()));
        map.put("merchantName",merchantName);
        map.put("outletName", storeName);
        map.put("notes",notes);
        map.put("dateAtClient", clientTime);
        map.put("terminalId", tId);
        return map;



    }



}
