package com.gtech.gvcore.common.request.pos;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "UpdatePosRequest")
public class UpdatePosRequest {


    @ApiModelProperty(value = "Pos code.", example = "2123123",required = true)
    @NotEmpty(message = "posCode can not be empty")
    @Length(max = 100)
    private String posCode;


    @ApiModelProperty(value = "Issuer code.", example = "2123123")
    private String issuerCode;


    @ApiModelProperty(value = "Pos name.", example = "Pos1")
    private String posName;


    @ApiModelProperty(value = "Machine id", example = "2123123")
    private String machineId;

    @ApiModelProperty(value = "Pos entry mode id.", example = "2123123")
    private String posEntryModeId;


    @ApiModelProperty(value = "Outlet code.", example = "2123123")
    private String outletCode;


    @ApiModelProperty(value = "Outlet code.", example = "[123,123]")
    private List<String> cpgCodes;


    @ApiModelProperty(value = "Update user.", example = "user22")
    private String updateUser;

    @ApiModelProperty(value = "Update time.", example = "2020-03-12 12:12:12")
    private Date updateTime;

    @ApiModelProperty(value = "Account.", example = "********")
    private String account;

    @ApiModelProperty(value = "Password.", example = "7457457")
    private String password;


}
