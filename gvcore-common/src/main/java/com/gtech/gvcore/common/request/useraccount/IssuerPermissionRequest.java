package com.gtech.gvcore.common.request.useraccount;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class IssuerPermissionRequest implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -1275722843564521899L;

	@ApiModelProperty(value = "Node is leaf.")
	private Boolean isLeaf;

	@ApiModelProperty(value = "Issuer code.")
	private String issuerCode;

	@ApiModelProperty(value = "Data Permission Code. ")
	private List<DataPermissionRequest> dataPermissionList;
}
