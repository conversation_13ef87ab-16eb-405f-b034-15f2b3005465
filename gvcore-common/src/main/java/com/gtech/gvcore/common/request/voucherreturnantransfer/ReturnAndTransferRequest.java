package com.gtech.gvcore.common.request.voucherreturnantransfer;

import com.gtech.gvcore.common.request.allocation.AllocateVoucherBatch;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/13 10:36
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "ReturnAndTransferRequest", description = "Return or transfer voucher request")
@Builder
public class ReturnAndTransferRequest {
    @NotBlank(message = "Voucher request code is required")
    @ApiModelProperty(value = "Voucher request code", required = true)
    private String voucherRequestCode;
    @ApiModelProperty(value = "voucherBatchList", required = true)
    @NotEmpty(message = "voucherBatchList can not be empty")
    private List<AllocateVoucherBatch> voucherBatchList;
    @ApiModelProperty(value = "Operation of user", required = true)
    @NotBlank(message = "user is required")
    private String user;
}
