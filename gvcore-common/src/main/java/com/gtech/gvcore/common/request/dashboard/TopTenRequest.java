package com.gtech.gvcore.common.request.dashboard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/8/31 16:41
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "TopTenRequest")
public class TopTenRequest {

    @NotNull(message = "issuerCode can not be null")
    @ApiModelProperty(value = "IssuerCode", required = true, example = "0123123")
    private String issuerCode;

    @NotNull(message = "date can not be null")
    @ApiModelProperty(value = "Date")
    private Date date;



}
