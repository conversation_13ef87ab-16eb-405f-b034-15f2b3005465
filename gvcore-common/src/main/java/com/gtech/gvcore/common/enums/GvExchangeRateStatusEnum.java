package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR> Gao.Yuhua
 * @date : 2022-02-22 09:23
 **/
public enum GvExchangeRateStatusEnum {


    /**
     * status,
     * 0:disable,1:enable
     */
    STATUS_DISABLE(0, "disable"), STATUS_ENABLE(1, "enable");

    private final int code;

    private final String desc;

    GvExchangeRateStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
