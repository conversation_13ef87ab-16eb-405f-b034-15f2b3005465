package com.gtech.gvcore.common.request.dashboard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/8/31 14:20
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "HistogramRequest")
public class HistogramRequest {

    @NotNull(message = "type can not be null")
    @ApiModelProperty(value = "Type 0-sales 1-redemption", required = true, example = "0")
    private String type;

    @NotNull(message = "date can not be null")
    @ApiModelProperty(value = "Date", required = true, example = "2022-08-31 15:26:12")
    private Date date;

    @NotNull(message = "issuerCode can not be null")
    @ApiModelProperty(value = "IssuerCode", required = true, example = "0123123")
    private String issuerCode;


}
