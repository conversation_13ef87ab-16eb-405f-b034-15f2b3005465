package com.gtech.gvcore.common.response.voucherbatch;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/3/8 10:35
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryMaxVoucherCodeResponse")
public class QueryStartCodeResponse implements Serializable {
    private static final long serialVersionUID = -7061949725484669524L;


    @ApiModelProperty(value = "Voucher start code.", example = "1002222220000001" )
    private String voucherStartNo;


    @ApiModelProperty(value = "Booklet start code.", example = "9001220000000001" )
    private String bookletStartNo;


    @ApiModelProperty(value = "Voucher batch code.", example = "001" )
    private String voucherBatchCode;

    @ApiModelProperty(value = "File name.", example = "01" )
    private String fileName;





}
