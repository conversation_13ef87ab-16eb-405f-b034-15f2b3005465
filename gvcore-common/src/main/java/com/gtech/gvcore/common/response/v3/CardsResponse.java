
package com.gtech.gvcore.common.response.v3;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**

 */
@Data
//@JsonNaming(PropertyNamingStrategy.UpperCamelCaseStrategy.class)
@JsonPropertyOrder({"CardNumber," +
        "CardPIN," +
        "CorporateName," +
        "Balance," +
        "CardType," +
        "ExpiryDate," +
        "CardStatus," +
        "TransferCardNumber," +
        "TransferCardBalance," +
        "TransferCardExpiry," +
        "CardStatusId," +
        "CardCurrencySymbol," +
        "CurrencyCode," +
        "CardCreationType," +
        "ActivationDate," +
        "Holder," +
        "AdjustmentAmount," +
        "InvoiceNumber," +
        "CardProgramGroupType," +
        "ActivationAmount," +
        "PreXactionCardBalance," +
        "TransactionAmount," +
        "IssuerName," +
        "EmployeeId," +
        "ReloadableAmount," +
        "TransactionDateTime," +
        "NativeCardBalance," +
        "CardNativeBalanceWithoutSymbol," +
        "CurrencyConvertedXactionAmount," +
        "CurrencyConversionRate," +
        "PreXactionCardBalanceInNativeCurrency," +
        "PreXactionCardBalanceInNativeCurrencyWithSymbol," +
        "ThemeId," +
        "Trackdata," +
        "ActivationCode," +
        "CardIssuingMode," +
        "ActivationURL," +
        "BarCode," +
        "ApprovalCode," +
        "TotalPreAuthAmount," +
        "TotalReloadedAmount," +
        "TotalRedeemedAmount," +
        "ResponseCode," +
        "ResponseMessage," +
        "ErrorCode," +
        "ErrorDescription," +
        "RedeemStartDate," +
        "PreAuthCode," +
        "RecentTransactions," +
        "Transferable," +
        "Reusable," +
        "ExtendedParameters," +
        "Beneficiaries," +
        "Notes," +
        "Reason," +
        "CardFormats," +
        "SequenceNumber"})
public class CardsResponse implements Serializable {

    private static final long serialVersionUID = 6441188419187832520L;
//    @JsonProperty("CardNumber")
    private String voucherNumber;

//    @JsonProperty("CardPIN")
    private String voucherPIN;

//    @JsonProperty("CorporateName")
    private String corporateName;

//    @JsonProperty("Balance")
    private BigDecimal balance;

//    @JsonProperty("CardType")
    private String voucherType;

//    @JsonProperty("ExpiryDate")
    private Date expiryDate;

//    @JsonProperty("CardStatus")
    private String voucherStatus;

//    @JsonProperty("TransferCardNumber")
    private String transferVoucherNumber;

//    @JsonProperty("TransferCardBalance")
    private Integer transferVoucherBalance;

//    @JsonProperty("TransferCardExpiry")
    private String transferVoucherExpiryDate;

//    @JsonProperty("CardStatusId")
    private Integer voucherStatusId;

//    @JsonProperty("CardCurrencySymbol")
    private String voucherCurrencySymbol;

//    @JsonProperty("CurrencyCode")
    private String currency;

//    @JsonProperty("CardCreationType")
    private String voucherCreationType;

//    @JsonProperty("ActivationDate")
    private Date activationDate;

//    @JsonProperty("Holder")
    private Holder holder;

//    @JsonProperty("AdjustmentAmount")
    private Integer adjustmentAmount;

//    @JsonProperty("InvoiceNumber")
    private String invoiceNumber;

//    @JsonProperty("CardProgramGroupType")
    private String vpgType;

//    @JsonProperty("ActivationAmount")
    private BigDecimal activationAmount;

//    @JsonProperty("PreXactionCardBalance")
    private BigDecimal preXactionCardBalance;

//    @JsonProperty("TransactionAmount")
    private BigDecimal transactionAmount;

//    @JsonProperty("IssuerName")
    private String issuerName;

//    @JsonProperty("EmployeeId")
    private String employeeId;

//    @JsonProperty("ReloadableAmount")
    private BigDecimal reloadableAmount;

//    @JsonProperty("TransactionDateTime")
    private Date transactionTimestamp;

//    @JsonProperty("NativeCardBalance")
    private String nativeVoucherBalance;

//    @JsonProperty("CardNativeBalanceWithoutSymbol")
    private String nativeBalance;

//    @JsonProperty("CurrencyConvertedXactionAmount")
    private BigDecimal currencyConvertedXactionAmount;

//    @JsonProperty("CurrencyConversionRate")
    private String currencyConversionRate;

//    @JsonProperty("PreXactionCardBalanceInNativeCurrency")
    private String preXactionCardBalanceInNativeCurrency;

//    @JsonProperty("PreXactionCardBalanceInNativeCurrencyWithSymbol")
    private String preXactionCardBalanceInNativeCurrencyWithSymbol;

//    @JsonProperty("ThemeId")
    private String themeId;

//    @JsonProperty("Trackdata")
    private String trackData;

//    @JsonProperty("ActivationCode")
    private String activationCode;

//    @JsonProperty("CardIssuingMode")
    private String voucherIssuingMode;

//    @JsonProperty("ActivationURL")
    private String activationURL;

//    @JsonProperty("BarCode")
    private String barcode;

//    @JsonProperty("ApprovalCode")
    private String approvalCode;

//    @JsonProperty("TotalPreAuthAmount")
//    private BigDecimal TotalPreAuthAmount;
//
//    @JsonProperty("TotalReloadedAmount")
//    private BigDecimal TotalReloadedAmount;
//
//    @JsonProperty("TotalRedeemedAmount")
//    private BigDecimal TotalRedeemedAmount;
//
//    @JsonProperty("ResponseCode")
    private Integer responseCode;

//    @JsonProperty("ResponseMessage")
    private String responseMessage;

//    @JsonProperty("ErrorCode")
    private String errorCode;

//    @JsonProperty("ErrorDescription")
    private String errorDescription;

//    @JsonProperty("RedeemStartDate")
    private String redeemStartDate;

//    @JsonProperty("PreAuthCode")
//    private String PreAuthCode;
//
//    @JsonProperty("RecentTransactions")
//    private String RecentTransactions;
//
//    @JsonProperty("Transferable")
    private Boolean transferable;

//    @JsonProperty("Reusable")
    private Boolean reusable;

//    @JsonProperty("ExtendedParameters")
    private String extendedParameters;

//    @JsonProperty("Beneficiaries")
//    private String Beneficiaries;
//
//    @JsonProperty("Notes")
//    private String Notes;
//
//    @JsonProperty("Reason")
//    private String Reason;
//
//    @JsonProperty("CardFormats")
    private List<CardFormats> voucherFormat;

//    @JsonProperty("SequenceNumber")
//    private String SequenceNumber;



}