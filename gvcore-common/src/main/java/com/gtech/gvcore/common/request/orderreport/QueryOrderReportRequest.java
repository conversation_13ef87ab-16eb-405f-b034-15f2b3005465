package com.gtech.gvcore.common.request.orderreport;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.gtech.commons.page.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/7 0:47
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryOrderReportByType", description = "Query order report by type")
public class QueryOrderReportRequest extends PageParam {
    @ApiModelProperty(value = "Report type", required = true)
    @NotNull(message = "Report type can not be empty")
    private Integer reportType;

    @ApiModelProperty(value = "User code can not be empty", required = true)
    @NotBlank(message = "User code can not be empty")
    private String userCode;
}
