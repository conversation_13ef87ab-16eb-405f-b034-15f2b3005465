package com.gtech.gvcore.common.request.releaseapprove;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/26 12:14
 */


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("Release approve amount list")
public class ReleaseApproveAmountListRequest {

    @ApiModelProperty(value = "releaseApproveAmountRequests", required = true)
    @Valid
    @NotEmpty(message = "releaseApproveAmountRequests can not be empty")
    @Size(min = 1)
    private List<ReleaseApproveAmountRequest> releaseApproveAmountRequests;

    @NotBlank(message = "Create user can not be empty")
    @ApiModelProperty(value = "Create user", required = true, example = "createUser")
    private String createUser;
}
