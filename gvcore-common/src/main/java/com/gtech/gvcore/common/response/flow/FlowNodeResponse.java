package com.gtech.gvcore.common.response.flow;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class FlowNodeResponse {

	
	private String flowCode;
	
    /**
     * flow node code
     */
    @ApiModelProperty(value = "flow node code")
    private String flowNodeCode;

    /**
     * flow node name
     */
    @ApiModelProperty(value = "flow node name")
    private String flowNodeName;

    /**
     * remark
     */
    private String remark;

    /**
     * status
     */
    private Integer status;
    
}