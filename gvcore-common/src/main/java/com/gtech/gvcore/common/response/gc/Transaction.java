package com.gtech.gvcore.common.response.gc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "Transaction", description = "Transaction Record")
public class Transaction {


    @ApiModelProperty(value = "Transaction ID", notes = "Unique transaction identifier", example = "TXN123456", position = 1)
    private String transactionId;

    @ApiModelProperty(value = "Transaction Date", notes = "Transaction date and time", example = "2023-01-01T12:00:00", position = 2)
    private Date transactionDate;

    @ApiModelProperty(value = "Transaction Type", notes = "Type of transaction: SALES, REDEMPTION, ACTIVATION, ACTIVATION_EXTENSION, CANCEL_SALES, BLOCK, UNBLOCK", example = "REDEMPTION", position = 3)
    private String transactionType;

    @ApiModelProperty(value = "Transaction Amount", notes = "Transaction amount", example = "50.00", position = 4)
    private BigDecimal transactionAmount;

    @ApiModelProperty(value = "Previous Balance", notes = "Gift card balance before transaction", example = "200.00", position = 5)
    private BigDecimal previousBalance;

    @ApiModelProperty(value = "Remaining Balance", notes = "Gift card balance after transaction", example = "150.00", position = 6)
    private BigDecimal remainingBalance;

    @ApiModelProperty(value = "Merchant Name", notes = "Transaction merchant name", example = "ABC Store", position = 7)
    private String merchant;

    @ApiModelProperty(value = "Notes", notes = "Transaction notes", example = "Customer purchase", position = 11)
    private String notes;

    @ApiModelProperty(value = "Invoice Number", notes = "Invoice number from e-commerce system", example = "INV-12345", position = 12)
    private String invoiceNumber;

    @ApiModelProperty(value = "Merchant Outlet", notes = "Merchant outlet", example = "ABC", position = 14)
    private String merchantOutlet;


}
