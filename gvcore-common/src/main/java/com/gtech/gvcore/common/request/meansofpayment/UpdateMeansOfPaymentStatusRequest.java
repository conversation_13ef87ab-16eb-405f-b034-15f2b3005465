package com.gtech.gvcore.common.request.meansofpayment;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年2月21日
 */
@Data
public class UpdateMeansOfPaymentStatusRequest {

    @ApiModelProperty(value = "id", required = true)
    @NotNull(message = "id can not be null")
    private Long id;

    @ApiModelProperty(value = "status", required = true)
    @NotNull(message = "status can not be null")
    @Range(min = 0, max = 1, message = "status value range [0,1]")
    private Integer status;

    @ApiModelProperty(value = "updateUser", required = true)
    @NotEmpty(message = "updateUser can not be empty")
    @Length(max = 50)
    private String updateUser;

}
