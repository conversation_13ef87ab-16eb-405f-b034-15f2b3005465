package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @date 2022年3月21日
 */
public enum VoucherOwnerTypeEnum implements IEnum<String> {

    PRINTER("printer", "printer"),
    WAREHOUSE("warehouse", "Warehouse"), 
    OUTLET("outlet", "outlet"), 
    CUSTOMER("customer", "customer");



    private final String code;

    private final String desc;

    VoucherOwnerTypeEnum(String code, String desc) {

        this.code = code;
        this.desc = desc;
    }
    @Override
    public String code() {
        return this.code;
    }

    @Override
    public String desc() {
        return this.desc;
    }

    @Override
    public boolean equalsCode(String code) {
        return this.code.equals(code);
    }

}
