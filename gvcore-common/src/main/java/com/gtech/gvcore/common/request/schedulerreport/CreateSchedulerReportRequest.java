package com.gtech.gvcore.common.request.schedulerreport;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/31 14:29
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "Create scheduler report request")
public class CreateSchedulerReportRequest implements Serializable {
    private static final long serialVersionUID = -9012843678506807181L;
    @ApiModelProperty(value = "Scheduler name", required = true)
    @NotBlank(message = "Scheduler name can not be empty")
    private String schedulerName;

    private String schedulerReportCode;

    @ApiModelProperty(value = "Execution time", required = true)
    @NotNull(message = "Execution time can not be empty")
    private Date executionTime;

    @ApiModelProperty(value = "Execution frequency,optional value is once/daily/weekly/monthly/or/yearly", required = true, example = "daily")
    @NotBlank(message = "Frequency can not be empty")
    private String frequency;

    @ApiModelProperty(value = "cron expression，The schedulerCron of frequency is not once")
    private String schedulerCron;

    @ApiModelProperty(value = "every", required = true)
    @NotNull(message = "every can not be empty")
    private Integer every;

    @ApiModelProperty(value = "Repeat End,The value can be never, after, or onDate. The default value is never")
    private String repeatEnd;

    @ApiModelProperty(value = "repeat end after")
    private Integer repeatEndAfter;

    @ApiModelProperty(value = "repeat end On date")
    private Date repeatEndTime;

    @ApiModelProperty(value = "Report name", required = true)
    @NotBlank(message = "Report name can not be empty")
    private String reportName;

    @ApiModelProperty(value = "Report type", required = true)
    @NotNull(message = "Report type can not be empty")
    private Integer reportType;

    @ApiModelProperty(value = "Issuer code", required = true, example = "IS102203281152001266")
    @NotBlank(message = "Issuer code can not be empty")
    private String issuerCode;

    @ApiModelProperty(value = "Merchant code")
    private String merchantCode;

    @ApiModelProperty(value = "Merchant outlet code")
    private String merchantOutletCode;

    @ApiModelProperty(value = "Transaction type")
    private String transactionType;

    @ApiModelProperty(value = "Data range yesterday(1), last 7 days(7), last 30 days(30)", required = true, example = "1")
    @NotNull(message = "Data range")
    private Integer dataRange;

    @ApiModelProperty(value = "Transaction Status")
    private String transactionStatus;

    @ApiModelProperty(value = "Voucher status")
    private Integer voucherStatus;

    @ApiModelProperty(value = "Voucher program group code", example = "e372bb6f97da4902b808db4386247ec5")
    private String vpgCode;

    @ApiModelProperty(value = "Email(s),you can enter multiple use ','", example = "<EMAIL>,<EMAIL>")
    private String emails;

    @ApiModelProperty(value = "Email subject")
    private String emailSubject;

    @ApiModelProperty(value = "FTP address")
    private String ftpAddress;

    @ApiModelProperty(value = "Login method")
    private String loginMethod;

    @ApiModelProperty(value = "Encryption key")
    private String encryptionKey;

    @ApiModelProperty(value = "username")
    private String ftpUsername;

    @ApiModelProperty(value = "password")
    private String ftpPassword;

    @ApiModelProperty(value = "Create user", required = true)
    @NotBlank(message = "Create user can not be empty")
    private String createUser;

}
