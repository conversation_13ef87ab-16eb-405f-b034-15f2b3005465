
package com.gtech.gvcore.common.request.v3;

import lombok.Data;

import java.util.List;

@Data
public class TransactionsRequest {

    private String transactionTypeId;
    private String inputType;
    private String businessReferenceNumber;
    private String invoiceNumber;
    private String numberOfCards;
    private List<Cards> cards;
    private Purchaser purchaser;
    private String notes;
    private String terminalId;

    private String transactionId;

    private String batchId;

    private List<String> cardNumbers;


}