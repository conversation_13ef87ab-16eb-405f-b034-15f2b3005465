package com.gtech.gvcore.common.request.voucherrequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/22 22:56
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "Bulk approve voucher request request")
public class BulkApproveVoucherRequestRequest implements Serializable {

    private static final long serialVersionUID = -3655274569776923359L;
    @ApiModelProperty(value = "The examination and approval status (3:Rejected,4:Pending Allocation)", required = true, example = "4")
    @Max(value = 4, message = "It can only be 3 or 4")
    @Min(value = 3, message = "It can only be 3 or 4")
    private Integer status;

    @NotBlank(message = "The updater cannot do without")
    @ApiModelProperty(value = "Update user", required = true, example = "Approver")
    private String updateUser;

    @NotNull(message = "The request voucherRequestCode list cannot be empty")
    @ApiModelProperty(value = "voucherRequestCode list of the request that you want to modify", required = true)
    private List<String> voucherRequestCode;

    @ApiModelProperty(value = "Approve notes")
    private String approveRemarks;
}
