package com.gtech.gvcore.common.request.opuseraccount;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2022/6/13 13:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CreateOpUserAccountRequest")
public class CreateOpUserAccountRequest {

    private String customerCode;

    private String account;

    private String password;

    private String email;




}
