package com.gtech.gvcore.common.request.voucherbatch;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.gvcore.common.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/3/2 10:45
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CreateVoucherBatchRequest")
public class CreateVoucherBatchRequest implements Serializable {
    private static final long serialVersionUID = 4755873450986619350L;


    @ApiModelProperty(value = "Purchase order no.", example = "1122333", required = true)
    @NotEmpty(message = "purchaseOrderNo can not be empty")
    @Length(max=40)
    private String purchaseOrderNo;

    @ApiModelProperty(value = "Issuer code.", example = "1122333", required = true)
    @NotEmpty(message = "issuerCode can not be empty")
    private String issuerCode;

    @ApiModelProperty(value = "Cpg code.", example = "1122333", required = true)
    @NotEmpty(message = "cpgCode can not be empty")
    private String cpgCode;

    /**
     * lot number
     */
    @ApiModelProperty(value = "Voucher batch code.", example = "1122333", required = true)
    @NotEmpty(message = "Lot number can not be empty")
    private String voucherBatchCode;

    @ApiModelProperty(value = "Article code.", example = "1122333", required = true)
    @NotEmpty(message = "articleCode can not be empty")
    private String articleCode;

    @ApiModelProperty(value = "Mop code.", example = "1122333", required = true)
    @NotEmpty(message = "mopCode can not be empty")
    private String mopCode;

    @ApiModelProperty(value = "Printer code.", example = "1122333", required = true)
    @NotEmpty(message = "printerCode can not be empty")
    private String printerCode;

    @ApiModelProperty(value = "Booklet start code.", example = "9001220000000001", required = true)
    @NotEmpty(message = "bookletStartNo can not be empty")
    private String bookletStartNo;

    @ApiModelProperty(value = "Booklet end code.", example = "9001220000000100", hidden = true)
    private String bookletEndNo;

    /**
     * number of vouchers per booklet
     */
    @ApiModelProperty(value = "Booklet per code.", example = "100", required = true)
    @NotNull(message = "bookletPerNum can not be empty")
    private Integer bookletPerNum;

    /**
     * number of booklets
     */
    @ApiModelProperty(value = "Booklet num.", example = "3", required = true)
    @NotNull(message = "bookletNum can not be empty")
    private Integer bookletNum;

    @ApiModelProperty(value = "Voucher start code.", example = "1002222220000001", required = true)
    @NotEmpty(message = "voucherStartNo can not be empty")
    private String voucherStartNo;

    @ApiModelProperty(value = "Voucher end code.", example = "1002222220003000", hidden = true)
    private String voucherEndNo;

    /**
     * number of voucher
     */
    @ApiModelProperty(value = "Voucher num.", example = "100", required = true)
    @NotNull(message = "voucherNum can not be empty")
    private Integer voucherNum;

    @ApiModelProperty(value = "Denomination.", example = "1.1", required = true)
    @NotNull(message = "denomination can not be empty")
    private BigDecimal denomination;

    @ApiModelProperty(value = "Voucher effective date.", example = "2022-02-17 16:27", required = true)
    @NotNull(message = "voucherEffectiveDate can not be empty")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date voucherEffectiveDate;

    @ApiModelProperty(value = "File name.", example = "MAP-MAPQCGV50K-022-122-001", required = true)
    @NotEmpty(message = "fileName can not be empty")
    private String fileName;

    @ApiModelProperty(value = "File format.", example = "Excel", required = true)
    @NotEmpty(message = "fileFormat can not be empty")
    private String fileFormat;

    @ApiModelProperty(value = "Permission code.", example = "1122333")
    private String permissionCode;

    @ApiModelProperty(value = "Create user.", example = "user1")
    private String createUser;

    @ApiModelProperty(value = "Create time.", example = "2022-02-17 16:27")
    private Date createTime;

    private String approvalCode;
    private String invoiceNo;


    public void validation() {


        if (voucherNum != bookletNum * bookletPerNum) {
            throw new GTechBaseException(ErrorCodes.PARAM_SPECIFICATION_ERROR, "Number of Voucher Calculation error");
        }

        //包结束code = 包开始code + 包数量
        this.bookletEndNo = String.valueOf(Long.valueOf(bookletStartNo) + Long.valueOf(bookletNum) - 1);

        //券结束code = 券开始code + 券总数
        this.voucherEndNo = String.valueOf(Long.valueOf(voucherStartNo) + Long.valueOf(voucherNum) - 1);


    }


}
