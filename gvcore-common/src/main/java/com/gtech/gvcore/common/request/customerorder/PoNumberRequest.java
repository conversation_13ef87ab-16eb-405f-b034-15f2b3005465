package com.gtech.gvcore.common.request.customerorder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @Date 2022/11/15 18:12
 */
@Data
public class PoNumberRequest {


    @ApiModelProperty(value = "StoreCode", required = true)
    @NotEmpty(message = "StoreCode can not be empty")
    private String storeCode;

    @ApiModelProperty(value = "poNumber")
    private String poNumber;


}
