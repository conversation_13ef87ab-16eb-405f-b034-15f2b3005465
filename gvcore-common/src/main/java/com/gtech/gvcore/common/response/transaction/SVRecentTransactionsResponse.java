package com.gtech.gvcore.common.response.transaction;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
//@JsonNaming(PropertyNamingStrategy.UpperCamelCaseStrategy.class)
public class SVRecentTransactionsResponse {

	@ApiModelProperty(value = "Card Number.", notes = "Card Number")
	private String voucherNumber;
	@ApiModelProperty(value = "Transaction Date.",  notes = "The date of transaction in YYYY-MM-DD HH:MM:SS format")
	private String transactionDate;
	@ApiModelProperty(value = "Transaction Date At Server.",  notes = "The date of transaction at the Server in YYYY-MM-DD HH:MM:SS format")
	private String transactionDateAtServer;
	@ApiModelProperty(value = "Outlet Name.", notes = "The outletname/storename to which this POS is assigned to in the server")
	private String storeName;
	@ApiModelProperty(value = "Outlet Code.", notes = "The code assigned to the outlet")
	private String storeCode;
	@ApiModelProperty(value = "Invoice Number.", notes = "Optional Invoice Number")
	private String invoiceNumber;
	@ApiModelProperty(value = "Transaction Type.", notes = "The transaction type ID passed in the request.")
	private String transactionType;
	@ApiModelProperty(value = "Transaction Amount.", notes = "transacted amount")
	private BigDecimal transactionAmount;
	@ApiModelProperty(value = "Card Balance.", notes = "The balance on the card")
	private BigDecimal voucherBalance;
	@ApiModelProperty(value = "Loyalty.", notes = "Represents loyalty value that got added/redeemed in the particular transaction")
	private BigDecimal loyalty;
	@ApiModelProperty(value = "Notes.", notes = "Any Reference text to be captured along with this transaction")
	private String notes;
	/**
	 * 	'batchid',
	 *  'approvalcode',
	 *  'referencenumber',
	 *  'businessreferencenumber',
	 *  'currencycode',
	 *  'transactionstatus',
	 *  'transactiondatetime',
	 *  'xactionreference',
	 *  'originalxactionreference',
	 *  'username'
	 */
//
//	private String batchId;
//	private String approvalCode;
//	private String referenceNumber;
//	private String businessReferenceNumber;
//	private String currencyCode;
//	private String transactionStatus;
//	private String transactionDateTime;
//	private String xactionReference;
//	private String originalXactionReference;
//	private String userName;



}