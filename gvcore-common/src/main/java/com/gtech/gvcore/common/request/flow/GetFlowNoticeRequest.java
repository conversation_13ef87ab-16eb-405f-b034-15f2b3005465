package com.gtech.gvcore.common.request.flow;

import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetFlowNoticeRequest {


	/**
	 * flow code
	 */
	@ApiModelProperty(value = "flow code", required = true, example = "sales,customer_order")
	@NotEmpty(message = "flow code can't be empty")
	private String flowCode;

	/**
	 * flow name
	 */
	@ApiModelProperty(value = "flow node code", required = true, example = "created,submit")
	@NotEmpty(message = "flow node code can't be empty")
	private String flowNodeCode;

	@ApiModelProperty(value = "business code", required = true)
	@NotEmpty(message = "business code can't be empty")
	private String businessCode;

}