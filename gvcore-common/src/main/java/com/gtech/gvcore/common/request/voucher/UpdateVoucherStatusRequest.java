package com.gtech.gvcore.common.request.voucher;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/3/9 16:38
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "UpdateVoucherStatusRequest")
public class UpdateVoucherStatusRequest implements Serializable {
    private static final long serialVersionUID = -5292213000441842200L;

    /**
     * 根据voucherCode修改状态
     */
    private String voucherCode;

    private List<String> voucherCodeList;

    /**
     * 根据voucher区间修改状态
     */
    private String voucherStartNo;

    private String voucherEndNo;


    private Integer status;

	private Integer oldStatus;

    private Integer circulationStatus;

	private Integer oldCirculationStatus;

	private Integer voucherStatus;

	private Integer oldVoucherStatus;

    private String updateUser;


    private Date salesTime;

    private String salesOutlet;

    private Date usedTime;

    private String usedOutlet;

}
