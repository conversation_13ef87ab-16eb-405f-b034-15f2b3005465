package com.gtech.gvcore.common.request.voucherbatch;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/3/7 10:56
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "GetVoucherBatchRequest")
@Accessors(chain = true)
public class GetVoucherBatchRequest implements Serializable {
    private static final long serialVersionUID = -6714969100311337855L;

    @ApiModelProperty(value = "Purchase order no.", example = "1122333",hidden = true)
    private String purchaseOrderNo;


    @ApiModelProperty(value = "Voucher batch code.", example = "1122333",required = true)
    @NotEmpty(message = "voucherBatchCode can not be empty")
    private String voucherBatchCode;


}
