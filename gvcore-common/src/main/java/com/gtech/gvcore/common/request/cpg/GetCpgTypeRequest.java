package com.gtech.gvcore.common.request.cpg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> Gao.Yuhua
 * @date : 2022-02-21 17:53
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "GetCpgTypeRequest")
public class GetCpgTypeRequest {

    /**
     * cpgTypeCode
     */
    @NotEmpty(message = "cpgTypeCode can not be empty")
    @ApiModelProperty(value = "cpgTypeCode", example = "CPC102202241512000241", required = true)
    private String cpgTypeCode;
}
