/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.gvcore.common.request.useraccount;

import java.io.Serializable;
import java.util.List;

import com.gtech.basic.idm.web.vo.param.QueryUserAccountListParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "GvQueryUserAccountRequest")
public class GvQueryUserAccountRequest extends QueryUserAccountListParam implements Serializable {


	/**
	 * 
	 */
	private static final long serialVersionUID = -705413953836895994L;

	@ApiModelProperty(value = "Full name", example = "junxian")
	private String fullName;

	@ApiModelProperty(value = "Issuer code", example = "SF0001")
	private String issuerCode;
	
	private List<String> roleCodeList;

}
