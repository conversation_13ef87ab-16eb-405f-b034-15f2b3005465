package com.gtech.gvcore.common.response.authorize;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class AuthorizeResponse {

	@ApiModelProperty(value = "Auth Token.", example = "XXXXXXXXX", required = true, notes = "JWT token (encoded Base64 format)")
	private String authToken;
	@ApiModelProperty(value = "Auth Token.", required = true, notes = "Please see below under MerchantOutletInfo")
	private MerchantOutletInfo merchantOutletInfo;
	@ApiModelProperty(value = "Locale Info.", required = true, notes = "Please see below under LocaleInfo")
	private LocaleInfo localeInfo;
	@ApiModelProperty(value = "Receipt Info.", required = true, notes = "Configured receipt Info if any. Please see below for details under ReceiptInfo")
	private ReceiptInfo receiptInfo;
	@ApiModelProperty(value = "Response Code.", required = true, notes = "A 5 digit integer response code. A zero value indicates success and a non-zero value indicates failure.")
	private Integer responseCode;
	@ApiModelProperty(value = "Response Message.", required = true, notes = "Contains the response message or error message in case the transacƟ on has failed (response code is non-zero).")
	private String responseMessage;
	@ApiModelProperty(value = "Batch Id.", required = true, notes = "This needs to be persisted to be passed as a OriginalBatchId for cancellaƟ on")
	private Integer batchId;

}
