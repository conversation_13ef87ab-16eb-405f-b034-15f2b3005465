package com.gtech.gvcore.common.enums;

import com.gtech.commons.code.IEnum;

/**
 * @ClassName CustomerTypeEnum
 * @Description
 * <AUTHOR>
 * @Date 2023/1/31 10:49
 * @Version V1.0
 **/
public enum CustomerTypeEnum implements IEnum {

    INDIVIDUAL("Individual", "Individual"),
    CORPORATE("Corporate", "Corporate"),
    ;

    public static final String REMARK = "";

    CustomerTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    //code
    private final String code;

    //desc
    private final String desc;

    public static CustomerTypeEnum valueOfCode(String code) {
        for (CustomerTypeEnum value : CustomerTypeEnum.values()) {
            if (value.code().equals(code)) {
                return value;
            }
        }
        return null;
    }

    @Override
    public String code() {

        return String.valueOf(code);
    }

    @Override
    public String desc() {

        return desc;
    }

    @Override
    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

}
