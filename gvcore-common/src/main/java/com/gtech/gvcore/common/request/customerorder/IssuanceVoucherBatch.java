package com.gtech.gvcore.common.request.customerorder;

import java.math.BigDecimal;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年3月23日
 */
@Data
public class IssuanceVoucherBatch {

    @ApiModelProperty(value = "voucherStartNo", required = true)
    @NotEmpty(message = "voucherStartNo can not be empty")
    private String voucherStartNo;

    @ApiModelProperty(value = "voucherEndNo", required = true)
    @NotEmpty(message = "voucherEndNo can not be empty")
    private String voucherEndNo;

    @ApiModelProperty(value = "denomination", required = true)
    @NotNull(message = "denomination can not be null")
    private BigDecimal denomination;

}
