package com.gtech.gvcore.common.request.gcapi;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

@Data
@Accessors(chain = true)
public class CustomerInfo {


	@ApiModelProperty(value = "Customer Id. ", example = "123456", notes = "Customer ID")
	@Length(max = 50, message = "CustomerId maximum length 50")
	@NotEmpty(message = "customerId can not be empty")
	private String customerId;

	@ApiModelProperty(value = "Salutation. ", example = "123456", notes = "Salutation")
	@Length(max = 20, message = "Salutation maximum length 20")
	private String customerName;
	@ApiModelProperty(value = "First Name. ", required = true, example = "123456", notes = "First name of the customer")
	@Length(max = 50, message = "First Name maximum length 50")
	private String firstName;
	@ApiModelProperty(value = "Last Name. ", required = true, example = "123456", notes = "Last name of the customer")
	@Length(max = 50, message = "Last Name maximum length 50")
	private String lastName;
	@ApiModelProperty(value = "Mobile. ", example = "123456", notes = "Mobile number")
	@Length(max = 50, message = "Mobile maximum length 50")
	private String phoneNumber;
	@ApiModelProperty(value = "Address Line 1. ", example = "123456", notes = "Address Line 1")
	@Length(max = 250, message = "Address Line 1 maximum length 250")
	private String addressLine1;
	@ApiModelProperty(value = "Address Line 2. ", example = "123456", notes = "Address Line 2")
	@Length(max = 250, message = "Address Line 2 maximum length 250")
	private String addressLine2;
	@ApiModelProperty(value = "Address Line 3. ", example = "123456", notes = "Address Line 3")
	@Length(max = 250, message = "Address Line 3 maximum length 250")
	private String addressLine3;
	@ApiModelProperty(value = "Date Of Birth. ", example = "2022-02-02", notes = "YYYY-MM-DD")
	private String birthdayDate;
	@ApiModelProperty(value = "Email. ", example = "123456", notes = "email id of the customer")
	@Length(max = 50, message = "Email maximum length 50")
	private String email;
	@ApiModelProperty(value = "City. ", example = "123456", notes = "city in which customer resides")
	@Length(max = 50, message = "City maximum length 50")
	private String city;
	@ApiModelProperty(value = "State. ", example = "123456", notes = "state in which customer resides")
	@Length(max = 50, message = "State maximum length 50")
	private String state;
	@ApiModelProperty(value = "Country. ", example = "123456", notes = "country in which customer resides")
	@Length(max = 50, message = "Country maximum length 50")
	private String country;
	@ApiModelProperty(value = "Empid. ", example = "123456", notes = "employee id")
	@Length(max = 50, message = "Empid maximum length 50")
	private String employeeId;
	@ApiModelProperty(value = "Corporate Name. ", example = "123456", notes = "corporate name")
	@Length(max = 50, message = "Corporate Name maximum length 50")
	private String corporateName;

	@ApiModelProperty(value = "Customer Type", example = "INDIVIDUAL", notes = "Type of customer: INDIVIDUAL, CORPORATE")
	private String customerType;

	@ApiModelProperty(value = "Gender", example = "M", notes = "Customer gender: M/F")
	private String gender;

	@ApiModelProperty(value = "Anniversary", example = "2020-06-15", notes = "Anniversary date in YYYY-MM-DD format")
	private String anniversary;

	@ApiModelProperty(value = "Marital Status", example = "MARRIED", notes = "Marital status: SINGLE, MARRIED, DIVORCED")
	private String maritalStatus;

	@ApiModelProperty(value = "Alternative Phone", example = "+1234567890", notes = "Alternative phone number")
	private String alterPhone;

	@ApiModelProperty(value = "Zip Code", example = "12345", notes = "Postal/ZIP code")
	private String zipCode;

	@ApiModelProperty(value = "Region", example = "North", notes = "Geographic region")
	private String region;

	@ApiModelProperty(value = "Area", example = "Downtown", notes = "Area within city")
	private String area;

	@ApiModelProperty(value = "Mobile", example = "+1234567890", notes = "Mobile phone number")
	private String mobile;



}