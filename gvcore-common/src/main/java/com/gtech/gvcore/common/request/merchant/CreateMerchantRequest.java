package com.gtech.gvcore.common.request.merchant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CreateMerchantRequest")
public class CreateMerchantRequest {

    @ApiModelProperty( value = "Merchant name.", example = "MAP",required = true)
    @NotEmpty(message = "merchantName can not be empty")
    @Length(max = 100)
    private String merchantName;

    @ApiModelProperty( value = "Company code.", example = "11223344",required = true)
    @NotEmpty(message = "companyCode can not be empty")
    @Length(max = 100)
    private String companyCode;

    @ApiModelProperty( value = "Issuer code.", example = "11223344")
    @Length(max = 100)
    private String issuerCode;

    @ApiModelProperty( value = "Remarks.", example = "remarks")
    @Length(max = 100)
    private String remarks;

    @ApiModelProperty( value="Create user.", example="user1",required = true)
    @NotEmpty(message = "createUser can not be empty")
    @Length(max = 100)
    private String createUser;




}
