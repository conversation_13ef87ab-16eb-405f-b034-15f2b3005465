package com.gtech.gvcore.common.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 该枚举应在与原枚举保持一致的情况下追加DEACTIVATED 数值
 * @see com.gtech.gvcore.common.enums.VoucherStatusEnum
 */
public enum ReportVoucherStatusEnum {

    /**
     * DEACTIVATED -1
     */

    VOUCHER_DEACTIVATED(-1, "DEACTIVATED"),
    VOUCHER_DESTROY(-2, "DESTROY"),

    VOUCHER_NEWLY_GENERATED(0, "CREATED"),
    VOUCHER_ACTIVATED(1, "ACTIVATED"),
    VOUCHER_REDEEMED(2, "REDEEMED"),
    VOUCHER_CANCELLED(3, "CANCELLED"),
    VOUCHER_EXPIRED(4, "EXPIRED"),
    VOUCHER_PURCHASED(5, "PURCHASED"),

    ;

    private final Integer code;

    private final String desc;

    ReportVoucherStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getCodeString() {
        return String.valueOf(code);
    }

    public String getDesc() {
        return desc;
    }

    public static ReportVoucherStatusEnum valueOfCode (Integer code) {

        return valueOfCode(String.valueOf(code));
    }

    public static ReportVoucherStatusEnum valueOfCode (String code) {

        for (ReportVoucherStatusEnum anEnum : ReportVoucherStatusEnum.values()) {
            if (Objects.equals(anEnum.getCodeString(), code)) {
                return anEnum;
            }
        }

        return null;

    }

    public static String getDescByCode(Integer code) {

        return getDescByCode(String.valueOf(code));
    }

    public static String getDescByCode(String code) {

        ReportVoucherStatusEnum status = valueOfCode(code);

        if (null == status) return StringUtils.EMPTY;

        return status.getDesc();

    }
}