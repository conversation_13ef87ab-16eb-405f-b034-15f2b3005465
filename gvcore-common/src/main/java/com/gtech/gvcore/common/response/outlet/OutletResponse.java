package com.gtech.gvcore.common.response.outlet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "OutletResponse")
public class OutletResponse {

    @ApiModelProperty(value = "Outlet Code.", example = "12341254")
    private String outletCode;

    @ApiModelProperty(value = "Outlet name.", example = "MAP")
    private String outletName;

    @ApiModelProperty(value = "Merchant code.", example = "1231254125")
    private String merchantCode;

    @ApiModelProperty(value = "Issuer code.", example = "1231254125")
    private String issuerCode;

    @ApiModelProperty(value = "Sbu",example = "independet")
    private String sbu;

    @ApiModelProperty(value = "Merchant name.", example = "1231254125")
    private String merchantName;

    @ApiModelProperty(value = "Business outlet code.", example = "1234124")
    private String businessOutletCode;

    @ApiModelProperty(value = "Outlet type.", example = "independet")
    private String outletType;

    @ApiModelProperty(value = "State code.", example = "112233")
    private String stateCode;

    @ApiModelProperty(value = "City code.", example = "223344")
    private String cityCode;

    @ApiModelProperty(value = "State name.", example = "112233")
    private String stateName;

    @ApiModelProperty(value = "City name.", example = "223344")
    private String cityName;

    @ApiModelProperty(value = "District code.", example = "551122")
    private String districtCode;

    @ApiModelProperty(value = "District name.", example = "beijing")
    private String districtName;

    @ApiModelProperty(value = "Address1.", example = "2nd Floor NO.23")
    private String address1;

    @ApiModelProperty(value = "Address2.", example = "2nd Floor NO.23")
    private String address2;

    @ApiModelProperty(value = "Pin code.", example = "0")
    private String pinCode;

    @ApiModelProperty(value = "First name.", example = "name1")
    private String firstName;

    @ApiModelProperty(value = "Last name.", example = "name2")
    private String lastName;

    @ApiModelProperty(value = "Email.", example = "<EMAIL>")
    private String email;

    @ApiModelProperty(value = "Phone.", example = "1234125412")
    private String phone;

    @ApiModelProperty(value = "Mobile.", example = "1231245215")
    private String mobile;

    @ApiModelProperty(value = "Alertnate email.", example = "<EMAIL>")
    private String alertnateEmail;

    @ApiModelProperty(value = "Alertnate phone.", example = "45765765765")
    private String alertnatePhone;

    @ApiModelProperty(value = "Descriptive.", example = "text")
    private String descriptive;

    @ApiModelProperty(value = "Status.", example = "0")
    private Integer status;

    @ApiModelProperty(value = "Cpgs.", example = "0")
    private List<OutletCpgVo> cpg;

    @ApiModelProperty(value = "Create user.", example = "user1")
    private String createUser;

    @ApiModelProperty(value = "Create time.", example = "2022-02-17 16:27")
    private Date createTime;

    @ApiModelProperty(value = "Update user.", example = "user1")
    private String updateUser;

    @ApiModelProperty(value = "Update time.", example = "2022-02-17 16:27")
    private Date updateTime;

    @ApiModelProperty(value = "Parent outlet.", example = "user1")
    private String parentOutlet;

    @ApiModelProperty(value = "Parent outlet name.", example = "user1")
    private String parentOutletName;

	@ApiModelProperty(value = "Parent outlet name.", example = "user1")
	private List<OutletResponse> childList;
}
