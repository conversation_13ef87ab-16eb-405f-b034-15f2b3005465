package com.gtech.gvcore.common.request.distribution;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

/**
 * @ClassName QueryCustomerCpgInventoryDetailRequest
 * @Description 查询客户cpg库存明细接口
 * <AUTHOR>
 * @Date 2022/9/7 16:19
 * @Version V1.0
 **/
@Setter
@Getter
@Accessors(chain = true)
@ApiModel(description = "QueryCustomerCpgInventoryDetailRequest")
public class QueryCustomerCpgInventoryDetailRequest {

    @ApiModelProperty(value = "cpgCode", required = true)
    @NotEmpty(message = "cpgCode can not be empty")
    private String cpgCode;

    @ApiModelProperty(value = "Customer code for check info", required = true)
    @NotBlank(message = "Customer code cannot be empty")
    private String customerCode;

}
