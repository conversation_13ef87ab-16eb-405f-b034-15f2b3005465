package com.gtech.gvcore.common.request.exchangerate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> Gao.Yuhua
 * @date 2022-02-25
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CreateExchangeRateRequest")
public class UpdateExchangeRateRequest {


    /**
     * exchangeRateCode
     */
    @NotEmpty(message = "exchangeRateCode can not be empty")
    @Length(max = 50)
    @ApiModelProperty(value = "exchangeRateCode", example = "currencyCode")
    private String exchangeRateCode;


    /**
     * currency code
     */
    @NotEmpty(message = "currencyCode can not be empty")
    @Length(max = 200)
    @ApiModelProperty(value = "currencyCode", example = "currencyCode", required = true)
    private String currencyCode;

    /**
     * exchange rate
     */
    @NotNull(message = "exchangeRate can not be empty")
    @ApiModelProperty(value = "exchangeRate", example = "7.2345", required = true)
    private BigDecimal exchangeRate;

    /**
     * exchange currency code
     */
    @NotEmpty(message = "exchangeCurrencyCode can not be empty")
    @Length(max = 100)
    private String exchangeCurrencyCode;

    /**
     * exchange exchange_rate_date
     */
    @NotNull(message = "exchangeRateDate can not be empty")
    @ApiModelProperty(value = "exchangeRateDate", example = "2022-02-25 14:17:18", required = true)
    private Date exchangeRateDate;

    /**
     * createUser
     */
    @NotEmpty(message = "updateUser can not be empty")
    @Length(max = 50)
    @ApiModelProperty(value = "updateUser", example = "gv")
    private String updateUser;

}
