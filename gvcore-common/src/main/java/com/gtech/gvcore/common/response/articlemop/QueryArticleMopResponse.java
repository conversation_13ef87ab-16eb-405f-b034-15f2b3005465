package com.gtech.gvcore.common.response.articlemop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询商品支付方式关联响应
 */
@Data
@ApiModel(value = "QueryArticleMopResponse", description = "查询商品支付方式关联响应")
public class QueryArticleMopResponse {
    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String articleCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String articleCodeName;
    
    /**
     * SAP商品编码(GTIN)
     */
    @ApiModelProperty(value = "SAP商品编码(GTIN)")
    private String sapArticleCode;

    /**
     * 支付方式编码
     */
    @ApiModelProperty(value = "支付方式编码")
    private String mopCode;

    /**
     * 支付方式名称
     */
    @ApiModelProperty(value = "支付方式名称")
    private String mopName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private String createTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private String updateTime;
} 