package com.gtech.gvcore.common.response.customer;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CustomerResponse")
public class CustomerResponse {

    @ApiModelProperty(value = "Customer code.", example = "********")
    private String customerCode;

    @ApiModelProperty(value = "Issuer code.", example = "123124")
    private String issuerCode;

    @ApiModelProperty(value = "Outlet code.", example = "123124")
    private String outletCode;

    @ApiModelProperty(value = "Customer name.", example = "user1")
    private String customerName;

    @ApiModelProperty(value = "Customer type.", example = "1")
    private String customerType;

    @ApiModelProperty(value = "Company name.", example = "test")
    private String companyName;

    @ApiModelProperty(value = "Contact first name.", example = "user1")
    private String contactFirstName;

    @ApiModelProperty(value = "Contact first name.", example = "user2")
    private String contactLastName;

    @ApiModelProperty(value = "Contact division.", example = "user1")
    private String contactDivision;

    @ApiModelProperty(value = "Contact phone.", example = "********")
    private String contactPhone;

    @ApiModelProperty(value = "Contact email.", example = "<EMAIL>")
    private String contactEmail;

    @ApiModelProperty(value = "Shipping address1.", example = "2nd Floor NO.23")
    private String shippingAddress1;

    @ApiModelProperty(value = "Shipping address2.", example = "2nd Floor NO.23")
    private String shippingAddress2;

    @ApiModelProperty(value = "Transfer account.", example = "**********")
    private String transferAccount;

    @ApiModelProperty(value = "Band card issuer.", example = " ********")
    private String bankCardIssuer;

    @ApiModelProperty(value = "Note.", example = "note")
    private String note;

    @ApiModelProperty(value = "Status.", example = "1")
    private Integer status;

    @ApiModelProperty(value = "productCategoryList.")
    private List<ProductCategory> productCategoryList;

    @ApiModelProperty(value = "Mop group", example = "1")
    private String mopGroup;

    @ApiModelProperty(value = "Customer payment method code.", example = "********")
    private String customerPaymentMethodCode;

    @ApiModelProperty(value = "Create user.", example = "user1")
    private String createUser;

    @ApiModelProperty(value = "Update time.", example = "2022-02-17 16:27")
    private Date updateTime;

    @ApiModelProperty( value="Update user.", example="user1")
    private String updateUser;

    @ApiModelProperty(value = "Create time.", example = "2022-02-17 16:27")
    private Date createTime;

    @ApiModelProperty(value = "Distribution Function.", example = "0")
    private String distributionFunction;

    @ApiModelProperty(value = "User email",example = "<EMAIL>;<EMAIL>")
    private String userEmail;

    @ApiModelProperty(value = "channel",example = "")
    private String channel;

    @ApiModelProperty(value = "Registration Year",example = "2021")
    private String registrationYear;


    @ApiModelProperty(value = "Beneficiary Name", example = "")
    private String beneficiaryName;

    @ApiModelProperty(value = "Branch Name", example = "")
    private String branchName;

    @ApiModelProperty(value = "Bank Name", example = "")
    private String bankName;

    @ApiModelProperty(value = "Account Number", example = "")
    private String accountNumber;

    @ApiModelProperty(value = "pph(0/1)", example = "0")
    private Integer pph;


    @ApiModelProperty(value = "companyAndBranchName", example = "Company Name (Branch Name)")
    private String companyAndBranchName;


}
