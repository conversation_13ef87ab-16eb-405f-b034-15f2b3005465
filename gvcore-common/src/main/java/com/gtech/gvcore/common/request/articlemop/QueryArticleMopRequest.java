package com.gtech.gvcore.common.request.articlemop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;

/**
 * 查询商品支付方式关联请求
 */
@Data
@ApiModel(value = "QueryArticleMopRequest", description = "查询商品支付方式关联请求")
public class QueryArticleMopRequest {
    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    @Size(max = 50)
    private String articleCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    @Size(max = 100)
    private String articleCodeName;
    
    /**
     * SAP商品编码(GTIN)
     */
    @ApiModelProperty(value = "SAP商品编码(GTIN)")
    @Size(max = 50)
    private String sapArticleCode;

    /**
     * 支付方式编码
     */
    @ApiModelProperty(value = "支付方式编码")
    @Size(max = 50)
    private String mopCode;

    /**
     * 支付方式名称
     */
    @ApiModelProperty(value = "支付方式名称")
    @Size(max = 100)
    private String mopName;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码", required = true)
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @ApiModelProperty(value = "每页大小", required = true)
    private Integer pageSize = 10;
} 