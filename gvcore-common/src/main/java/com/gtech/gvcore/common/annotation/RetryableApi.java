package com.gtech.gvcore.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation for enabling retry mechanism on an API method.
 * If a request with a 'retryKey' is sent to a method annotated with @RetryableApi,
 * the response will be cached. Subsequent requests with the same 'retryKey'
 * will receive the cached response directly.
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RetryableApi {

    /**
     *
     * @return true if the retry mechanism is enabled, false otherwise
     */
    boolean enable() default true;

    /**
     * Cache expiration time in seconds.
     * Defaults to 24 hours.
     * @return cache expiration time in seconds
     */
    long expireSeconds() default 24 * 60 * 60;
} 