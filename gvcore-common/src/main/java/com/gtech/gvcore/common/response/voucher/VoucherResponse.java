package com.gtech.gvcore.common.response.voucher;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/3/9 16:36
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "VoucherResponse")
public class VoucherResponse implements Serializable {
    private static final long serialVersionUID = -5886851354660654281L;


    private String issuerCode;

    private String voucherBatchCode;

    private String bookletCode;

    private Long bookletCodeNum;

    private String voucherCode;

    private Long voucherCodeNum;

    private String cpgCode;

    private String cpgName;

    private String mopCode;

    private BigDecimal denomination;

    private String voucherPin;

    private String voucherBarcode;

    private Date voucherEffectiveDate;

    private Integer status;

    private Integer voucherStatus;

    private Integer circulationStatus;

    private String voucherActiveCode;

    private String voucherActiveUrl;

    private Date voucherUsedTime;

    private String voucherOwnerCode;

    private String voucherOwnerType;

    private String permissionCode;

    private Date createTime;

    private Date updateTime;

    private String createUser;

    private String updateUser;




}
