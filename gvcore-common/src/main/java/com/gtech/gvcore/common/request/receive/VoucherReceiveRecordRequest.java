package com.gtech.gvcore.common.request.receive;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class VoucherReceiveRecordRequest {

    /**
     * voucher receive code
     */
	@ApiModelProperty(value = "Voucher receive code")
    private String voucherReceiveCode;

    /**
     * cpg code 
     */
	@ApiModelProperty(value = "Cpg code")
    private String cpgCode;

    /**
     * voucher start NO
     */
	@ApiModelProperty(value = "Voucher start NO")
    private String voucherStartNo;

    /**
     * voucher end NO
     */
	@ApiModelProperty(value = "Voucher end NO")
    private String voucherEndNo;

    /**
     * denomination
     */
	@ApiModelProperty(value = "Denomination")
    private BigDecimal denomination;

    /**
     * received number of vouchers
     */
	@ApiModelProperty(value = "Received number of vouchers")
    private Integer receivedNum;

    /**
     * booklet start NO
     */
	@ApiModelProperty(value = "Booklet start NO")
    private String bookletStartNo;

    /**
     * booklet end NO
     */
	@ApiModelProperty(value = "Booklet end NO")
    private String bookletEndNo;

	/**
	 * create user
	 */
	@ApiModelProperty(value = "Create user")
	private String createUser;
}