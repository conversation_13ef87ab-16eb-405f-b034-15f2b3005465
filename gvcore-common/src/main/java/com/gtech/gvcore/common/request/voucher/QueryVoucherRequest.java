package com.gtech.gvcore.common.request.voucher;

import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Query Voucher Request
 * 查询礼品卡请求
 * <AUTHOR>
 * @Date 2022/3/9 16:38
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryVoucherRequest", description = "Query Voucher Request")
public class QueryVoucherRequest extends PageBean implements Serializable {
    private static final long serialVersionUID = -5292213000441842200L;

    @ApiModelProperty(value = "Voucher Batch Code", notes = "Batch code for the vouchers", example = "BATCH202301")
    private String voucherBatchCode;


}
