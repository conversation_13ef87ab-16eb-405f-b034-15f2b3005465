package com.gtech.gvcore.common.request.distribution;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;

/**
 * @ClassName ResendVoucherEmailRequest
 * @Description 重新发送卡券邮件
 * <AUTHOR>
 * @Date 2022/9/6 19:04
 * @Version V1.0
 **/
@Setter
@Getter
@Accessors(chain = true)
@ApiModel(description = "ResendVoucherEmailRequest")
public class ResendVoucherEmailRequest {

    @ApiModelProperty(value = "distribution item code", example = "distribution item code",required = true)
    @NotEmpty(message = "distribution item code")
    private String distributionItemCode;

    @ApiModelProperty(value = "voucher code", example = "voucher code",required = true)
    @NotEmpty(message = "voucher code")
    private String voucherCode;

}
