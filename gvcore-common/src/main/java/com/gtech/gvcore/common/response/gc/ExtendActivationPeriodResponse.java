package com.gtech.gvcore.common.response.gc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

@Data
@ApiModel(value = "ExtendActivationPeriodResponse", description = "Extend Activation Period Response")
public class ExtendActivationPeriodResponse extends BaseApiResponse {


    @ApiModelProperty(value = "Gift Card Number", notes = "16 digit gift card number", example = "1234567890123456", position = 3)
    private String giftCardNumber;

    @ApiModelProperty(value = "New Activation Period", notes = "New activation expiration date", example = "2024-12-31T23:59:59", position = 4)
    private Date newActivationPeriod;

}
