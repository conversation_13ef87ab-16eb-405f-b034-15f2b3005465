package com.gtech.gvcore.common.request.customerorder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * @ClassName ResendEmailRequest
 * @Description 重新发送邮件请求入参
 * <AUTHOR>
 * @Date 2022/8/10 15:56
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "add send email request.")
public class AddSendEmailRequest {

    @NotBlank(message = "Customer order code is required")
    @ApiModelProperty(value = "Customer order code", required = true, example = "CO1231412415251")
    private String customerOrderCode;

    @NotBlank(message = "email address is required")
    @ApiModelProperty(value = "email address", required = true, example = "<EMAIL>")
    private String emailAddress;

    @ApiModelProperty(value = "login user code", example = "UC0001", required = true)
    @NotBlank(message = "UserCode is required")
    private String userCode;

}
