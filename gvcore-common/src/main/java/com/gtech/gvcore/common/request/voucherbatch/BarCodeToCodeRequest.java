package com.gtech.gvcore.common.request.voucherbatch;

import com.gtech.gvcore.common.response.customerorder.GetCustomerOrderDetailsResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "GenerateElectronicVouchersRequest")
public class BarCodeToCodeRequest {

    @ApiModelProperty("barCode")
    private String barCode;

    @ApiModelProperty("0-VoucherBarcode 1-BookletBarCode")
    private String type;


}
