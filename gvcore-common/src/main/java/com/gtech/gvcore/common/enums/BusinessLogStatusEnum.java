package com.gtech.gvcore.common.enums;

public enum BusinessLogStatusEnum implements IEnum<Integer>{
	SUCCESS(0, "success"),
	FAILED(1, "failed"),
    ;



    private final Integer code;

    private final String desc;

    BusinessLogStatusEnum(Integer code, String desc) {

        this.code = code;
        this.desc = desc;
    }

	@Override
	public Integer code() {
		return this.code;
	}

	@Override
	public String desc() {
		return this.desc;
	}

	@Override
	public boolean equalsCode(Integer code) {
		return this.code.equals(code);
	}

}
