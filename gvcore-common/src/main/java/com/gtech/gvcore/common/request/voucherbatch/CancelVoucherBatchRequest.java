package com.gtech.gvcore.common.request.voucherbatch;

import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/3/7 10:56
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CancelVoucherBatchRequest")
public class CancelVoucherBatchRequest implements Serializable {
    private static final long serialVersionUID = -6714969100311337855L;


    @ApiModelProperty(value = "Voucher batch code.", example = "1122333",required = true)
    @NotEmpty(message = "voucherBatchCode can not be empty")
    private String voucherBatchCode;

    @ApiModelProperty(value = "Create user.", example = "1122333",required = true)
    @NotEmpty(message = "createUser can not be empty")
    private String createUser;


}
