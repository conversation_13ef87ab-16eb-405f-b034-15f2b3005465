package com.gtech.gvcore.common.request.gcapi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Extend Activation Period Request
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "ExtendActivationPeriodRequest", description = "Extend Activation Period Request")
public class ExtendActivationPeriodRequest {

    @ApiModelProperty(value = "Gift Card Number", notes = "16 digit gift card number", required = true, example = "1234567890123456", position = 2)
    private String giftCardNumber;

    @ApiModelProperty(value = "Notes", notes = "Additional notes for the extension operation", required = false, example = "Extension requested by customer", position = 3)
    private String notes;

}