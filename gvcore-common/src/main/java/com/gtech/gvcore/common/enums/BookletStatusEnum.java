package com.gtech.gvcore.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2022/7/26 10:10
 */
public enum BookletStatusEnum {


    GENERATING(0, "GENERATING"),
    GENERATED(1, "CREATED"),
    PARTIALLY_ACTIVATED(2, "PARTIALLY ACTIVATED");

    private Integer code;
    private String desc;

    BookletStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static BookletStatusEnum getStatus(Integer code) {

        return getStatus(String.valueOf(code));
    }

    public static BookletStatusEnum getStatus(String code) {

        if (StringUtils.isEmpty(code)) return null;

        BookletStatusEnum[] values = BookletStatusEnum.values();
        for (BookletStatusEnum value : values) {
            if (String.valueOf(value.code).equals(code)) {
                return value;
            }
        }

        return null;

    }

    public static String getDescByCode(Integer code) {

        return getDescByCode(String.valueOf(code));
    }

    public static String getDescByCode(String code) {

        BookletStatusEnum status = getStatus(code);

        if (null == status) return StringUtils.EMPTY;

        return status.getDesc();

    }


}
