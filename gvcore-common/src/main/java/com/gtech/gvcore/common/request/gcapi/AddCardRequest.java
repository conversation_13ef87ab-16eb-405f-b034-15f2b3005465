package com.gtech.gvcore.common.request.gcapi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "AddCardRequest", description = "Add Gift Card Request")
public class AddCardRequest {

    @ApiModelProperty(value = "Gift Card Number", notes = "16 digit gift card number", required = true, example = "1234567890123456")
    private String giftCardNumber;

    @ApiModelProperty(value = "Gift Card PIN", notes = "6 digit numeric PIN", required = true, example = "123456")
    private String giftCardPIN;

    @ApiModelProperty(value = "Gift Card Expiry", notes = "Gift card activation expiry date", required = true)
    private Date giftCardExpiry;

    @ApiModelProperty(value = "Activation Code", notes = "Activation code")
    private String activationCode;

    @ApiModelProperty(value = "Denomination", notes = "Denomination when first time purchased gift card")
    private Double denomination;

    @ApiModelProperty(value = "Customer Info", notes = "Customer information", required = true)
    private CustomerInfo customer;

    @ApiModelProperty(value = "Token", notes = "Authentication token", example = "Bearer token", hidden = true)
    private String token;

    @ApiModelProperty(value = "Transaction ID", notes = "Unique transaction ID", example = "1234", hidden = true)
    private Integer transactionId;

    @ApiModelProperty(value = "Client Time", notes = "Client application timestamp in YYYY-MM-DDTHH:MM:SS format", example = "2023-01-01T12:00:00", hidden = true)
    private String clientTime;


}
