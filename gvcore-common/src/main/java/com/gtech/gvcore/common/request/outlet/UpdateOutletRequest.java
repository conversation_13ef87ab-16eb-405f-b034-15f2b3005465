package com.gtech.gvcore.common.request.outlet;

import java.util.List;

import javax.validation.constraints.NotEmpty;

import org.hibernate.validator.constraints.Length;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.exception.ErrorCodes;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CreateOutletRequest")
public class UpdateOutletRequest {

    @ApiModelProperty(value = "Outlet code.", example = "1231245125",required = true)
    @NotEmpty(message = "outletCode can not be empty")
    @Length(max = 100)
    private String outletCode;

    @ApiModelProperty(value = "Outlet name.", example = "MAP")
    @Length(max = 100)
    private String outletName;

    @ApiModelProperty(value = "Merchant code.", example = "1231254125")
    @Length(max = 100)
    private String merchantCode;

    @ApiModelProperty(value = "Sbu",example = "independet")
    private String sbu;

    @ApiModelProperty(value = "District code.", example = "551122", required = true)
    @Length(max = 100)
    private String districtCode;

    @ApiModelProperty(value = "Address1.", example = "2nd Floor NO.23")
    @Length(max = 512)
    private String address1;

    @ApiModelProperty(value = "Address2.", example = "2nd Floor NO.23")
    @Length(max = 512)
    private String address2;

    @ApiModelProperty(value = "Outlet type.", example = "independet")
    @Length(max = 100)
    private String outletType;

    @ApiModelProperty(value = "State code.", example = "112233", required = true)
    @Length(max = 100)
    private String stateCode;

    @ApiModelProperty(value = "City code.", example = "223344", required = true)
    @Length(max = 100)
    private String cityCode;

    @ApiModelProperty(value = "Cpg code.", example = "2123123")
    private List<String> cpgCode;

    @ApiModelProperty(value = "Business outlet code.", example = "1234124")
    @Length(max = 100)
    private String businessOutletCode;

    @ApiModelProperty(value = "Pin code.", example = "0")
    @Length(max = 100)
    private String pinCode;

    @ApiModelProperty(value = "Email.", example = "<EMAIL>")
    private String email;

    @ApiModelProperty(value = "Phone.", example = "1234125412")
    @Length(max = 100)
    private String phone;

    @ApiModelProperty(value = "Mobile.", example = "1231245215")
    @Length(max = 100)
    private String mobile;

    @ApiModelProperty(value = "First name.", example = "name1")
    @Length(max = 100)
    private String firstName;

    @ApiModelProperty(value = "Last name.", example = "name2")
    @Length(max = 100)
    private String lastName;

    @ApiModelProperty(value = "Alertnate email.", example = "<EMAIL>")
    private String alertnateEmail;

    @ApiModelProperty(value = "Descriptive.", example = "text")
    @Length(max = 100)
    private String descriptive;

    @ApiModelProperty(value = "Status.", example = "0")
    private Integer status;

    @ApiModelProperty(value = "Alertnate phone.", example = "45765765765")
    @Length(max = 100)
    private String alertnatePhone;

    @ApiModelProperty(value = "Update user.", example = "user1")
    @Length(max = 100)
    private String updateUser;

    @ApiModelProperty(value = "Parent outlet.", example = "user1")
    @Length(max = 100)
    private String parentOutlet;

    public void validation(){

        if (StringUtil.isNotEmpty(phone)) {
            CheckUtils.isTrue(CheckUtils.isPhone(phone), ErrorCodes.PARAM_FORMAT_ERROR, "phone");
        }
        if (StringUtil.isNotEmpty(alertnatePhone)) {
            CheckUtils.isTrue(CheckUtils.isPhone(alertnatePhone), ErrorCodes.PARAM_FORMAT_ERROR, "alertnatePhone");
        }

    }

}
