package com.gtech.gvcore.common.response.flow;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class FlowResponse {

    /**
     * issuer code
     */
    @ApiModelProperty(value = "issuer code")
    private String issuerCode;

    /**
     * flow code
     */
    @ApiModelProperty(value = "flow code")
    private String flowCode;

    /**
     * flow name
     */
    @ApiModelProperty(value = "flow name")
    private String flowName;

    /**
     * remark
     */
    private String remark;

    /**
     * status
     */
    private Integer status;

    /**
     * create user
     */
    @ApiModelProperty(value = "create user")
    private String createUser;
    
    private List<FlowNodeResponse> flowNodeList;

}