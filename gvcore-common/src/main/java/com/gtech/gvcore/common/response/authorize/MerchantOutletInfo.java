package com.gtech.gvcore.common.response.authorize;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class MerchantOutletInfo {

	@ApiModelProperty(value = "Merchant Outlet Name.", required = true, notes = "The outletname/storename to which this POS is assigned to in the server")
	private String storeName;
	@ApiModelProperty(value = "Merchant Outlet Address1.", notes = "The address1 of the outlet if available.")
	private String storeAddress1;
	@ApiModelProperty(value = "Merchant Outlet Address2.", notes = "The address2 of the outlet if available.")
	private String storeAddress2;
	@ApiModelProperty(value = "Merchant Outlet City.", notes = "City in which outlet belongs to.")
	private String storeCity;
	@ApiModelProperty(value = "Merchant Outlet State.", notes = "State in which outlet belongs to.")
	private String storeState;
	@ApiModelProperty(value = "Merchant Outlet PinCode.", notes = "Pincode of city.")
	private String storeZipCode;

}