package com.gtech.gvcore.common.request.voucherrequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/8 11点08分
 */
@Data
@ApiModel(value = "CreateVoucherRequestRequest")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CreateVoucherRequestRequest implements Serializable {
    private static final long serialVersionUID = -6107328391438772591L;
    private String businessType;
    @NotBlank(message = "voucherOwnerCode cannot be empty")
    @ApiModelProperty(value = "voucherOwnerCode from outlet code.", example = "OU12131231241", required = true)
    private String voucherOwnerCode;
    @NotBlank(message = "voucherOwnerName cannot be empty")
    @ApiModelProperty(value = "voucherOwnerName from outlet name", example = "HO01", required = true)
    private String voucherOwnerName;
    @NotBlank(message = "issuerCode cannot be empty")
    @ApiModelProperty(value = "Issuer code", example = "MAP", required = true)
    private String issuerCode;
    @NotBlank(message = "receiverCode cannot be empty")
    @ApiModelProperty(value = "receiverCode from Request Source", example = "OU102203071410000010", required = true)
    private String receiverCode;
    @NotBlank(message = "receiverName cannot be empty")
    @ApiModelProperty(value = "receiverName from Request Source", example = "MAP", required = true)
    private String receiverName;
    @ApiModelProperty(value = "stateCode")
    private String stateCode;
    @ApiModelProperty(value = "cityCode")
    private String cityCode;
    @ApiModelProperty(value = "districtCode")
    private String districtCode;
    @ApiModelProperty(value = "address1")
    private String address1;
    @ApiModelProperty(value = "email")
    private String email;
    @ApiModelProperty(value = "phone")
    private String phone;
    @ApiModelProperty(value = "mobile")
    private String mobile;
    @ApiModelProperty(value = "requestRemarks")
    private String requestRemarks;
    @NotNull(message = "Please enter the quantity")
    @ApiModelProperty(value = "Number of Vouchers Total", example = "4000")
    private Integer voucherNum;
    @NotNull(message = "Please enter the quantity")
    @ApiModelProperty(value = "Voucher Amount Total ", example = "1200000000")
    private BigDecimal voucherAmount;
    @ApiModelProperty(value = "Currency code")
    private String currencyCode;
    @ApiModelProperty(value = "Permission code.", example = "1122333")
    private String permissionCode;
    @NotBlank(message = "The creator cannot do without")
    @ApiModelProperty(value = "Create user")
    private String createUser;
    @Size(min = 1, message = "Pass at least one piece of data")
    @ApiModelProperty(value = "Create voucher request details requests", required = true)
    private List<CreateVoucherRequestDetailsRequest> detailsRequests;


}
