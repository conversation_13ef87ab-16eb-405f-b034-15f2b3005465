package com.gtech.gvcore.web.contoreller;


import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@ConditionalOnProperty(name = "gvcore.report", havingValue = "report")
@RocketMQMessageListener(topic = "report", consumerGroup = "titan_gvcore")
public class MqCustomer  implements RocketMQListener<String> {


    @Override
    public void onMessage(String message) {
        log.info("接收到report消息:{}",message);
    }
}
