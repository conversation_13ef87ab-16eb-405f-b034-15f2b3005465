package com.gtech.gvcore.web.scheduled;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.request.customerorder.SapSalesPostingXmlRequest;
import com.gtech.gvcore.common.request.postingxml.QueryPostingxmlRequest;
import com.gtech.gvcore.web.external.PostingXmlController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
@EnableScheduling
@Component
public class ScheduledTask {


    @Value("${xml.init:true}")
    private Boolean xmlInit;

    private static final Logger log = LoggerFactory.getLogger(ScheduledTask.class);

    private StringRedisTemplate redisTemplate;

    @Autowired
    public void setRedisTemplate(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    private PostingXmlController postingXmlService;

    @Autowired
    public void setPostingXmlService(PostingXmlController postingXmlService) {
        this.postingXmlService = postingXmlService;
    }

    private ScheduledExecutorService executor;

    public void setScheduledExecutorService(ScheduledExecutorService scheduledExecutorService) {
        this.executor = scheduledExecutorService;
    }

    /*@PostConstruct
    public void initScheduledTask() {
        log.info("Initializing scheduled task...");
        scheduleTaskAtSpecificTime();
    }*/

    private Boolean getLock(){
        String lockKey = "scheduled-task-lock:"+DateUtil.format(new Date(),DateUtil.FORMAT_DEFAULT);
        String lockValue = "lock-value";
        return Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(lockKey, lockValue, 7, TimeUnit.DAYS));
    }

    public void scheduleTaskAtSpecificTime() {
        executor = Executors.newSingleThreadScheduledExecutor();

        // 计算下次执行时间
        Date nextExecutionTime = getNextExecutionTime(LocalDateTime.now());
        log.info("Next execution time: {}", DateUtil.format(nextExecutionTime, DateUtil.FORMAT_YYYYMMDDHHMISS));

        // 初次执行任务
        long initialDelay = nextExecutionTime.getTime() - System.currentTimeMillis();
        if (initialDelay < 0) {
            initialDelay = 0;
        }

        if (xmlInit) executor.schedule(this::executeTask, initialDelay, TimeUnit.MILLISECONDS);

        // 设置后续每日执行任务
        executor.scheduleAtFixedRate(this::executeTask, 24 * 60 * 60 * 1000, 24 * 60 * 60 * 1000, TimeUnit.MILLISECONDS);

    }

    public Date getNextExecutionTime(LocalDateTime now) {
        // 获取第二天凌晨 1 点的时间
        LocalDateTime nextExecutionTime = now.plusDays(1).with(LocalTime.of(1, 0, 0));

        return Date.from(nextExecutionTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    @Scheduled(cron = "${xml.cron:0 15 0 * * ?}")//每天凌晨0点15分执行
    public void executeTask() {


        try {
            if (getLock()) {
                try {
                    log.info("Executing scheduled task at {}", new Date());
                    doWork(getPreviousDayEndTime());
                } catch (Exception e) {
                    log.error("Error executing task", e);
                } /*finally {
                    // 设置后续每日执行任务
                    executor.scheduleAtFixedRate(this::executeTask, 24 * 60 * 60 * 1000, 24 * 60 * 60 * 1000, TimeUnit.MILLISECONDS);
                }*/
            } else {
                log.info("Lock not acquired, skipping task execution.");
            }
        } catch (Exception e) {
            log.error("Error while trying to acquire lock", e);
        }
    }

    public Date getPreviousDayEndTime() {
        // 获取前一天的 23:59:59 时间
        LocalDateTime endOfDay = LocalDateTime.now().minusDays(1).with(LocalTime.MAX);

        return Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    private void doWork(Date queryTime) {
        log.info("Executing scheduled task with query time: {}", DateUtil.format(queryTime, DateUtil.FORMAT_YYYYMMDDHHMISS));

        // 使用前一天 23:59:59 的时间作为查询时间
        log.info("Executing scheduled task customerOrder.");
        SapSalesPostingXmlRequest salesRequest = new SapSalesPostingXmlRequest();
        salesRequest.setQueryDate(queryTime);
        postingXmlService.customerOrderXml(salesRequest);

        log.info("Executing scheduled task MV01");
        QueryPostingxmlRequest mv01 = new QueryPostingxmlRequest();
        mv01.setQueryTime(DateUtil.format(queryTime, DateUtil.FORMAT_YYYYMMDDHHMISS));
        postingXmlService.generatePosting(mv01);

        log.info("Executing scheduled task MV03");
        SapSalesPostingXmlRequest mv03 = new SapSalesPostingXmlRequest();
        mv03.setQueryDate(queryTime);
        postingXmlService.mv03Xml(mv03);

        log.info("Executing scheduled task MV04");
        SapSalesPostingXmlRequest mv04 = new SapSalesPostingXmlRequest();
        mv04.setQueryDate(queryTime);
        postingXmlService.mv04Xml(mv04);
    }

    @PreDestroy
    public void shutdown() {
        if (executor != null) {
            try {
                executor.shutdown();
                if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
            }
        }
    }
}


