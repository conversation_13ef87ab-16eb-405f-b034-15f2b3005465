package com.gtech.gvcore.web.external;

import com.gtech.gvcore.common.externalmapping.request.*;
import com.gtech.gvcore.common.request.transaction.BulkCancelRedeemRequestV2;
import com.gtech.gvcore.common.response.cancelredeem.APIBulkCancelRedeemResponse;
import com.gtech.gvcore.common.response.transaction.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.gtech.gvcore.common.annotation.NotSysLogger;
import com.gtech.gvcore.common.externalmapping.request.mapgiftvoucher.AuthV3Request;
import com.gtech.gvcore.common.externalmapping.request.mapgiftvoucher.VoucherTransactionV3Request;
import com.gtech.gvcore.common.externalmapping.response.ActivityOnlyResponse;
import com.gtech.gvcore.common.externalmapping.response.AuthResponse;
import com.gtech.gvcore.common.externalmapping.response.AuthV3Response;
import com.gtech.gvcore.common.externalmapping.response.BatchCloseResponse;
import com.gtech.gvcore.common.externalmapping.response.CancelActivityResponse;
import com.gtech.gvcore.common.externalmapping.response.CancelCreateAndIssueResponse;
import com.gtech.gvcore.common.externalmapping.response.CancelRedeemResponse;
import com.gtech.gvcore.common.externalmapping.response.CreateAndIssueV3Response;
import com.gtech.gvcore.common.externalmapping.response.OneTimeBarCodeResponse;
import com.gtech.gvcore.common.externalmapping.response.VoucherCreationAndIssuanceResponse;
import com.gtech.gvcore.common.externalmapping.response.VoucherTransactionHistoryResponse;
import com.gtech.gvcore.common.externalmapping.response.VoucherTransactionResponse;
import com.gtech.gvcore.common.response.authorize.AuthorizeResponse;
import com.gtech.gvcore.common.response.authorize.AuthorizeResponseV3;
import com.gtech.gvcore.common.response.v3.CreateAndIssueResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping(value = "/gv/external")
@Api(value = "External API.", tags = { "External API" })
public class RequestMappingController {



    @Autowired
    private GvPosController gvPosController;

    @Autowired
    private MapGiftVoucherController mapGiftVoucherController;



    @ApiOperation(value = "Authorize")
    @PostMapping(value = "/authenticate")
	@NotSysLogger(disableLogger = false)
    public ResponseEntity<AuthResponse> auth(@RequestBody AuthRequest request) {
        ResponseEntity<AuthorizeResponse> authorize = gvPosController.authorize(request.toMap());
        AuthResponse authResponse = new AuthResponse();
        return ResponseEntity.status(authorize.getStatusCodeValue()).body(authResponse.setAuthResponse(authorize.getBody()));
    }

    @ApiOperation(value = "Transaction")
    @PostMapping(value = "/bulkprocess")
	@NotSysLogger(disableLogger = false)
    public ResponseEntity<VoucherTransactionResponse> voucherTransaction(@RequestBody VoucherTransactionRequest request,
                                                                         @RequestHeader("Authorization") String authHeader) {
        ResponseEntity<TransactionResponse> transaction = gvPosController.transaction(request.toMap(), authHeader);
        VoucherTransactionResponse voucherTransactionResponse = new VoucherTransactionResponse();
        return ResponseEntity.status(transaction.getStatusCodeValue()).body(voucherTransactionResponse.setVoucherTransactionResponse(transaction.getBody()));
    }


    @ApiOperation(value = "Cancelredeem")
    @PostMapping(value = "/cancelredeem")
	@NotSysLogger(disableLogger = false)
    public ResponseEntity<CancelRedeemResponse> cancelRedeem(@RequestBody CancelRedeemRequest request,
                                                             @RequestHeader("Authorization") String authHeader) {
        ResponseEntity<CancelredeemResponse> cancelredeem = gvPosController.cancelredeem(request.toMap(), authHeader);
        CancelRedeemResponse response = new CancelRedeemResponse();
        return ResponseEntity.status(cancelredeem.getStatusCodeValue()).body(response.setCancelRedeemResponse(cancelredeem.getBody()));
    }


    @ApiOperation(value = "bulkcancelredeem")
    @PostMapping(value = "/bulkcancelredeem")
    @NotSysLogger(disableLogger = false)
    public ResponseEntity<BulkCancelRedeemResponseV2> bulkCancelRedeem(@RequestBody BulkCancelRedeemRequestV2 request,
                                                                        @RequestHeader("Authorization") String authHeader) {
        ResponseEntity<BulkCancelRedeemResponseV2> cancelredeem = gvPosController.bulkcancelredeem(request, authHeader);
        if (cancelredeem.getBody() != null && cancelredeem.getBody().getResponseCode() != 0) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(cancelredeem.getBody());
        }

        return ResponseEntity.status(cancelredeem.getStatusCodeValue()).body(cancelredeem.getBody());
    }


    @ApiOperation(value = "Batchclose")
    @PostMapping(value = "/endsession")
	@NotSysLogger(disableLogger = false)
    public ResponseEntity<BatchCloseResponse> batchClose(@RequestBody BatchCloseRequest request,
                                                         @RequestHeader("Authorization") String authHeader) {
        //TODO 命名
        ResponseEntity<BatchcloseResponse> batchclose = gvPosController.batchclose(request.toMap(), authHeader);
        BatchCloseResponse response = new BatchCloseResponse();
        return ResponseEntity.status(batchclose.getStatusCodeValue()).body(response.setBatchCloseResponse(batchclose.getBody()));
    }


    @ApiOperation(value = "Createandissue")
    @PostMapping(value = "/gvissuance")
	@NotSysLogger(disableLogger = false)
    public ResponseEntity<VoucherCreationAndIssuanceResponse> voucherCreationAndIssuance(@RequestBody VoucherCreationAndIssuanceRequest request,
                                                                             @RequestHeader("Authorization") String authHeader) {
        ResponseEntity<CreateandissueResponse> createandissue = gvPosController.createandissue(request.toMap(), authHeader);
        VoucherCreationAndIssuanceResponse response = new VoucherCreationAndIssuanceResponse();
        return ResponseEntity.status(createandissue.getStatusCodeValue()).body(response.setCreateAndIssueResponse(createandissue.getBody()));
    }



    @ApiOperation(value = "CancelCreateandissue")
    @PostMapping(value = "/cancelissuance")
	@NotSysLogger(disableLogger = false)
    public ResponseEntity<CancelCreateAndIssueResponse> cancelVoucherCreationAndIssuance(@RequestBody CancelVoucherCreationAndIssuanceRequest request,
                                                                             @RequestHeader("Authorization") String authHeader) {
        ResponseEntity<CancelCreateandissueResponse> cancelcreateandissue = gvPosController.cancelcreateandissue(request.toMap(), authHeader);
        CancelCreateAndIssueResponse response = new CancelCreateAndIssueResponse();
        return ResponseEntity.status(cancelcreateandissue.getStatusCodeValue()).body(response.setCancelCreateAndIssueResponse(cancelcreateandissue.getBody()));
    }



    @ApiOperation(value = "Cancelactivate")
    @PostMapping(value = "cancelactivate")
	@NotSysLogger(disableLogger = false)
    public ResponseEntity<CancelActivityResponse> cancelActivate(@RequestBody CancelActivateRequest request,
                                                                 @RequestHeader("Authorization") String authHeader) {
        ResponseEntity<CancelactivateResponse> cancelactivate = gvPosController.cancelactivate(request.toMap(), authHeader);
        CancelActivityResponse response = new CancelActivityResponse();
        return ResponseEntity.status(cancelactivate.getStatusCodeValue()).body(response.setBaseOfResponse(cancelactivate.getBody(), response.getClass()));
    }


    @ApiOperation(value = "Activateonly")
    @PostMapping(value = "/egvactivate")
	@NotSysLogger(disableLogger = false)
    public ResponseEntity<ActivityOnlyResponse> voucherActivate(@RequestBody VoucherActivityRequest request,

                                                             @RequestHeader("Authorization") String authHeader) {
        ResponseEntity<ActivateonlyResponse> activateonly = gvPosController.activateonly(request.toMap(), authHeader);
        ActivityOnlyResponse response = new ActivityOnlyResponse();
        return ResponseEntity.status(activateonly.getStatusCodeValue()).body(response.setActivityOnlyResonse(activateonly.getBody()));
    }


    @ApiOperation(value = "Cardtransactionhistory")
    @PostMapping(value = "/transactionhistory")
	@NotSysLogger(disableLogger = false)
    public ResponseEntity<VoucherTransactionHistoryResponse> voucherTransactionHistory(@RequestBody VoucherTransactionHistoryRequest request,
                                                                                    @RequestHeader("Authorization") String authHeader) {
        ResponseEntity<CardtransactionhistoryResponse> cardtransactionhistory = gvPosController.cardtransactionhistory(request.toMap(), authHeader);
        VoucherTransactionHistoryResponse response = new VoucherTransactionHistoryResponse();
        return ResponseEntity.status(cardtransactionhistory.getStatusCodeValue()).body(response.setVoucherTransactionHistoryResponse(cardtransactionhistory.getBody()));
    }



    @ApiOperation(value = "Onetimebarcode")
    @PostMapping(value = "/dynamicbarcode")
	@NotSysLogger(disableLogger = false)
    public ResponseEntity<OneTimeBarCodeResponse> oneTimeBarcode(@RequestBody OneTimeBarCodeRequest request,
                                                                 @RequestHeader("Authorization") String authHeader) {
        ResponseEntity<OnetimebarcodeResponse> onetimebarcode = gvPosController.onetimebarcode(request.toMap(), authHeader);
        OneTimeBarCodeResponse response = new OneTimeBarCodeResponse();
        return ResponseEntity.status(onetimebarcode.getStatusCodeValue()).body(response.setOneTimeBarCodeResponse(onetimebarcode.getBody()));
    }

    @ApiOperation(value = "Authorize")
    @PostMapping(value = "/new/authenticate")
	@NotSysLogger(disableLogger = false)
    public ResponseEntity<AuthV3Response> auth(@RequestBody AuthV3Request param,
                                                         @RequestHeader("ClientTime") String clientTime ) {
        ResponseEntity<AuthorizeResponseV3> authorize = mapGiftVoucherController.authorize(param.toAuthorizeRequest(), clientTime);
        AuthV3Response authResponse = new AuthV3Response();
        return ResponseEntity.status(authorize.getStatusCodeValue()).body(authResponse.setAuthV3Response(authorize.getBody()));
    }


    @ApiOperation(value = "V3Transactions")
    @PostMapping(value = "/new/gvissuance")
	@NotSysLogger(disableLogger = false)
    public ResponseEntity<CreateAndIssueV3Response> voucherTransaction(@RequestBody @Validated VoucherTransactionV3Request param,
                                                               @RequestHeader("Authorization") String authHeader,
                                                               @RequestHeader("TransactionId") String transactionId,
                                                               @RequestHeader("clientTime") String clientTime,
                                                               @RequestHeader(value = "storeName" ,required = false) String storeName) {
        ResponseEntity<CreateAndIssueResponse> transactions = mapGiftVoucherController.transactions(param.toTransactionsRequest(), authHeader, transactionId);
        CreateAndIssueV3Response createAndIssueV3Response = new CreateAndIssueV3Response();
        return ResponseEntity.status(transactions.getStatusCodeValue()).body(createAndIssueV3Response.setCreateAndIssueV3Response(transactions.getBody()));
    }


}
