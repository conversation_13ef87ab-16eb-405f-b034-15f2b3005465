package com.gtech.gvcore.web.config;

import com.google.common.collect.Range;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.sharding.standard.RangeShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.RangeShardingValue;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
/**
 * <AUTHOR>
 * @Date 2023/2/8 17:10
 */
public class VoucherRangeShardingConfig implements RangeShardingAlgorithm<String> {

    private volatile Map<Long, String> indexToNameMap;

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames, RangeShardingValue<String> shardingValue) {

        if (availableTargetNames == null || availableTargetNames.isEmpty()) {
            return Collections.emptyList();
        }


        if (this.indexToNameMap == null) {
            synchronized (this) {
                if (this.indexToNameMap == null) {
                    this.indexToNameMap = buildIndexToNameMap(availableTargetNames);
                }
            }
        }

        Range<String> valueRange = shardingValue.getValueRange();
        String lowerEndpoint = valueRange.hasLowerBound() ? valueRange.lowerEndpoint() : null;
        String upperEndpoint = valueRange.hasUpperBound() ? valueRange.upperEndpoint() : null;

        if (lowerEndpoint == null || upperEndpoint == null) {
            return availableTargetNames;
        }
        long lowerVal = Long.parseLong(lowerEndpoint.replaceAll("[a-zA-Z]", ""));
        long upperVal = Long.parseLong(upperEndpoint.replaceAll("[a-zA-Z]", ""));

        long rangeSize = upperVal - lowerVal + 1 ;

        if (rangeSize > 5000) {
            log.error("Check query range! LogicTable: [{}], Range: [{} - {}], Size: {}. ",
                    shardingValue.getLogicTableName(), lowerEndpoint, upperEndpoint, rangeSize);
            throw new IllegalArgumentException("Query range is too large");
        }
        int shardingCount = availableTargetNames.size();

        if ((rangeSize) >= shardingCount) {
            return availableTargetNames;
        }

        Set<String> result = new LinkedHashSet<>();

        for (long i = lowerVal; i <= upperVal; i++) {
            long shardingIndex = i % shardingCount;
            String targetTable = this.indexToNameMap.get(shardingIndex);
            if (targetTable != null) {
                result.add(targetTable);
            }
        }
        return result;
    }



    private Map<Long, String> buildIndexToNameMap(Collection<String> availableTargetNames) {
        Map<Long, String> map = new ConcurrentHashMap<>(availableTargetNames.size());
        for (String name : availableTargetNames) {
            int lastUnderscoreIndex = name.lastIndexOf('_');
            if (lastUnderscoreIndex > 0 && lastUnderscoreIndex < name.length() - 1) {
                String indexStr = name.substring(lastUnderscoreIndex + 1);
                try {
                    long index = Long.parseLong(indexStr);
                    map.put(index, name);
                } catch (NumberFormatException e) {
                    log.warn("表名 [{}] 虽然包含下划线，但后缀 '{}' 不是一个有效的数字索引，将被忽略。", name, indexStr);
                }
            } else {
                // 表名中没有下划线，或者下划线在末尾
                log.warn("无法从表名 [{}] 中解析分片索引。期望的格式是 '前缀_数字'。", name);
            }
        }
        return map;
    }



}
