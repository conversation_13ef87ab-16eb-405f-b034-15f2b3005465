package com.gtech.gvcore.web.external;

import com.gtech.gvcore.monitor.JvmObjectMonitor;
import com.gtech.gvcore.service.impl.SystemLoggerServiceImpl;
import com.gtech.gvcore.service.report.base.ReportTaskRegisterServiceImpl;
import com.gtech.gvcore.service.report.impl.support.sales.CancelSalesDataRefresh;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/gv/external")
@Api(value = "Om API.", tags = { "Om API" })
public class OmController {


    @Autowired
    private CancelSalesDataRefresh cancelSalesDataRefresh;

    @Autowired
    private SystemLoggerServiceImpl systemLoggerService;

    @Autowired
    private ReportTaskRegisterServiceImpl reportTaskRegisterService;

    @PostMapping("initCancelSalesData")
    public void initCancelSalesData(){
        cancelSalesDataRefresh.init(null);
    }


    @GetMapping("/truncate")
    public void truncate(){
        systemLoggerService.truncate();
    }

    @GetMapping("/performTask")
    public void performTask(){
        reportTaskRegisterService.performTask();
    }

    /**
     * 每天凌晨2点执行一次垃圾回收并记录效果
     */

    @PostMapping("/gc")
    public void dailyGarbageCollection() {
        try {
            // 记录GC前的内存状态
            String beforeStats = JvmObjectMonitor.getSimpleObjectStatistics();
            log.info("=== 定时GC === GC前内存状态: {}", beforeStats);

            // 执行GC
            System.gc();

            // 等待GC完成
            Thread.sleep(2000);

            // 记录GC后的内存状态
            String afterStats = JvmObjectMonitor.getSimpleObjectStatistics();
            log.info("=== 定时GC === GC后内存状态: {}", afterStats);

            // 计算GC效果
            logGcEffect(beforeStats, afterStats);

        } catch (Exception e) {
            log.error("定时GC执行失败", e);
        }
    }


    /**
     * 分析并记录GC效果
     */
    private void logGcEffect(String beforeStats, String afterStats) {
        try {
            // 简单解析内存使用情况
            long beforeHeap = extractHeapUsage(beforeStats);
            long afterHeap = extractHeapUsage(afterStats);

            if (beforeHeap > 0 && afterHeap > 0) {
                long freedMemory = beforeHeap - afterHeap;
                double freedPercent = (double) freedMemory / beforeHeap * 100;

                if (freedMemory > 0) {
                    log.info("=== GC效果 === 释放内存: {}MB ({:.1f}%)", freedMemory, freedPercent);
                } else {
                    log.warn("=== GC效果 === 内存未减少，可能存在内存泄漏");
                }
            }
        } catch (Exception e) {
            log.debug("分析GC效果失败: {}", e.getMessage());
        }
    }



    /**
     * 从统计字符串中提取堆内存使用量
     */
    private long extractHeapUsage(String stats) {
        try {
            // 解析 "Heap:824/7282MB" 格式
            if (stats.contains("Heap:")) {
                String heapPart = stats.substring(stats.indexOf("Heap:") + 5);
                String usedPart = heapPart.substring(0, heapPart.indexOf("/"));
                return Long.parseLong(usedPart);
            }
        } catch (Exception e) {
            log.debug("解析堆内存使用量失败: {}", e.getMessage());
        }
        return 0;
    }
}
