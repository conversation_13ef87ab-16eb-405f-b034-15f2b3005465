package com.gtech.gvcore.web.external;

import java.util.Date;
import java.util.concurrent.CompletableFuture;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.util.StringUtil;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.request.customerorder.SapSalesPostingXmlRequest;
import com.gtech.gvcore.common.request.postingxml.QueryPostingxmlRequest;
import com.gtech.gvcore.external.PostingXmlService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping(value = "/gv/external/postingxml")
@Api(value = "Posting XML API.", tags = { "Posting XML API" })
public class PostingXmlController {

	@Autowired
	private PostingXmlService postingXmlService;

	@PostMapping(value = "/allXml")
	public void allXml(@RequestBody SapSalesPostingXmlRequest request){
		Date queryTime = request.getQueryDate();

		// 使用前一天 23:59:59 的时间作为查询时间
		log.info("Executing scheduled task customerOrder.");
		SapSalesPostingXmlRequest salesRequest = new SapSalesPostingXmlRequest();
		salesRequest.setQueryDate(queryTime);
		postingXmlService.customerOrderXml(salesRequest);

		log.info("Executing scheduled task MV01");
		QueryPostingxmlRequest mv01 = new QueryPostingxmlRequest();
		mv01.setQueryTime(DateUtil.format(queryTime, DateUtil.FORMAT_YYYYMMDDHHMISS));
		postingXmlService.generatePosting(mv01);

		log.info("Executing scheduled task MV03");
		SapSalesPostingXmlRequest mv03 = new SapSalesPostingXmlRequest();
		mv03.setQueryDate(queryTime);
		postingXmlService.mv03Xml(mv03);

		log.info("Executing scheduled task MV04");
		SapSalesPostingXmlRequest mv04 = new SapSalesPostingXmlRequest();
		mv04.setQueryDate(queryTime);
		postingXmlService.mv04Xml(mv04);


	}



	@PostMapping(value = "/generatePosting")
	public Result<Void> generatePosting(@RequestBody QueryPostingxmlRequest request) {
		if (StringUtil.isEmpty(request.getQueryTime())) {
			String date = DateUtil.format(DateUtil.addDay(new Date(), -1), DateUtil.FORMAT_DEFAULT);
			request.setQueryTime(date);
		}
		postingXmlService.generatePosting(request);
		return Result.ok();
	}

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年6月13日
     */
    @ApiOperation(value = "Customer order posting xml", notes = "Customer order posting xml")
    @PostMapping(value = "/customerOrderXml")
    public Result<Void> customerOrderXml(@RequestBody @Validated SapSalesPostingXmlRequest request) {

        log.info("customerOrderXml request={}", JSON.toJSONString(request));
        return postingXmlService.customerOrderXml(request);
    }






	@PostMapping(value = "/mv03Xml")
	public Result<Void> mv03Xml(@RequestBody @Validated SapSalesPostingXmlRequest request) {

		log.info("mv03Xml request={}", JSON.toJSONString(request));
		return postingXmlService.mv03Xml(request);
	}


	@PostMapping(value = "/mv04Xml")
	public Result<Void> mv04Xml(@RequestBody @Validated SapSalesPostingXmlRequest request) {

		log.info("mv04Xml request={}", JSON.toJSONString(request));
		return postingXmlService.mv04Xml(request);
	}





	
}
