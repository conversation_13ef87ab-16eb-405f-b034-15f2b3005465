package com.gtech.gvcore.web.controller;

import com.alibaba.fastjson.JSON;
import com.gtech.gvcore.common.request.authorize.AuthorizePayload;
import com.gtech.gvcore.common.request.transaction.ActivateonlyRequest;
import com.gtech.gvcore.common.request.transaction.BatchcloseRequest;
import com.gtech.gvcore.common.request.transaction.CancelredeemRequest;
import com.gtech.gvcore.common.request.transaction.CardtransactionhistoryRequest;
import com.gtech.gvcore.common.request.transaction.CreateandissueRequest;
import com.gtech.gvcore.common.request.transaction.OnetimebarcodeRequest;
import com.gtech.gvcore.common.request.transaction.TransactionRequest;
import com.gtech.gvcore.common.response.authorize.AuthorizeResponse;
import com.gtech.gvcore.common.response.transaction.ActivateonlyResponse;
import com.gtech.gvcore.common.response.transaction.BatchcloseResponse;
import com.gtech.gvcore.common.response.transaction.CancelredeemResponse;
import com.gtech.gvcore.common.response.transaction.CardtransactionhistoryResponse;
import com.gtech.gvcore.common.response.transaction.CreateandissueResponse;
import com.gtech.gvcore.common.response.transaction.OnetimebarcodeResponse;
import com.gtech.gvcore.common.response.transaction.TransactionResponse;
import com.gtech.gvcore.external.GvPosService;
import com.gtech.gvcore.web.external.GvPosController;
import org.apache.commons.lang3.ObjectUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class GvPosControllerTest {

	@InjectMocks
	private GvPosController gvPosController;

	@Mock
	private GvPosService gvPosService;

	@Test
	public void authorizeTest() {
		ResponseEntity<AuthorizeResponse> re = this.authorize();
		Assert.assertTrue(!ObjectUtils.isEmpty(re));
	}

	@Test
	public void transactionTest() {
		String authHeader = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJhdXRoMCIsImV4cCI6MTY0OTc0MzY0MywiR1YiOiJ7XCJmb3J3YXJkaW5nRW50aXR5SWRcIjpcIjEyM1wiLFwiZm9yd2FyZGluZ0VudGl0eVBhc3N3b3JkXCI6XCIxMjM0NTZcIixcInBhc3N3b3JkXCI6XCIxMjM0NTZcIixcInRlcm1pbmFsSWRcIjpcIlBvcyB0ZXN0IG1hY2hpbmUgaWRcIixcInVzZXJOYW1lXCI6XCJ0b255XCJ9In0.SxncDMz5qruG2iEUHsELiYyLb1XUmu5IAphC40HCsu0";
		AuthorizePayload authorizePayload = new AuthorizePayload();
		authorizePayload.setTerminalId("Pos test machine id");
		Mockito.when(gvPosService.getAuthorizePayloadByToken(Mockito.any())).thenReturn(authorizePayload);
		TransactionRequest param = new TransactionRequest();
		param.setActionType("2");
		param.setTransactionTypeId(705);
		Map<String, Object> map=new HashMap<>();
		map.put("actionType", "2");
		map.put("transactionTypeId", 705);
		TransactionResponse response = JSON.parseObject(transactionResponseString, TransactionResponse.class);
		Mockito.when(gvPosService.transactionValidateActivate(Mockito.any(), Mockito.any())).thenReturn(response);
		ResponseEntity<TransactionResponse> re = gvPosController.transaction(map, authHeader);

		map.put("actionType", "1");
		map.put("transactionTypeId", 705);
		param.setActionType("1");
		param.setTransactionTypeId(705);
		Mockito.when(gvPosService.transactionExecuteActivate(Mockito.any(), Mockito.any())).thenReturn(response);
		re = gvPosController.transaction(map, authHeader);

		map.put("actionType", "2");
		map.put("transactionTypeId", 702);
		param.setActionType("2");
		param.setTransactionTypeId(702);
		Mockito.when(gvPosService.transactionValidateRedeem(Mockito.any(), Mockito.any())).thenReturn(response);
		re = gvPosController.transaction(map, authHeader);
		
		map.put("actionType", "1");
		map.put("transactionTypeId", 702);
		param.setActionType("1");
		param.setTransactionTypeId(702);
		Mockito.when(gvPosService.transactionExecuteRedeem(Mockito.any(), Mockito.any())).thenReturn(response);
		re = gvPosController.transaction(map, authHeader);

		Assert.assertTrue(!ObjectUtils.isEmpty(re));
	}

	@Test
	public void cancelredeemTest() {
		String authHeader = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJhdXRoMCIsImV4cCI6MTY0OTc0MzY0MywiR1YiOiJ7XCJmb3J3YXJkaW5nRW50aXR5SWRcIjpcIjEyM1wiLFwiZm9yd2FyZGluZ0VudGl0eVBhc3N3b3JkXCI6XCIxMjM0NTZcIixcInBhc3N3b3JkXCI6XCIxMjM0NTZcIixcInRlcm1pbmFsSWRcIjpcIlBvcyB0ZXN0IG1hY2hpbmUgaWRcIixcInVzZXJOYW1lXCI6XCJ0b255XCJ9In0.SxncDMz5qruG2iEUHsELiYyLb1XUmu5IAphC40HCsu0";
		AuthorizePayload authorizePayload = new AuthorizePayload();
		authorizePayload.setTerminalId("Pos test machine id");
		Mockito.when(gvPosService.getAuthorizePayloadByToken(Mockito.any())).thenReturn(authorizePayload);

		CancelredeemRequest param = new CancelredeemRequest();
		param.setTransactionId(1);
		param.setOriginalApprovalCode("1");
		param.setOriginalBatchNumber(1);
		param.setOriginalTransactionId(1);
		Map<String, Object> map=new HashMap<>();
		map.put("originalApprovalCode", "1");
		map.put("originalBatchNumber", 1);
		map.put("originalTransactionId", 1);
		map.put("transactionId", 1);
		CancelredeemResponse response = new CancelredeemResponse();
		Mockito.when(gvPosService.cancelredeem(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(response);
		ResponseEntity<CancelredeemResponse> re = gvPosController.cancelredeem(map, authHeader);

		Assert.assertTrue(!ObjectUtils.isEmpty(re));
	}

	@Test
	public void batchcloseTest() {
		String authHeader = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJhdXRoMCIsImV4cCI6MTY0OTc0MzY0MywiR1YiOiJ7XCJmb3J3YXJkaW5nRW50aXR5SWRcIjpcIjEyM1wiLFwiZm9yd2FyZGluZ0VudGl0eVBhc3N3b3JkXCI6XCIxMjM0NTZcIixcInBhc3N3b3JkXCI6XCIxMjM0NTZcIixcInRlcm1pbmFsSWRcIjpcIlBvcyB0ZXN0IG1hY2hpbmUgaWRcIixcInVzZXJOYW1lXCI6XCJ0b255XCJ9In0.SxncDMz5qruG2iEUHsELiYyLb1XUmu5IAphC40HCsu0";
		AuthorizePayload authorizePayload = new AuthorizePayload();
		authorizePayload.setTerminalId("Pos test machine id");
		Mockito.when(gvPosService.getAuthorizePayloadByToken(Mockito.any())).thenReturn(authorizePayload);

		BatchcloseRequest param = new BatchcloseRequest();
		BatchcloseResponse response = new BatchcloseResponse();
		Mockito.when(gvPosService.batchclose(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(response);
		ResponseEntity<BatchcloseResponse> re = gvPosController.batchclose(new HashMap<>(), authHeader);

		Assert.assertTrue(!ObjectUtils.isEmpty(re));
	}

	@Test
	public void createandissueTest() {
		String authHeader = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJhdXRoMCIsImV4cCI6MTY0OTc0MzY0MywiR1YiOiJ7XCJmb3J3YXJkaW5nRW50aXR5SWRcIjpcIjEyM1wiLFwiZm9yd2FyZGluZ0VudGl0eVBhc3N3b3JkXCI6XCIxMjM0NTZcIixcInBhc3N3b3JkXCI6XCIxMjM0NTZcIixcInRlcm1pbmFsSWRcIjpcIlBvcyB0ZXN0IG1hY2hpbmUgaWRcIixcInVzZXJOYW1lXCI6XCJ0b255XCJ9In0.SxncDMz5qruG2iEUHsELiYyLb1XUmu5IAphC40HCsu0";
		AuthorizePayload authorizePayload = new AuthorizePayload();
		authorizePayload.setTerminalId("Pos test machine id");
		Mockito.when(gvPosService.getAuthorizePayloadByToken(Mockito.any())).thenReturn(authorizePayload);


		CreateandissueRequest param = new CreateandissueRequest();
		CreateandissueResponse response = new CreateandissueResponse();
		response.setResponseCode(0);
		Mockito.when(gvPosService.createandissue(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(response);
		ResponseEntity<CreateandissueResponse> re = gvPosController.createandissue(new HashMap<>(), authHeader);

		Assert.assertTrue(!ObjectUtils.isEmpty(re));
	}

	/*@Test
	public void cancelactivateTest() {
		String authHeader = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJhdXRoMCIsImV4cCI6MTY0OTc0MzY0MywiR1YiOiJ7XCJmb3J3YXJkaW5nRW50aXR5SWRcIjpcIjEyM1wiLFwiZm9yd2FyZGluZ0VudGl0eVBhc3N3b3JkXCI6XCIxMjM0NTZcIixcInBhc3N3b3JkXCI6XCIxMjM0NTZcIixcInRlcm1pbmFsSWRcIjpcIlBvcyB0ZXN0IG1hY2hpbmUgaWRcIixcInVzZXJOYW1lXCI6XCJ0b255XCJ9In0.SxncDMz5qruG2iEUHsELiYyLb1XUmu5IAphC40HCsu0";
		AuthorizePayload authorizePayload = new AuthorizePayload();
		authorizePayload.setTerminalId("Pos test machine id");
		Mockito.when(gvPosService.getAuthorizePayloadByToken(Mockito.any())).thenReturn(authorizePayload);

		CancelactivateRequest param = new CancelactivateRequest();
		CancelactivateResponse response = new CancelactivateResponse();
		Mockito.when(gvPosService.cancelactivate(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(response);
		ResponseEntity<CancelactivateResponse> re = gvPosController.cancelactivate(new HashMap<>(), authHeader);

		Assert.assertTrue(!ObjectUtils.isEmpty(re));
	}*/

	@Test
	public void activateonlyTest() {
		String authHeader = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJhdXRoMCIsImV4cCI6MTY0OTc0MzY0MywiR1YiOiJ7XCJmb3J3YXJkaW5nRW50aXR5SWRcIjpcIjEyM1wiLFwiZm9yd2FyZGluZ0VudGl0eVBhc3N3b3JkXCI6XCIxMjM0NTZcIixcInBhc3N3b3JkXCI6XCIxMjM0NTZcIixcInRlcm1pbmFsSWRcIjpcIlBvcyB0ZXN0IG1hY2hpbmUgaWRcIixcInVzZXJOYW1lXCI6XCJ0b255XCJ9In0.SxncDMz5qruG2iEUHsELiYyLb1XUmu5IAphC40HCsu0";
		AuthorizePayload authorizePayload = new AuthorizePayload();
		authorizePayload.setTerminalId("Pos test machine id");
		Mockito.when(gvPosService.getAuthorizePayloadByToken(Mockito.any())).thenReturn(authorizePayload);

		ActivateonlyRequest param = new ActivateonlyRequest();
		ActivateonlyResponse response = new ActivateonlyResponse();
		response.setResponseCode(0);
		Mockito.when(gvPosService.activateonly(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(response);
		ResponseEntity<ActivateonlyResponse> re = gvPosController.activateonly(new HashMap<>(), authHeader);

		Assert.assertTrue(!ObjectUtils.isEmpty(re));
	}

	@Test
	public void cardtransactionhistoryTest() {
		String authHeader = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJhdXRoMCIsImV4cCI6MTY0OTc0MzY0MywiR1YiOiJ7XCJmb3J3YXJkaW5nRW50aXR5SWRcIjpcIjEyM1wiLFwiZm9yd2FyZGluZ0VudGl0eVBhc3N3b3JkXCI6XCIxMjM0NTZcIixcInBhc3N3b3JkXCI6XCIxMjM0NTZcIixcInRlcm1pbmFsSWRcIjpcIlBvcyB0ZXN0IG1hY2hpbmUgaWRcIixcInVzZXJOYW1lXCI6XCJ0b255XCJ9In0.SxncDMz5qruG2iEUHsELiYyLb1XUmu5IAphC40HCsu0";
		AuthorizePayload authorizePayload = new AuthorizePayload();
		authorizePayload.setTerminalId("Pos test machine id");
		Mockito.when(gvPosService.getAuthorizePayloadByToken(Mockito.any())).thenReturn(authorizePayload);

		CardtransactionhistoryRequest param = new CardtransactionhistoryRequest();
		CardtransactionhistoryResponse response = new CardtransactionhistoryResponse();
		response.setResponseCode(0);
		Mockito.when(gvPosService.cardtransactionhistory(Mockito.any(), Mockito.any())).thenReturn(response);
		ResponseEntity<CardtransactionhistoryResponse> re = gvPosController.cardtransactionhistory(new HashMap<>(), authHeader);

		Assert.assertTrue(!ObjectUtils.isEmpty(re));
	}

	@Test
	public void onetimebarcodeTest() {
		String authHeader = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJhdXRoMCIsImV4cCI6MTY0OTc0MzY0MywiR1YiOiJ7XCJmb3J3YXJkaW5nRW50aXR5SWRcIjpcIjEyM1wiLFwiZm9yd2FyZGluZ0VudGl0eVBhc3N3b3JkXCI6XCIxMjM0NTZcIixcInBhc3N3b3JkXCI6XCIxMjM0NTZcIixcInRlcm1pbmFsSWRcIjpcIlBvcyB0ZXN0IG1hY2hpbmUgaWRcIixcInVzZXJOYW1lXCI6XCJ0b255XCJ9In0.SxncDMz5qruG2iEUHsELiYyLb1XUmu5IAphC40HCsu0";
		AuthorizePayload authorizePayload = new AuthorizePayload();
		authorizePayload.setTerminalId("Pos test machine id");
		Mockito.when(gvPosService.getAuthorizePayloadByToken(Mockito.any())).thenReturn(authorizePayload);

		OnetimebarcodeRequest param = new OnetimebarcodeRequest();
		OnetimebarcodeResponse response = new OnetimebarcodeResponse();
		response.setResponseCode(0);
		Mockito.when(gvPosService.onetimebarcode(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(response);
		ResponseEntity<OnetimebarcodeResponse> re = gvPosController.onetimebarcode(new HashMap<>(), authHeader);

		Assert.assertTrue(!ObjectUtils.isEmpty(re));
	}

	private ResponseEntity<AuthorizeResponse> authorize() {
		String authorizeString = "{" + "  \"dateAtClient\": \"2022-02-02 18:00:00\","
				+ "  \"forwardingEntityId\": \"123\"," + "  \"forwardingEntityPassword\": \"123456\","
				+ "  \"password\": \"123456\"," + "  \"terminalId\": \"Pos test machine id\","
				+ "  \"transactionId\": 123," + "  \"userName\": \"tony\"" + "}";

		Map<String,Object> request = JSON.parseObject(authorizeString, HashMap.class);
		String authorizeResponseString = "{"
				+ "    \"authToken\": \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJhdXRoMCIsImV4cCI6MTY0OTc0Mjk3NiwiR1YiOiJ7XCJmb3J3YXJkaW5nRW50aXR5SWRcIjpcIjEyM1wiLFwiZm9yd2FyZGluZ0VudGl0eVBhc3N3b3JkXCI6XCIxMjM0NTZcIixcInBhc3N3b3JkXCI6XCIxMjM0NTZcIixcInRlcm1pbmFsSWRcIjpcIlBvcyB0ZXN0IG1hY2hpbmUgaWRcIixcInVzZXJOYW1lXCI6XCJ0b255XCJ9In0.AE5tNySWOI7NDGHSB9pmupTUjmki_yOueZQKFFWjxAQ\","
				+ "    \"merchantOutletInfo\": {" + "        \"merchantOutletName\": \"不要删 outlet2\","
				+ "        \"merchantOutletAddress1\": \"2nd Floor NO.23\","
				+ "        \"merchantOutletAddress2\": \"2nd Floor NO.23\","
				+ "        \"merchantOutletPinCode\": \"0\"" + "    }," + "    \"localeInfo\": {"
				+ "        \"culture\": \"id-ID\"," + "        \"currencySymbol\": \"IDR\","
				+ "        \"currencyPosition\": 0," + "        \"currencyDecimalDigits\": 0,"
				+ "        \"displayUnitForPoints\": \"Points\"" + "    }," + "    \"responseCode\": 0,"
				+ "    \"responseMessage\": \"Success\"" + "}";
		AuthorizeResponse authorizeResponse = JSON.parseObject(authorizeResponseString, AuthorizeResponse.class);
		Mockito.when(gvPosService.authorize(Mockito.any())).thenReturn(authorizeResponse);

		ResponseEntity<AuthorizeResponse> re = gvPosController.authorize(request);
		return re;
	}

	public String transactionResponseString = "{" + "    \"responseCode\": 0,"
			+ "    \"responseMessage\": \"Validation Failed\"," + "    \"transactionId\": 123,"
			+ "    \"transactionTypeId\": 705," + "    \"actionType\": \"2\"," + "    \"totalSuccessCardCount\": 0,"
			+ "    \"totalCardCount\": 18," + "    \"transactionAmount\": 0," + "    \"sourceId\": \"123456\","
			+ "    \"messageId\": \"123456\"," + "    \"ordernumber\": \"123456\"," + "    \"lineItems\": ["
			+ "        {" + "            \"lineItemNo\": \"1\"," + "            \"lineItemStatus\": \"SUCCESS\","
			+ "            \"successCardCount\": 0," + "            \"totalCardCount\": 1,"
			+ "            \"designCode\": \"DISPLAY DENOMINATION\"," + "            \"productCode\": \"\","
			+ "            \"cardProgramGroupName\": \"DISPLAY DENOMINATION\"," + "            \"totalAmount\": 100.00,"
			+ "            \"startCardNumber\": \"1004224220000001\","
			+ "            \"endCardNumber\": \"1004224220000001\"," + "            \"cards\": [" + "                {"
			+ "                    \"cardNumber\": \"51050472204202050080010162\","
			+ "                    \"activationCode\": \"52dfd3cb01524d938f042f85aac912b7\","
			+ "                    \"cardBalance\": 100.00,"
			+ "                    \"cardExpiry\": \"2023-02-17 00:00:00\","
			+ "                    \"transactionAmount\": 100.00," + "                    \"cardStatus\": \"ISSUED\","
			+ "                    \"cardProgramGroupName\": \"DISPLAY DENOMINATION\","
			+ "                    \"barcode\": \"51050472204202050080010162\","
			+ "                    \"responseCode\": 0,"
			+ "                    \"responseMessage\": \"Transaction successful.\"" + "                }"
			+ "            ]" + "        }," + "        {" + "            \"lineItemNo\": \"2\","
			+ "            \"lineItemStatus\": \"SUCCESS\"," + "            \"successCardCount\": 0,"
			+ "            \"totalCardCount\": 1," + "            \"designCode\": \"DISPLAY DENOMINATION\","
			+ "            \"productCode\": \"\"," + "            \"cardProgramGroupName\": \"DISPLAY DENOMINATION\","
			+ "            \"totalAmount\": 100.00," + "            \"startCardNumber\": \"1004224220000002\","
			+ "            \"endCardNumber\": \"1004224220000002\"," + "            \"cards\": [" + "                {"
			+ "                    \"cardNumber\": \"21060472244292000010090281\","
			+ "                    \"activationCode\": \"52dfd3cb01524d938f042f85aac912b7\","
			+ "                    \"cardBalance\": 100.00,"
			+ "                    \"cardExpiry\": \"2023-02-17 00:00:00\","
			+ "                    \"transactionAmount\": 100.00," + "                    \"cardStatus\": \"ISSUED\","
			+ "                    \"cardProgramGroupName\": \"DISPLAY DENOMINATION\","
			+ "                    \"barcode\": \"21060472244292000010090281\","
			+ "                    \"responseCode\": 0,"
			+ "                    \"responseMessage\": \"Transaction successful.\"" + "                }"
			+ "            ]" + "        }," + "        {" + "            \"lineItemNo\": \"3\","
			+ "            \"lineItemStatus\": \"SUCCESS\"," + "            \"successCardCount\": 0,"
			+ "            \"totalCardCount\": 1," + "            \"designCode\": \"DISPLAY DENOMINATION\","
			+ "            \"productCode\": \"\"," + "            \"cardProgramGroupName\": \"DISPLAY DENOMINATION\","
			+ "            \"totalAmount\": 100.00," + "            \"startCardNumber\": \"1004224220000003\","
			+ "            \"endCardNumber\": \"1004224220000003\"," + "            \"cards\": [" + "                {"
			+ "                    \"cardNumber\": \"41010402294262050010060339\","
			+ "                    \"activationCode\": \"52dfd3cb01524d938f042f85aac912b7\","
			+ "                    \"cardBalance\": 100.00,"
			+ "                    \"cardExpiry\": \"2023-02-17 00:00:00\","
			+ "                    \"transactionAmount\": 100.00," + "                    \"cardStatus\": \"ISSUED\","
			+ "                    \"cardProgramGroupName\": \"DISPLAY DENOMINATION\","
			+ "                    \"barcode\": \"41010402294262050010060339\","
			+ "                    \"responseCode\": 0,"
			+ "                    \"responseMessage\": \"Transaction successful.\"" + "                }"
			+ "            ]" + "        }," + "        {" + "            \"lineItemNo\": \"4\","
			+ "            \"lineItemStatus\": \"SUCCESS\"," + "            \"successCardCount\": 0,"
			+ "            \"totalCardCount\": 3," + "            \"designCode\": \"DISPLAY DENOMINATION\","
			+ "            \"productCode\": \"\"," + "            \"cardProgramGroupName\": \"DISPLAY DENOMINATION\","
			+ "            \"totalAmount\": 300.00," + "            \"startCardNumber\": \"1004224220000004\","
			+ "            \"endCardNumber\": \"1004224220000006\"," + "            \"cards\": [" + "                {"
			+ "                    \"cardNumber\": \"21050472224222070060030416\","
			+ "                    \"activationCode\": \"52dfd3cb01524d938f042f85aac912b7\","
			+ "                    \"cardBalance\": 100.00,"
			+ "                    \"cardExpiry\": \"2023-02-17 00:00:00\","
			+ "                    \"transactionAmount\": 100.00," + "                    \"cardStatus\": \"ISSUED\","
			+ "                    \"cardProgramGroupName\": \"DISPLAY DENOMINATION\","
			+ "                    \"barcode\": \"21050472224222070060030416\","
			+ "                    \"responseCode\": 0,"
			+ "                    \"responseMessage\": \"Transaction successful.\"" + "                },"
			+ "                {" + "                    \"cardNumber\": \"01000422264202040010090531\","
			+ "                    \"activationCode\": \"52dfd3cb01524d938f042f85aac912b7\","
			+ "                    \"cardBalance\": 100.00,"
			+ "                    \"cardExpiry\": \"2023-02-17 00:00:00\","
			+ "                    \"transactionAmount\": 100.00," + "                    \"cardStatus\": \"ISSUED\","
			+ "                    \"cardProgramGroupName\": \"DISPLAY DENOMINATION\","
			+ "                    \"barcode\": \"01000422264202040010090531\","
			+ "                    \"responseCode\": 0,"
			+ "                    \"responseMessage\": \"Transaction successful.\"" + "                },"
			+ "                {" + "                    \"cardNumber\": \"81060402234292090080090684\","
			+ "                    \"activationCode\": \"52dfd3cb01524d938f042f85aac912b7\","
			+ "                    \"cardBalance\": 100.00,"
			+ "                    \"cardExpiry\": \"2023-02-17 00:00:00\","
			+ "                    \"transactionAmount\": 100.00," + "                    \"cardStatus\": \"ISSUED\","
			+ "                    \"cardProgramGroupName\": \"DISPLAY DENOMINATION\","
			+ "                    \"barcode\": \"81060402234292090080090684\","
			+ "                    \"responseCode\": 0,"
			+ "                    \"responseMessage\": \"Transaction successful.\"" + "                }"
			+ "            ]" + "        }" + "    ]," + "    \"invalidLineItems\": [" + "        {"
			+ "            \"lineItemNo\": \"1\"," + "            \"lineItemStatus\": \"FAILED\","
			+ "            \"successCardCount\": 0," + "            \"totalCardCount\": 1,"
			+ "            \"designCode\": \"DISPLAY DENOMINATION\"," + "            \"productCode\": \"\","
			+ "            \"cardProgramGroupName\": \"DISPLAY DENOMINATION\"," + "            \"totalAmount\": 100.00,"
			+ "            \"startCardNumber\": \"1004224220000001\","
			+ "            \"endCardNumber\": \"1004224220000001\"," + "            \"cards\": [" + "                {"
			+ "                    \"cardNumber\": \"51050472204202050080010162\","
			+ "                    \"activationCode\": \"52dfd3cb01524d938f042f85aac912b7\","
			+ "                    \"cardBalance\": 100.00,"
			+ "                    \"cardExpiry\": \"2023-02-17 00:00:00\","
			+ "                    \"transactionAmount\": 100.00," + "                    \"cardStatus\": \"ISSUED\","
			+ "                    \"cardProgramGroupName\": \"DISPLAY DENOMINATION\","
			+ "                    \"barcode\": \"51050472204202050080010162\","
			+ "                    \"responseCode\": 10024,"
			+ "                    \"responseMessage\": \"CARD IS ISSUED\"" + "                }" + "            ]"
			+ "        }," + "        {" + "            \"lineItemNo\": \"2\","
			+ "            \"lineItemStatus\": \"FAILED\"," + "            \"successCardCount\": 0,"
			+ "            \"totalCardCount\": 1," + "            \"designCode\": \"DISPLAY DENOMINATION\","
			+ "            \"productCode\": \"\"," + "            \"cardProgramGroupName\": \"DISPLAY DENOMINATION\","
			+ "            \"totalAmount\": 100.00," + "            \"startCardNumber\": \"1004224220000002\","
			+ "            \"endCardNumber\": \"1004224220000002\"," + "            \"cards\": [" + "                {"
			+ "                    \"cardNumber\": \"21060472244292000010090281\","
			+ "                    \"activationCode\": \"52dfd3cb01524d938f042f85aac912b7\","
			+ "                    \"cardBalance\": 100.00,"
			+ "                    \"cardExpiry\": \"2023-02-17 00:00:00\","
			+ "                    \"transactionAmount\": 100.00," + "                    \"cardStatus\": \"ISSUED\","
			+ "                    \"cardProgramGroupName\": \"DISPLAY DENOMINATION\","
			+ "                    \"barcode\": \"21060472244292000010090281\","
			+ "                    \"responseCode\": 10024,"
			+ "                    \"responseMessage\": \"CARD IS ISSUED\"" + "                }" + "            ]"
			+ "        }," + "        {" + "            \"lineItemNo\": \"3\","
			+ "            \"lineItemStatus\": \"FAILED\"," + "            \"successCardCount\": 0,"
			+ "            \"totalCardCount\": 1," + "            \"designCode\": \"DISPLAY DENOMINATION\","
			+ "            \"productCode\": \"\"," + "            \"cardProgramGroupName\": \"DISPLAY DENOMINATION\","
			+ "            \"totalAmount\": 100.00," + "            \"startCardNumber\": \"1004224220000003\","
			+ "            \"endCardNumber\": \"1004224220000003\"," + "            \"cards\": [" + "                {"
			+ "                    \"cardNumber\": \"41010402294262050010060339\","
			+ "                    \"activationCode\": \"52dfd3cb01524d938f042f85aac912b7\","
			+ "                    \"cardBalance\": 100.00,"
			+ "                    \"cardExpiry\": \"2023-02-17 00:00:00\","
			+ "                    \"transactionAmount\": 100.00," + "                    \"cardStatus\": \"ISSUED\","
			+ "                    \"cardProgramGroupName\": \"DISPLAY DENOMINATION\","
			+ "                    \"barcode\": \"41010402294262050010060339\","
			+ "                    \"responseCode\": 10024,"
			+ "                    \"responseMessage\": \"CARD IS ISSUED\"" + "                }" + "            ]"
			+ "        }," + "        {" + "            \"lineItemNo\": \"4\","
			+ "            \"lineItemStatus\": \"FAILED\"," + "            \"successCardCount\": 0,"
			+ "            \"totalCardCount\": 3," + "            \"designCode\": \"DISPLAY DENOMINATION\","
			+ "            \"productCode\": \"\"," + "            \"cardProgramGroupName\": \"DISPLAY DENOMINATION\","
			+ "            \"totalAmount\": 300.00," + "            \"startCardNumber\": \"1004224220000004\","
			+ "            \"endCardNumber\": \"1004224220000006\"," + "            \"cards\": [" + "                {"
			+ "                    \"cardNumber\": \"21050472224222070060030416\","
			+ "                    \"activationCode\": \"52dfd3cb01524d938f042f85aac912b7\","
			+ "                    \"cardBalance\": 100.00,"
			+ "                    \"cardExpiry\": \"2023-02-17 00:00:00\","
			+ "                    \"transactionAmount\": 100.00," + "                    \"cardStatus\": \"ISSUED\","
			+ "                    \"cardProgramGroupName\": \"DISPLAY DENOMINATION\","
			+ "                    \"barcode\": \"21050472224222070060030416\","
			+ "                    \"responseCode\": 10024,"
			+ "                    \"responseMessage\": \"CARD IS ISSUED\"" + "                }," + "                {"
			+ "                    \"cardNumber\": \"01000422264202040010090531\","
			+ "                    \"activationCode\": \"52dfd3cb01524d938f042f85aac912b7\","
			+ "                    \"cardBalance\": 100.00,"
			+ "                    \"cardExpiry\": \"2023-02-17 00:00:00\","
			+ "                    \"transactionAmount\": 100.00," + "                    \"cardStatus\": \"ISSUED\","
			+ "                    \"cardProgramGroupName\": \"DISPLAY DENOMINATION\","
			+ "                    \"barcode\": \"01000422264202040010090531\","
			+ "                    \"responseCode\": 10024,"
			+ "                    \"responseMessage\": \"CARD IS ISSUED\"" + "                }," + "                {"
			+ "                    \"cardNumber\": \"81060402234292090080090684\","
			+ "                    \"activationCode\": \"52dfd3cb01524d938f042f85aac912b7\","
			+ "                    \"cardBalance\": 100.00,"
			+ "                    \"cardExpiry\": \"2023-02-17 00:00:00\","
			+ "                    \"transactionAmount\": 100.00," + "                    \"cardStatus\": \"ISSUED\","
			+ "                    \"cardProgramGroupName\": \"DISPLAY DENOMINATION\","
			+ "                    \"barcode\": \"81060402234292090080090684\","
			+ "                    \"responseCode\": 10024,"
			+ "                    \"responseMessage\": \"CARD IS ISSUED\"" + "                }" + "            ]"
			+ "        }," + "        {" + "            \"lineItemNo\": \"5\","
			+ "            \"lineItemStatus\": \"FAILED\"," + "            \"successCardCount\": 0,"
			+ "            \"totalCardCount\": 3," + "            \"designCode\": \"DISPLAY DENOMINATION\","
			+ "            \"productCode\": \"\"," + "            \"cardProgramGroupName\": \"DISPLAY DENOMINATION\","
			+ "            \"totalAmount\": 300.00," + "            \"startCardNumber\": \"1004224220000004\","
			+ "            \"endCardNumber\": \"1004224220000006\"," + "            \"cards\": [" + "                {"
			+ "                    \"cardNumber\": \"21050472224222070060030416\","
			+ "                    \"activationCode\": \"52dfd3cb01524d938f042f85aac912b7\","
			+ "                    \"cardBalance\": 100.00,"
			+ "                    \"cardExpiry\": \"2023-02-17 00:00:00\","
			+ "                    \"transactionAmount\": 100.00," + "                    \"cardStatus\": \"ISSUED\","
			+ "                    \"cardProgramGroupName\": \"DISPLAY DENOMINATION\","
			+ "                    \"barcode\": \"21050472224222070060030416\","
			+ "                    \"responseCode\": 10024,"
			+ "                    \"responseMessage\": \"CARD IS ISSUED\"" + "                }," + "                {"
			+ "                    \"cardNumber\": \"01000422264202040010090531\","
			+ "                    \"activationCode\": \"52dfd3cb01524d938f042f85aac912b7\","
			+ "                    \"cardBalance\": 100.00,"
			+ "                    \"cardExpiry\": \"2023-02-17 00:00:00\","
			+ "                    \"transactionAmount\": 100.00," + "                    \"cardStatus\": \"ISSUED\","
			+ "                    \"cardProgramGroupName\": \"DISPLAY DENOMINATION\","
			+ "                    \"barcode\": \"01000422264202040010090531\","
			+ "                    \"responseCode\": 10024,"
			+ "                    \"responseMessage\": \"CARD IS ISSUED\"" + "                }," + "                {"
			+ "                    \"cardNumber\": \"81060402234292090080090684\","
			+ "                    \"activationCode\": \"52dfd3cb01524d938f042f85aac912b7\","
			+ "                    \"cardBalance\": 100.00,"
			+ "                    \"cardExpiry\": \"2023-02-17 00:00:00\","
			+ "                    \"transactionAmount\": 100.00," + "                    \"cardStatus\": \"ISSUED\","
			+ "                    \"cardProgramGroupName\": \"DISPLAY DENOMINATION\","
			+ "                    \"barcode\": \"81060402234292090080090684\","
			+ "                    \"responseCode\": 10024,"
			+ "                    \"responseMessage\": \"CARD IS ISSUED\"" + "                }" + "            ]"
			+ "        }," + "        {" + "            \"lineItemNo\": \"6\","
			+ "            \"lineItemStatus\": \"FAILED\"," + "            \"successCardCount\": 0,"
			+ "            \"totalCardCount\": 3," + "            \"designCode\": \"DISPLAY DENOMINATION\","
			+ "            \"productCode\": \"\"," + "            \"cardProgramGroupName\": \"DISPLAY DENOMINATION\","
			+ "            \"totalAmount\": 300.00," + "            \"startCardNumber\": \"1004224220000004\","
			+ "            \"endCardNumber\": \"1004224220000006\"," + "            \"cards\": [" + "                {"
			+ "                    \"cardNumber\": \"21050472224222070060030416\","
			+ "                    \"activationCode\": \"52dfd3cb01524d938f042f85aac912b7\","
			+ "                    \"cardBalance\": 100.00,"
			+ "                    \"cardExpiry\": \"2023-02-17 00:00:00\","
			+ "                    \"transactionAmount\": 100.00," + "                    \"cardStatus\": \"ISSUED\","
			+ "                    \"cardProgramGroupName\": \"DISPLAY DENOMINATION\","
			+ "                    \"barcode\": \"21050472224222070060030416\","
			+ "                    \"responseCode\": 10024,"
			+ "                    \"responseMessage\": \"CARD IS ISSUED\"" + "                }," + "                {"
			+ "                    \"cardNumber\": \"01000422264202040010090531\","
			+ "                    \"activationCode\": \"52dfd3cb01524d938f042f85aac912b7\","
			+ "                    \"cardBalance\": 100.00,"
			+ "                    \"cardExpiry\": \"2023-02-17 00:00:00\","
			+ "                    \"transactionAmount\": 100.00," + "                    \"cardStatus\": \"ISSUED\","
			+ "                    \"cardProgramGroupName\": \"DISPLAY DENOMINATION\","
			+ "                    \"barcode\": \"01000422264202040010090531\","
			+ "                    \"responseCode\": 10024,"
			+ "                    \"responseMessage\": \"CARD IS ISSUED\"" + "                }," + "                {"
			+ "                    \"cardNumber\": \"81060402234292090080090684\","
			+ "                    \"activationCode\": \"52dfd3cb01524d938f042f85aac912b7\","
			+ "                    \"cardBalance\": 100.00,"
			+ "                    \"cardExpiry\": \"2023-02-17 00:00:00\","
			+ "                    \"transactionAmount\": 100.00," + "                    \"cardStatus\": \"ISSUED\","
			+ "                    \"cardProgramGroupName\": \"DISPLAY DENOMINATION\","
			+ "                    \"barcode\": \"81060402234292090080090684\","
			+ "                    \"responseCode\": 10024,"
			+ "                    \"responseMessage\": \"CARD IS ISSUED\"" + "                }" + "            ]"
			+ "        }" + "    ]" + "}";
}
