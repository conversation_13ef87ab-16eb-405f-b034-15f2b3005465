
package com.gtech.gvcore.web.controller;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.gtech.gvcore.common.request.postingxml.QueryPostingxmlRequest;
import com.gtech.gvcore.external.PostingXmlService;
import com.gtech.gvcore.web.external.PostingXmlController;

@RunWith(MockitoJUnitRunner.class)
public class PostingXmlControllerTest {

	@InjectMocks
	PostingXmlController postingXmlController;

	@Mock
	private PostingXmlService postingXmlService;

	@Test
	public void generatePostingTest() {
		QueryPostingxmlRequest request = new QueryPostingxmlRequest();
		postingXmlController.generatePosting(request);
		request.setQueryTime("2022-06-01");
		postingXmlController.generatePosting(request);
		Assert.assertTrue(true);
	}
}
