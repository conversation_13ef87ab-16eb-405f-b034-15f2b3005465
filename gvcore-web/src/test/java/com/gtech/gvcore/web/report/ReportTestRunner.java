package com.gtech.gvcore.web.report;

import com.gtech.gvcore.GvcoreApplication;
import com.gtech.gvcore.common.request.outlet.ReplaceOutletCodeRequest;
import com.gtech.gvcore.service.OutletService;
import com.gtech.gvcore.web.report.export.excel.DefaultExcelContextTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

/**
 * @ClassName ReportTestRunner
 * @Description
 * <AUTHOR>
 * @Date 2023/3/6 10:50
 * @Version V1.0
 **/
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {GvcoreApplication.class})
public class ReportTestRunner {

    @Autowired
    DefaultExcelContextTest defaultExcelContextTest;

    public @Test
    @Transactional void testDefaultExcelContext() {
        defaultExcelContextTest.test();
    }


    @Autowired
    private OutletService outletService;

    @Test
    public void updateOutletCode() {
        ReplaceOutletCodeRequest outlet = ReplaceOutletCodeRequest.builder()
                .oldOutletCode("")
                .newOutletCode("")
                .build();
        outletService.updateOutletCodeScript(outlet);
    }
}
