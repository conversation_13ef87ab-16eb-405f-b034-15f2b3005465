package com.gtech.gvcore.web;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.NoSuchFileException;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.stereotype.Component;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.GvcoreApplication;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ReportVoucherStatusEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.utils.GvConvertUtils;
import com.gtech.gvcore.dao.mapper.TransactionDataMapper;
import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.helper.TableHelper;
import com.gtech.gvcore.service.report.extend.voucherstatus.ReportVoucherStatusConvertUtils;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.Sqls;

@Slf4j
@Component
@ActiveProfiles("prod")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {GvcoreApplication.class})
public class LiabilityDataScriptFixData {

    @Autowired
    private VoucherMapper voucherMapper;

    @Autowired
    private TransactionDataMapper transactionDataMapper;

    //设置执行的cpg
	public static final String CPG_NAME = "MAP QC GV 100K";
    public static final boolean runnerAll = StringUtils.isBlank(CPG_NAME);
	public static final String FILE_NAME = runnerAll ? DateUtil.format(new Date(), "ddHHmm") : CPG_NAME + "-" + DateUtil.format(new Date(), "ddHHmm");
    //设置路径
	public static final String PATH = "/Users/<USER>/IdeaProjects/gvcore/liability_temp/";

    //查询截止时间
    public static final Date END_TIME = DateUtil.parseDate("20240901", DateUtil.FORMAT_YYYYMMDD);
    //本月过期时间
    public static final Date LAST_END_TIME = DateUtil.addMonth(END_TIME, -1);
    public static final String TABLE_CODE = "2410";

    // 未命中数据集合(新增)
    public static final List<String> UN_ADD_VOUCHER = Collections.synchronizedList(new LinkedList<>());
    // 未命中数据集合(删除)
    public static final List<String> UN_DELETE_VOUCHER = Collections.synchronizedList(new LinkedList<>());

    //明细数据
    @Setter
    public static List<ExcelReadDetailBean> detailList;

    //summary 数据
    @Setter
    public static List<ExcelReadSummaryBean> summaryList;

    // sql 标记
    public static final String DATE = "{{date}}";
    public static final String ID = "{{id}}";
    public static final String ISSUER_CODE = "{{issuerCode}}";
    public static final String MERCHANT_CODE = "{{merchantCode}}";
    public static final String OUTLET_CODE = "{{outletCode}}";
    public static final String CPG_CODE = "{{cpgCode}}";
    public static final String VOUCHER_CODE = "{{voucherCode}}";
    public static final String EFFECTIVE_DATE = "{{effectiveDate}}";
    public static final String STATUS = "{{status}}";
    public static final String DENOMINATION = "{{denomination}}";
    public static final String DEACTIVATED_DENOMINATION = "{{deactivated_denomination}}";
    public static final String PURCHASED_DENOMINATION = "{{purchased_denomination}}";
    public static final String ACTIVATED_DENOMINATION = "{{activated_denomination}}";
    public static final String EXPIRED_DENOMINATION = "{{expired_denomination}}";
    public static final String RECENTLY_EXPIRED_DENOMINATION = "{{recently_expired_denomination}}";


    //    执行请打开注解
    @Test
    public void runner() throws Exception {

        //设置summary 数据
        setSummaryList(EasyExcelFactory.read(PATH + "data/gv_report_temp_liability_s_" + TABLE_CODE + ".csv")
                .head(ExcelReadSummaryBean.class)
                .excelType(ExcelTypeEnum.CSV)
                .sheet()
                .doReadSync());

        //设置明细数据
        setDetailList(EasyExcelFactory.read(PATH + "data/gv_report_temp_liability_d_" + TABLE_CODE + ".csv")
                .head(ExcelReadDetailBean.class)
                .excelType(ExcelTypeEnum.CSV)
                .sheet()
                .doReadSync());

        UN_ADD_VOUCHER.add("##UN_ADD_VOUCHER:");
        UN_DELETE_VOUCHER.add("##UN_DELETE_VOUCHER:");

        //获取add sql
        final List<String> addVoucherSql = add();
        //获取delete sql
        final List<String> deleteVoucherSql = delete();

        // 合并sql 内容
        final List<String> union = ListUtils.union(ListUtils.union(addVoucherSql, Collections.singletonList("\n\n-- -------------------------------------------------------------------- \n\n")), deleteVoucherSql);

        //sql内容不为空则输出文件
        if (CollectionUtils.isNotEmpty(union)) {
			List<String> all = Arrays.asList(String.join("", union).split(";")).stream().map(v -> v + ";").collect(Collectors.toList());
//			writeStringToFile(String.join("", union), PATH + FILE_NAME + ".sql");
			List<String> summary = all.stream().filter(vo -> vo.contains("gv_report_temp_liability_s")).collect(Collectors.toList());
			List<String> detail = all.stream().filter(vo -> vo.contains("gv_report_temp_liability_d")).collect(Collectors.toList());
			writeStringToFile(String.join("", summary), PATH + FILE_NAME + "_S" + ".sql");
			writeStringToFile(String.join("", detail), PATH + FILE_NAME + "_D" + ".sql");
            // 如果脚本过大打开该代码
//          final List<List<String>> partition = ListUtils.partition(union, 50000);
//          for (int i = 0; i < partition.size(); i++) {
//              writeStringToFile(String.join("", partition.get(i)), PATH + CPG_CODE + i + ".sql");
//          }
        }

        //存在未命中数据则输出文件
        if (UN_ADD_VOUCHER.size() > 1 || UN_DELETE_VOUCHER.size() > 1) {
            writeStringToFile(String.join("\n", UN_ADD_VOUCHER) + "\n" + String.join("\n", UN_DELETE_VOUCHER), PATH + FILE_NAME + "_未命中数据" + ".txt");
        }

    }

	public static void main(String[] args) throws IOException {
		String a = readFileToString(PATH + FILE_NAME + ".sql");
		String[] b = a.split(";");
		System.out.println(b.length);
		List<String> summary = Arrays.asList(b).stream().filter(vo -> vo.contains("gv_report_temp_liability_s")).map(v -> v + ";").collect(Collectors.toList());
		List<String> detail = Arrays.asList(b).stream().filter(vo -> vo.contains("gv_report_temp_liability_d")).map(v -> v + ";").collect(Collectors.toList());
		writeStringToFile(String.join("", summary), PATH + FILE_NAME + "_S" + ".sql");
		writeStringToFile(String.join("", detail), PATH + FILE_NAME + "_D" + ".sql");
	}

    private List<String> add() throws IOException, InterruptedException, ExecutionException {

        //设置基础依赖
        VoucherAddBean.setVoucherMapper(voucherMapper);
        VoucherAddBean.setTransactionDataMapper(transactionDataMapper);

        final String path = PATH + "addDataByCpg/";

        //读取相关券号
        final String addVoucher = readFileToString(runnerAll ? path : path + CPG_NAME + ".txt");
        if (StringUtils.isBlank(addVoucher)) return new ArrayList<>();

        //创建线任务组
        final ForkJoinPool addPool = new ForkJoinPool(20);
        try {
            //执行sql 脚本转换
            return addPool.submit(() -> Arrays.stream(addVoucher.split("\n"))
                    .filter(StringUtils::isNotBlank)
                    //启用任务组
                    .parallel()
                    .map(StringUtils::trim)
                    .map(VoucherAddBean::newInstance)
                    .filter(Objects::nonNull)
                    .map(VoucherAddBean::toSql)
                    .collect(Collectors.toList())
            ).get();
        } finally {
            //关闭任务组
            addPool.shutdown();
        }
    }

    public List<String> delete() throws Exception {

        //读取相关券号
        final String path = PATH + "deleteDataByCpg/";
        final String deleteVoucher = readFileToString(runnerAll ? path : path + CPG_NAME + ".txt");
        if (StringUtils.isBlank(deleteVoucher)) return new ArrayList<>();

        //创建线任务组
        //该方法为单机执行 可以根据机器情况增大线程数
        final ForkJoinPool deletePool = new ForkJoinPool(64);
        try {
            //执行sql 脚本转换
            return deletePool.submit(() ->
                    Arrays.stream(deleteVoucher.split("\n"))
                            .filter(StringUtils::isNotBlank)
                            .parallel()
                            .map(StringUtils::trim)
                            .map(VoucherSubtractBean::newInstance)
                            .filter(Objects::nonNull)
                            .map(VoucherSubtractBean::toSql)
                            .collect(Collectors.toList())
            ).get();
        } finally {
            deletePool.shutdown();
        }

    }


    public static void writeStringToFile(String content, String filePath) throws IOException {
        // 将 String 内容写入指定文件，使用 UTF-8 编码
        Files.write(Paths.get(filePath), Collections.singleton(content), StandardCharsets.UTF_8);
    }

    public static String readFileToString(String filePath) throws IOException {

        File path = new File(filePath);

        if (path.isDirectory()) {
            // 处理文件夹
            return Arrays.stream(Objects.requireNonNull(path.listFiles()))
                    .map(File::getPath)
                    .map(Paths::get)
                    .map(e -> {
                        try {
                            return Files.readAllBytes(e);
                        } catch (IOException ex) {
                            throw new RuntimeException(ex);
                        }
                    })
                    .map(e -> new String(e, StandardCharsets.UTF_8))
                    .collect(Collectors.joining("\n"));

        } else {
            try {
                // 使用 Files 类读取文件，编码使用 UTF-8
                byte[] bytes = Files.readAllBytes(Paths.get(filePath));
                return new String(bytes, StandardCharsets.UTF_8);
            } catch (NoSuchFileException e) {
                return "";
            }
        }
    }


    @Getter
    @Setter
    @Accessors(chain = true)
    public static class VoucherAddBean {

        @Setter
        public static VoucherMapper voucherMapper;
        @Setter
        public static TransactionDataMapper transactionDataMapper;

        //新增历史
        private static final Set<String> INSERT_DETAIL_HISTORY = new HashSet<>();
        private static final Set<String> INSERT_SUMMARY_HISTORY = new HashSet<>();


        private static final String INSERT_SUMMARY = "INSERT INTO gv_report_temp_liability_s_" + DATE + "(issuer_code, merchant_code, outlet_code, cpg_code, activated_amount, purchased_amount, deactivated_amount, expired_amount, recently_expired_amount)" +
                " VALUE ('" + ISSUER_CODE + "', '" + MERCHANT_CODE + "', '" + OUTLET_CODE + "', '" + CPG_CODE + "', '" + ACTIVATED_DENOMINATION + "', '" + PURCHASED_DENOMINATION + "', '" + DEACTIVATED_DENOMINATION + "', '" + EXPIRED_DENOMINATION + "', '" + RECENTLY_EXPIRED_DENOMINATION + "');";

        private static final String INSERT_DETAIL = "INSERT INTO gv_report_temp_liability_d_" + DATE + "(issuer_code, merchant_code, outlet_code, cpg_code, voucher_status, denomination, effective_date, voucher_code_page_index, voucher_codes)" +
                " VALUE ('" + ISSUER_CODE + "', '" + MERCHANT_CODE + "', '" + OUTLET_CODE + "', '" + CPG_CODE + "', '" + STATUS + "', '" + DENOMINATION + "', '" + EFFECTIVE_DATE + "', '0', '" + VOUCHER_CODE + "');";
        private static final String UPDATE_DETAIL_BY_INSERT = "UPDATE gv_report_temp_liability_d_" + DATE + " SET voucher_codes = CONCAT(voucher_codes, ',', '" + VOUCHER_CODE + "') , voucher_code_page_index = voucher_code_page_index + 1 " +
                " WHERE issuer_code = '" + ISSUER_CODE + "' AND merchant_code = '" + MERCHANT_CODE + "' AND outlet_code = '" + OUTLET_CODE + "' AND cpg_code = '" + CPG_CODE + "' AND voucher_status = '" + STATUS + "' AND denomination = '" + DENOMINATION + "' AND effective_date = '" + EFFECTIVE_DATE + "' ; ";

        private static final String UPDATE_DETAIL = "UPDATE gv_report_temp_liability_d_" + DATE + " SET voucher_codes = CONCAT(voucher_codes, ',', '" + VOUCHER_CODE + "') , voucher_code_page_index = voucher_code_page_index + 1 WHERE id = {{id}}; ";

        private static final String UPDATE_SUMMARY = "UPDATE gv_report_temp_liability_s_" + DATE + " " +
                "SET activated_amount = activated_amount + " + ACTIVATED_DENOMINATION + ", purchased_amount = purchased_amount + " + PURCHASED_DENOMINATION + ", deactivated_amount = deactivated_amount + " + DEACTIVATED_DENOMINATION + ", " +
                "expired_amount = expired_amount + " + EXPIRED_DENOMINATION + ", recently_expired_amount = recently_expired_amount + " + RECENTLY_EXPIRED_DENOMINATION + " ";

        private static final String WHERE_SUMMARY_ALL = "WHERE issuer_code = '" + ISSUER_CODE + "' AND merchant_code = '" + MERCHANT_CODE + "' AND outlet_code = '" + OUTLET_CODE + "' AND cpg_code = '" + CPG_CODE + "';";
        private static final String WHERE_SUMMARY_ID = "WHERE id = {{id}}; ";

        //实体字段
        private String issuerCode;
        private String merchantCode;
        private String outletCode;
        private String cpgCode;
        private String voucherCode;
        private Date effectiveDate;
        private ReportVoucherStatusEnum status;
        private BigDecimal denomination;
        private boolean addRecentlyExpired;

        public static VoucherAddBean newInstance(final String voucherCode) {

            if (isExist(voucherCode)) {
                UN_ADD_VOUCHER.add(voucherCode);
                log.info("VoucherAddBean => 卡券{}存在, 跳过", voucherCode);
                return null;
            }

            final Voucher condition = new Voucher();
            condition.setVoucherCode(voucherCode);
            final Voucher voucher = voucherMapper.selectOne(condition);

            final TransactionData transactionData = GvConvertUtils.<List<TransactionData>>toObject(transactionDataMapper.selectByCondition(Example.builder(TransactionData.class)
                            .select(TransactionData.C_ID, TransactionData.C_TRANSACTION_CODE, TransactionData.C_VOUCHER_CODE, TransactionData.C_OUTLET_CODE, TransactionData.C_MERCHANT_CODE, "issuerCode")
                            .from("t_transaction_data_" + TableHelper.getTableIndex(voucherCode))
                            .where(Sqls.custom()
                                    .andEqualTo(TransactionData.C_VOUCHER_CODE, voucherCode)
                                    .andLessThan(TransactionData.C_TRANSACTION_DATE, END_TIME)
                                    .andEqualTo(TransactionData.C_TRANSACTION_TYPE, TransactionTypeEnum.GIFT_CARD_SELL.getCode())
                            ).build()), new ArrayList<>())
                    .stream()
                    .max(Comparator.comparingLong(TransactionData::getId))
                    .orElseGet(TransactionData::new);

            final ReportVoucherStatusEnum voucherStatus = getVoucherStatus(voucher, END_TIME);

            return new VoucherAddBean()
                    .setVoucherCode(voucherCode)
                    .setCpgCode(voucher.getCpgCode())
                    .setStatus(voucherStatus)
                    .setIssuerCode(ConvertUtils.toString(transactionData.getIssuerCode(), ""))
                    .setMerchantCode(ConvertUtils.toString(transactionData.getMerchantCode(), ""))
                    .setOutletCode(ConvertUtils.toString(transactionData.getOutletCode(), ""))
                    .setDenomination(voucher.getDenomination())
                    .setAddRecentlyExpired(voucherStatus == ReportVoucherStatusEnum.VOUCHER_EXPIRED
                            && getVoucherStatus(voucher, LAST_END_TIME) != ReportVoucherStatusEnum.VOUCHER_EXPIRED)
                    .setEffectiveDate(voucher.getVoucherEffectiveDate())
                    ;

        }

        private static ReportVoucherStatusEnum getVoucherStatus(final Voucher voucher, final Date now) {

            if (ReportVoucherStatusConvertUtils.getVoucherStatus(voucher, now) == ReportVoucherStatusEnum.VOUCHER_EXPIRED) return ReportVoucherStatusEnum.VOUCHER_EXPIRED;

            return GvcoreConstants.MOP_CODE_VCE.equals(voucher.getMopCode()) ? ReportVoucherStatusEnum.VOUCHER_PURCHASED : ReportVoucherStatusEnum.VOUCHER_ACTIVATED;
        }

        public String toSql() {

            final StringBuilder sql = new StringBuilder();

            //查找detail
            final ExcelReadDetailBean detailBean = LiabilityDataScriptFixData.detailList
                    .stream()
                    .filter(e -> issuerCode.equals(e.getIssuerCode()))
                    .filter(e -> merchantCode.equals(e.getMerchantCode()))
                    .filter(e -> outletCode.equals(e.getOutletCode()))
                    .filter(e -> cpgCode.equals(e.getCpgCode()))
                    .filter(e -> status.getCode().equals(e.getVoucherStatus()))
                    .filter(e -> denomination.compareTo(e.getDenomination()) == 0)
                    .filter(e -> effectiveDate.compareTo(e.getEffectiveDate()) == 0)
                    .max(Comparator.comparingLong(ExcelReadDetailBean::getVoucherCodePageIndex))
                    .orElse(null);

            final String denominationString = denomination.toPlainString();

            // 不存在同类明细 「INSERT」
            if (detailBean == null) {

                this.insertDetail(denominationString, sql);

                //查找summary
                final List<ExcelReadSummaryBean> summaryList = LiabilityDataScriptFixData.summaryList.stream()
                        .filter(e -> issuerCode.equals(e.getIssuerCode()))
                        .filter(e -> merchantCode.equals(e.getMerchantCode()))
                        .filter(e -> outletCode.equals(e.getOutletCode()))
                        .filter(e -> cpgCode.equals(e.getCpgCode()))
                        .collect(Collectors.toList());

                // 不存在同类summary 「INSERT」
                if (CollectionUtils.isEmpty(summaryList)) {

                    insertSummary(sql, denominationString);

                } else {

                    //存在同类Summary 「UPDATE」
                    final ExcelReadSummaryBean summaryBean = summaryList.get(0);

                    sql.append((UPDATE_SUMMARY + WHERE_SUMMARY_ID).replace(DATE, TABLE_CODE)
                            .replace(ID, summaryBean.getId())
                            .replace(DEACTIVATED_DENOMINATION, ReportVoucherStatusEnum.VOUCHER_DEACTIVATED == status ? denominationString : "0")
                            .replace(PURCHASED_DENOMINATION, ReportVoucherStatusEnum.VOUCHER_PURCHASED == status ? denominationString : "0")
                            .replace(ACTIVATED_DENOMINATION, ReportVoucherStatusEnum.VOUCHER_ACTIVATED == status ? denominationString : "0")
                            .replace(EXPIRED_DENOMINATION, ReportVoucherStatusEnum.VOUCHER_EXPIRED == status ? denominationString : "0")
                            .replace(RECENTLY_EXPIRED_DENOMINATION, addRecentlyExpired ? denominationString : "0")
                    ).append("\n");
                }


            } else {

                //存在同类明细 「UPDATE」 SUMMARY DETAIL
                sql.append(UPDATE_DETAIL
                        .replace(DATE, TABLE_CODE)
                        .replace(VOUCHER_CODE, voucherCode)
                        .replace(ID, detailBean.getId().toString())
                ).append("\n");

                sql.append((UPDATE_SUMMARY + WHERE_SUMMARY_ALL).replace(DATE, TABLE_CODE)
                        .replace(ISSUER_CODE, issuerCode)
                        .replace(MERCHANT_CODE, merchantCode)
                        .replace(OUTLET_CODE, outletCode)
                        .replace(CPG_CODE, cpgCode)
                        .replace(DEACTIVATED_DENOMINATION, ReportVoucherStatusEnum.VOUCHER_DEACTIVATED == status ? denominationString : "0")
                        .replace(PURCHASED_DENOMINATION, ReportVoucherStatusEnum.VOUCHER_PURCHASED == status ? denominationString : "0")
                        .replace(ACTIVATED_DENOMINATION, ReportVoucherStatusEnum.VOUCHER_ACTIVATED == status ? denominationString : "0")
                        .replace(EXPIRED_DENOMINATION, ReportVoucherStatusEnum.VOUCHER_EXPIRED == status ? denominationString : "0")
                        .replace(RECENTLY_EXPIRED_DENOMINATION, addRecentlyExpired ? denominationString : "0")
                ).append("\n");

            }

            return sql.toString();
        }

        private void insertSummary(final StringBuilder sql, final String denominationString) {

            final String summaryInsertKey = String.join(",", issuerCode, merchantCode, outletCode, cpgCode);

            StringLock.lockAndRun(summaryInsertKey, () -> {
                if (!INSERT_SUMMARY_HISTORY.contains(summaryInsertKey)) {

                    INSERT_SUMMARY_HISTORY.add(summaryInsertKey);
                    sql.append(INSERT_SUMMARY.replace(DATE, TABLE_CODE)
                            .replace(ISSUER_CODE, issuerCode)
                            .replace(MERCHANT_CODE, merchantCode)
                            .replace(OUTLET_CODE, outletCode)
                            .replace(CPG_CODE, cpgCode)
                            .replace(DEACTIVATED_DENOMINATION, ReportVoucherStatusEnum.VOUCHER_DEACTIVATED == status ? denominationString : "0")
                            .replace(PURCHASED_DENOMINATION, ReportVoucherStatusEnum.VOUCHER_PURCHASED == status ? denominationString : "0")
                            .replace(ACTIVATED_DENOMINATION, ReportVoucherStatusEnum.VOUCHER_ACTIVATED == status ? denominationString : "0")
                            .replace(EXPIRED_DENOMINATION, ReportVoucherStatusEnum.VOUCHER_EXPIRED == status ? denominationString : "0")
                            .replace(RECENTLY_EXPIRED_DENOMINATION, addRecentlyExpired ? denominationString : "0")
                    ).append("\n");

                } else {

                    sql.append((UPDATE_SUMMARY + WHERE_SUMMARY_ALL)
                            .replace(DATE, TABLE_CODE)
                            .replace(ISSUER_CODE, issuerCode)
                            .replace(MERCHANT_CODE, merchantCode)
                            .replace(OUTLET_CODE, outletCode)
                            .replace(CPG_CODE, cpgCode)
                            .replace(DEACTIVATED_DENOMINATION, ReportVoucherStatusEnum.VOUCHER_DEACTIVATED == status ? denominationString : "0")
                            .replace(PURCHASED_DENOMINATION, ReportVoucherStatusEnum.VOUCHER_PURCHASED == status ? denominationString : "0")
                            .replace(ACTIVATED_DENOMINATION, ReportVoucherStatusEnum.VOUCHER_ACTIVATED == status ? denominationString : "0")
                            .replace(EXPIRED_DENOMINATION, ReportVoucherStatusEnum.VOUCHER_EXPIRED == status ? denominationString : "0")
                            .replace(RECENTLY_EXPIRED_DENOMINATION, addRecentlyExpired ? denominationString : "0")
                    ).append("\n");


                }
            });
        }

        private void insertDetail(final String denominationString, final StringBuilder sql) {
            final String detailInsertKey = String.join(",", issuerCode, merchantCode, outletCode, cpgCode, status.getCodeString(), denominationString, String.valueOf(effectiveDate.getTime()));

            StringLock.lockAndRun(detailInsertKey, () -> {
                if (!INSERT_DETAIL_HISTORY.contains(detailInsertKey)) {
                    INSERT_DETAIL_HISTORY.add(detailInsertKey);

                    sql.append(INSERT_DETAIL
                            .replace(DATE, TABLE_CODE)
                            .replace(ISSUER_CODE, issuerCode)
                            .replace(MERCHANT_CODE, merchantCode)
                            .replace(OUTLET_CODE, outletCode)
                            .replace(CPG_CODE, cpgCode)
                            .replace(VOUCHER_CODE, voucherCode)
                            .replace(EFFECTIVE_DATE, DateUtil.format(effectiveDate, "yyyy-MM-dd HH:mm:ss"))
                            .replace(STATUS, status.getCodeString())
                            .replace(DENOMINATION, denominationString)
                    ).append("\n");

                } else {

                    sql.append(UPDATE_DETAIL_BY_INSERT
                            .replace(DATE, TABLE_CODE)
                            .replace(ISSUER_CODE, issuerCode)
                            .replace(MERCHANT_CODE, merchantCode)
                            .replace(OUTLET_CODE, outletCode)
                            .replace(CPG_CODE, cpgCode)
                            .replace(STATUS, status.getCodeString())
                            .replace(DENOMINATION, denominationString)
                            .replace(EFFECTIVE_DATE, DateUtil.format(effectiveDate, "yyyy-MM-dd HH:mm:ss"))
                            .replace(VOUCHER_CODE, voucherCode)
                    ).append("\n");
                }
            });
        }

        private static boolean isExist(final String voucherCode) {

            final long exist = LiabilityDataScriptFixData.detailList.stream()
                    .filter(e -> e.getVoucherCodeSet().contains(voucherCode))
                    .count();

            return exist > 0;
        }

    }


    @Getter
    @Setter
    @Accessors(chain = true)
    public static class VoucherSubtractBean {

        private ExcelReadDetailBean reportTempLiabilityDStructure;
        private String voucherCode;

        public static VoucherSubtractBean newInstance(final String voucherCode) {

            final List<ExcelReadDetailBean> detailList = LiabilityDataScriptFixData.detailList.stream()
                    .filter(e -> e.getVoucherCodeSet().contains(voucherCode))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(detailList)) {
                UN_DELETE_VOUCHER.add(voucherCode);
                log.info("VoucherSubtractBean => 卡券{}不存在, 跳过", voucherCode);
                return null;
            }

            if (detailList.size() != 1) {
                UN_DELETE_VOUCHER.add(voucherCode);
                log.error("VoucherSubtractBean => 卡券{}存在异常请手动处理, 跳过", voucherCode);
                return null;
            }

            final ExcelReadDetailBean reportTempLiabilityDStructure = detailList.get(0);

            return new VoucherSubtractBean().setVoucherCode(voucherCode)
                    .setReportTempLiabilityDStructure(reportTempLiabilityDStructure);
        }

        public static final String DETAIL_UPDATE = "UPDATE gv_report_temp_liability_d_" + DATE + " SET voucher_codes = REPLACE(voucher_codes, '" + VOUCHER_CODE + "', '') WHERE id = '" + ID + "'; ";
        public static final String UPDATE_SUMMARY = "UPDATE gv_report_temp_liability_s_" + DATE + " " +
                " SET activated_amount = activated_amount - " + ACTIVATED_DENOMINATION + ", purchased_amount = purchased_amount - " + PURCHASED_DENOMINATION + ", deactivated_amount = deactivated_amount - " + DEACTIVATED_DENOMINATION + ", " +
                " expired_amount = expired_amount - " + EXPIRED_DENOMINATION + ", recently_expired_amount = recently_expired_amount - " + RECENTLY_EXPIRED_DENOMINATION + " " +
                " WHERE issuer_code = '" + ISSUER_CODE + "' AND merchant_code = '" + MERCHANT_CODE + "' AND outlet_code = '" + OUTLET_CODE + "' AND cpg_code = '" + CPG_CODE + "';";

        public String toSql() {

            final StringBuilder sql = new StringBuilder();

            sql.append(DETAIL_UPDATE.replace(DATE, TABLE_CODE)
                    .replace(VOUCHER_CODE, voucherCode)
                    .replace(ID, reportTempLiabilityDStructure.getId().toString())
            ).append("\n");

            final ReportVoucherStatusEnum status = ReportVoucherStatusEnum.valueOfCode(reportTempLiabilityDStructure.getVoucherStatus());
            final String denomination = reportTempLiabilityDStructure.getDenomination().toPlainString();

            sql.append(UPDATE_SUMMARY.replace(DATE, TABLE_CODE)
                    .replace(ISSUER_CODE, reportTempLiabilityDStructure.getIssuerCode())
                    .replace(MERCHANT_CODE, reportTempLiabilityDStructure.getMerchantCode())
                    .replace(OUTLET_CODE, reportTempLiabilityDStructure.getOutletCode())
                    .replace(CPG_CODE, reportTempLiabilityDStructure.getCpgCode())
                    .replace(DEACTIVATED_DENOMINATION, ReportVoucherStatusEnum.VOUCHER_DEACTIVATED == status ? denomination : "0")
                    .replace(PURCHASED_DENOMINATION, ReportVoucherStatusEnum.VOUCHER_PURCHASED == status ? denomination : "0")
                    .replace(ACTIVATED_DENOMINATION, ReportVoucherStatusEnum.VOUCHER_ACTIVATED == status ? denomination : "0")
                    .replace(EXPIRED_DENOMINATION, ReportVoucherStatusEnum.VOUCHER_EXPIRED == status ? denomination : "0")
                    .replace(RECENTLY_EXPIRED_DENOMINATION, ReportVoucherStatusEnum.VOUCHER_EXPIRED == status && reportTempLiabilityDStructure.getEffectiveDate().getTime() > LAST_END_TIME.getTime() ? denomination : "0")
            ).append("\n");


            return sql.toString();
        }

    }

    @Getter
    @Setter
    public static class ExcelReadDetailBean {

        @ExcelProperty(index = 0, value = "id")
        private Long id;

        @ExcelProperty(index = 1, value = "issuer_code")
        private String issuerCode;

        @ExcelProperty(index = 2, value = "merchant_code")
        private String merchantCode;

        @ExcelProperty(index = 3, value = "outlet_code")
        private String outletCode;

        @ExcelProperty(index = 4, value = "cpg_code")
        private String cpgCode;

        @ExcelProperty(index = 5, value = "voucher_status")
        private Integer voucherStatus;

        @ExcelProperty(index = 6, value = "denomination")
        private BigDecimal denomination;

        @DateTimeFormat("dd/MM/yyyy HH:mm:ss")
        @ExcelProperty(index = 7, value = "effective_date")
        private Date effectiveDate;

        @ExcelProperty(index = 8, value = "voucher_code_page_index")
        private Integer voucherCodePageIndex;

        @ExcelProperty(index = 9, value = "voucher_codes")
        private String voucherCodes;

        @ExcelIgnore
        private Set<String> voucherCodeSet;

        public void setVoucherCodes(final String voucherCodes) {
            this.voucherCodeSet = Arrays.stream(voucherCodes.split(",")).filter(StringUtils::isNotBlank).map(StringUtils::trim).collect(Collectors.toSet());
            this.voucherCodes = voucherCodes;
        }

        public String getIssuerCode() {
            return ConvertUtils.toString(issuerCode, "");
        }

        public String getMerchantCode() {
            return ConvertUtils.toString(merchantCode, "");
        }

        public String getOutletCode() {
            return ConvertUtils.toString(outletCode, "");
        }

        public String getCpgCode() {
            return ConvertUtils.toString(cpgCode, "");
        }

    }

    @Getter
    @Setter
    public static class ExcelReadSummaryBean {

        @ExcelProperty(index = 0, value = "id")
        private String id;

        @ExcelProperty(index = 1, value = "issuer_code")
        private String issuerCode;

        @ExcelProperty(index = 2, value = "merchant_code")
        private String merchantCode;

        @ExcelProperty(index = 3, value = "outlet_code")
        private String outletCode;

        @ExcelProperty(index = 4, value = "cpg_code")
        private String cpgCode;

        @ExcelProperty(index = 5, value = "activated_amount")
        private BigDecimal activatedAmount;

        @ExcelProperty(index = 6, value = "purchased_amount")
        private BigDecimal purchasedAmount;

        @ExcelProperty(index = 7, value = "deactivated_amount")
        private BigDecimal deactivatedAmount;

        @ExcelProperty(index = 8, value = "expired_amount")
        private BigDecimal expiredAmount;

        @ExcelProperty(index = 9, value = "recently_expired_amount")
        private BigDecimal recentlyExpiredAmount;

        public String getIssuerCode() {
            return ConvertUtils.toString(issuerCode, "");
        }

        public String getMerchantCode() {
            return ConvertUtils.toString(merchantCode, "");
        }

        public String getOutletCode() {
            return ConvertUtils.toString(outletCode, "");
        }

        public String getCpgCode() {
            return ConvertUtils.toString(cpgCode, "");
        }
    }


    public static class StringLock {

        // 用于存储字符串对应的锁
        private static final ConcurrentHashMap<String, Lock> lockMap = new ConcurrentHashMap<>();

        /**
         * 获取字符串对应的锁，使用ReentrantLock作为具体的锁实现
         *
         * @param key 要加锁的字符串
         * @return 锁
         */
        private static Lock getLock(String key) {
            return lockMap.computeIfAbsent(key, k -> new ReentrantLock());
        }

        /**
         * 加锁并执行操作
         *
         * @param key      锁的字符串
         * @param runnable 要执行的操作
         */
        public static void lockAndRun(String key, Runnable runnable) {
            Lock lock = getLock(key);
            lock.lock();
            try {
                // 执行关键代码
                runnable.run();
            } finally {
                // 确保锁释放
                lock.unlock();
            }
        }

        /**
         * 移除锁以释放资源，避免锁对象长期存在
         *
         * @param key 需要移除锁的字符串
         */
        public static void removeLock(String key) {
            lockMap.remove(key);
        }


    }


}
