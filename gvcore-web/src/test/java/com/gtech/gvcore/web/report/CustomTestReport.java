package com.gtech.gvcore.web.report;

import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.ReportQueryParam;

/**
 * @ClassName CustomTestReport
 * @Description
 * <AUTHOR>
 * @Date 2023/3/3 14:55
 * @Version V1.0
 **/
public interface CustomTestReport<P extends ReportQueryParam, R> extends BusinessReport<P, R> {

    ReportExportTypeEnum exportTypeEnum();

}
