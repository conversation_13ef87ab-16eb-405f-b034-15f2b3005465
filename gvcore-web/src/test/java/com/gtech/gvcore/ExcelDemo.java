package com.gtech.gvcore;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.commons.utils.StringUtil;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

public class ExcelDemo {

	public static final String PATH = "D:\\Documents\\WXWork\\1688853230977581\\Cache\\File\\2024-09\\GVCompare\\sources\\100k\\";

	public static void main(String[] args) {
//		read1();
		read2();
	}

	private static void read2() {
		try {
			HashSet<String> l9 = read("l9.txt");
			System.out.println("l9 size:" + l9.size());
			HashSet<String> l8 = read("l8.txt");
			System.out.println("l8 size:" + l8.size());
			System.out.println("l9-l8 size:" + (l9.size() - l8.size()));
//			List<String> s9 = readExcel("Sales Detailed Report_OR10240925144755059868(1).xlsx");
//			writeStringToFile(String.join("\n", s9), PATH + "S9.txt");
			HashSet<String> s9 = getList("S9.txt");
			System.out.println("s9 size:" + s9.size());
//			List<String> r9 = readExcel("Redemption Detailed Report_OR10240925144735059867(1).xlsx");
//			writeStringToFile(String.join("\n", r9), PATH + "R9.txt");
			HashSet<String> r9 = getList("R9.txt");
			System.out.println("r9 size:" + r9.size());
			System.out.println("s9-r9 size:" + (s9.size() - r9.size()));

//			System.out.println("l9.size() - l8.size() =>" + (l9.size() - l8.size()));
//			l8.addAll(s9);
//			System.out.println(l8.size());
//			l9.addAll(r9);
//			System.out.println(l9.size());

//			List<String> l8youl9wu = new ArrayList<>();
//			for (String s : l8) {
//				if (!l9.contains(s)) {
//					l8youl9wu.add(s);
//				}
//			}
//			writeStringToFile(String.join("\n", l8youl9wu), PATH + "50k-l8有l9没有.txt");
//			List<String> l9youl8wu = new ArrayList<>();
//			for (String s : l9) {
//				if (!l8.contains(s)) {
//					l9youl8wu.add(s);
//				}
//			}
//			writeStringToFile(String.join("\n", l9youl8wu), PATH + "50k-l9有l8没有.txt");
			HashSet<String> l8l9 = new HashSet<>();
			for (String s : l9) {
				if (!l8.contains(s)) {
					l8l9.add(s);
				}
			}
			for (String s : l8) {
				if (!l9.contains(s)) {
					l8l9.add(s);
				}
			}
			HashSet<String> s9r9 = new HashSet<>();
			for (String s : s9) {
				if (!r9.contains(s)) {
					s9r9.add(s);
				}
			}
			for (String s : r9) {
				if (!s9.contains(s)) {
					s9r9.add(s);
				}
			}
			Set<String> diff = new HashSet<>();
			for (String s : l8l9) {
				if (!s9r9.contains(s)) {
					diff.add(s);
				}
			}
			for (String s : s9r9) {
				if (!l8l9.contains(s)) {
					diff.add(s);
				}
			}
			System.out.println(diff.size());
			writeStringToFile(String.join("\n", diff), PATH + "差异券.txt");

//			writeStringToFile(String.join("\n", l9youl8wu), PATH + "50k-l9有l8没有.txt");

		} catch (Exception e) {
			e.printStackTrace();
		}
	}


	private static void read1() {
		try {
			HashSet<String> l9 = read("l9.txt");
			HashSet<String> l8 = read("l8.txt");
			HashSet<String> s9 = getList("S9.txt");
			HashSet<String> r9 = getList("R9.txt");
			l8.addAll(s9);
//			System.out.println(l8.size());
			l9.addAll(r9);
//			System.out.println(r9.size());
			
			List<String> l8_l9 = new ArrayList<>();
			for (String s : l8) {
				if (!l9.contains(s)) {
					l8_l9.add(s);
				}
			}
			writeStringToFile(String.join("\n", l8), PATH + "l8有l9没有.txt");

			List<String> l9_l8 = new ArrayList<>();
			for (String s : l9) {
				if (!l8.contains(s)) {
					l9_l8.add(s);
				}
			}
			writeStringToFile(String.join("\n", l9), PATH + "l9有l8没有.txt");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static void writeStringToFile(String content, String filePath) throws IOException {
		// 将 String 内容写入指定文件，使用 UTF-8 编码
		Files.write(Paths.get(filePath), Collections.singleton(content), StandardCharsets.UTF_8);
	}

	private static HashSet<String> getList(String url) throws IOException {
		byte[] bytes = Files.readAllBytes(Paths.get(PATH + url));
		String l8 = new String(bytes, StandardCharsets.UTF_8);
		List<String> l8List = Arrays.asList(l8.split("\n")).stream().map(v -> v.replace("\r", "")).filter(x -> !StringUtil.isEmpty(x))
				.collect(Collectors.toList());
		return new HashSet<>(l8List);
	}

	private static HashSet<String> read(String url) throws IOException {
		byte[] bytes = Files.readAllBytes(Paths.get(PATH + url));
		String l8 = new String(bytes, StandardCharsets.UTF_8);
		HashSet<String> all = new HashSet<>();
		List<String> l8List = Arrays.asList(l8.split("\n")).stream().filter(x -> !StringUtil.isEmpty(x)).collect(Collectors.toList());
		l8List.forEach(s -> {
			all.addAll(Arrays.asList(s.split(",")).stream().map(v -> v.replace("\r", "")).filter(x -> !StringUtil.isEmpty(x)).collect(Collectors.toSet()));
		});
		return all;
	}

	public static List<String> readExcel(String url) {
		return EasyExcelFactory.read(PATH + url).head(ExcelData.class).sheet().<ExcelData>doReadSync().stream().map(ExcelData::getVoucherCode)
				.collect(Collectors.toList());
	}

	@Getter
	@Setter
	@NoArgsConstructor
	@AllArgsConstructor
	public static class ExcelData {

		@ExcelProperty(index = 0, value = "Voucher Number")
		private String voucherCode;

	}
}
