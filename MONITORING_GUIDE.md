# 基于注解的对象监控系统使用指南

## 概述

新的监控系统采用基于注解的方式，对代码几乎无侵入，可以自动监控方法中创建的对象，帮助识别内存泄漏和性能问题。

## 主要特性

- ✅ **无侵入性**：只需添加注解，无需修改业务代码
- ✅ **自动监控**：自动追踪常用对象的创建（ArrayList、HashMap等）
- ✅ **灵活配置**：支持阈值、对象类型过滤等配置
- ✅ **多线程支持**：可选择是否监控子线程
- ✅ **性能友好**：只在需要时启用监控，避免性能影响

## 使用方法

### 1. 方法级别监控

在需要监控的方法上添加 `@TrackObjects` 注解：

```java
@TrackObjects(description = "用户数据处理", threshold = 10)
public void processUserData() {
    List<User> users = new ArrayList<>();  // 会被自动监控
    Map<String, Object> cache = new HashMap<>();  // 会被自动监控
    
    // 正常的业务代码，无需任何监控相关代码
    for (int i = 0; i < 100; i++) {
        users.add(new User("user" + i));
        cache.put("key" + i, "value" + i);
    }
}
```

### 2. 类级别监控

在类上添加 `@TrackObjects` 注解，该类的所有public方法都会被监控：

```java
@TrackObjects(description = "数据处理服务", threshold = 5)
@Service
public class DataProcessingService {
    
    public void method1() {
        // 会被监控
    }
    
    public void method2() {
        // 也会被监控
    }
    
    @TrackObjects(enabled = false)
    public void method3() {
        // 不会被监控（方法级别覆盖类级别）
    }
}
```

### 3. API接口监控

在Controller方法上使用注解：

```java
@RestController
public class UserController {
    
    @TrackObjects(description = "获取用户列表", threshold = 20)
    @GetMapping("/users")
    public List<User> getUsers() {
        // API调用会被自动监控对象创建
        return userService.getAllUsers();
    }
}
```

## 注解参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enabled` | boolean | true | 是否启用监控 |
| `description` | String | "" | 监控描述，用于日志标识 |
| `threshold` | int | 0 | 阈值，只有创建对象数量超过此值才记录日志 |
| `objectTypes` | String[] | {} | 要监控的对象类型，为空则监控所有支持的类型 |
| `includeSubThreads` | boolean | false | 是否包含子线程监控 |

## 支持的对象类型

当前支持监控以下对象类型：
- `ArrayList`
- `HashMap`
- `HashSet`
- `LinkedList`
- `TreeMap`
- `TreeSet`
- `ConcurrentHashMap`
- `StringBuilder`
- `StringBuffer`

## 配置选项

在 `application.properties` 中可以配置：

```properties
# 启用基于注解的监控
gv.monitor.annotation.enabled=true

# 启用监控统计日志
gv.monitor.stats.log.enabled=true

# 启用每日统计重置
gv.monitor.stats.reset.enabled=false

# 启用字节码级别监控（高级功能，需要Java Agent）
gv.monitor.bytecode.enabled=false
```

## 监控输出示例

当方法执行完成后，会输出类似以下的监控日志：

```
2025-06-27 10:30:15 INFO  - 🔍 对象追踪结果 - UserController.getUsers: 耗时125ms, 创建对象45个, 详情: {"ArrayList":15,"HashMap":20,"StringBuilder":10}
```

## 性能影响

- **无注解方法**：零性能影响
- **有注解但未达到阈值**：极小性能影响（仅计数）
- **有注解且达到阈值**：轻微性能影响（记录日志）

## 最佳实践

1. **选择性使用**：只在怀疑有内存问题的方法上使用注解
2. **设置合理阈值**：避免过多的日志输出
3. **使用描述性名称**：便于日志分析
4. **定期检查日志**：关注对象创建异常的方法
5. **生产环境谨慎使用**：建议先在测试环境验证

## 故障排除

### 1. 监控不生效
- 检查 `gv.monitor.annotation.enabled=true`
- 确认方法是public的
- 确认注解 `enabled=true`

### 2. 没有日志输出
- 检查是否达到阈值 `threshold`
- 检查日志级别是否为INFO
- 确认创建了支持的对象类型

### 3. 性能问题
- 提高阈值减少日志输出
- 限制监控的对象类型
- 在生产环境禁用监控

## 示例代码

参考以下示例类：
- `MonitoringExample.java` - 方法级别监控示例
- `ClassLevelMonitoringExample.java` - 类级别监控示例

## 技术实现

- 基于Spring AOP实现
- 使用AspectJ切面拦截注解方法
- ThreadLocal存储监控上下文
- 支持多线程环境下的对象追踪
