-- 数据迁移脚本：将现有 yyMM 格式表的数据迁移到新的月份表中
-- 注意：此脚本需要根据实际存在的表进行调整

-- ========================================
-- 查询现有的 liability 表
-- ========================================
/*
使用以下 SQL 查询现有的表：

SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND (TABLE_NAME LIKE 'gv_report_temp_liability_s_%' 
       OR TABLE_NAME LIKE 'gv_report_temp_liability_d_%'
       OR TABLE_NAME LIKE 'gc_report_temp_liability_s_%'
       OR TABLE_NAME LIKE 'gc_report_temp_liability_d_%')
  AND TABLE_NAME NOT LIKE '%_structure'
ORDER BY TABLE_NAME;
*/

-- ========================================
-- 示例迁移脚本（需要根据实际表名调整）
-- ========================================

-- 假设现有表格式为：gv_report_temp_liability_s_2408 (2024年8月)
-- 迁移到：gv_report_temp_liability_s_8 (8月份表)

-- GV Summary 表迁移示例
-- INSERT INTO gv_report_temp_liability_s_8 SELECT * FROM gv_report_temp_liability_s_2408;
-- INSERT INTO gv_report_temp_liability_s_7 SELECT * FROM gv_report_temp_liability_s_2407;
-- INSERT INTO gv_report_temp_liability_s_6 SELECT * FROM gv_report_temp_liability_s_2406;

-- GV Detail 表迁移示例
-- INSERT INTO gv_report_temp_liability_d_8 SELECT * FROM gv_report_temp_liability_d_2408;
-- INSERT INTO gv_report_temp_liability_d_7 SELECT * FROM gv_report_temp_liability_d_2407;
-- INSERT INTO gv_report_temp_liability_d_6 SELECT * FROM gv_report_temp_liability_d_2406;

-- GC Summary 表迁移示例
-- INSERT INTO gc_report_temp_liability_s_8 SELECT * FROM gc_report_temp_liability_s_2408;
-- INSERT INTO gc_report_temp_liability_s_7 SELECT * FROM gc_report_temp_liability_s_2407;
-- INSERT INTO gc_report_temp_liability_s_6 SELECT * FROM gc_report_temp_liability_s_2406;

-- GC Detail 表迁移示例
-- INSERT INTO gc_report_temp_liability_d_8 SELECT * FROM gc_report_temp_liability_d_2408;
-- INSERT INTO gc_report_temp_liability_d_7 SELECT * FROM gc_report_temp_liability_d_2407;
-- INSERT INTO gc_report_temp_liability_d_6 SELECT * FROM gc_report_temp_liability_d_2406;

-- ========================================
-- 动态生成迁移脚本的存储过程
-- ========================================

DELIMITER $$

CREATE PROCEDURE GenerateMigrationScript()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE table_name VARCHAR(255);
    DECLARE month_num INT;
    DECLARE table_type VARCHAR(10);
    DECLARE table_prefix VARCHAR(50);
    
    -- 声明游标
    DECLARE cur CURSOR FOR 
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = DATABASE() 
          AND (TABLE_NAME LIKE 'gv_report_temp_liability_s_%' 
               OR TABLE_NAME LIKE 'gv_report_temp_liability_d_%'
               OR TABLE_NAME LIKE 'gc_report_temp_liability_s_%'
               OR TABLE_NAME LIKE 'gc_report_temp_liability_d_%')
          AND TABLE_NAME NOT LIKE '%_structure'
          AND TABLE_NAME REGEXP '.*_[0-9]{4}$';  -- 匹配 yyMM 格式
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- 打开游标
    OPEN cur;
    
    read_loop: LOOP
        FETCH cur INTO table_name;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 解析表名获取月份
        SET month_num = CAST(RIGHT(table_name, 2) AS UNSIGNED);
        
        -- 确定表类型和前缀
        IF table_name LIKE 'gv_report_temp_liability_s_%' THEN
            SET table_type = 's';
            SET table_prefix = 'gv_report_temp_liability';
        ELSEIF table_name LIKE 'gv_report_temp_liability_d_%' THEN
            SET table_type = 'd';
            SET table_prefix = 'gv_report_temp_liability';
        ELSEIF table_name LIKE 'gc_report_temp_liability_s_%' THEN
            SET table_type = 's';
            SET table_prefix = 'gc_report_temp_liability';
        ELSEIF table_name LIKE 'gc_report_temp_liability_d_%' THEN
            SET table_type = 'd';
            SET table_prefix = 'gc_report_temp_liability';
        END IF;
        
        -- 生成迁移 SQL
        SELECT CONCAT(
            'INSERT INTO ', table_prefix, '_', table_type, '_', month_num, 
            ' SELECT * FROM ', table_name, ';'
        ) AS migration_sql;
        
    END LOOP;
    
    CLOSE cur;
END$$

DELIMITER ;

-- 执行存储过程生成迁移脚本
-- CALL GenerateMigrationScript();

-- 执行完成后删除存储过程
-- DROP PROCEDURE IF EXISTS GenerateMigrationScript;

-- ========================================
-- 验证迁移结果
-- ========================================

-- 验证数据迁移是否成功的查询示例
/*
-- 检查各月份表的数据量
SELECT 'gv_s_1' as table_name, COUNT(*) as record_count FROM gv_report_temp_liability_s_1
UNION ALL
SELECT 'gv_s_2', COUNT(*) FROM gv_report_temp_liability_s_2
UNION ALL
SELECT 'gv_s_3', COUNT(*) FROM gv_report_temp_liability_s_3
-- ... 继续其他月份

-- 检查原表和新表数据是否一致
-- SELECT COUNT(*) FROM gv_report_temp_liability_s_2408;  -- 原表
-- SELECT COUNT(*) FROM gv_report_temp_liability_s_8;     -- 新表
*/

-- ========================================
-- 清理旧表（可选，建议先备份）
-- ========================================
/*
-- 在确认迁移成功后，可以删除旧的 yyMM 格式表
-- DROP TABLE IF EXISTS gv_report_temp_liability_s_2408;
-- DROP TABLE IF EXISTS gv_report_temp_liability_d_2408;
-- DROP TABLE IF EXISTS gc_report_temp_liability_s_2408;
-- DROP TABLE IF EXISTS gc_report_temp_liability_d_2408;
-- ... 其他旧表
*/
