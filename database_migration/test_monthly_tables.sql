-- 测试脚本：验证月份表的创建和数据操作

-- ========================================
-- 1. 验证表是否创建成功
-- ========================================

-- 检查所有月份表是否存在
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    CREATE_TIME
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND (TABLE_NAME LIKE 'gv_report_temp_liability_s_%' 
       OR TABLE_NAME LIKE 'gv_report_temp_liability_d_%'
       OR TABLE_NAME LIKE 'gc_report_temp_liability_s_%'
       OR TABLE_NAME LIKE 'gc_report_temp_liability_d_%')
  AND TABLE_NAME NOT LIKE '%_structure'
  AND TABLE_NAME REGEXP '.*_[0-9]{1,2}$'  -- 匹配月份格式 1-12
ORDER BY TABLE_NAME;

-- ========================================
-- 2. 验证表结构是否正确
-- ========================================

-- 检查 GV Summary 表结构（以1月为例）
DESCRIBE gv_report_temp_liability_s_1;

-- 检查 GV Detail 表结构（以1月为例）
DESCRIBE gv_report_temp_liability_d_1;

-- 检查 GC Summary 表结构（以1月为例）
DESCRIBE gc_report_temp_liability_s_1;

-- 检查 GC Detail 表结构（以1月为例）
DESCRIBE gc_report_temp_liability_d_1;

-- ========================================
-- 3. 测试数据插入
-- ========================================

-- 测试插入数据到当前月份表（假设当前是8月）
-- 注意：这里的数据仅用于测试，实际数据由定时任务生成

-- 测试 GV Summary 表插入
INSERT INTO gv_report_temp_liability_s_8 (
    issuer_code, merchant_code, outlet_code, cpg_code,
    activated_amount, purchased_amount, deactivated_amount, 
    expired_amount, recently_expired_amount
) VALUES (
    'TEST_ISSUER', 'TEST_MERCHANT', 'TEST_OUTLET', 'TEST_CPG',
    1000.00, 2000.00, 100.00, 50.00, 25.00
);

-- 测试 GV Detail 表插入
INSERT INTO gv_report_temp_liability_d_8 (
    issuer_code, merchant_code, outlet_code, cpg_code,
    voucher_status, denomination, effective_date,
    voucher_code_page_index, voucher_codes
) VALUES (
    'TEST_ISSUER', 'TEST_MERCHANT', 'TEST_OUTLET', 'TEST_CPG',
    1, 100.00, NOW(),
    1, 'TEST001,TEST002,TEST003'
);

-- 测试 GC Summary 表插入
INSERT INTO gc_report_temp_liability_s_8 (
    issuer_code, merchant_code, outlet_code, cpg_code,
    activated_amount, purchased_amount, deactivated_amount,
    expired_amount, total_amount, expiry_date
) VALUES (
    'TEST_ISSUER', 'TEST_MERCHANT', 'TEST_OUTLET', 'TEST_CPG',
    1000.00, 2000.00, 100.00, 50.00, 2950.00, NOW()
);

-- 测试 GC Detail 表插入
INSERT INTO gc_report_temp_liability_d_8 (
    issuer_code, merchant_code, outlet_code, cpg_code,
    voucher_status, denomination, expiry_date, balance,
    voucher_code_page_index, voucher_codes
) VALUES (
    'TEST_ISSUER', 'TEST_MERCHANT', 'TEST_OUTLET', 'TEST_CPG',
    'ACTIVATED', 100.00, NOW(), 100.00,
    1, 'GC001,GC002,GC003'
);

-- ========================================
-- 4. 验证数据插入结果
-- ========================================

-- 检查各表的数据量
SELECT 'gv_s_8' as table_name, COUNT(*) as record_count FROM gv_report_temp_liability_s_8
UNION ALL
SELECT 'gv_d_8', COUNT(*) FROM gv_report_temp_liability_d_8
UNION ALL
SELECT 'gc_s_8', COUNT(*) FROM gc_report_temp_liability_s_8
UNION ALL
SELECT 'gc_d_8', COUNT(*) FROM gc_report_temp_liability_d_8;

-- 查看插入的测试数据
SELECT 'GV Summary' as table_type, issuer_code, merchant_code, activated_amount 
FROM gv_report_temp_liability_s_8 WHERE issuer_code = 'TEST_ISSUER'
UNION ALL
SELECT 'GV Detail', issuer_code, merchant_code, denomination 
FROM gv_report_temp_liability_d_8 WHERE issuer_code = 'TEST_ISSUER'
UNION ALL
SELECT 'GC Summary', issuer_code, merchant_code, activated_amount 
FROM gc_report_temp_liability_s_8 WHERE issuer_code = 'TEST_ISSUER'
UNION ALL
SELECT 'GC Detail', issuer_code, merchant_code, denomination 
FROM gc_report_temp_liability_d_8 WHERE issuer_code = 'TEST_ISSUER';

-- ========================================
-- 5. 清理测试数据
-- ========================================

-- 删除测试数据
DELETE FROM gv_report_temp_liability_s_8 WHERE issuer_code = 'TEST_ISSUER';
DELETE FROM gv_report_temp_liability_d_8 WHERE issuer_code = 'TEST_ISSUER';
DELETE FROM gc_report_temp_liability_s_8 WHERE issuer_code = 'TEST_ISSUER';
DELETE FROM gc_report_temp_liability_d_8 WHERE issuer_code = 'TEST_ISSUER';

-- 验证清理结果
SELECT 'After cleanup' as status,
       (SELECT COUNT(*) FROM gv_report_temp_liability_s_8 WHERE issuer_code = 'TEST_ISSUER') as gv_s_count,
       (SELECT COUNT(*) FROM gv_report_temp_liability_d_8 WHERE issuer_code = 'TEST_ISSUER') as gv_d_count,
       (SELECT COUNT(*) FROM gc_report_temp_liability_s_8 WHERE issuer_code = 'TEST_ISSUER') as gc_s_count,
       (SELECT COUNT(*) FROM gc_report_temp_liability_d_8 WHERE issuer_code = 'TEST_ISSUER') as gc_d_count;

-- ========================================
-- 6. 性能测试查询
-- ========================================

-- 测试查询性能（根据实际数据情况调整）
EXPLAIN SELECT * FROM gv_report_temp_liability_s_8 WHERE cpg_code = 'TEST_CPG';
EXPLAIN SELECT * FROM gv_report_temp_liability_d_8 WHERE issuer_code = 'TEST_ISSUER';

-- ========================================
-- 使用说明
-- ========================================
/*
执行步骤：
1. 先执行 create_monthly_liability_tables.sql 创建表
2. 执行本测试脚本验证表创建和基本功能
3. 部署代码改动
4. 观察定时任务是否正常写入数据到对应月份表

注意事项：
- 测试数据中的月份（8）需要根据当前实际月份调整
- 在生产环境执行前，请先在测试环境验证
- 确保有足够的数据库权限执行这些操作
*/
