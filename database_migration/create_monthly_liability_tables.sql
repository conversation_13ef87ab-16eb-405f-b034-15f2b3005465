-- 创建12个月份的 liability 报表表
-- 改造说明：将原来的 yyMM 格式改为固定的 1-12 月份表

-- ========================================
-- GV Liability Tables (1-12)
-- ========================================

-- GV Liability Summary Tables
CREATE TABLE gv_report_temp_liability_s_1 LIKE gv_report_temp_liability_s_structure;
CREATE TABLE gv_report_temp_liability_s_2 LIKE gv_report_temp_liability_s_structure;
CREATE TABLE gv_report_temp_liability_s_3 LIKE gv_report_temp_liability_s_structure;
CREATE TABLE gv_report_temp_liability_s_4 LIKE gv_report_temp_liability_s_structure;
CREATE TABLE gv_report_temp_liability_s_5 LIKE gv_report_temp_liability_s_structure;
CREATE TABLE gv_report_temp_liability_s_6 LIKE gv_report_temp_liability_s_structure;
CREATE TABLE gv_report_temp_liability_s_7 LIKE gv_report_temp_liability_s_structure;
CREATE TABLE gv_report_temp_liability_s_8 LIKE gv_report_temp_liability_s_structure;
CREATE TABLE gv_report_temp_liability_s_9 LIKE gv_report_temp_liability_s_structure;
CREATE TABLE gv_report_temp_liability_s_10 LIKE gv_report_temp_liability_s_structure;
CREATE TABLE gv_report_temp_liability_s_11 LIKE gv_report_temp_liability_s_structure;
CREATE TABLE gv_report_temp_liability_s_12 LIKE gv_report_temp_liability_s_structure;

-- GV Liability Detail Tables
CREATE TABLE gv_report_temp_liability_d_1 LIKE gv_report_temp_liability_d_structure;
CREATE TABLE gv_report_temp_liability_d_2 LIKE gv_report_temp_liability_d_structure;
CREATE TABLE gv_report_temp_liability_d_3 LIKE gv_report_temp_liability_d_structure;
CREATE TABLE gv_report_temp_liability_d_4 LIKE gv_report_temp_liability_d_structure;
CREATE TABLE gv_report_temp_liability_d_5 LIKE gv_report_temp_liability_d_structure;
CREATE TABLE gv_report_temp_liability_d_6 LIKE gv_report_temp_liability_d_structure;
CREATE TABLE gv_report_temp_liability_d_7 LIKE gv_report_temp_liability_d_structure;
CREATE TABLE gv_report_temp_liability_d_8 LIKE gv_report_temp_liability_d_structure;
CREATE TABLE gv_report_temp_liability_d_9 LIKE gv_report_temp_liability_d_structure;
CREATE TABLE gv_report_temp_liability_d_10 LIKE gv_report_temp_liability_d_structure;
CREATE TABLE gv_report_temp_liability_d_11 LIKE gv_report_temp_liability_d_structure;
CREATE TABLE gv_report_temp_liability_d_12 LIKE gv_report_temp_liability_d_structure;

-- ========================================
-- GC Liability Tables (1-12)
-- ========================================

-- GC Liability Summary Tables
CREATE TABLE gc_report_temp_liability_s_1 LIKE gc_report_temp_liability_s_structure;
CREATE TABLE gc_report_temp_liability_s_2 LIKE gc_report_temp_liability_s_structure;
CREATE TABLE gc_report_temp_liability_s_3 LIKE gc_report_temp_liability_s_structure;
CREATE TABLE gc_report_temp_liability_s_4 LIKE gc_report_temp_liability_s_structure;
CREATE TABLE gc_report_temp_liability_s_5 LIKE gc_report_temp_liability_s_structure;
CREATE TABLE gc_report_temp_liability_s_6 LIKE gc_report_temp_liability_s_structure;
CREATE TABLE gc_report_temp_liability_s_7 LIKE gc_report_temp_liability_s_structure;
CREATE TABLE gc_report_temp_liability_s_8 LIKE gc_report_temp_liability_s_structure;
CREATE TABLE gc_report_temp_liability_s_9 LIKE gc_report_temp_liability_s_structure;
CREATE TABLE gc_report_temp_liability_s_10 LIKE gc_report_temp_liability_s_structure;
CREATE TABLE gc_report_temp_liability_s_11 LIKE gc_report_temp_liability_s_structure;
CREATE TABLE gc_report_temp_liability_s_12 LIKE gc_report_temp_liability_s_structure;

-- GC Liability Detail Tables
CREATE TABLE gc_report_temp_liability_d_1 LIKE gc_report_temp_liability_d_structure;
CREATE TABLE gc_report_temp_liability_d_2 LIKE gc_report_temp_liability_d_structure;
CREATE TABLE gc_report_temp_liability_d_3 LIKE gc_report_temp_liability_d_structure;
CREATE TABLE gc_report_temp_liability_d_4 LIKE gc_report_temp_liability_d_structure;
CREATE TABLE gc_report_temp_liability_d_5 LIKE gc_report_temp_liability_d_structure;
CREATE TABLE gc_report_temp_liability_d_6 LIKE gc_report_temp_liability_d_structure;
CREATE TABLE gc_report_temp_liability_d_7 LIKE gc_report_temp_liability_d_structure;
CREATE TABLE gc_report_temp_liability_d_8 LIKE gc_report_temp_liability_d_structure;
CREATE TABLE gc_report_temp_liability_d_9 LIKE gc_report_temp_liability_d_structure;
CREATE TABLE gc_report_temp_liability_d_10 LIKE gc_report_temp_liability_d_structure;
CREATE TABLE gc_report_temp_liability_d_11 LIKE gc_report_temp_liability_d_structure;
CREATE TABLE gc_report_temp_liability_d_12 LIKE gc_report_temp_liability_d_structure;

-- ========================================
-- 添加表注释
-- ========================================

-- GV Tables Comments
ALTER TABLE gv_report_temp_liability_s_1 COMMENT = 'GV Liability Summary Report - January';
ALTER TABLE gv_report_temp_liability_s_2 COMMENT = 'GV Liability Summary Report - February';
ALTER TABLE gv_report_temp_liability_s_3 COMMENT = 'GV Liability Summary Report - March';
ALTER TABLE gv_report_temp_liability_s_4 COMMENT = 'GV Liability Summary Report - April';
ALTER TABLE gv_report_temp_liability_s_5 COMMENT = 'GV Liability Summary Report - May';
ALTER TABLE gv_report_temp_liability_s_6 COMMENT = 'GV Liability Summary Report - June';
ALTER TABLE gv_report_temp_liability_s_7 COMMENT = 'GV Liability Summary Report - July';
ALTER TABLE gv_report_temp_liability_s_8 COMMENT = 'GV Liability Summary Report - August';
ALTER TABLE gv_report_temp_liability_s_9 COMMENT = 'GV Liability Summary Report - September';
ALTER TABLE gv_report_temp_liability_s_10 COMMENT = 'GV Liability Summary Report - October';
ALTER TABLE gv_report_temp_liability_s_11 COMMENT = 'GV Liability Summary Report - November';
ALTER TABLE gv_report_temp_liability_s_12 COMMENT = 'GV Liability Summary Report - December';

ALTER TABLE gv_report_temp_liability_d_1 COMMENT = 'GV Liability Detail Report - January';
ALTER TABLE gv_report_temp_liability_d_2 COMMENT = 'GV Liability Detail Report - February';
ALTER TABLE gv_report_temp_liability_d_3 COMMENT = 'GV Liability Detail Report - March';
ALTER TABLE gv_report_temp_liability_d_4 COMMENT = 'GV Liability Detail Report - April';
ALTER TABLE gv_report_temp_liability_d_5 COMMENT = 'GV Liability Detail Report - May';
ALTER TABLE gv_report_temp_liability_d_6 COMMENT = 'GV Liability Detail Report - June';
ALTER TABLE gv_report_temp_liability_d_7 COMMENT = 'GV Liability Detail Report - July';
ALTER TABLE gv_report_temp_liability_d_8 COMMENT = 'GV Liability Detail Report - August';
ALTER TABLE gv_report_temp_liability_d_9 COMMENT = 'GV Liability Detail Report - September';
ALTER TABLE gv_report_temp_liability_d_10 COMMENT = 'GV Liability Detail Report - October';
ALTER TABLE gv_report_temp_liability_d_11 COMMENT = 'GV Liability Detail Report - November';
ALTER TABLE gv_report_temp_liability_d_12 COMMENT = 'GV Liability Detail Report - December';

-- GC Tables Comments
ALTER TABLE gc_report_temp_liability_s_1 COMMENT = 'GC Liability Summary Report - January';
ALTER TABLE gc_report_temp_liability_s_2 COMMENT = 'GC Liability Summary Report - February';
ALTER TABLE gc_report_temp_liability_s_3 COMMENT = 'GC Liability Summary Report - March';
ALTER TABLE gc_report_temp_liability_s_4 COMMENT = 'GC Liability Summary Report - April';
ALTER TABLE gc_report_temp_liability_s_5 COMMENT = 'GC Liability Summary Report - May';
ALTER TABLE gc_report_temp_liability_s_6 COMMENT = 'GC Liability Summary Report - June';
ALTER TABLE gc_report_temp_liability_s_7 COMMENT = 'GC Liability Summary Report - July';
ALTER TABLE gc_report_temp_liability_s_8 COMMENT = 'GC Liability Summary Report - August';
ALTER TABLE gc_report_temp_liability_s_9 COMMENT = 'GC Liability Summary Report - September';
ALTER TABLE gc_report_temp_liability_s_10 COMMENT = 'GC Liability Summary Report - October';
ALTER TABLE gc_report_temp_liability_s_11 COMMENT = 'GC Liability Summary Report - November';
ALTER TABLE gc_report_temp_liability_s_12 COMMENT = 'GC Liability Summary Report - December';

ALTER TABLE gc_report_temp_liability_d_1 COMMENT = 'GC Liability Detail Report - January';
ALTER TABLE gc_report_temp_liability_d_2 COMMENT = 'GC Liability Detail Report - February';
ALTER TABLE gc_report_temp_liability_d_3 COMMENT = 'GC Liability Detail Report - March';
ALTER TABLE gc_report_temp_liability_d_4 COMMENT = 'GC Liability Detail Report - April';
ALTER TABLE gc_report_temp_liability_d_5 COMMENT = 'GC Liability Detail Report - May';
ALTER TABLE gc_report_temp_liability_d_6 COMMENT = 'GC Liability Detail Report - June';
ALTER TABLE gc_report_temp_liability_d_7 COMMENT = 'GC Liability Detail Report - July';
ALTER TABLE gc_report_temp_liability_d_8 COMMENT = 'GC Liability Detail Report - August';
ALTER TABLE gc_report_temp_liability_d_9 COMMENT = 'GC Liability Detail Report - September';
ALTER TABLE gc_report_temp_liability_d_10 COMMENT = 'GC Liability Detail Report - October';
ALTER TABLE gc_report_temp_liability_d_11 COMMENT = 'GC Liability Detail Report - November';
ALTER TABLE gc_report_temp_liability_d_12 COMMENT = 'GC Liability Detail Report - December';

-- ========================================
-- 使用说明
-- ========================================
/*
改造说明：
1. 原来使用 yyMM 格式（如 2408 表示2024年8月）
2. 现在改为固定的 1-12 月份表
3. 数据存储逻辑保持不变：
   - 每月1号凌晨执行定时任务
   - 查询条件：create_time < 当月1号00:00:00
   - 存储上月及之前的所有数据到当月表中
4. 查询逻辑：根据查询日期的月份来确定查询哪个表

执行顺序：
1. 先执行此 SQL 脚本创建12个月份的表
2. 部署代码改动
3. 可选：迁移现有 yyMM 格式表的数据到对应月份表中
*/
