# Liability 报表表结构改造

## 改造目标

将 `gv_report_temp_liability_d` 和 `gv_report_temp_liability_s` 以及对应的 `gc` 表从当前的 `yyMM` 格式改造为固定的12个月份表（1-12）。

## 当前逻辑分析

### 现有逻辑
- **表名格式**：`gv_report_temp_liability_{type}_{yyMM}` (如 `gv_report_temp_liability_s_2408` 表示2024年8月)
- **定时任务**：每月1号凌晨执行 `@Scheduled(cron = "0 0 0 1 * ?")`
- **数据存储**：查询条件 `create_time < 当月1号00:00:00`，存储上月及之前的所有数据
- **查询逻辑**：根据查询参数的 `transactionDateStart` 生成对应月份的 tableCode

### 改造后逻辑
- **表名格式**：`gv_report_temp_liability_{type}_{1-12}` (如 `gv_report_temp_liability_s_8` 表示8月)
- **定时任务**：保持不变，每月1号凌晨执行
- **数据存储**：逻辑保持不变，存储上月及之前的数据到当月对应的月份表中
- **查询逻辑**：根据查询日期的月份来确定查询哪个表

## 代码改动

### 修改的文件

1. **LiabilityDataScript.java**
   - 修改 `TABLE_CODE_DATE_FORMAT = "M"` (从 "yyMM" 改为 "M")

2. **GcLiabilityDataScript.java**
   - 修改 `TABLE_CODE_DATE_FORMAT = "M"` (从 "yyMM" 改为 "M")

3. **LiabilitySummaryImpl.java**
   - 查询时使用新的日期格式

4. **LiabilityDetailedImpl.java**
   - 查询时使用新的日期格式

5. **GcLiabilitySummaryImpl.java**
   - 查询时使用新的日期格式

6. **GcLiabilityDetailedImpl.java**
   - 查询时使用新的日期格式

### 关键改动点

```java
// 原来
public static final String TABLE_CODE_DATE_FORMAT = "yyMM";

// 改为
public static final String TABLE_CODE_DATE_FORMAT = "M"; // 改为月份格式 1-12
```

**重要修改：日期解析逻辑**

由于 tableCode 从 "yyMM" 格式改为 "M" 格式，原来的 `DateUtil.parseDate(tableCode, TABLE_CODE_DATE_FORMAT)` 无法正确解析单个月份数字。因此修改了以下逻辑：

```java
// 原来
final Date now = DateUtil.parseDate(tableCode, TABLE_CODE_DATE_FORMAT);

// 改为
final Date now = new Date(); // 直接使用当前日期
```

这样保持了原有的数据处理逻辑，但使用更简单直接的方式获取当前时间。

## 数据库改动

### 新建表结构

执行 `create_monthly_liability_tables.sql` 创建以下表：

**GV 表：**
- `gv_report_temp_liability_s_1` ~ `gv_report_temp_liability_s_12` (汇总表)
- `gv_report_temp_liability_d_1` ~ `gv_report_temp_liability_d_12` (详细表)

**GC 表：**
- `gc_report_temp_liability_s_1` ~ `gc_report_temp_liability_s_12` (汇总表)
- `gc_report_temp_liability_d_1` ~ `gc_report_temp_liability_d_12` (详细表)

### 数据迁移

使用 `migrate_existing_liability_data.sql` 将现有数据迁移到新表中。

## 部署步骤

### 1. 数据库准备
```sql
-- 1. 创建新的月份表
source create_monthly_liability_tables.sql;

-- 2. 查看现有表
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND (TABLE_NAME LIKE 'gv_report_temp_liability_s_%' 
       OR TABLE_NAME LIKE 'gv_report_temp_liability_d_%'
       OR TABLE_NAME LIKE 'gc_report_temp_liability_s_%'
       OR TABLE_NAME LIKE 'gc_report_temp_liability_d_%')
  AND TABLE_NAME NOT LIKE '%_structure'
ORDER BY TABLE_NAME;

-- 3. 根据实际情况执行数据迁移
-- 参考 migrate_existing_liability_data.sql 中的示例
```

### 2. 代码部署
1. 部署包含改动的代码
2. 重启应用服务

### 3. 验证
1. 检查定时任务是否正常执行
2. 验证数据是否正确写入到对应月份表
3. 测试报表查询功能

## 注意事项

### 1. 数据一致性
- 改造期间确保没有正在运行的定时任务
- 建议在维护窗口期间执行

### 2. 回滚方案
- 保留原有的 yyMM 格式表作为备份
- 代码可以快速回滚到原版本

### 3. 监控要点
- 定时任务执行状态
- 新表的数据写入情况
- 报表查询性能

## 测试建议

### 1. 单元测试
- 测试 tableCode 生成逻辑
- 测试数据写入到正确的月份表

### 2. 集成测试
- 测试完整的定时任务流程
- 测试报表查询功能

### 3. 性能测试
- 对比改造前后的查询性能
- 验证12个固定表的性能表现

## 优势

1. **表结构固定**：不再需要动态创建表，12个固定表便于管理
2. **查询简化**：月份表名更直观，便于运维和排查
3. **数据分布**：数据按月份均匀分布，便于维护和备份
4. **兼容性好**：保持原有的数据存储和查询逻辑
