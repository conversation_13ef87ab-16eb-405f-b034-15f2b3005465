package com.gtech.gvcore.helper;

import com.gtech.gvcore.common.exception.GvcoreParamValidateException;
import com.gtech.gvcore.common.response.useraccount.PermissionCodeResponse;
import com.gtech.gvcore.service.GvUserAccountService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName PermissionHelper
 * @Description PermissionHelper
 * <AUTHOR>
 * @Date 2022/7/29 16:46
 * @Version V1.0
 **/
@Component
public class PermissionHelper {

    @Autowired
    private GvUserAccountService userAccountService;

    /**
     * 获取用户指定issueCode下的权限信息
     *
     * @param userCode  用户编码 非空
     * @param issueCode issue 编码 非空
     * @return com.gtech.gvcore.common.response.useraccount.PermissionCodeResponse 权限信息
     * <AUTHOR>
     * @date 2022/7/29 17:00
     * @since 1.0.0
     */
    public PermissionCodeResponse getPermission(final String userCode, final String issueCode) {

        if (StringUtils.isBlank(userCode) || StringUtils.isBlank(issueCode)) {
            throw new GvcoreParamValidateException();
        }

        final List<PermissionCodeResponse> permissionList = this.userAccountService.queryPerrmissionCodeList(userCode)
                .stream()
                .filter(permission -> permission.getIssuerCode().equals(issueCode) && Boolean.TRUE.equals(permission.getHaveIssuer()))
                .filter(permission -> Boolean.TRUE.equals(permission.getHaveIssuer()))
                .collect(Collectors.toList());

        return CollectionUtils.isEmpty(permissionList) ? null : permissionList.get(0);
    }

}