package com.gtech.gvcore.helper;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import net.lingala.zip4j.io.outputstream.ZipOutputStream;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.model.enums.AesKeyStrength;
import net.lingala.zip4j.model.enums.CompressionMethod;
import net.lingala.zip4j.model.enums.EncryptionMethod;
import org.apache.commons.lang3.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName ZipHelper
 * @Description FileCompressionHelper
 * <AUTHOR>
 * @Date 2022/7/22 10:35
 * @Version V1.0
 **/
public class FileCompressionHelper {

    private static final String PARAMETER_VALIDATE_ERROR_MESSAGE = "Parameter missing: %s";
    private static final String PARAMETER_FILE_URL = "fileUrl";
    private static final String PARAMETER_FILE_NAME = "fileName";

    public static InputStream zip(final CompressionParameter parameter) throws IOException {
        final CompressionParameter simpleCompressionParameter = CompressionParameter.builder()
                .fileName(parameter.getFileName())
                .fileUrl(parameter.getFileUrl())
                .build();

        return FileCompressionHelper.zip(Collections.singletonList(simpleCompressionParameter));
    }

    public static InputStream zipEncrypt(final EncryptCompressionParameter parameter) throws IOException {
        final CompressionParameter simpleCompressionParameter = CompressionParameter.builder()
                .fileName(parameter.getFileName())
                .fileUrl(parameter.getFileUrl())
                .build();

        return FileCompressionHelper.zipEncrypt(Collections.singletonList(simpleCompressionParameter), parameter.getPassword());
    }

    public static InputStream zip(final List<CompressionParameter> parameterList) throws IOException {
        parameterList.forEach(CompressionParameter::validate);
        return FileCompressionHelper.zip(parameterList, false, null);
    }

    public static InputStream zipEncrypt(final List<CompressionParameter> parameterList, final String password) throws IOException {
        parameterList.forEach(CompressionParameter::validate);
        return FileCompressionHelper.zip(parameterList, true, password);
    }

    /**
     * 压缩为zip
     * 如果需要加密,将使用标准zip加密算法进行
     *
     * @param parameterList 多个文件参数
     * @param encrypt       是否需要加密,如果不需要,password可以为null
     * @param password      压缩密码.
     * @return java.io.InputStream 压缩后的压缩文件输入流
     * <AUTHOR>
     * @date 2022/7/22 12:07
     * @since 1.0.0
     */
    private static InputStream zip(final Collection<CompressionParameter> parameterList, final boolean encrypt, final String password) throws IOException {

        final ZipParameters zipParameters = FileCompressionHelper.buildZipParameters(CompressionMethod.DEFLATE, encrypt, EncryptionMethod.ZIP_STANDARD, null);

        final byte[] buff = new byte[4096];
        int readLen;

        final ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try (final ZipOutputStream zos = FileCompressionHelper.initializeZipOutputStream(encrypt, password, outputStream)) {

            for (final CompressionParameter fileInfo : parameterList) {

                zipParameters.setFileNameInZip(fileInfo.getFileName());
                zos.putNextEntry(zipParameters);

                try (InputStream inputStream = new URL(fileInfo.getFileUrl()).openStream()) {
                    while ((readLen = inputStream.read(buff)) != -1) {
                        zos.write(buff, 0, readLen);
                    }
                }
                zos.closeEntry();
            }
        }

        return new ByteArrayInputStream(outputStream.toByteArray());
    }


    private static ZipOutputStream initializeZipOutputStream(final boolean encrypt, final String password, final ByteArrayOutputStream outputStream) throws IOException {

        if (encrypt) {
            return new ZipOutputStream(outputStream, password.toCharArray());
        }

        return new ZipOutputStream(outputStream);
    }

    private static ZipParameters buildZipParameters(final CompressionMethod compressionMethod, final boolean encrypt, final EncryptionMethod encryptionMethod, final AesKeyStrength aesKeyStrength) {

        ZipParameters zipParameters = new ZipParameters();
        zipParameters.setCompressionMethod(compressionMethod);
        zipParameters.setEncryptionMethod(encryptionMethod);
        zipParameters.setAesKeyStrength(aesKeyStrength);
        zipParameters.setEncryptFiles(encrypt);
        return zipParameters;
    }

    @Getter
    @Builder
    public static class EncryptCompressionParameter {
        /**
         * 文件路径
         */
        private String fileUrl;

        /**
         * fileUrl对应的文件文件名称,需要包含完整后缀名
         */
        private String fileName;

        /**
         * 压缩密码.不可为空
         */
        private String password;

        public void validate() {
            if (StringUtils.isBlank(this.fileUrl)) {
                throw new IllegalArgumentException(String.format(PARAMETER_VALIDATE_ERROR_MESSAGE, PARAMETER_FILE_URL));
            }
            if (StringUtils.isBlank(this.fileName)) {
                throw new IllegalArgumentException(String.format(PARAMETER_VALIDATE_ERROR_MESSAGE, PARAMETER_FILE_NAME));
            }
            if (StringUtils.isBlank(this.password)) {
                throw new IllegalArgumentException(String.format(PARAMETER_VALIDATE_ERROR_MESSAGE, "password"));
            }
        }
    }

    @Getter
    @Builder
    @Setter
    public static class CompressionParameter {
        /**
         * 文件路径
         */
        private String fileUrl;

        /**
         * fileUrl对应的文件文件名称,需要包含完整后缀名
         */
        private String fileName;

        public void validate() {
            if (StringUtils.isBlank(this.fileUrl)) {
                throw new IllegalArgumentException(String.format(PARAMETER_VALIDATE_ERROR_MESSAGE, PARAMETER_FILE_URL));
            }
            if (StringUtils.isBlank(this.fileName)) {
                throw new IllegalArgumentException(String.format(PARAMETER_VALIDATE_ERROR_MESSAGE, PARAMETER_FILE_NAME));
            }
        }
    }

}
