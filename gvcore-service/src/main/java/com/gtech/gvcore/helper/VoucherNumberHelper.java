package com.gtech.gvcore.helper;

import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.response.cpg.GetCpgResponse;
import com.gtech.gvcore.dao.mapper.VoucherBookletMapper;
import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.dao.model.Voucher;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2022/2/25 14:30
 */
@Component
public class VoucherNumberHelper {

    @Autowired
    private RedisTemplate redisTemplate;

    VoucherNumberHelper(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Autowired
    private VoucherMapper voucherMapper;

    @Autowired
    private VoucherBookletMapper voucherBookletMapper;

    public static final String REDIS_HEAD = "GV:VOUCHER:";

    public static final String REDIS_DIGITAL_HEAD = "GV:VOUCHER:DIGITAL";

    public static final String RANDOM_STR = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";

    private static final String SPECIAL_CHARS = "!@#$%^&*_=+-/";

    private static Random random = new Random();


    /*
    测试生成券
    public static void main(String[] args) {


        RedisTemplate<Object,Object> redisTemplate = new RedisTemplate<>();

        RedisStandaloneConfiguration redisStandaloneConfiguration = new RedisStandaloneConfiguration("172.19.109.55", 6379);
        redisStandaloneConfiguration.setDatabase(6);
        RedisConnectionFactory connectionFactory = new LettuceConnectionFactory(redisStandaloneConfiguration);



        ((LettuceConnectionFactory) connectionFactory).afterPropertiesSet();
        redisTemplate.setConnectionFactory(connectionFactory);

        Jackson2JsonRedisSerializer serializer = new Jackson2JsonRedisSerializer(Object.class);

        ObjectMapper mapper = new ObjectMapper();
        mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        mapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance,ObjectMapper.DefaultTyping.NON_FINAL);
        serializer.setObjectMapper(mapper);

        redisTemplate.setValueSerializer(serializer);
        //使用StringRedisSerializer来序列化和反序列化redis的key值
        redisTemplate.setKeySerializer(new StringRedisSerializer());

        redisTemplate.afterPropertiesSet(); // 初始化 redisTemplate





        System.out.println(new VoucherNumberHelper(redisTemplate).voucherCodeElectronicList("888", new BigDecimal(100000), "2023", 1, "MAP"));
    }*/


    /**
     * 并发情况下可能会重复
     *
     * @param type
     * @param denomination
     * @param year
     * @return
     */
    public String voucherCodeElectronic(String type, BigDecimal denomination, String year, String issuerCode) {

        //电子券
        String key;

        if (issuerCode.equals("MAP") || StringUtil.isBlank(issuerCode)){
            //电子券
            key = voucherType(type, denomination, year);
        }else {
            key = brandDigitalCode(type,denomination);
        }

        String value = "";
        HashSet<String> set = (HashSet) redisTemplate.opsForValue().get(REDIS_DIGITAL_HEAD + key);

        if (null == set) {
            set = new HashSet<>();
        }

        boolean add = true;
        do {
            value = String.valueOf(RandomUtils.nextInt(100, 1000)) + RandomUtils.nextInt(1000, 10000);
            add = set.add(value);
        } while (!add);
        Calendar rightNow = Calendar.getInstance();
        int hour = rightNow.get(Calendar.HOUR_OF_DAY);
        //, 24L - hour, TimeUnit.HOURS 暂无过期时间
        redisTemplate.opsForValue().set(REDIS_DIGITAL_HEAD + key, set);

        return key + value;
    }


    /**
     * 批量添加
     *
     * @param type
     * @param denomination
     * @param year
     * @param count
     * @return
     */
    public Set<String> voucherCodeElectronicList(String type, BigDecimal denomination, String year, Integer count, String issuerCode) {

        String key;
        if (issuerCode.equals("MAP")){
            //电子券
            key = voucherType(type, denomination, year);
        }else {
            key = brandDigitalCode(type,denomination);
        }




        String value = "";
        HashSet<String> set = (HashSet) redisTemplate.opsForValue().get(REDIS_DIGITAL_HEAD + key);

        if (null == set) {
            set = new HashSet<>();
        }
        Integer oldSize = set.size();
        HashSet<String> result = new HashSet<>();
        boolean add = true;
        do {
            value = String.valueOf(RandomUtils.nextInt(100, 1000)) + RandomUtils.nextInt(1000, 10000);
            add = set.add(value);
            if (add) {
                result.add(key + value);
            }

            if (oldSize == (set.size() - count)){
                break;
            }
        } while (oldSize != (set.size() - count) );

        Calendar rightNow = Calendar.getInstance();
        int hour = rightNow.get(Calendar.HOUR_OF_DAY);
        //, 24L - hour, TimeUnit.HOURS   暂时没有过期时间
        redisTemplate.opsForValue().set(REDIS_DIGITAL_HEAD + key, set);

        return result;
    }

    /**
     * 根据bookletCode获取voucherCode
     *
     * @param bookletCode
     * @return
     */
    public String voucherCodePhysical(String bookletCode) {
        Long increment = redisTemplate.opsForValue().increment(REDIS_HEAD +"BOOKLET:"+ bookletCode);
        return String.valueOf(increment);

    }

    /*public static void main(String[] args) {

        VoucherNumberHelper voucherNumberHelper = new VoucherNumberHelper(new RedisTemplate());

        String barCode27or26 = "1100019244128416758125017711";
        if (barCode27or26.matches("\\d{27}")) {
            System.out.println(voucherNumberHelper.barCode27BitChangeVoucherCode(barCode27or26)); ;
        }else if (barCode27or26.matches("\\d{26}")){
            System.out.println(voucherNumberHelper.barCodeChangeVoucherCode(barCode27or26));;
        }else {
            throw new IllegalArgumentException("Invalid input. Must be a 26-digit or 26-digit bar code.");
        }
    }*/

    /**
     * 使用barCode 27 位或者 barCode 26位计算出 voucherCode 或者 bookletCode
     * @param barCode27or26
     * @return
     */
    public String barCodeToVoucher(String barCode27or26){

        if (barCode27or26.matches("\\d{27}")) {
            return this.barCode27BitChangeVoucherCode(barCode27or26);
        }else if (barCode27or26.matches("\\d{26}")){
            return this.barCodeChangeVoucherCode(barCode27or26);
        }else {
            throw new IllegalArgumentException("Invalid input. Must be a 26-digit or 27-digit bar code.");
        }
    }

    public String barCode27BitChangeVoucherCode(String barCode27Bit) {
        if (!barCode27Bit.matches("\\d{27}")) {
            throw new IllegalArgumentException("Invalid input. Must be a 27-digit bar code.");
        }

        // 定义要提取的字符位置
        int[] positions = {9, 10, 6, 7, 3, 4, 25, 26, 11, 13, 15, 17, 19, 21, 0, 1};

        // 使用StringBuilder来构建新的条形码
        StringBuilder newBarCode = new StringBuilder();
        for (int position : positions) {
            newBarCode.append(barCode27Bit.charAt(position));
        }

        return newBarCode.toString();
    }


    public  String barCode27Bit(String voucherCode) {
        if (!voucherCode.matches("\\d{16}")) {
            throw new IllegalArgumentException("Invalid input. Must be a 16-voucher number.");
        }
        Random random = new Random();

        // 按照规则提取数字并在$位置插入随机数
        String barCode = String.valueOf(
                voucherCode.charAt(14)) +
                voucherCode.charAt(15) +
                random.nextInt(10) +
                voucherCode.charAt(4) +
                voucherCode.charAt(5) +
                random.nextInt(10) +
                voucherCode.charAt(2) +
                voucherCode.charAt(3) +
                random.nextInt(10) +
                voucherCode.charAt(0) +
                voucherCode.charAt(1) +
                voucherCode.charAt(8) +
                random.nextInt(10) +
                voucherCode.charAt(9) +
                random.nextInt(10) +
                voucherCode.charAt(10) +
                random.nextInt(10) +
                voucherCode.charAt(11) +
                random.nextInt(10) +
                voucherCode.charAt(12) +
                random.nextInt(10) +
                voucherCode.charAt(13) +
                random.nextInt(10) +
                random.nextInt(10) +
                random.nextInt(10) +
                voucherCode.charAt(6) +
                voucherCode.charAt(7);
        return barCode.trim();
    }



    /**
     * 将barCode转为voucherCode或者bookletCode
     * @param barCode
     * @return
     */
    public String barCodeChangeVoucherCode(String barCode){
        StringBuilder voucherCode = new StringBuilder(barCode);
        for (int i = 0; i < voucherCode.length(); i += 2) {
            voucherCode.deleteCharAt(i);
        }
        voucherCode.deleteCharAt(voucherCode.length()-1);
        return voucherCode.toString();
    }

    public String barCode(String voucherCode) {
        StringBuilder barCode = new StringBuilder();
        int s = 0;
        int e = 2;

        //第一位
        barCode.append(RandomUtils.nextInt(1, 10));
        for (int i = 0; i < voucherCode.length(); i += 2) {
            String voucher = voucherCode.substring(s, e);
            s += 2;
            e += 2;
            barCode.append(voucher);
            barCode.append(RandomUtils.nextInt(0, 10));

        }
        barCode.append(RandomUtils.nextInt(0, 10));
        return barCode.toString();

    }


    public String bookletCode(String year) {
        String begin = "9001" + year.substring(2);
        Long maxBooklet = (Long) redisTemplate.opsForValue().get(REDIS_HEAD + begin);
        if (null == maxBooklet) {
            String queryMaxBooklet = voucherBookletMapper.queryMaxBooklet(begin);
            if (StringUtil.isNotEmpty(queryMaxBooklet)) {
                maxBooklet = Long.valueOf(queryMaxBooklet);
            } else {
                maxBooklet = Long.valueOf(begin + "0000000001");
            }
            redisTemplate.opsForValue().set(REDIS_HEAD + begin, maxBooklet);
        }
        return redisTemplate.opsForValue().increment(REDIS_HEAD + begin).toString();
    }


    /**
     * 用于voucherCode的生成
     *
     * @param type
     * @param
     * @param year
     * @return
     */
    public String voucherType(String type, BigDecimal denomination, String year) {

        //TODO 502 品牌券

        String voucher123 = "";

        if (type.equals("100")) {
            voucher123 = "100";
        } else if (type.equals("101")) {
            voucher123 = "101";
        } else if (type.equals("103")) {
            voucher123 = "103";
        } else if (type.equals("105")) {
            voucher123 = "105";
        } else {
            voucher123 = type;
        }

        //面值
        String voucher4 = "";

        if (voucher123.equals("105")){
            denomination = BigDecimal.ZERO;
        }
        voucher4 = denominationRules(denomination, voucher4);

        //年
        String voucher56 = year.substring(2);
        return voucher123 + voucher4 + voucher56 + voucher4 + voucher56;
    }



    public String bookletType(BigDecimal denomination, String year) {
        String booklet123 = "900";
        //面值
        String booklet4 = "";
        booklet4 = denominationRules(denomination, booklet4);

        //年
        String booklet56 = year.substring(2);
        return booklet123 + booklet4 + booklet56;
    }

    private static String denominationRules(BigDecimal denomination, String fourthPlace) {
        if (denomination.compareTo(new BigDecimal(0)) == 0) {
            fourthPlace = "0";
        } else if (denomination.compareTo(new BigDecimal(50000)) == 0) {
            fourthPlace = "1";
        } else if (denomination.compareTo(new BigDecimal(100000)) == 0) {
            fourthPlace = "2";
        } else if (denomination.compareTo(new BigDecimal(500000)) == 0) {
            fourthPlace = "3";
        } else if (denomination.compareTo(new BigDecimal(1000000)) == 0) {
            fourthPlace = "4";
        } else if (denomination.compareTo(new BigDecimal(25000)) == 0) {
            fourthPlace = "5";
        }
        return fourthPlace;
    }


    /**
     * pinCode 随机六位数
     *
     * @return
     */
    public String pinCode() {
        return String.valueOf(RandomUtils.nextInt(100000, 1000000));
    }


    /**
     * activationCode    随机生成数字加字母
     *
     * @return
     */
    public String activationCode() {
        StringBuilder valSb = new StringBuilder();
        String charStr = RANDOM_STR;
        int charLength = charStr.length();

        for (int i = 0; i < 16; i++) {
            int index = random.nextInt(charLength);
            valSb.append(charStr.charAt(index));
        }
        return valSb.toString();
    }


    /**
     * 查找一个char数组中还没有填充字符的位置
     */
    private static int nextIndex(char[] chars, Random rnd) {
        int index = rnd.nextInt(chars.length);
        while (chars[index] != 0) {
            index = rnd.nextInt(chars.length);
        }
        return index;
    }

    /**
     * 返回一个随机的特殊字符
     */
    private static char nextSpecialChar(Random rnd) {
        return SPECIAL_CHARS.charAt(rnd.nextInt(SPECIAL_CHARS.length()));
    }

    /**
     * 返回一个随机的大写字母
     */
    private static char nextUpperLetter(Random rnd) {
        return (char) ('A' + rnd.nextInt(26));
    }

    /**
     * 返回一个随机的小写字母
     */
    private static char nextLowerLetter(Random rnd) {
        return (char) ('a' + rnd.nextInt(26));
    }

    /**
     * 返回一个随机的数字
     */
    private static char nextNumLetter(Random rnd) {
        return (char) ('0' + rnd.nextInt(10));
    }

    /**
     * 返回一个随机的字符
     */
    private static char nextChar(Random rnd) {
        switch (rnd.nextInt(4)) {
            case 0:
                return (char) ('a' + rnd.nextInt(26));
            case 1:
                return (char) ('A' + rnd.nextInt(26));
            case 2:
                return (char) ('0' + rnd.nextInt(10));
            default:
                return SPECIAL_CHARS.charAt(rnd.nextInt(SPECIAL_CHARS.length()));
        }
    }

    /**
     * 生成指定位数的随机数
     */
    public String randomPassword(int length) {
        if (length < 4) {
            return "";
        }
        char[] chars = new char[length];

        //1. 至少生成一个大写字母、小写字母、特殊字符、数字
        chars[nextIndex(chars, random)] = nextSpecialChar(random);
        chars[nextIndex(chars, random)] = nextUpperLetter(random);
        chars[nextIndex(chars, random)] = nextLowerLetter(random);
        chars[nextIndex(chars, random)] = nextNumLetter(random);

        for (int i = 0; i < length; i++) {
            if (chars[i] == 0) {
                chars[i] = nextChar(random);
            }
        }

        return new String(chars);
    }


    /**
     *
     *
     * 除了MAP外没有生成实体券
     *
     *
     *
     *
     *
     *
     * @param denomination
     * @return
     */
    public  String brandDigitalCode(String prefix,BigDecimal denomination){

        String str123 = prefix;

        String str9;

        if (denomination.compareTo(new BigDecimal(0)) == 0) {
            str9 = "0";
        }else if (denomination.compareTo(new BigDecimal(50000)) == 0) {
            str9 = "1";
        } else if (denomination.compareTo(new BigDecimal(100000)) == 0) {
            str9 = "2";
        } else if (denomination.compareTo(new BigDecimal(200000)) == 0) {
            str9 = "3";
        } else {
            throw new GTechBaseException(ResultErrorCodeEnum.DENOMINATION_DOES_NOT_EXIST.code(),ResultErrorCodeEnum.DENOMINATION_DOES_NOT_EXIST.desc());
        }
        //+RandomUtils.nextInt(1000000, 10000000)
        return str123+RandomUtils.nextInt(10000, 100000)+str9;
    }




    /**
     * VoucherNumberHelper helper = new VoucherNumberHelper();
     *
     *         Date date = helper.cpgEffectiveDateToDate(GetCpgResponse.builder()
     *                 .gracePeriods(1)
     *                 .effectiveYears(1)
     *                 .effectiveMonth(0)
     *                 .effectiveDay(0)
     *                 .effectiveHour(0)
     *                 .build());
     *
     *         System.out.println(date);
     * @param cpg
     * @return
     */
    public Date cpgEffectiveDateToDate(GetCpgResponse cpg) {

        /**
         *         获取一天如  2022-01-31 23:59:59
         *         这个时间加上过期时间 年 月 日  如过期时间设置为 1   1   5
         *         则过期时间为 2023-03-05 23:59:59
         *
         */


        /*Date resultDate = null;

        //当前年+effective_years (年)     effective_month （月）     effective_day （日）
        if (cpg.getGracePeriods()==0){

            //宽限期为0  当前时间加年月日 时
            Date years = DateUtils.addYears(DateUtil.getEndOfDay(new Date()), cpg.getEffectiveYears());
            Date months = DateUtils.addMonths(years, cpg.getEffectiveMonth());
            Date day = DateUtils.addDays(months, cpg.getEffectiveDay());
            resultDate =  DateUtils.addHours(day,cpg.getEffectiveHour());
        }else {

            //获取当年的最后一天
            Date endOfDay = DateUtil.getEndOfDay(DateUtil.getEndOfYear(new Date()));

            //年份加上 宽限期-1
            Date addYears = DateUtils.addYears(endOfDay, cpg.getGracePeriods() - 1);

            //过期时间年月日
            Date years = DateUtils.addYears(addYears, cpg.getEffectiveYears());
            Date months = DateUtils.addMonths(years, cpg.getEffectiveMonth());
            Date day = DateUtils.addDays(months, cpg.getEffectiveDay());
            resultDate = DateUtils.addHours(day,cpg.getEffectiveHour());
        }
        return resultDate;*/

        LocalDate date = null;
        // 宽限期为0，以当前时间为基础
        if (cpg.getGracePeriods() == 0) {
            date = LocalDate.now()
                    .plusYears(cpg.getEffectiveYears())
                    .plusMonths(cpg.getEffectiveMonth())
                    .plusDays(cpg.getEffectiveDay());
        } else { // 宽限期不为0，以今年的最后一天为基础
            date = LocalDate.of(LocalDate.now().getYear(), 12, 31)
                    .plusYears(cpg.getGracePeriods() - 1)
                    .plusYears(cpg.getEffectiveYears())
                    .plusMonths(cpg.getEffectiveMonth())
                    .plusDays(cpg.getEffectiveDay());

        }

        LocalTime time = LocalTime.of(23, 59, 59);
        if (cpg.getEffectiveHour() != 0){
            time = LocalTime.now();
        }
        LocalDateTime dateTime = LocalDateTime.of(date, time);
        if (cpg.getEffectiveHour() != 0) {
            dateTime = dateTime.plusHours(cpg.getEffectiveHour());
        }

        //转为Date类型
        return Date.from(dateTime.atZone(ZoneId.systemDefault()).toInstant());
    }
}
