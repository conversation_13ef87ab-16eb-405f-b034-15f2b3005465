package com.gtech.gvcore.helper;

import com.alibaba.fastjson.JSONObject;
import com.gtech.commons.utils.BeanCopyUtils;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringReader;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Collection;
import java.util.Map;


/**
 * @ClassName GvPdfHelper
 * @Description PDF工具
 * <AUTHOR>
 * @Date 2022/7/19 17:11
 * @Version V1.0
 **/
@Slf4j
public class GvHtmlHelper {

    /**
     * 默认模板资源路径
     */
    private static final String DEFAULT_HTML_TEMPLATES_PATH = "/";

    /**
     * 默认字符集
     */
    private static final Charset DEFAULT_CHARSET = StandardCharsets.UTF_8;

    /**
     * 配置信息,多线程可共享
     */
    private static final Configuration HTML_CONFIGURATION;

    static {
        HTML_CONFIGURATION = new Configuration(Configuration.getVersion());
        // 使用ClassLoader加载资源路径
        HTML_CONFIGURATION.setClassForTemplateLoading(GvHtmlHelper.class, GvHtmlHelper.DEFAULT_HTML_TEMPLATES_PATH);
        // 默认字符集显示设置为U8,默认使用系统字符集
        HTML_CONFIGURATION.setDefaultEncoding(GvHtmlHelper.DEFAULT_CHARSET.name());
        // 使用兼容模式.设置后如果目标中的占位符在数据模型中不存在不会抛出异常
        HTML_CONFIGURATION.setClassicCompatible(true);
    }

    /**
     * 使用数据模型填充指定路径的模板
     *
     * @param templatePath 模板相对路径
     * @param dataModel    模板对应的数据模型.
     * @return java.io.InputStream 转换后的pdf的输入流.当转换失败时将返回null
     * <AUTHOR>
     * @date 2022/7/19 17:41
     * @since 1.0.0
     */
    public static InputStream fillDataWithTemplate(final String templatePath, final Object dataModel) {

        try {
            final Template template = GvHtmlHelper.HTML_CONFIGURATION.getTemplate(templatePath);

            final String content = FreeMarkerTemplateUtils.processTemplateIntoString(template, dataModel);

            return new ByteArrayInputStream(content.getBytes(GvHtmlHelper.DEFAULT_CHARSET));

        } catch (IOException | TemplateException e) {
            log.error("fillDataWithTemplate exception.", e);
            return null;
        }
    }

    /**
     * 使用数据模型填充指定模板内容中的占位符
     *
     * @param templateContext 模板内容
     * @param dataModel       数据模型
     * @return java.lang.String 填充完成后的模板内容
     * <AUTHOR>
     * @date 2022/7/20 9:36
     * @since 1.0.0
     */
    public static String fillTemplateData(final String templateContext, final Object dataModel) {

        try {
            // 这里是即时制作的模板,不是从某处加载,name可以为null. StringReader 会被自动关闭
            final Template template = new Template(null, new StringReader(templateContext), GvHtmlHelper.HTML_CONFIGURATION);

            return FreeMarkerTemplateUtils.processTemplateIntoString(template, dataModel);

        } catch (IOException | TemplateException e) {
            log.error("fillTemplateData exception.", e);
            return null;
        }
    }

    public static String getCommonListPlaceholder() {
        return "tableList";
    }

    @Getter
    @Setter
    public static class OuterLayer {
        private Collection<?> tableList;
    }


    public static String getCommonAppendTablePlaceholder() {
        return "appendTablePlaceholder";
    }

    /**
     * 基于模板路径构建一个追加写入表格数据的html编写器
     *
     * @param appendTablePlaceholder 用于填充表格行占位符信息的占位符
     * @param tableRowTemplate       表格行的占位信息
     * @return 追加写入表格数据的html编写器
     */
    public static AppendTableWriter buildAppendTableWriterWithPath(final String templatePath, final String appendTablePlaceholder, final String tableRowTemplate) {
        return new AppendTableWriter(templatePath, null, true, appendTablePlaceholder, tableRowTemplate, GvHtmlHelper.HTML_CONFIGURATION);
    }

    /**
     * 基于模板内容构建一个追加写入表格数据的html编写器
     *
     * @param appendTablePlaceholder 用于填充表格行占位符信息的占位符
     * @param tableRowTemplate       表格行的占位信息
     * @return 追加写入表格数据的html编写器
     */
    public static AppendTableWriter buildAppendTableWriterWithContent(final String templateContext, final String appendTablePlaceholder, final String tableRowTemplate) {
        return new AppendTableWriter(null, templateContext, false, appendTablePlaceholder, tableRowTemplate, GvHtmlHelper.HTML_CONFIGURATION);
    }


    public static String fillWithTemplateContent(final String templateContent, final Object dataModel) throws IOException, TemplateException {

        final Template template = new Template(null, new StringReader(templateContent), GvHtmlHelper.HTML_CONFIGURATION);

        return FreeMarkerTemplateUtils.processTemplateIntoString(template, dataModel);
    }

    public static class AppendTableWriter {

        private static final String PLACEHOLDER_EXPRESSION = "${%s}";

        /**
         * 追加写表格数据的占位符
         */
        private String appendTablePlaceholder;

        /**
         * 追加写表格数据的占位符的内容({}中的内容)
         */
        private String appendTablePlaceholderKey;

        /**
         * 添加的表格的行模板.
         * 示例:
         * <#list userList as user>
         * <tr>
         * <td style="width: 13.2981%; height: 23px;">${user.name}</td>
         * <td style="width: 13.2981%; height: 23px;">${user.age}</td>
         * </tr>
         * </#list>
         */
        private String tableRowTemplate;

        /**
         * 配置信息
         */
        private Configuration configuration;

        /**
         * 模板的资源路径
         */
        private String templatePath;

        /**
         * 模板的模板内容
         */
        private String templateContext;

        /**
         * 表示是使用资源路径加载模板还是使用资源内容加载模板
         */
        private final boolean withPath;

        /**
         * html文本内容的缓存
         */
        private String htmlContextCache;

        /**
         * 标识该编写器是否完成
         */
        private boolean closed;

        private AppendTableWriter(final String templatePath, final String templateContext, final boolean withPath, final String appendTablePlaceholder, final String tableRowTemplate, final Configuration configuration) {
            this.templatePath = templatePath;
            this.templateContext = templateContext;
            this.withPath = withPath;

            if (appendTablePlaceholder.startsWith("${") && appendTablePlaceholder.endsWith("}")) {
                this.appendTablePlaceholder = appendTablePlaceholder;
                this.appendTablePlaceholderKey = appendTablePlaceholder.substring(2, appendTablePlaceholder.length() - 1);
            } else {
                this.appendTablePlaceholder = String.format(AppendTableWriter.PLACEHOLDER_EXPRESSION, appendTablePlaceholder);
                this.appendTablePlaceholderKey = appendTablePlaceholder;
            }

            this.tableRowTemplate = tableRowTemplate;
            this.configuration = configuration;
            this.closed = false;
        }

        /**
         * 添加填充数据.
         * 对于简单的填充数据,该方法仅在首次被调用时进行填充,后续调用过程中的简单填充数据将不生效
         * 对于通过tableRowTemplate添加的符合其中占位符标识的数据,将在appendTablePlaceholder标记的位置进行追加写入
         *
         * @param dataModel 数据模型,该数据不应该是一个Collection,否则将无法正常的追加写.编写器不会对该数据进行缓存,方法结束后编写器即失去对其的引用
         * @return com.gtech.gvcore.helper.GvHtmlHelper.AppendTableWriter 当前实例本身
         * <AUTHOR>
         * @date 2022/7/26 22:33
         * @since 1.0.0
         */
        @SuppressWarnings(value = "unchecked")
        public AppendTableWriter addFillData(final Object dataModel) throws IOException, TemplateException {

            this.checkClosed();

            if (null == dataModel) {
                return this;
            }

            final Template template;
            // 首次加载资源,注意,这里应该只以是否为null作为判断条件
            if (null == this.htmlContextCache) {
                // 根据标识符决定首次加载资源的方式
                if (this.withPath) {
                    template = this.configuration.getTemplate(this.templatePath);
                } else {
                    template = new Template(null, new StringReader(this.templateContext), this.configuration);
                }
            } else {
                // 追加占位信息
                this.htmlContextCache = this.htmlContextCache.replace(this.appendTablePlaceholder, this.tableRowTemplate + this.appendTablePlaceholder);
                template = new Template(null, new StringReader(this.htmlContextCache), this.configuration);
            }

            Object fillData = dataModel;

            // 向dataModel添加appendTablePlaceholder,用于下次改方法的执行
            if (dataModel instanceof JSONObject) {
                final JSONObject jsonDataModel = (JSONObject) dataModel;
                jsonDataModel.put(this.appendTablePlaceholderKey, this.appendTablePlaceholder);
            } else if (dataModel instanceof Map) {
                final Map mapDataModel = (Map) dataModel; // NOSONAR
                mapDataModel.put(this.appendTablePlaceholderKey, this.appendTablePlaceholder);
            } else if (!(dataModel instanceof Collection)) {
                final JSONObject jsonDataModel = BeanCopyUtils.jsonCopyBean(dataModel, JSONObject.class);
                jsonDataModel.put(this.appendTablePlaceholderKey, this.appendTablePlaceholder);
                fillData = jsonDataModel;
            }

            this.htmlContextCache = FreeMarkerTemplateUtils.processTemplateIntoString(template, fillData);

            return this;
        }

        /**
         * 获取填充后的html内容.
         * 该方法为终结方法,调用后无论成功与否均会释放该编写器的全部资源
         *
         * @return java.lang.String html内容
         * <AUTHOR>
         * @date 2022/7/26 22:37
         * @since 1.0.0
         */
        public String getContext() {
            this.checkClosed();
            try {
                return this.getHtmlContext();
            } finally {
                this.close();
            }
        }

        /**
         * 获取填充后的html的输入流
         * 该方法为终结方法,调用后无论成功与否均会释放该编写器的全部资源
         *
         * @return java.io.InputStream html输入流
         * <AUTHOR>
         * @date 2022/7/26 22:38
         * @since 1.0.0
         */
        public InputStream getStream() {
            this.checkClosed();
            try {
                return new ByteArrayInputStream(this.getHtmlContext().getBytes(GvHtmlHelper.DEFAULT_CHARSET));
            } finally {
                this.close();
            }
        }

        private String getHtmlContext() {
            return this.htmlContextCache.replace(this.appendTablePlaceholder, "");
        }

        private void checkClosed() {
            if (this.closed) {
                throw new IllegalStateException("AppendTableWriter resources have been released!");
            }
        }

        private void close() {
            this.closed = true;
        }
    }

}
