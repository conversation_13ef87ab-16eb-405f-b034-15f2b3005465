package com.gtech.gvcore.helper;


/**
 * TableHelper
 * 表帮助工具
 */
public class TableHelper {

    private TableHelper() {
    }

    /**
     * 定位index
     * 支持 卡券表 交易表
     */
    public static int getTableIndex (final Object voucherCode) {

        if (null == voucherCode) return -1;

        final String voucherCodeString = voucherCode.toString();

        return (int)(Long.parseLong(voucherCodeString.replaceAll("[a-zA-Z]", "")) % 64);

    }

}
