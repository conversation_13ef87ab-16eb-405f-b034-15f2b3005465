package com.gtech.gvcore.helper;

import com.gtech.commons.redis.GTechRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * @ClassName GvRedisTemplate
 * @Description
 * <AUTHOR>
 * @Date 2023/2/2 20:31
 * @Version V1.0
 **/
@Component
public class GvRedisTemplate extends GTechRedisTemplate {

    /**
     * redis队列操作
     * 从redis队列中获取指定的key的value
     *
     * @param appKey 应用模块的固定key部分(必要)
     * @param key 对象key(必要)
     * @param requireClass 返回对象类型
     */
    public <V> V opsSetPop(String appKey, String key, Class<V> requireClass){

        return this.deserialize(this.getRedisTemplate().opsForSet().pop(combineKey(appKey, key)), requireClass);
    }

    public Set<String> keys(String keys){
        return this.getRedisTemplate().keys(keys);
    }


    public void deleteKeys(Set<String> key){
        this.getRedisTemplate().delete(key);
    }

}
