package com.gtech.gvcore.helper;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName GvExcelHelper
 * @Description GvExcelHelper.
 * <AUTHOR>
 * @Date 2022/7/20 14:18
 * @Version V1.0
 **/
@Slf4j
public class GvExcelHelper {

    private GvExcelHelper() {}

    public static AppendTableWriter buildAppendTableWriter(final String templatePath, final Class<?> head) {
        return GvExcelHelper.buildAppendTableWriter(templatePath, head, new ByteArrayOutputStream());
    }

    public static AppendTableWriter buildAppendTableWriter(final String templatePath, final Class<?> head, final ByteArrayOutputStream outputStream) {
        return GvExcelHelper.appendTableWriterBuilder()
                .templatePath(templatePath)
                .head(head)
                .outputStream(outputStream)
                .addWriteHandler(GvExcelHelper.getHeadStrategy())
                .addWriteHandler(GvExcelHelper.getColumnWidthStyleStrategy())
                .addWriteHandler(GvExcelHelper.getCustomWriteHandler())
                .build();
    }

    public static AppendTableWriterBuilder appendTableWriterBuilder() {
        return new AppendTableWriterBuilder();
    }

    public static class AppendTableWriterBuilder {
        private final List<WriteHandler> writeHandlerList;
        private final List<Converter<?>> converterList;
        private String templatePath;
        private Class<?> head;
        private ByteArrayOutputStream outputStream;

        public AppendTableWriterBuilder() {
            this.writeHandlerList = new ArrayList<>();
            this.converterList = new ArrayList<>();
        }

        public AppendTableWriterBuilder templatePath(final String templatePath) {
            this.templatePath = templatePath;
            return this;
        }

        public AppendTableWriterBuilder head(final Class<?> head) {
            this.head = head;
            return this;
        }

        public AppendTableWriterBuilder outputStream(final ByteArrayOutputStream outputStream) {
            this.outputStream = outputStream;
            return this;
        }

        public AppendTableWriterBuilder addWriteHandler(final WriteHandler writeHandler) {
            this.writeHandlerList.add(writeHandler);
            return this;
        }

        public AppendTableWriterBuilder addConverter(final Converter<?> converter) {
            this.converterList.add(converter);
            return this;
        }

        public AppendTableWriter build() {
            return new AppendTableWriter(this.templatePath, this.head, this.outputStream, this.writeHandlerList, this.converterList);
        }
    }

    public static class AppendTableWriter {

        /**
         * 状态
         */
        private boolean closed = false;

        /**
         * excel编写器
         */
        private ExcelWriter excelWriter;

        /**
         * 模板地址
         */
        private final String templatePath;

        /**
         * excel表头
         */
        private final Class<?> head;

        /**
         * 用于缓存文件的输出流
         */
        private final ByteArrayOutputStream outputStream;

        /**
         * sheet 缓存
         */
        private final Map<String, WriteSheet> sheetMap;

        /**
         * 写处理器缓存
         */
        private final Collection<WriteHandler> writeHandlerList;

        /**
         * 转换器缓存
         */
        private final Collection<Converter<?>> converterList;

        /**
         * total data size
         */
        int totalDataListSize = 0;

        private AppendTableWriter(final String templatePath, final Class<?> head, final ByteArrayOutputStream outputStream, final Collection<WriteHandler> writeHandlerList, final Collection<Converter<?>> converterList) {
            this.templatePath = templatePath;
            this.head = head;
            this.outputStream = outputStream;
            this.sheetMap = new HashMap<>();
            this.writeHandlerList = writeHandlerList;
            this.converterList = converterList;
            this.totalDataListSize = 0;
            this.init();
        }

        private void init() {
            this.buildExcelWriter();
        }

        private void buildExcelWriter() {
            final ExcelWriterBuilder excelWriterBuilder = EasyExcelFactory.write()
                    .file(this.outputStream)

                    .autoCloseStream(Boolean.TRUE)
                    //指定类型
                    .excelType(ExcelTypeEnum.XLSX);

            //模板
            if (null != this.templatePath) excelWriterBuilder.withTemplate(Thread.currentThread().getContextClassLoader().getResourceAsStream(this.templatePath));

            if (null != this.head) excelWriterBuilder.head(this.head);

            this.writeHandlerList.forEach(excelWriterBuilder::registerWriteHandler);
            this.converterList.forEach(excelWriterBuilder::registerConverter);

            this.excelWriter = excelWriterBuilder.build();
        }

        public void doFill(final Object dataModel, final String sheetName) {
            this.checkClosed();
            if (null != dataModel) {
                this.excelWriter.fill(dataModel, this.getSheet(sheetName, null));
            }
        }

        public void doWrite(final List<?> dataList, final String sheetName) {
            this.doWrite(dataList, sheetName, null);
        }

        public void doWrite(final List<?> dataList, final String sheetName, final Boolean needHead) {
            this.checkClosed();
            if (CollectionUtils.isNotEmpty(dataList)) {
                this.excelWriter.write(dataList, this.getSheet(sheetName, needHead));
            }
        }

        public InputStream getStream() {
            try (final ByteArrayOutputStream ops = this.outputStream) {
                // 在ops.toByteArray()之前必须先finish
                this.finish();
                return new ByteArrayInputStream(ops.toByteArray());
            } catch (Exception e) {
                log.error("获取输入流失败.", e);
                return null;
            } finally {
                this.close();
            }
        }

        private void close() {
            this.closed = true;
        }

        private void checkClosed() {
            if (this.closed) throw new IllegalStateException("AppendTableWriter resources have been released!");
        }

        private void finish() {
            this.excelWriter.finish();
        }

        /**
         * 获取sheet,如果不存在则新增
         *
         * @param sheetName sheet名称
         * @return
         */
        private WriteSheet getSheet(final String sheetName, final Boolean needHead) {
            return this.sheetMap.computeIfAbsent(sheetName, key -> EasyExcelFactory.writerSheet(key).needHead(needHead).build());
        }

    }

    private static WriteHandler getHeadStrategy() {
        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 背景色
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontName("Calibri");
        headWriteFont.setFontHeightInPoints((short) 12);
        headWriteFont.setBold(true);
        headWriteFont.setColor((short) 1);

        headWriteCellStyle.setWriteFont(headWriteFont);
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        //内容样式策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 垂直居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER); NO SONAR

        // 设置表格边框(配合去除网格线)
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);

        // 自动换行
        contentWriteCellStyle.setWrapped(Boolean.TRUE);

        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontName("Calibri");
        contentWriteCellStyle.setWriteFont(contentWriteFont);

        return new CustomSheetHorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }

    private static WriteHandler getColumnWidthStyleStrategy() {
        return new SimpleColumnWidthStyleStrategy(35);
    }

    private static WriteHandler getCustomWriteHandler() {
        return new CustomSheetWriteHandler();
    }

    public static class CustomSheetWriteHandler implements SheetWriteHandler {
        @Override
        public void afterSheetCreate(final WriteWorkbookHolder writeWorkbookHolder, final WriteSheetHolder writeSheetHolder) {
            // 去除网格线
            writeSheetHolder.getSheet().setDisplayGridlines(false);
        }

        @Override
        public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            // no code
        }
    }

    public static class CustomSheetHorizontalCellStyleStrategy extends HorizontalCellStyleStrategy {

        public CustomSheetHorizontalCellStyleStrategy(final WriteCellStyle headWriteCellStyle, final WriteCellStyle contentWriteCellStyle) {
            super(headWriteCellStyle, contentWriteCellStyle);
        }

        @Override
        protected void setContentCellStyle(final Cell cell, final Head head, final Integer relativeRowIndex) {
            if (null != relativeRowIndex) {
                super.setContentCellStyle(cell, head, relativeRowIndex);
            }
        }
    }

}
