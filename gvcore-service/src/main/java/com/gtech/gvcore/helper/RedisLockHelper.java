package com.gtech.gvcore.helper;

import com.gtech.commons.redis.GTechRedisTemplate;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * RedisLockHelper
 * redis锁
 * 静态调用
 *
 * <AUTHOR>
 */
@Component
public class RedisLockHelper {

    public static final String BEAN_NAME = "redisLockHelper";

    @Setter
    private static GTechRedisTemplate redisTemplate;

    public RedisLockHelper(GTechRedisTemplate redisTemplate) {
        setRedisTemplate(redisTemplate);
    }

    /**
     * 默认锁超时时间
     * 加锁方法操作时间不应该超过4秒
     */
    private static final Long DEFAULT_TRY_LOCK_TIMEOUT = 4 * 1000L;

    /**
     * 获取锁线程睡眠间隔时间
     */
    private static final Long DEFAULT_THREAD_SLEEP_TIME = 500L;

    /**
     * 锁value
     */
    private static final Integer DEFAULT_VALUE = 1;

    /**
     * 线程锁key title
     */
    private static final String REDIS_KEY_TITLE = "LOCKHELPER:";

    /**
     * 尝试锁定
     * 锁定某个key
     *
     * @param key
     * @return
     */
    public static boolean tryLock(String key) {

        return tryLock(key, key);
    }

    /**
     * 尝试锁定
     * 锁定某个key 并缺获取锁的超时时间 = 锁的超时时间
     *
     * @param key
     * @param timeout
     * @return
     */
    public static boolean tryLock(String key, Long timeout) {

        return tryLock(key, timeout, timeout);
    }

    /**
     * 尝试锁定
     * 锁定某个key并指定超时时
     *
     * @param key
     * @param timeout
     * @param lockTime
     * @return
     */
    public static boolean tryLock(String key, long timeout, long lockTime) {

        return tryLock(key, key, timeout, lockTime);
    }

    /**
     * 尝试锁定
     * 锁定某个key下的value
     *
     * @param key
     * @param lockValue
     * @return
     */
    public static boolean tryLock(String key, String lockValue) {

        return tryLock(key, lockValue, DEFAULT_TRY_LOCK_TIMEOUT, DEFAULT_TRY_LOCK_TIMEOUT);
    }

    /**
     * 尝试锁定
     * 锁定某个key下的value并指定超时时间
     *
     * @param key
     * @param lockValue
     * @param timeout
     * @return
     */
    public static boolean tryLock(String key, String lockValue, long timeout, long lockTime) {

        return StringUtils.isNotBlank(tryLockAndGetRedisKey(key, lockValue, timeout, lockTime));
    }

    public static String tryLockAndGetRedisKey(String key, String lockValue, long timeout, long lockTime) {

        String redisKey = REDIS_KEY_TITLE + key + lockValue;
        int reNumber = (int) (timeout / DEFAULT_THREAD_SLEEP_TIME);

        for (int i = 0; i < reNumber; i++) {

            Boolean lock = redisTemplate.opsValueSetIfAbsent(GvcoreConstants.APP_CODE, redisKey, DEFAULT_VALUE, lockTime);
            if (lock != null && lock) return redisKey;

            try {
                Thread.sleep(DEFAULT_THREAD_SLEEP_TIME);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        return StringUtils.EMPTY;
    }

    /**
     * 解锁
     * 解锁指定key下的value值
     * 该方法对应
     * {@link com.gtech.gvcore.helper.RedisLockHelper#tryLock(String, String, long, long)}
     * {@link com.gtech.gvcore.helper.RedisLockHelper#tryLock(String, String)}
     *
     * @param key
     * @param lockValue
     */
    public static void unLock(String key, String lockValue) {

        String redisKey = REDIS_KEY_TITLE + key + lockValue;

        unLockRedisKey(redisKey);
    }

    public static void unLockRedisKey(String key) {

        redisTemplate.delete(GvcoreConstants.APP_CODE, key);
    }

    /**
     * 解锁
     * 解锁指定key
     * 注意该方法不是解锁指定key下的所有value值
     * 该方法对应
     * {@link com.gtech.gvcore.helper.RedisLockHelper#tryLock(String)}
     * {@link com.gtech.gvcore.helper.RedisLockHelper#tryLock(String, Long)}
     *
     * @param key
     */
    public static void unLock(String key) {

        unLock(key, key);
    }
}
