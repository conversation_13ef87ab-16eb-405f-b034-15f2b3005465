package com.gtech.gvcore.helper.bean;


import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.ProductCategoryDiscountTypeEnum;
import com.gtech.gvcore.common.utils.AmountUtils;
import com.gtech.gvcore.common.utils.CollectorsUtils;
import com.gtech.gvcore.common.utils.GvDateUtil;
import com.gtech.gvcore.helper.CustomerOrderPdfHelper;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Getter
@Setter
@Accessors(chain = true)
public class SalesOrder {

    public static final String SOURCE_TEMPLATE_PATH = "templates/html/SalesOrder.html";
    public static final String INCREASE_KEY = "CUSTOMER_ORDER:PDF:SALES_ORDER";
    private static final String FILE_NAME_TEMPLATE = "%sSales Order Form.html";

    public String getFileName() {

        return String.format(FILE_NAME_TEMPLATE, CustomerOrderPdfHelper.getBaseFileName());
    }

    private String titleDate = DateUtil.format(DateUtil.now(), "MMyy");

    private String increaseNumber;//自增编号

    private String generationTime = GvDateUtil.formatId(DateUtil.now(), GvDateUtil.FORMAT_US_DATETIME_DD_MMMM_YYYY);//生成时间

    private String customerName;

    private String division;//如果是corporate客户，这里填入Division信息。如果是individual客户，这里不需要填写

    private List<Detail> detailList;

    public BigDecimal getBigDecimalTotal() {
        return ConvertUtils.toBigDecimal(detailList.stream().map(Detail::getBigDecimalAmount).filter(Objects::nonNull).collect(CollectorsUtils.summingBigDecimal(e -> e)), BigDecimal.ZERO);
    }

    public String getTotalAmount() {
        return AmountUtils.idrFormat(this.getBigDecimalTotal(), "0");
    }

    private BigDecimal discount;

    private String discountType;

    public String getDiscount() {
        return ProductCategoryDiscountTypeEnum.PERCENTAGE.equalsCode(discountType) ? ConvertUtils.toBigDecimal(discount, BigDecimal.ZERO) + "%" : getDiscountAmount();
    }

    private BigDecimal discountAmount;

    public String getDiscountAmount() {
        return AmountUtils.idrFormat(discountAmount, "0");
    }


    public BigDecimal getBigDecimalGrandTotal() {
        return this.getBigDecimalTotal().subtract(discountAmount);
    }

    @SuppressWarnings("unused")
    public String getGrandTotal() {
        return AmountUtils.idrFormat(this.getBigDecimalGrandTotal(), "0");
    }

    private String beneficiaryName;

    private String branchName;

    private String bankName;

    private String accountNumber;

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class Detail {

        private Integer line;

        private String articleCodeName;

        private boolean isEgv;

        @SuppressWarnings("unused")
        public String getEgvFlag() {
            return isEgv ? "E-" : StringUtils.EMPTY;
        }

        private BigDecimal denomination;

        public String getDenomination() {
            return AmountUtils.idrFormat(denomination, "0");
        }

        private BigDecimal voucherNum;

        public String getVoucherNum() {
            return AmountUtils.idrFormat(voucherNum, "0");
        }

        public BigDecimal getBigDecimalAmount() {
            return denomination.multiply(voucherNum);
        }

        public String getAmount() {
            return AmountUtils.idrFormat(getBigDecimalAmount());
        }

    }

}