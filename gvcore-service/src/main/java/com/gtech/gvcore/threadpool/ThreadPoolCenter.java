package com.gtech.gvcore.threadpool;

import com.google.common.util.concurrent.ThreadFactoryBuilder;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy;
import java.util.concurrent.TimeUnit;

public class ThreadPoolCenter {

    private static final ThreadPoolExecutor commonThreadPool = new ThreadPoolExecutor(200, 300, 60, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(500000),
            new ThreadFactoryBuilder().setNameFormat("ThreadPoolCenter-common-pool-%d").build(),
            new CallerRunsPolicy());

    static {
        commonThreadPool.allowCoreThreadTimeOut(true);
    }

    private ThreadPoolCenter(){

    }

    public static void commonThreadPoolExecute(Runnable t) {
        commonThreadPool.execute(t);
    }

}
