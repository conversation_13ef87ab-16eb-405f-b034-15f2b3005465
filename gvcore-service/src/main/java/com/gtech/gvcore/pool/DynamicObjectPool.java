package com.gtech.gvcore.pool;

import lombok.extern.slf4j.Slf4j;

import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.function.Supplier;

@Slf4j
public class DynamicObjectPool<T> {
    private final Queue<T> corePool;
    private final Queue<T> overflowPool;
    private final Supplier<T> factory;
    private final Consumer<T> reset;
    private final AtomicInteger createCount = new AtomicInteger(0);
    private final AtomicInteger borrowedCount = new AtomicInteger(0);
    private final AtomicInteger missCount = new AtomicInteger(0);
    
    private final int initialSize;
    private final int maxSize;
    private final int incrementSize;
    
    public DynamicObjectPool(Supplier<T> factory, Consumer<T> reset,
                           int initialSize, int maxSize, int incrementSize) {
        this.factory = factory;
        this.reset = reset;
        this.initialSize = initialSize;
        this.maxSize = maxSize;
        this.incrementSize = incrementSize;
        this.corePool = new ConcurrentLinkedQueue<>();
        this.overflowPool = new ConcurrentLinkedQueue<>();
        
        // 初始化核心池
        for (int i = 0; i < initialSize; i++) {
            corePool.offer(factory.get());
            createCount.incrementAndGet();
        }
    }
    
    public T borrowObject() {
        borrowedCount.incrementAndGet();
        
        // 1. 尝试从核心池获取
        T obj = corePool.poll();
        if (obj != null) {
            return obj;
        }
        
        // 2. 尝试从溢出池获取
        obj = overflowPool.poll();
        if (obj != null) {
            return obj;
        }
        
        // 3. 对象池耗尽时增长
        missCount.incrementAndGet();
        synchronized (this) {
            if (createCount.get() < maxSize) {
                int toCreate = Math.min(incrementSize, maxSize - createCount.get());
                for (int i = 0; i < toCreate; i++) {
                    T newObj = factory.get();
                    overflowPool.offer(newObj);
                    createCount.incrementAndGet();
                }
                obj = overflowPool.poll();
            }
        }
        
        // 4. 如果仍然没有可用对象，创建临时对象
        return obj != null ? obj : factory.get();
    }
    
    public void returnObject(T obj) {
        if (obj == null) {
            return;
        }
        
        borrowedCount.decrementAndGet();
        
        // 重置对象状态
        reset.accept(obj);
        
        // 优先归还到核心池
        if (corePool.size() < initialSize) {
            corePool.offer(obj);
            return;
        }
        
        // 其次考虑归还到溢出池
        if (overflowPool.size() < (maxSize - initialSize)) {
            overflowPool.offer(obj);
        }
        // 如果都满了，让对象被GC
    }
    
    public void close() {
        corePool.clear();
        overflowPool.clear();
        createCount.set(0);
        borrowedCount.set(0);
        missCount.set(0);
    }
    
    public PoolStats getStats() {
        return new PoolStats(
            createCount.get(),
            borrowedCount.get(),
            missCount.get(),
            corePool.size(),
            overflowPool.size()
        );
    }
} 