package com.gtech.gvcore.giftcard.masterdata.gcpg.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
@Table(name = "gc_cpg")
public class GcCpg {

    @Id
    private Long id;


    private String cpgCode;

    private String issuerCode;

    @Column(name = "cpg_name")
    private String cpgName;

    private boolean automaticActivate;

    private String activationPeriod;

    private String activationGracePeriod;

    private String effectivePeriod;
    private String articleMopCode;

    private String currency;
    private BigDecimal denomination;
    private String walletHost;
    private String conceptIdentifier;
    private String productBrand;
    private String coverFrontUrl;
    private String coverBackUrl;
    private String status;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_time")
    private Date updateTime;





}
