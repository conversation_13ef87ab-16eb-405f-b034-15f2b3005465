package com.gtech.gvcore.giftcard.service;

import com.gtech.gvcore.dto.CardNumberConfigDto;
import com.gtech.gvcore.giftcard.domain.service.CardNumberGenerator;
import com.gtech.gvcore.giftcard.masterdata.gcpg.dto.GcCpgDTO;
import com.gtech.gvcore.giftcard.masterdata.gcpg.service.GcCpgService;
import com.gtech.gvcore.service.CardNumberConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 礼品卡卡号服务
 * 负责生成唯一卡号，并使用Redis持久化存储卡号信息
 */
@Slf4j
@Service
public class CardNumberService {
    
    // Redis键前缀
    private static final String REDIS_KEY_PREFIX = "GV:GIFTCARD:CARDNUMBERS:";
    private static final String REDIS_BATCH_PREFIX = REDIS_KEY_PREFIX + "BATCH:";
    
    // 批次临时数据过期时间（1天）
    private static final int BATCH_DATA_EXPIRE_HOURS = 24;
    
    // 重试配置
    private static final int MAX_RETRY_COUNT = 3;
    private static final int MAX_ATTEMPTS_PER_CARD = 5;
    
    // 卡号结构
    private static final int PREFIX_LENGTH = 9;  // 前9位是固定前缀
    private static final int RANDOM_LENGTH = 7;  // 后7位是随机部分
    
    // 默认值（仅在无法从CPG获取时使用）
    private static final String DEFAULT_WALLET_HOST = "1";
    private static final String DEFAULT_CONCEPT_ID = "01";
    private static final String DEFAULT_BRAND = "01";
    
    @Autowired
    private CardNumberGenerator cardNumberGenerator;
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    @Autowired
    private GcCpgService gcCpgService;


    @Autowired
    private CardNumberConfigService cardNumberConfigService;

    /**
     * Batch generate unique card numbers
     *
     * @param count Number of card numbers to generate
     * @param cpgCode CPG code
     * @return List of generated card numbers
     */
    public List<String> batchGenerateCardNumbers(int count, String cpgCode) {
        // Parameter validation
        if (!isValidRequest(count, cpgCode)) {
            log.warn("Invalid request parameters: count={}, cpgCode={}", count, cpgCode);
            return Collections.emptyList();
        }

        // Get card number generation parameters from CPG
        CardParams params = resolveCardParams(cpgCode);
        if (!params.isValid()) {
            log.warn("Unable to resolve valid card number parameters: {}", params.getErrorReason());
            return Collections.emptyList();
        }

        // Generate batch ID and result container
        String batchId = generateBatchId();
        List<String> results = new ArrayList<>(count);

        // Execute generation logic with retry
        executeWithRetry(count, params, batchId, results);

        // Clean batch temporary data
        cleanBatchData(batchId);


        // Log completion status
        logCompletionStatus(count, results.size());
        
        return results;
    }

    
    /**
     * 处理重复卡号情况，生成替代卡号
     */
    public List<String> regenerateDuplicateCardNumbers(List<String> invalidCardNumbers, String cpgCode) {
        if (CollectionUtils.isEmpty(invalidCardNumbers)) {
            return Collections.emptyList();
        }
        
        log.info("处理{}个重复或无效的卡号", invalidCardNumbers.size());
        
        // 从Redis中移除这些无效卡号
        invalidCardNumbers.forEach(this::removeCardNumber);
        
        // 重新生成替代卡号
        return batchGenerateCardNumbers(invalidCardNumbers.size(), cpgCode);
    }
    
    /**
     * 批量将卡号保存到Redis
     */
    public void batchSaveCardNumbers(List<String> cardNumbers) {
        if (CollectionUtils.isEmpty(cardNumbers)) {
            return;
        }
        
        int successCount = 0;
        for (String cardNumber : cardNumbers) {
            try {
                saveCardNumber(cardNumber);
                successCount++;
            } catch (Exception e) {
                log.error("批量保存卡号时单个保存异常: {}", e.getMessage(), e);
            }
        }
        
        log.info("批量持久化保存卡号: 总数={}, 成功={}", cardNumbers.size(), successCount);
    }
    
    /**
     * 从现有数据库加载卡号到Redis
     */
    public void loadCardNumbersFromDatabase(Set<String> cardNumbers) {
        if (CollectionUtils.isEmpty(cardNumbers)) {
            return;
        }
        
        log.info("开始从数据库加载{}个卡号到Redis缓存", cardNumbers.size());
        
        // 分批处理，避免一次处理太多数据
        List<List<String>> batches = partitionCollection(new ArrayList<>(cardNumbers), 5000);
        
        int totalSuccess = 0;
        int totalFailed = 0;
        
        for (List<String> batch : batches) {
            int batchSuccess = 0;
            
            for (String cardNumber : batch) {
                try {
                    saveCardNumber(cardNumber);
                    batchSuccess++;
                } catch (Exception e) {
                    // 只记录部分错误日志以避免日志过多
                    if (totalFailed < 10) {
                        log.error("加载卡号到Redis异常: {}", e.getMessage(), e);
                    }
                    totalFailed++;
                }
            }
            
            totalSuccess += batchSuccess;
            log.info("加载批次完成: 当前批次={}, 成功={}, 总成功={}", batch.size(), batchSuccess, totalSuccess);
        }
        
        log.info("从数据库加载卡号到Redis完成: 总数={}, 成功={}, 失败={}", 
                cardNumbers.size(), totalSuccess, totalFailed);
    }
    
    /**
     * 从Redis中移除卡号
     */
    public void removeCardNumber(String cardNumber) {
        try {
            String[] parts = splitCardNumber(cardNumber);
            String redisKey = generateCardNumberKey(parts[0]);
            String randomPart = parts[1];
            
            Long removed = redisTemplate.opsForSet().remove(redisKey, randomPart);
            log.debug("从Redis移除卡号: {}, 结果: {}", cardNumber, removed);
        } catch (Exception e) {
            log.error("移除卡号异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Check if card number already exists
     */
    public boolean checkCardNumberExists(String cardNumber) {
        try {
            String[] parts = splitCardNumber(cardNumber);
            String redisKey = generateCardNumberKey(parts[0]);
            String randomPart = parts[1];
            
            Boolean exists = redisTemplate.opsForSet().isMember(redisKey, randomPart);
            return exists != null && exists;
        } catch (Exception e) {
            log.error("Exception checking card number existence: {}", e.getMessage(), e);
            // For safety, if exception occurs, consider as already exists
            return true;
        }
    }
    
    // ==================== Private Helper Methods ====================

    /**
     * Validate if request parameters are valid
     */
    private boolean isValidRequest(int count, String cpgCode) {
        return count > 0 && cpgCode != null && !cpgCode.trim().isEmpty();
    }
    
    /**
     * 从CPG解析卡号生成参数
     */
    private CardParams resolveCardParams(String cpgCode) {
        CardParams params = new CardParams();
        // 尝试获取CPG信息
        GcCpgDTO cpgInfo = gcCpgService.getCpg(cpgCode);
        if (null == cpgInfo) {
            log.warn("无法获取CPG信息: {}", cpgCode);
            params.setValid(false);
            params.setErrorReason("无法获取CPG信息: " + cpgCode);
        }
        // 从CPG中获取钱包主机标识
        if (StringUtils.isNotBlank(cpgInfo.getWalletHost())) {
            CardNumberConfigDto cardNumberConfig = cardNumberConfigService.getCardNumberConfig(cpgInfo.getWalletHost());
            if (null == cardNumberConfig) {
                log.warn("无法获取卡号配置信息: {}", cpgInfo.getWalletHost());
                params.setValid(false);
                params.setErrorReason("无法获取卡号配置信息: " + cpgInfo.getWalletHost());
            }
            params.setWalletHost(cardNumberConfig.getCode());
        }
        // 从CPG中获取概念标识符
        if (StringUtils.isNotBlank(cpgInfo.getConceptIdentifier())) {
            CardNumberConfigDto cardNumberConfig = cardNumberConfigService.getCardNumberConfig(cpgInfo.getConceptIdentifier());
            if (null == cardNumberConfig) {
                log.warn("无法获取卡号配置信息: {}", cpgInfo.getConceptIdentifier());
                params.setValid(false);
                params.setErrorReason("无法获取卡号配置信息: " + cpgInfo.getConceptIdentifier());
            }
            params.setConceptId(cardNumberConfig.getCode());
        }
        // 从CPG中获取产品品牌
        if (StringUtils.isNotBlank(cpgInfo.getProductBrand())) {
            CardNumberConfigDto cardNumberConfig = cardNumberConfigService.getCardNumberConfig(cpgInfo.getProductBrand());
            if (null == cardNumberConfig) {
                log.warn("无法获取卡号配置信息: {}", cpgInfo.getProductBrand());
                params.setValid(false);
                params.setErrorReason("无法获取卡号配置信息: " + cpgInfo.getProductBrand());
            }
            params.setBrand(cardNumberConfig.getCode());
        }
        params.setDenomination(cpgInfo.getDenomination());
        return params;
    }

    
    /**
     * 执行卡号生成，包含重试逻辑
     */
    private void executeWithRetry(int targetCount, CardParams params, String batchId, List<String> results) {
        int retryCount = 0;
        
        // 首次生成
        generateBatch(targetCount, params, batchId, results);
        
        // 如果首次未达目标数量，进行重试
        while (results.size() < targetCount && retryCount < MAX_RETRY_COUNT) {
            retryCount++;
            log.info("第{}次重试生成卡号，当前已生成: {}, 剩余: {}", 
                    retryCount, results.size(), targetCount - results.size());
            
            // 计算剩余需要生成的数量
            int remainingCount = targetCount - results.size();
            
            // 生成剩余部分
            List<String> retryResult = new ArrayList<>();
            generateBatch(remainingCount, params, batchId, retryResult);
            
            if (retryResult.isEmpty()) {
                log.warn("第{}次重试未能生成任何卡号", retryCount);
                break;  // 避免无谓的继续重试
            }
            
            // 添加到结果集
            results.addAll(retryResult);
        }
    }
    
    /**
     * 生成一批卡号
     */
    private void generateBatch(int count, CardParams params, String batchId, List<String> results) {
        int attempts = 0;
        int maxAttempts = count * 2;  // 避免无限循环
        
        while (results.size() < count && attempts < maxAttempts) {
            attempts++;
            
            // 生成单个卡号
            String cardNumber = generateSingleCardNumber(params, batchId);
            if (cardNumber != null) {
                results.add(cardNumber);
            }
        }
        
        log.debug("单批生成完成: 请求数量={}, 实际生成={}, 尝试次数={}", count, results.size(), attempts);
    }
    
    /**
     * Generate single card number
     */
    private String generateSingleCardNumber(CardParams params, String batchId) {
        for (int attempt = 0; attempt < MAX_ATTEMPTS_PER_CARD; attempt++) {
            // Generate basic card number
            String cardNumber = cardNumberGenerator.generate(
                params.getWalletHost(),
                params.getConceptId(),
                params.getBrand(),
                params.getDenomination()
            );

            // Check if already exists in Redis
            if (checkCardNumberExists(cardNumber)) {
                continue;
            }

            // Check if already exists in current batch
            if (batchId != null && !batchId.isEmpty()) {
                if (checkCardNumberExistsInBatch(cardNumber, batchId)) {
                    continue;
                }

                // Add card number to current batch
                addCardNumberToBatch(cardNumber, batchId);
            }

            // Persist save card number
            saveCardNumber(cardNumber);
            return cardNumber;
        }

        return null;  // Failed after maximum attempts
    }
    
    /**
     * 检查卡号是否在当前批次中已存在
     */
    private boolean checkCardNumberExistsInBatch(String cardNumber, String batchId) {
        try {
            String batchKey = REDIS_BATCH_PREFIX + batchId;
            Boolean exists = redisTemplate.opsForSet().isMember(batchKey, cardNumber);
            return exists != null && exists;
        } catch (Exception e) {
            log.error("检查批次卡号存在性异常: {}", e.getMessage(), e);
            // 安全起见，如果发生异常，视为已存在
            return true;
        }
    }
    
    /**
     * 将卡号添加到当前批次（临时存储）
     */
    private void addCardNumberToBatch(String cardNumber, String batchId) {
        try {
            String batchKey = REDIS_BATCH_PREFIX + batchId;
            redisTemplate.opsForSet().add(batchKey, cardNumber);
            redisTemplate.expire(batchKey, BATCH_DATA_EXPIRE_HOURS, TimeUnit.HOURS);
        } catch (Exception e) {
            log.error("添加卡号到批次异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 将卡号持久化保存到Redis
     */
    private void saveCardNumber(String cardNumber) {
        try {
            String[] parts = splitCardNumber(cardNumber);
            String redisKey = generateCardNumberKey(parts[0]);
            String randomPart = parts[1];
            
            redisTemplate.opsForSet().add(redisKey, randomPart);
            // 不设置过期时间，实现持久化存储
        } catch (Exception e) {
            log.error("保存卡号异常: {}", e.getMessage(), e);
            throw new RuntimeException("保存卡号失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 清理批次临时数据
     */
    private void cleanBatchData(String batchId) {
        try {
            String batchKey = REDIS_BATCH_PREFIX + batchId;
            redisTemplate.delete(batchKey);
        } catch (Exception e) {
            log.error("清理批次临时数据异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 从卡号中提取前缀和随机部分
     */
    private String[] splitCardNumber(String cardNumber) {
        if (cardNumber == null || cardNumber.length() < PREFIX_LENGTH + RANDOM_LENGTH) {
            throw new IllegalArgumentException("卡号格式无效: " + cardNumber);
        }
        
        String prefix = cardNumber.substring(0, PREFIX_LENGTH);
        String randomPart = cardNumber.substring(PREFIX_LENGTH);
        
        return new String[] {prefix, randomPart};
    }
    
    /**
     * 生成Redis键
     */
    private String generateCardNumberKey(String prefix) {
        return REDIS_KEY_PREFIX + prefix;
    }
    
    /**
     * 生成批次ID
     */
    private String generateBatchId() {
        return "BATCH_" + UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }
    
    /**
     * 记录完成状态和性能指标
     */
    private void logCompletionStatus(int requestedCount, int generatedCount) {
        if (generatedCount < requestedCount) {
            log.warn("批量生成卡号不完整: 请求={}, 实际={}",
                    requestedCount, generatedCount);
        } else {
            log.info("批量生成卡号成功: 数量={}",
                    generatedCount);
        }
    }
    
    /**
     * 将集合分割成多个批次
     */
    private <T> List<List<T>> partitionCollection(List<T> items, int batchSize) {
        if (CollectionUtils.isEmpty(items)) {
            return Collections.emptyList();
        }
        
        int size = items.size();
        int batchCount = (size + batchSize - 1) / batchSize; // 向上取整
        
        List<List<T>> batches = new ArrayList<>(batchCount);
        for (int i = 0; i < batchCount; i++) {
            int fromIndex = i * batchSize;
            int toIndex = Math.min(fromIndex + batchSize, size);
            batches.add(items.subList(fromIndex, toIndex));
        }
        
        return batches;
    }
    
    /**
     * 卡号生成参数
     */
    private static class CardParams {
        private String walletHost;
        private String conceptId;
        private String brand;
        private BigDecimal denomination;
        private boolean valid = true;
        private String errorReason;
        
        public String getWalletHost() {
            return walletHost;
        }
        
        public void setWalletHost(String walletHost) {
            this.walletHost = walletHost;
        }
        
        public String getConceptId() {
            return conceptId;
        }
        
        public void setConceptId(String conceptId) {
            this.conceptId = conceptId;
        }
        
        public String getBrand() {
            return brand;
        }
        
        public void setBrand(String brand) {
            this.brand = brand;
        }
        
        public BigDecimal getDenomination() {
            return denomination;
        }
        
        public void setDenomination(BigDecimal denomination) {
            this.denomination = denomination;
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public void setValid(boolean valid) {
            this.valid = valid;
        }
        
        public String getErrorReason() {
            return errorReason;
        }
        
        public void setErrorReason(String errorReason) {
            this.errorReason = errorReason;
        }
    }
} 