package com.gtech.gvcore.giftcard.infrastructure.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.giftcard.infrastructure.entity.GcActivationExtensionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 礼品卡延长激活期限记录数据访问接口
 */
@Mapper
public interface GcActivationExtensionMapper extends GTechBaseMapper<GcActivationExtensionEntity> {
    
    /**
     * 根据卡号查询延长记录
     */
    @Select("SELECT * FROM gc_extend_activation_period WHERE card_number = #{cardNumber} ORDER BY extension_time DESC")
    List<GcActivationExtensionEntity> selectByCardNumber(@Param("cardNumber") String cardNumber);
    
    /**
     * 根据延长码查询延长记录
     */
    @Select("SELECT * FROM gc_extend_activation_period WHERE extension_code = #{extensionCode} LIMIT 1")
    GcActivationExtensionEntity selectByExtensionCode(@Param("extensionCode") String extensionCode);
    
    /**
     * 根据批次号查询延长记录
     */
    @Select("SELECT * FROM gc_extend_activation_period WHERE batch_number = #{batchNumber}")
    List<GcActivationExtensionEntity> selectByBatchNumber(@Param("batchNumber") String batchNumber);
}
