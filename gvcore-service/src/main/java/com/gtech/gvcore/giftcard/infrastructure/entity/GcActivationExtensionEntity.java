package com.gtech.gvcore.giftcard.infrastructure.entity;

import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 礼品卡延长激活期限记录实体
 */
@Data
@Entity
@Table(name = "gc_extend_activation_period")
public class GcActivationExtensionEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "card_number", nullable = false)
    private String cardNumber;
    
    @Column(name = "extension_code", nullable = false, unique = true)
    private String extensionCode;

    @Column(name = "extension_time", nullable = false)
    private Date extensionTime;

    @Column(name = "old_activation_deadline", nullable = false)
    private Date oldActivationDeadline;

    @Column(name = "new_activation_deadline", nullable = false)
    private Date newActivationDeadline;

    @Column(name = "extension_count", nullable = false)
    private Integer extensionCount;

    @Column(name = "issuer_code")
    private String issuerCode;

    @Column(name = "outlet_code")
    private String outletCode;

    @Column(name = "denomination")
    private BigDecimal denomination;

    @Column(name = "cpg_code")
    private String cpg_code;

    @Column(name = "merchant_code")
    private String merchantCode;

    @Column(name = "invoice_number")
    private String invoiceNumber;

    @Column(name = "approval_code")
    private String approvalCode;

    @Column(name = "notes")
    private String notes;

    @Column(name = "batch_number")
    private String batchNumber;

    @Column(name = "source", nullable = false)
    private String source;

    @Column(name = "create_time", nullable = false)
    private Date createTime;

    @Column(name = "update_time", nullable = false)
    private Date updateTime;
}
