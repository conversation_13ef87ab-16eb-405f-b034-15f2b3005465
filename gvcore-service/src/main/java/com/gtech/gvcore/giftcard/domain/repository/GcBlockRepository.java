package com.gtech.gvcore.giftcard.domain.repository;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.giftcard.domain.model.GcBlockRecord;
import com.gtech.gvcore.giftcard.infrastructure.entity.GcBlockEntity;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GcBlockMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;
import java.util.Optional;

/**
 * 礼品卡禁用记录仓储实现
 */
@Repository
public class GcBlockRepository {

    @Autowired
    private GcBlockMapper gcBlockMapper;
    
    /**
     * 保存禁用记录
     */
    @Transactional
    public GcBlockRecord save(GcBlockRecord record) {
        GcBlockEntity entity = BeanCopyUtils.jsonCopyBean(record, GcBlockEntity.class);
        
        if (entity.getId() == null) {
            // 新增记录时设置创建时间和更新时间
            entity.setCreateTime(new java.util.Date());
            entity.setUpdateTime(new java.util.Date());
            gcBlockMapper.insertSelective(entity);
        } else {
            // 更新记录时只设置更新时间
            entity.setUpdateTime(new java.util.Date());
            gcBlockMapper.updateByPrimaryKey(entity);
        }
        
        return record;
    }

    /**
     * 根据卡号查询禁用记录
     */
    public List<GcBlockRecord> findByCardNumber(String cardNumber) {
        Weekend<GcBlockEntity> weekend = Weekend.of(GcBlockEntity.class);
        weekend.weekendCriteria().andEqualTo(GcBlockEntity::getCardNumber, cardNumber);
        List<GcBlockEntity> entities = gcBlockMapper.selectByCondition(weekend);
        return BeanCopyUtils.jsonCopyList(entities, GcBlockRecord.class);
    }
}
