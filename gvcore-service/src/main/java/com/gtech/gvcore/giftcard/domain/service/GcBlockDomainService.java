package com.gtech.gvcore.giftcard.domain.service;

import com.gtech.gvcore.giftcard.domain.model.GcBlockRecord;
import com.gtech.gvcore.giftcard.domain.model.GcUnblockRecord;
import com.gtech.gvcore.giftcard.domain.repository.GcBlockRepository;
import com.gtech.gvcore.giftcard.domain.repository.GcUnblockRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 礼品卡禁用/启用领域服务
 */
@Slf4j
@Service
public class GcBlockDomainService {

    @Autowired
    private GcBlockRepository gcBlockRepository;

    @Autowired
    private GcUnblockRepository gcUnblockRepository;

    /**
     * 根据卡号查询禁用记录
     */
    public List<GcBlockRecord> getBlockRecordsByCardNumber(String cardNumber) {
        if (StringUtils.isBlank(cardNumber)) {
            return Collections.emptyList();
        }
        return gcBlockRepository.findByCardNumber(cardNumber);
    }

    /**
     * 根据卡号查询启用记录
     */
    public List<GcUnblockRecord> getUnblockRecordsByCardNumber(String cardNumber) {
        if (StringUtils.isBlank(cardNumber)) {
            return Collections.emptyList();
        }
        return gcUnblockRepository.findByCardNumber(cardNumber);
    }
}
