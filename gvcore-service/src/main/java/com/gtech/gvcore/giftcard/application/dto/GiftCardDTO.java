package com.gtech.gvcore.giftcard.application.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 礼品卡信息DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GiftCardDTO {
    private String cardNumber;
    private String status;
    private String managementStatus;
    private BigDecimal balance;
    private BigDecimal denomination;
    private String activationCode;
    private String pinCode;
    private Date activationDeadline;
    private Integer activationExtensionCount;
    private Integer maxActivationExtension;
    private Date activationTime;
    private Date expiryTime;
    private String salesOutlet;
    private Date salesTime;

    
    public void validate() {
        if (cardNumber == null || cardNumber.trim().isEmpty()) {
            throw new IllegalArgumentException("Card number cannot be null or blank");
        }
        if (status == null || status.trim().isEmpty()) {
            throw new IllegalArgumentException("Status cannot be null or blank");
        }
        if (balance == null) {
            throw new IllegalArgumentException("Balance cannot be null");
        }
        if (denomination == null || denomination.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Denomination must be greater than zero");
        }
    }
} 