package com.gtech.gvcore.giftcard.infrastructure.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.giftcard.infrastructure.entity.GcSalesCancelEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 礼品卡销售取消记录数据访问接口
 */
@Mapper
public interface GcCancelSalesMapper extends GTechBaseMapper<GcSalesCancelEntity> {
    
    /**
     * 根据取消编码查询销售取消记录
     */
    @Select("SELECT * FROM gc_cancel_sales WHERE cancel_code = #{cancelCode} LIMIT 1")
    GcSalesCancelEntity selectByCancelCode(@Param("cancelCode") String cancelCode);
    
    /**
     * 根据销售编码查询销售取消记录
     */
    @Select("SELECT * FROM gc_cancel_sales WHERE sales_code = #{salesCode} LIMIT 1")
    GcSalesCancelEntity selectBySalesCode(@Param("salesCode") String salesCode);
    
    /**
     * 根据卡号查询销售取消记录
     */
    @Select("SELECT * FROM gc_cancel_sales WHERE card_number = #{cardNumber}")
    List<GcSalesCancelEntity> selectByCardNumber(@Param("cardNumber") String cardNumber);
    
    /**
     * 根据取消人查询销售取消记录
     */
    @Select("SELECT * FROM gc_cancel_sales WHERE cancelled_by = #{cancelledBy}")
    List<GcSalesCancelEntity> selectByCancelledBy(@Param("cancelledBy") String cancelledBy);
} 