package com.gtech.gvcore.giftcard.domain.repository;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.page.PageData;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.request.base.PageBean;
import com.gtech.gvcore.giftcard.domain.model.GcMgtCardStatus;
import com.gtech.gvcore.giftcard.domain.model.GiftCard;
import com.gtech.gvcore.giftcard.infrastructure.entity.GiftCardEntity;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GiftCardMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 礼品卡仓储
 * 简化的仓储模式实现，直接处理实体与领域模型的转换
 */
@Repository
public class GiftCardRepository {

    @Autowired
    private GiftCardMapper giftCardMapper;


    @Autowired
    private SqlSessionFactory sqlSessionFactory;

    /**
     * 保存礼品卡
     */
    @Transactional
    public GiftCard save(GiftCard giftCard) {
        GiftCardEntity entity = toEntity(giftCard);
        giftCardMapper.insertSelective(entity);
        return giftCard;
    }

    public int updateCard(GiftCard giftCard){
        Weekend<GiftCardEntity> weekend = Weekend.of(GiftCardEntity.class);
        WeekendCriteria<GiftCardEntity, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(GiftCardEntity::getCardNumber, giftCard.getCardNumber());
        GiftCardEntity entity = toEntity(giftCard);

        return giftCardMapper.updateByConditionSelective(entity,weekend);
    }

    public int updateCardByCardNumbers(GiftCard giftCard, List<String> cardNumbers){
        Weekend<GiftCardEntity> weekend = Weekend.of(GiftCardEntity.class);
        WeekendCriteria<GiftCardEntity, Object> criteria = weekend.weekendCriteria();
        criteria.andIn(GiftCardEntity::getCardNumber, cardNumbers);

        GiftCardEntity entity = toEntity(giftCard);

        return giftCardMapper.updateByConditionSelective(entity,weekend);
    }

    /**
     * 批量保存礼品卡
     */
    @Transactional
    public void batchSave(List<GiftCard> giftCards) {
        //giftCards 转类型
        List<GiftCardEntity> giftCardEntities = giftCards.stream().map(this::toEntity).collect(Collectors.toList());
        giftCardMapper.insertList(giftCardEntities);
    }

    /**
     * 根据卡号查找礼品卡
     */
    public Optional<GiftCard> findByCardNumber(String cardNumber) {
        GiftCardEntity entity = giftCardMapper.selectByCardNumber(cardNumber);
        return Optional.ofNullable(entity).map(this::toDomainModel);
    }

    /**
     * 根据卡号查找礼品卡
     */
    public Optional<List<GiftCard>> findByCardNumber(List<String> cardNumber) {
        Weekend<GiftCardEntity> weekend = Weekend.of(GiftCardEntity.class);
        weekend.weekendCriteria().andIn(GiftCardEntity::getCardNumber, cardNumber);
        List<GiftCardEntity> giftCardEntities = giftCardMapper.selectByCondition(weekend);
        return Optional.ofNullable(giftCardEntities).map(e -> e.stream().map(this::toDomainModel).collect(Collectors.toList()));
    }

    /**
     * 根据卡号查找礼品卡
     */
    public Optional<List<GiftCard>> findByBatchCode(String batchCode) {
        Weekend<GiftCardEntity> weekend = Weekend.of(GiftCardEntity.class);
        weekend.weekendCriteria().andEqualTo(GiftCardEntity::getCardBatchCode, batchCode);
        List<GiftCardEntity> giftCardEntities = giftCardMapper.selectByCondition(weekend);
        return Optional.ofNullable(giftCardEntities).map(e -> e.stream().map(this::toDomainModel).collect(Collectors.toList()));
    }

    /**
     * 根据卡号查找礼品卡
     */
    public PageData<GiftCard> findByBatchCode(String batchCode, PageBean page) {
        PageHelper.startPage(page.getPageNum(), page.getPageSize());
        Weekend<GiftCardEntity> weekend = Weekend.of(GiftCardEntity.class);
        weekend.weekendCriteria().andEqualTo(GiftCardEntity::getCardBatchCode, batchCode);
        List<GiftCardEntity> giftCardEntities = giftCardMapper.selectByCondition(weekend);
        PageInfo<GiftCardEntity> info = PageInfo.of(giftCardEntities);
        return new PageData<>(BeanCopyUtils.jsonCopyList(info.getList(), GiftCard.class), info.getTotal());

    }



    /**
     * 检查卡号是否已存在
     */
    public boolean existsByCardNumber(String cardNumber) {
        Weekend<GiftCardEntity> weekend = Weekend.of(GiftCardEntity.class);
        WeekendCriteria<GiftCardEntity, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(GiftCardEntity::getCardNumber, cardNumber);

        return giftCardMapper.selectCountByCondition(weekend) > 0;
    }

    /**
     * 查询所有卡号
     */
    public Set<String> findAllCardNumbers() {
        return giftCardMapper.selectAllCardNumbers();
    }

    /**
     * 根据前缀查询所有卡号
     */
    public Set<String> findCardNumbersByPrefix(String prefix) {
        return giftCardMapper.selectCardNumbersByPrefix(prefix);
    }

    /**
     * 将实体转换为领域模型
     */
    private GiftCard toDomainModel(GiftCardEntity entity) {
        GiftCard model = BeanCopyUtils.jsonCopyBean(entity, GiftCard.class);
        model.setManagementStatus(StringUtil.isEmpty(entity.getManagementStatus()) ? GcMgtCardStatus.ENABLE : GcMgtCardStatus.valueOf(entity.getManagementStatus()));
        //日期格式化 yyyy-MM-dd HH:mm:ss
        if (model.getActivationDeadline() != null) {
            model.setActivationDeadline(DateUtil.parseDate(DateUtil.format(model.getActivationDeadline(), DateUtil.FORMAT_YYYYMMDDHHMISS), DateUtil.FORMAT_YYYYMMDDHHMISS));
        }
        if (model.getExpiryTime() != null) {
            model.setExpiryTime(DateUtil.parseDate(DateUtil.format(model.getExpiryTime(), DateUtil.FORMAT_YYYYMMDDHHMISS), DateUtil.FORMAT_YYYYMMDDHHMISS));
        }
        if (model.getSalesTime() != null) {
            model.setSalesTime(DateUtil.parseDate(DateUtil.format(model.getSalesTime(), DateUtil.FORMAT_YYYYMMDDHHMISS), DateUtil.FORMAT_YYYYMMDDHHMISS));
        }
        if (model.getActivationTime() != null) {
            model.setActivationTime(DateUtil.parseDate(DateUtil.format(model.getActivationTime(), DateUtil.FORMAT_YYYYMMDDHHMISS), DateUtil.FORMAT_YYYYMMDDHHMISS));
        }
        if (model.getCreateTime() != null) {
            model.setCreateTime(DateUtil.parseDate(DateUtil.format(model.getCreateTime(), DateUtil.FORMAT_YYYYMMDDHHMISS), DateUtil.FORMAT_YYYYMMDDHHMISS));
        }
        if (model.getUpdateTime() != null) {
            model.setUpdateTime(DateUtil.parseDate(DateUtil.format(model.getUpdateTime(), DateUtil.FORMAT_YYYYMMDDHHMISS), DateUtil.FORMAT_YYYYMMDDHHMISS));
        }
        return model;
    }

    /**
     * 将领域模型转换为实体
     */
    private GiftCardEntity toEntity(GiftCard model) {
        GiftCardEntity entity = BeanCopyUtils.jsonCopyBean(model, GiftCardEntity.class);
        entity.setStatus(String.valueOf(model.getStatus()));
        entity.setManagementStatus(String.valueOf(model.getManagementStatus()));
        return entity;
    }

    public void destroyFromOrder(String customerOrderCode) {
        Weekend<GiftCardEntity> weekend = Weekend.of(GiftCardEntity.class);
        weekend.weekendCriteria().andEqualTo(GiftCardEntity::getCardBatchCode, customerOrderCode);
        GiftCardEntity giftCardEntity = new GiftCardEntity();
        giftCardEntity.setManagementStatus(GcMgtCardStatus.DESTROY.name());
        giftCardMapper.updateByConditionSelective(giftCardEntity,weekend);
    }

    public int deleteFromOrder(String customerOrderCode) {
        Weekend<GiftCardEntity> weekend = Weekend.of(GiftCardEntity.class);
        weekend.weekendCriteria().andEqualTo(GiftCardEntity::getCardBatchCode, customerOrderCode);
       return giftCardMapper.deleteByCondition(weekend);
    }

    public int updateBalance(GiftCard giftCard,BigDecimal oldBalance) {

        Weekend<GiftCardEntity> weekend = Weekend.of(GiftCardEntity.class);
        weekend.weekendCriteria()
                .andEqualTo(GiftCardEntity::getCardNumber, giftCard.getCardNumber())
                .andEqualTo(GiftCardEntity::getBalance, oldBalance);
        GiftCardEntity entity = BeanCopyUtils.jsonCopyBean(giftCard, GiftCardEntity.class);
        return giftCardMapper.updateByConditionSelective(entity, weekend);

    }



    @Transactional
    public int updateBalance(List<GiftCard> giftCards, Map<String, BigDecimal> oldBalanceMap) {
        int totalUpdatedRows = 0;
        int i = 0;
        try(SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);) {
            GiftCardMapper batchMapper = sqlSession.getMapper(GiftCardMapper.class);
            for (GiftCard giftCard : giftCards) {
                String cardNumber = giftCard.getCardNumber();
                BigDecimal oldBalance = oldBalanceMap.get(cardNumber);
                if (oldBalance != null) {
                    Weekend<GiftCardEntity> weekend = Weekend.of(GiftCardEntity.class);
                    weekend.weekendCriteria()
                            .andEqualTo(GiftCardEntity::getCardNumber, cardNumber)
                            .andEqualTo(GiftCardEntity::getBalance, oldBalance); // 使用旧余额作为条件
                    GiftCardEntity entity = BeanCopyUtils.jsonCopyBean(giftCard, GiftCardEntity.class);
                    int updatedRows = batchMapper.updateByConditionSelective(entity, weekend);
                    totalUpdatedRows += updatedRows;
                    if (i % 100 == 0) {
                        sqlSession.commit();
                    }
                }
            }
            sqlSession.commit();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("批量更新礼品卡余额失败", e);
        }
        return totalUpdatedRows;

    }

    public List<GiftCard> getCustomerCardList(String customerCode,List<String> cardNumbers) {

    Weekend<GiftCardEntity> weekend = Weekend.of(GiftCardEntity.class);
        WeekendCriteria<GiftCardEntity, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(GiftCardEntity::getOwnerCustomer, customerCode);
        if (cardNumbers != null && !cardNumbers.isEmpty()) {
            criteria.andIn(GiftCardEntity::getCardNumber, cardNumbers);
        }

        List<GiftCardEntity> giftCardEntities = giftCardMapper.selectByCondition(weekend);
        if (giftCardEntities != null && !giftCardEntities.isEmpty()) {
            return giftCardEntities.stream()
                    .map(this::toDomainModel)
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    public PageData<GiftCard> getCustomerCardListWithPagination(String customerCode, List<String> cardNumbers, PageBean pageBean) {
        PageHelper.startPage(pageBean.getPageNum(), pageBean.getPageSize());

        Weekend<GiftCardEntity> weekend = Weekend.of(GiftCardEntity.class);
        WeekendCriteria<GiftCardEntity, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(GiftCardEntity::getOwnerCustomer, customerCode);
        if (cardNumbers != null && !cardNumbers.isEmpty()) {
            criteria.andIn(GiftCardEntity::getCardNumber, cardNumbers);
        }
        weekend.orderBy("cardNumber").asc();
        List<GiftCardEntity> giftCardEntities = giftCardMapper.selectByCondition(weekend);
        PageInfo<GiftCardEntity> pageInfo = new PageInfo<>(giftCardEntities);

        List<GiftCard> giftCards = giftCardEntities.stream()
                .map(this::toDomainModel)
                .collect(Collectors.toList());

        return new PageData<>(giftCards, pageInfo.getTotal());
    }
}