package com.gtech.gvcore.giftcard.infrastructure.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 礼品卡使用记录实体
 */
@Data
@Entity
@Table(name = "gc_redemption")
public class GcRedemptionEntity {

    @Id
    private Long id;

    @Column(name = "redemption_code", nullable = false, unique = true)
    private String redemptionCode;

    @Column(name = "original_redemption_code", nullable = false, unique = true)
    private String originalRedemptionCode;

    @Column(name = "batch_number", nullable = false)
    private String batchNumber;

    @Column(name = "card_number", nullable = false)
    private String cardNumber;

    @Column(name = "issuer_code", nullable = false)
    private String issuerCode;

    @Column(name = "outlet_code", nullable = false)
    private String outletCode;

    @Column(name = "amount", nullable = false)
    private BigDecimal amount;

    @Column(name = "balance_before", nullable = false)
    private BigDecimal balanceBefore;

    @Column(name = "balance_after", nullable = false)
    private BigDecimal balanceAfter;

    @Column(name = "merchant_code", nullable = false)
    private String merchantCode;

    @Column(name = "terminal_id")
    private String terminalId;

    @Column(name = "invoice_number")
    private String invoiceNumber;

    @Column(name = "redemption_time", nullable = false)
    private Date redemptionTime;

    @Column(name = "redemption_channel", nullable = false)
    private String redemptionChannel;

    @Column(name = "redemption_cancelled", nullable = false)
    private Integer redemptionCanceled;

    @Column(name = "transaction_type", nullable = false)
    private String transactionType;

    @Column(name = "create_time", nullable = false)
    private Date createTime;

    @Column(name = "update_time", nullable = false)
    private Date updateTime;

    @Column(name = "notes", nullable = false)
    private String notes;

    @Column(name = "cpg_code", nullable = false)
    private String cpgCode;

    @Column(name = "denomination", nullable = false)
    private BigDecimal denomination;

    @Column(name = "approval_code", nullable = false)
    private String approvalCode;
} 