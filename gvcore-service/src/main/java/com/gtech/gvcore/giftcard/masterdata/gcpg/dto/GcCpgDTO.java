package com.gtech.gvcore.giftcard.masterdata.gcpg.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
public class GcCpgDTO {
    private String issuerCode;
    private String cpgCode;
    private String cpgName;
    private Boolean automaticActivate;
    private String activationPeriod;
    private String activationGracePeriod;
    private String effectivePeriod;

    // Activation Period
    private Integer activationPeriodYears;
    private Integer activationPeriodMonths;
    private Integer activationPeriodDays;
    private Integer activationPeriodHours;

    // Activation Grace Period
    private Integer activationGracePeriodYears;
    private Integer activationGracePeriodMonths;
    private Integer activationGracePeriodDays;
    private Integer activationGracePeriodHours;

    // Effective Period
    private Integer effectivePeriodYears;
    private Integer effectivePeriodMonths;
    private Integer effectivePeriodDays;
    private Integer effectivePeriodHours;

    private String currency;
    private BigDecimal denomination;
    private String articleMopCode;
    private String walletHost;
    private String conceptIdentifier;
    private String productBrand;
    private String coverFrontUrl;
    private String coverBackUrl;
    private String status;
    private String createUser;
    private Date createTime;
    private String updateUser;
    private Date updateTime;
} 