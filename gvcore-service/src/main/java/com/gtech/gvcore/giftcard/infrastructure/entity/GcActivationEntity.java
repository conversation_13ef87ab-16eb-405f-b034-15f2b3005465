package com.gtech.gvcore.giftcard.infrastructure.entity;

import lombok.Data;

import java.util.Date;

import javax.persistence.*;

/**
 * 礼品卡激活记录实体
 */
@Data
@Entity
@Table(name = "gc_activation")
public class GcActivationEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "batch_number", nullable = false)
    private String batchNumber;

    @Column(name = "card_number", nullable = false)
    private String cardNumber;

    @Column(name = "activation_code", nullable = false, unique = true)
    private String activationCode;

    @Column(name = "owner_customer", nullable = false, unique = true)
    private String ownerCustomer;

    @Column(name = "issuer_code")
    private String issuerCode;

    @Column(name = "outlet_code")
    private String outletCode;

    @Column(name = "merchant_code")
    private String merchantCode;

    @Column(name = "invoice_number")
    private String invoiceNumber;

    @Column(name = "approval_code")
    private String approvalCode;

    @Column(name = "notes")
    private String notes;

    @Column(name = "activation_time", nullable = false)
    private Date activationTime;

    @Column(name = "create_time", nullable = false)
    private Date createTime;

    @Column(name = "update_time", nullable = false)
    private Date updateTime;

    @Column(name = "create_user", nullable = false)
    private String createUser;

} 