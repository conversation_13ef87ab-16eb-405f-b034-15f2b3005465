package com.gtech.gvcore.giftcard.application.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 礼品卡余额调整DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BalanceAdjustmentDTO {
    private String id;
    private String cardNumber;
    private BigDecimal adjustmentAmount;
    private BigDecimal balanceBefore;
    private BigDecimal balanceAfter;
    private String adjustmentType;
    private String reason;
    private String adjustedBy;
    private LocalDateTime adjustmentTime;
    
    public void validate() {
        if (cardNumber == null || cardNumber.trim().isEmpty()) {
            throw new IllegalArgumentException("Card number cannot be null or blank");
        }
        if (adjustmentAmount == null) {
            throw new IllegalArgumentException("Adjustment amount cannot be null");
        }
        if (adjustmentAmount.compareTo(BigDecimal.ZERO) == 0) {
            throw new IllegalArgumentException("Adjustment amount cannot be zero");
        }
        if (adjustmentType == null || adjustmentType.trim().isEmpty()) {
            throw new IllegalArgumentException("Adjustment type cannot be null or blank");
        }
        if (reason == null || reason.trim().isEmpty()) {
            throw new IllegalArgumentException("Reason cannot be null or blank");
        }
        if (adjustedBy == null || adjustedBy.trim().isEmpty()) {
            throw new IllegalArgumentException("Adjusted by cannot be null or blank");
        }
    }
} 