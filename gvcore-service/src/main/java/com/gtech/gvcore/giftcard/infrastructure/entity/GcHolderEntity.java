package com.gtech.gvcore.giftcard.infrastructure.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Entity
@Table(name = "gc_holder")
public class GcHolderEntity {

    @Id
    private Long id;
    @Column(name = "card_number", nullable = false)
    private String cardNumber;
    @Column(name = "card_holder", nullable = false)
    private String cardHolder;
    @Column(name = "holder_name", nullable = false)
    private String holderName;
    @Column(name = "holder_email", nullable = false)
    private String holderEmail;
    @Column(name = "holder_address", nullable = false)
    private String holderAddress;
    @Column(name = "create_time", nullable = false)
    private Date createTime;
    @Column(name = "update_time", nullable = false)
    private Date updateTime;

}
