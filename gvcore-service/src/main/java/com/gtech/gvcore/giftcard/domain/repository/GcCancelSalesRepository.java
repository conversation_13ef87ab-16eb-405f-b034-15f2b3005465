package com.gtech.gvcore.giftcard.domain.repository;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.giftcard.domain.model.SalesCancelRecord;
import com.gtech.gvcore.giftcard.infrastructure.entity.GcSalesCancelEntity;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GcCancelSalesMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 礼品卡销售取消记录仓储实现
 */
@Repository
public class GcCancelSalesRepository {

    @Autowired
    private GcCancelSalesMapper gcCancelSalesMapper;
    

    
    /**
     * 保存销售取消记录
     */
    @Transactional
    public SalesCancelRecord save(SalesCancelRecord record) {
        GcSalesCancelEntity entity = BeanCopyUtils.jsonCopyBean(record, GcSalesCancelEntity.class);
        
        if (entity.getId() == null) {
            gcCancelSalesMapper.insert(entity);
        } else {
            gcCancelSalesMapper.updateByPrimaryKey(entity);
        }
        
        return record;
    }


    @Transactional
    public int saveList(List<SalesCancelRecord> record) {
        if (CollectionUtils.isEmpty(record)) return 0;
        List<GcSalesCancelEntity> entity = BeanCopyUtils.jsonCopyList(record, GcSalesCancelEntity.class);
        return gcCancelSalesMapper.insertList(entity);
    }

    /**
     * 根据取消编码查询销售取消记录
     */
    public Optional<SalesCancelRecord> findByCancelCode(String cancelCode) {
        GcSalesCancelEntity entity = gcCancelSalesMapper.selectByCancelCode(cancelCode);
        return Optional.ofNullable(entity).map(x  -> BeanCopyUtils.jsonCopyBean(x, SalesCancelRecord.class));
    }
    
    /**
     * 根据销售编码查询销售取消记录
     */
    public Optional<SalesCancelRecord> findBySalesCode(String salesCode) {
        GcSalesCancelEntity entity = gcCancelSalesMapper.selectBySalesCode(salesCode);
        return Optional.ofNullable(entity).map(x -> BeanCopyUtils.jsonCopyBean(x, SalesCancelRecord.class) );
    }
    
    /**
     * 根据卡号查询销售取消记录
     */
    public List<SalesCancelRecord> findByCardNumber(String cardNumber) {
        List<GcSalesCancelEntity> entities = gcCancelSalesMapper.selectByCardNumber(cardNumber);
        return BeanCopyUtils.jsonCopyList(entities, SalesCancelRecord.class);
    }
    
    /**
     * 根据取消人查询销售取消记录
     */
    public List<SalesCancelRecord> findByCancelledBy(String cancelledBy) {
        List<GcSalesCancelEntity> entities = gcCancelSalesMapper.selectByCancelledBy(cancelledBy);
        return BeanCopyUtils.jsonCopyList(entities, SalesCancelRecord.class);
    }
} 