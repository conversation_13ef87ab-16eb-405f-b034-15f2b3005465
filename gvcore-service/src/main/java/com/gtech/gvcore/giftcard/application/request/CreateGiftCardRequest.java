package com.gtech.gvcore.giftcard.application.request;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 创建礼品卡请求
 * 面额、过期时间等信息优先使用参数传入的值，如果没有提供则从CPG中获取
 */
@Data
public class CreateGiftCardRequest {


    private String issuerCode;

    
    /**
     * 客户订单编码
     */
    private String customerOrderCode;
    
    /**
     * 客户订单详情编码
     */
    private String customerOrderDetailCode;
    
    /**
     * 创建人
     */
    private String createUser;
    
    /**
     * 批量生成项列表（批量生成多种卡时使用）
     */
    private List<GiftCardBatchItem> batchItems;
    
    /**
     * 批次ID（可选，如未提供则自动生成）
     */
    private String batchId;
    
    /**
     * 批量生成的礼品卡项
     */
    @Data
    public static class GiftCardBatchItem {
        /**
         * CPG编码（必填）
         */
        private String cpgCode;
        
        /**
         * 数量（必填）
         */
        private Integer quantity;
        

        private BigDecimal denomination;
        
        /**
         * 客户订单详情编码
         */
        private String customerOrderDetailCode;
    }
}
