package com.gtech.gvcore.giftcard.domain.model;

import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 礼品卡启用记录
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GcUnblockRecord {
    private Long id;
    private String issuerCode;
    private String merchantCode;
    private String outletCode;
    private String cpgCode;
    private String ownerCustomer;
    private String invoiceNumber;
    private BigDecimal denomination;
    private String cardNumber;
    private String unblockReason;
    private Date createTime;
    private Date unblockTime;
    private Date updateTime;
}
