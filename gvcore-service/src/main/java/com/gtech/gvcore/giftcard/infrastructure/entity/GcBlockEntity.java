package com.gtech.gvcore.giftcard.infrastructure.entity;

import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 礼品卡激活记录实体
 */
@Data
@Entity
@Table(name = "gc_block")
public class GcBlockEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "issuer_code", nullable = false)
    private String issuerCode;

    @Column(name = "merchant_code", nullable = false)
    private String merchantCode;

    @Column(name = "outlet_code")
    private String outletCode;

    @Column(name = "cpg_code")
    private String cpgCode;

    @Column(name = "owner_customer", nullable = false, unique = true)
    private String ownerCustomer;

    @Column(name = "invoice_number")
    private String invoiceNumber;

    @Column(name = "denomination")
    private BigDecimal denomination;

    @Column(name = "card_number")
    private String cardNumber;

    @Column(name = "block_reason", nullable = false)
    private String blockReason;

    @Column(name = "create_time", nullable = false)
    private Date createTime;

    @Column(name = "block_time", nullable = false)
    private Date blockTime;

    @Column(name = "update_time", nullable = false)
    private Date updateTime;

} 