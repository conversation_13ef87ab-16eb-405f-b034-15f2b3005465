package com.gtech.gvcore.giftcard.application.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.gtech.basic.filecloud.exports.core.annotation.FileColumn;
import com.gtech.basic.filecloud.exports.core.annotation.NonFileColumn;
import lombok.Data;

@Data
public class ExportGiftCardDto {


    @FileColumn(name = "GiftCard Number")
    private String cardNumber;

    @FileColumn(name = "ActivationCode")
    private String activationCode;

    @FileColumn(name = "ActivationURL")
    private String activationUrl;

    @FileColumn(name = "GiftCard Expiry Date")
    private String expiryTime;

    @FileColumn(name = "27DigitBarcode")
    private String barCode;

    @FileColumn(name = "Denomination")
    private String denomination;

    @FileColumn(name = "GiftCard Pin")
    private String pinCode;

    @FileColumn(name = "Invoice Number")
    private String invoiceNumber;

}
