package com.gtech.gvcore.giftcard.infrastructure.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.*;

/**
 * 礼品卡销售取消记录实体
 */
@Data
@Entity
@Table(name = "gc_cancel_sales")
public class GcSalesCancelEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "issuer_code")
    private String issuerCode;
    
    @Column(name = "cancel_code", nullable = false, unique = true)
    private String cancelCode;

    @Column(name = "merchant_code", nullable = false)
    private String merchantCode;

    @Column(name = "outlet_code")
    private String outletCode;
    
    @Column(name = "card_number", nullable = false)
    private String cardNumber;

    @Column(name = "cpg_code", nullable = false)
    private String cpgCode;

    @Column(name = "customer_code", nullable = false)
    private String customerCode;

    @Column(name = "invoice_number")
    private String invoiceNumber;

    @Column(name = "denomination")
    private BigDecimal denomination;

    @Column(name = "approval_code")
    private String approvalCode;

    @Column(name = "cancel_reason", nullable = false)
    private String cancelReason;

    @Column(name = "create_user", nullable = false)
    private String createUser;

    @Column(name = "update_user", nullable = false)
    private String updateUser;
    
    @Column(name = "cancel_time", nullable = false)
    private Date cancelTime;
    
    @Column(name = "create_time", nullable = false)
    private Date createTime;
    
    @Column(name = "update_time", nullable = false)
    private Date updateTime;

} 