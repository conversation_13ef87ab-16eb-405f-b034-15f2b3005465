package com.gtech.gvcore.giftcard.domain.model;

import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 礼品卡禁用记录
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GcBlockRecord {
    private Long id;
    private String issuerCode;
    private String merchantCode;
    private String outletCode;
    private String cpgCode;
    private String ownerCustomer;
    private String invoiceNumber;
    private BigDecimal denomination;
    private String cardNumber;
    private String blockReason;
    private Date createTime;
    private Date blockTime;
    private Date updateTime;
}
