package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.common.request.voucher.SendVoucherRequest;
import com.gtech.gvcore.common.request.voucherbatch.GetVoucherBatchRequest;
import com.gtech.gvcore.common.request.voucherbatch.QueryVoucherBatchRequest;
import com.gtech.gvcore.common.response.voucherbatch.ExportDigitalVoucherResponse;
import com.gtech.gvcore.common.response.voucherbatch.ExportVoucherResponse;
import com.gtech.gvcore.common.response.voucherbatch.VoucherBatchResponse;
import com.gtech.gvcore.dao.model.VoucherBatch;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/3/2 11:18
 */
@Mapper
public interface VoucherBatchMapper extends GTechBaseMapper<VoucherBatch> {
    @Select("" +
            "SELECT " +
            " MAX( voucher_end_no )  " +
            "FROM " +
            " gv_voucher_batch  " +
            "WHERE " +
            " substring( voucher_end_no, 1, 9 )= #{head} " +
            " AND status <> '6' " +
            "")
    String queryMaxVoucher(String head);


    @Select(" SELECT voucher_batch_code " +
            " FROM gv_voucher_batch " +
            " where left(gv_voucher_batch.voucher_batch_code, 3) <> 'EGV' and mop_code = 'VCR' " +
            "ORDER BY CAST(SUBSTRING_INDEX(voucher_batch_code, '-', -1) AS UNSIGNED) DESC " +
            " LIMIT 1;" +
            "")
    String queryMaxVoucherBatchCode();


    @Select("" +
            "SELECT " +
            " MAX( substring_index( file_name, '-', -1 ) )  " +
            "FROM " +
            " gv_voucher_batch  " +
            "")
    String queryMaxFileName();


    /*@Select("<script>" +
            "SELECT " +
            " vb.purchase_order_no, " +
            " c.cpg_name, " +
            " vb.issuer_code, " +
            " i.issuer_name, " +
            " vb.cpg_code, " +
            " vb.voucher_batch_code, " +
            " am.article_code_name articleName, " +
            " vb.article_code, " +
            " vb.mop_code, " +
            //" mop.mop_name, " +
            " vb.printer_code, " +
            " p.printer_name, " +
            " vb.booklet_start_no, " +
            " vb.booklet_end_no, " +
            " vb.booklet_per_num, " +
            " vb.booklet_num, " +
            " vb.voucher_start_no, " +
            " vb.voucher_end_no, " +
            " vb.voucher_num, " +
            " vb.denomination, " +
            " vb.voucher_effective_date, " +
            " vb.file_name, " +
            " vb.file_format, " +
            " vb.voucher_num_active, " +
            " vb.voucher_num_used, " +
            " vb.`status`, " +
            " vb.permission_code, " +
            " vb.create_time, " +
            " vb.update_time, " +
            " vb.create_user, " +
            " vb.update_user  " +
            " FROM " +
            " gv_voucher_batch vb LEFT JOIN gv_cpg c ON vb.cpg_code = c.cpg_code " +
            " LEFT JOIN gv_issuer i ON vb.issuer_code = i.issuer_code " +
            " LEFT JOIN gv_article_mop am ON vb.article_code = am.article_mop_code " +
           // " LEFT JOIN gv_means_of_payment mop ON vb.mop_code = mop.mop_code " +
            " LEFT JOIN gv_printer p ON vb.printer_code = p.printer_code " +
            " where  vb.status &lt;&gt; 5" +
            " and vb.status &lt;&gt; 9 "+

                " <if test=\" request.cpgCode != null and request.cpgCode != '' \"> " +
                " AND vb.cpg_code = #{request.cpgCode} " +
                " </if> " +
                " <if test=\" request.voucherStartNo != null and request.voucherStartNo != '' \"> " +
                " AND vb.voucher_start_no = #{request.voucherStartNo} " +
                " </if> " +
                " <if test=\" request.voucherEndNo != null and request.voucherEndNo != '' \"> " +
                " AND vb.voucher_end_no = #{request.voucherEndNo} " +
                " </if> " +
                " <if test=\" request.voucherBatchCode != null and request.voucherBatchCode != '' \"> " +
                " AND vb.voucher_batch_code like concat('%',#{request.voucherBatchCode},'%')  " +
                " </if> " +
                " <if test=\" request.status != null and request.status != '' \"> " +
                " AND vb.status = #{request.status} " +
                " </if> " +
                " <if test=\" request.mopCode != null and request.mopCode != '' \"> " +
                " AND vb.mop_code = #{request.mopCode} " +
                " </if> " +
                " <if test=\" request.issuerCode != null and request.issuerCode != '' \"> " +
                " AND vb.issuer_code = #{request.issuerCode} " +
                " </if> " +
            "GROUP BY  vb.voucher_batch_code " +
            "ORDER BY " +
            " vb.create_time DESC " +

            "</script>")*/
    List<VoucherBatchResponse> queryVoucherBatchList(@Param("request") QueryVoucherBatchRequest request);


    @Select("<script>" +
            "SELECT " +
            " vb.purchase_order_no, " +
            " c.cpg_name, " +
            " vb.issuer_code, " +
            " i.issuer_name, " +
            " vb.cpg_code, " +
            " vb.voucher_batch_code, " +
            " am.article_code_name articleName, " +
            " vb.article_code, " +
            " vb.mop_code, " +
            //" mop.mop_name, " +
            " vb.printer_code, " +
            " p.printer_name, " +
            " vb.booklet_start_no, " +
            " vb.booklet_end_no, " +
            " vb.booklet_per_num, " +
            " vb.booklet_num, " +
            " vb.voucher_start_no, " +
            " vb.voucher_end_no, " +
            " vb.voucher_num, " +
            " vb.denomination, " +
            " vb.voucher_effective_date, " +
            " vb.file_name, " +
            " vb.file_format, " +
            " vb.voucher_num_active, " +
            " vb.voucher_num_used, " +
            " vb.`status`, " +
            " vb.permission_code, " +
            " vb.create_time, " +
            " vb.update_time, " +
            " vb.create_user, " +
            " vb.update_user  " +
            " FROM " +
            " gv_voucher_batch vb LEFT JOIN gv_cpg c ON vb.cpg_code = c.cpg_code " +
            " LEFT JOIN gv_issuer i ON vb.issuer_code = i.issuer_code " +
            " LEFT JOIN gv_article_mop am ON vb.article_code = am.article_mop_code " +
            /*" LEFT JOIN gv_means_of_payment mop ON vb.mop_code = mop.mop_code " +*/
            " LEFT JOIN gv_printer p ON vb.printer_code = p.printer_code " +
            " WHERE 1=1 " +

            " <if test=\" request.voucherBatchCode != null and request.voucherBatchCode != '' \"> " +
            " and vb.voucher_batch_code = #{request.voucherBatchCode} " +
            " </if> " +
            " <if test=\" request.purchaseOrderNo != null and request.purchaseOrderNo != '' \"> " +
            " and vb.purchase_order_no = #{request.purchaseOrderNo} " +
            " </if> " +
            "GROUP BY  vb.voucher_batch_code " +
            "ORDER BY " +
            " create_time DESC " +
            "</script>")
    VoucherBatchResponse getVoucherBatch(@Param("request") GetVoucherBatchRequest request);

    
    @Select("" +
            "SELECT " +
            " booklet.booklet_code, " +
            " booklet.booklet_barcode, " +
            " voucher.voucher_code, " +
            " voucher.voucher_barcode, " +
            " voucher.denomination, " +
            " voucher.voucher_effective_date  " +
            "FROM " +
            " gv_voucher voucher " +
            " left JOIN  gv_voucher_booklet booklet  on voucher.booklet_code= booklet.booklet_code  " +
            "WHERE " +
            " voucher.voucher_batch_code = #{request.voucherBatchCode}  " +
            " ORDER BY voucher_code" +
            "")
    List<ExportVoucherResponse> export(@Param("request") SendVoucherRequest request);



    @Select("" +
            "SELECT " +
            " voucher.voucher_code, " +
            " voucher.voucher_active_code ActivationCode, " +
            " voucher.voucher_barcode digitBarcode, " +
            " voucher.voucher_pin pinCode, " +
            " voucher.voucher_active_url ActivationUrl, " +
            " voucher.denomination, " +
            " voucher.voucher_effective_date  " +
            "FROM " +
            " gv_voucher voucher " +
            "WHERE " +
            " voucher.voucher_batch_code = #{request.voucherBatchCode}  " +
            " ORDER BY voucher_code" +
            "")
    List<ExportDigitalVoucherResponse> exportDigital(@Param("request")SendVoucherRequest request);

}
