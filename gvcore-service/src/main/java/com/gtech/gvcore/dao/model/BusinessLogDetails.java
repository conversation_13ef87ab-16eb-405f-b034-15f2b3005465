
package com.gtech.gvcore.dao.model;


import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "gv_business_log_details")
@ApiModel(value = "BusinessLogDetails)", description = "")
public class BusinessLogDetails implements Serializable {
    private static final long serialVersionUID = -82310002375685179L;
    
    @Id
    private Long id;

    @Column(name = "business_details_code")
    private String businessDetailsCode;
    
    @Column(name = "business_code")
    private String businessCode;
    
    @Column(name = "reason")
    private String reason;

    @Column(name = "detail_content_code")
    private String detailContentCode;
    
    @Column(name = "create_user")
    private String createUser;
    
    @Column(name = "create_time")
    private Date createTime;
    
    @Column(name = "update_user")
    private String updateUser;
    
    @Column(name = "update_time")
    private Date updateTime;


    /** the constant of field {@link BusinessLogDetails#businessDetailsCode} */
    public static final String C_BUSINESS_DETAILS_CODE = "businessDetailsCode";
    /** the constant of field {@link BusinessLogDetails#businessCode} */
    public static final String C_BUSINESS_CODE = "businessCode";
    /** the constant of field {@link BusinessLogDetails#reason} */
    public static final String C_REASON = "reason";

    public static final String C_DETAIL_CONTENT_CODE = "detailContentCode";
    /** the constant of field {@link BusinessLogDetails#createUser} */
    public static final String C_CREATE_USER = "createUser";
    /** the constant of field {@link BusinessLogDetails#createTime} */
    public static final String C_CREATE_TIME = "createTime";
    /** the constant of field {@link BusinessLogDetails#updateUser} */
    public static final String C_UPDATE_USER = "updateUser";
    /** the constant of field {@link BusinessLogDetails#updateTime} */
    public static final String C_UPDATE_TIME = "updateTime";
}
