package com.gtech.gvcore.dao.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "gv_order_report_file")
public class OrderReportFile {

    @Id
    private Long id;
    @Column(name = "order_report_code")
    private String orderReportCode;
    @Column(name = "report_file_type")
    private String reportFileType;
    @Column(name = "report_file_url")
    private String reportFileUrl;
    @Column(name = "permission_code")
    private String permissionCode;
    @Column(name = "create_user")
    private String createUser;
    @Column(name = "create_time")
    private Date createTime;
    @Column(name = "update_user")
    private String updateUser;
    @Column(name = "update_time")
    private Date updateTime;

}
