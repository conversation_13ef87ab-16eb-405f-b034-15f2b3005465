package com.gtech.gvcore.dao.model;

import io.swagger.annotations.ApiModel;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "gv_customer_payment_method")
@ApiModel(value = "CustomerPaymentMethod)", description = "customer payment method")
public class CustomerPaymentMethod implements Serializable {
    private static final long serialVersionUID = -76443444212515207L;

    public static final String C_CUSTOMER_PAYMENT_METHOD_CODE = "customerPaymentMethodCode";
    public static final String C_CUSTOMER_CODE = "customerCode";
    public static final String C_MOP_GROUP = "mopGroup";
    public static final String C_STATUS = "status";
    public static final String C_CREATE_USER = "createUser";
    public static final String C_CREATE_TIME = "createTime";
    public static final String C_UPDATE_USER = "updateUser";
    public static final String C_UPDATE_TIME = "updateTime";



    @Id
    private Long id;

    @Column(name = "customer_payment_method_code")
    private String customerPaymentMethodCode;

    @Column(name = "customer_code")
    private String customerCode;

    @Column(name = "mop_group")
    private String mopGroup;

    @Column(name = "`status`")
    private Integer status;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_time")
    private Date updateTime;



}
