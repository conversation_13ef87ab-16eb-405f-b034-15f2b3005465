package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.model.ReportTempLiabilityDStructure;
import org.apache.ibatis.annotations.Param;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @ClassName ReportTempLiabilityDStructureMapper
 * @Description ReportTempLiabilityDStructure
 * <AUTHOR>
 * @Date 2023/4/18 15:34
 * @Version V1.0
 **/
@Mapper
public interface ReportTempLiabilityDStructureMapper extends GTechBaseMapper<ReportTempLiabilityDStructure> {

    int insertDetail(@Param("tableCode") String tableCode,
                     @Param("detail") ReportTempLiabilityDStructure detail);

    void insertBatch(@Param("tableCode") String tableCode,
                     @Param("detailList") List<ReportTempLiabilityDStructure> detailList);
}
