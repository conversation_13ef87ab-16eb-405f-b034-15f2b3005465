package com.gtech.gvcore.dao.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

@Data
@Table(name = "gv_voucher_allocation")
public class VoucherAllocation {

    public static final String C_VOUCHER_ALLOCATION_CODE = "voucherAllocationCode";

    @Id
    private Long id;

    /**
     * voucher allocation code
     */
    @Column(name = "voucher_allocation_code")
    private String voucherAllocationCode;

    /**
     * source data code
     */
    @Column(name = "source_data_code")
    private String sourceDataCode;

    /**
     * issuer code
     */
    @Column(name = "issuer_code")
    private String issuerCode;

    /**
     * request type, sales、return、transfer、customerOrder
     */
    @Column(name = "business_type")
    private String businessType;

    /**
     * owner code: : issuerCode or outletCode
     */
    @Column(name = "voucher_owner_code")
    private String voucherOwnerCode;

    /**
     * owner name
     */
    @Column(name = "voucher_owner_name")
    private String voucherOwnerName;

    /**
     * receiver code: issuerCode or outletCode
     */
    @Column(name = "receiver_code")
    private String receiverCode;

    /**
     * receiver name
     */
    @Column(name = "receiver_name")
    private String receiverName;

    /**
     * status
     */
    private Integer status;

    /**
     * permission code
     */
    @Column(name = "permission_code")
    private String permissionCode;

    /**
     * create user
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * create time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update user
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * update time
     */
    @Column(name = "update_time")
    private Date updateTime;

}