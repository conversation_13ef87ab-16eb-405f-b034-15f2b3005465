package com.gtech.gvcore.dao.mapper;

import java.math.BigDecimal;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.model.ReleaseApproveNode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/17 8:50
 */

@Mapper
public interface ReleaseApproveNodeMapper extends GTechBaseMapper<ReleaseApproveNode> {
    List<ReleaseApproveNode> selectByAmountCodeAndSort(@Param("releaseApproveAmountCode") String releaseApproveAmountCode);

	List<ReleaseApproveNode> selectNodesByAmountAndType(@Param("issuerCode") String issuerCode, @Param("voucherAmount") BigDecimal voucherAmount,
			@Param("releaseApproveType") String releaseApproveType);

    void deleteAll();
}
