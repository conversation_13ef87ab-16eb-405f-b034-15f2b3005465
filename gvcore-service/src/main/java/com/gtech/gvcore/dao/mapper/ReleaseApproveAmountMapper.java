package com.gtech.gvcore.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.model.ReleaseApproveAmount;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/16 17:48
 */

@Mapper
public interface ReleaseApproveAmountMapper extends GTechBaseMapper<ReleaseApproveAmount> {
	List<ReleaseApproveAmount> selectAllAndSort(@Param("issuerCode") String issuerCode);

    void deleteAll();
}
