package com.gtech.gvcore.dao.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * GC商品支付方式关联实体类
 */
@Data
@Accessors(chain = true)
@Table(name = "gc_article_mop")
public class GcArticleMop {
    /**
     * id
     */
    @Id
    private Long id;

    /**
     * data UNIQUE KEY
     */
    @Column(name = "article_mop_code")
    private String articleMopCode;

    /**
     * article code
     */
    @Column(name = "article_code")
    private String articleCode;

    /**
     * article code name
     */
    @Column(name = "article_code_name")
    private String articleCodeName;

    /**
     * SAP Article Code (GTIN)
     */
    @Column(name = "sap_article_code")
    private String sapArticleCode;

    /**
     * MOP code
     */
    @Column(name = "mop_code")
    private String mopCode;

    /**
     * status,0:disable,1:enable
     */
    private Integer status;

    /**
     * create user
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * create time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update user
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * update time
     */
    @Column(name = "update_time")
    private Date updateTime;
} 