package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.model.ArticleMop;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ArticleMopMapper extends GTechBaseMapper<ArticleMop> {

    /**
     * 
     * <AUTHOR>
     * @param articleMop
     * @return
     * @date 2022年2月23日
     */
    List<ArticleMop> selectSelective(ArticleMop articleMop);
    
    /**
     * 
     * <AUTHOR>
     * @param articleMop
     * @return
     * @date 2022年3月25日
     */
    List<ArticleMop> queryByArticleMopCodeList(List<String> articleMopCodeList);

    ArticleMop getArticleNameByCpgCode(String cpgCode);
}