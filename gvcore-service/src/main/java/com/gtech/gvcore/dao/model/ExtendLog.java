package com.gtech.gvcore.dao.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Table(name = "gv_extend_log")
public class ExtendLog {

    @Id
	@Column(name = "id")
	private Long id;

    @Column(name = "request_id")
    private String requestId;

    /**
     * 请求来源
     */
    @Column(name = "request_source")
    private String requestSource;

    /**
     * 请求类型
     */
    @Column(name = "request_type")
    private Integer requestType;

    /**
     * 请求url
     */
    @Column(name = "request_url")
    private String requestUrl;

    /**
     * 请求时间
     */
    @Column(name = "request_time")
    private Date requestTime;

    /**
     * 请求入参
     */
    @Column(name = "request_body")
    private String requestBody;

    /**
     * 请求出参
     */
    @Column(name = "response_body")
    private String responseBody;

    /**
     * 错误信息
     */
    @Column(name = "error_msg")
    private String errorMsg;

    /**
     * 差异字段
     */
    @Column(name = "difference_fields")
    private String differenceFields;

    /**
     * @return request_id
     */
    public String getRequestId() {
        return requestId;
    }

    /**
     * @param requestId
     */
    public void setRequestId(String requestId) {
        this.requestId = requestId == null ? null : requestId.trim();
    }

    /**
     * 获取请求来源
     *
     * @return request_source - 请求来源
     */
    public String getRequestSource() {
        return requestSource;
    }

    /**
     * 设置请求来源
     *
     * @param requestSource 请求来源
     */
    public void setRequestSource(String requestSource) {
        this.requestSource = requestSource == null ? null : requestSource.trim();
    }

    /**
     * 获取请求类型
     *
     * @return request_type - 请求类型
     */
    public Integer getRequestType() {
        return requestType;
    }

    /**
     * 设置请求类型
     *
     * @param requestType 请求类型
     */
    public void setRequestType(Integer requestType) {
        this.requestType = requestType;
    }

    /**
     * 获取请求url
     *
     * @return request_url - 请求url
     */
    public String getRequestUrl() {
        return requestUrl;
    }

    /**
     * 设置请求url
     *
     * @param requestUrl 请求url
     */
    public void setRequestUrl(String requestUrl) {
        this.requestUrl = requestUrl == null ? null : requestUrl.trim();
    }

    /**
     * 获取请求时间
     *
     * @return request_time - 请求时间
     */
    public Date getRequestTime() {
        return requestTime;
    }

    /**
     * 设置请求时间
     *
     * @param requestTime 请求时间
     */
    public void setRequestTime(Date requestTime) {
        this.requestTime = requestTime;
    }

    /**
     * 获取请求入参
     *
     * @return request_body - 请求入参
     */
    public String getRequestBody() {
        return requestBody;
    }

    /**
     * 设置请求入参
     *
     * @param requestBody 请求入参
     */
    public void setRequestBody(String requestBody) {
        this.requestBody = requestBody == null ? null : requestBody.trim();
    }

    /**
     * 获取请求出参
     *
     * @return response_body - 请求出参
     */
    public String getResponseBody() {
        return responseBody;
    }

    /**
     * 设置请求出参
     *
     * @param responseBody 请求出参
     */
    public void setResponseBody(String responseBody) {
        this.responseBody = responseBody == null ? null : responseBody.trim();
    }

    /**
     * 获取错误信息
     *
     * @return error_msg - 错误信息
     */
    public String getErrorMsg() {
        return errorMsg;
    }

    /**
     * 设置错误信息
     *
     * @param errorMsg 错误信息
     */
    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg == null ? null : errorMsg.trim();
    }

    /**
     * 获取差异字段
     *
     * @return difference_fields - 差异字段
     */
    public String getDifferenceFields() {
        return differenceFields;
    }

    /**
     * 设置差异字段
     *
     * @param differenceFields 差异字段
     */
    public void setDifferenceFields(String differenceFields) {
        this.differenceFields = differenceFields == null ? null : differenceFields.trim();
    }
}