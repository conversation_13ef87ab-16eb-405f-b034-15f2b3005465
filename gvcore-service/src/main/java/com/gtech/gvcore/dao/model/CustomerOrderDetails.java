package com.gtech.gvcore.dao.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "gv_customer_order_details")
public class CustomerOrderDetails implements Serializable {

    private static final long serialVersionUID = 942533897608496913L;
    public static final String C_DELETE_STATUS = "deleteStatus";
    public static final String C_CUSTOMER_ORDER_CODE = "customerOrderCode";
    public static final String C_CPG_CODE = "cpgCode";
    @Id
    private Long id;
    @Column(name = "customer_order_details_code")
    private String customerOrderDetailsCode;
    @Column(name = "customer_order_code")
    private String customerOrderCode;
    @Column(name = "cpg_code")
    private String cpgCode;
    @Column(name = "voucher_num")
    private Integer voucherNum;
    @Column(name = "denomination")
    private BigDecimal denomination;
    @Column(name = "order_denomination")
    private BigDecimal orderDenomination;

    /**
     * amount
     */
    @Column(name = "amount")
    private BigDecimal amount;

    /**
     * discount
     */
    @Column(name = "discount")
    private BigDecimal discount;

    /**
     * voucher_amount
     */
    @Column(name = "voucher_amount")
    private BigDecimal voucherAmount;

    @Column(name = "delete_status")
    private Integer deleteStatus;
    @Column(name = "create_user")
    private String createUser;
    @Column(name = "create_time")
    private Date createTime;
    @Column(name = "update_user")
    private String updateUser;
    @Column(name = "update_time")
    private Date updateTime;

    public CustomerOrderDetails(String customerOrderCode) {
        this.customerOrderCode = customerOrderCode;
    }
}
