package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.common.response.voucherrequest.QueryVoucherRequestResponse;
import com.gtech.gvcore.dao.model.VoucherRequest;
import com.gtech.gvcore.dto.QueryVoucherRequestPermissionDto;
import com.gtech.gvcore.service.report.impl.param.GoodsInTransitQueryData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/8 14:00
 */

@Mapper
public interface VoucherRequestMapper extends GTechBaseMapper<VoucherRequest> {
    @Update("update gv_voucher_request set status=#{status}, update_user=#{updateUser}, update_time=now() where voucher_request_code=#{requestCode}")
    int updateStatus(@Param("requestCode") String requestCode, @Param("status") Integer status, @Param("updateUser") String updateUser);

    /**
     * @param voucherRequestCode
     * @return
     * <AUTHOR>
     * @date 2022年3月9日
     */
    VoucherRequest queryByVoucherRequestCode(String voucherRequestCode);

    List<QueryVoucherRequestResponse> queryVoucherRequestList(@Param("queryVoucherRequestRequest") QueryVoucherRequestPermissionDto queryVoucherRequestRequest);

    /**
     * @param voucherRequestCodeList
     * @return
     * <AUTHOR>
     * @date 2022年3月11日
     */
    List<VoucherRequest> queryByVoucherRequestCodeList(List<String> voucherRequestCodeList);

    List<QueryVoucherRequestResponse> queryVoucherReturnOrTransfer(@Param("queryVoucherRequestRequest") QueryVoucherRequestPermissionDto queryVoucherRequestRequest, @Param("ignoreFromAndToStore") boolean ignoreFromAndToStore);


    Integer countGoodsInTransit(GoodsInTransitQueryData goodsInTransit);

}
