/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WA<PERSON>RANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.gvcore.dao.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.session.RowBounds;

import com.github.pagehelper.Page;
import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.common.request.useraccount.GvQueryUserAccountsCondition;
import com.gtech.gvcore.dao.model.UserAccount;

@Mapper
public interface UserAccountMapper extends GTechBaseMapper<UserAccount> {

    @Select("<script>select ua.id, ua.domain_code, ua.tenant_code, ua.user_code, ua.user_type, ua.account, ua.password, ua.last_name, ua.first_name, "
			+ " ua.email, ua.email_verified, ua.mobile, ua.mobile_verified, ua.org_code, ua.source, ua.status, ua.mobile_country,ua.issuer_code, ua.extend_params"
        + " from idm_user_account ua "

        + "<if test='roleCode != null'> join idm_user_role_mapping ur on ur.tenant_code=ua.tenant_code and ur.user_code=ua.user_code and ur.role_code=#{roleCode} </if>"
        + "<if test='roleCodeList != null'> join idm_user_role_mapping ur on ur.tenant_code=ua.tenant_code and ur.user_code=ua.user_code and ur.role_code in"
        + "<foreach item=\"roleCode\" collection=\"roleCodeList\" separator=\",\" open=\"(\" close=\")\" >"
        +"#{roleCode}"
		+"</foreach>" 
        + "</if>"

        + " where ua.domain_code=#{domainCode} and ua.tenant_code=#{tenantCode}"

			+ "<if test='fullName != null'> and ua.full_name like concat('%',#{fullName},'%') </if>"

		+ "<if test='issuerCode != null'> and ua.issuer_code=#{issuerCode} </if>"
        + "<if test='orgCode != null'> and ua.org_code=#{orgCode} </if>"
        + "<if test='source != null'> and ua.source=#{source} </if>"
        + "<if test='status != null'> and ua.status=#{status} </if>"
        + "<if test='account != null and account.trim().length() != 0'> and ua.account=#{account} </if>"
        + "<if test='mobile != null and mobile.trim().length() != 0'> and ua.mobile=#{mobile} </if>"
        + "<if test='email != null and email.trim().length() != 0'> and ua.email=#{email} </if>"
        + "<if test='userCode != null and userCode.trim().length() != 0'> and ua.user_code=#{userCode} </if>"
			+ " group by user_code"
        + " order by ua.create_time desc "
        + "</script>")
	Page<UserAccount> queryUserAccountList(GvQueryUserAccountsCondition queryCond, @Param("rowBounds") RowBounds rowBounds);
}
