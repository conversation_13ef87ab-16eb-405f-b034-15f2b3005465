package com.gtech.gvcore.dao.model;

import com.gtech.gvcore.common.enums.DistributionItemVoucherStatusEnum;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Getter
@Setter
@Table(name = "gv_distribution_resend")
public class DistributionResend {

    /**
     * id
     */
    @Id
    private Long id;

    /**
     * 分发编码
     */
    @Column(name = "distribution_code")
    private String distributionCode;

    /**
     * 分发item编码
     */
    @Column(name = "distribution_item_code")
    private String distributionItemCode;

    /**
     * 所属customer
     */
    @Column(name = "customer_code")
    private String customerCode;

    /**
     * voucher code.
     */
    @Column(name = "voucher_code")
    private String voucherCode;

    /**
     * 邮箱编码
     */
    @Column(name = "email_address")
    private String emailAddress;

    /**
     * 邮箱用户名
     */
    @Column(name = "email_end_customer_name")
    private String emailEndCustomerName;

    /**
     * 券分发状态.Available,Distributing,Distributed,Fail
     *
     * @see DistributionItemVoucherStatusEnum
     */
    @Column(name = "status")
    private String status;

    /**
     * 失败描述
     */
    @Column(name = "error_msg")
    private String errorMsg;

    /**
     * create time.
     */
    @Column(name = "create_time")
    private Date createTime;

}