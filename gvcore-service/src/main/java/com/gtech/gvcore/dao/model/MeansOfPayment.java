package com.gtech.gvcore.dao.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

@Data
@Table(name = "gv_means_of_payment")
public class MeansOfPayment {
    /**
     * id
     */
    @Id
    private Long id;

    /**
     * data UNIQUE KEY
     */
    @Column(name = "means_of_payment_code")
    private String meansOfPaymentCode;

    /**
     * MOP name
     */
    @Column(name = "mop_name")
    private String mopName;

    /**
     * MOP group
     */
    @Column(name = "mop_group")
    private String mopGroup;

    /**
     * GRP
     */
    private String grp;

    /**
     * external payment mode
     */
    @Column(name = "external_payment_mode")
    private String externalPaymentMode;

    /**
     * remarks
     */
    private String remarks;

    /**
     * status,0:disable,1:enable
     */
    private Integer status;

    /**
     * create user
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * create time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update user
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * update time
     */
    @Column(name = "update_time")
    private Date updateTime;

}