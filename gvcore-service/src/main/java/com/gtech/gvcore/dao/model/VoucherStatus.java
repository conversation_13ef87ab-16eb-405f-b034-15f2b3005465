package com.gtech.gvcore.dao.model;

import lombok.Getter;

@Getter
public enum VoucherStatus {
    PURCHASED(1, "已购买"),
    ACTIVATION_PERIOD_ENDED(2, "激活期已结束"),
    ACTIVATION_PERIOD_EXTENDED(3, "激活期已延长"),
    ACTIVATED(4, "已激活"),
    ZERO_BALANCE(5, "余额为零"),
    EXPIRED(6, "已过期"),
    DEACTIVATED(7, "已停用"),
    DESTROYED(8, "已销毁");

    private final Integer code;
    private final String description;

    VoucherStatus(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public static VoucherStatus fromCode(Integer code) {
        for (VoucherStatus status : VoucherStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("无效的券状态码: " + code);
    }
} 