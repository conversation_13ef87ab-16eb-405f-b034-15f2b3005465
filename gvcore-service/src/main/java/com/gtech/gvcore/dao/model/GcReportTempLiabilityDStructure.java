package com.gtech.gvcore.dao.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;


@Getter
@Setter
@Accessors(chain = true)
@Table(name = "gc_report_temp_liability_d_structure")
public class GcReportTempLiabilityDStructure {

    @Id
    private Long id;

    @Column(name = "issuer_code")
    private String issuerCode;

    @Column(name = "merchant_code")
    private String merchantCode;

    @Column(name = "outlet_code")
    private String outletCode;

    @Column(name = "cpg_code")
    private String cpgCode;

    @Column(name = "voucher_status")
    private String voucherStatus;

    @Column(name = "denomination")
    private BigDecimal denomination;

    @Column(name = "expiry_date")
    private Date expiryDate;

    @Column(name = "voucher_code_page_index")
    private Integer voucherCodePageIndex;

    @Column(name = "voucher_codes")
    private String voucherCodes;

    @Column(name = "balance")
    private BigDecimal balance;

}
