package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.common.request.articlemop.QueryArticleMopsByPageRequest;
import com.gtech.gvcore.dao.model.ArticleMop;
import com.gtech.gvcore.dao.model.GcArticleMop;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * GC商品支付方式关联Mapper接口
 */
@Mapper
public interface GcArticleMopMapper extends GTechBaseMapper<GcArticleMop> {
    /**
     *
     * <AUTHOR>
     * @param articleMop
     * @return
     * @date 2022年2月23日
     */
    List<GcArticleMop> selectSelective(GcArticleMop articleMop);


    List<GcArticleMop> queryByArticleMopCodeList(List<String> articleMopCodeList);

    GcArticleMop getArticleNameByCpgCode(String cpgCode);
} 