package com.gtech.gvcore.dao.dto;

import java.util.List;

import com.gtech.gvcore.dao.model.Outlet;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 * <AUTHOR>
 * @date 2022年4月22日
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OutletDto extends Outlet {

    /**
     * 
     */
    private static final long serialVersionUID = 5382262202848949295L;

    private List<String> outletCodeList;

    private List<String> companyCodeList;
    private List<String> merchantCodeList;

}


