package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.model.CpgPrinter;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface CpgPrinterMapper extends GTechBaseMapper<CpgPrinter> {
    List<CpgPrinter> query(Map<String, Object> parameters);

    int count(Map<String, Object> parameters);

    int updateStatusByPrimaryKey(Map<String, Object> map);

    CpgPrinter getByCode(Map<String, Object> map);

    int delByCode(Map<String, Object> map);

    void insertBatch(List<CpgPrinter> list);
}