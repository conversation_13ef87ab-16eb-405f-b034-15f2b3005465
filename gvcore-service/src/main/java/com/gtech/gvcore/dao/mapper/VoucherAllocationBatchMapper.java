package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.model.VoucherAllocationBatch;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface VoucherAllocationBatchMapper extends GTechBaseMapper<VoucherAllocationBatch> {

    List<VoucherAllocationBatch> getAllocateByVoucherCode(String voucherCode);

	List<VoucherAllocationBatch> selectAllocationBatchByVoucherNums(List<String> voucherNumVCR);
}