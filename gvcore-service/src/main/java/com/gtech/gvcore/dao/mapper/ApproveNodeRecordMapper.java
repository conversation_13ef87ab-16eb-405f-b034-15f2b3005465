package com.gtech.gvcore.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.model.ApproveNodeRecord;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/18 11:42
 */

@Mapper
public interface ApproveNodeRecordMapper extends GTechBaseMapper<ApproveNodeRecord> {
    List<ApproveNodeRecord> selectByBusinessAndType(@Param("businessCode") String businessCode,
            @Param("releaseApproveType01") String releaseApproveType01, @Param("deleteStatus") Integer deleteStatus);

	ApproveNodeRecord selectNewNote(String voucherRequestCode, String approveType);

	ApproveNodeRecord selectNewNoteVCE(String voucherCode, String type);

	Boolean existRelease(@Param("customerOrderCode") String customerOrderCode);

	Boolean existApprove(@Param("voucherRequestCode") String voucherRequestCode);

    /**
     * 
     * @param approveNodeRecord
     * @return
     * <AUTHOR>
     * @date 2022年5月17日
     */
    int updateByDeleted(ApproveNodeRecord approveNodeRecord);

}
