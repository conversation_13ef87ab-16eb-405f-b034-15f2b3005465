package com.gtech.gvcore.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.dto.MeansOfPaymentDto;
import com.gtech.gvcore.dao.model.MeansOfPayment;

@Mapper
public interface MeansOfPaymentMapper extends GTechBaseMapper<MeansOfPayment> {

    /**
     * 
     * @param dto
     * @return
     * <AUTHOR>
     * @date 2022年4月22日
     */
    List<MeansOfPayment> selectSelective(MeansOfPaymentDto dto);

    /**
     * 
     * <AUTHOR>
     * @param meansOfPaymentCodeList
     * @return
     * @date 2022年6月15日
     */
    List<MeansOfPayment> queryByCodeList(List<String> meansOfPaymentCodeList);

}