package com.gtech.gvcore.dao.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

@Data
@Table(name = "gc_means_of_payment")
public class GcMeansOfPayment {
    /**
     * id
     */
    @Id
    private Long id;

    /**
     * 支付方式编码
     */
    @Column(name = "means_of_payment_code")
    private String meansOfPaymentCode;

    /**
     * 支付方式名称
     */
    @Column(name = "mop_name")
    private String mopName;

    /**
     * 支付方式组
     */
    @Column(name = "mop_group")
    private String mopGroup;

    /**
     * GRP
     */
    private String grp;

    /**
     * 外部支付模式
     */
    @Column(name = "external_payment_mode")
    private String externalPaymentMode;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 状态,0:禁用,1:启用
     */
    private Integer status;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
} 