package com.gtech.gvcore.dao.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "gv_report_management")
public class ReportManagement implements Serializable {

    private static final long serialVersionUID = 1085871187664969172L;
    public static final String C_REPORT_MANAGEMENT_CODE = "reportManagementCode";
    public static final String C_MERCHANT_CODE = "merchantCode";
    public static final String C_ROLE_CODE = "roleCode";
    public static final String C_REPORT_NAME = "reportName";
    public static final String C_REPORT_TYPE = "reportType";
    @Id
    private Long id;
    @Column(name = "report_management_code")
    private String reportManagementCode;
    @Column(name = "merchant_code")
    private String merchantCode;
    @Column(name = "report_name")
    private String reportName;
    @Column(name = "report_type")
    private Integer reportType;
    @Column(name = "export_pdf")
    private Boolean exportPdf;
    @Column(name = "export_excel")
    private Boolean exportExcel;
    @Column(name = "create_time")
    private Date createTime;
    @Column(name = "create_user")
    private String createUser;
    @Column(name = "role_code")
    private String roleCode;
    @Column(name = "update_time")
    private Date updateTime;
    @Column(name = "update_user")
    private String updateUser;

}
