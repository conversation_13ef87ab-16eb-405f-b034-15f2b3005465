package com.gtech.gvcore.dao.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
@Table(name = "gv_issue_handling_details")
public class IssueHandlingDetails {
    public static final String C_UPDATE_TIME = "updateTime";
    public static final String C_UPDATE_USER = "updateUser";
    public static final String C_CREATE_TIME = "createTime";
    public static final String C_CREATE_USER = "createUser";
    public static final String C_PERMISSION_CODE = "permissionCode";
    public static final String C_RESULT = "result";
    public static final String C_PROCESS_STATUS = "processStatus";
    public static final String C_REMARKS = "remarks";
    public static final String C_RECEIVER_EMAIL = "receiverEmail";
    public static final String C_NEW_VOUCHER_CODE = "newVoucherCode";
    public static final String C_VOUCHER_EFFECTIVE_DATE = "voucherEffectiveDate";
    public static final String C_APPROVAL_CODE = "approvalCode";
    public static final String C_INVOICE_NO = "invoiceNo";
    public static final String C_ROW_OF_SHEET = "rowOfSheet";
    public static final String C_CPG_CODE = "cpgCode";
    public static final String C_VOUCHER_OWNER_CODE = "voucherOwnerCode";
    public static final String C_DENOMINATION = "denomination";
    public static final String C_ISSUE_HANDLING_CODE = "issueHandlingCode";
    public static final String C_ISSUE_HANDLING_DETAILS_CODE = "issueHandlingDetailsCode";
    public static final String C_ID = "id";
    public static final String C_AMOUNT = "amount";
    /**
     * id
     */
    @Id
    private Long id;

    /**
     * issue handling details code.
     */
    @Column(name = "issue_handling_details_code")
    private String issueHandlingDetailsCode;

    /**
     * issue handling code.
     */
    @Column(name = "issue_handling_code")
    private String issueHandlingCode;

	/**
	 * issuer type: cancel_sales、cancel_redeem、bulk_active、...
	 */
	@Column(name = "issue_type")
	private String issueType;

    /**
     * Voucher code.
     */
    @Column(name = "voucher_code")
    private String voucherCode;

    /**
     * Denomination.
     */
    private BigDecimal denomination;

    /**
     * voucher owner code
     */
    @Column(name = "voucher_owner_code")
    private String voucherOwnerCode;

    /**
     * cpg code(VPG code)
     */
    @Column(name = "cpg_code")
    private String cpgCode;

    /**
     * Row of sheet
     */
    @Column(name = "row_of_sheet")
    private Integer rowOfSheet;

    /**
     * Original invoice NO
     */
    @Column(name = "invoice_no")
    private String invoiceNo;

    /**
     * Original Approval Code
     */
    @Column(name = "approval_code")
    private String approvalCode;

    /**
     * new Voucher effective date.
     */
    @Column(name = "voucher_effective_date")
    private Date voucherEffectiveDate;

    /**
     * new Voucher code.
     */
    @Column(name = "new_voucher_code")
    private String newVoucherCode;

    /**
     * receiver email?? 是不是receiver的email
     */
    @Column(name = "receiver_email")
    private String receiverEmail;

    /**
     * request remarks
     */
    private String remarks;

    /**
     * process status. 0:created,1:processing,2:success,3:failed
     */
    @Column(name = "process_status")
    private Integer processStatus;

    /**
     * result
     */
    private String result;

    /**
     * permission code
     */
    @Column(name = "permission_code")
    private String permissionCode;

    /**
     * create user
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * create time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update user
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * update time
     */
    @Column(name = "update_time")
    private Date updateTime;

	@Column(name = "outlet_name")
	private String outletName;

    @Column(name = "old_activation_code")
    private String oldActivationCode;

    @Column(name = "amount")
    private BigDecimal amount;

    public static final String C_ISSUE_TYPE = "issueType";
    public static final String C_VOUCHER_CODE = "voucherCode";

}