package com.gtech.gvcore.dao.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/8 16:16分
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "gv_voucher_request_details")
@Builder
@Accessors(chain = true)
public class VoucherRequestDetails implements Serializable {

    private static final long serialVersionUID = 8494818123081455551L;
    public static final String C_VOUCHER_REQUEST_CODE = "voucherRequestCode";
    @Id
    private Long id;
    @Column(name = "voucher_request_details_code")
    private String voucherRequestDetailsCode;
    @Column(name = "voucher_request_code")
    private String voucherRequestCode;
    @Column(name = "cpg_code")
    private String cpgCode;
    @Column(name = "denomination")
    private BigDecimal denomination;
    @Column(name = "voucher_num")
    private Integer voucherNum;
    @Column(name = "voucher_amount")
    private BigDecimal voucherAmount;
    @Column(name = "permission_code")
    private String permissionCode;
    @Column(name = "create_user")
    private String createUser;
    @Column(name = "create_time")
    private Date createTime;
    @Column(name = "update_user")
    private String updateUser;
    @Column(name = "update_time")
    private Date updateTime;

    public VoucherRequestDetails(String voucherRequestDetailsCode) {
        this.voucherRequestDetailsCode = voucherRequestDetailsCode;
    }
}
