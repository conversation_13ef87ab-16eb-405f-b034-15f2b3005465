package com.gtech.gvcore.dao.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * @ClassName CpgMonthInventorySnapshot
 * @Description CPG库存快照
 * <AUTHOR>
 * @Date 2022/7/6 15:13
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
@Table(name = "gv_cpg_month_inventory_snapshot")
public class CpgMonthInventorySnapshot {

    @Id
    private Long id;

    /**
     * 客户编码
     */
    @Column(name = "customer_code")
    private String customerCode;

    /**
     * cpg code
     */
    @Column(name = "cpg_code")
    private String cpgCode;

    /**
     * 月份字符
     */
    @Column(name = "month")
    private Integer month;

    /**
     * 可用CPG库存数量
     */
    @Column(name = "available")
    private Integer available;

    /**
     * 已分发CPG数量
     */
    @Column(name = "distributed")
    private Integer distributed;

    /**
     * create time
     */
    @Column(name = "create_time")
    private Date createTime;

}
