package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.model.VoucherBooklet;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @Date 2022/3/2 13:55
 */
@Mapper
public interface VoucherBookletMapper extends GTechBaseMapper<VoucherBooklet> {

    @Select("" +
            "SELECT " +
            " MAX( booklet_code )  " +
            "FROM " +
            " gv_voucher_booklet  " +
            "WHERE " +
            " substring( booklet_code, 1, 6 )= #{begin};" +
            "")
    String queryMaxBooklet(String begin);
}
