package com.gtech.gvcore.dao.dto;

import java.util.Date;

import com.gtech.gvcore.dao.model.CustomerOrder;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022年3月23日
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CustomerOrderDto extends CustomerOrder {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1529428553233712410L;
	private String oldStatus;

    // CustomerOrderDetails deleteStatus
    private Integer deleteStatus;

    private Date releaseTimeStart;
    private Date releaseTimeEnd;

    private Date updateTimeStart;
    private Date updateTimeEnd;

}
