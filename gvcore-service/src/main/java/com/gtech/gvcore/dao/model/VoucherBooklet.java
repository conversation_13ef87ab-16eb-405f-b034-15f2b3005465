
package com.gtech.gvcore.dao.model;


import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "gv_voucher_booklet")
@ApiModel(value = "VoucherBooklet)", description = "Voucher booklet.")
public class VoucherBooklet implements Serializable {
    private static final long serialVersionUID = -98785458274135866L;

    public static final String C_ISSUER_CODE = "issuerCode";
    public static final String C_BOOKLET_CODE = "bookletCode";
    public static final String C_BOOKLET_BARCODE = "bookletBarcode";
    public static final String C_VOUCHER_BATCH_CODE = "voucherBatchCode";
    public static final String C_VOUCHER_NO_START = "voucherStartNo";
    public static final String C_VOUCHER_NO_END = "voucherEndNo";
    public static final String C_BOOKLET_PER_NUM = "bookletPerNum";
    public static final String C_STATUS = "status";
    public static final String C_CREATE_TIME = "createTime";
    public static final String C_UPDATE_TIME = "updateTime";
    public static final String C_CREATE_USER = "createUser";
    public static final String C_UPDATE_USER = "updateUser";



    @Id
    private Long id;

    @Column(name = "issuer_code")
    private String issuerCode;
    
    @Column(name = "booklet_code")
    private String bookletCode;
    
    @Column(name = "booklet_barcode")
    private String bookletBarcode;
    
    @Column(name = "voucher_batch_code")
    private String voucherBatchCode;
    
    @Column(name = "voucher_start_no")
    private String voucherStartNo;
    
    @Column(name = "voucher_end_no")
    private String voucherEndNo;

    @Column(name = "booklet_per_num")
    private Integer bookletPerNum;
    
    @Column(name = "`status`")
    private Integer status;
    
    @Column(name = "create_time")
    private Date createTime;
    
    @Column(name = "update_time")
    private Date updateTime;
    
    @Column(name = "create_user")
    private String createUser;
    
    @Column(name = "update_user")
    private String updateUser;

    public void reset() {
        this.issuerCode = null;
        this.bookletCode = null;
        this.bookletBarcode = null;
        this.voucherBatchCode = null;
        this.voucherStartNo = null;
        this.voucherEndNo = null;
        this.bookletPerNum = null;
        this.status = null;
        this.createTime = null;
        this.updateTime = null;
        this.createUser = null;
        this.updateUser = null;
    }


}
