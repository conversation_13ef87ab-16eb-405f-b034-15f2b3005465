package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.dto.DashboardDateDto;
import com.gtech.gvcore.dto.SalesDataDto;
import com.gtech.gvcore.dto.SalesDataResultDto;
import com.gtech.gvcore.dto.TransactionDashboardDataBase;
import com.gtech.gvcore.service.report.impl.bo.SalesBo;
import com.gtech.gvcore.service.report.impl.support.sales.bo.CancelSalesDataBo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.lang.Nullable;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;

@Mapper
public interface TransactionDataMapper extends GTechBaseMapper<TransactionData> {

    @Select("<script>" +
            "select " +
            "t.voucher_code as voucherCode," +
            "t.invoice_number as invoiceNumber " +
            "from gv_transaction_data t " +
            "where " +
            "t.invoice_number &lt;&gt; '' " +
            "and t.invoice_number is not null " +
            "and t.voucher_code in"
            + "<foreach collection=\"voucherCodeList\" item=\"code\" open=\"(\" separator=\",\" close=\")\">"
            + "#{code}"
            + "</foreach> " +

            " <if test=\" type != null and type != '' \"> " +
            " and t.transaction_type = #{type}" +
            " </if> " +
            " and t.success_or_failure = 0 "
            + " group by voucher_code,invoice_number" +
            " ORDER BY create_time desc,t.transaction_code desc " +
            "</script>"
            )
    List<TransactionData> queryVoucherInvoiceNo(@Param("voucherCodeList") List<String> voucherCodeList,@Param("type")String typeEnum);





    @Select("<script>" +
            "select t.voucher_code as voucherCode,IFNULL(t.approve_code,\"default\") as approveCode from gv_transaction_data t where t.voucher_code in"
            + "<foreach collection=\"voucherCodeList\" item=\"code\" open=\"(\" separator=\",\" close=\")\">"
            + "#{code}"
            + "</foreach> " +
            " and t.transaction_type = #{type}"
            + " and t.success_or_failure = '0'"
            + " group by voucher_code,approve_code" +
            " ORDER BY create_time desc " +
            "</script>"
    )
    List<TransactionData> queryVoucherApprovalCode(@Param("voucherCodeList") List<String> voucherCodeList, @Param("type")String type);

    /**
     * 
     * <AUTHOR>
     * @param billNumberList
     * @return
     * @date 2022年6月23日
     */
    List<TransactionData> sumAmountGroupByBillNumber(List<String> billNumberList);

    @Select("<script>" +
            " SELECT denomination FROM gv_transaction_data " +
            " WHERE transaction_id = #{transactionId} " +
            "  AND transaction_type IN " +
            "  <foreach collection=\"transactionTypes\" item=\"type\" open=\"(\" separator=\",\" close=\")\"> " +
            "  #{type}" +
            "  </foreach>" +
            "  <if test=\" null != cpgCode and cpgCode != '' \"> " +
            "  AND cpg_code = #{cpgCode} " +
            "  </if> " +
            "  AND success_or_failure = 0" + //Whitespace and control characters in literals should be explicit
            "  GROUP BY voucher_code" +
            "</script>")
    List<BigDecimal> countAmountByTransactionIdAndType(@Param("transactionId") String transactionId, @Param("transactionTypes") Collection<String> transactionTypes, @Param("cpgCode") @Nullable String cpgCode);

    @Select("<script>" +
            " SELECT COUNT(DISTINCT voucher_code) FROM gv_transaction_data " +
            " WHERE transaction_id = #{transactionId}" +
            " AND transaction_type IN" +
            "     <foreach collection=\"transactionTypes\" item=\"type\" open=\"(\" separator=\",\" close=\")\">"+
            "         #{type}"+
            "     </foreach> "+
            " <if test=\" null != cpgCode and cpgCode != '' \"> " +
            "     AND cpg_code = #{cpgCode} " +
            " </if> " +
            " AND success_or_failure = 0" +

            "</script>")
    Integer countNumByTransactionIdAndType(@Param("transactionId") String transactionId, @Param("transactionTypes") Collection<String> transactionTypes,  @Param("cpgCode") @Nullable String cpgCode);

    /*@Select("<script>" +
            "SELECT " +
            "  IFNULL(article.sap_article_code, '' ) article_code, " +
            "  sum( gtd.denomination ) voucherAmount, " +
            "  sum( cod.discount ) discount, " +
            "  gtd.voucher_code, " +
            "  mp.mop_name, " +
            "  article.mop_code, " +
            "  co.customer_order_code, " +
            "  gtd.transaction_id, " +
            "  cod.customer_order_details_code, " +
            "  sum( cod.voucher_num  )voucherNum"+
            " FROM " +
            "  gv_transaction_data gtd " +
            "  LEFT JOIN gv_cpg cpg ON cpg.cpg_code = gtd.cpg_code " +
            "  LEFT JOIN gv_article_mop article ON article.article_mop_code = cpg.article_mop_code " +
            "  LEFT JOIN gv_customer_order co ON co.customer_order_code = gtd.transaction_id " +
            "  LEFT JOIN gv_means_of_payment mp ON co.means_of_payment_code = mp.means_of_payment_code " +
            "  LEFT JOIN gv_customer_order_details cod ON cod.customer_order_code = co.customer_order_code and cod.denomination = gtd.denomination " +
            " WHERE transaction_type = \"10\" " +
            "AND gtd.transaction_date &gt;= #{request.queryDateTimeStart} " +
            "AND gtd.transaction_date &lt; #{request.queryDateTimeEnd} " +

            " <if test=\"  request.mopCode != null and request.mopCode != ''  \"> " +
            "AND article.mop_code = #{request.mopCode}" +

            " </if> " +

            "AND gtd.outlet_code = #{request.outletCode}  " +
            "AND co.status &lt;&gt; 'Cancel' " +
            "AND cod.delete_status &lt;&gt; '1' " +


            " " +
            "GROUP BY " +

            " <if test=\"  request.xmlType == 'WPUBON_C' and request.mvStore == 'MV04'  \"> " +
            " article.mop_code " +
            " </if> " +

            " <if test=\"  request.xmlType == 'WPUBON_C'  and request.mvStore == 'MV03' \"> " +
            " mp.means_of_payment_code " +
            " </if> " +

            " <if test=\"  request.xmlType == 'WPUBON' and request.mvStore == 'MV04'  \"> " +
            " mp.means_of_payment_code " +
            " </if> " +

            " <if test=\"  request.xmlType == 'WPUBON'  and request.mvStore == 'MV03' \"> " +
            " mp.means_of_payment_code " +
            " </if> " +

            " <if test=\"  request.xmlType == 'WPUUMS' \"> " +
            " article.article_code" +
            " </if> " +



            "</script>")*/
    List<SalesDataResultDto> querySalesData(@Param("request")SalesDataDto param);





    @Select("<script>" +
            "" +
            "SELECT " +
            " t.voucher_code, " +
            " t.issuer_code," +
            " v.denomination, " +
            " t.outlet_code, " +
            " outlet.outlet_type, " +
            " t.transaction_id," +
            " v.mop_code  " +
            "FROM " +
            " gv_transaction_data t " +
            " LEFT JOIN gv_outlet outlet ON outlet.outlet_code = t.outlet_code  " +
            " LEFT JOIN gv_voucher v ON t.voucher_code = v.voucher_code  " +
            "WHERE " +
            " transaction_type = #{transactionType}  " +
            " AND t.create_time &gt;= date_format( date_sub( #{request}, INTERVAL 1 HOUR ), '%Y-%m-%d %H' )  " +
            " AND t.create_time &lt;= date_format( #{request}, '%Y-%m-%d %H' )  " +
            /*" <if test=\" null != mopType and '' != mopType\"> " +
            " AND v.mop_code = #{mopType} " +
            " </if> " +*/

            "GROUP BY " +
            " t.voucher_code"+

            "</script>")
    List<TransactionDashboardDataBase> transctionDashboard(@Param("request") Date request,@Param("transactionType") String transactionType);

    @Select("<script> " +
            " SELECT " +
            " v.voucher_code, " +
            " v.denomination " +
            "FROM " +
            " gv_transaction_data t " +
            " LEFT JOIN gv_voucher v ON t.voucher_code = v.voucher_code  " +
            "WHERE " +
            " t.issuer_code = #{issuerCode} " +
            "" +
            " AND t.create_time &gt;= date_format( #{request.startDate}, '%Y-%m-%d' )  " +
            " AND t.create_time &lt;= date_format(#{request.endDate},'%Y-%m-%d')" +
            "" +
            " AND transaction_type = #{transactionType}  " +

            " <if test=\" null != outletCode and '' != outletCode\"> " +
            " AND outlet_code = #{outletCode}  " +
            " </if> " +

            " <if test=\" null != mopType and '' != mopType\"> " +
            " AND v.mop_code = #{mopType} " +
            " </if> " +

            "</script>")
    List<TransactionData> selectTransactionDataByDashboard(@Param("outletCode") String mv01, @Param("mopType") String mopType, @Param("transactionType") String transactionType, @Param("request")DashboardDateDto dto, @Param("issuerCode")String  issuerCode);

    @Select("<script> " +

            " SELECT " +
            " SUM(t.amount) amount " +
            "FROM " +
            " gv_dashboard t " +
            "WHERE " +
            " t.issuer_code = #{issuerCode} " +
            "" +
            " AND t.`datetime` &gt;= date_format( #{year.startDate}, '%Y-%m-%d' )  " +
            " AND t.`datetime` &lt;= date_format(#{year.endDate},'%Y-%m-%d')" +
            "" +
            " AND dashboard_type = '1'  " +
            "</script>")
    BigDecimal selectTotalRedemptionData(@Param("year") DashboardDateDto year,@Param("issuerCode") String issuerCode);


    @Select("" +
            "SELECT " +
            " t.voucher_code voucherCode " +
            "FROM " +
            " gv_transaction_data t" +
            " where t.transaction_id = #{customerOrderCode} " +
            "  AND success_or_failure = 0 " +
            " group by t.voucher_code " +
            "")
    List<String> selectVoucherByCustomerOrder(@Param("customerOrderCode")String customerOrderCode);

    List<TransactionData> selectTransactionList(@Param("tableName") String tableName, @Param("batchNumber") String batchNumber, @Param("successOrFailure") String successOrFailure);

    @Select("SELECT * FROM gv_transaction_data" +
            "ORDER BY create_time DESC" +
            "LIMIT 1")
    TransactionData selectMaxCreateTime();

    List<CancelSalesDataBo> selectCancelSalesTransactionList(@Param("maxCreateTime")Date maxCreateTime);

}
