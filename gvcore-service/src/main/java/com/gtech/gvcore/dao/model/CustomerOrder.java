package com.gtech.gvcore.dao.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
@Table(name = "gv_customer_order")
public class CustomerOrder implements Serializable {

    private static final long serialVersionUID = -6922639486214420862L;
    public static final String C_CUSTOMER_ORDER_CODE = "customerOrderCode";
    public static final String C_TRACK_NO = "trackNo";
    public static final String C_LOGISTICS_NAME = "logisticsName";
    public static final String C_ISSUER_CODE = "issuerCode";
    public static final String C_OUTLET_CODE = "outletCode";
    public static final String C_INVOICE_NO = "invoiceNo";
    public static final String C_CUSTOMER_TYPE = "customerType";
    public static final String C_CUSTOMER_CODE = "customerCode";
    public static final String C_PURCHASE_ORDER_NO = "purchaseOrderNo";
    public static final String C_STATUS = "status";
    public static final String C_RELEASE_TIME = "releaseTime";
    public static final String C_VOUCHER_BATCH_CODE= "voucherBatchCode";
    public static final String C_CREATE_TIME= "createTime";
    public static final String C_UPDATE_TIME= "updateTime";
    public static final String C_MOP_CODE= "mopCode";

    @Id
    private Long id;
    @Column(name = "customer_order_code")
    private String customerOrderCode;
    @Column(name = "issuer_code")
    private String issuerCode;
    @Column(name = "outlet_code")
    private String outletCode;
    @Column(name = "purchase_order_no")
    private String purchaseOrderNo;
    @Column(name = "mop_code")
    private String mopCode;
    @Column(name = "means_of_payment_code")
    private String meansOfPaymentCode;
    @Column(name = "voucher_num")
    private Integer voucherNum;
    @Column(name = "voucher_amount")
    private BigDecimal voucherAmount;
    @Column(name = "discount")
    private BigDecimal discount;
    @Column(name = "amount")
    private BigDecimal amount;
    @Column(name = "currency_code")
    private String currencyCode;
    @Column(name = "customer_code")
    private String customerCode;
    @Column(name = "customer_name")
    private String customerName;
    @Column(name = "customer_type")
    private String customerType;
    @Column(name = "company_name")
    private String companyName;
    @Column(name = "contact_first_name")
    private String contactFirstName;
    @Column(name = "contact_last_name")
    private String contactLastName;
    @Column(name = "contact_phone")
    private String contactPhone;
    @Column(name = "contact_email")
    private String contactEmail;
    @Column(name = "product_category_code")
    private String productCategoryCode;
    @Column(name = "discount_type")
    private String discountType;
    @Column(name = "invoice_no")
    private String invoiceNo;
    @Column(name = "delive_type")
    private Integer deliveType;
    @Column(name = "logistics_name")
    private String logisticsName;
    @Column(name = "track_no")
    private String trackNo;
    @Column(name = "status")
    private String status;

    @Column(name = "voucher_batch_code")
    private String voucherBatchCode;

    @Column(name = "sales_note_url")
    private String salesNoteUrl;

    /**
     * release time
     */
    @Column(name = "release_time")
    private Date releaseTime;

    @Column(name = "create_user")
    private String createUser;
    @Column(name = "create_time")
    private Date createTime;
    @Column(name = "update_user")
    private String updateUser;
    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "delivery_date")
    private String deliveryDate;

    @Column(name = "order_address")
    private String orderAddress;

    @Column(name = "awb")
    private String awb;

    @Column(name = "scan_of_receipt")
    private String scanOfReceipt;

    public CustomerOrder(String customerOrderCode) {
        this.customerOrderCode = customerOrderCode;
    }
}
