package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.common.request.pos.QueryPosListRequest;
import com.gtech.gvcore.common.response.pos.PosResponse;
import com.gtech.gvcore.dao.model.Pos;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PosMapper extends GTechBaseMapper<Pos> {

    /**
     * 
     * <AUTHOR>
     * @param posCodeList
     * @return
     * @date 2022年6月23日
     */
    List<Pos> queryByPosCodeList(List<String> posCodeList);
    
    
  /*  @Select("<script>" +

            "SELECT " +

            " p.pos_code, " +
            " p.issuer_code, " +
            " p.pos_name, " +
            " p.machine_id, " +
            " p.pos_entry_mode_id, " +
            " p.outlet_code, " +
            " p.`status`, " +
            " p.create_user, " +
            " p.create_time, " +
            " p.update_user, " +
            " p.update_time, " +
            " p.account, " +
            " p.PASSWORD," +
            " m.merchant_name  " +
            "FROM " +
            " gv_pos  p " +
            " LEFT JOIN gv_outlet o ON p.outlet_code = o.outlet_code " +
            " LEFT JOIN gv_merchant m ON m.merchant_code = o.merchant_code " +
            " <where>" +
                " <if test=\" request.posName != null and request.posName != '' \"> " +
                    " AND p.pos_name LIKE CONCAT('%',#{request.posName},'%') " +
                " </if> " +
                " <if test=\" request.status != null and request.status != '' \"> " +
                    "AND p.`status` = #{request.status} " +
                " </if> " +
                " <if test=\" request.outletCode != null and request.outletCode != '' \"> " +
                    "AND p.outlet_code = #{request.outletCode}   " +
                " </if> " +
                " <if test=\" request.issuerCode != null and request.issuerCode != '' \"> " +
                    "AND p.issuer_code = #{request.issuerCode}   " +
                " </if> " +
                " <if test=\" request.merchantCode != null and request.merchantCode != '' \"> " +
                    "AND m.merchant_code = #{request.merchantCode} " +
                " </if> " +
            " </where>" +
            "ORDER BY " +

            " m.merchant_name  " +

            "</script>")*/
    List<PosResponse> queryPosList(@Param("request") QueryPosListRequest request);

}
