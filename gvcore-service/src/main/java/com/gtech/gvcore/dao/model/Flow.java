package com.gtech.gvcore.dao.model;

import java.util.Date;
import javax.persistence.*;

import lombok.Data;

@Table(name = "gv_flow")
@Data
public class Flow {
    /**
     * id
     */
    @Id
    private Long id;

    /**
     * issuer code
     */
    @Column(name = "issuer_code")
    private String issuerCode;

    /**
     * flow code
     */
    @Column(name = "flow_code")
    private String flowCode;

    /**
     * flow name
     */
    @Column(name = "flow_name")
    private String flowName;

    /**
     * remark
     */
    private String remark;

    /**
     * status
     */
    private Integer status;

    /**
     * create time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update time
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * create user
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * update user
     */
    @Column(name = "update_user")
    private String updateUser;
}