package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.common.request.receive.QueryCancelVoucherReceiveRequest;
import com.gtech.gvcore.dao.dto.VoucherReceiveDto;
import com.gtech.gvcore.dao.model.CancelVoucherReceive;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/3/8 15:41
 */
@Mapper
public interface CancelVoucherReceiveMapper extends GTechBaseMapper<CancelVoucherReceive> {

    List<VoucherReceiveDto> selectCancelByXml( QueryCancelVoucherReceiveRequest request);

}
