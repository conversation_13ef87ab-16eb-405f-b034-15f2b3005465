package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.dto.CountStatusByItemCodeDto;
import com.gtech.gvcore.dao.model.DistributionItemVoucher;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.List;

/**
 * @ClassName DistributionItemVoucherMapper
 * @Description 分发的voucher信息
 * <AUTHOR>
 * @Date 2022/8/9 10:53
 * @Version V1.0
 **/
@Mapper
public interface DistributionItemVoucherMapper extends GTechBaseMapper<DistributionItemVoucher> {

    @Select("" +
            "SELECT COUNT(DISTINCT voucher_code)  " +
            "FROM `gv_distribution_item_voucher` " +
            "WHERE `customer_code` = #{customerCode} " +
            "AND `status` = 'Distributed' " +
            "AND `cpg_code` = #{cpgCode}" +
            "")
    Integer countDistributedVoucher(@Value("customerCode") String customerCode, 
                                    @Value("cpgCode") String cpgCode);


    @Select("" +
            "select MIN(`create_time`) FROM `gv_distribution_item_voucher` " +
            "WHERE `distribution_item_code` = #{distributionItemCode} " +
            "")
    Date minCreateTimeByItemCode (@Value("distributionItemCode") String distributionItemCode);

    @Select("select status, count(1) as num from gv_distribution_item_voucher where 1 = 1 group by status")
    List<CountStatusByItemCodeDto> countStatusByItemCode (@Value("distributionItemCode") String distributionItemCode);

}
