package com.gtech.gvcore.dao.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * @ClassName ReportTempLiabilitySStructure
 * @Description gv_report_temp_liability_s_structure
 * <p>
 *      DROP TABLE `gv_report_temp_liability_s_structure`;
 *      CREATE TABLE `gv_report_temp_liability_s_structure`
 *      (
 *          `id`                      INT PRIMARY KEY AUTO_INCREMENT NOT NULL COMMENT 'id auto increment',
 *          `issuer_code`             VARCHAR(64)                    NOT NULL COMMENT 'issuer code',
 *          `merchant_code`           VARCHAR(64)                    NULL COMMENT 'merchant code',
 *          `outlet_code`             VARCHAR(64)                    NULL COMMENT 'outlet code',
 *          `cpg_code`                VARCHAR(64)                    NOT NULL COMMENT 'cpg code',
 *          `activated_amount`        DECIMAL(13, 2) DEFAULT 0       NOT NULL COMMENT 'activated number',
 *          `purchased_amount`        DECIMAL(13, 2) DEFAULT 0       NOT NULL COMMENT 'purchased number',
 *          `deactivated_amount`      DECIMAL(13, 2) DEFAULT 0       NOT NULL COMMENT 'deactivated number',
 *          `expired_amount`          DECIMAL(13, 2) DEFAULT 0       NOT NULL COMMENT 'expired number',
 *          `recently_expired_amount` DECIMAL(13, 2) DEFAULT 0       NOT NULL COMMENT 'expired number',
 *          INDEX `idx_liability_s_merchant_outlet` (`merchant_code`, `outlet_code`),
 *          INDEX `idx_liability_s_cpg` (`cpg_code`),
 *          INDEX `idx_liability_s_issuer` (`issuer_code`)
 *      ) COMMENT 'Gv Report Temp Liability Summary Structure Table.';
 * </p>
 * <AUTHOR>
 * @Date 2023/4/18 15:32
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
@Table(name = "gv_report_temp_liability_s_structure")
public class ReportTempLiabilitySStructure {

    @Id
    private String id;

    @Column(name = "issuer_code")
    private String issuerCode;

    @Column(name = "merchant_code")
    private String merchantCode;

    @Column(name = "outlet_code")
    private String outletCode;

    @Column(name = "cpg_code")
    private String cpgCode;

    @Column(name = "activated_amount")
    private BigDecimal activatedAmount;

    @Column(name = "purchased_amount")
    private BigDecimal purchasedAmount;

    @Column(name = "deactivated_amount")
    private BigDecimal deactivatedAmount;

    @Column(name = "expired_amount")
    private BigDecimal expiredAmount;

    @Column(name = "recently_expired_amount")
    private BigDecimal recentlyExpiredAmount;


}
