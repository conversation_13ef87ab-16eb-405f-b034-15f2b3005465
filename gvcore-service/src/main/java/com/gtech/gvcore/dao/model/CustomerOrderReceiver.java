package com.gtech.gvcore.dao.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
@Table(name = "gv_customer_order_receiver")
public class CustomerOrderReceiver implements Serializable {

    private static final long serialVersionUID = 4645734023223493975L;
    @Id
    private Long id;
    @Column(name = "customer_order_code")
    private String customerOrderCode;
    @Column(name = "shipping_address")
    private String shippingAddress;
    @Column(name = "customer_remarks")
    private String customerRemarks;
    @Column(name = "quotation")
    private String quotation;
    @Column(name = "invoice")
    private String invoice;
    @Column(name = "sales_order")
    private String salesOrder;
    @Column(name = "payment_voucher")
    private String paymentVoucher;
    @Column(name = "create_user")
    private String createUser;
    @Column(name = "create_time")
    private Date createTime;
    @Column(name = "update_user")
    private String updateUser;
    @Column(name = "update_time")
    private Date updateTime;

    public CustomerOrderReceiver(String customerOrderCode) {
        this.customerOrderCode = customerOrderCode;

    }
}
