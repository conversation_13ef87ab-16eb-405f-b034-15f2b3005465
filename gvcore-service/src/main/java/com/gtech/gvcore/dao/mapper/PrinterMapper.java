package com.gtech.gvcore.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.dto.PrinterDto;
import com.gtech.gvcore.dao.model.Printer;

@Mapper
public interface PrinterMapper extends GTechBaseMapper<Printer> {

    /**
     * 
     * <AUTHOR>
     * @param printerCodeList
     * @return
     * @date 2022年3月7日
     */
    List<Printer> queryBasicInfoByPrinterCodeList(List<String> printerCodeList);

    /**
     * 
     * @param dto
     * @return
     * <AUTHOR>
     * @date 2022年5月25日
     */
    int countByPrinterCodeList(PrinterDto dto);

}