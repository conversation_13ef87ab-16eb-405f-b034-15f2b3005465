package com.gtech.gvcore.dao.mapper;

import com.gtech.gvcore.dao.model.ReportTempLiabilityDStructure;
import com.gtech.gvcore.dao.model.ReportTempLiabilitySStructure;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.report.impl.bo.AgingBo;
import com.gtech.gvcore.service.report.impl.bo.AgingVoucherTransactionBoVoucher;
import com.gtech.gvcore.service.report.impl.bo.BookletInventoryDetailedBo;
import com.gtech.gvcore.service.report.impl.bo.BookletInventorySummaryBo;
import com.gtech.gvcore.service.report.impl.bo.CancelRedeemBo;
import com.gtech.gvcore.service.report.impl.bo.CancelSalesBo;
import com.gtech.gvcore.service.report.impl.bo.DeactivatedBo;
import com.gtech.gvcore.service.report.impl.bo.EgvTrackingActivateTransactionBo;
import com.gtech.gvcore.service.report.impl.bo.EgvTrackingBo;
import com.gtech.gvcore.service.report.impl.bo.EgvVoucherTrackingRedeemTransactionBo;
import com.gtech.gvcore.service.report.impl.bo.ExpiryGvBo;
import com.gtech.gvcore.service.report.impl.bo.FinanceRetailAdminRedemptionBo;
import com.gtech.gvcore.service.report.impl.bo.FinanceRetailAdminSalesBo;
import com.gtech.gvcore.service.report.impl.bo.GoodsBookletBo;
import com.gtech.gvcore.service.report.impl.bo.GoodsInTransitDetailBo;
import com.gtech.gvcore.service.report.impl.bo.GoodsInTransitSummaryBo;
import com.gtech.gvcore.service.report.impl.bo.GoodsReceiveBo;
import com.gtech.gvcore.service.report.impl.bo.InventoryDetailedBo;
import com.gtech.gvcore.service.report.impl.bo.InventorySummaryBo;
import com.gtech.gvcore.service.report.impl.bo.PartnerDetailBo;
import com.gtech.gvcore.service.report.impl.bo.PerformanceBo;
import com.gtech.gvcore.service.report.impl.bo.ReactivatedBo;
import com.gtech.gvcore.service.report.impl.bo.RedemptionBo;
import com.gtech.gvcore.service.report.impl.bo.ReissuedBo;
import com.gtech.gvcore.service.report.impl.bo.SalesAnyVoucherCodeBo;
import com.gtech.gvcore.service.report.impl.bo.SalesBo;
import com.gtech.gvcore.service.report.impl.bo.SalesVoucherToRemarkBo;
import com.gtech.gvcore.service.report.impl.bo.TransactionDetailedBo;
import com.gtech.gvcore.service.report.impl.bo.VoucherMovementBo;
import com.gtech.gvcore.service.report.impl.bo.VoucherPrintingBo;
import com.gtech.gvcore.service.report.impl.bo.VoucherRequestAllocateReceiveBo;
import com.gtech.gvcore.service.report.impl.bo.VoucherReturnAndTransferBo;
import com.gtech.gvcore.service.report.impl.param.*;
import com.gtech.gvcore.service.report.impl.support.life.bo.VoucherLifeCycleMovementBo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.Date;
import java.util.List;

/**
 * @ClassName ReportMapper
 * @Description report mapper
 * <AUTHOR>
 * @Date 2023/1/5 10:20
 * @Version V1.0
 **/
@Mapper
public interface ReportBusinessMapper {

    // --------------------------------------- GoodsInTransit BEGIN ---------------------------------------

    List<GoodsInTransitSummaryBo> selectGoodsInTransitSummary(GoodsInTransitQueryData goodsInTransit);

    List<GoodsInTransitDetailBo> selectGoodsInTransitDetail(GoodsInTransitQueryData goodsInTransit, @Param("rowBounds") RowBounds rowBounds);

    List<GoodsReceiveBo> selectReceiveTimeByVoucherCodeAndReceiveCode(@Param("receiveCode") String receiveCode);

    List<GoodsBookletBo> selectGoodsBookletDto(@Param("voucherStart") String voucherStart, @Param("voucherEnd") String voucherEnd);

    // --------------------------------------- GoodsInTransit END ---------------------------------------


    // --------------------------------------- VoucherLifeCycle BEGIN ---------------------------------------

    List<VoucherLifeCycleMovementBo> selectVoucherLifeCycleVoucherMovement(@Param("voucherCode") String voucherCode);

    Integer voucherLifeCycleEexistVoucher(@Param("voucherCodeNum") final long voucherCodeNum, @Param("tableIndex") final int tableIndex);

    Date selectVoucherLifeCycleReceiveTime(@Param("voucherCode") String voucherCode, @Param("receiveCode") String receiveCode);

    List<TransactionData> selectVoucherLifeCycleTransactionDataByVoucherCodeNum(@Param("voucherCodeNum") final long voucherCodeNum, @Param("tableIndex") final int tableIndex);

    // --------------------------------------- VoucherLifeCycle END ---------------------------------------


    // --------------------------------------- VoucherMovement BEGIN ---------------------------------------

    List<VoucherMovementBo> selectVoucherMovement(VoucherMovementSummaryQueryData param, @Param("rowBounds") RowBounds rowBounds);

    // --------------------------------------- VoucherMovement END ---------------------------------------


    // --------------------------------------- Deactivated BEGIN ---------------------------------------

    List<DeactivatedBo> selectDeactivated(DeactivatedQueryData param, @Param("rowBounds") RowBounds rowBounds);

    // --------------------------------------- Deactivated END ---------------------------------------


    // --------------------------------------- Reactivated BEGIN ---------------------------------------

    List<ReactivatedBo> selectReactivated(ReactivatedQueryData param, @Param("rowBounds") RowBounds rowBounds);

    // --------------------------------------- Reactivated END ---------------------------------------


    // --------------------------------------- BookletInventory BEGIN ---------------------------------------

    List<BookletInventoryDetailedBo> bookletInventoryDetailed(@Param("request") BookletInventoryQueryData request, @Param("rowBounds") RowBounds rowBounds);

    List<BookletInventorySummaryBo> bookletInventorySummary(@Param("request") BookletInventoryQueryData request);

    // --------------------------------------- BookletInventory END ---------------------------------------

    // --------------------------------------- CancelRedeem END ---------------------------------------

    List<CancelRedeemBo> selectCancelRedeem(CancelRedeemQueryData queryData, @Param("rowBounds") RowBounds rowBounds);

    // --------------------------------------- CancelRedeem BEGIN ---------------------------------------


    // --------------------------------------- CancelSales END ---------------------------------------

    List<CancelSalesBo> selectCancelSales(CancelSalesQueryData cancelSalesDetail, @Param("rowBounds") RowBounds rowBounds);

    // --------------------------------------- CancelSales BEGIN ---------------------------------------


    // --------------------------------------- EgvTrackingQ BEGIN ---------------------------------------

    List<EgvTrackingBo> selectEgvTrackingVoucher(EgvTrackingQueryData request, @Param("rowBounds") RowBounds rowBounds);

    List<EgvTrackingActivateTransactionBo> selectEgvTrackingActivateTransactionDate(@Param("voucherCodeList") List<String> voucherCodeList,
                                                                                    @Param("transactionTypes") List<String> transactionTypes);

    List<EgvVoucherTrackingRedeemTransactionBo> selectEgvTrackingRedeemTransactionDate(@Param("voucherCodeList") List<String> voucherCodeList,
                                                                                       @Param("transactionTypes") List<String> transactionTypes);


    // --------------------------------------- EgvTrackingQ END ---------------------------------------


    // --------------------------------------- ExpiryGv BEGIN ---------------------------------------

    List<ExpiryGvBo> selectExpiryGv(ExpiryGvQueryData param, @Param("rowBounds") RowBounds rowBounds);
    List<String> selectExpiryGvOldData(ExpiryGvMigrationDataQueryData param, @Param("rowBounds") RowBounds rowBounds);

    // --------------------------------------- ExpiryGv END ---------------------------------------


    // --------------------------------------- FINANCE RETAIL ADMIN SALES BEGIN ---------------------------------------

    List<FinanceRetailAdminSalesBo> financeRetailAdminSales(FinanceRetailAdminSalesQueryData request, @Param("rowBounds") RowBounds rowBounds);

    // --------------------------------------- FINANCE RETAIL ADMIN SALES END ---------------------------------------

    // --------------------------------------- FinanceRetailAdmin BEGIN ---------------------------------------

    List<FinanceRetailAdminRedemptionBo> selectFinanceRetailRedeemAdmin(FinanceRetailAdminRedemptionQueryData request, @Param("rowBounds") RowBounds rowBounds);


    // --------------------------------------- FinanceRetailAdmin END ---------------------------------------


    // --------------------------------------- Inventory BEGIN ---------------------------------------

    List<InventoryDetailedBo> inventoryDetailed(@Param("request") InventoryQueryData request);

    List<InventorySummaryBo> inventorySummary(@Param("request") InventoryQueryData request, @Param("rowBounds") RowBounds rowBounds);

    // --------------------------------------- Inventory END ---------------------------------------


    // --------------------------------------- LatestGvStatus BEGIN ---------------------------------------

    List<Voucher> latestGvStatusReport(LatestGvStatusQueryData latestGvStatus, @Param("rowBounds") RowBounds rowBounds);

    // --------------------------------------- LatestGvStatus END ---------------------------------------


    // --------------------------------------- LiabilityDetail BEGIN ---------------------------------------

    List<ReportTempLiabilityDStructure> liabilityDetailReport(LiabilityDetailQueryData liabilityDetailQueryData, @Param("rowBounds") RowBounds rowBounds);

    List<ReportTempLiabilitySStructure> liabilitySummaryReport(LiabilitySummaryQueryData liabilityDetailQueryData);

    // --------------------------------------- LiabilityDetail END ---------------------------------------


    // --------------------------------------- Partner BEGIN ---------------------------------------

    List<PartnerDetailBo> partnerSalesDetail(PartnerDetailQueryData param, @Param("rowBounds") RowBounds rowBounds);

    // --------------------------------------- Partner END ---------------------------------------


    // --------------------------------------- Redemption BEGIN ---------------------------------------

    List<RedemptionBo> selectRedemption(RedemptionQueryData request, @Param("rowBounds") RowBounds rowBounds);

    // --------------------------------------- Redemption END ---------------------------------------


    // --------------------------------------- Reissued BEGIN ---------------------------------------

    List<ReissuedBo> selectReissued(ReissuedQueryData reissuedQueryData, @Param("rowBounds") RowBounds rowBounds);

    // --------------------------------------- Reissued END ---------------------------------------

    // --------------------------------------- Sales BEGIN ---------------------------------------

    List<SalesBo> salesReport(SalesQueryData salesDetailed, @Param("rowBounds") RowBounds rowBounds);

    List<SalesVoucherToRemarkBo> selectSalesIssuerHandlingRemarkByVoucherCode(@Param("voucherCodeList") List<String> voucherCodeList);

    List<SalesAnyVoucherCodeBo> selectSalesAnyVoucherCodeByTransactionId(@Param("transactionIdList") List<String> transactionIdList);

    // --------------------------------------- Sales END ---------------------------------------

    // --------------------------------------- TransactionDetailed BEGIN ---------------------------------------

    List<TransactionDetailedBo> transactionDetailReport(TransactionDetailQueryData request, @Param("rowBounds") RowBounds rowBounds);

    // --------------------------------------- TransactionDetailed END ---------------------------------------

    // --------------------------------------- Performance BEGIN ---------------------------------------

    List<PerformanceBo> selectPerformance(PerformanceQueryData queryData, @Param("rowBounds") RowBounds rowBounds);

    // --------------------------------------- Performance END ---------------------------------------

    // --------------------------------------- AGING BEGIN ---------------------------------------

    List<AgingBo> selectAgingVoucher(AgingReportQueryParamData request, @Param("rowBounds") RowBounds rowBounds);

    List<AgingVoucherTransactionBoVoucher> selectAgingVoucherTransactionData(AgingReportQueryParamData.AgingReportRedeemQueryParam param, @Param("rowBounds") RowBounds rowBounds);

    // --------------------------------------- AGING END ---------------------------------------

    // --------------------------------------- VOUCHER_REQUEST_ALLOCATE_RECEIVE BEGIN ---------------------------------------

    List<VoucherRequestAllocateReceiveBo> selectVoucherRequestAllocateReceive(VoucherRequestAllocateQueryDate param, @Param("rowBounds") RowBounds rowBounds);

    // --------------------------------------- VOUCHER_REQUEST_ALLOCATE_RECEIVE END ---------------------------------------

    // --------------------------------------- VoucherPrinting BEGIN ---------------------------------------

    List<VoucherPrintingBo> selectVoucherPrinting(VoucherPrintingQueryDate param, @Param("rowBounds") RowBounds rowBounds);

    // --------------------------------------- VoucherPrinting END ---------------------------------------

    // --------------------------------------- VoucherReturnAndTransfer BEGIN ---------------------------------------

    List<VoucherReturnAndTransferBo> selectReturnTransfer(VoucherReturnAndTransferQueryDate param, @Param("rowBounds") RowBounds rowBounds);

    // --------------------------------------- VoucherReturnAndTransfer END ---------------------------------------

    // ---------------------------------------  BEGIN ---------------------------------------
    // ---------------------------------------  END ---------------------------------------

}
