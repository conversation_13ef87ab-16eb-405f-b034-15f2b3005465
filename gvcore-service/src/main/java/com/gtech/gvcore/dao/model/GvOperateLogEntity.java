package com.gtech.gvcore.dao.model;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import java.io.Serializable;

/**
 * (GvOperateLog)实体类
 *
 * <AUTHOR>
 * @since 2022-02-28 10:20:22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "gv_operate_log")
@ApiModel(value = "GvOperateLogEntity)", description = "OperateLog")
public class GvOperateLogEntity implements Serializable {

    public static final String C_CREATE_TIME = "createTime";

    private static final long serialVersionUID = 400376507850557159L;

    public static final String C_METHOD = "method";

    public static final String C_BUSINESS_CODE = "businessCode";

    public static final String C_OPERATE_USER = "operateUser";

    @Id
    private Long id;

    @Column(name = "business_code")
    private String businessCode;

    private String input;

    private String output;

    private String method;

    @Column(name = "operate_user")
    private String operateUser;

    @Column(name = "first_name")
    private String firstName;

    @Column(name = "last_name")
    private String lastName;

    private String success;

    private String remark;

    @Column(name = "create_time")
    private Date createTime;
}

