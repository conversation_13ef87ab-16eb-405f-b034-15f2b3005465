package com.gtech.gvcore.dao.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.model.VoucherReceiveBatch;

@Mapper
public interface VoucherReceiveBatchMapper extends GTechBaseMapper<VoucherReceiveBatch> {
	List<VoucherReceiveBatch> query(Map<String, Object> map);

	void updateVoucherBatchReceivedNum(@Param("receiveBatchCode") String receiveBatchCode, @Param("receivedNum") int receivedNum);
}