package com.gtech.gvcore.dao.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "gv_distribution_item")
public class DistributionItem {

    public static final String C_DISTRIBUTION_CODE = "distributionCode";
    public static final String C_DISTRIBUTION_ITEM_CODE = "distributionItemCode";
    public static final String C_STATUS = "status";

    /**
     * id
     */
    @Id
    private Long id;

    /**
     * 分发编码，唯一标识符
     */
    @Column(name = "distribution_item_code")
    private String distributionItemCode;

    /**
     * 分发编码
     */
    @Column(name = "distribution_code")
    private String distributionCode;

    /**
     * 所属customer
     */
    @Column(name = "customer_code")
    private String customerCode;

    /**
     * cpg code
     */
    @Column(name = "cpg_code")
    private String cpgCode;

    /**
     * cpg名称快照，在实际执行分发时录入
     */
    @Column(name = "cpg_name_snapshot")
    private String cpgNameSnapshot;

    /**
     * cpg到期时间的快照，在实际执行分发时录入
     */
    @Column(name = "cpg_expiry_date_snapshot")
    private Date cpgExpiryDateSnapshot;

    /**
     * 每封电子邮件的优惠券数量
     */
    @Column(name = "vouchers_per_email")
    private Integer vouchersPerEmail;

    /**
     * 邮箱地址，多个;拼接
     */
    @Column(name = "recipients")
    private String recipients;

    /**
     * 收件人数量,如果recipients存在多个相同邮箱地址,该统计不做去重
     */
    @Column(name = "recipients_num")
    private Integer recipientsNum;

//    /**
//     * 分发状态.Available,Distributing,Distributed,Fail
//     * @see com.gtech.gvcore.common.enums.DistributionItemStatusEnum
//     */
//    @Column(name = "status")
//    private String status;

    /**
     * 总价值(金额)
     */
    @Column(name = "voucher_amount")
    private BigDecimal voucherAmount;

    /**
     * create user code
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * create time.
     */
    @Column(name = "create_time")
    private Date createTime;

}