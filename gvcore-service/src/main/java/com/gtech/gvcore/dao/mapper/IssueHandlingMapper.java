package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.dto.IssueHandlingDto;
import com.gtech.gvcore.dao.model.IssueHandling;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface IssueHandlingMapper extends GTechBaseMapper<IssueHandling> {
    
	/**
	 * 
	 * @param dto
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月6日
	 */
	int updateStatus(IssueHandlingDto dto);
	
	/**
	 * 
	 * @param dto
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月6日
	 */
	List<IssueHandling> selectSelective(IssueHandlingDto dto);
	
	/**
	 * 
	 * @param dto
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月16日
	 */
	int updateByEdit(IssueHandlingDto dto);

	@Select("<script> " +
			" select remarks FROM gv_issue_handling " +
			" WHERE issue_handling_code = " +
			" (SELECT issue_handling_code " +
			" FROM gv_issue_handling_details " +
			" WHERE voucher_code in " +
			 "<foreach collection=\"voucherCode\" item=\"code\" open=\"(\" separator=\",\" close=\")\">"
				+"#{code}"
			+ "</foreach> "+
			" ORDER BY id DESC " +
			" LIMIT 1)" +
			"</script>")
    String getNotesByMaxIdInVoucherCodes(@Param("voucherCode")List<String> voucherCode);
	
	
	@Select("<script> " +
			"SELECT voucher_code FROM gv_transaction_data " +
			"    WHERE transaction_id = #{transactionId} " +
			"    ORDER BY create_time DESC " +
			"    LIMIT 1 " +
			"</script>")
    List<String> getNotesByMaxTransactionCode(@Param("transactionId")String transactionId);
}