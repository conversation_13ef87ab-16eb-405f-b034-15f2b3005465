package com.gtech.gvcore.dao.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

@Data
@Table(name = "gv_issue_handling_proof")
public class IssueHandlingProof {
    /**
     * id
     */
    @Id
    private Long id;

    /**
     * issue handling proof code.
     */
    @Column(name = "issue_handling_proof_code")
    private String issueHandlingProofCode;

    /**
     * issue handling code.
     */
    @Column(name = "issue_handling_code")
    private String issueHandlingCode;

    /**
     * proof type
     */
    @Column(name = "proof_type")
    private String proofType;

    /**
     * proof file name
     */
    @Column(name = "proof_file_name")
    private String proofFileName;

    /**
     * proof file url
     */
    @Column(name = "proof_file_url")
    private String proofFileUrl;

    /**
     * permission code
     */
    @Column(name = "permission_code")
    private String permissionCode;

    /**
     * create user
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * create time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update user
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * update time
     */
    @Column(name = "update_time")
    private Date updateTime;

}