package com.gtech.gvcore.dao.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "gv_scheduler_report")
public class SchedulerReport {
    @Id
    private Long id;
    @Column(name = "scheduler_report_code")
    private String schedulerReportCode;
    @Column(name = "scheduler_name")
    private String schedulerName;
    @Column(name = "scheduler_id")
    private Long schedulerId;
    @Column(name = "scheduler_cron")
    private String schedulerCron;
    @Column(name = "execution_time")
    private Date executionTime;
    @Column(name = "last_execution_time")
    private Date lastExecutionTime;
    /**
     * 执行频率
     * {@link com.gtech.gvcore.common.enums.FrequencyEnum}
     */
    @Column(name = "frequency")
    private String frequency;
    @Column(name = "every")
    private Integer every;
    /**
     * 结束执行类型 时间/次数/永远
     * {@link com.gtech.gvcore.common.enums.RepeatEndEnum}
     */
    @Column(name = "repeat_end")
    private String repeatEnd;
    /**
     * 结束执行次数
     */
    @Column(name = "repeat_end_after")
    private Integer repeatEndAfter;
    /**
     * 结束执行时间
     */
    @Column(name = "repeat_end_time")
    private Date repeatEndTime;
    @Column(name = "report_name")
    private String reportName;
    @Column(name = "report_type")
    private Integer reportType;
    @Column(name = "issuer_code")
    private String issuerCode;
    @Column(name = "merchant_code")
    private String merchantCode;
    @Column(name = "merchant_outlet_code")
    private String merchantOutletCode;
    @Column(name = "transaction_type")
    private String transactionType;
    @Column(name = "data_range")
    private Integer dataRange;
    @Column(name = "transaction_status")
    private String transactionStatus;
    @Column(name = "voucher_status")
    private Integer voucherStatus;
    @Column(name = "vpg_code")
    private String vpgCode;
    @Column(name = "emails")
    private String emails;
    @Column(name = "email_subject")
    private String emailSubject;
    @Column(name = "ftp_address")
    private String ftpAddress;

    @Column(name = "ftp_username")
    private String ftpUsername;

    @Column(name = "ftp_password")
    private String ftpPassword;

    @Column(name = "login_method")
    private String loginMethod;
    @Column(name = "encryption_key")
    private String encryptionKey;
    @Column(name = "status")
    private Boolean status;
    @Column(name = "create_time")
    private Date createTime;
    @Column(name = "create_user")
    private String createUser;
    @Column(name = "update_time")
    private Date updateTime;
    @Column(name = "update_user")
    private String updateUser;
    @Column(name = "number_of_executions")
    private Integer numberOfExecutions;
    @Column(name = "execute_flag")
    private Integer executeFlag;


    public static final String C_STATUS = "status";
    public static final String C_EXECUTE_FLAG = "executeFlag";
    public static final String C_EXECUTION_TIME = "executionTime";

}
