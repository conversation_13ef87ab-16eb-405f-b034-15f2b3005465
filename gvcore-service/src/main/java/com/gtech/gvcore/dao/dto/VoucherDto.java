package com.gtech.gvcore.dao.dto;

import com.gtech.gvcore.dao.model.Voucher;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年3月9日
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class VoucherDto extends Voucher {

    /**
     * 
     */
    private static final long serialVersionUID = -7186569624145460795L;

    private Integer oldStatus;

    private Integer oldCirculationStatus;
    
    private String oldVoucherOwnerCode;
    
    private Long voucherCodeNumStart;

    private Long voucherCodeNumEnd;

	private String voucherCodeStart;

	private String voucherCodeEnd;

    private List<String> voucherCodeList;

    private List<VoucherCodeNumDto> voucherCodeNumDtoList;

    private List<BigDecimal> denominationList;

    private List<Long> voucherCodeNumList;

    private List<String> cpgCodeList;

    private List<String> issuerCodeList;

    private List<String> voucherOwnerCodeList;

    private Long startId;

    private Integer count;
    
    private Integer pageSize;

    private String voucherCode;

    private String tableName;
    private String salesOutlet;

    private Date salesTime;
}
