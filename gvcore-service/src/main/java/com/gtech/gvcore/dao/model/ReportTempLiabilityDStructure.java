package com.gtech.gvcore.dao.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName ReportTempLiabilityDStructure
 * @Description gv_report_temp_liability_d_structure
 * <p>
 *      DROP TABLE `gv_report_temp_liability_d_structure`;
 *      CREATE TABLE `gv_report_temp_liability_d_structure`
 *      (
 *          `id`                      INT PRIMARY KEY AUTO_INCREMENT NOT NULL COMMENT 'id auto increment',
 *          `issuer_code`             VARCHAR(64)                    NOT NULL COMMENT 'issuer code',
 *          `merchant_code`           VARCHAR(64)                    NULL COMMENT 'merchant code',
 *          `outlet_code`             VARCHAR(64)                    NULL COMMENT 'outlet code',
 *          `cpg_code`                VARCHAR(64)                    NOT NULL COMMENT 'cpg code',
 *          `voucher_status`          INT                            NOT NULL COMMENT 'voucher status',
 *          `denomination`            DECIMAL(13, 2)                 NOT NULL COMMENT 'Denomination.',
 *          `effective_date`          DATETIME                       NULL COMMENT 'Voucher effective date.',
 *          `voucher_code_page_index` INT                            NOT NULL COMMENT 'voucher code page index',
 *          `voucher_codes`           TEXT                  NOT NULL COMMENT 'voucher codes split(,)',
 *          UNIQUE INDEX `uidx_liability_d_all` (`issuer_code`, `merchant_code`, `outlet_code`, `cpg_code`, `voucher_status`, `denomination`, `effective_date`, `voucher_code_page_index`),
 *          INDEX `idx_liability_d_merchant_outlet` (`merchant_code`, `outlet_code`),
 *          INDEX `idx_liability_d_cpg` (`cpg_code`),
 *          INDEX `idx_liability_d_issuer` (`issuer_code`),
 *          INDEX `idx_liability_d_voucher_status` (`voucher_status`)
 *      ) COMMENT 'Gv Report Temp Liability Detail Structure Table.';
 * </p>
 * <AUTHOR>
 * @Date 2023/4/18 15:39
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
@Table(name = "gv_report_temp_liability_d_structure")
public class ReportTempLiabilityDStructure {

    @Id
    private Long id;

    @Column(name = "issuer_code")
    private String issuerCode;

    @Column(name = "merchant_code")
    private String merchantCode;

    @Column(name = "outlet_code")
    private String outletCode;

    @Column(name = "cpg_code")
    private String cpgCode;

    @Column(name = "voucher_status")
    private Integer voucherStatus;

    @Column(name = "denomination")
    private BigDecimal denomination;

    @Column(name = "effective_date")
    private Date effectiveDate;

    @Column(name = "voucher_code_page_index")
    private Integer voucherCodePageIndex;

    @Column(name = "voucher_codes")
    private String voucherCodes;

}
