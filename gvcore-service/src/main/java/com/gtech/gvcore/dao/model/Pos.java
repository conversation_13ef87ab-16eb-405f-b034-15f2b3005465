package com.gtech.gvcore.dao.model;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("Pos")
@Table(name = "gv_pos")
public class Pos implements Serializable {

    private static final long serialVersionUID = -38081699368068785L;

    @Id
    private Long id;

    /**
     * Pos code
     */
    @Column(name = "pos_code")
    private String posCode;

    /**
     * Issuer code
     */
    /*@Column(name = "issuer_code")
    private String issuerCode;*/

    /**
     * Pos name
     */
    @Column(name = "pos_name")
    private String posName;

    /**
     * Machine id
     */
    @Column(name = "machine_id")
    private String machineId;

    /**
     * POS Entry ModeId
     */
    @Column(name = "pos_entry_mode_id")
    private String posEntryModeId;

    /**
     * Outlet code
     */
    @Column(name = "outlet_code")
    private String outletCode;

    /**
     * Status
     */
    @Column(name = "`status`")
    private Integer status;

    /**
     * create user
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * create time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update user
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * update time
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "account")
    private String account;

    @Column(name = "password")
    private String password;

    public static final String CONST_POS_CODE = "posCode";
    public static final String CONST_ISSUER_CODE = "issuerCode";
    public static final String CONST_POS_NAME = "posName";
    public static final String CONST_MACHINE_ID = "machineId";
    public static final String CONST_POS_ENTRY_MODEID = "posEntryModeid";
    public static final String CONST_OUTLET_CODE = "outletCode";
    public static final String CONST_STATUS = "status";
    public static final String CONST_CREATE_USER = "createUser";
    public static final String CONST_CREATE_TIME = "createTime";
    public static final String CONST_UPDATE_USER = "updateUser";
    public static final String CONST_UPDATE_TIME = "updateTime";
    public static final String CONST_ACCOUNT = "account";
    public static final String CONST_PASSWORD = "password";//NOSONAR
}
