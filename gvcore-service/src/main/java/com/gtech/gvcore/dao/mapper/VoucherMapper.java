package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.enums.VoucherCirculationStatusEnum;
import com.gtech.gvcore.common.enums.VoucherStatusEnum;
import com.gtech.gvcore.common.request.voucher.UpdateVoucherStatusRequest;
import com.gtech.gvcore.common.response.distribution.CpgInventoryResponse;
import com.gtech.gvcore.common.response.distribution.CustomerCpgEffectiveDateResult;
import com.gtech.gvcore.common.response.voucher.VoucherResponse;
import com.gtech.gvcore.dao.dto.CpgVoucherCodeNumDto;
import com.gtech.gvcore.dao.dto.DenominationCountDto;
import com.gtech.gvcore.dao.dto.VoucherCodeNumDto;
import com.gtech.gvcore.dao.dto.VoucherDto;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.dto.CpgCodeCount;
import com.gtech.gvcore.dto.CustomerCpgGroup;
import com.gtech.gvcore.dto.TopTenDto;
import com.gtech.gvcore.service.report.impl.param.LatestGvStatusQueryData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.session.RowBounds;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2022/3/2 11:18
 */
@Mapper
public interface VoucherMapper extends GTechBaseMapper<Voucher> {

    @Select("<script> " +
            "SELECT " +

            " v.issuer_code, " +
            " v.voucher_batch_code, " +
            " v.booklet_code, " +
            " v.booklet_code_num, " +
            " v.voucher_code, " +
            " v.voucher_code_num, " +
            " v.cpg_code, " +
            " c.cpg_name, " +
            " v.denomination, " +
            " v.mop_code, " +
            " v.voucher_pin, " +
            " v.voucher_barcode, " +
            " v.voucher_effective_date, " +
            " v.`status`, " +
            " v.voucher_status, " +
            " v.circulation_status, " +
            " v.voucher_active_code, " +
            " v.voucher_active_url, " +
            " v.voucher_used_time, " +
            " v.voucher_owner_code, " +
            " v.voucher_owner_type, " +
            " v.permission_code, " +
            " v.create_time, " +
            " v.update_time, " +
            " v.create_user, " +
            " v.update_user  " +
            "FROM " +
            " gv_voucher v " +
            " LEFT JOIN gv_cpg c ON v.cpg_code = c.cpg_code " +
            " WHERE v.voucher_code In " +
            "<foreach collection=\"voucherCodes\" item=\"voucher\" index=\"index\" open=\"(\" close=\")\" separator=\",\">"+
            "       #{voucher} " +
            "      </foreach> " +
            " </script>")
    List<VoucherResponse> queryVoucherList(@Param("voucherCodes") List<String> voucherCodes);

    /**
     * 
     * <AUTHOR>
     * @param voucherDto
     * @return
     * @date 2022年3月9日
     */
    List<Voucher> queryByVoucherCodeList(VoucherDto voucherDto);


    List<Voucher> queryByVoucherCodeListByTableName(VoucherDto voucherDto);


    /**
     * 
     * @param dto
     * @return
     * <AUTHOR>
     * @date 2022年3月11日
     */
    List<DenominationCountDto> countGroupByDenomination(VoucherDto dto);

    /**
     * 
     * @param voucherCodeNumDtoList
     * @return
     * <AUTHOR>
     * @date 2022年3月21日
     */
    List<VoucherDto> countGroupByDenominationAndCirculationStatus(List<VoucherCodeNumDto> voucherCodeNumDtoList);
    
    /**
     * 
     * @param voucherCodeNumDtoList
     * @return
     * <AUTHOR>
     * @date 2022年3月23日
     */
    List<VoucherDto> countGroupByCustomerOrderAllocation(List<VoucherCodeNumDto> voucherCodeNumDtoList);
    
    /**
     * 
     * @param voucherDtoList
     * @param pageSize
     * @return
     * <AUTHOR>
     * @date 2022年4月1日
     */
    List<Voucher> queryMismatchByAllocationCountDto(@Param("voucherDtoList") List<VoucherDto> voucherDtoList, @Param("pageSize") Integer pageSize);

    /**
     * 
     * @param dto
     * @return
     * <AUTHOR>
     * @date 2022年3月11日
     */
    List<CpgVoucherCodeNumDto> groupByDenominationAndCpg(VoucherDto dto);

    /**
     * 
     * @param dto
     * @return
     * <AUTHOR>
     * @date 2022年3月11日
     */
    List<Voucher> queryByVoucherCodeNumList(VoucherDto dto);

    /**
     * 
     * @param voucherCodeNumLlist
     * @return
     * <AUTHOR>
     * @date 2022年3月15日
     */
    int allocateVoucher(@Param("voucherDto") VoucherDto voucherDto,
                        @Param("cpgVoucherCodeNum") CpgVoucherCodeNumDto cpgVoucherCodeNum);
    
    /**
     * 
     * @param voucherDto
     * @param voucherCodeNumLlist
     * @return
     * <AUTHOR>
     * @date 2022年4月8日
     */
    int activatPhysicalVoucherByCustomerOrder(@Param("voucherDto") VoucherDto voucherDto,
			@Param("voucherCodeNumLlist") List<CpgVoucherCodeNumDto> voucherCodeNumLlist);
    


    void cancelSales(String voucherOwnerCode, String voucherOwnerType, List<String> voucherCodeList, Integer voucherStatus, Integer circulationStatus, Integer status);
    Integer cancelSalesByStartAndEnd(String voucherOwnerCode, String voucherOwnerType, String startVoucherCode,String endVoucherCode, Integer voucherStatus, Integer circulationStatus, Integer status);

    void updateVoucherStatusByTableName(String voucherOwnerCode, String voucherOwnerType, List<String> voucherCodeList, Integer voucherStatus, Integer circulationStatus, Integer status,String tableName);

    @Select("<script>" +
            "SELECT " +
            " v.issuer_code, " +
            " v.voucher_batch_code, " +
            " v.booklet_code, " +
            " v.booklet_code_num, " +
            " v.voucher_code, " +
            " v.voucher_code_num, " +
            " v.cpg_code, " +
            " v.mop_code, " +
            " v.denomination, " +
            " v.voucher_pin, " +
            " v.voucher_barcode, " +
            " v.voucher_effective_date, " +
            " v.voucher_status, " +
            " v.STATUS, " +
            " v.circulation_status, " +
            " v.voucher_active_code, " +
            " v.voucher_active_url, " +
            " v.voucher_used_time, " +
            " v.voucher_owner_code, " +
            " v.voucher_owner_type, " +
            " v.permission_code, " +
            " v.create_time, " +
            " v.update_time, " +
            " v.create_user, " +
            " v.update_user   " +
            "FROM " +
            " gv_voucher v " +
            "WHERE " +
            " v.voucher_code &gt;=  #{start}  " +
            " AND v.voucher_code &lt;=  #{end} " +
            "</script>")
    List<Voucher> queryVoucherListByAllocateCode(@Param("start") String start, @Param("end") String end);


    /**
     * 
     * @param latestGvStatus
     * @return
     * <AUTHOR>
     * @date 2022年4月25日
     */
    List<Voucher> latestGvStatusReport(LatestGvStatusQueryData latestGvStatus);

    /**
     * 
     * @param dto
     * @return
     * <AUTHOR>
     * @date 2022年4月26日
     */
    List<Voucher> queryByVoucherCodeNumRange(@Param("voucherCode") String voucherCode,
                                             @Param("voucherCodeNumStart") Long voucherCodeNumStart,
                                             @Param("voucherCodeNumEnd") Long voucherCodeNumEnd,
                                             @Param("issuerCodeList") List<String> issuerCodeList);

    /**
     * 
     * @param voucherBatchCode
     * @return
     * <AUTHOR>
     * @date 2022年4月28日
     */
    List<CpgCodeCount> queryCpgCodeCountByVoucherBatchCode(String voucherBatchCode);

    /**
     * 
     * @param dto
     * @return
     * <AUTHOR>
     * @date 2022年4月28日
     */
    int receiveDigitalVoucherByCustomerOrder(@Param("dto")VoucherDto dto,@Param("index")Integer index);

    /**
     * 
     * @param dto
     * @return
     * <AUTHOR>
     * @date 2022年4月28日
     */
    int activatDigitalVoucherByCustomerOrder(VoucherDto dto);

    List<String> selectVoucherBatchCodesByVoucherNums(List<String> voucherNumVCE);

    @Select(" <script>" +
            " SELECT cpg_code" +
            " FROM gv_voucher" +
            " WHERE " +
            "   voucher_owner_code = #{customerCode}" +
            "   AND voucher_owner_type = 'customer'" +
            "    AND voucher_batch_code IN" +
            "    <foreach collection=\"batchCodeSet\" item=\"batchCode\" open=\"(\" separator=\",\" close=\")\">"+
            "        #{batchCode}"+
            "    </foreach> "+
            " GROUP BY cpg_code" +
            " </script>")
    List<String> queryOwnedCpgCodeList(@Param("customerCode") String customerCode, @Param("batchCodeSet") Set<String> batchCodeSet);

    @Select(" <script>" +
            " SELECT cpg_code,denomination" +
            " FROM gv_voucher" +
            " WHERE " +
            " voucher_owner_code = #{customerCode}" +
            "    AND voucher_batch_code IN" +
            "    <foreach collection=\"batchCodeSet\" item=\"batchCode\" open=\"(\" separator=\",\" close=\")\">"+
            "        #{batchCode}"+
            "    </foreach> "+
            " AND voucher_owner_type = 'customer'" +
            " <if test=\" null != cpgCode and cpgCode != '' \"> " +
            "   AND cpg_code = #{cpgCode}  " +
            " </if> " +
            " GROUP BY cpg_code" +
            " </script>")
    List<CpgInventoryResponse> queryOwnedCpgInvInfoList(@Param("cpgCode") String cpgCode, @Param("customerCode") String customerCode, @Param("batchCodeSet") Set<String> batchCodeSet);

    @Select(" <script>" +
            " SELECT COUNT(1) as `inventory`, `voucher_effective_date` " +
            " FROM `gv_voucher` " +
            " WHERE `voucher_owner_type` = 'customer' " +
            "   AND `voucher_owner_code` = #{customerCode} " +
            "   AND `cpg_code`           = #{cpgCode} " +
            "   AND voucher_code NOT IN " +
            "       (SELECT voucher_code FROM gv_distribution_item_voucher WHERE customer_code = #{customerCode} AND cpg_code = #{cpgCode} AND `status` = 'Distributed') " +
            "    AND voucher_batch_code IN" +
            "        <foreach collection=\"batchCodeSet\" item=\"batchCode\" open=\"(\" separator=\",\" close=\")\">"+
            "            #{batchCode}"+
            "        </foreach> "+
            /** {@link VoucherCirculationStatusEnum#VOUCHER_DISTRIBUTING} */
            /** {@link VoucherCirculationStatusEnum#VOUCHER_DISTRIBUTED} */
            "   AND circulation_status NOT IN (4, 5) " +
            " GROUP BY voucher_effective_date" +
            " </script>"
    )
    List<CustomerCpgEffectiveDateResult> queryCustomerCpg(@Param("customerCode") String customerCode, @Param("cpgCode") String cpgCode, @Param("batchCodeSet") Set<String> batchCodeSet);

    @Select(" <script>" +
            " SELECT voucher_owner_code,cpg_code FROM gv_voucher WHERE voucher_owner_type ='customer' GROUP BY  voucher_owner_code,cpg_code ORDER BY voucher_owner_code,cpg_code " +
            " </script>")
    List<CustomerCpgGroup> queryCustomerCpgGroupPage(RowBounds rowBounds);

    @Select("<script>" +
            "SELECT " +
            " v.voucher_owner_code, " +
            " c.customer_name name, " +
            " SUM( denomination ) amount  " +
            "FROM " +
            " gv_voucher v " +
            " LEFT JOIN gv_customer c ON c.customer_code = v.voucher_owner_code  " +
            "WHERE " +
            " v.voucher_owner_type = 'customer'  " +
            " <if test=\" null != vouchers and vouchers.size != 0\"> " +
            "    AND v.voucher_code IN" +
            "    <foreach collection=\"vouchers\" item=\"voucherCode\" open=\"(\" separator=\",\" close=\")\">"+
            "        #{voucherCode}"+
            "    </foreach> "+
            " </if> " +
            " and c.customer_type = 'Corporate' " +
            "GROUP BY " +
            " voucher_owner_code  " +
            " order by SUM(denomination) DESC " +
            " LIMIT 10" +
            "</script>")
    List<TopTenDto>topTen(@Param("vouchers")List<String> voucherCodes);

    /**
     * used ==0
     * sales ==1
     * @param record
     * @param example
     * @param type
     * @return
     */
    int setUsedOrSalesNull(@Param("record") Voucher record, @Param("example") Example example,@Param("type") Integer type);

    int updateVoucherStatusByTable(@Param("request") UpdateVoucherStatusRequest request,@Param("index") Integer index);

    @Select("SELECT voucher_code FROM gv_voucher " +
            "WHERE voucher_code >= #{startNo} " +
            "AND voucher_code <= #{endNo} " +
            "AND circulation_status <> #{circulationStatus} ")
    List<String> findIssuedVoucherInRange(@Param("startNo") String startNo, 
                                         @Param("endNo") String endNo,@Param("circulationStatus")Integer circulationStatus);

    @Select("<script>" +
            "SELECT t.* FROM gv_transaction_data t " +
            "WHERE t.voucher_code IN " +
            "<foreach collection='voucherCodes' item='code' open='(' separator=',' close=')'>" +
            "#{code}" +
            "</foreach>" +
            " AND t.transaction_type = '1'" +
            "<if test='transactionId != null'>" +
            " AND t.transaction_id = #{transactionId}" +
            "</if>" +
            "<if test='invoiceNumber != null and invoiceNumber.trim().length() != 0'>" +
            " AND t.invoice_number = #{invoiceNumber}" +
            "</if>" +
            "<if test='batchNumber != null'>" +
            " AND t.batch_id = #{batchNumber}" +
            "</if> " +
            " AND t.success_or_failure = '0' " +
            " order by create_time desc " +
            "</script>")
    List<TransactionData> queryRedeemTransactions(
        @Param("voucherCodes") List<String> voucherCodes,
        @Param("transactionId") Long transactionId,
        @Param("invoiceNumber") String invoiceNumber,
        @Param("batchNumber") Long batchNumber
    );

}
