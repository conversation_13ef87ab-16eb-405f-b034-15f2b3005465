package com.gtech.gvcore.dao.model;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "gv_customer")
@ApiModel(value = "Customer)", description = "customer")
public class Customer implements Serializable {
    private static final long serialVersionUID = -47007122467427854L;


    public static final String C_CUSTOMER_CODE = "customerCode";
    public static final String C_ISSUER_CODE = "issuerCode";
    public static final String C_CUSTOMER_NAME = "customerName";
    public static final String C_CUSTOMER_TYPE = "customerType";
    public static final String C_COMPANY_NAME = "companyName";
    public static final String C_CONTACT_FIRST_NAME = "contactFirstName";
    public static final String C_CONTACT_LAST_NAME = "contactLastName";
    public static final String C_CONTACT_DIVISION = "contactDivision";
    public static final String C_CONTACT_PHONE = "contactPhone";
    public static final String C_CONTACT_EMAIL = "contactEmail";
    public static final String C_SHIPPING_ADDRESS1 = "shippingAddress1";
    public static final String C_SHIPPING_ADDRESS2 = "shippingAddress2";
    public static final String C_TRANSFER_ACCOUNT = "transferAccount";
    public static final String C_BANK_CARD_ISSUER = "bankCardIssuer";
    public static final String C_NOTE = "note";
    public static final String C_STATUS = "status";
    public static final String C_CREATE_USER = "createUser";
    public static final String C_CREATE_TIME = "createTime";
    public static final String C_UPDATE_USER = "updateUser";
    public static final String C_UPDATE_TIME = "updateTime";
    public static final String C_CHANNEL = "channel";
    public static final String C_OUTLET_CODE = "outletCode";
    public static final String C_REGISTRATION_YEAR = "registrationYear";



    @Id
    private Long id;

    @Column(name = "customer_code")
    private String customerCode;

    @Column(name = "issuer_code")
    private String issuerCode;

    @Column(name = "outlet_code")
    private String outletCode;

    @Column(name = "customer_name")
    private String customerName;

    @Column(name = "customer_type")
    private String customerType;

    @Column(name = "company_name")
    private String companyName;

    @Column(name = "contact_first_name")
    private String contactFirstName;

    @Column(name = "contact_last_name")
    private String contactLastName;

    @Column(name = "contact_division")
    private String contactDivision;

    @Column(name = "contact_phone")
    private String contactPhone;

    @Column(name = "contact_email")
    private String contactEmail;

    @Column(name = "shipping_address1")
    private String shippingAddress1;

    @Column(name = "shipping_address2")
    private String shippingAddress2;

    @Column(name = "transfer_account")
    private String transferAccount;

    @Column(name = "bank_card_issuer")
    private String bankCardIssuer;

    @Column(name = "note")
    private String note;

    @Column(name = "`status`")
    private Integer status;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "distribution_function")
    private String distributionFunction;

    @Column(name = "user_email")
    private String userEmail;

    @Column(name = "channel")
    private String channel;

    @Column(name = "registration_year")
    private String registrationYear;

    @Column(name = "beneficiary_name")
    private String beneficiaryName;

    @Column(name = "branch_name")
    private String branchName;

    @Column(name = "bank_name")
    private String bankName;

    @Column(name = "account_number")
    private String accountNumber;

    //0 false 1 true
    @Column(name = "pph")
    private Integer pph;


}
