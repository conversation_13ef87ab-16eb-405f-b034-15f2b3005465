package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.common.request.customer.GetCustomerRequest;
import com.gtech.gvcore.common.request.customer.QueryCustomerCompanyNameRequest;
import com.gtech.gvcore.common.request.customer.QueryCustomerRequest;
import com.gtech.gvcore.common.response.customer.CustomerResponse;
import com.gtech.gvcore.dao.model.Customer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/17 13:36
 */
@Mapper
public interface CustomerMapper extends GTechBaseMapper<Customer> {

    @Select("<script> " +
            " SELECT " +
            " c.customer_code, " +
            " c.issuer_code, " +
            " c.outlet_code, " +
            " c.customer_name, " +
            " c.customer_type, " +
            " c.company_name, " +
            " c.contact_first_name, " +
            " c.contact_last_name, " +
            " c.contact_division, " +
            " c.contact_phone, " +
            " c.contact_email, " +
            " c.shipping_address1, " +
            " c.shipping_address2, " +
            " c.transfer_account, " +
            " c.bank_card_issuer, " +
            " c.note, " +
            " c.`status`, " +
            " group_concat(cpm.mop_group) mopGroup, " +

            " c.create_user, " +
            " c.create_time, " +
            " c.update_user, " +
            " c.update_time, " +
            " c.distribution_function, " +
            " c.user_email  " +
            " FROM" +
            " gv_customer c " +
            " LEFT JOIN gv_customer_payment_method cpm ON c.customer_code = cpm.customer_code " +
            " <where>" +

            " <if test=\" request.customerCode != null and request.customerCode != '' \"> " +
            " AND c.customer_code = #{request.customerCode} " +
            " </if> " +

            " <if test=\" request.name != null and request.name != '' \"> " +
            " AND (( c.customer_type = 'individual' and c.customer_name LIKE CONCAT('%', #{request.name}, '%')) or " +
            "   c.company_name LIKE CONCAT('%', #{request.name}, '%') ) " +

            " </if> " +
            " <if test=\" request.contactDivision != null and request.contactDivision != '' \"> " +
            " AND c.contact_division LIKE CONCAT('%', #{request.contactDivision}, '%')" +
            " </if> " +
            " <if test=\" request.issuerCode != null and request.issuerCode != '' \"> " +
            " AND c.issuer_code = #{request.issuerCode} " +
            " </if> " +
            " <if test=\" request.customerName != null and request.customerName != '' \"> " +
            " AND c.customer_name like concat('%',#{request.customerName},'%') " +
            " </if> " +
            " <if test=\" request.customerType != null and request.customerType != '' \"> " +
            " AND c.customer_type = #{request.customerType} " +
            " </if> " +
            " <if test=\" request.status != null and request.status != '' \"> " +
            " AND c.status = #{request.status} " +
            " </if> " +
            " <if test=\" request.distributionFunction != null and request.distributionFunction != '' \"> " +
            " AND c.distribution_function = #{request.distributionFunction} " +
            " </if> " +
            " <if test=\" request.userEmail != null and request.userEmail != '' \"> " +
            " AND c.user_email like concat('%',#{request.userEmail},'%') " +
            " </if> " +

            " </where>" +
            " GROUP BY c.customer_code " +
            " ORDER BY " +
            " c.customer_name  " +
            " </script>")
    List<CustomerResponse> queryCustomerList(@Param("request") QueryCustomerRequest request);




    @Select("<script> " +
            " SELECT " +
            " c.customer_code, " +
            " c.issuer_code, " +
            " c.outlet_code, " +
            " c.customer_name, " +
            " c.customer_type, " +
            " c.company_name, " +
            " c.contact_first_name, " +
            " c.contact_last_name, " +
            " c.contact_division, " +
            " c.contact_phone, " +
            " c.contact_email, " +
            " c.shipping_address1, " +
            " c.shipping_address2, " +
            " c.transfer_account, " +
            " c.bank_card_issuer, " +
            " c.note, " +
            " c.`status`, " +
            " group_concat(cpm.mop_group) mopGroup, " +
            " c.create_user, " +
            " c.create_time, " +
            " c.update_user, " +
            " c.update_time, " +
            " c.distribution_function, " +
            " c.user_email,  " +
            " c.channel,  " +
            " c.registration_year,  " +
            " c.beneficiary_name,  " +
            " c.branch_name,       " +
            " c.bank_name,         " +
            " c.account_number,    " +
            " c.pph               " +
            " FROM" +
            " gv_customer c " +
            " LEFT JOIN gv_customer_payment_method cpm ON c.customer_code = cpm.customer_code " +
            " where c.customer_code = #{request.customerCode} " +
            " GROUP BY c.customer_code " +
            " </script>")
    CustomerResponse getCustomer(@Param("request") GetCustomerRequest request);


    @Select(
            value = "select count(1) from gv_customer where contact_email= #{email} and issuer_code = #{issuerCode}"
    )
    Boolean selectEmailExists(@Param("email") String email,@Param("issuerCode")String issuerCode);

    @Select("" +
            "SELECT DISTINCT " +
            " company_name  " +
            "FROM " +
            " gv_customer  " +
            "WHERE " +
            " company_name <> ''  " +
            " AND issuer_code = #{request.issuerCode} " +
            "")
    List<String> queryCustomerCompanyName(@Param("request")QueryCustomerCompanyNameRequest request);
}
