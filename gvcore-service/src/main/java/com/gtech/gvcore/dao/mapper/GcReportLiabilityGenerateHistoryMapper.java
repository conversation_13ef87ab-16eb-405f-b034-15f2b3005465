package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.model.GcReportLiabilityGenerateHistory;
import com.gtech.gvcore.service.report.impl.support.liability.model.GcLiabilityVoucherMode;
import org.apache.ibatis.annotations.Param;
import org.mapstruct.Mapper;

import java.util.Date;
import java.util.List;

/**
 * @ClassName ReportLiabilityGenerateHistoryMapper
 * @Description
 * <AUTHOR>
 * @Date 2023/4/15 17:01
 * @Version V1.0
 **/
@Mapper
public interface GcReportLiabilityGenerateHistoryMapper extends GTechBaseMapper<GcReportLiabilityGenerateHistory> {

    int createLiabilityTable(@Param("tableCode") String tableCode, @Param("tableType") String tableType);

    int truncateLiabilityTable(@Param("tableCode") String tableCode, @Param("tableType") String tableType);

    void clearTable(@Param("tableCode") String tableCode, @Param("tableType") String tableType);

    List<GcLiabilityVoucherMode> selectVoucher(@Param("createTime") Date createTime, @Param("lastId") int lastId, @Param("pageSize") int pageSize);

    Integer existTable(@Param("tableName") String tableName);

}
