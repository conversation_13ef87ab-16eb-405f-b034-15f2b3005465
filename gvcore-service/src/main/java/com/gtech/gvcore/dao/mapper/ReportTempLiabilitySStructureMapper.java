package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.model.ReportTempLiabilitySStructure;
import org.apache.ibatis.annotations.Param;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @ClassName ReportTempLiabilitySStructureMapper
 * @Description ReportTempLiabilitySStructure
 * <AUTHOR>
 * @Date 2023/4/18 15:34
 * @Version V1.0
 **/
@Mapper
public interface ReportTempLiabilitySStructureMapper extends GTechBaseMapper<ReportTempLiabilitySStructure> {

    void insertBatch(@Param("tableCode") String tableCode,
                     @Param("summaryList") List<ReportTempLiabilitySStructure> summaryList);
}
