package com.gtech.gvcore.dao.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

@Data
@Table(name = "gv_means_of_payment_outlet")
public class MeansOfPaymentOutlet {
    /**
     * id
     */
    @Id
    private Long id;

    /**
     * data UNIQUE KEY
     */
    @Column(name = "means_of_payment_outlet_code")
    private String meansOfPaymentOutletCode;

    /**
     * means of payment code
     */
    @Column(name = "means_of_payment_code")
    private String meansOfPaymentCode;

    /**
     * outlet code
     */
    @Column(name = "outlet_code")
    private String outletCode;

    /**
     * delete status,0:disable,1:enable
     */
    @Column(name = "delete_status")
    private Integer deleteStatus;

    /**
     * create user
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * create time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update user
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * update time
     */
    @Column(name = "update_time")
    private Date updateTime;

}