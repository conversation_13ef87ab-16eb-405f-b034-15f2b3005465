package com.gtech.gvcore.dao.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

@Data
@Table(name = "gv_cpg_printer")
public class CpgPrinter {
    /**
     * id
     */
    @Id
    private Long id;

    /**
     * cpg code
     */
    @Column(name = "cpg_code")
    private String cpgCode;

    /**
     * printer code
     */
    @Column(name = "printer_code")
    private String printerCode;

    /**
     * delete status,0:disable,1:enable
     */
    @Column(name = "delete_status")
    private Integer deleteStatus;

    /**
     * create user
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * create time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update user
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * update time
     */
    @Column(name = "update_time")
    private Date updateTime;

}