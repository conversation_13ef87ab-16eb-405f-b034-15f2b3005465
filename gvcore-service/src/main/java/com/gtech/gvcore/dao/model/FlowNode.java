package com.gtech.gvcore.dao.model;

import java.util.Date;
import javax.persistence.*;

import lombok.Data;

@Table(name = "gv_flow_node")
@Data
public class FlowNode {
	
	
	public static final String C_FLOW_CODE = "flowCode";
    /**
     * id
     */
    @Id
    private Long id;

    /**
     * flow code
     */
    @Column(name = "flow_code")
    private String flowCode;

    /**
     * flow node code
     */
    @Column(name = "flow_node_code")
    private String flowNodeCode;

    /**
     * flow node name
     */
    @Column(name = "flow_node_name")
    private String flowNodeName;

    /**
     * remark
     */
    private String remark;

    /**
     * status
     */
    private Integer status;

    /**
     * create time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update time
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * create user
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * update user
     */
    @Column(name = "update_user")
    private String updateUser;
}