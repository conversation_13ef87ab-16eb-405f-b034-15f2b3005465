package com.gtech.gvcore.dao.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "gv_release_approve_node")
public class ReleaseApproveNode implements Serializable {

    private static final long serialVersionUID = 1598869524814023817L;

    public static final String C_RELEASE_APPROVE_AMOUNT_CODE = "releaseApproveAmountCode";

    @Id
    private Long id;
    @Column(name = "release_approve_node_code")
    private String releaseApproveNodeCode;
    @Column(name = "release_approve_amount_code")
    private String releaseApproveAmountCode;
    @Column(name = "node_name")
    private Integer nodeName;
    @Column(name = "role_code")
    private String roleCode;
    @Column(name = "create_time")
    private Date createTime;
    @Column(name = "create_user")
    private String createUser;
    @Column(name = "update_time")
    private Date updateTime;
    @Column(name = "update_user")
    private String updateUser;
    @Column(name = "issuer_code")
    private String issuerCode;

}
