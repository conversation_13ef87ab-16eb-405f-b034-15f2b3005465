package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.model.ReportLiabilityGenerateHistory;
import com.gtech.gvcore.service.report.impl.support.liability.model.LiabilityTransactionModel;
import com.gtech.gvcore.service.report.impl.support.liability.model.LiabilityVoucherMode;
import org.apache.ibatis.annotations.Param;
import org.mapstruct.Mapper;

import java.util.Date;
import java.util.List;

/**
 * @ClassName ReportLiabilityGenerateHistoryMapper
 * @Description
 * <AUTHOR>
 * @Date 2023/4/15 17:01
 * @Version V1.0
 **/
@Mapper
public interface ReportLiabilityGenerateHistoryMapper extends GTechBaseMapper<ReportLiabilityGenerateHistory> {

    int createLiabilityTable(@Param("tableCode") String tableCode, @Param("tableType") String tableType);

    int truncateLiabilityTable(@Param("tableCode") String tableCode, @Param("tableType") String tableType);

    void clearTable(@Param("tableCode") String tableCode, @Param("tableType") String tableType);

    List<LiabilityVoucherMode> selectVoucher(@Param("createTime") Date createTime, @Param("tableIndex") int tableIndex, @Param("lastId") int lastId, @Param("pageSize") int pageSize);
    List<LiabilityVoucherMode> selectVoucherBySalesTime(@Param("createTime") Date createTime, @Param("tableIndex") int tableIndex, @Param("lastId") int lastId, @Param("pageSize") int pageSize);

    List<LiabilityTransactionModel> selectTransaction(@Param("voucherCodeList") List<String> voucherCodeList, @Param("tableIndex") int tableIndex);

    Integer existTable(@Param("tableName") String tableName);

}
