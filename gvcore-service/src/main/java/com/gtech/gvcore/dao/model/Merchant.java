package com.gtech.gvcore.dao.model;

import io.swagger.annotations.ApiModel;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "gv_merchant")
@ApiModel(value = "Merchant)", description = "merchant")
public class Merchant implements Serializable {
    private static final long serialVersionUID = 616355973428071492L;
    /**
     * id
     */

    public static final String C_MERCHANT_CODE = "merchantCode";
    public static final String C_MERCHANT_NAME = "merchantName";
    public static final String C_COMPANY_CODE = "companyCode";
    /*public static final String C_ISSUER_CODE = "issuerCode";*/
    public static final String C_STATUS = "status";
    public static final String C_REMARKS = "remarks";
    public static final String C_CREATE_USER = "createUser";
    public static final String C_CREATE_TIME = "createTime";
    public static final String C_UPDATE_USER = "updateUser";
    public static final String C_UPDATE_TIME = "updateTime";

    @Id
    private Long id;

    @Column(name = "merchant_code")
    private String merchantCode;

    @Column(name = "merchant_name")
    private String merchantName;

    @Column(name = "company_code")
    private String companyCode;

    /*@Column(name = "issuer_code")
    private String issuerCode;*/

    @Column(name = "`status`")
    private Integer status;

    @Column(name = "remarks")
    private String remarks;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_time")
    private Date updateTime;



}
