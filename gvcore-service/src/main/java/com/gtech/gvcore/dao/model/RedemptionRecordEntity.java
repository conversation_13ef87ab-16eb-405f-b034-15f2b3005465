package com.gtech.gvcore.dao.model;

import com.gtech.commons.dao.entity.GTechBaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RedemptionRecordEntity", description = "礼品卡兑换记录")
public class RedemptionRecordEntity extends GTechBaseEntity {
    
    private String cardNumber;
    private BigDecimal amount;
    private String merchantId;
    private String transactionId;
    private LocalDateTime redeemedAt;
    private Integer status;
    private String failureReason;
    private String channel;
    private String remark;
} 