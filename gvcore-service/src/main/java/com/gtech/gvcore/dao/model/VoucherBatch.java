
package com.gtech.gvcore.dao.model;


import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "gv_voucher_batch")
@ApiModel(value = "VoucherBatch)", description = "Voucher batch.")
public class VoucherBatch implements Serializable {
    private static final long serialVersionUID = 153730690255052754L;

    public static final String C_PURCHASE_ORDER_NO = "purchaseOrderNo";
    public static final String C_ISSUER_CODE = "issuerCode";
    public static final String C_CPG_CODE = "cpgCode";
    public static final String C_VOUCHER_BATCH_CODE = "voucherBatchCode";
    public static final String C_ARTICLE_CODE = "articleCode";
    public static final String C_MOP_CODE = "mopCode";
    public static final String C_PRINTER_CODE = "printerCode";
    public static final String C_BOOKLET_START_NO = "bookletStartNo";
    public static final String C_BOOKLET_END_NO = "bookletEndNo";
    public static final String C_BOOKLET_PER_NUM = "bookletPerNum";
    public static final String C_BOOKLET_NUM = "bookletNum";
    public static final String C_VOUCHER_START_NO = "voucherStartNo";
    public static final String C_VOUCHER_END_NO = "voucherEndNo";
    public static final String C_VOUCHER_NUM = "voucherNum";
    public static final String C_DENOMINATION = "denomination";
    public static final String C_VOUCHER_EFFECTIVE_DATE = "voucherEffectiveDate";
    public static final String C_FILE_NAME = "fileName";
    public static final String C_FILE_FORMAT = "fileFormat";
    public static final String C_VOUCHER_NUM_ACTIVE = "voucherNumActive";
    public static final String C_VOUCHER_NUM_USED = "voucherNumUsed";
    public static final String C_STATUS = "status";
    public static final String C_PERMISSION_CODE = "permissionCode";
    public static final String C_CREATE_TIME = "createTime";
    public static final String C_UPDATE_TIME = "updateTime";
    public static final String C_CREATE_USER = "createUser";
    public static final String C_UPDATE_USER = "updateUser";
    
    @Id
    private Long id;

    @Column(name = "purchase_order_no")
    private String purchaseOrderNo;
    
    @Column(name = "issuer_code")
    private String issuerCode;
    
    @Column(name = "cpg_code")
    private String cpgCode;
    
    @Column(name = "voucher_batch_code")
    private String voucherBatchCode;
    
    @Column(name = "article_code")
    private String articleCode;
    
    @Column(name = "mop_code")
    private String mopCode;
    
    @Column(name = "printer_code")
    private String printerCode;
    
    @Column(name = "booklet_start_no")
    private String bookletStartNo;
    
    @Column(name = "booklet_end_no")
    private String bookletEndNo;
    
    @Column(name = "booklet_per_num")
    private Integer bookletPerNum;
    
    @Column(name = "booklet_num")
    private Integer bookletNum;
    
    @Column(name = "voucher_start_no")
    private String voucherStartNo;
    
    @Column(name = "voucher_end_no")
    private String voucherEndNo;
    
    @Column(name = "voucher_num")
    private Integer voucherNum;
    
    @Column(name = "denomination")
    private BigDecimal denomination;
    
    @Column(name = "voucher_effective_date")
    private Date voucherEffectiveDate;
    
    @Column(name = "file_name")
    private String fileName;
    
    @Column(name = "file_format")
    private String fileFormat;
    
    @Column(name = "voucher_num_active")
    private Integer voucherNumActive;
    
    @Column(name = "voucher_num_used")
    private Integer voucherNumUsed;
    
    @Column(name = "`status`")
    private Integer status;
    
    @Column(name = "permission_code")
    private String permissionCode;
    
    @Column(name = "create_time")
    private Date createTime;
    
    @Column(name = "update_time")
    private Date updateTime;
    
    @Column(name = "create_user")
    private String createUser;
    
    @Column(name = "update_user")
    private String updateUser;



}
