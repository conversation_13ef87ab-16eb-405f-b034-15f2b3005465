package com.gtech.gvcore.dao.model;


import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "gv_dashboard_detail")
@ApiModel(value = "GvDashboardDetail)", description = "")
public class GvDashboardDetail implements Serializable {
    private static final long serialVersionUID = 330664833100447847L;
    /**
    * id
    */
    
    @Id
    private Integer id;

    @Column(name = "dashboard_detail_code")
    private String dashboardDetailCode;
    
    @Column(name = "dashboard_code")
    private String dashboardCode;
    
    @Column(name = "outlet_type")
    private String outletType;
    
    @Column(name = "outlet_code")
    private String outletCode;
    
    @Column(name = "issuer_code")
    private String issuerCode;
    
    @Column(name = "amount")
    private BigDecimal amount;
    
    @Column(name = "voucher_count")
    private Integer voucherCount;
    

    @Column(name = "`datetime`")
    private Date datetime;
    
    @Column(name = "dashboard_type")
    private String dashboardType;
    
    @Column(name = "create_time")
    private Date createTime;
    
    @Column(name = "create_user")
    private String createUser;
    
    @Column(name = "update_time")
    private Date updateTime;
    
    @Column(name = "update_user")
    private String updateUser;
    

}
