package com.gtech.gvcore.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.common.response.cpg.QueryCpgByOutletCodeResponse;
import com.gtech.gvcore.dao.dto.CpgDto;
import com.gtech.gvcore.dao.model.Cpg;

@Mapper
public interface CpgMapper extends GTechBaseMapper<Cpg> {

    /**
     * @param dto
     * @return
     * <AUTHOR>
     * @date 2022年2月23日
     */
    List<Cpg> selectSelective(CpgDto dto);

    /**
     * @param cpgCodeList
     * @return
     * <AUTHOR>
     * @date 2022年2月24日
     */
    List<Cpg> queryByCpgCodeList(List<String> cpgCodeList);

    /**
     * @param outletCode
     * @return
     * <AUTHOR>
     */
    List<QueryCpgByOutletCodeResponse> selectDenomination(String outletCode);

    /**
     * 
     * @param cpgCodeList
     * @param automaticActivate
     * @param issuerCode
     * @return
     * <AUTHOR>
     * @date 2022年5月9日
     */
    List<String> queryAutomaticActivateCpg(@Param("cpgCodeList") List<String> cpgCodeList,
            @Param("automaticActivate") String automaticActivate, @Param("issuerCode") String issuerCode);

}