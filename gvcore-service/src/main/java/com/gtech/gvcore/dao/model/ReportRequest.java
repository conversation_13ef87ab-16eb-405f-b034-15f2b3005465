package com.gtech.gvcore.dao.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * @ClassName Report
 * @Description
 * <AUTHOR>
 * @Date 2023/1/20 15:33
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
@Table(name = "gv_report_request")
public class ReportRequest {

    @Id
    private Long id;

    @Column(name = "report_code")
    private String reportCode;

    @Column(name = "report_type")
    private Integer reportType;
    @Column(name = "report_status")
    private Integer reportStatus;

    @Column(name = "request_user")
    private String requestUser;
    @Column(name = "request_time")
    private Date requestTime;
    @Column(name = "request_json")
    private String requestJson;

    @Column(name = "scheduler_report_code")
    private String schedulerReportCode;

    @Column(name = "page_size")
    private Integer pageSize;
    @Column(name = "total_json")
    private String totalJson;
    @Column(name = "head_html")
    private String headHtml;

    @Column(name = "receive_email")
    private String receiveEmail;

    @Column(name = "create_user")
    private String createUser;
    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "error_message")
    private String errorMessage;

    @Column(name = "allot_time")
    private Date allotTime;

    @Column(name = "execute_time_ms")
    private String executeTimeMs;

    @Column(name = "application_no")
    private String applicationNo;

    @Column(name = "update_user")
    private String updateUser;
    @Column(name = "update_time")
    private Date updateTime;

    public static final String C_ID = "id";
    public static final String C_REPORT_CODE = "reportCode";
    public static final String C_REPORT_TYPE = "reportType";
    public static final String C_REPORT_STATUS = "reportStatus";
    public static final String C_REQUEST_USER = "requestUser";
    public static final String C_REQUEST_TIME = "requestTime";
    public static final String C_REQUEST_JSON = "requestJson";
    public static final String C_SCHEDULER_REPORT_CODE = "schedulerReportCode";
    public static final String C_PAGE_SIZE = "pageSize";
    public static final String C_TOTAL_JSON = "totalJson";
    public static final String C_RECEIVE_EMAIL = "receiveEmail";
    public static final String C_CREATE_USER = "createUser";
    public static final String C_CREATE_TIME = "createTime";
    public static final String C_ERROR_MESSAGE = "errorMessage";
    public static final String C_ALLOT_TIME = "allotTime";
    public static final String C_EXECUTE_TIME_MS = "executeTimeMs";
    public static final String C_APPLICATION_NO = "applicationNo";
    public static final String C_UPDATE_USER = "updateUser";
    public static final String C_UPDATE_TIME = "updateTime";

}
