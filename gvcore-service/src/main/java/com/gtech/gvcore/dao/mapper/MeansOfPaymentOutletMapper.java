package com.gtech.gvcore.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.model.MeansOfPaymentOutlet;

@Mapper
public interface MeansOfPaymentOutletMapper extends GTechBaseMapper<MeansOfPaymentOutlet> {

    /**
     * 
     * @param meansOfPaymentCodeList
     * @return
     * <AUTHOR>
     * @date 2022年4月22日
     */
    List<MeansOfPaymentOutlet> queryByMeansOfPaymentCodeList(
            @Param("meansOfPaymentCodeList") List<String> meansOfPaymentCodeList,
            @Param("deleteStatus") Integer deleteStatus);

}