package com.gtech.gvcore.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import com.gtech.gvcore.monitor.JvmObjectMonitor;

/**
 * 监控配置类
 * 负责监控系统的配置和定时任务
 */
@Slf4j
@Component
public class MonitoringConfig {

    @Value("${gv.monitor.stats.log.enabled:true}")
    private boolean statsLogEnabled;

    /**
     * 应用启动完成后记录内存使用情况
     */
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        log.info("=== 应用启动完成 - 内存监控信息 ===");
        String memoryInfo = JvmObjectMonitor.getSimpleObjectStatistics();
        log.info("启动后内存使用: {}", memoryInfo);
        log.info("定时监控任务已就绪");
    }

    /**
     * 定时记录全局监控统计（每30分钟）
     */
    @Scheduled(fixedRate = 1800000) // 30分钟
    public void logGlobalStats() {
        if (!statsLogEnabled) {
            return;
        }

        try {
            String memoryInfo = JvmObjectMonitor.getSimpleObjectStatistics();
            log.info("=== 定时内存快照 ===");
            log.info("当前内存使用: {}", memoryInfo);
            log.info("==================");
        } catch (Exception e) {
            log.error("记录定时内存快照失败", e);
        }
    }
}
