package com.gtech.gvcore.external;

import com.alibaba.druid.util.StringUtils;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.enums.*;
import com.gtech.gvcore.common.externalmapping.request.BulkCancelRedeemRequest;
import com.gtech.gvcore.common.request.activateonly.ActivateOnlyParam;
import com.gtech.gvcore.common.request.authorize.AuthorizePayload;
import com.gtech.gvcore.common.request.authorize.AuthorizeRequest;
import com.gtech.gvcore.common.request.cancelactivate.CancelActivateParam;
import com.gtech.gvcore.common.request.cancelredeem.CancelRedeemRequest;
import com.gtech.gvcore.common.request.cpg.GetCpgRequest;
import com.gtech.gvcore.common.request.cpg.GetCpgTypeRequest;
import com.gtech.gvcore.common.request.createandissue.CreateAndIssueParam;
import com.gtech.gvcore.common.request.outlet.GetOutletRequest;
import com.gtech.gvcore.common.request.pos.GetPosRequest;
import com.gtech.gvcore.common.request.pos.QueryOutletByPosIdRequest;
import com.gtech.gvcore.common.request.transaction.*;
import com.gtech.gvcore.common.request.v3.TransactionsRequest;
import com.gtech.gvcore.common.request.voucher.GetVoucherInformationRequest;
import com.gtech.gvcore.common.request.voucher.VerifyVoucherInfo;
import com.gtech.gvcore.common.request.voucher.VerifyVoucherRequest;
import com.gtech.gvcore.common.request.voucherbatch.GetVoucherBatchRequest;
import com.gtech.gvcore.common.response.activateonly.ActivateOnlyGinseng;
import com.gtech.gvcore.common.response.authorize.AuthorizeResponse;
import com.gtech.gvcore.common.response.authorize.AuthorizeResponseV3;
import com.gtech.gvcore.common.response.authorize.LocaleInfo;
import com.gtech.gvcore.common.response.authorize.MerchantOutletInfo;
import com.gtech.gvcore.common.response.authorize.ReceiptInfo;
import com.gtech.gvcore.common.response.cancelactivate.CancelActivateGinseng;
import com.gtech.gvcore.common.response.cancelcreateandissue.CancelCreateAndIssueGinseng;
import com.gtech.gvcore.common.response.cancelredeem.APIBulkCancelRedeemResponse;
import com.gtech.gvcore.common.response.cancelredeem.CancelRedeemResponse;
import com.gtech.gvcore.common.response.cpg.GetCpgResponse;
import com.gtech.gvcore.common.response.cpg.GetCpgTypeResponse;
import com.gtech.gvcore.common.response.createandissue.CreateAndIssueGinseng;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.common.response.pos.PosResponse;
import com.gtech.gvcore.common.response.transaction.*;
import com.gtech.gvcore.common.response.v3.CreateAndIssueResponse;
import com.gtech.gvcore.common.response.voucher.GetVoucherInformationResponse;
import com.gtech.gvcore.common.response.voucher.VerifyResponse;
import com.gtech.gvcore.common.response.voucher.VerifyVoucherGinseng;
import com.gtech.gvcore.common.response.voucher.VoucherInfo;
import com.gtech.gvcore.common.response.voucherbatch.VoucherBatchResponse;
import com.gtech.gvcore.dao.model.*;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.PosService;
import com.gtech.gvcore.service.TransactionDataService;
import com.gtech.gvcore.service.VoucherService;
import com.gtech.gvcore.pool.DynamicObjectPool;
import com.gtech.gvcore.pool.ObjectPoolManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import javax.annotation.PreDestroy;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GvPosService {

	@Autowired
	JwtService jwtService;
	@Autowired
	PosService posService;

	@Autowired
	TransactionDataService transactionDataService;
	@Value("${gvcore.jwt.effective.hours:1440}")
	private Integer effectiveHours;

	@Lazy
	@Autowired
	VoucherService voucherService;

	@Autowired
	private GvCodeHelper codeHelper;

	@Autowired
	private RedisTemplate redisTemplate;

	@Autowired
	private ObjectPoolManager objectPoolManager;

	private DynamicObjectPool<TransactionResponse> getTransactionResponsePool() {
		return objectPoolManager.getPool(
			TransactionResponse.class,
			TransactionResponse::new,
			response -> {
				response.setResponseCode(null);
				response.setResponseMessage(null);
				response.setTransactionId(null);
				response.setTransactionTypeId(null);
				response.setActionType(null);
				response.setSourceId(null);
				response.setMessageId(null);
				response.setOrdernumber(null);
				response.setTransactionAmount(null);
				response.setBatchId(null);
				response.setApprovalCode(null);
				response.setReferenceNumber(null);
				response.setTotalSuccessCardCount(null);
				response.setTotalCardCount(null);
				response.setLineItems(null);
				response.setInvalidLineItems(null);
				response.setValidationToken(null);
			}
		);
	}

	public AuthorizeResponse authorize(AuthorizeRequest param) {
		AuthorizeResponse authorizeResponse = new AuthorizeResponse();
		// 1.验证账号密码
		QueryOutletByPosIdRequest queryOutletByPosIdRequest = new QueryOutletByPosIdRequest();
		queryOutletByPosIdRequest.setMachineId(param.getTerminalId());
		queryOutletByPosIdRequest.setPosAccount(param.getUserName());
		queryOutletByPosIdRequest.setPosPassword(param.getPassword());
		Result<OutletResponse> queryOutletByPosIdResult = posService.queryOutletByPosId(queryOutletByPosIdRequest);
		if (!queryOutletByPosIdResult.isSuccess() || queryOutletByPosIdResult.getData() == null) {
			return authorizeResponse
					.setResponseCode(
							GvPosCommonResponseCodesEnum.USER_AUTHORIZATION_FOR_MERCHANT_FAILED.getResponseCode())
					.setResponseMessage(
							GvPosCommonResponseCodesEnum.USER_AUTHORIZATION_FOR_MERCHANT_FAILED.getResponseMessage());
		}
		OutletResponse outletResponse = queryOutletByPosIdResult.getData();
		authorizeResponse
				.setMerchantOutletInfo(new MerchantOutletInfo().setStoreName(outletResponse.getOutletName())
						.setStoreZipCode(outletResponse.getPinCode())
						.setStoreState(outletResponse.getStateName())
						.setStoreCity(outletResponse.getCityName())
						.setStoreAddress1(outletResponse.getAddress1())
						.setStoreAddress2(outletResponse.getAddress2()));
		authorizeResponse.setLocaleInfo(new LocaleInfo().setCulture("id-ID").setCurrency("IDR")
				.setCurrencyPosition(0).setCurrencyDecimalDigits(0).setDisplayUnitForPoints("Points"));
		//Integer batchId = RandomUtils.nextInt(0, 9999);
		//TODO 修改->生成唯一码保存到token，截取后四位，返回出去
		String batchId = codeHelper.generateBatchId();
		authorizeResponse.setReceiptInfo(new ReceiptInfo()
				.setReceiptLine1(null)
				.setReceiptLine2(null)
				.setReceiptLine3(null)
				.setReceiptLine4(null));
		authorizeResponse.setBatchId(Integer.valueOf(batchId.substring(batchId.length() - 4)));
		// 2.获取信息封装加密参数
		AuthorizePayload authorizePayload = new AuthorizePayload();
		BeanUtils.copyProperties(param, authorizePayload);
		authorizePayload.setCurrentBatchNumber(batchId);
		//log.info("authorize jwtService.create-Request:{}", JSON.toJSONString(authorizePayload));
		String token = jwtService.create(authorizePayload);
		log.info("authorize jwtService.create-Response:{}", token);
		authorizeResponse.setAuthToken(token);

		authorizeResponse.setResponseCode(0);
		authorizeResponse.setResponseMessage("Transaction successful.");
		long expireTimes = System.currentTimeMillis() + effectiveHours  * 60 * 1000L;
		redisTemplate.opsForValue().set("GV:AUTHORIZE_BATCHID:"+token,batchId, expireTimes, TimeUnit.MILLISECONDS);


		return authorizeResponse;
	}

	public AuthorizePayload getAuthorizePayloadByToken(String token) {
		//log.info("getAuthorizePayloadByToken jwtService.resolve-Request:{}", token);
		AuthorizePayload authorizePayload = (AuthorizePayload) jwtService.resolve(token, AuthorizePayload.class);
		//log.info("getAuthorizePayloadByToken jwtService.resolve-Response:{}", JSON.toJSONString(authorizePayload));
		return authorizePayload;
	}

	/**
	 * 验证-激活
	 *
	 * @param param
	 * @return
	 */
	public TransactionResponse transactionValidateActivate(TransactionRequest param,
			AuthorizePayload authorizePayload) {
		VerifyVoucherRequest verifyVoucherRequest = bulidVerifyVoucherRequest(param, authorizePayload,
				VerifyVoucherTypeEnum.VOUCHER_VERIFY_ACTIVATION.getCode());
//		log.info("transactionValidateActivate voucherService.verifyVoucher-Request:{}",
//				JSON.toJSONString(verifyVoucherRequest));
		VerifyResponse verifyResponse = voucherService.verifyVoucher(verifyVoucherRequest);
//		log.info("transactionValidateActivate voucherService.verifyVoucher-Response:{}",
//				JSON.toJSONString(verifyResponse));
		return bulidTransactionResponse(verifyResponse, param, authorizePayload);
	}

	/**
	 * 激活
	 *
	 * @param param
	 * @return
	 */
	public TransactionResponse transactionExecuteActivate(TransactionRequest param, AuthorizePayload authorizePayload) {
		VerifyVoucherRequest verifyVoucherRequest = bulidVerifyVoucherRequest(param, authorizePayload,
				VerifyVoucherTypeEnum.VOUCHER_ACTIVATION.getCode());
//		log.info("transactionExecuteActivate voucherService.verifyVoucher-Request:{}",
//				JSON.toJSONString(verifyVoucherRequest));
		VerifyResponse verifyResponse = voucherService.verifyVoucher(verifyVoucherRequest);
//		log.info("transactionExecuteActivate voucherService.verifyVoucher-Response:{}",
//				JSON.toJSONString(verifyResponse));
		return bulidTransactionResponse(verifyResponse, param, authorizePayload);
	}

	/**
	 * 验证-使用
	 *
	 * @param param
	 * @return
	 */
	public TransactionResponse transactionValidateRedeem(TransactionRequest param, AuthorizePayload authorizePayload) {
		VerifyVoucherRequest verifyVoucherRequest = bulidVerifyVoucherRequest(param, authorizePayload,
				VerifyVoucherTypeEnum.VOUCHER_VERIFY_REDEMPTION.getCode());
//		log.info("transactionValidateRedeem voucherService.verifyVoucher-Request:{}",
//				JSON.toJSONString(verifyVoucherRequest));
		VerifyResponse verifyResponse = voucherService.verifyVoucher(verifyVoucherRequest);
//		log.info("transactionValidateRedeem voucherService.verifyVoucher-Response:{}",
//				JSON.toJSONString(verifyResponse));
		return bulidTransactionResponse(verifyResponse, param, authorizePayload);
	}

	/**
	 * 使用
	 *
	 * @param param
	 * @return
	 */
	public TransactionResponse transactionExecuteRedeem(TransactionRequest param, AuthorizePayload authorizePayload) {
		VerifyVoucherRequest verifyVoucherRequest = bulidVerifyVoucherRequest(param, authorizePayload,
				VerifyVoucherTypeEnum.VOUCHER_REDEMPTION.getCode());
//		log.info("transactionExecuteRedeem voucherService.verifyVoucher-Request:{}",
//				JSON.toJSONString(verifyVoucherRequest));
		VerifyResponse verifyResponse = voucherService.verifyVoucher(verifyVoucherRequest);
//		log.info("transactionExecuteRedeem voucherService.verifyVoucher-Response:{}",
//				JSON.toJSONString(verifyResponse));
		return bulidTransactionResponse(verifyResponse, param, authorizePayload);
	}

	/**
	 * 取消使用
	 *
	 * @param param
	 * @param batchId
	 * @return
	 */
	public CancelredeemResponse cancelredeem(CancelredeemRequest param, String terminalId, String batchId) {
		CancelRedeemRequest cancelRedeemRequest = new CancelRedeemRequest();
		BeanUtils.copyProperties(param, cancelRedeemRequest);
		//log.info("cancelredeem voucherService.cancelredeem-Request:{}", JSON.toJSONString(cancelRedeemRequest));
		cancelRedeemRequest.setTerminalId(terminalId);
		cancelRedeemRequest.setBatchID(batchId);
		CancelRedeemResponse cancelRedeemResponse = voucherService.cancelRedeem(cancelRedeemRequest);
		//log.info("cancelredeem voucherService.cancelredeem-Response:{}", JSON.toJSONString(cancelRedeemResponse));

		cancelRedeemResponse.setTerminalId(terminalId).setTransactionId(param.getTransactionId())
				.setAmount(param.getAmount()).setResponseCode(cancelRedeemResponse.getResponseCode())
				.setResponseMessage(cancelRedeemResponse.getResponseMessage());

		return BeanCopyUtils.jsonCopyBean(cancelRedeemResponse,CancelredeemResponse.class);
	}

	/**
	 * 对外接口 batchclose
	 * 这个接口是用来关闭当前批次,并且返回这这批交易的信息,包括交易类型,交易金额,交易时间,交易状态,交易id,交易流水号
	 * 然后开启一批新的交易
	 * batchNumber和TransactionId是一一对应的
	 * <p>
	 * <p>
	 * <p>
	 * <p>
	 * authorize : 生成一个授权令牌,这个操作也会打开一个新的批次,并且分配一个批次id
	 * <p>
	 * <p>
	 * <p>
	 * <p>
	 * batchClose：可以选择在一天结束的时候关闭一个批次,这个api可以获取这个批次的摘要.
	 * 同时,这个api也会获取一个新的批次ID.在交易中提供的TransactionID在一个批次中应该是唯一的.
	 * 建议为交易ID使用一个运行序列,每当参数下载或者批次关闭的时候重置该序列
	 * 每一个此api关闭时候的token也要做持久化.
	 * qc系统也会会返回这批次服务器交易摘要.如果客户端和服务器事务不匹配,api会返回Batch Mismatch错误
	 * 如果数据不可用,这些字段都为0,如果数据不可用,无论是否匹配,这个批次都将关闭.
	 * <p>
	 * <p>
	 * <p>
	 * 可以选择在一天结束时关闭一个批次，使用 batchclose API 可以获得该批次的交易摘要。这也将提供一个新的批次ID。
	 * 在交易中提供的TransactionIds在一个批次中应该是唯一的。建议为交易ID使用一个运行序列，每当参数下载或批量关闭发生时重置该序列。
	 * 请注意，每一个批次关闭将返回新的JWT令牌，这需要被持久化，并在随后的交易中传递。
	 * 批量关闭API也接受来自客户端的该批次的交易摘要，QC系统将返回同一批次的服务器端交易摘要。如果客户端和服务器端的事务不匹配，
	 * API会在响应中返回Batch Mismatch的错误。如果数据不可用，客户端应用程序可以在这些字段上传递0。
	 * 字段，如果数据不可用。无论金额是否匹配，批处理都将被关闭。
	 *
	 * @param param
	 * @param token
	 */
	public BatchcloseResponse batchclose(BatchcloseRequest param, String terminalId, String token) {
		//log.info("batchclose voucherService.batchclose-Request:{},terminalId={}", JSON.toJSONString(param), terminalId);
		BatchcloseResponse batchcloseResponse = transactionDataService.batchClose(param,token );
		//log.info("batchclose voucherService.batchclose-Response:{}", JSON.toJSONString(batchcloseResponse));
		return batchcloseResponse;
	}

	/**
	 * 创建一个新的礼品卡并激活
	 *
	 * @param param
	 * @param batchNumber
	 * @return
	 */
	public CreateandissueResponse createandissue(CreateandissueRequest param, String terminalId, String batchNumber) {
//		log.info("createandissue voucherService.createandissue-Request:{},terminalId={}", JSON.toJSONString(param),
//				terminalId);
		param.setTerminalId(terminalId);
		CreateAndIssueParam createAndIssueParam = BeanCopyUtils.jsonCopyBean(param, CreateAndIssueParam.class);
		createAndIssueParam.setBatchId(batchNumber);
		createAndIssueParam.setCardNumber(param.getCardNumber());
		CreateAndIssueGinseng andIssue = voucherService.createAndIssue(createAndIssueParam);
		CreateandissueResponse createandissueResponse = BeanCopyUtils.jsonCopyBean(andIssue,CreateandissueResponse.class);

		//log.info("createandissue voucherService.createandissue-Response:{}", JSON.toJSONString(createandissueResponse));
		return createandissueResponse;
	}




	public CancelCreateandissueResponse cancelcreateandissue(CancelCreateandissueRequest param, String terminalId) {

//		log.info("createandissue voucherService.cancelcreateandissue-Request:{},terminalId={}", JSON.toJSONString(param),
//				terminalId);
		param.setTerminalId(terminalId);
		CancelCreateAndIssueGinseng andIssue = voucherService.cancelCreateAndIssue(BeanCopyUtils.jsonCopyBean(param, CancelCreateandissueRequest.class));
		CancelCreateandissueResponse cancelCreateandissueResponse = BeanCopyUtils.jsonCopyBean(andIssue,CancelCreateandissueResponse.class);

		//log.info("createandissue voucherService.cancelcreateandissue-Response:{}", JSON.toJSONString(cancelCreateandissueResponse));
		return cancelCreateandissueResponse;
	}




	public CreateAndIssueResponse transactionsCreateAndIssue(TransactionsRequest param, String terminalId) {
//		log.info("createandissue voucherService.createandissue-Request:{},terminalId={}", JSON.toJSONString(param),
//				terminalId);
		param.setTerminalId(terminalId);
		CreateAndIssueResponse andIssue = voucherService.transactionsCreateAndIssue(param);

		//log.info("createandissue voucherService.createandissue-Response:{}", JSON.toJSONString(andIssue));
		return andIssue;
	}



	/**
	 * 取消对礼品卡的激活
	 *
	 * @param param
	 * @param batchId
	 * @return
	 */
	public CancelactivateResponse cancelactivate(CancelactivateRequest param, String terminalId, String batchId) {
//		log.info("cancelactivate voucherService.cancelactivate-Request:{},terminalId={}", JSON.toJSONString(param),
//				terminalId);


		param.setTerminalId(terminalId);

		CancelActivateGinseng cancelActivate = voucherService.cancelActivate(BeanCopyUtils.jsonCopyBean(param, CancelActivateParam.class),batchId );
		CancelactivateResponse cancelactivateResponse = BeanCopyUtils.jsonCopyBean(cancelActivate,CancelactivateResponse.class);


		//log.info("cancelactivate voucherService.cancelactivate-Response:{}", JSON.toJSONString(cancelactivateResponse));
		return cancelactivateResponse;
	}

	/**
	 * 激活一个礼品卡从购买状态到激活状态
	 *
	 * @param param
	 * @param batchNumber
	 * @return
	 */
	public ActivateonlyResponse activateonly(ActivateonlyRequest param, String terminalId, String batchNumber) {
//		log.info("activateonly voucherService.activateonly-Request:{},terminalId={}", JSON.toJSONString(param),
//				terminalId);
		param.setTerminalId(terminalId);
		ActivateOnlyParam activateOnlyParam = BeanCopyUtils.jsonCopyBean(param, ActivateOnlyParam.class);
		activateOnlyParam.setBatchNumber(batchNumber);
		ActivateOnlyGinseng activateOnly = voucherService.activateOnly(activateOnlyParam);


		ActivateonlyResponse activateonlyResponse = BeanCopyUtils.jsonCopyBean(activateOnly,ActivateonlyResponse.class);
		//log.info("activateonly voucherService.activateonly-Response:{}", JSON.toJSONString(activateonlyResponse));
		return activateonlyResponse;
	}

	/**
	 * 获取卡上的交易历史记录（目前最多为30笔交易）
	 *
	 * @param param
	 * @return
	 */
	public CardtransactionhistoryResponse cardtransactionhistory(CardtransactionhistoryRequest param,
			String terminalId) {
//		log.info("cardtransactionhistory voucherService.cardtransactionhistory-Request:{},terminalId={}",
//				JSON.toJSONString(param), terminalId);

		param.setTerminalId(terminalId);
		CardtransactionhistoryResponse cardtransactionhistoryResponse = transactionDataService.cardtransactionhistory(param);
//		log.info("cardtransactionhistory voucherService.cardtransactionhistory-Response:{}",
//				JSON.toJSONString(cardtransactionhistoryResponse));
		return cardtransactionhistoryResponse;
	}

	/**
	 * 为虚拟卡号生成具有有限有效期的一次条形码
	 *
	 * @param param
	 * @param batchNumber
	 * @return
	 */
	public OnetimebarcodeResponse onetimebarcode(OnetimebarcodeRequest param, String terminalId, String batchNumber) {
//		log.info("onetimebarcode voucherService.onetimebarcode-Request:{},terminalId={}", JSON.toJSONString(param),
//				terminalId);
		param.setTerminalId(terminalId);
		param.setBatchId(batchNumber);
		OnetimebarcodeResponse onetimebarcodeResponse = voucherService.generateDigitalBarCode(param);

		//log.info("onetimebarcode voucherService.onetimebarcode-Response:{}", JSON.toJSONString(onetimebarcodeResponse));
		return onetimebarcodeResponse;
	}

	private VerifyVoucherRequest bulidVerifyVoucherRequest(TransactionRequest param, AuthorizePayload authorizePayload,
			String type) {
		VerifyVoucherRequest verifyVoucherRequest = new VerifyVoucherRequest();
		verifyVoucherRequest.setMachineId(authorizePayload.getTerminalId());
		verifyVoucherRequest.setPurchaserInfo(param.getPurchaserInfo());
		verifyVoucherRequest.setCardholderInfo(param.getCardholderInfo());
		verifyVoucherRequest.setTransactionId(String.valueOf(param.getTransactionId()));
		if (StringUtil.isNotEmpty(param.getNotes())){
			verifyVoucherRequest.setNotes(param.getNotes());
		}
		List<VerifyVoucherInfo> serifyVoucherInfos = new ArrayList<>();
		param.getLineItems().stream().forEach(lineItem -> {
			VerifyVoucherInfo verifyVoucherInfo = new VerifyVoucherInfo();
			verifyVoucherInfo.setType(type);
			verifyVoucherInfo.setInputType(lineItem.getInputType());

			verifyVoucherInfo.setCardInfo(lineItem.getCardInfo());
			verifyVoucherInfo.setStartCardInfo(lineItem.getStartCardInfo());
			verifyVoucherInfo.setEndCardInfo(lineItem.getEndCardInfo());

			serifyVoucherInfos.add(verifyVoucherInfo);
		});
		verifyVoucherRequest.setVerifyVoucherInfo(serifyVoucherInfos);
		return verifyVoucherRequest;
	}

	private TransactionResponse bulidTransactionResponse(VerifyResponse verifyResponse, TransactionRequest param,
			AuthorizePayload authorizePayload) {
		TransactionResponse transactionResponse = null;
		// 使用新的对象池管理器
		try {
			transactionResponse = getTransactionResponsePool().borrowObject();
		} catch (Exception e) {
			log.warn("Failed to borrow object from pool, falling back to creating new object", e);
			transactionResponse = new TransactionResponse();
		}

		// 以下逻辑与原来完全相同
		transactionResponse
				.setResponseCode(0)
				.setTransactionId(param.getTransactionId())
				.setTransactionTypeId(param.getTransactionTypeId())
				.setActionType(Integer.valueOf(param.getActionType()))
				.setSourceId(param.getSourceId())
				.setMessageId(param.getMessageId())
				.setOrdernumber(param.getOrderNumber())
				.setResponseCode(GvPosCommonResponseCodesEnum.TRANSACTION_SUCCESS.getResponseCode())
				.setResponseMessage(GvPosCommonResponseCodesEnum.TRANSACTION_SUCCESS.getResponseMessage())
				.setTransactionAmount(verifyResponse.getSuccessVoucherList().stream().map(VerifyVoucherGinseng::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(1))
				.setBatchId(authorizePayload.getResponseBatchId(4))
				//https://jira.gtech.asia/browse/MER-750  approvalCode 空 MER-1844已经修改
				//.setApprovalCode(codeHelper.generateApproveCode())
				//.setApprovalCode(null)
				.setReferenceNumber(String.valueOf(param.getTransactionId()));
		//FW: Map Voucher New Integration  PLIPos


		Integer totalSuccessCardCount = 0;
		Integer totalCardCount = 0;

		List<LineItemResponseInfo> lineItems = new ArrayList<>();
		List<LineItemResponseInfo> invalidLineItems = new ArrayList<>();

		List<VerifyVoucherGinseng> successVoucherList = verifyResponse.getSuccessVoucherList();
		List<VerifyVoucherGinseng> failVoucherList = verifyResponse.getFailVoucherList();

		if (!CollectionUtils.isEmpty(successVoucherList)) {
			lineItems.addAll(bulidLineItems(successVoucherList));
			for (VerifyVoucherGinseng verifyVoucherGinseng : successVoucherList) {
				totalSuccessCardCount += verifyVoucherGinseng.getSuccessCardCount();
				totalCardCount += verifyVoucherGinseng.getTotalCardCount();
			}
		}
		if (!CollectionUtils.isEmpty(failVoucherList)) {
			for (VerifyVoucherGinseng verifyVoucherGinseng : failVoucherList) {
				totalCardCount += verifyVoucherGinseng.getTotalCardCount();
			}
			invalidLineItems.addAll(bulidInvalidLineItems(failVoucherList));

			transactionResponse.setResponseCode(GvPosCommonResponseCodesEnum.VALIDATION_FAILED.getResponseCode())
					.setResponseMessage(GvPosCommonResponseCodesEnum.VALIDATION_FAILED.getResponseMessage());
		}
		if (CollectionUtils.isEmpty(successVoucherList) && CollectionUtils.isEmpty(failVoucherList)) {
			if (GvPosTransactionTypesEnum.BULK_ACTIVATE.getCode() == param.getTransactionTypeId()
					&& GvPosActionTypeEnum.EXECUTE.getCode().equals(param.getActionType())) {
				transactionResponse.setResponseCode(GvPosCommonResponseCodesEnum.INVALID_LINEITEM.getResponseCode())
						.setResponseMessage(GvPosCommonResponseCodesEnum.INVALID_LINEITEM.getResponseMessage());
			} else if (GvPosTransactionTypesEnum.BULK_REDEEM.getCode() == param.getTransactionTypeId()
					&& GvPosActionTypeEnum.EXECUTE.getCode().equals(param.getActionType())) {
				transactionResponse.setResponseCode(GvPosCommonResponseCodesEnum.REDEMPTION_FAILED.getResponseCode())
						.setResponseMessage(GvPosCommonResponseCodesEnum.REDEMPTION_FAILED.getResponseMessage());
			}
		}
		transactionResponse.setTotalSuccessCardCount(totalSuccessCardCount);
		transactionResponse.setTotalCardCount(totalCardCount);
		transactionResponse.setLineItems(lineItems);
		transactionResponse.setInvalidLineItems(invalidLineItems);

		if (transactionResponse.getResponseCode().equals(GvPosCommonResponseCodesEnum.TRANSACTION_SUCCESS.getResponseCode())) {
			String approvalCode = String.valueOf(redisTemplate.opsForValue().get("APPROVALCODE:" + param.getTransactionId()));
			if (StringUtil.isEmpty(approvalCode)){
				approvalCode = codeHelper.generateApproveCode();
				redisTemplate.opsForValue().set("APPROVALCODE:" + param.getTransactionId(), approvalCode, 365, TimeUnit.DAYS);
			}
			transactionResponse.setApprovalCode(approvalCode);
		}
		
		// 重要：处理完成后不要归还对象到池中，因为返回值会被外部使用
		return transactionResponse;
	}

	private List<LineItemResponseInfo> bulidLineItems(List<VerifyVoucherGinseng> successVoucherList) {
		List<LineItemResponseInfo> lineItems = new ArrayList<>();
		successVoucherList.stream().forEach(verifyVoucherGinseng -> {
			LineItemResponseInfo lineItemResponseInfo = new LineItemResponseInfo()
					.setLineItemNo(String.format("%2d",lineItems.size() + 1).replace(" ", "0")).setLineItemStatus("SUCCESS")
					//.setCardProgramGroupName(verifyVoucherGinseng.getCardProgramGroupName())
					.setSuccessCardCount(verifyVoucherGinseng.getSuccessCardCount())
					.setTotalCardCount(verifyVoucherGinseng.getTotalCardCount())
					.setDesignCode(verifyVoucherGinseng.getDesignCode())
					.setProductCode(verifyVoucherGinseng.getProductCode())
					.setTotalAmount(verifyVoucherGinseng.getTotalAmount())
					.setTransactionAmount(verifyVoucherGinseng.getTotalAmount().setScale(1))
					.setStartCardNumber(verifyVoucherGinseng.getStartCardNumber())
					.setEndCardNumber(verifyVoucherGinseng.getEndCardNumber());

			if (!CollectionUtils.isEmpty(verifyVoucherGinseng.getVerifyVoucherResponses())) {
				List<CardResponse> cards = new ArrayList<>();
				verifyVoucherGinseng.getVerifyVoucherResponses().stream()
						.filter(verifyVoucherResponse -> verifyVoucherResponse.getVoucherInfo() != null)
						.forEach(verifyVoucherResponse -> {
							// verifyVoucherResponse.getIsSuccess() 目前不判断状态
							VoucherInfo voucherInfo = verifyVoucherResponse.getVoucherInfo();

							//https://jira.gtech.asia/browse/MER-742 barCode --> voucherCode
							cards.add(new CardResponse().setCardNumber(voucherInfo.getVoucherCode())
									.setResponseCode(0).setResponseMessage("Transaction successful.")
									.setCardProgramGroupName(voucherInfo.getCpgName())
									.setCardPin(voucherInfo.getVoucherPin())
									//https://jira.gtech.asia/browse/MER-742 ActivationCode --> null
									//.setActivationCode(voucherInfo.getVoucherActiveCode())
									.setDesignCode(voucherInfo.getMopCode())
									.setActivationURL(voucherInfo.getVoucherActiveUrl())
									.setBarcode(voucherInfo.getVoucherBarcode())
									//2. cardBalance : non Activated voucher should be "0". Currently, we still got the cardBalance with amount.
									.setCardBalance(voucherInfo.getStatus().equals(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode()) ?
											voucherInfo.getDenomination().setScale(3) : BigDecimal.ZERO.setScale(3))
									.setTransactionAmount(voucherInfo.getDenomination().setScale(1))
									.setCardExpiry(DateUtil.format(voucherInfo.getVoucherEffectiveDate(),"yyyy-MM-dd'T'HH:mm:ss"))

									.setProductCode(voucherInfo.getProductCode())
									.setCardCreationType(GvPosCardCreationTypeEnum
											.getCodeByVoucherCardCreationTypeCode(voucherInfo.getMopCode()))
									.setCardStatus(voucherInfo.getVoucherStatus() == 0 ?
											VoucherEnableDisablePosEnum.getDescByCode(voucherInfo.getVoucherStatus()):
											GvPosCardStatusCodesEnum.getCodeByVoucherCode(voucherInfo.getStatus())));
						});
				lineItemResponseInfo.setCards(cards);
			}
			lineItems.add(lineItemResponseInfo);
		});
		return lineItems;
	}

	private List<LineItemResponseInfo> bulidInvalidLineItems(List<VerifyVoucherGinseng> failVoucherList) {
		List<LineItemResponseInfo> invalidLineItems = new ArrayList<>();
		failVoucherList.stream().forEach(verifyVoucherGinseng -> {
			Integer no = invalidLineItems.size() + 1;
			LineItemResponseInfo lineItemResponseInfo = new LineItemResponseInfo()
					//https://jira.gtech.asia/browse/MER-750 01-1
					.setLineItemNo(String.format("%2d",no).replace(" ", "0") +"-"+no)
					.setLineItemStatus("FAILED")
					//.setCardProgramGroupName(verifyVoucherGinseng.getCardProgramGroupName()).setSuccessCardCount(0)
					.setTotalCardCount(verifyVoucherGinseng.getTotalCardCount())
					.setDesignCode(verifyVoucherGinseng.getDesignCode())
					.setProductCode(verifyVoucherGinseng.getProductCode())
					//https://jira.gtech.asia/browse/MER-750 successCardCount should 0
					.setSuccessCardCount(0)
					.setTotalAmount(verifyVoucherGinseng.getTotalAmount())

					//https://jira.gtech.asia/browse/MER-750  transactionamount should 0
					//.setTransactionAmount(verifyVoucherGinseng.getTotalAmount())
					.setTransactionAmount(BigDecimal.ZERO.setScale(1))
					.setStartCardNumber(verifyVoucherGinseng.getStartCardNumber())
					.setEndCardNumber(verifyVoucherGinseng.getEndCardNumber());

			if (!CollectionUtils.isEmpty(verifyVoucherGinseng.getVerifyVoucherResponses())) {
				List<CardResponse> cards = new ArrayList<>();
				verifyVoucherGinseng.getVerifyVoucherResponses().stream()
						.filter(verifyVoucherResponse -> verifyVoucherResponse.getVoucherInfo() != null)
						.forEach(verifyVoucherResponse -> {
							// verifyVoucherResponse.getIsSuccess() 目前不判断状态
							VoucherInfo voucherInfo = verifyVoucherResponse.getVoucherInfo();
							////https://jira.gtech.asia/browse/MER-742 barCode --> voucherCode
							CardResponse cardResponse = new CardResponse()
									.setCardNumber(voucherInfo.getVoucherCode())
									.setCardProgramGroupName(voucherInfo.getCpgName())
									.setCardPin(voucherInfo.getVoucherPin())
									//https://jira.gtech.asia/browse/MER-742 ActivationCode --> null
									//.setActivationCode(voucherInfo.getVoucherActiveCode())
									.setActivationURL(voucherInfo.getVoucherActiveUrl())
									//https://jira.gtech.asia/browse/MER-750  barcode should null
									//.setBarcode(voucherInfo.getVoucherBarcode())
									.setBarcode(null)
									//2. cardBalance : non Activated voucher should be "0". Currently, we still got the cardBalance with amount.
									.setCardBalance(voucherInfo.getStatus().equals(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode())  ? voucherInfo.getDenomination().setScale(3) : BigDecimal.ZERO.setScale(3))
									//https://jira.gtech.asia/browse/MER-750 TransactionAmount should null
									//.setTransactionAmount(voucherInfo.getDenomination())
									.setTransactionAmount(BigDecimal.ZERO.setScale(1))
									//https://jira.gtech.asia/browse/MER-742  designCode   productCode
									.setDesignCode(voucherInfo.getMopCode())

									.setProductCode(voucherInfo.getProductCode())
									//https://jira.gtech.asia/browse/MER-750 cardExpiry should null
									//.setCardExpiry(voucherInfo.getVoucherEffectiveDate())
									.setCardExpiry(null)
									.setCardCreationType(GvPosCardCreationTypeEnum
											.getCodeByVoucherCardCreationTypeCode(voucherInfo.getMopCode()))
									.setCardStatus(null == voucherInfo.getVoucherStatus() ? "" : voucherInfo.getVoucherStatus() == 0 ?
											VoucherEnableDisablePosEnum.getDescByCode(voucherInfo.getVoucherStatus()):
											GvPosCardStatusCodesEnum.getCodeByVoucherCode(voucherInfo.getStatus()))
									.setResponseCode(
											StringUtils.isEmpty(verifyVoucherResponse.getResponseCode()) ? 10012
													: Integer.valueOf(verifyVoucherResponse.getResponseCode()))
									.setResponseMessage(verifyVoucherResponse.getResponseMessage());
							cards.add(cardResponse);
						});
				lineItemResponseInfo.setCards(cards);
			}
			invalidLineItems.add(lineItemResponseInfo);
		});

		return invalidLineItems;
	}



	public Result<GetVoucherInformationResponse>  getVoucherInformation(GetVoucherInformationRequest request){
		return voucherService.getVoucherInformation(request);
	}


	public AuthorizeResponseV3 authorizeV3(AuthorizeRequest param) {
		AuthorizeResponseV3 authorizeResponse = new AuthorizeResponseV3();
		// 1.验证账号密码
		QueryOutletByPosIdRequest queryOutletByPosIdRequest = new QueryOutletByPosIdRequest();
		queryOutletByPosIdRequest.setMachineId(param.getTerminalId());
		queryOutletByPosIdRequest.setPosAccount(param.getUserName());
		queryOutletByPosIdRequest.setPosPassword(param.getPassword());
		Result<OutletResponse> queryOutletByPosIdResult = posService.queryOutletByPosId(queryOutletByPosIdRequest);
		if (!queryOutletByPosIdResult.isSuccess() || queryOutletByPosIdResult.getData() == null) {
			return authorizeResponse
					.setResponseCode(
							GvPosCommonResponseCodesEnum.USER_AUTHORIZATION_FOR_MERCHANT_FAILED.getResponseCode())
					.setResponseMessage(
							GvPosCommonResponseCodesEnum.USER_AUTHORIZATION_FOR_MERCHANT_FAILED.getResponseMessage());
		}
		OutletResponse outletResponse = queryOutletByPosIdResult.getData();
		authorizeResponse
				.setMerchantName(outletResponse.getOutletName())
				.setOutletPinCode(outletResponse.getPinCode())
				.setOutletState(outletResponse.getStateName())
				.setOutletCity(outletResponse.getCityName())
				.setOutletAddress1(outletResponse.getAddress1())
				.setOutletAddress2(outletResponse.getAddress2());
		authorizeResponse
				.setCultureName("id-ID")
				.setCurrencySymbol("IDR")
				.setCurrencyPosition(0)
				.setCurrencyDecimalDigits(0)
				.setDisplayUnitForPoints("Points");
		//Integer batchId = RandomUtils.nextInt(0, 9999);
		String batchId = codeHelper.generateBatchId();
		authorizeResponse
				.setReceiptFooterLine1(null)
				.setReceiptFooterLine2(null)
				.setReceiptFooterLine3(null)
				.setReceiptFooterLine4(null);
		authorizeResponse.setBatchId(Integer.valueOf(batchId.substring(batchId.length() - 4)));
		authorizeResponse.setApprovalCode(codeHelper.generateApproveCode());
		// 2.获取信息封装加密参数
		AuthorizePayload authorizePayload = new AuthorizePayload();
		BeanUtils.copyProperties(param, authorizePayload);
		authorizePayload.setCurrentBatchNumber(batchId);
		//log.info("authorize jwtService.create-Request:{}", JSON.toJSONString(authorizePayload));
		String token = jwtService.create(authorizePayload);
		log.info("authorize jwtService.create-Response:{}", token);
		authorizeResponse.setAuthToken(token);
		authorizeResponse.setTransactionType("INITIALIZE");

		DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.FFFFFFFXXX");
		df.setTimeZone(TimeZone.getDefault());
		String format = df.format(new Date());

		authorizeResponse.setDateAtServer(format);
		authorizeResponse.setResponseCode(0);
		authorizeResponse.setResponseMessage("Transaction successful.");
		return authorizeResponse;
	}

	public BulkCancelRedeemResponseV2 bulkCancelRedeem(BulkCancelRedeemRequestV2 request, String terminalId, String batchId) {


		return voucherService.bulkCancelRedeemV2(request,terminalId,batchId);

	}

	/**
	 * 归还TransactionResponse对象到池中
	 * 注意：只有确认对象不再被使用时才能调用此方法
	 */
	public void returnTransactionResponseToPool(TransactionResponse response) {
		if (response != null) {
			try {
				getTransactionResponsePool().returnObject(response);
			} catch (Exception e) {
				log.warn("Failed to return object to pool", e);
			}
		}
	}
	
	/**
	 * 销毁对象池
	 * 通常在应用关闭时调用
	 */
	@PreDestroy
	public void destroyPool() {
		// 对象池管理器会自动处理关闭
	}
}
