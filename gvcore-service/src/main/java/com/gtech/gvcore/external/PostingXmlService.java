package com.gtech.gvcore.external;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.constants.SapXmlConstants;
import com.gtech.gvcore.common.enums.CustomerOrderStatusEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.enums.VoucherReceiveSourceTypeEnum;
import com.gtech.gvcore.common.request.customerorder.SapSalesPostingXmlRequest;
import com.gtech.gvcore.common.request.postingxml.QueryPostingxmlRequest;
import com.gtech.gvcore.common.request.receive.QueryCancelVoucherReceiveRequest;
import com.gtech.gvcore.common.request.receive.QueryReceiveRecordRequest;
import com.gtech.gvcore.common.utils.SftpUtil;
import com.gtech.gvcore.common.utils.XmlUtils;
import com.gtech.gvcore.dao.dto.CustomerOrderDto;
import com.gtech.gvcore.dao.dto.VoucherReceiveDto;
import com.gtech.gvcore.dao.mapper.CancelVoucherReceiveMapper;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.MeansOfPayment;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dto.SalesDataDto;
import com.gtech.gvcore.dto.SalesDataResultDto;
import com.gtech.gvcore.dto.salespostingxml.SumCustomerOrderGroupByArticle;
import com.gtech.gvcore.service.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.xml.parsers.ParserConfigurationException;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PostingXmlService {

    public static final String ARTNR = "ARTNR";
    public static final String E_1_WPB_06 = "E1WPB06";
    public static final String VORZEICHEN = "VORZEICHEN";
    public static final String ZAHLART = "ZAHLART";
    public static final String E_1_WPB_01 = "E1WPB01";
    public static final String VORGDATUM = "VORGDATUM";
    public static final String BONNUMMER = "BONNUMMER";
    public static final String SUMME = "SUMME";
    public static final String WPUUMS = "WPUUMS";
    public static final String BELEGWAERS = "BELEGWAERS";
    public static final String WPUBON_C = "WPUBON_C";
    @Value("${gv.sftp.url:}")
	private String sftpUrl;

	@Value("${gv.sftp.name:}")
	private String name;

	@Value("${gv.sftp.pwd:}")
	private String pwd;

    @Value("${gv.outlet.warehouse.MV01:}")
    private String mv01;
    @Value("${gv.outlet.warehouse.MV03:}")
    private String mv03;
    @Value("${gv.outlet.warehouse.MV04:}")
    private String mv04;

	@Autowired
	private VoucherReceiveRecordService voucherReceiveRecordService;

    @Autowired
    private CancelVoucherReceiveMapper cancelVoucherReceiveMapper;

    @Autowired
    private OutletService outletService;

    @Autowired
    private CustomerOrderService customerOrderService;

    @Autowired
    private MeansOfPaymentService meansOfPaymentService;

    @Autowired
    private TransactionDataService transactionDataService;




    public static final String WPUBON = "WPUBON";





    public void generatePosting(QueryPostingxmlRequest request) {
        if (StringUtil.isBlank(request.getIssuerCode()))
            request.setIssuerCode("MAP");

		// 1. query receive record
		QueryReceiveRecordRequest queryReceiveRecordRequest = new QueryReceiveRecordRequest();
		queryReceiveRecordRequest.setCreateTime(request.getQueryTime());
		queryReceiveRecordRequest.setSourceType(VoucherReceiveSourceTypeEnum.GENERATE.getCode());
		queryReceiveRecordRequest.setIssuerCode(request.getIssuerCode());
		List<VoucherReceiveDto> receiveList = voucherReceiveRecordService.queryReceiveRecord(queryReceiveRecordRequest);
		//log.info(JSON.toJSONString(receiveList));
		if (CollectionUtils.isEmpty(receiveList)) {
			//log.info("source type : {}, {} don't have goods receipt", VoucherReceiveSourceTypeEnum.GENERATE.getCode(), request.getQueryTime());
			return;
		}

        QueryCancelVoucherReceiveRequest cancelRequest = new QueryCancelVoucherReceiveRequest();
        cancelRequest.setCreateTime(request.getQueryTime());
        cancelRequest.setSourceType(VoucherReceiveSourceTypeEnum.GENERATE.getCode());
        cancelRequest.setIssuerCode(request.getIssuerCode());
        List<VoucherReceiveDto> cancelByXml = cancelVoucherReceiveMapper.selectCancelByXml(cancelRequest);
        if (CollectionUtils.isNotEmpty(cancelByXml)) {
            //将cancelByXml中的receiveNum改为负数
            cancelByXml.forEach(voucherReceiveDto -> {
                voucherReceiveDto.setReceivedNum(-voucherReceiveDto.getReceivedNum());
            });
        }
        receiveList.addAll(cancelByXml);


        // 2. make xml, group by order no
		Map<String, List<VoucherReceiveDto>> receigeDtoMap = receiveList.stream().collect(Collectors.groupingBy(VoucherReceiveDto::getPurchaseOrderNo));
		int i = 1;
		for (Iterator<String> iterator = receigeDtoMap.keySet().iterator(); iterator.hasNext();) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            String orderNo = iterator.next();
			List<VoucherReceiveDto> list = receigeDtoMap.get(orderNo);
			String nowDate = DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14);
			String createDate = nowDate.substring(0, 8);
			String createTime = nowDate.substring(8, 14);
			VoucherReceiveDto voucherReceiveDto = list.get(0);
			String receoveRecordDate = DateUtil.format(voucherReceiveDto.getCreateTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
			String receiveDate = receoveRecordDate.substring(0, 8);
			String articleCode = voucherReceiveDto.getArticleCode();
			Integer receiveNum = list.stream().map(VoucherReceiveDto::getReceivedNum).reduce(0, Integer::sum);
			String xml = String.format(PostingXmlTemplate.GOODS_RECEIPT_XML, 
					createDate, createTime, createDate, receiveDate, receiveDate, orderNo, articleCode, receiveNum);
			String fileName = "PUBLISHED_WPUWBW_PO_HO01_" + nowDate.substring(0, 6) + "-" + i + "_" + createDate + "_" + createTime + ".xml";
			if (0!=receiveNum) {
                upload(fileName, xml,"Receive");
                i++;
            }

		}
	}

    private void upload(String fileName, String xml,String filePath) {
		try {
			URL url = null;
			if (filePath.equals("Receive")){
			    url= new URL(sftpUrl+"/GR");
            }else {
                url = new URL(sftpUrl+"/CorpSales");
            }
            uploadXml(xml,fileName);

			SftpUtil sftpUtil = new SftpUtil(name, pwd, url.getHost(), url.getPort(), null, SftpUtil.AUTH_TYPE_PAD);

			// 创建连接
			sftpUtil.connect();
			InputStream inputStream = IOUtils.toInputStream(xml, StandardCharsets.UTF_8);
            //上传到userDir路径中没有文件夹则新建
			sftpUtil.upload(url.getPath(), fileName, inputStream);
			sftpUtil.disconnect();

		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}

	}


  /*  private static String generateXmlContent() {
        return "<root>\n" +
                "  <element>Example content</element>\n" +
                "</root>";
    }
    public static void main(String[] args) {
        String xmlContent = generateXmlContent();
        uploadXml( xmlContent,"test.xml");
    }
*/



    @SneakyThrows
    public static void uploadXml( String xmlContent,String xmlName) {
        // 拼接完整的文件路径
        String path = System.getProperty("user.dir") + "/gvcore-web/src/main/resources/sftp/" + xmlName;

        // 创建文件对象
        Path file = Paths.get(path);

        // 将字符串内容写入到文件
        try {
            // 创建父目录，如果不存在
            Files.createDirectories(file.getParent());
            Files.write(file, xmlContent.getBytes());
            log.info("XML file uploaded successfully.");
        } catch (IOException e) {
            log.error("Error uploading XML file: " + e.getMessage());
        }

    }

    /**
     * MV01
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年6月14日
     */
    public Result<Void> customerOrderXml(SapSalesPostingXmlRequest request) {

        if (StringUtil.isBlank(request.getOutletCode()))
            request.setOutletCode(mv01);


        Outlet outlet = outletService.queryByOutletCode(request.getOutletCode());
        if (outlet == null) {
            log.info("customerOrderXml outlet is null, outletCode={}", request.getOutletCode());
            return Result.failed(ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.code(),
                    ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.desc());
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        if (request.getQueryDate() == null) {
            calendar.add(Calendar.DAY_OF_YEAR, -1);
        } else {
            calendar.setTime(request.getQueryDate());
        }
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        Date queryDateTimeStart = calendar.getTime();
        calendar.add(Calendar.DAY_OF_YEAR, 1);
        Date queryDateTimeEnd = calendar.getTime();

        CustomerOrderDto dto = new CustomerOrderDto();
        //dto.setIssuerCode(outlet.getIssuerCode());
        dto.setOutletCode(request.getOutletCode());
        dto.setDeleteStatus(GvcoreConstants.DELETE_STATUS_ENABLE);

        dto.setReleaseTimeStart(queryDateTimeStart);
        dto.setReleaseTimeEnd(queryDateTimeEnd);

        dto.setStatus(CustomerOrderStatusEnum.CANCELED.getStatus());
        dto.setUpdateTimeStart(queryDateTimeStart);
        dto.setUpdateTimeEnd(queryDateTimeEnd);

        try {
            sumGroupByArticleXml(request, outlet, dto);
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
                sumGroupByMopXml(request, outlet, dto);
        } catch (ParserConfigurationException e) {
            log.error(request.getOutletCode() + " customerOrderXml newDocument Exception=" + e.getMessage(), e);
        }

        return Result.ok();
    }

    private void sumGroupByMopXml(SapSalesPostingXmlRequest request, Outlet outlet, CustomerOrderDto dto)
            throws ParserConfigurationException {

        // 真确的应该是means_of_payment_code, IFNULL(SUM(amount), 0) amount
        // 目前这样计算是因为保存的数据是错的，其他人不想修改，只能配合错误的数据修改, 会产生负数需要再程序内修改
        List<CustomerOrder> sumList = customerOrderService.sumGroupByMeansOfPaymentCode(dto);
        if (CollectionUtils.isEmpty(sumList)) {
            log.info("sumGroupByMopXml sumList is empty, outletCode={}", request.getOutletCode());
            return;
        }

        Pair<List<CustomerOrder>, Map<String, MeansOfPayment>> pair = subtractCancelAmount(dto, sumList);
        sumList = pair.getLeft();
        if (CollectionUtils.isEmpty(sumList)) {
            log.info("sumGroupByMopXml subtractCancel sumList is empty, outletCode={}", request.getOutletCode());
            return;
        }

        Map<String, MeansOfPayment> mopMap = pair.getRight();
        try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        String nowDate = DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        String currentDate = nowDate.substring(0, 8);
        String currentTime = nowDate.substring(8, 14);
        String queryDate = DateUtil.format(dto.getReleaseTimeStart(), DateUtil.FORMAT_YYYYMMDD);

        org.w3c.dom.Document document = XmlUtils.newDocument();

        String docrel = "701";
        String idoctyp = "WPUBON01";
        String mestyp = WPUBON;


        org.w3c.dom.Element idoc = createIdoc(outlet, currentDate, currentTime, document, docrel, idoctyp, mestyp);

        org.w3c.dom.Element e1wpb02 = XmlUtils.createElement(document, "E1WPB02");
        e1wpb02.setAttribute(SapXmlConstants.SEGMENT_NAME, SapXmlConstants.SEGMENT_VALUE);
        e1wpb02.appendChild(XmlUtils.createElement(document, "VORGANGART", "ZMOP"));
        e1wpb02.appendChild(XmlUtils.createElement(document, "QUALARTNR", "ARTN"));
        org.w3c.dom.Element artnr = XmlUtils.createElement(document, ARTNR, "MEANS_OF_PAYMENT");
        e1wpb02.appendChild(artnr);

        org.w3c.dom.Element e1wpb06 = XmlUtils.createElement(document, E_1_WPB_06);
        e1wpb06.setAttribute(SapXmlConstants.SEGMENT_NAME, SapXmlConstants.SEGMENT_VALUE);

        e1wpb06.appendChild(XmlUtils.createElement(document, VORZEICHEN, "-"));
        e1wpb06.appendChild(XmlUtils.createElement(document, ZAHLART, "ZMOP"));

        DecimalFormat df = new DecimalFormat("0.00##");

        int i = 0;
        BigDecimal sumAmount = new BigDecimal("0");
        for (CustomerOrder customerOrder : sumList) {

            // 负数改为0
            if (BigDecimal.ZERO.compareTo(customerOrder.getAmount()) > 0) {
                customerOrder.setAmount(BigDecimal.ZERO);
            }

            i++;
            org.w3c.dom.Element e1wpb01 = XmlUtils.createElement(document, E_1_WPB_01);
            e1wpb01.setAttribute(SapXmlConstants.SEGMENT_NAME, SapXmlConstants.SEGMENT_VALUE);
            e1wpb01.appendChild(XmlUtils.createElement(document, VORGDATUM, queryDate));

            MeansOfPayment meansOfPayment = mopMap.get(customerOrder.getMeansOfPaymentCode());
            String bonnummer = StringUtil.EMPTY;
            if (meansOfPayment != null) {
                bonnummer = meansOfPayment.getMopName();
                int index = bonnummer.indexOf('-');
                if (index >= 0) {
                    bonnummer = bonnummer.substring(index + 1);
                }
            }
            e1wpb01.appendChild(XmlUtils.createElement(document, BONNUMMER, bonnummer));
            e1wpb01.appendChild(XmlUtils.createElement(document, "KUNDNR", bonnummer));
            e1wpb01.appendChild(
                    XmlUtils.createElement(document, SapXmlConstants.BELEGWAERS, SapXmlConstants.BELEGWAERS_IDR));
            e1wpb01.appendChild(e1wpb02);

            org.w3c.dom.Element summe = XmlUtils.createElement(document, SUMME, df.format(customerOrder.getAmount()));
            e1wpb06.appendChild(summe);
            e1wpb01.appendChild(e1wpb06);

            idoc.appendChild(e1wpb01);

            String xmlFileName = "WPUBON_" + outlet.getBusinessOutletCode() + SapXmlConstants.SEPARATOR + queryDate
                    + SapXmlConstants.SEPARATOR + i + SapXmlConstants.SEPARATOR + currentDate
                    + SapXmlConstants.SEPARATOR + currentTime + SapXmlConstants.XML_EXTENSION;

            upload(xmlFileName, XmlUtils.documentToString(document),"Sales");

            e1wpb06.removeChild(summe);
            idoc.removeChild(e1wpb01);

            sumAmount = sumAmount.add(customerOrder.getAmount());
        }

        org.w3c.dom.Element e1wpb01 = XmlUtils.createElement(document, E_1_WPB_01);
        e1wpb01.setAttribute(SapXmlConstants.SEGMENT_NAME, SapXmlConstants.SEGMENT_VALUE);
        e1wpb01.appendChild(XmlUtils.createElement(document, VORGDATUM, queryDate));
        e1wpb01.appendChild(XmlUtils.createElement(document, BONNUMMER, "POS CLEARING"));
        e1wpb01.appendChild(
                XmlUtils.createElement(document, SapXmlConstants.BELEGWAERS, SapXmlConstants.BELEGWAERS_IDR));
        e1wpb01.appendChild(e1wpb02);
        artnr.setTextContent("POS_CLEARING");
        e1wpb01.appendChild(e1wpb02);

        e1wpb06 = XmlUtils.createElement(document, E_1_WPB_06);
        e1wpb06.setAttribute(SapXmlConstants.SEGMENT_NAME, SapXmlConstants.SEGMENT_VALUE);
        e1wpb06.appendChild(XmlUtils.createElement(document, ZAHLART, "ZPOS"));
        e1wpb06.appendChild(XmlUtils.createElement(document, SUMME, df.format(sumAmount)));
        e1wpb01.appendChild(e1wpb06);

        idoc.appendChild(e1wpb01);

        i = 1;
        String xmlFileName = "WPUBON_C_" + outlet.getBusinessOutletCode() + SapXmlConstants.SEPARATOR + queryDate
                + SapXmlConstants.SEPARATOR + i + SapXmlConstants.SEPARATOR + currentDate + SapXmlConstants.SEPARATOR
                + currentTime + SapXmlConstants.XML_EXTENSION;

        upload(xmlFileName, XmlUtils.documentToString(document),"Sales");

    }

    /**
     * 
     * <AUTHOR>
     * @param dto
     * @param sumList
     * @return
     * @date 2022年6月15日
     */
    private Pair<List<CustomerOrder>, Map<String, MeansOfPayment>> subtractCancelAmount(CustomerOrderDto dto,
            List<CustomerOrder> sumList) {

        List<CustomerOrder> sumCancelList = customerOrderService.sumCancelGroupByMeansOfPaymentCode(dto);
        Map<String, CustomerOrder> sumCancelMap = sumCancelList.stream()
                .collect(Collectors.toMap(CustomerOrder::getMeansOfPaymentCode, v -> v));

        List<String> meansOfPaymentCodeList = new ArrayList<>(sumList.size());
        List<CustomerOrder> mopSumList = new ArrayList<>(sumList.size());
        for (CustomerOrder customerOrder : sumList) {
            CustomerOrder cancel = sumCancelMap.get(customerOrder.getMeansOfPaymentCode());
            if (cancel != null) {
                customerOrder.setAmount(customerOrder.getAmount().subtract(cancel.getAmount()));
                if (BigDecimal.ZERO.compareTo(customerOrder.getAmount()) == 0) {
                    continue;
                }
            }
            meansOfPaymentCodeList.add(customerOrder.getMeansOfPaymentCode());
            mopSumList.add(customerOrder);
        }
        
        Map<String, MeansOfPayment> mopMap = meansOfPaymentService.queryByCodeList(meansOfPaymentCodeList);
        return Pair.of(mopSumList, mopMap);
    }

    /**
     * 
     * <AUTHOR>
     * @param request
     * @param outlet
     * @param dto
     * @throws ParserConfigurationException
     * @date 2022年6月14日
     */
    private void sumGroupByArticleXml(SapSalesPostingXmlRequest request, Outlet outlet, CustomerOrderDto dto)
            throws ParserConfigurationException {

        List<SumCustomerOrderGroupByArticle> sumList = customerOrderService.sumCustomerOrderGroupByArticle(dto);
        if (CollectionUtils.isEmpty(sumList)) {
            log.info("sumGroupByArticleXml sumList is empty, outletCode={}", request.getOutletCode());
            return;
        }

        List<SumCustomerOrderGroupByArticle> sumCancelList = customerOrderService.sumCancelCustomerOrderGroupByArticle(dto);
        Map<String, SumCustomerOrderGroupByArticle> sumCancelMap = sumCancelList.stream()
                .collect(Collectors.toMap(SumCustomerOrderGroupByArticle::getArticleCode, v -> v));
        try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        String nowDate = DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        String currentDate = nowDate.substring(0, 8);
        String currentTime = nowDate.substring(8, 14);
        String queryDate = DateUtil.format(dto.getReleaseTimeStart(), DateUtil.FORMAT_YYYYMMDD);

        org.w3c.dom.Document document = XmlUtils.newDocument();

        String docrel = "620";
        String idoctyp = "WPUUMS01";
        String mestyp = WPUUMS;

        org.w3c.dom.Element idoc = createIdoc(outlet, currentDate, currentTime, document, docrel, idoctyp, mestyp);

        org.w3c.dom.Element e1wpu01 = XmlUtils.createElement(document, "E1WPU01");
        e1wpu01.setAttribute(SapXmlConstants.SEGMENT_NAME, SapXmlConstants.SEGMENT_VALUE);
        idoc.appendChild(e1wpu01);
        e1wpu01.appendChild(XmlUtils.createElement(document, "BELEGDATUM", queryDate));
        e1wpu01.appendChild(XmlUtils.createElement(document, BELEGWAERS, "IDR"));

        DecimalFormat df = new DecimalFormat("0.00##");

        int i = 0;
        for (SumCustomerOrderGroupByArticle sumArticle : sumList) {

            SumCustomerOrderGroupByArticle sumCancel = sumCancelMap.get(sumArticle.getArticleCode());
            if (sumCancel != null) {
                sumArticle.setVoucherNum(sumArticle.getVoucherNum() - sumCancel.getVoucherNum());
                sumArticle.setVoucherAmount(sumArticle.getVoucherAmount().subtract(sumCancel.getVoucherAmount()));
                sumArticle.setDiscount(sumArticle.getDiscount().subtract(sumCancel.getDiscount()));
                sumArticle.setAmount(sumArticle.getAmount().subtract(sumCancel.getAmount()));
                if (sumArticle.getVoucherNum() == 0) {
                    continue;
                }
            }

            org.w3c.dom.Element e1wpu02 = XmlUtils.createElement(document, "E1WPU02");
            e1wpu02.setAttribute(SapXmlConstants.SEGMENT_NAME, SapXmlConstants.SEGMENT_VALUE);
            e1wpu01.appendChild(e1wpu02);

            e1wpu02.appendChild(XmlUtils.createElement(document, ARTNR, sumArticle.getArticleCode()));
            e1wpu02.appendChild(XmlUtils.createElement(document, "VORZMENGE", "-"));
            e1wpu02.appendChild(XmlUtils.createElement(document, "UMSMENGE", sumArticle.getVoucherNum().toString()));
            e1wpu02.appendChild(XmlUtils.createElement(document, "UMSWERT", df.format(sumArticle.getVoucherAmount())));
            if (sumArticle.getDiscount().compareTo(BigDecimal.ZERO) > 0) {
                org.w3c.dom.Element e1wpu03 = XmlUtils.createElement(document, "E1WPU03");
                e1wpu03.setAttribute(SapXmlConstants.SEGMENT_NAME, SapXmlConstants.SEGMENT_VALUE);
                e1wpu02.appendChild(e1wpu03);

                e1wpu03.appendChild(XmlUtils.createElement(document, VORZEICHEN, "-"));
                e1wpu03.appendChild(XmlUtils.createElement(document, "KONDITION", "ZDIS"));
                e1wpu03.appendChild(XmlUtils.createElement(document, "KONDVALUE", df.format(sumArticle.getDiscount())));
            }

            i++;
        }

        if (i == 0) {
            log.info("sumGroupByArticleXml subtractCancel sumList is empty, outletCode={}", request.getOutletCode());
            return;
        }

        String xmlFileName = "WPUUMS_" + outlet.getBusinessOutletCode() + SapXmlConstants.SEPARATOR + queryDate
                + SapXmlConstants.SEPARATOR + i + SapXmlConstants.SEPARATOR + currentDate + SapXmlConstants.SEPARATOR
                + currentTime + SapXmlConstants.XML_EXTENSION;
        upload(xmlFileName, XmlUtils.documentToString(document),"Sales");
    }

    /**
     * 
     * <AUTHOR>
     * @param outlet
     * @param currentDate
     * @param currentTime
     * @param document
     * @param docrel
     * @param idoctyp
     * @param mestyp
     * @return
     * @date 2022年6月14日
     */
    private org.w3c.dom.Element createIdoc(Outlet outlet, String currentDate, String currentTime,
            org.w3c.dom.Document document, String docrel, String idoctyp, String mestyp) {

        org.w3c.dom.Element root = XmlUtils.createElement(document, idoctyp);
        document.appendChild(root);

        org.w3c.dom.Element idoc = XmlUtils.createElement(document, "IDOC");
        idoc.setAttribute("BEGIN", "1");

        org.w3c.dom.Element edidc40 = XmlUtils.createElement(document, "EDI_DC40");
        edidc40.setAttribute(SapXmlConstants.SEGMENT_NAME, SapXmlConstants.SEGMENT_VALUE);
        edidc40.appendChild(XmlUtils.createElement(document, "TABNAM", "EDI_DC40"));
        edidc40.appendChild(XmlUtils.createElement(document, "MANDT", "999"));
        edidc40.appendChild(XmlUtils.createElement(document, "DOCREL", docrel));
        edidc40.appendChild(XmlUtils.createElement(document, "DIRECT", "2"));
        edidc40.appendChild(XmlUtils.createElement(document, "IDOCTYP", idoctyp));
        edidc40.appendChild(XmlUtils.createElement(document, "MESTYP", mestyp));
        edidc40.appendChild(XmlUtils.createElement(document, "SNDPOR", "XML_PORT"));
        edidc40.appendChild(XmlUtils.createElement(document, "SNDPRT", "KU"));
        edidc40.appendChild(XmlUtils.createElement(document, "SNDPRN", outlet.getBusinessOutletCode()));
        edidc40.appendChild(XmlUtils.createElement(document, "RCVPOR", "SAPMGP"));
        edidc40.appendChild(XmlUtils.createElement(document, "RCVPRT", "LS"));
        edidc40.appendChild(XmlUtils.createElement(document, "RCVPRN", "MGPCLNT999"));
        edidc40.appendChild(XmlUtils.createElement(document, "CREDAT", currentDate));
        edidc40.appendChild(XmlUtils.createElement(document, "CRETIM", currentTime));
        edidc40.appendChild(XmlUtils.createElement(document, "ARCKEY", outlet.getBusinessOutletCode() + currentDate));

        idoc.appendChild(edidc40);

        root.appendChild(idoc);
        return idoc;
    }



    public Result<Void> mv03Xml(SapSalesPostingXmlRequest request) {

        if (StringUtil.isBlank(request.getOutletCode()))
            request.setOutletCode(mv03);

        getVoidResult(request, mv03);
        return Result.ok();
    }

    private Result<Void> getVoidResult(SapSalesPostingXmlRequest request,String mvStore) {
        Outlet outlet = outletService.queryByOutletCode(mvStore);
        if (outlet == null) {
            log.info("customerOrderXml outlet is null, outletCode={}", mvStore);
            return Result.failed(ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.code(),
                    ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.desc());
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        if (request.getQueryDate() == null) {
            calendar.add(Calendar.DAY_OF_YEAR, -1);
        } else {
            calendar.setTime(request.getQueryDate());
        }
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        Date queryDateTimeStart = calendar.getTime();
        calendar.add(Calendar.DAY_OF_YEAR, 1);
        Date queryDateTimeEnd = calendar.getTime();
        SalesDataDto dto = new SalesDataDto();
        dto.setOutletCode(mvStore);
        dto.setTransactionType(TransactionTypeEnum.GIFT_CARD_ACTIVATE.getCode());
        dto.setQueryDateTimeStart(queryDateTimeStart);
        dto.setQueryDateTimeEnd(queryDateTimeEnd);

        if (outlet.getOutletCode().equals(mv01)){
            dto.setMvStore("MV01");
        }else if (outlet.getOutletCode().equals(mv03)){
            dto.setMvStore("MV03");
        }else if (outlet.getOutletCode().equals(mv04)){
            dto.setMvStore("MV04");
        }

        try {
            
            if (dto.getMvStore().equals("MV04")){
                xmlNoMop( outlet, dto);
                xmlMop( GvcoreConstants.MOP_CODE_VCE, outlet, dto);
                try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
                xmlMop( GvcoreConstants.MOP_CODE_VCR, outlet, dto);

            }else {
                xml( "", outlet, dto);
            }

        } catch (ParserConfigurationException e) {
            log.error(mvStore + " customerOrderXml newDocument Exception=" + e.getMessage(), e);
        }
        return null;
    }

    private void xml( String mop, Outlet outlet, SalesDataDto dto) throws ParserConfigurationException {
        xmlMop(mop, outlet, dto);
        xmlNoMop(outlet, dto);
    }

    private void xmlNoMop(Outlet outlet, SalesDataDto dto) throws ParserConfigurationException {
        //追加
        //outlet
        dto.setXmlType(WPUUMS);
        dto.setMopCode("");
        wpuums(outlet, dto,WPUUMS);
    }

    private void xmlMop(String mop, Outlet outlet, SalesDataDto dto) throws ParserConfigurationException {
        //总计
        //pos
        dto.setXmlType(WPUBON_C);
        wpubon01(outlet, dto,WPUBON_C, mop);
        //拆分多个xml
        //财务
        dto.setXmlType(WPUBON);
        wpubon01(outlet, dto,WPUBON, mop);
    }


    private List<SalesDataResultDto> querySalesData(SalesDataDto dto,String mopCode){
        if(StringUtil.isNotEmpty(mopCode)){
            dto.setMopCode(mopCode);
        }
        return transactionDataService.querySalesData(dto);
    }



    private void wpubon01( Outlet outlet, SalesDataDto dto, String type, String mop)
            throws ParserConfigurationException {
        List<SalesDataResultDto> transactionData = null;

        transactionData = querySalesData(dto,mop);


        if (CollectionUtils.isEmpty(transactionData)) {
            log.info("sumTransactionXml transactionData is empty, outletCode={}", dto.getOutletCode());
            return;
        }
        for (SalesDataResultDto transactionDatum : transactionData) {

            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            String queryDate = DateUtil.format(dto.getQueryDateTimeStart(), DateUtil.FORMAT_YYYYMMDD);
            String nowDate = DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14);
            String currentDate = nowDate.substring(0, 8);
            String currentTime = nowDate.substring(8, 14);
            org.w3c.dom.Document document = XmlUtils.newDocument();

            String docrel = "701";
            String idoctyp = "WPUBON01";
            String mestyp = WPUBON;


            org.w3c.dom.Element idoc = createIdoc(outlet, currentDate, currentTime, document, docrel, idoctyp, mestyp);

            org.w3c.dom.Element e1wpb01 = XmlUtils.createElement(document, E_1_WPB_01);
            e1wpb01.setAttribute(SapXmlConstants.SEGMENT_NAME, SapXmlConstants.SEGMENT_VALUE);
            e1wpb01.appendChild(XmlUtils.createElement(document, VORGDATUM, String.valueOf(Integer.valueOf(queryDate))));


            if (type.equals(WPUBON_C)) {
                e1wpb01.appendChild(XmlUtils.createElement(document, BONNUMMER, "POS CLEARING"));

            } else if (type.equals(WPUBON)) {

                String bonnummer = StringUtil.EMPTY;




                if (StringUtil.isNotEmpty(transactionDatum.getMopName())) {
                    bonnummer = transactionDatum.getMopName();
                    if (!bonnummer.contains("_")){
                        bonnummer = transactionDatum.getMopName();
                    }else {
                        int index = bonnummer.indexOf('_');
                        if (index >= 0) {
                            bonnummer = bonnummer.substring(index + 1);
                        }
                    }
                }
                if (StringUtil.isEmpty(bonnummer)){
                    if (dto.getMvStore().equals("MV03")){
                        bonnummer = "ECOMV03";
                    } else if (dto.getMvStore().equals("MV04")){
                        bonnummer = "VABMV04";
                    }
                }

                e1wpb01.appendChild(XmlUtils.createElement(document, BONNUMMER, bonnummer));
                e1wpb01.appendChild(XmlUtils.createElement(document, "KUNDNR", bonnummer));
                if (dto.getMvStore().equals("MV03")) {
                    e1wpb01.appendChild(XmlUtils.createElement(document, "KASSIERER", "8C"));
                } else if (dto.getMvStore().equals("MV04")) {
                    e1wpb01.appendChild(XmlUtils.createElement(document, "KASSIERER", "8T"));
                }


            }

            e1wpb01.appendChild(XmlUtils.createElement(document, BELEGWAERS, "IDR"));

            org.w3c.dom.Element e1wpb02 = XmlUtils.createElement(document, "E1WPB02");
            e1wpb02.setAttribute(SapXmlConstants.SEGMENT_NAME, SapXmlConstants.SEGMENT_VALUE);
            e1wpb02.appendChild(XmlUtils.createElement(document, "VORGANGART", "ZMOP"));
            e1wpb02.appendChild(XmlUtils.createElement(document, "QUALARTNR", "ARTN"));
            if (type.equals(WPUBON_C)) {
                e1wpb02.appendChild(XmlUtils.createElement(document, ARTNR, "POS_CLEARING"));

            } else if (type.equals(WPUBON)) {
                e1wpb02.appendChild(XmlUtils.createElement(document, ARTNR, "MEANS_OF_PAYMENT"));
            }
            e1wpb01.appendChild(e1wpb02);


            org.w3c.dom.Element e1wpb06 = XmlUtils.createElement(document, E_1_WPB_06);
            e1wpb06.setAttribute(SapXmlConstants.SEGMENT_NAME, SapXmlConstants.SEGMENT_VALUE);
            if (type.equals(WPUBON_C)) {
                e1wpb06.appendChild(XmlUtils.createElement(document, ZAHLART, "ZPOS"));

            } else if (type.equals(WPUBON)) {
                e1wpb06.appendChild(XmlUtils.createElement(document, VORZEICHEN, "-"));
                e1wpb06.appendChild(XmlUtils.createElement(document, ZAHLART, "ZMOP"));
            }
            DecimalFormat df = new DecimalFormat("0.00##");

            String formatAmount = df.format(transactionData.stream()
                    .map(SalesDataResultDto::getVoucherAmount).reduce(BigDecimal.ZERO, BigDecimal::add).subtract(transactionData.stream()
                            .map(SalesDataResultDto::getDiscount).reduce(BigDecimal.ZERO, BigDecimal::add)));
            int i = 1;

            if (type.equals(WPUBON_C)) {

                e1wpb06.appendChild(XmlUtils.createElement(document, SUMME, formatAmount));
                e1wpb01.appendChild(e1wpb06);
                idoc.appendChild(e1wpb01);

                String xmlFileName = type + SapXmlConstants.SEPARATOR + outlet.getBusinessOutletCode() + SapXmlConstants.SEPARATOR + currentDate
                        + SapXmlConstants.SEPARATOR + i + SapXmlConstants.SEPARATOR + currentDate
                        + SapXmlConstants.SEPARATOR + currentTime + SapXmlConstants.XML_EXTENSION;
                upload(xmlFileName, XmlUtils.documentToString(document),"Sales");
                break;

            } else if (type.equals(WPUBON)) {

                e1wpb06.appendChild(XmlUtils.createElement(document, SUMME, df.format(transactionDatum.getVoucherAmount().subtract(transactionDatum.getDiscount()))));
                e1wpb01.appendChild(e1wpb06);
                idoc.appendChild(e1wpb01);


                String xmlFileName = type + SapXmlConstants.SEPARATOR + outlet.getBusinessOutletCode() + SapXmlConstants.SEPARATOR + currentDate
                        + SapXmlConstants.SEPARATOR + i + SapXmlConstants.SEPARATOR + currentDate
                        + SapXmlConstants.SEPARATOR + currentTime + Optional.ofNullable(mop).orElse("")+ SapXmlConstants.XML_EXTENSION;
                upload(xmlFileName, XmlUtils.documentToString(document),"Sales");
                i++;
            }
        }


    }

    private void wpuums(Outlet outlet, SalesDataDto dto , String type)
            throws ParserConfigurationException {
        List<SalesDataResultDto> transactionData = querySalesData(dto,"");
        if (CollectionUtils.isEmpty(transactionData)) {
            log.info("sumTransactionXml transactionData is empty, outletCode={}", dto.getOutletCode());
            return;
        }
        Map<String, SalesDataResultDto> sumSalesMap = transactionData.stream()
                .collect(Collectors.toMap(SalesDataResultDto::getArticleCode, v -> v));
        try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        String queryDate = DateUtil.format(dto.getQueryDateTimeStart(), DateUtil.FORMAT_YYYYMMDD);
        String nowDate = DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        String currentDate = nowDate.substring(0, 8);
        String currentTime = nowDate.substring(8, 14);
        org.w3c.dom.Document document = XmlUtils.newDocument();

        String docrel = "620";
        String idoctyp = "WPUUMS01";
        String mestyp = WPUUMS;

        org.w3c.dom.Element idoc = createIdoc(outlet, currentDate, currentTime, document, docrel, idoctyp, mestyp);
        org.w3c.dom.Element e1wpu01 = XmlUtils.createElement(document, "E1WPU01");
        e1wpu01.setAttribute(SapXmlConstants.SEGMENT_NAME, SapXmlConstants.SEGMENT_VALUE);
        e1wpu01.appendChild(XmlUtils.createElement(document, "BELEGDATUM", String.valueOf(Integer.valueOf(queryDate))));
        e1wpu01.appendChild(XmlUtils.createElement(document, BELEGWAERS, "IDR"));
        e1wpu01.appendChild(XmlUtils.createElement(document, "PACKAGE_ID", "03"));


        DecimalFormat df = new DecimalFormat("0.00##");
        int i=1;
        for (SalesDataResultDto dataResult : transactionData) {
            SalesDataResultDto salesData = sumSalesMap.get(dataResult.getArticleCode());


            org.w3c.dom.Element e1wpu02 = XmlUtils.createElement(document, "E1WPU02");
            e1wpu02.setAttribute(SapXmlConstants.SEGMENT_NAME, SapXmlConstants.SEGMENT_VALUE);
            e1wpu02.appendChild(XmlUtils.createElement(document, ARTNR, salesData.getArticleCode()));
            e1wpu02.appendChild(XmlUtils.createElement(document, "VORZMENGE", "-"));
            e1wpu02.appendChild(XmlUtils.createElement(document, "UMSMENGE", String.valueOf(salesData.getVoucherNum())));
            e1wpu02.appendChild(XmlUtils.createElement(document, "UMSWERT", df.format(salesData.getVoucherAmount())));
            if (salesData.getDiscount().compareTo(BigDecimal.ZERO) > 0) {
                org.w3c.dom.Element e1wpu03 = XmlUtils.createElement(document, "E1WPU03");
                e1wpu03.setAttribute(SapXmlConstants.SEGMENT_NAME, SapXmlConstants.SEGMENT_VALUE);
                e1wpu02.appendChild(e1wpu03);

                e1wpu03.appendChild(XmlUtils.createElement(document, VORZEICHEN, "-"));
                e1wpu03.appendChild(XmlUtils.createElement(document, "KONDITION", "ZDIS"));
                e1wpu03.appendChild(XmlUtils.createElement(document, "KONDVALUE", df.format(salesData.getDiscount())));
            }
            e1wpu01.appendChild(e1wpu02);

            idoc.appendChild(e1wpu01);



        }
        String xmlFileName = type +SapXmlConstants.SEPARATOR  + outlet.getBusinessOutletCode() + SapXmlConstants.SEPARATOR + currentDate
                + SapXmlConstants.SEPARATOR + i + SapXmlConstants.SEPARATOR + currentDate
                + SapXmlConstants.SEPARATOR + currentTime + SapXmlConstants.XML_EXTENSION;

        upload(xmlFileName, XmlUtils.documentToString(document),"Sales");

    }


    public Result<Void> mv04Xml(SapSalesPostingXmlRequest request) {
        if (StringUtil.isBlank(request.getOutletCode()))
            request.setOutletCode(mv04);
        getVoidResult(request, mv04);
        return Result.ok();
    }
}
