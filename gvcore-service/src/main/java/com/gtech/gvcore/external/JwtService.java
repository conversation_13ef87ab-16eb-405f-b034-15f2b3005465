package com.gtech.gvcore.external;

import com.alibaba.fastjson.JSON;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.TokenExpiredException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.gtech.basic.masterdata.web.entity.MasterDataValueEntity;
import com.gtech.basic.masterdata.web.service.MasterDataValueService;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.GvPosCommonResponseCodesEnum;
import com.gtech.gvcore.common.exception.GvcoreUnknownException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
@Slf4j
public class JwtService {

	public static final String ISSUER = "auth0";
	public static final String CLAIM_APP = "GV";

	@Value("${gvcore.jwt.token.secret:1qaz2wsx}")
	private String jwtSecret;

	@Value("${gvcore.jwt.effective.hours:1440}")
	private Integer effectiveHours;

	@Autowired
	private MasterDataValueService masterDataValueService;

	public String create(Object object) {
		try {
			Algorithm algorithm = Algorithm.HMAC256(jwtSecret);
			MasterDataValueEntity masterDataValueEntity = new MasterDataValueEntity();
			masterDataValueEntity.setValueCode("TOKEN_EFFECTIVE");
			masterDataValueEntity.setTenantCode(GvcoreConstants.SYSTEM_DEFAULT);
			MasterDataValueEntity value = masterDataValueService.getByCode(masterDataValueEntity);
			if (value != null) {
				effectiveHours = Integer.parseInt(value.getValueValue());
			}
			long expireTimes = System.currentTimeMillis() + effectiveHours  * 60 * 1000L;
			return JWT.create().withIssuer(ISSUER).withExpiresAt(new Date(expireTimes))
					.withClaim(CLAIM_APP, JSON.toJSONString(object)).sign(algorithm);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new GvcoreUnknownException(
					String.valueOf(GvPosCommonResponseCodesEnum.SECURITY_CHECK_FAILED.getResponseCode()),
					GvPosCommonResponseCodesEnum.SECURITY_CHECK_FAILED.getResponseMessage());
		}
	}

	public Object resolve(String appToken, Class<?> cla) {
		try {
			Algorithm algorithm = Algorithm.HMAC256(jwtSecret);
			JWTVerifier verifier = JWT.require(algorithm).withIssuer(ISSUER).build();
			DecodedJWT jwt = verifier.verify(appToken);
			String claim = jwt.getClaim(CLAIM_APP).asString();
			return JSON.parseObject(claim, cla);
		} catch (TokenExpiredException e) {
			log.error(e.getMessage(), e);
			throw new GvcoreUnknownException(
					String.valueOf(GvPosCommonResponseCodesEnum.AUTHORIZATION_FAILED_10744.getResponseCode()),
					GvPosCommonResponseCodesEnum.AUTHORIZATION_FAILED_10744.getResponseMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new GvcoreUnknownException(
					String.valueOf(GvPosCommonResponseCodesEnum.AUTHORIZATION_FAILED_10744.getResponseCode()),
					GvPosCommonResponseCodesEnum.AUTHORIZATION_FAILED_10744.getResponseMessage());
		}
	}
}
