package com.gtech.gvcore.external.impl;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.dao.mapper.GCPureReportBusinessMapper;
import com.gtech.gvcore.external.GcPostingXmlService;
import com.gtech.gvcore.common.utils.SftpUtil;
import com.gtech.gvcore.service.report.impl.bo.WpubonBo;
import com.gtech.gvcore.service.report.impl.bo.WpubonCBo;
import com.gtech.gvcore.service.report.impl.bo.WpuumsBo;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GcPostingXmlServiceImpl implements GcPostingXmlService {

    @Autowired
    private GCPureReportBusinessMapper gcPureReportBusinessMapper;

    @Value("${gv.outlet.warehouse.MV01}")
    private String mv01Outlet;

    @Value("${gv.outlet.warehouse.MV03}")
    private String mv03Outlet;

    @Value("${gv.outlet.warehouse.MV04}")
    private String mv04Outlet;

    @Value("${gv.sftp.url:}")
    private String sftpUrl;

    @Value("${gv.sftp.name:}")
    private String sftpName;

    @Value("${gv.sftp.pwd:}")
    private String sftpPwd;

    @Override
    public void generateWpubonC(Date queryDate) {
        List<String> mvStoreCodes = Arrays.asList(mv01Outlet, mv03Outlet, mv04Outlet);
        List<WpubonCBo> data = gcPureReportBusinessMapper.selectWpubonCData(queryDate, mvStoreCodes);
        Map<String, List<WpubonCBo>> groupedByOutlet = data.stream().collect(Collectors.groupingBy(WpubonCBo::getOutletCode));

        for (String outletCode : mvStoreCodes) {
            String mvStoreName = getMvStoreName(outletCode);
            List<WpubonCBo> outletData = groupedByOutlet.getOrDefault(outletCode, new java.util.ArrayList<>());

            if (outletData.isEmpty()) {
                String xmlContent = buildWpubonCXml(mvStoreName,null ,queryDate, BigDecimal.ZERO, ""); // Using a default mop code
                uploadFile("WPUBON_C", mvStoreName, queryDate, "", xmlContent);

            } else {
                for (WpubonCBo bo : outletData) {
                    String xmlContent = buildWpubonCXml(mvStoreName,bo.getBusinessOutletCode() ,queryDate, bo.getTotalAmount(), bo.getMopCode());
                    uploadFile("WPUBON_C", mvStoreName, queryDate, bo.getMopCode(), xmlContent);
                }
            }
        }
    }

    @Override
    public void generateWpubon(Date queryDate) {
        List<String> mvStoreCodes = Arrays.asList(mv01Outlet, mv03Outlet, mv04Outlet);
        List<WpubonBo> data = gcPureReportBusinessMapper.selectWpubonData(queryDate, mvStoreCodes);
        Map<String, List<WpubonBo>> groupedByOutlet = data.stream().collect(Collectors.groupingBy(WpubonBo::getOutletCode));

        for (String outletCode : mvStoreCodes) {
            String mvStoreName = getMvStoreName(outletCode);
            List<WpubonBo> outletData = groupedByOutlet.getOrDefault(outletCode, new java.util.ArrayList<>());

            if (outletData.isEmpty()) {
                // No data for this outlet, generate a zero file
                String xmlContent = buildWpubonXml(mvStoreName, null,queryDate, BigDecimal.ZERO, "");
                uploadFile("WPUBON", mvStoreName, queryDate, "", xmlContent);
            } else {
                for (WpubonBo bo : outletData) {
                    String xmlContent = buildWpubonXml(mvStoreName, bo.getBusinessOutletCode(),queryDate, bo.getTotalAmount(), bo.getMopName());
                    uploadFile("WPUBON", mvStoreName, queryDate, bo.getMopName(), xmlContent);
                }
            }
        }
    }

    @Override
    public void generateWpuums(Date queryDate) {
        List<String> mvStoreCodes = Arrays.asList(mv01Outlet, mv03Outlet, mv04Outlet);
        List<WpuumsBo> data = gcPureReportBusinessMapper.selectWpuumsData(queryDate, mvStoreCodes);
        Map<String, List<WpuumsBo>> groupedByOutlet = data.stream().collect(Collectors.groupingBy(WpuumsBo::getOutletCode));

        for (String outletCode : mvStoreCodes) {
            String mvStoreName = getMvStoreName(outletCode);
            List<WpuumsBo> outletData = groupedByOutlet.getOrDefault(outletCode, new java.util.ArrayList<>());
            // Always generate a file, even if outletData is empty.
            // buildWpuumsXml can handle an empty list and will produce a file with no data segments.
            String xmlContent = buildWpuumsXml(mvStoreName, outletData.get(0).getBusinessOutletCode() , queryDate, outletData);
            uploadFile("WPUUMS", mvStoreName, queryDate, null, xmlContent);
        }
    }

    private String getMvStoreName(String outletCode) {
        if (outletCode.equals(mv01Outlet)) return "MV01";
        if (outletCode.equals(mv03Outlet)) return "MV03";
        if (outletCode.equals(mv04Outlet)) return "MV04";
        return "UNKNOWN";
    }

    private String buildWpubonCXml(String mvStoreName,String outletCode, Date queryDate, BigDecimal totalAmount, String mopCode) {
        Date now = new Date();
        String creationDate = new SimpleDateFormat("yyyyMMdd").format(now);
        String creationTime = new SimpleDateFormat("HHmmss").format(now);
        String transactionDate = new SimpleDateFormat("yyyyMMdd").format(queryDate);
        String formattedAmount = String.format("%.2f", totalAmount);

        return String.format(
                "<WPUBON01>\n" +
                "<IDOC BEGIN=\"1\">\n" +
                "<EDI_DC40 SEGMENT=\"1\">\n" +
                "<TABNAM>EDI_DC40</TABNAM>\n" +
                "<MANDT>999</MANDT>\n" +
                "<DOCREL>701</DOCREL>\n" +
                "<DIRECT>2</DIRECT>\n" +
                "<IDOCTYP>WPUBON01</IDOCTYP>\n" +
                "<MESTYP>WPUBON</MESTYP>\n" +
                "<SNDPOR>XML_PORT</SNDPOR>\n" +
                "<SNDPRT>KU</SNDPRT>\n" +
                "<SNDPRN>%s</SNDPRN>\n" +
                "<RCVPOR>SAPMGP</RCVPOR>\n" +
                "<RCVPRT>LS</RCVPRT>\n" +
                "<RCVPRN>MGPCLNT999</RCVPRN>\n" +
                "<CREDAT>%s</CREDAT>\n" +
                "<CRETIM>%s</CRETIM>\n" +
                "<ARCKEY>%s%s</ARCKEY>\n" +
                "</EDI_DC40>\n" +
                "<E1WPB01 SEGMENT=\"1\">\n" +
                "<VORGDATUM>%s</VORGDATUM>\n" +
                "<BONNUMMER>POS CLEARING</BONNUMMER>\n" +
                "<BELEGWAERS>IDR</BELEGWAERS>\n" +
                "<E1WPB02 SEGMENT=\"1\">\n" +
                "<VORGANGART>ZMOP</VORGANGART>\n" +
                "<QUALARTNR>ARTN</QUALARTNR>\n" +
                "<ARTNR>POS_CLEARING</ARTNR>\n" +
                "</E1WPB02>\n" +
                "<E1WPB06 SEGMENT=\"1\">\n" +
                "<ZAHLART>ZPOS</ZAHLART>\n" +
                "<SUMME>%s</SUMME>\n" +
                "</E1WPB06>\n" +
                "</E1WPB01>\n" +
                "</IDOC>\n" +
                "</WPUBON01>",
                mvStoreName, creationDate, creationTime, outletCode, transactionDate, transactionDate, formattedAmount
        );
    }

    private String buildWpubonXml(String mvStoreName,String businessOutletCode, Date queryDate, BigDecimal totalAmount, String mopCode) {
        Date now = new Date();
        String creationDate = new SimpleDateFormat("yyyyMMdd").format(now);
        String creationTime = new SimpleDateFormat("HHmmss").format(now);
        String transactionDate = new SimpleDateFormat("yyyyMMdd").format(queryDate);
        String formattedAmount = String.format("%.2f", totalAmount);

        return String.format(
                "<WPUBON01>\n" +
                "<IDOC BEGIN=\"1\">\n" +
                "<EDI_DC40 SEGMENT=\"1\">\n" +
                "<TABNAM>EDI_DC40</TABNAM>\n" +
                "<MANDT>999</MANDT>\n" +
                "<DOCREL>701</DOCREL>\n" +
                "<DIRECT>2</DIRECT>\n" +
                "<IDOCTYP>WPUBON01</IDOCTYP>\n" +
                "<MESTYP>WPUBON</MESTYP>\n" +
                "<SNDPOR>XML_PORT</SNDPOR>\n" +
                "<SNDPRT>KU</SNDPRT>\n" +
                "<SNDPRN>%s</SNDPRN>\n" +
                "<RCVPOR>SAPMGP</RCVPOR>\n" +
                "<RCVPRT>LS</RCVPRT>\n" +
                "<RCVPRN>MGPCLNT999</RCVPRN>\n" +
                "<CREDAT>%s</CREDAT>\n" +
                "<CRETIM>%s</CRETIM>\n" +
                "<ARCKEY>%s%s</ARCKEY>\n" +
                "</EDI_DC40>\n" +
                "<E1WPB01 SEGMENT=\"1\">\n" +
                "<VORGDATUM>%s</VORGDATUM>\n" +
                "<BONNUMMER>%s</BONNUMMER>\n" +
                "<KUNDNR>%s</KUNDNR>\n" +
                "<BELEGWAERS>IDR</BELEGWAERS>\n" +
                "<E1WPB02 SEGMENT=\"1\">\n" +
                "<VORGANGART>ZMOP</VORGANGART>\n" +
                "<QUALARTNR>ARTN</QUALARTNR>\n" +
                "<ARTNR>MEANS_OF_PAYMENT</ARTNR>\n" +
                "</E1WPB02>\n" +
                "<E1WPB06 SEGMENT=\"1\">\n" +
                "<VORZEICHEN>-</VORZEICHEN>\n" +
                "<ZAHLART>ZMOP</ZAHLART>\n" +
                "<SUMME>%s</SUMME>\n" +
                "</E1WPB06>\n" +
                "</E1WPB01>\n" +
                "</IDOC>\n" +
                "</WPUBON01>",
                mvStoreName, creationDate, creationTime, businessOutletCode, transactionDate, transactionDate, mopCode == null ? "DEFAULT" : mopCode, mopCode == null ? "DEFAULT" : mopCode, formattedAmount
        );
    }

    private String buildWpuumsXml(String mvStoreName,String  businessOutletCode, Date queryDate, List<WpuumsBo> data) {
        Date now = new Date();
        String creationDate = new SimpleDateFormat("yyyyMMdd").format(now);
        String creationTime = new SimpleDateFormat("HHmmss").format(now);
        String transactionDate = new SimpleDateFormat("yyyyMMdd").format(queryDate);

        StringBuilder sb = new StringBuilder();
        sb.append("<WPUUMS01>\n");
        sb.append("<IDOC BEGIN=\"1\">\n");
        sb.append("<EDI_DC40 SEGMENT=\"1\">\n");
        sb.append("<TABNAM>EDI_DC40</TABNAM>\n");
        sb.append("<MANDT>999</MANDT>\n");
        sb.append("<DOCREL>620</DOCREL>\n");
        sb.append("<DIRECT>2</DIRECT>\n");
        sb.append("<IDOCTYP>WPUUMS01</IDOCTYP>\n");
        sb.append("<MESTYP>WPUUMS</MESTYP>\n");
        sb.append("<SNDPOR>XML_PORT</SNDPOR>\n");
        sb.append("<SNDPRT>KU</SNDPRT>\n");
        sb.append(String.format("<SNDPRN>%s</SNDPRN>\n", mvStoreName));
        sb.append("<RCVPOR>SAPMGP</RCVPOR>\n");
        sb.append("<RCVPRT>LS</RCVPRT>\n");
        sb.append("<RCVPRN>MGPCLNT999</RCVPRN>\n");
        sb.append(String.format("<CREDAT>%s</CREDAT>\n", creationDate));
        sb.append(String.format("<CRETIM>%s</CRETIM>\n", creationTime));
        sb.append(String.format("<ARCKEY>%s%s</ARCKEY>\n", businessOutletCode, transactionDate));
        sb.append("</EDI_DC40>\n");

        sb.append("<E1WPU01 SEGMENT=\"1\">\n");
        sb.append(String.format("<BELEGDATUM>%s</BELEGDATUM>\n", transactionDate));
        sb.append("<BELEGWAERS>IDR</BELEGWAERS>\n");

        for (WpuumsBo bo : data) {
            sb.append("<E1WPU02 SEGMENT=\"1\">\n");
            sb.append(String.format("<ARTNR>%s</ARTNR>\n", bo.getArticleCode()));
            sb.append("<VORZMENGE>-</VORZMENGE>\n");
            sb.append(String.format("<UMSMENGE>%d</UMSMENGE>\n", bo.getQuantity()));
            sb.append(String.format("<UMSWERT>%.2f</UMSWERT>\n", bo.getTotalAmount()));

            if (bo.getDiscountAmount() != null && bo.getDiscountAmount().compareTo(BigDecimal.ZERO) > 0) {
                sb.append("<E1WPU03 SEGMENT=\"1\">\n");
                sb.append("<VORZEICHEN>-</VORZEICHEN>\n");
                sb.append("<KONDITION>ZDIS</KONDITION>\n");
                sb.append(String.format("<KONDVALUE>%.2f</KONDVALUE>\n", bo.getDiscountAmount()));
                sb.append("</E1WPU03>\n");
            }
            sb.append("</E1WPU02>\n");
        }

        sb.append("</E1WPU01>\n");
        sb.append("</IDOC>\n");
        sb.append("</WPUUMS01>");

        return sb.toString();
    }

    private void uploadFile(String fileType, String mvStoreName, Date queryDate, String mopName, String content) {
        String dateStr = DateUtil.format(queryDate, "yyyyMMdd");
        String fileName = fileType + "_" + mvStoreName + "_" + dateStr;
        if (mopName != null && !mopName.isEmpty()) {
            fileName += "_" + mopName;
        }
        fileName += ".xml";

        uploadXml(content, fileName);

        // 2. Upload to SFTP
        try {
            URL url = new URL(sftpUrl + "/GC");
            SftpUtil sftpUtil = new SftpUtil(sftpName, sftpPwd, url.getHost(), url.getPort(), null, SftpUtil.AUTH_TYPE_PAD);
            sftpUtil.connect();
            try (InputStream inputStream = org.apache.commons.io.IOUtils.toInputStream(content, StandardCharsets.UTF_8)) {
                sftpUtil.upload(url.getPath(), fileName, inputStream);
                log.info("Successfully uploaded GC XML file to SFTP: {}", fileName);
            } finally {
                sftpUtil.disconnect();
            }
        } catch (Exception e) {
            log.error("Error uploading GC XML file to SFTP: {}", fileName, e);
        }
    }

    @SneakyThrows
    public static void uploadXml( String xmlContent,String xmlName) {
        // 拼接完整的文件路径
        String path = System.getProperty("user.dir") + "/gvcore-web/src/main/resources/sftp/" + xmlName;

        // 创建文件对象
        Path file = Paths.get(path);

        // 将字符串内容写入到文件
        try {
            // 创建父目录，如果不存在
            Files.createDirectories(file.getParent());
            Files.write(file, xmlContent.getBytes());
            log.info("XML file uploaded successfully.");
        } catch (IOException e) {
            log.error("Error uploading XML file: " + e.getMessage());
        }
    }
} 