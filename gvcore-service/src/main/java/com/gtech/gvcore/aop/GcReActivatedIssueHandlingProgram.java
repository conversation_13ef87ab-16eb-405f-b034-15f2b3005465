package com.gtech.gvcore.aop;

import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.common.request.issuehandling.GetIssueHandlingRequest;
import com.gtech.gvcore.common.request.outlet.GetOutletRequest;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.giftcard.application.service.GiftCardApplicationService;
import com.gtech.gvcore.giftcard.infrastructure.entity.GcUnBlockEntity;
import com.gtech.gvcore.giftcard.infrastructure.entity.GiftCardEntity;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GcUnblockMapper;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.IssueHandlingService;
import com.gtech.gvcore.service.OutletService;
import com.gtech.gvcore.threadpool.ThreadPoolCenter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.gtech.gvcore.aop.IssueHandlerTransactionAspect.summarizeCompletedIssue;

@Component
public class GcReActivatedIssueHandlingProgram implements IssueHandlerTransactionAspect.IssueHandlingTransactionDataProgram {

    @Autowired
    GiftCardApplicationService gcService;

    @Autowired
    private GcUnblockMapper gcUnblockMapper;
    @Autowired
    private OutletService outletService;
    @Autowired
    private GvCodeHelper gvCodeHelper;
    @Autowired
    private IssueHandlingService issueHandlingService;

    @Override
    public IssueHandlingTypeEnum getIssueHandlingType() {
        return IssueHandlingTypeEnum.GC_BULK_REACTIVATE;
    }

    @Override
    public void execute(final Collection<IssueHandlingDetails> issueHandlingDetailsList) {
        List<String> cardNumbers = summarizeCompletedIssue(issueHandlingDetailsList);
        List<GiftCardEntity> giftCardEntities = this.gcService.queryByCardNumberList(null, cardNumbers);
        int manageSize = 500;
        List<IssueHandlingDetails> list = (List<IssueHandlingDetails>) issueHandlingDetailsList;
        final Map<String, GiftCardEntity> giftCardEntityMap = giftCardEntities
                .stream().collect(Collectors.toMap(GiftCardEntity::getCardNumber, Function.identity(), (v1, v2) -> v2));
        if (issueHandlingDetailsList.size() > manageSize) {
            int count = issueHandlingDetailsList.size() / manageSize + (issueHandlingDetailsList.size() % manageSize == 0 ? 0 : 1);
            List<IssueHandlingDetails> issueHandlingDetails = new ArrayList<>(issueHandlingDetailsList);
            for (int i = 0; i < count; i++) {

                int fromIndex = i * manageSize;
                int toIndex = (i + 1) * manageSize;
                List<IssueHandlingDetails> saveData = issueHandlingDetails.subList(fromIndex, Math.min(toIndex, issueHandlingDetails.size()));

                ThreadPoolCenter.commonThreadPoolExecute(() -> storeReactivated(new ArrayList<>(saveData), giftCardEntityMap));
            }
        } else {
            storeReactivated(list, giftCardEntityMap);
        }
    }

    public void storeReactivated(List<IssueHandlingDetails> details, final Map<String, GiftCardEntity> giftCardEntityMap) {
        Date createTime = new Date();
        String invoiceNumber = gvCodeHelper.generateInvoiceNumber();
        List<GcUnBlockEntity> collect = new ArrayList<>();
        details.forEach(x -> {
            GiftCardEntity giftCard = giftCardEntityMap.get(x.getVoucherCode());
            if (giftCard == null) {
                return;
            }
            GcUnBlockEntity entity = new GcUnBlockEntity();
            entity.setIssuerCode(giftCard.getIssuerCode());
            entity.setCardNumber(x.getVoucherCode());
            entity.setCreateTime(createTime);
            entity.setUpdateTime(createTime);
            entity.setDenomination(giftCard.getDenomination());
            GetOutletRequest getOutletRequest = new GetOutletRequest();
            getOutletRequest.setOutletCode(giftCard.getSalesOutlet());
            OutletResponse outlet = outletService.getOutlet(getOutletRequest);
            if (null != outlet) {
                entity.setOutletCode(outlet.getOutletCode());
                entity.setMerchantCode(outlet.getMerchantCode());
            }
            entity.setCpgCode(giftCard.getCpgCode());
            entity.setInvoiceNumber(invoiceNumber);
            entity.setUnblockReason((issueHandlingService.getIssueHandling(GetIssueHandlingRequest.builder()
                            .issueHandlingCode(x.getIssueHandlingCode()).build())
                    .getData().getRemarks()));
            entity.setUnblockTime(createTime);
            entity.setUpdateTime(createTime);
            entity.setOwnerCustomer(giftCard.getOwnerCustomer());
            collect.add(entity);
        });
        if (!collect.isEmpty()) {
            this.gcUnblockMapper.insertList(collect);
        }
    }
}