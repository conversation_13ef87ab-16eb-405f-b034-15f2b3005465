package com.gtech.gvcore.cache;

import com.alibaba.fastjson.JSONArray;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.request.cpg.GetCpgRequest;
import com.gtech.gvcore.common.request.merchant.GetMerchantRequest;
import com.gtech.gvcore.common.request.outlet.GetOutletRequest;
import com.gtech.gvcore.common.request.pos.GetPosRequest;
import com.gtech.gvcore.common.response.cpg.GetCpgResponse;
import com.gtech.gvcore.common.response.merchant.MerchantResponse;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.common.response.outletcpg.OutletCpgResponse;
import com.gtech.gvcore.common.response.pos.PosCpgResponse;
import com.gtech.gvcore.common.response.pos.PosResponse;
import com.gtech.gvcore.dao.model.*;
import com.gtech.gvcore.giftcard.masterdata.gcpg.dto.GcCpgDTO;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.giftcard.masterdata.gcpg.service.GcCpgService;
import com.gtech.gvcore.helper.GvRedisTemplate;
import com.gtech.gvcore.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@Component
public class MasterDataCache {

    //private static final long CACHE_EXPIRE_TIME = 3600000L; // 1小时过期
    //private static final long OUTLET_CPG_CACHE_EXPIRE_TIME = 1800000L; // 30分钟过期
    //private static final long LOCAL_CACHE_EXPIRE_TIME = 300000L; // 5分钟过期

    @Value("${local.cache.expire.time:3600000}")
    private Long CACHE_EXPIRE_TIME;

    @Value("${local.cache.expire.time:300000}")
    private Long OUTLET_CPG_CACHE_EXPIRE_TIME;


    @Value("${local.cache.expire.time:300000}")
    private Long LOCAL_CACHE_EXPIRE_TIME;

    @Autowired
    private PosCpgService posCpgService;

    @Autowired
    private OutletService outletService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private PosService posService;

    @Autowired
    private CpgService cpgService;


    @Lazy
    @Autowired
    private GcCpgService gcCpgService;

    @Autowired
    private OutletCpgService outletCpgService;

    @Autowired
    private ArticleMopService articleMopService;


    @Autowired
    private GcArticleMopService gcArticleMopService;

    @Autowired
    private GvRedisTemplate redisTemplate;

    // 缓存实例
    private CacheTemplate<String, Pos, PosResponse> posCache;
    private CacheTemplate<String, Cpg, GetCpgResponse> cpgCache;
    private CacheTemplate<String, GcCpgDTO, GcCpgDTO> gcCpgCache;
    private CacheTemplate<String, GetCpgResponse, GetCpgResponse> cpgResCache;
    private CacheTemplate<String, Outlet, OutletResponse> outletCache;
    private CacheTemplate<String, Merchant, MerchantResponse> merchantCache;
    private CacheTemplate<String, List<PosCpg>, List<PosCpgResponse>> posCpgCache;
    private CacheTemplate<String, List<OutletCpg>, List<OutletCpgResponse>> outletCpgCache;
    private CacheTemplate<String, ArticleMop, ArticleMop> articleCache;
    private CacheTemplate<String, GcArticleMop, GcArticleMop> gcArticleCache;

    // 常量定义
    private static final String APP = "GV";
    private static final String PATH = "CACHE:";
    private static final String APP_CODE = "GV:";

    @PostConstruct
    public void init() {
        // 初始化各种缓存
        posCache = new CacheTemplate.Builder<String, Pos, PosResponse>()
                .withCacheType("POS")
                .withValueType(Pos.class)
                .withExpireTime(CACHE_EXPIRE_TIME)
                .withRedisTemplate(redisTemplate)
                .withDataLoader(machineId -> {
                    Result<PosResponse> result = posService.getPos(GetPosRequest.builder().machineId(machineId).build());
                    return result != null ? result.getData() : null;
                })
                .withDataConverter(data -> BeanCopyUtils.jsonCopyBean(data, Pos.class))
                .useLocalCache(true)
                .withLocalCacheExpireTime(LOCAL_CACHE_EXPIRE_TIME)
                .build();

        cpgCache = new CacheTemplate.Builder<String, Cpg, GetCpgResponse>()
                .withCacheType("CPG")
                .withValueType(Cpg.class)
                .withExpireTime(CACHE_EXPIRE_TIME)
                .withRedisTemplate(redisTemplate)
                .withDataLoader(cpgCode -> {
                    Result<GetCpgResponse> result = cpgService.getCpg(GetCpgRequest.builder().cpgCode(cpgCode).build());
                    return result != null ? result.getData() : null;
                })
                .withDataConverter(data -> BeanCopyUtils.jsonCopyBean(data, Cpg.class))
                .useLocalCache(true)
                .withLocalCacheExpireTime(LOCAL_CACHE_EXPIRE_TIME)
                .build();

        gcCpgCache = new CacheTemplate.Builder<String, GcCpgDTO, GcCpgDTO>()
                .withCacheType("GC_CPG")
                .withValueType(GcCpgDTO.class)
                .withExpireTime(CACHE_EXPIRE_TIME)
                .withRedisTemplate(redisTemplate)
                .withDataLoader(cpgCode -> {
                    return  gcCpgService.getCpg(cpgCode);
                })
                .withDataConverter(data -> data)
                .useLocalCache(true)
                .withLocalCacheExpireTime(LOCAL_CACHE_EXPIRE_TIME)
                .build();

        outletCache = new CacheTemplate.Builder<String, Outlet, OutletResponse>()
                .withCacheType("OUTLET")
                .withValueType(Outlet.class)
                .withExpireTime(CACHE_EXPIRE_TIME)
                .withRedisTemplate(redisTemplate)
                .withDataLoader(outletCode -> {
                    OutletResponse result = outletService.getOutlet(GetOutletRequest.builder().outletCode(outletCode).build());
                    return result;
                })
                .withDataConverter(data -> BeanCopyUtils.jsonCopyBean(data, Outlet.class))
                .useLocalCache(true)
                .withLocalCacheExpireTime(LOCAL_CACHE_EXPIRE_TIME)
                .build();

        merchantCache = new CacheTemplate.Builder<String, Merchant, MerchantResponse>()
                .withCacheType("OUTLET")
                .withValueType(Merchant.class)
                .withExpireTime(CACHE_EXPIRE_TIME)
                .withRedisTemplate(redisTemplate)
                .withDataLoader(merchantCode -> {
                    MerchantResponse result = merchantService.getMerchant(GetMerchantRequest.builder().merchantCode(merchantCode).build());
                    return result;
                })
                .withDataConverter(data -> BeanCopyUtils.jsonCopyBean(data, Merchant.class))
                .useLocalCache(true)
                .withLocalCacheExpireTime(LOCAL_CACHE_EXPIRE_TIME)
                .build();

        posCpgCache = new CacheTemplate.Builder<String, List<PosCpg>, List<PosCpgResponse>>()
                .withCacheType("POS_CPG")
                .withValueType((Class<List<PosCpg>>) (Class<?>) List.class)
                .withElementType(PosCpg.class)
                .withExpireTime(OUTLET_CPG_CACHE_EXPIRE_TIME)
                .withRedisTemplate(redisTemplate)
                .withDataLoader(posCode -> posCpgService.queryPosCpgListByPos(posCode))
                .withDataConverter(data -> BeanCopyUtils.jsonCopyList(data, PosCpg.class))
                .useLocalCache(true)
                .withLocalCacheExpireTime(LOCAL_CACHE_EXPIRE_TIME)
                .build();

        outletCpgCache = new CacheTemplate.Builder<String, List<OutletCpg>, List<OutletCpgResponse>>()
                .withCacheType("OUTLET_CPG")
                .withValueType((Class<List<OutletCpg>>) (Class<?>) List.class)
                .withElementType(OutletCpg.class)
                .withExpireTime(OUTLET_CPG_CACHE_EXPIRE_TIME)
                .withRedisTemplate(redisTemplate)
                .withDataLoader(outletCode -> outletCpgService.queryOutletCpgListByOutlet(outletCode))
                .withDataConverter(data -> BeanCopyUtils.jsonCopyList(data, OutletCpg.class))
                .useLocalCache(true)
                .withLocalCacheExpireTime(LOCAL_CACHE_EXPIRE_TIME)
                .build();

        gcArticleCache = new CacheTemplate.Builder<String, GcArticleMop, GcArticleMop>()
                .withCacheType("GC_ARTICLE_MOP")
                .withValueType(GcArticleMop.class)
                .withExpireTime(CACHE_EXPIRE_TIME)
                .withRedisTemplate(redisTemplate)
                .withDataLoader(articleCode -> gcArticleMopService.queryByArticleMopCode(articleCode))
                .withDataConverter(data -> data)
                .useLocalCache(true)
                .withLocalCacheExpireTime(LOCAL_CACHE_EXPIRE_TIME)
                .build();



    }



    // === POS相关方法 ===

    public Pos getPos(String machineId) {
        return posCache.get(machineId);
    }

    public void updatePosCache(Pos pos) {
        posCache.update(pos.getMachineId(), pos);
    }

    public boolean hasPos(String machineId) {
        if (StringUtils.isBlank(machineId)) {
            return false;
        }
        return getPos(machineId) != null;
    }

    // === CPG相关方法 ===

    public Cpg getCpg(String cpgCode) {
        return cpgCache.get(cpgCode);
    }

    public void updateCpgCache(Cpg cpg) {
        cpgCache.update(cpg.getCpgCode(), cpg);
    }


    // === CPG相关方法 ===

    public GcCpgDTO getGcCpg(String cpgCode) {
        return gcCpgCache.get(cpgCode);
    }

    public void updateGcCpgCache(GcCpgDTO cpg) {
        gcCpgCache.update(cpg.getCpgCode(), cpg);
    }


    // === Merchant 相关方法 ===
    public Merchant getMerchant(String merchantCode) {
        return merchantCache.get(merchantCode);
    }
    public void updateMerchantCache(Merchant merchant) {
        merchantCache.update(merchant.getMerchantCode(), merchant);
    }


    // === ArticleMop相关方法 ===
    public ArticleMop getArticleMop(String articleMopCode) {
        return articleCache.get(articleMopCode);
    }

    public void updateArticleMopCache(ArticleMop articleMop) {
        articleCache.update(articleMop.getArticleMopCode(), articleMop);
    }


    // === GcArticleMop相关方法 ===
    public GcArticleMop getGcArticleMop(String articleMopCode) {
        return gcArticleCache.get(articleMopCode);
    }

    public void updateGcArticleMopCache(GcArticleMop articleMop) {
        gcArticleCache.update(articleMop.getArticleMopCode(), articleMop);
    }

    // === Outlet相关方法 ===

    public Outlet getOutlet(String outletCode) {
        return outletCache.get(outletCode);
    }

    public void updateOutletCache(Outlet outlet) {
        outletCache.update(outlet.getOutletCode(), outlet);
    }

    public List<Outlet> getOutletByOutletCode(List<String> outletCodeList) {
        if (CollectionUtils.isEmpty(outletCodeList)) {
            return new ArrayList<>();
        }
        return outletCache.batchGet(outletCodeList);
    }

    // === PosCpg相关方法 ===

    public List<PosCpg> getPosCpg(String posCode) {
        return BeanCopyUtils.jsonCopyList(posCpgCache.get(posCode),PosCpg.class);
    }

    public void deletePosCpgCache(String posCode) {
        posCpgCache.delete(posCode);
    }
    public boolean hasPosCpg(String posCode) {
        if (StringUtils.isBlank(posCode)) {
            return false;
        }
        return CollectionUtils.isNotEmpty(getPosCpg(posCode));
    }

    /**
     * 检查指定POS和CPG组合是否存在
     * @param posCode POS编码
     * @param cpgCode CPG编码
     * @return true-存在，false-不存在
     */
    public boolean hasPosCpgWithCpgCode(String posCode, String cpgCode) {
        if (StringUtils.isBlank(posCode) || StringUtils.isBlank(cpgCode)) {
            return false;
        }
        
        if (!hasPosCpg(posCode)) {
            return false;
        }
        
        List<PosCpg> posCpgs = getPosCpg(posCode);
        return posCpgs != null && posCpgs.stream()
                .anyMatch(cpg -> cpgCode.equals(cpg.getCpgCode()));
    }

    // === OutletCpg相关方法 ===

    public List<OutletCpg> getOutletCpg(String outletCode) {
        return outletCpgCache.get(outletCode);
    }

    public void deleteOutletCpgCache(String outletCode) {
        outletCpgCache.delete(outletCode);
    }

    public boolean hasOutletCpg(String outletCode) {
        if (StringUtils.isBlank(outletCode)) {
            return false;
        }
        return CollectionUtils.isNotEmpty(getOutletCpg(outletCode));
    }

    /**
     * 检查指定outlet和cpg组合是否存在
     * @param outletCode 门店编码
     * @param cpgCode CPG编码
     * @return true-存在，false-不存在
     */
    public boolean hasOutletCpgWithCpgCode(String outletCode, String cpgCode) {
        if (StringUtils.isBlank(outletCode) || StringUtils.isBlank(cpgCode)) {
            return false;
        }
        
        if (!hasOutletCpg(outletCode)) {
            return false;
        }
        
        List<OutletCpg> outletCpgs = getOutletCpg(outletCode);
        return outletCpgs != null && outletCpgs.stream()
                .anyMatch(cpg -> cpgCode.equals(cpg.getCpgCode()));
    }

    // === 缓存管理方法 ===

    /**
     * 获取缓存统计信息
     */
    public String getCacheStats() {
        StringBuilder stats = new StringBuilder("缓存统计信息:\n");
        stats.append(posCache.getStats()).append("\n");
        stats.append(cpgCache.getStats()).append("\n");
        stats.append(outletCache.getStats()).append("\n");
        stats.append(posCpgCache.getStats()).append("\n");
        stats.append(outletCpgCache.getStats());
        return stats.toString();
    }

    /**
     * 清除所有缓存
     */
    public void clearAllCaches() {
        posCache.clearAll();
        cpgCache.clearAll();
        outletCache.clearAll();
        posCpgCache.clearAll();
        outletCpgCache.clearAll();
        log.info("已清除所有缓存");
    }
    
    /**
     * 清理所有本地缓存
     */
    public void clearAllLocalCaches() {
        posCache.clearLocalCache();
        cpgCache.clearLocalCache();
        outletCache.clearLocalCache();
        posCpgCache.clearLocalCache();
        outletCpgCache.clearLocalCache();
        log.info("已清除所有本地缓存");
    }
    
    /**
     * 清理过期的本地缓存
     */
    public void cleanExpiredLocalCaches() {
        posCache.cleanExpiredLocalCache();
        cpgCache.cleanExpiredLocalCache();
        outletCache.cleanExpiredLocalCache();
        posCpgCache.cleanExpiredLocalCache();
        outletCpgCache.cleanExpiredLocalCache();
    }
    
    /**
     * 定期清理过期的本地缓存
     */
    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void scheduledCleanExpiredLocalCaches() {
        log.debug("开始定期清理过期的本地缓存");
        cleanExpiredLocalCaches();
    }

    public void deleteOutletCache(String outletCode) {
        if (StringUtils.isNotBlank(outletCode)) {
            outletCache.delete(outletCode);
        }
    }

    /*private void putCpg() {
        List<Cpg> cpgList = cpgService.queryCpgAll();
        for (Cpg cpg : cpgList) {
            cpgCache.update(cpg.getCpgCode(), cpg);
        }
    }

    private void putOutlet() {
        List<Outlet> outletList = outletService.queryAllOutLet();
        for (Outlet outlet : outletList) {
            outletCache.update(outlet.getOutletCode(), outlet);
        }
    }

    private void putPos() {
        List<Pos> posList = posService.queryPosALL();
        for (Pos pos : posList) {
            posCache.update(pos.getMachineId(), pos);
        }
    }*/
}