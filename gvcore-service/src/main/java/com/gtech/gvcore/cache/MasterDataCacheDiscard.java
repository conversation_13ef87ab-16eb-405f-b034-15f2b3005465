package com.gtech.gvcore.cache;

import com.alibaba.fastjson.JSONArray;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.request.cpg.GetCpgRequest;
import com.gtech.gvcore.common.request.outlet.GetOutletRequest;
import com.gtech.gvcore.common.request.pos.GetPosRequest;
import com.gtech.gvcore.common.response.cpg.GetCpgResponse;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.common.response.outletcpg.OutletCpgResponse;
import com.gtech.gvcore.common.response.pos.PosCpgResponse;
import com.gtech.gvcore.common.response.pos.PosResponse;
import com.gtech.gvcore.dao.model.*;
import com.gtech.gvcore.helper.GvRedisTemplate;
import com.gtech.gvcore.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Slf4j
@Component
public class MasterDataCacheDiscard{

    private static final long CACHE_EXPIRE_TIME = 3600000L; // 1小时过期
    private static final long OUTLET_CPG_CACHE_EXPIRE_TIME = 1800000L; // 30分钟过期
    private static final int MAX_CACHE_SIZE = 10000; // 最大缓存条目数

    @Autowired
    private PosCpgService posCpgService;

    @Autowired
    private OutletService outletService;

    @Autowired
    private PosService posService;

    @Autowired
    private CpgService cpgService;

    @Autowired
    private OutletCpgService outletCpgService;

    @Autowired
    private GvRedisTemplate redisTemplate;

    // 缓存统计
    private final AtomicLong cacheHits = new AtomicLong(0);
    private final AtomicLong cacheMisses = new AtomicLong(0);
    private final AtomicLong outletCpgCacheSize = new AtomicLong(0);

    String initKey = "GV:INIT:KEY";
    String APP_CODE = "GV:";
    String APP = "GV";
    String PATH = "CACHE:";
    String CPG = "CPG";
    String OUTLET = "OUTLET";
    String OUTLET_CPG = "OUTLET_CPG:";
    String POS = "POS";
    String POS_CPG = "POS_CPG:";



    private void putCpg() {
        List<Cpg> cpgList = cpgService.queryCpgAll();
        Map<String, Object> collect = cpgList.stream().collect(Collectors.toMap(Cpg::getCpgCode, cpg -> cpg));
        redisTemplate.opsValueMultiSet(APP_CODE + PATH + CPG, collect);
        for (Map.Entry<String, Object> entry : collect.entrySet()) {
            redisTemplate.expire(APP, PATH + CPG + ":" + entry.getKey(), CACHE_EXPIRE_TIME);
        }
    }

    private void putOutlet() {
        List<Outlet> outletList = outletService.queryAllOutLet();
        Map<String, Object> collect = outletList.stream().collect(Collectors.toMap(Outlet::getOutletCode, value -> value));
        redisTemplate.opsValueMultiSet(APP_CODE + PATH + OUTLET, collect);
        for (Map.Entry<String, Object> entry : collect.entrySet()) {
            redisTemplate.expire(APP, PATH + OUTLET + ":" + entry.getKey(), CACHE_EXPIRE_TIME);
        }
    }

    private void putPos() {
        List<Pos> posList = posService.queryPosALL();
        Map<String, Object> collect = posList.stream().collect(Collectors.toMap(Pos::getMachineId, value -> value));
        redisTemplate.opsValueMultiSet(APP_CODE + PATH + POS, collect);
        for (Map.Entry<String, Object> entry : collect.entrySet()) {
            redisTemplate.expire(APP, PATH + POS + ":" + entry.getKey(), CACHE_EXPIRE_TIME);
        }
    }
    //更新 pos 缓存
    public void updatePosCache(Pos pos) {
        String key = PATH + POS + ":" + pos.getMachineId();
        redisTemplate.opsValueSet(APP, key, pos, CACHE_EXPIRE_TIME);
    }
    //更新 outlet 缓存
    public void updateOutletCache(Outlet outlet) {
        String key = PATH + OUTLET + ":" + outlet.getOutletCode();
        redisTemplate.opsValueSet(APP, key, outlet, CACHE_EXPIRE_TIME);
    }
    //更新 pos cpg 缓存
    public void updatePosCpgCache(PosCpg posCpg) {
        String key = PATH + POS_CPG + posCpg.getPosCode();
        redisTemplate.opsValueSet(APP, key, posCpg, CACHE_EXPIRE_TIME);
    }
    //更新 outlet cpg 缓存
    public void updateOutletCpgCache(OutletCpg outletCpg) {
        String key = PATH + OUTLET_CPG + outletCpg.getOutletCode();
        redisTemplate.opsValueSet(APP, key, outletCpg, OUTLET_CPG_CACHE_EXPIRE_TIME);
    }
    //更新 cpg 缓存
    public void updateCpgCache(Cpg cpg) {
        String key = PATH + CPG + ":" + cpg.getCpgCode();
        redisTemplate.opsValueSet(APP, key, cpg, CACHE_EXPIRE_TIME);
    }


    public Pos getPos(String machineId) {
        String key = PATH + POS + ":" + machineId;
        Pos pos = redisTemplate.opsValueGet(APP, key, Pos.class);
        if (pos == null) {
            cacheMisses.incrementAndGet();
            log.warn("POS缓存未命中: {}", machineId);
            Result<PosResponse> posResponseResult = posService.getPos(GetPosRequest.builder().machineId(machineId).build());
            if (posResponseResult != null) {
                pos = BeanCopyUtils.jsonCopyBean(posResponseResult.getData(), Pos.class);
                redisTemplate.opsValueSet(APP, key, pos, CACHE_EXPIRE_TIME);
            }
        } else {
            cacheHits.incrementAndGet();
        }
        return pos;
    }

    /**
     * 检查指定POS是否存在
     * @param machineId 机器ID
     * @return true-存在，false-不存在
     */
    public boolean hasPos(String machineId) {
        if (StringUtils.isBlank(machineId)) {
            return false;
        }
        return getPos(machineId) != null;
    }

    public List<PosCpg> getPosCpg(String posCode) {
        String key = PATH + POS_CPG + posCode;
        JSONArray pos = redisTemplate.opsValueGet(APP, key, JSONArray.class);
        if (pos == null) {
            cacheMisses.incrementAndGet();
            log.warn("POS_CPG缓存未命中: {}", posCode);
            List<PosCpgResponse> posResponseResult = posCpgService.queryPosCpgListByPos(posCode);
            if (posResponseResult != null) {
                List<PosCpg> posCpgs = BeanCopyUtils.jsonCopyList(posResponseResult, PosCpg.class);
                redisTemplate.opsValueSet(APP, key, JSONArray.toJSONString(posCpgs), OUTLET_CPG_CACHE_EXPIRE_TIME);
                outletCpgCacheSize.incrementAndGet();
                return posCpgs;
            }
            return null;
        }
        cacheHits.incrementAndGet();
        return pos.toJavaList(PosCpg.class);
    }

    /**
     * 检查指定POS是否存在POS_CPG关联
     * @param posCode POS编码
     * @return true-存在，false-不存在
     */
    public boolean hasPosCpg(String posCode) {
        if (StringUtils.isBlank(posCode)) {
            return false;
        }
        return CollectionUtils.isNotEmpty(getPosCpg(posCode));
    }

    /**
     * 检查指定POS和CPG组合是否存在
     * @param posCode POS编码
     * @param cpgCode CPG编码
     * @return true-存在，false-不存在
     */
    public boolean hasPosCpgWithCpgCode(String posCode, String cpgCode) {
        if (StringUtils.isBlank(posCode) || StringUtils.isBlank(cpgCode)) {
            return false;
        }
        
        // 先检查是否存在POS_CPG关联
        if (!hasPosCpg(posCode)) {
            return false;
        }
        
        // 如果缓存中没有数据，说明hasPosCpg已经查询过数据库并缓存了结果
        // 此时可以直接从缓存中获取数据
        List<PosCpg> posCpgs = getPosCpg(posCode);
        if (posCpgs != null && !posCpgs.isEmpty()) {
            return posCpgs.stream()
                    .anyMatch(cpg -> cpgCode.equals(cpg.getCpgCode()));
        }
        return false;
    }

    public Cpg getCpg(String cpgCode) {
        String key = PATH + CPG + ":" + cpgCode;
        Cpg cpg = redisTemplate.opsValueGet(APP, key, Cpg.class);
        if (cpg == null) {
            cacheMisses.incrementAndGet();
            log.warn("CPG缓存未命中: {}", cpgCode);
            Result<GetCpgResponse> cpgResponseResult = cpgService.getCpg(GetCpgRequest.builder().cpgCode(cpgCode).build());
            if (cpgResponseResult != null) {
                cpg = BeanCopyUtils.jsonCopyBean(cpgResponseResult.getData(), Cpg.class);
                redisTemplate.opsValueSet(APP, key, cpg, CACHE_EXPIRE_TIME);
            }
        } else {
            cacheHits.incrementAndGet();
        }
        return cpg;
    }

    public Outlet getOutlet(String outletCode) {
        String key = PATH + OUTLET + ":" + outletCode;
        Outlet outlet = redisTemplate.opsValueGet(APP, key, Outlet.class);
        if (outlet == null) {
            cacheMisses.incrementAndGet();
            log.warn("OUTLET缓存未命中: {}", outletCode);
            OutletResponse outletResponse = outletService.getOutlet(GetOutletRequest.builder().outletCode(outletCode).build());
            if (outletResponse != null) {
                outlet = BeanCopyUtils.jsonCopyBean(outletResponse, Outlet.class);
                redisTemplate.opsValueSet(APP, key, outlet, CACHE_EXPIRE_TIME);
            }
        } else {
            cacheHits.incrementAndGet();
        }
        return outlet;
    }

    public List<Outlet> getOutletByOutletCode(List<String> outletCodeList) {
        List<String> notInCache = new ArrayList<>();
        List<Outlet> inCache = new ArrayList<>();
        for (String outletCode : outletCodeList) {
            String key = PATH + OUTLET + ":" + outletCode;
            Outlet outlet = redisTemplate.opsValueGet(APP, key, Outlet.class);
            if (outlet == null) {
                notInCache.add(outletCode);
            } else {
                inCache.add(outlet);
                cacheHits.incrementAndGet();
            }
        }

        if (CollectionUtils.isNotEmpty(notInCache)) {
            cacheMisses.addAndGet(notInCache.size());
            List<Outlet> outlets = outletService.queryByOutletCodeList(notInCache);
            if (CollectionUtils.isNotEmpty(outlets)) {
                inCache.addAll(outlets);
                for (Outlet outlet : outlets) {
                    String key = PATH + OUTLET + ":" + outlet.getOutletCode();
                    redisTemplate.opsValueSet(APP, key, outlet, CACHE_EXPIRE_TIME);
                }
            }
        }
        return inCache;
    }

    public List<OutletCpg> getOutletCpg(String outletCode) {
        String key = PATH + OUTLET_CPG + outletCode;
        JSONArray outletCpg = redisTemplate.opsValueGet(APP, key, JSONArray.class);
        if (outletCpg == null) {
            cacheMisses.incrementAndGet();
            log.warn("OUTLET_CPG缓存未命中: {}", outletCode);
            List<OutletCpgResponse> outletCpgResponses = outletCpgService.queryOutletCpgListByOutlet(outletCode);
            if (outletCpgResponses != null) {
                List<OutletCpg> outletCpgs = BeanCopyUtils.jsonCopyList(outletCpgResponses, OutletCpg.class);
                redisTemplate.opsValueSet(APP, key, JSONArray.toJSONString(outletCpgs), OUTLET_CPG_CACHE_EXPIRE_TIME);
                outletCpgCacheSize.incrementAndGet();
                return outletCpgs;
            }
            return null;
        }
        cacheHits.incrementAndGet();
        return outletCpg.toJavaList(OutletCpg.class);
    }

    /**
     * 检查指定outlet是否存在OutletCpg关联
     * @param outletCode 门店编码
     * @return true-存在，false-不存在
     */
    public boolean hasOutletCpg(String outletCode) {
        if (StringUtils.isBlank(outletCode)) {
            return false;
        }
        return CollectionUtils.isNotEmpty(getOutletCpg(outletCode));
    }

    /**
     * 检查指定outlet和cpg组合是否存在
     * @param outletCode 门店编码
     * @param cpgCode CPG编码
     * @return true-存在，false-不存在
     */
    public boolean hasOutletCpgWithCpgCode(String outletCode, String cpgCode) {
        if (StringUtils.isBlank(outletCode) || StringUtils.isBlank(cpgCode)) {
            return false;
        }
        
        // 先检查是否存在OutletCpg关联
        if (!hasOutletCpg(outletCode)) {
            return false;
        }
        // 如果缓存中没有数据，说明hasOutletCpg已经查询过数据库并缓存了结果
        // 此时可以直接从缓存中获取数据
        List<OutletCpg> outletCpgs = getOutletCpg(outletCode);
        if (outletCpgs != null && !outletCpgs.isEmpty()) {
            return outletCpgs.stream()
                    .anyMatch(cpg -> cpgCode.equals(cpg.getCpgCode()));
        }
        return false;
    }


}
