package com.gtech.gvcore.cache;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import com.gtech.gvcore.helper.GvRedisTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;

/**
 * 通用缓存操作模板
 * @param <K> 键类型
 * @param <V> 值类型
 * @param <R> 数据源返回类型（可能与V不同）
 */
@Slf4j
public class CacheTemplate<K, V, R> {

    // 缓存统计
    private final AtomicLong cacheHits = new AtomicLong(0);
    private final AtomicLong cacheMisses = new AtomicLong(0);
    private final AtomicLong localCacheHits = new AtomicLong(0);

    // 基础常量
    private final String appName;
    private final String cachePath;
    private final String cacheType;
    private final Long expireTime;
    private final Class<V> valueType;
    
    // 集合元素类型
    private final Class<?> elementType;

    // 数据加载与转换函数
    private final Function<K, R> dataLoader;
    private final Function<R, V> dataConverter;

    // Redis模板
    private final GvRedisTemplate redisTemplate;
    
    // 本地缓存相关
    private final boolean useLocalCache;
    private final long localCacheExpireTime;
    private final int localCacheMaxSize;
    private final Cache<String, V> localCache;

    // 构造函数
    private CacheTemplate(Builder<K, V, R> builder) {
        this.appName = builder.appName;
        this.cachePath = builder.cachePath;
        this.cacheType = builder.cacheType;
        this.expireTime = builder.expireTime;
        this.valueType = builder.valueType;
        this.elementType = builder.elementType;
        this.dataLoader = builder.dataLoader;
        this.dataConverter = builder.dataConverter;
        this.redisTemplate = builder.redisTemplate;
        this.useLocalCache = builder.useLocalCache;
        this.localCacheExpireTime = builder.localCacheExpireTime;
        this.localCacheMaxSize = builder.localCacheMaxSize;
        
        // 初始化Caffeine缓存
        if (this.useLocalCache) {
            this.localCache = Caffeine.newBuilder()
                .expireAfterWrite(this.localCacheExpireTime, TimeUnit.MILLISECONDS)
                .maximumSize(this.localCacheMaxSize)
                .recordStats()
                .build();
        } else {
            this.localCache = null;
        }
    }

    /**
     * 获取缓存键
     */
    private String getCacheKey(K id) {
        return cachePath + cacheType + ":" + id.toString();
    }

    /**
     * 从缓存获取单个对象，如果缓存未命中则从数据源加载
     */
    public V get(K id) {
        if (id == null) {
            return null;
        }
        
        String key = getCacheKey(id);
        V value = null;
        
        // 先尝试从本地缓存获取
        if (useLocalCache) {
            value = getFromLocalCache(key);
            if (value != null) {
                return value;
            }
        }
        
        // 本地缓存未命中，尝试从Redis缓存获取
        if (List.class.isAssignableFrom(valueType)) {
            // 对于列表类型，特殊处理
            try {
                JSONArray jsonArray = redisTemplate.opsValueGet(appName, key, JSONArray.class);
                if (jsonArray != null) {
                    cacheHits.incrementAndGet();
                    Class<?> actualType = getActualTypeForList();
                    value = (V) jsonArray.toJavaList(actualType);
                    // 如果启用了本地缓存，将结果放入本地缓存
                    if (useLocalCache && value != null) {
                        putToLocalCache(key, value);
                    }
                    return value;
                }
                
                // 两种格式都没有获取到，计为缓存未命中
                cacheMisses.incrementAndGet();
            } catch (Exception e) {
                log.error("{}缓存数据转换错误: {}, 异常: {}", cacheType, key, e.getMessage());
                cacheMisses.incrementAndGet();
            }
        } else {
            // 普通对象类型
            value = redisTemplate.opsValueGet(appName, key, valueType);
            if (value != null) {
                cacheHits.incrementAndGet();
                // 如果启用了本地缓存，将结果放入本地缓存
                if (useLocalCache) {
                    putToLocalCache(key, value);
                }
                return value;
            } else {
                cacheMisses.incrementAndGet();
            }
        }
        
        log.warn("{}缓存未命中: {}", cacheType, id);
        
        // 缓存未命中，从数据源加载
        R result = dataLoader.apply(id);
        if (result == null) {
            return null;
        }
        
        // 转换数据
        value = dataConverter.apply(result);
        if (value == null) {
            return null;
        }
        
        // 存入Redis缓存
        if (List.class.isAssignableFrom(valueType)) {
            redisTemplate.opsValueSet(appName, key, JSON.toJSONString(value), expireTime);
        } else {
            redisTemplate.opsValueSet(appName, key, value, expireTime);
        }
        
        // 如果启用了本地缓存，将结果放入本地缓存
        if (useLocalCache) {
            putToLocalCache(key, value);
        }
        
        return value;
    }
    
    /**
     * 从本地缓存获取值
     */
    private V getFromLocalCache(String key) {
        if (!useLocalCache) {
            return null;
        }
        
        V value = localCache.getIfPresent(key);
        if (value != null) {
            localCacheHits.incrementAndGet();
        }
        return value;
    }
    
    /**
     * 将值存入本地缓存
     */
    private void putToLocalCache(String key, V value) {
        if (!useLocalCache || value == null) {
            return;
        }
        localCache.put(key, value);
    }

    /**
     * 批量获取对象
     */
    public List<V> batchGet(List<K> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }

        List<V> result = new ArrayList<>();
        List<K> missingIds = new ArrayList<>();

        // 尝试从缓存获取
        for (K id : ids) {
            V value = get(id);
            if (value != null) {
                result.add(value);
            } else {
                missingIds.add(id);
            }
        }

        // 这里可以扩展批量加载逻辑

        return result;
    }

    /**
     * 更新缓存
     */
    public void update(K id, V value) {
        if (id == null || value == null) {
            return;
        }

        String key = getCacheKey(id);
        // 更新Redis缓存
        if (List.class.isAssignableFrom(valueType)) {
            redisTemplate.opsValueSet(appName, key, JSON.toJSONString(value), expireTime);
        } else {
            redisTemplate.opsValueSet(appName, key, value, expireTime);
        }
        
        // 如果启用了本地缓存，同时更新本地缓存
        if (useLocalCache) {
            putToLocalCache(key, value);
        }
    }

    /**
     * 删除缓存
     */
    public void delete(K id) {
        if (id == null) {
            return;
        }

        String key = getCacheKey(id);
        // 删除Redis缓存
        redisTemplate.delete(appName, key);
        
        // 如果启用了本地缓存，同时删除本地缓存
        if (useLocalCache) {
            localCache.invalidate(key);
        }
    }

    /**
     * 清空特定类型的所有缓存
     */
    public void clearAll() {
        // 清空Redis缓存
        String pattern = appName + ":" + cachePath + cacheType + ":*";
        redisTemplate.deleteKeys(redisTemplate.keys(pattern));
        
        // 如果启用了本地缓存，同时清空本地缓存
        if (useLocalCache) {
            clearLocalCache();
        }
    }
    
    /**
     * 清空本地缓存
     */
    public void clearLocalCache() {
        if (useLocalCache) {
            localCache.invalidateAll();
            log.info("已清空[{}]本地缓存", cacheType);
        }
    }
    
    /**
     * 清理过期的本地缓存
     * 注意：Caffeine会自动清理过期条目，此方法只是强制执行清理
     */
    public void cleanExpiredLocalCache() {
        if (useLocalCache) {
            localCache.cleanUp();
            log.debug("[{}]已执行本地缓存清理", cacheType);
        }
    }

    /**
     * 获取列表类型的实际元素类型
     */
    private Class<?> getActualTypeForList() {
        if (elementType != null) {
            return elementType;
        }
        return Object.class;
    }

    /**
     * 缓存统计信息
     */
    public String getStats() {
        if (useLocalCache) {
            CacheStats stats = localCache.stats();
            return String.format("缓存[%s] - Redis命中: %d, 本地命中: %d (命中率: %.2f%%), 未命中: %d,  本地加载成功: %d, 本地加载失败: %d",
                    cacheType, 
                    cacheHits.get(),
                    stats.hitCount(),
                    stats.hitRate() * 100,
                    cacheMisses.get(),
                    stats.loadSuccessCount(),
                    stats.loadFailureCount());
        } else {
            return String.format("缓存[%s] - 命中: %d, 未命中: %d",
                    cacheType, cacheHits.get(), cacheMisses.get());
        }
    }

    /**
     * Builder模式构建器
     */
    public static class Builder<K, V, R> {
        private String appName = "GV";
        private String cachePath = "CACHE:";
        private String cacheType;
        private Long expireTime = 3600000L; // 默认1小时
        private Class<V> valueType;
        private Class<?> elementType;
        private Function<K, R> dataLoader;
        private Function<R, V> dataConverter;
        private GvRedisTemplate redisTemplate;
        private boolean useLocalCache = false;
        private long localCacheExpireTime = 300000L; // 默认5分钟
        private int localCacheMaxSize = 10000; // 默认最大缓存条目数

        public Builder<K, V, R> withAppName(String appName) {
            this.appName = appName;
            return this;
        }

        public Builder<K, V, R> withCachePath(String cachePath) {
            this.cachePath = cachePath;
            return this;
        }

        public Builder<K, V, R> withCacheType(String cacheType) {
            this.cacheType = cacheType;
            return this;
        }

        public Builder<K, V, R> withExpireTime(Long expireTime) {
            this.expireTime = expireTime;
            return this;
        }

        public Builder<K, V, R> withValueType(Class<V> valueType) {
            this.valueType = valueType;
            return this;
        }

        public Builder<K, V, R> withElementType(Class<?> elementType) {
            this.elementType = elementType;
            return this;
        }

        public Builder<K, V, R> withDataLoader(Function<K, R> dataLoader) {
            this.dataLoader = dataLoader;
            return this;
        }

        public Builder<K, V, R> withDataConverter(Function<R, V> dataConverter) {
            this.dataConverter = dataConverter;
            return this;
        }

        public Builder<K, V, R> withRedisTemplate(GvRedisTemplate redisTemplate) {
            this.redisTemplate = redisTemplate;
            return this;
        }
        
        public Builder<K, V, R> useLocalCache(boolean useLocalCache) {
            this.useLocalCache = useLocalCache;
            return this;
        }
        
        public Builder<K, V, R> withLocalCacheExpireTime(long localCacheExpireTime) {
            this.localCacheExpireTime = localCacheExpireTime;
            return this;
        }
        
        public Builder<K, V, R> withLocalCacheMaxSize(int localCacheMaxSize) {
            this.localCacheMaxSize = localCacheMaxSize;
            return this;
        }

        public CacheTemplate<K, V, R> build() {
            if (StringUtils.isBlank(cacheType)) {
                throw new IllegalArgumentException("缓存类型不能为空");
            }
            if (valueType == null) {
                throw new IllegalArgumentException("值类型不能为空");
            }
            if (dataLoader == null) {
                throw new IllegalArgumentException("数据加载器不能为空");
            }
            if (redisTemplate == null) {
                throw new IllegalArgumentException("Redis模板不能为空");
            }
            if (dataConverter == null) {
                // 默认转换器，假设R和V类型兼容
                dataConverter = r -> (V) r;
            }

            return new CacheTemplate<>(this);
        }
    }
}