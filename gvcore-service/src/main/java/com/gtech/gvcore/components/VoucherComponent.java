package com.gtech.gvcore.components;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

import org.apache.tomcat.util.threads.ThreadPoolExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.enums.BookletStatusEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.enums.VoucherCirculationStatusEnum;
import com.gtech.gvcore.common.enums.VoucherStatusEnum;
import com.gtech.gvcore.common.enums.VpgDisableGenerationEnum;
import com.gtech.gvcore.common.request.cpg.GetCpgRequest;
import com.gtech.gvcore.common.request.cpg.GetCpgTypeRequest;
import com.gtech.gvcore.common.request.transactiondata.CreateTransactionDataRequest;
import com.gtech.gvcore.common.request.voucher.CreateVoucherRequest;
import com.gtech.gvcore.common.request.voucherbatch.CreateVoucherBatchRequest;
import com.gtech.gvcore.common.response.cpg.GetCpgResponse;
import com.gtech.gvcore.common.response.cpg.GetCpgTypeResponse;
import com.gtech.gvcore.dao.mapper.VoucherBatchMapper;
import com.gtech.gvcore.dao.mapper.VoucherBookletMapper;
import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.dao.model.ArticleMop;
import com.gtech.gvcore.dao.model.VoucherBatch;
import com.gtech.gvcore.dao.model.VoucherBooklet;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.helper.VoucherNumberHelper;
import com.gtech.gvcore.service.ArticleMopService;
import com.gtech.gvcore.service.CpgService;
import com.gtech.gvcore.service.CpgTypeService;
import com.gtech.gvcore.service.TransactionDataService;
import com.gtech.gvcore.service.VoucherService;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;


/**
 * <AUTHOR>
 * @Date 2023/3/13 10:49
 */
@Slf4j
@Component
public class VoucherComponent {

    @Autowired
    private VoucherNumberHelper voucherNumberHelper;
    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private VoucherMapper voucherMapper;

    @Autowired
    private GvCodeHelper gvCodeHelper;
    @Autowired
    private CpgService cpgService;

    @Autowired
    private CpgTypeService cpgTypeService;

    @Autowired
    private ArticleMopService articleMopService;
    @Autowired
    private VoucherBookletMapper voucherBookletMapper;
    @Autowired
    private VoucherNumberHelper voucherBatchHelper;

    @Lazy
    @Autowired
    VoucherService voucherService;

    @Autowired
    private TransactionDataService transactionDataService;

    @Autowired
    private VoucherBatchMapper voucherBatchMapper;

    // 批量生成 ID
    @Autowired
    private SnowflakeRedis idGenerator;


    public static final String REDIS_HEAD = "GV:VOUCHER:";
    public static final String YEAR = "2022";
    public static final String HEAD = "9001";

    public static final String DIGITAL = "101";

    public static final String VOUCHER_AUTO_INCR = "0000001";
    public static final String BOOKLET_AUTO_INCR = "0000000001";
    public static final String BATCH_AUTO_INCR = "001";
    public static final String FILE_NAME_AUTO_INCR = "01";

    public static final String RE = "[a-zA-Z]";
    public static final String REDIS_BOOKLET = "BOOKLET:";

    public static final Integer GENERATING = 0;
    public static final Integer GENERATED = 1;
    public static final Integer PUBLISHED = 2;
    public static final Integer COMPLETED = 3;
    public static final Integer FAILURE = 4;
    public static final Integer DELETE = 5;
    public static final Integer CANCEL = 6;
    public static final String VOUCHER_CODE = "VoucherCode:";

    public static final Integer BATCH_GENERATING = 0;
    public static final Integer BATCH_GENERATED = 1;
    public static final Integer BATCH_PUBLISHED = 2;
    public static final Integer BATCH_COMPLETED = 3;
    public static final Integer BATCH_FAILURE = 4;


    public class CustomThreadFactory implements ThreadFactory {
        private final String poolName;

        public CustomThreadFactory(String poolName) {
            this.poolName = poolName;
        }

        @Override
        public Thread newThread(Runnable r) {
            return new Thread(r, poolName + "-thread");
        }
    }

    // 使用自定义线程池名称的示例代码
    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(
            500,
            500,
            100,
            TimeUnit.SECONDS,
            new LinkedBlockingDeque<>(2000000),
            new CustomThreadFactory("GenerateVoucher")
    );


    RedisTemplate<String, Long> redisTemplateSnow = new RedisTemplate<>();


    private LoadingCache<String, GetCpgResponse> cpgCache = CacheBuilder.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build(new CacheLoader<String, GetCpgResponse>() {
                @Override
                public GetCpgResponse load(String key) {
                    GetCpgRequest cpgRequest = new GetCpgRequest();
                    cpgRequest.setCpgCode(key);
                    Result<GetCpgResponse> result = cpgService.getCpg(cpgRequest);
                    if (result.isSuccess()) {
                        return result.getData();
                    }
                    return null;
                }
            });

    public Result<String> createVoucherBatch(CreateVoucherBatchRequest request) {
        GetCpgResponse cpg = null;
        try {
            cpg = cpgCache.get(request.getCpgCode());
        } catch (ExecutionException e) {
            throw new GTechBaseException(ResultErrorCodeEnum.GET_CPG_ERROR.code(),ResultErrorCodeEnum.GET_CPG_ERROR.desc());
        }
        if (cpg == null){
            throw new GTechBaseException(ResultErrorCodeEnum.GET_CPG_ERROR.code(),ResultErrorCodeEnum.GET_CPG_ERROR.desc());
        }

        String disableGenerationStr = VpgDisableGenerationEnum.DISABLED.code();
        boolean canBeGenerated = !disableGenerationStr.equals(cpg.getDisableGeneration());
        if (!canBeGenerated){
            return Result.failed("This vpg cannot be used to generate coupons and sales");
        }
        Date date = voucherNumberHelper.cpgEffectiveDateToDate(cpg);
        if (date != null){
            request.setVoucherEffectiveDate(date);
        }

        ArticleMop articleMop = articleMopService.queryByArticleMopCode(request.getArticleCode());
        request.setMopCode(articleMop.getMopCode());

        //设置结束值
        setRequestEndNo(request);


        VoucherBatch voucherBatch = generateVoucherBatch(request, GENERATING);
        String approvalCode = gvCodeHelper.generateApproveCode();
        request.setApprovalCode(approvalCode);

        executor.execute(()->addVoucherBookletAndCreateVoucher(request, voucherBatch.getVoucherBatchCode(), voucherBatch.getCreateTime(), approvalCode));

        return Result.ok(voucherBatch.getVoucherBatchCode());
    }

    private static void setRequestEndNo(CreateVoucherBatchRequest request) {
        //计算券的结束值和包的结束值
        String voucherStartNo = request.getVoucherStartNo();
        //voucherEndNo = voucherStartNo + voucherNum
        String voucherEndNo = voucherStartNo;
        request.setVoucherEndNo(String.valueOf(Long.parseLong(voucherEndNo) +(request.getVoucherNum() - 1) ));
        String bookletStartNo = request.getBookletStartNo();
        //bookletEndNo = bookletStartNo + bookletNum
        String bookletEndNo = bookletStartNo;
        request.setBookletEndNo(String.valueOf(Long.parseLong(bookletEndNo) +(request.getBookletNum() - 1) ));
    }

    private VoucherBatch generateVoucherBatch(CreateVoucherBatchRequest request, Integer status) {
        VoucherBatch voucherBatch = BeanCopyUtils.jsonCopyBean(request, VoucherBatch.class);
        voucherBatch.setStatus(status);
        voucherBatch.setCreateTime(new Date());
        Long voucherStartNo = Long.valueOf(request.getVoucherStartNo());
        String voucherEndNo = request.getVoucherEndNo();
        Long bookletStartNo = Long.valueOf(request.getBookletStartNo());
        String bookletEndNo = request.getBookletEndNo();

        String prefix = "";
        if (StringUtil.isNotEmpty(request.getCpgCode())){
            try {
                GetCpgRequest cpgRequest = new GetCpgRequest();
                cpgRequest.setCpgCode(request.getCpgCode());
                Result<GetCpgResponse> cpg = cpgService.getCpg(cpgRequest);
                Result<Object> cpgType = cpgTypeService.getCpgType(GetCpgTypeRequest.builder().cpgTypeCode(cpg.getData().getCpgTypeCode()).build());
                if (cpg.getData().getDisableGeneration().equals(VpgDisableGenerationEnum.DISABLED.code())){
                    throw new GTechBaseException(ResultErrorCodeEnum.GET_CPG_ERROR.code(),ResultErrorCodeEnum.GET_CPG_ERROR.desc());
                }
                GetCpgTypeResponse data = (GetCpgTypeResponse) cpgType.getData();
                prefix = data.getPrefix();
            }catch (Exception e){
                throw new GTechBaseException(ResultErrorCodeEnum.GET_CPG_ERROR.code(),ResultErrorCodeEnum.GET_CPG_ERROR.desc());
            }
        }

        int year = Calendar.getInstance().get(Calendar.YEAR);
        Long incr = year - Long.valueOf(YEAR);
        Long bookletHead = Long.valueOf(HEAD);
        bookletHead += incr;
        /*String voucherHead = voucherNumberHelper.voucherType(prefix, request.getDenomination(), String.valueOf(Calendar.getInstance().get(Calendar.YEAR)));
        String maxVoucher = (String) redisTemplate.opsForValue().get("VOUCHERMAX:" + voucherHead);
        String maxBookletCode = (String) redisTemplate.opsForValue().get("BOOKLETMAX:" + bookletHead);*/
        String voucherHead = "";
        String maxVoucher = "";
        String maxBookletCode = "";


        if (StringUtil.isNotEmpty(maxVoucher) && StringUtil.isNotEmpty(maxBookletCode)){
            if (Long.parseLong(maxVoucher)>voucherStartNo
                    || Long.parseLong(maxBookletCode)>bookletStartNo){
                throw new GTechBaseException("","Already generated vouchers please try again");
            }
        }else {
            redisTemplate.opsForValue().set("VOUCHERMAX:" + voucherHead,voucherEndNo);
            redisTemplate.opsForValue().set("BOOKLETMAX:" + bookletHead,bookletEndNo);
        }

        String voucherCode = (String) redisTemplate.opsForValue().get(REDIS_HEAD + VOUCHER_CODE + request.getVoucherStartNo().substring(0, 8));

        String cacheHeadVoucherCode = REDIS_HEAD + VOUCHER_CODE + voucherEndNo.substring(0, 8);
        if (null == voucherCode) {
            redisTemplate.opsForValue().set(cacheHeadVoucherCode, voucherEndNo);
        } else if (voucherStartNo <= Long.valueOf(voucherCode)) {
            throw new GTechBaseException();
        } else {
            redisTemplate.opsForValue().set(cacheHeadVoucherCode, voucherEndNo);
        }

        voucherBatchMapper.insert(voucherBatch);

        return voucherBatch;
    }

   /*
    调试雪花算法生成的id

    public static void main(String[] args) {

        RedisTemplate<Object,Object> redisTemplate = new RedisTemplate<>();

        RedisStandaloneConfiguration redisStandaloneConfiguration = new RedisStandaloneConfiguration("172.19.109.55", 6379);
        redisStandaloneConfiguration.setDatabase(6);
        RedisConnectionFactory connectionFactory = new LettuceConnectionFactory(redisStandaloneConfiguration);



        ((LettuceConnectionFactory) connectionFactory).afterPropertiesSet();
        redisTemplate.setConnectionFactory(connectionFactory);

        Jackson2JsonRedisSerializer serializer = new Jackson2JsonRedisSerializer(Object.class);

        ObjectMapper mapper = new ObjectMapper();
        mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        mapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance,ObjectMapper.DefaultTyping.NON_FINAL);
        serializer.setObjectMapper(mapper);

        redisTemplate.setValueSerializer(serializer);
        //使用StringRedisSerializer来序列化和反序列化redis的key值
        redisTemplate.setKeySerializer(new StringRedisSerializer());

        redisTemplate.afterPropertiesSet(); // 初始化 redisTemplate

        SnowflakeRedis idGenerator = new SnowflakeRedis(redisTemplate);

        List<Long> longs = idGenerator.nextIds("123", 4003233231620251L, 4003233231620281L);
        System.out.println(longs);

    }*/


    private void addVoucherBookletAndCreateVoucher(CreateVoucherBatchRequest request, String voucherBatchCode, Date createTime, String approvalCode) {
        //起始包的券起始和结束号
        long voucherStartNo = Long.parseLong(request.getVoucherStartNo());
        long bookletStartNo = Long.parseLong(request.getBookletStartNo());
        long bookletEndNo = Long.parseLong(request.getBookletEndNo());
        List<CreateVoucherRequest> voucherRequestList = new ArrayList<>();
        List<VoucherBooklet> bookletRequestList = new ArrayList<>();
        List<Long> bookletList = getStartAndEnd(bookletStartNo, bookletEndNo+1);

        log.info("等待插入的包号为:{} - {}, 数量为:{}", bookletList.get(0),bookletList.get(bookletList.size()-1),bookletList.size());
        CreateVoucherBatchRequest createVoucherBatchRequest = BeanCopyUtils.jsonCopyBean(request, CreateVoucherBatchRequest.class);

        //倒序
        Collections.reverse(bookletList);
        for (Long bookletCode : bookletList) {
            List<Long> voucherCodes = getStartAndEnd(voucherStartNo, voucherStartNo+50);
            log.info("等待插入的券号为:{} - {}, 数量为:{}", voucherCodes.get(0),voucherCodes.get(voucherCodes.size()-1),voucherCodes.size());
            //List<Long> voucherCodes = idGenerator.nextIds(bookletCode, Long.parseLong(createVoucherBatchRequest.getVoucherStartNo()), Long.parseLong(createVoucherBatchRequest.getVoucherEndNo()));
            VoucherBooklet voucherBooklet = addVoucherBooklet(createVoucherBatchRequest, voucherBatchCode, createTime, approvalCode, String.valueOf(bookletCode), voucherCodes);
            bookletRequestList.add(voucherBooklet);
            if (bookletRequestList.size() >= 5000) {
                List<VoucherBooklet> voucherBooklets = new ArrayList<>(bookletRequestList);
                bookletRequestList.clear();
                executor.execute(() -> {
                    int i = voucherBookletMapper.insertList(voucherBooklets);
                    log.info("{},已插入Booklet数量 {}", i);
                });
            }

            List<CreateVoucherRequest> voucherRequest = createVoucherRequest(request, voucherCodes, String.valueOf(bookletCode), approvalCode);
            voucherRequestList.addAll(voucherRequest);


            if (voucherRequestList.size() >= 5000) {
                List<CreateVoucherRequest> createVoucherRequests = new ArrayList<>(voucherRequestList);
                voucherRequestList.clear();
                bookletRequestList.clear();
                executor.execute(() -> {
                    addVoucherData(request, voucherBatchCode, createVoucherRequests);
                });
            }
            voucherStartNo+=50;
        }


        if (!voucherRequestList.isEmpty()) {
            executor.execute(() -> {
                addVoucherData(request, voucherBatchCode, voucherRequestList);
            });
        }

        if (!bookletRequestList.isEmpty()) {
            executor.execute(() -> {
                try {
                    voucherBookletMapper.insertList(bookletRequestList);
                } catch (Exception e) {
                    log.error("生成凭证失败", e);
                    updateBatchStatusFailure(request);
                }
            });
        }
    }




    List<Long> getStartAndEnd(Long startNo, Long endNo) {
        List<Long> list = new ArrayList<>();
        for (long i = startNo; i < endNo; i++) {
            list.add(startNo);
            startNo++;
        }
        return list;

    }


    private void addVoucherData(CreateVoucherBatchRequest request, String voucherBatchCode, List<CreateVoucherRequest> voucherRequestList) {
        try {
            log.info("{},插入数据库开始 {}", voucherBatchCode,voucherRequestList.size());
            int voucherList = voucherService.createVoucherList(voucherRequestList);
            redisTemplate.opsForValue().increment(REDIS_HEAD + request.getVoucherBatchCode(), voucherList);
            log.info("{},已插入数据库数量 {}", voucherBatchCode,voucherList);
            //统计数量
            updateVoucherBatchStatusIfNeeded(request, voucherBatchCode);
            createTransactionData(request, voucherRequestList, request.getCreateUser());
        } catch (Exception e) {
            log.error("生成凭证失败", e);
            updateBatchStatusFailure(request);
        }
    }

    private VoucherBooklet addVoucherBooklet(CreateVoucherBatchRequest request, String voucherBatchCode, Date createTime, String approvalCode, String bookletCode, List<Long> voucherCodes) {
        VoucherBooklet voucherBooklet = BeanCopyUtils.jsonCopyBean(request, VoucherBooklet.class);
        String barCode = voucherBatchHelper.barCode27Bit(bookletCode);
        voucherBooklet.setBookletCode(bookletCode);
        voucherBooklet.setBookletBarcode(barCode);
        voucherBooklet.setCreateTime(createTime);
        voucherBooklet.setStatus(BookletStatusEnum.GENERATED.getCode());
        return voucherBooklet;
    }


    private void updateVoucherBatchStatusIfNeeded(CreateVoucherBatchRequest request, String voucherBatchCode) {
        //统计数量 每包默认50
        String redisHeadVoucherBatchCode = REDIS_HEAD + request.getVoucherBatchCode();
        Integer increment = (Integer)redisTemplate.opsForValue().get(redisHeadVoucherBatchCode);
        log.info("{},已经生成凭证数量{}",voucherBatchCode,increment);
        if (request.getVoucherNum().equals(increment)) {
            VoucherBatch voucherBatch = new VoucherBatch();
            voucherBatch.setStatus(BATCH_GENERATED);
            Example example = new Example(VoucherBatch.class);
            example.createCriteria().andEqualTo(VoucherBatch.C_VOUCHER_BATCH_CODE, voucherBatchCode);
            voucherBatchMapper.updateByConditionSelective(voucherBatch, example);
            redisTemplate.delete(redisHeadVoucherBatchCode);
            log.info("生成凭证结束");
        }
    }

    private List<CreateVoucherRequest> createVoucherRequest(CreateVoucherBatchRequest batchRequest, List<Long> voucherCodes, String bookletCode, String approvalCode) {

        List<CreateVoucherRequest> voucherRequestList = new ArrayList<>();

        for (Long voucherCode : voucherCodes) {
            CreateVoucherRequest voucherRequest = new CreateVoucherRequest();
            voucherRequest.setIssuerCode(batchRequest.getIssuerCode());
            voucherRequest.setVoucherBatchCode(batchRequest.getVoucherBatchCode());
            voucherRequest.setBookletCode(bookletCode);
            voucherRequest.setVoucherCode(String.valueOf(voucherCode));
            voucherRequest.setCpgCode(batchRequest.getCpgCode());
            voucherRequest.setMopCode(batchRequest.getMopCode());
            voucherRequest.setDenomination(batchRequest.getDenomination());
            voucherRequest.setVoucherBarcode(voucherBatchHelper.barCode27Bit(String.valueOf(voucherCode)));
            voucherRequest.setVoucherEffectiveDate(batchRequest.getVoucherEffectiveDate());
            voucherRequest.setStatus(VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
            voucherRequest.setCirculationStatus(VoucherCirculationStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
            voucherRequest.setVoucherActiveCode(batchRequest.getArticleCode());
            voucherRequest.setPermissionCode(batchRequest.getPermissionCode());
            voucherRequest.setCreateTime(new Date());
            voucherRequest.setCreateUser(batchRequest.getCreateUser());
            voucherRequest.setVoucherCodeNum(Long.valueOf(voucherRequest.getVoucherCode().replaceAll(RE,"")));
            voucherRequest.setBookletCodeNum(Long.valueOf(voucherRequest.getBookletCode().replaceAll(RE,"")));
            voucherRequestList.add(voucherRequest);
        }





        return voucherRequestList;
    }

    private void createTransactionData(CreateVoucherBatchRequest batch,List<CreateVoucherRequest> vouchers,String createUser){
        List<CreateTransactionDataRequest> createVoucherRequests = new ArrayList<>();

        for (CreateVoucherRequest voucher : vouchers) {
            //TODO invoiceNumber  outletCode
            CreateTransactionDataRequest request = new CreateTransactionDataRequest();
            request.setTransactionId(batch.getVoucherBatchCode());
            request.setApproveCode(batch.getApprovalCode());
            request.setInvoiceNumber(batch.getInvoiceNo());
            request.setIssuerCode(batch.getIssuerCode());
            request.setTransactionType(TransactionTypeEnum.GIFT_CARD_NEW_GENERATE.getCode());
            request.setMerchantCode("");
            request.setMopCode(voucher.getMopCode());
            request.setOutletCode("");
            request.setCpgCode(voucher.getCpgCode());
            request.setTransactionDate(new Date());
            request.setVoucherCode(voucher.getVoucherCode());
            request.setVoucherCodeNum(Long.valueOf(voucher.getVoucherCode().replaceAll("[a-zA-Z]", "")));
            request.setInitiatedBy("");
            request.setPosCode("");
            request.setBatchCode(batch.getVoucherBatchCode());
            request.setLoginSource("");
            request.setDenomination(voucher.getDenomination());
            request.setActualOutlet("");
            request.setForwardingEntityId("");
            request.setResponseMessage("Transaction successful.");
            request.setTransactionMode("");
            request.setCustomerCode("");
            request.setCustomerSalutation("");
            request.setCustomerFirstName("");
            request.setCustomerLastName("");
            request.setMobile("");
//            request.setOtherInputParameter("");
            request.setSuccessOrFailure("0");
            request.setCreateUser(createUser);
            request.setCreateTime(new Date());
            request.setVoucherEffectiveDate(batch.getVoucherEffectiveDate());
            createVoucherRequests.add(request);
        }
        transactionDataService.createTransactionDataList(createVoucherRequests);
    }

    private void updateBatchStatusFailure(CreateVoucherBatchRequest batchRequest) {
        VoucherBatch voucherBatch = new VoucherBatch();
        voucherBatch.setStatus(BATCH_FAILURE);
        Example example = new Example(VoucherBatch.class);
        example.createCriteria().andEqualTo(VoucherBatch.C_VOUCHER_BATCH_CODE, batchRequest.getVoucherBatchCode());
        voucherBatchMapper.updateByConditionSelective(voucherBatch,example);
    }


}
