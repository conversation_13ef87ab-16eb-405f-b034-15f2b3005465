package com.gtech.gvcore.components.atomic;

import com.gtech.gvcore.components.atomic.impl.CustomerOrderImpl;
import com.gtech.gvcore.components.atomic.impl.TransactionDataImpl;
import com.gtech.gvcore.components.atomic.impl.VoucherStatusImpl;
import com.gtech.gvcore.components.atomic.request.AtomicRequest;

import java.util.ArrayList;
import java.util.List;

public class AtomicBuilder {


    static final List<VoucherStatusStrategy> actionList = new ArrayList<>();

    public static final String SALES = "SALES";
    public static final String ACTIVITY = "ACTIVITY";
    public static final String REDEEM = "REDEEM";




    public AtomicBuilder builder(){
        return new AtomicBuilder();
    }


    public AtomicBuilder addVoucherStatusAction(AtomicRequest request){
        actionList.add(new VoucherStatusImpl(request));
        return this;
    }

    public AtomicBuilder addTransactionDataAction(AtomicRequest request){
        actionList.add(new TransactionDataImpl(request));
        return this;
    }

    public AtomicBuilder addCustomerOrderAction(AtomicRequest request){
        actionList.add(new CustomerOrderImpl(request));
        return this;
    }

    public List<VoucherStatusStrategy> build(){
        return actionList;
    }




}
