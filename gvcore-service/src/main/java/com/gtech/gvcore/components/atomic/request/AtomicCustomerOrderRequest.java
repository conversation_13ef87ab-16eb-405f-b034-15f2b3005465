package com.gtech.gvcore.components.atomic.request;

import com.gtech.gvcore.common.request.customerorder.CreateCustomerOrderDetailsRequest;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.dao.model.Voucher;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class AtomicCustomerOrderRequest extends AtomicRequest{

    private Voucher voucher;

    private String invoiceNumber;

    private OutletResponse outletResponse;

    private String transactionId;

    private Integer numOfCards;

    private List<CreateCustomerOrderDetailsRequest> detailsRequestList;

    private BigDecimal totalAmount;
}
