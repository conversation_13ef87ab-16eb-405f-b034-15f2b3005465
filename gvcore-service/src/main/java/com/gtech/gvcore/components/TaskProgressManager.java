package com.gtech.gvcore.components;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.redis.GTechRedisTemplate;
import com.gtech.gvcore.dto.TaskProgress;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class TaskProgressManager {

    private static final String TASK_PROGRESS_PREFIX = "task:progress";
    private static final String APP_KEY = "GV";

    private GTechRedisTemplate redisTemplate;

    public TaskProgressManager(GTechRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    // 初始化任务
    public void initializeTask(String taskId, Map<String, TaskProgress> taskProgressMap) {
        String taskKey = TASK_PROGRESS_PREFIX + taskId;
        taskProgressMap.forEach((subtaskId, progress) -> {
            redisTemplate.opsHashPut(APP_KEY,taskKey, subtaskId, progress);
        });
    }

    // 原子性地更新某个子任务的进度
    public void incrementSubtaskProgress(String taskId, String subtaskId, long unitsCompletedIncrement) {
        String taskKey = TASK_PROGRESS_PREFIX + taskId;
        TaskProgress progress = redisTemplate.opsHashGet(APP_KEY,taskKey, subtaskId, TaskProgress.class);
        if (progress != null) {
            if (null == progress.getCurrentUnits())
                progress.setCurrentUnits(0L);
            progress.setCurrentUnits(progress.getCurrentUnits() + unitsCompletedIncrement);
            redisTemplate.opsHashPut(APP_KEY,taskKey, subtaskId, progress);
        }
    }

    // 获取某个子任务的进度
    public TaskProgress getSubtaskProgress(String taskId, String subtaskId) {
        String taskKey = TASK_PROGRESS_PREFIX + taskId;
        return redisTemplate.opsHashGet(APP_KEY,taskKey, subtaskId, TaskProgress.class);
    }

    // 获取所有子任务的进度
    public List<TaskProgress> getAllSubtaskProgress(String taskId) {
        String taskKey = TASK_PROGRESS_PREFIX + taskId;
        return redisTemplate.opsHashValues(APP_KEY,taskKey, TaskProgress.class);
    }

    // 删除任务
    public void deleteTaskProgress(String taskId) {
        String taskKey = TASK_PROGRESS_PREFIX + taskId;
        redisTemplate.opsHashDelete(APP_KEY,taskKey);
    }

    // 删除子任务
    public void deleteSubTaskProgress(String taskId,String subtaskId) {
        String taskKey = TASK_PROGRESS_PREFIX + taskId;
        redisTemplate.opsHashDelete(APP_KEY,taskKey,subtaskId);
    }

    /**
     * 使用示例
     *
     * 假设你有一个任务 task1，包含两个子任务 subtask1 和 subtask2，并且你想要管理这些子任务的进度。
     *
     * 初始化任务进度：
     *
     * Map<String, TaskProgress> taskProgressMap = new HashMap<>();
     * taskProgressMap.put("subtask1", new TaskProgress(100, 0));
     * taskProgressMap.put("subtask2", new TaskProgress(200, 0));
     *
     * taskProgressManager.initializeTask("task1", taskProgressMap);
     *
     * 更新某个子任务的进度：
     * taskProgressManager.incrementSubtaskProgress("task1", "subtask1", 10);
     *
     * 获取某个子任务的进度：
     *
     * TaskProgress subtask1Progress = taskProgressManager.getSubtaskProgress("task1", "subtask1");
     * System.out.println("Subtask1 Progress: " + subtask1Progress.getCurrentUnits() + "/" + subtask1Progress.getTotalUnits());
     *
     * 获取所有子任务的进度：
     *
     * List<TaskProgress> progressList = taskProgressManager.getAllSubtaskProgress("task1");
     * for (TaskProgress progress : progressList) {
     *     System.out.println("Subtask Progress: " + progress.getCurrentUnits() + "/" + progress.getTotalUnits());
     * }
     *
     * 删除任务进度：
     *
     * taskProgressManager.deleteTaskProgress("task1");
     */



}
