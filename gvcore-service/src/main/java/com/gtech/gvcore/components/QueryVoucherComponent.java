package com.gtech.gvcore.components;

import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.request.outlet.GetOutletRequest;
import com.gtech.gvcore.dao.dto.VoucherDto;
import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.OutletService;
import com.gtech.gvcore.service.TransactionDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.tomcat.util.threads.ThreadPoolExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2023/6/12 10:15
 */
@Slf4j
@Component
public class QueryVoucherComponent {


    @Autowired
    private VoucherMapper voucherMapper;

    @Autowired
    private TransactionDataService transactionDataService;

    @Autowired
    private GvCodeHelper gvCodeHelper;

    @Autowired
    private OutletService outletService;

    @Value("${gvcore.voucher.querySize:500}")
    private Integer querySize;


    private static final ThreadPoolExecutor executor = new ThreadPoolExecutor(20, 200, 600, TimeUnit.SECONDS, new LinkedBlockingDeque<>(5000),
            new ThreadPoolExecutor.CallerRunsPolicy()){
        @Override
        protected void afterExecute(Runnable r, Throwable t) {
            super.afterExecute(r, t);

            if (t != null) {
                // 在主线程中打印异常
                log.error("Exception occurred in custom thread pool:");
                t.printStackTrace();
            }
        }

    };
    public Result<String> queryVoucherByVoucherCodesAndInsertTransactionData(List<String> voucherCodes, CustomerOrder customerOrder, String approvalCode, TransactionTypeEnum typeEnum) {

        long start = System.currentTimeMillis();
        Map<Integer, List<String>> listHashMap = getListHashMapByVoucherCodes(voucherCodes);
        //获取listHashMap的value值 不为空的数量
        Integer count = (int) listHashMap.values().stream().filter(e -> e.size() > 0).count();


        CountDownLatch countDownLatch = new CountDownLatch(count);

        log.info("待完成线程数：{}",countDownLatch.getCount());

        queryVoucherAndInsertTransactionData(customerOrder, approvalCode, typeEnum, listHashMap, countDownLatch);

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            log.error("线程异常",e);
            throw new RuntimeException(e);
        }
        long end = System.currentTimeMillis();
        //耗时
        System.out.println("耗时：" + (end - start) / 1000 + "秒");


        return Result.ok();
    }



    public Result<String> insertTransactionDataByVoucherList(List<Voucher> voucherList, CustomerOrder customerOrder, String approvalCode, TransactionTypeEnum typeEnum) {

        long start = System.currentTimeMillis();
        Map<Integer, List<Voucher>> listHashMap = getListHashMapByVoucherList(voucherList);
        //获取listHashMap的value值 不为空的数量
        Integer count = (int) listHashMap.values().stream().filter(e -> e.size() > 0).count();


        CountDownLatch countDownLatch = new CountDownLatch(count);

        log.info("待完成线程数：{}",countDownLatch.getCount());

        insertTransactionDataByVoucherList(customerOrder, approvalCode, typeEnum, listHashMap, countDownLatch);

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            log.error("线程异常",e);
            throw new RuntimeException(e);
        }
        long end = System.currentTimeMillis();
        //耗时
        System.out.println("耗时：" + (end - start) / 1000 + "秒");


        return Result.ok();
    }


    private void queryVoucherAndInsertTransactionData(CustomerOrder customerOrder, String approvalCode, TransactionTypeEnum typeEnum, Map<Integer, List<String>> listHashMap, CountDownLatch countDownLatch) {
        listHashMap.forEach((k, v) -> {
            executor.execute(()->{
                try {
                    ListUtils.partition(v, querySize)
                            .parallelStream()
                            .forEach(e ->{
                                VoucherDto voucherDto = new VoucherDto();
                                voucherDto.setTableName("gv_voucher_"+k);
                                voucherDto.setVoucherCodeList(e);
                                List<Voucher> vouchers = voucherMapper.queryByVoucherCodeListByTableName(voucherDto);
                                Integer transactionData = createTransactionData(customerOrder, vouchers, approvalCode, typeEnum);
                                log.info("已经插入交易记录数据条数：{}",transactionData);
                            });
                } catch (Exception e) {
                    log.error("插入交易记录异常",e);
                    throw new RuntimeException(e);
                } catch (Error error){
                    log.error("插入交易记录异常",error);
                    throw new RuntimeException(error);
                }finally {
                    countDownLatch.countDown();
                    log.info("插入交易记录剩余未完成线程数：{}", countDownLatch.getCount());
                }

            });
        });
    }


    /**
     * 根据voucherCodeList分组插入交易记录
     * @param customerOrder
     * @param approvalCode
     * @param typeEnum
     * @param listHashMap
     * @param countDownLatch
     */
    private void insertTransactionDataByVoucherList(CustomerOrder customerOrder, String approvalCode, TransactionTypeEnum typeEnum, Map<Integer, List<Voucher>> listHashMap, CountDownLatch countDownLatch) {
        listHashMap.forEach((k, v) -> {
            executor.execute(()->{
                try {
                    ListUtils.partition(v, querySize)
                            .parallelStream()
                            .forEach(e ->{
                                Integer transactionData = createTransactionData(customerOrder, e, approvalCode, typeEnum);
                                log.info("已经插入交易记录数据条数：{}",transactionData);
                            });
                } catch (Exception e) {
                    log.error("插入交易记录异常",e);
                    throw new RuntimeException(e);
                } catch (Error error){
                    log.error("插入交易记录异常",error);
                    throw new RuntimeException(error);
                }finally {
                    countDownLatch.countDown();
                    log.info("插入交易记录剩余未完成线程数：{}", countDownLatch.getCount());
                }

            });
        });
    }






    public static Map<Integer, List<String>> getListHashMapByVoucherCodes(List<String> voucherCodes) {
        int n = voucherCodes.size();
        ConcurrentMap<Integer, List<String>> listHashMap = new ConcurrentHashMap<>(64);
        voucherCodes.parallelStream().forEach(item -> {
            long index = Long.parseLong(item) % 64;
            List<String> strings = listHashMap.computeIfAbsent((int) index, k -> new CopyOnWriteArrayList<>());
            strings.add(item);
        });
        return listHashMap;
    }


    private static Map<Integer, List<Voucher>> getListHashMapByVoucherList(List<Voucher> voucherList) {
        int n = voucherList.size();
        ConcurrentHashMap<Integer, List<Voucher>> listHashMap = new ConcurrentHashMap<>(64);
        voucherList.parallelStream().forEach(item -> {
            long index = Long.parseLong(item.getVoucherCode()) % 64;
            List<Voucher> strings = listHashMap.computeIfAbsent((int) index, k -> new CopyOnWriteArrayList<>());
            strings.add(item);
        });
        return listHashMap;
    }


    public Result<String> queryVoucherAndUpdateVoucherStatus(String voucherOwnerCode, String voucherOwnerType, List<String> voucherCodeList, Integer voucherStatus, Integer circulationStatus, Integer status) {

        long start = System.currentTimeMillis();
        Map<Integer, List<String>> listHashMap = getListHashMapByVoucherCodes(voucherCodeList);
        //获取listHashMap的value值 不为空的数量
        Integer count = (int) listHashMap.values().stream().filter(e -> e.size() > 0).count();


        CountDownLatch countDownLatch = new CountDownLatch(count);

        log.info("待完成线程数：{}",countDownLatch.getCount());


        updateVoucherStatusByVoucherCodeList(voucherOwnerCode, voucherOwnerType, voucherCodeList, voucherStatus, circulationStatus, status, listHashMap, countDownLatch);

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            log.error("线程异常",e);
            throw new RuntimeException(e);
        } catch (Exception e) {
            log.error("线程异常",e);
            throw new RuntimeException(e);
        } catch (Throwable e) {
            log.error("线程异常",e);
            throw new RuntimeException(e);
        }
        long end = System.currentTimeMillis();
        //耗时
        System.out.println("耗时：" + (end - start) / 1000 + "秒");


        return Result.ok();
    }

    private void updateVoucherStatusByVoucherCodeList(String voucherOwnerCode, String voucherOwnerType, List<String> voucherCodeList, Integer voucherStatus, Integer circulationStatus, Integer status, Map<Integer, List<String>> listHashMap, CountDownLatch countDownLatch) {
        listHashMap.forEach((k, v) -> {
            executor.execute(()->{
                try {
                    ListUtils.partition(v, querySize)
                            .forEach(e ->{
                                voucherMapper.updateVoucherStatusByTableName(voucherOwnerCode, voucherOwnerType, voucherCodeList, voucherStatus,
                                        circulationStatus, status,"gv_voucher_"+k);
                                log.info("已经修改券记录：{}",e.size());
                            });
                } catch (Exception e) {
                    log.error("修改券记录异常",e);
                    throw new RuntimeException(e);
                } catch (Error error){
                    log.error("修改券记录异常",error);
                }finally {
                    countDownLatch.countDown();
                    log.info("修改券记录剩余未完成线程数：{}", countDownLatch.getCount());
                }
            });
        });
    }


    private Integer createTransactionData(CustomerOrder customerOrder, List<Voucher> voucherList, String approvalCode, TransactionTypeEnum typeEnum) {
        ArrayList<TransactionData> transactionDatas = new ArrayList<>();

        String merchantCode = outletService.getOutlet(GetOutletRequest.builder().outletCode(customerOrder.getOutletCode()).build()).getMerchantCode();

        voucherList.forEach(x->{
            //插入transactionData
            TransactionData transactionData = new TransactionData();
            transactionData.setTransactionId(customerOrder.getCustomerOrderCode());
            transactionData.setApproveCode(approvalCode);
            transactionData.setTransactionType(typeEnum.getCode());
            transactionData.setMerchantCode(merchantCode);
            transactionData.setIssuerCode(customerOrder.getIssuerCode());
            transactionData.setBatchId("");
            transactionData.setBillNumber("");
            transactionData.setOutletCode(customerOrder.getOutletCode());
            transactionData.setCpgCode(x.getCpgCode());
            transactionData.setTransactionDate(new Date());
            transactionData.setVoucherCode(x.getVoucherCode());
            transactionData.setVoucherCodeNum(Long.valueOf(x.getVoucherCode().replaceAll("[a-zA-Z]","")));
            transactionData.setInitiatedBy("");
            transactionData.setPosCode("");
            transactionData.setBatchCode("");
            transactionData.setLoginSource("");
            transactionData.setDenomination(x.getDenomination());
            transactionData.setPaidAmount(new BigDecimal("0"));
            transactionData.setPaymentMethod("");
            transactionData.setDiscountAmount(new BigDecimal("0"));
            transactionData.setActualOutlet("");
            transactionData.setCardEntryMode("GV POS");
            transactionData.setMopCode(x.getMopCode());
            transactionData.setForwardingEntityId("");
            transactionData.setResponseMessage("Transaction successful.");
            transactionData.setTransactionMode("");
            transactionData.setCorporateName("");
            transactionData.setDepartmentDivisionBranch("");
            transactionData.setCustomerSalutation("");
            transactionData.setCustomerFirstName("");
            transactionData.setCustomerLastName("");
            transactionData.setMobile("");
            transactionData.setEmail("");
            transactionData.setInvoiceNumber(customerOrder.getInvoiceNo());
            transactionData.setOtherInputParameter("{}");
            transactionData.setCustomerType("");
            transactionData.setSuccessOrFailure("0");
            transactionData.setPurchaseOrderNo(customerOrder.getPurchaseOrderNo());
            transactionData.setVoucherEffectiveDate(x.getVoucherEffectiveDate());
            transactionData.setCreateUser("");
            transactionData.setCreateTime(new Date());
            transactionData.setUpdateUser("");
            transactionData.setUpdateTime(new Date());
            transactionData.setCustomerCode(customerOrder.getCustomerCode());
            transactionData.setReferenceNumber(customerOrder.getCustomerOrderCode());
            transactionData.setReferenceNumber(gvCodeHelper.generateReferenceNumber());
            transactionDatas.add(transactionData);
        });
        return transactionDataService.insertList(transactionDatas);
    }











}
