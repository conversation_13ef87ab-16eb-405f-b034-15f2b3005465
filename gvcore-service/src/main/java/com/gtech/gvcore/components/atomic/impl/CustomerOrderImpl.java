package com.gtech.gvcore.components.atomic.impl;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.CustomerOrderStatusEnum;
import com.gtech.gvcore.common.request.customerorder.CreateCustomerOrderDetailsRequest;
import com.gtech.gvcore.common.request.customerorder.CreateCustomerOrderRequest;
import com.gtech.gvcore.common.request.transaction.CustomerInfo;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.components.atomic.VoucherStatusStrategy;
import com.gtech.gvcore.components.atomic.request.AtomicCustomerOrderRequest;
import com.gtech.gvcore.components.atomic.request.AtomicRequest;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.CustomerOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Component
public class CustomerOrderImpl implements VoucherStatusStrategy {

    public static final String API_CUSTOMER = "api customer";

    @Autowired
    private CustomerOrderService customerOrderService;

    private AtomicRequest request;


    public CustomerOrderImpl() {
    }

    public CustomerOrderImpl(AtomicRequest request) {
        this.request = request;
    }

    @Override
    public int execute(AtomicRequest request) {
        if (null == request){
            request = this.request;
        }

        AtomicCustomerOrderRequest customerOrderRequest = request.getCustomerOrderRequest();
        customerOrderAction(customerOrderRequest.getVoucher(),
                customerOrderRequest.getInvoiceNumber(),
                customerOrderRequest.getOutletResponse(),
                customerOrderRequest.getTransactionId(),
                customerOrderRequest.getNumOfCards(),
                customerOrderRequest.getDetailsRequestList(),
                customerOrderRequest.getTotalAmount());
        return 0;
    }


    private void customerOrderAction(Voucher voucher,
                                     String invoiceNumber,
                                     OutletResponse outletResponse,
                                     String transactionId,
                                     Integer numOfCards,
                                     List<CreateCustomerOrderDetailsRequest> detailsRequestList,
                                     BigDecimal totalAmount

    ) {
        createCustomerOrderByCreateAndIssue(invoiceNumber,
                outletResponse,
                null,
                voucher,
                transactionId,
                ""
                , numOfCards,
                detailsRequestList,
                totalAmount);
    }

    public CreateCustomerOrderRequest createCustomerOrderByCreateAndIssue(String invoiceNumber,
                                                                          OutletResponse outlet,
                                                                          CustomerInfo customerInfo,
                                                                          Voucher voucher,
                                                                          String transactionId,
                                                                          String notes,
                                                                          Integer numOfCards,
                                                                          List<CreateCustomerOrderDetailsRequest> detailsRequestList,
                                                                          BigDecimal voucherAmount) {
        CreateCustomerOrderRequest customerOrderRequest = new CreateCustomerOrderRequest();
        customerOrderRequest.setCustomerOrderCode(transactionId);
        customerOrderRequest.setIssuerCode(voucher.getIssuerCode());
        customerOrderRequest.setOutletCode(outlet.getOutletCode());
        customerOrderRequest.setInvoiceNo(invoiceNumber);
        customerOrderRequest.setPurchaseOrderNo(outlet.getOutletName() + System.currentTimeMillis());
        customerOrderRequest.setMopCode(voucher.getMopCode());
        customerOrderRequest.setVoucherNum(numOfCards);
        customerOrderRequest.setVoucherAmount(voucherAmount);
        customerOrderRequest.setDiscount(new BigDecimal("0"));
        customerOrderRequest.setAmount(BigDecimal.ZERO);
        if (voucher.getMopCode().equals(GvcoreConstants.MOP_CODE_VCE)) {
            customerOrderRequest.setVoucherBatchCode(voucher.getVoucherBatchCode());
        }

        customerOrderRequest.setContactFirstName(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getFirstName()) ?
                customerInfo.getFirstName() : API_CUSTOMER);
        customerOrderRequest.setContactLastName(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getLastName()) ?
                customerInfo.getLastName() : API_CUSTOMER);
        customerOrderRequest.setContactPhone(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getMobile()) ?
                customerInfo.getMobile() : API_CUSTOMER);
        customerOrderRequest.setCompanyName(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getCorporatename()) ?
                customerInfo.getCorporatename() : API_CUSTOMER);
        customerOrderRequest.setContactEmail(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getEmail()) ?
                customerInfo.getEmail() : API_CUSTOMER);

        customerOrderRequest.setCurrencyCode(API_CUSTOMER);
        customerOrderRequest.setCustomerCode(API_CUSTOMER);
        customerOrderRequest.setCustomerName(API_CUSTOMER);
        customerOrderRequest.setCustomerType(API_CUSTOMER);
        customerOrderRequest.setProductCategoryCode("");
        customerOrderRequest.setDiscountType("");
        customerOrderRequest.setCreateUser("api");
        customerOrderRequest.setMeansOfPaymentCode("");
        customerOrderRequest.setShippingAddress("");
        customerOrderRequest.setCustomerRemarks(notes);
        customerOrderRequest.setStatus(CustomerOrderStatusEnum.API.getStatus());
        customerOrderRequest.setReleaseTime(new Date());

        //detailsRequestList将相同cpgCode的数据的voucherNum相加
        Map<String, CreateCustomerOrderDetailsRequest> map = new HashMap<>();
        for (CreateCustomerOrderDetailsRequest detailsRequest : detailsRequestList) {
            if (map.containsKey(detailsRequest.getCpgCode())) {
                CreateCustomerOrderDetailsRequest createCustomerOrderDetailsRequest = map.get(detailsRequest.getCpgCode());
                createCustomerOrderDetailsRequest.setVoucherNum(createCustomerOrderDetailsRequest.getVoucherNum() + detailsRequest.getVoucherNum());
            } else {
                map.put(detailsRequest.getCpgCode(), detailsRequest);
            }
        }
        //map转list
        List<CreateCustomerOrderDetailsRequest> list = new ArrayList<>();
        for (Map.Entry<String, CreateCustomerOrderDetailsRequest> entry : map.entrySet()) {
            list.add(entry.getValue());
        }

        customerOrderRequest.setCreateCustomerOrderDetailsRequests(list);
        Result<String> customerOrder = customerOrderService.createCustomerOrder(customerOrderRequest);
        if (!customerOrder.isSuccess()) {
            throw new GTechBaseException(customerOrder.getCode(), customerOrder.getMessage());
        }
        return customerOrderRequest;
    }



}
