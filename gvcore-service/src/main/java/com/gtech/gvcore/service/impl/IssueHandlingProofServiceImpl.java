package com.gtech.gvcore.service.impl;

import java.util.Collections;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.gtech.gvcore.dao.mapper.IssueHandlingProofMapper;
import com.gtech.gvcore.dao.model.IssueHandlingProof;
import com.gtech.gvcore.service.IssueHandlingProofService;

@Service
public class IssueHandlingProofServiceImpl implements IssueHandlingProofService {
	
	@Autowired
	private IssueHandlingProofMapper issueHandlingProofMapper;

	@Transactional(rollbackFor = Exception.class)
	@Override
	public int insertList(List<IssueHandlingProof> proofList) {
		
		return issueHandlingProofMapper.insertList(proofList);
	}

	@Override
	public List<IssueHandlingProof> queryByIssueHandlingCode(String issueHandlingCode) {
		
		IssueHandlingProof handlingProof = new IssueHandlingProof();
		handlingProof.setIssueHandlingCode(issueHandlingCode);
		List<IssueHandlingProof> list = issueHandlingProofMapper.select(handlingProof);
		if(CollectionUtils.isEmpty(list)) {
			return Collections.emptyList();
		}
		return list;
	}

	@Transactional(rollbackFor = Exception.class)
    @Override
    public int deleteByIssueHandlingCode(String issueHandlingCode) {
        
        if(StringUtils.isBlank(issueHandlingCode)) {
            return 0;
        }
        
        IssueHandlingProof handlingProof = new IssueHandlingProof();
        handlingProof.setIssueHandlingCode(issueHandlingCode);
        return issueHandlingProofMapper.delete(handlingProof);
    }
	
}
