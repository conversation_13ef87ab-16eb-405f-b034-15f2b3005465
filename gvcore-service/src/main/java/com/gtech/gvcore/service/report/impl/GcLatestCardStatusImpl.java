package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.GcTransactionTypeEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.mapper.GcReportBusinessMapper;
import com.gtech.gvcore.dao.model.Company;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.giftcard.infrastructure.entity.GiftCardEntity;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.giftcard.util.GiftCardStatusCalculator;
import com.gtech.gvcore.helper.GvPageHelper;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.PollReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.voucherstatus.ReportVoucherStatusConvertSupport;
import com.gtech.gvcore.service.report.impl.bean.GcLatestGvStatusBean;
import com.gtech.gvcore.service.report.impl.param.GcLatestStatusQueryData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName LatestGvStatusEnhancedImpl
 * @Description Latest Gift Card Status Enhanced Report Implementation
 * <AUTHOR>
 * @Date 2025-06-17
 * @Version V1.0
 **/
@Slf4j
@Service
public class GcLatestCardStatusImpl extends ReportSupport
        implements BusinessReport<GcLatestStatusQueryData, GcLatestGvStatusBean>, PollReport, ReportVoucherStatusConvertSupport {

    @Autowired
    private GcReportBusinessMapper reportBusinessMapper;

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_LATEST_GV_STATUS_REPORT;
    }

    @Override
    public GcLatestStatusQueryData builderQueryParam(CreateReportRequest reportParam) {
        GcLatestStatusQueryData queryData = new GcLatestStatusQueryData();

        // Set CPG codes
        queryData.setCpgCodeList(reportParam.getCpgCodes());

        // Set gift card status list
        queryData.setCardStatusList(reportParam.getVoucherStatus());

        // Set gift card numbers from uploaded file (pre-parsed)
        Set<String> cardNumbers = new HashSet<>();
        if (reportParam.getVoucherCode() != null) {
            cardNumbers.addAll(Arrays.asList(reportParam.getVoucherCode().split(",")));
        }
        if (CollectionUtils.isNotEmpty(reportParam.getVoucherCodeList())) {
            cardNumbers.addAll(reportParam.getVoucherCodeList());
        }
        queryData.setCardNumberList(new ArrayList<>(cardNumbers));
        return queryData;
    }

    @Override
    public List<GcLatestGvStatusBean> getExportData(GcLatestStatusQueryData queryData) {
        // Get gift card data
        List<GiftCardEntity> giftCardList = getGiftCardList(queryData);

        // filter outlet code list
        if (CollectionUtils.isEmpty(giftCardList)) {
            return Collections.emptyList();
        }

        // Batch query for latest transaction types and dates
        List<String> cardNumbers = giftCardList.stream()
                .map(GiftCardEntity::getCardNumber)
                .collect(Collectors.toList());

        Map<String, Map<String, String>> transactionDataMap = getLatestTransactionDataBatch(cardNumbers);

        giftCardList.forEach(x -> {
            String outletCode = transactionDataMap.get(x.getCardNumber()).get("outletCode");
            if (outletCode != null) {
                x.setSalesOutlet(outletCode);
            }
        });

        // Get related CPG data
        JoinDataMap<Outlet> outletMap = super.getMapByCode(giftCardList, GiftCardEntity::getSalesOutlet, Outlet.class);
        JoinDataMap<Merchant> merchantMap = super.getMapByCode(outletMap.values(), Outlet::getMerchantCode, Merchant.class);
        JoinDataMap<GcCpg> cpgMap = super.getMapByCode(giftCardList, GiftCardEntity::getCpgCode, GcCpg.class);
        JoinDataMap<Company> companyMap = super.getMapByCode(merchantMap.values(), Merchant::getCompanyCode, Company.class);


        // Convert to report beans
        return giftCardList.stream().map(giftCard -> {
            GcLatestGvStatusBean bean = new GcLatestGvStatusBean();

            // Set basic gift card info
            bean.setVoucherNumber(giftCard.getCardNumber());
            String status = GiftCardStatusCalculator.builder().cardStatus(giftCard.getStatus())
                    .activationDeadline(giftCard.getActivationDeadline())
                    .expiryTime(giftCard.getExpiryTime())
                    .managementStatus(giftCard.getManagementStatus())
                    .activationExtensionCount(giftCard.getActivationExtensionCount()).build().determineCardStatus();
            bean.setVoucherStatus(status);


            // Format dates
            if (giftCard.getCreateTime() != null) {
                bean.setIssuanceDate(DateUtil.format(giftCard.getSalesTime(), "yyyy-MM-dd HH:mm:ss"));
            }
            if (giftCard.getExpiryTime() != null) {
                bean.setExpiryDate(DateUtil.format(giftCard.getExpiryTime(), "yyyy-MM-dd HH:mm:ss"));
            }

            Map<String, String> transMap = transactionDataMap.get(giftCard.getCardNumber());

            bean.setTransactionType(transMap.get("transactionType"));
            bean.setLastActionDate(transMap.get("latestTime"));

            bean.setRedeemAmount(toAmount(transMap.get("redeemAmount")));

            // Get CPG info
            Optional<GcCpg> optionalCpg = Optional.ofNullable(cpgMap.get(giftCard.getCpgCode()));
            optionalCpg.ifPresent(cpg -> bean.setVoucherProgramGroup(cpg.getCpgName()));

            // Get merchant info from sales records
            Outlet outlet = outletMap.findValue(giftCard.getSalesOutlet());
            Merchant merchant = merchantMap.findValue(outlet.getMerchantCode());
            final Company company = companyMap.findValue(merchant.getCompanyCode());
            bean.setMerchant(merchant.getMerchantName());
            bean.setSbuCompanyName(company.getCompanyName());
            bean.setCurrentBalance(toAmount(giftCard.getBalance()));
            bean.setOutletName(outlet.getOutletName());
            return bean;
        }).collect(Collectors.toList());
    }

    /**
     * Get gift card list based on query parameters
     */
    private List<GiftCardEntity> getGiftCardList(GcLatestStatusQueryData queryData) {
        return reportBusinessMapper.selectLatestGcStatus(queryData, GvPageHelper.getRowBounds(queryData));
    }

    /**
     * Get latest transaction data for multiple card numbers using batch queries
     *
     * @param cardNumbers List of card numbers to query
     * @return Map of card number to transaction data (type and time)
     */
    private Map<String, Map<String, String>> getLatestTransactionDataBatch(List<String> cardNumbers) {
        if (CollectionUtils.isEmpty(cardNumbers)) {
            return Collections.emptyMap();
        }

        // Split into batches of 500 to avoid too large IN clause
        Map<String, Map<String, String>> result = new HashMap<>();
        int batchSize = 500;

        for (int i = 0; i < cardNumbers.size(); i += batchSize) {
            List<String> batch = cardNumbers.subList(i, Math.min(i + batchSize, cardNumbers.size()));

            // Process batch using the optimized method
            Map<String, Map<String, String>> batchResult = getLatestTransactionDataForBatch(batch);
            result.putAll(batchResult);
        }

        return result;
    }

    /**
     * Process a single batch of card numbers to get latest transaction data
     */
    private Map<String, Map<String, String>> getLatestTransactionDataForBatch(List<String> cardNumbers) {
        Map<String, Map<String, String>> result = new HashMap<>();

        // Initialize result map for all card numbers
        for (String cardNumber : cardNumbers) {
            result.put(cardNumber, new HashMap<>());
        }

        // Query each table separately and find the latest transaction for each card
        processTransactionTable(result, GcTransactionTypeEnum.GIFT_CARD_ACTIVATE.getDesc(),
                () -> reportBusinessMapper.getBatchLatestGcActivationTime(cardNumbers));

        processTransactionTable(result, GcTransactionTypeEnum.GIFT_CARD_SELL.getDesc(),
                () -> reportBusinessMapper.getBatchLatestGcSalesTime(cardNumbers));

        processTransactionTable(result, GcTransactionTypeEnum.GIFT_CARD_REDEEM.getDesc(),
                () -> reportBusinessMapper.getBatchLatestGcRedemptionTime(cardNumbers));

        processTransactionTable(result, GcTransactionTypeEnum.GIFT_CARD_CANCEL_REDEEM.getDesc(),
                () -> reportBusinessMapper.getBatchLatestGcCancelRedemptionTime(cardNumbers));

        processTransactionTable(result, GcTransactionTypeEnum.GIFT_CARD_DEACTIVATE.getDesc(),
                () -> reportBusinessMapper.getBatchLatestGcBlockTime(cardNumbers));

        processTransactionTable(result, GcTransactionTypeEnum.GIFT_CARD_REACTIVATE.getDesc(),
                () -> reportBusinessMapper.getBatchLatestGcUnblockTime(cardNumbers));

        processTransactionTable(result, GcTransactionTypeEnum.GIFT_CARD_CANCEL_SELL.getDesc(),
                () -> reportBusinessMapper.getBatchLatestGcCancelSalesTime(cardNumbers));

        processTransactionTable(result, GcTransactionTypeEnum.GIFT_CARD_ACTIVATION_EXTENSION.getDesc(),
                () -> reportBusinessMapper.getBatchLatestGcExtendActivationTime(cardNumbers));

        return result;
    }

    /**
     * Process a single transaction table and update the result map
     */
    private void processTransactionTable(Map<String, Map<String, String>> result,
                                         String transactionType, java.util.function.Supplier<List<Map<String, String>>> querySupplier) {
        try {
            List<Map<String, String>> tableResults = querySupplier.get();

            for (Map<String, String> tableResult : tableResults) {
                String cardNumber = tableResult.get("cardNumber");
                Date latestTime = DateUtil.parseDate(tableResult.get("latestTime"), "yyyy-MM-dd HH:mm:ss");

                if (cardNumber != null && latestTime != null) {
                    Map<String, String> currentData = result.get(cardNumber);
                    Date currentLatestTime = DateUtil.parseDate(currentData.get("latestTime"), "yyyy-MM-dd HH:mm:ss");

                    // Update if this is the first time or if this time is later
                    if (currentLatestTime == null || latestTime.after(currentLatestTime)) {
                        currentData.put("transactionType", transactionType);
                        currentData.put("latestTime", tableResult.get("latestTime"));
                        currentData.put("outletCode", tableResult.get("outletCode"));
                        currentData.put("redeemAmount", tableResult.get("redeemAmount") == null ? "0" : String.valueOf(tableResult.get("redeemAmount")));
                    }
                }
            }
        } catch (Exception e) {
            log.warn("Failed to query transaction table for type: {}", transactionType, e);
        }
    }
}
