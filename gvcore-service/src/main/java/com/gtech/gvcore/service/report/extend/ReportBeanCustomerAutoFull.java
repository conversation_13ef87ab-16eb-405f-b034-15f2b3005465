package com.gtech.gvcore.service.report.extend;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.common.enums.CustomerTypeEnum;
import com.gtech.gvcore.dao.model.Customer;
import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName ReportBeanCustomerAutoFull
 * @Description 客户信息填充通用逻辑
 *  根据客户类型 自动填充相应字段
 *  需要注意子类实现时字段的命名
 * <AUTHOR>
 * @Date 2023/1/31 11:07
 * @Version V1.0
 **/
public interface ReportBeanCustomerAutoFull {

    default <T extends ReportBeanCustomerAutoFull> T autoFull(Customer customer, Class<T> thisType) {

        if (CustomerTypeEnum.CORPORATE.equalsCode(customer.getCustomerType())) {

            this.setDepartmentDivisionBranch(customer.getContactDivision())
                    .setCustomerFullName(ConvertUtils.toString(customer.getContactFirstName(), StringUtils.EMPTY) + ConvertUtils.toString(customer.getContactLastName(), StringUtils.EMPTY))
                    .setFirstName(ConvertUtils.toString(customer.getContactFirstName(), StringUtils.EMPTY))
                    .setLastName(ConvertUtils.toString(customer.getContactLastName(), StringUtils.EMPTY))
                    .setCompanyName(customer.getCompanyName());

        } else {

            this.setCustomerFullName(customer.getCustomerName())
                    .setFirstName(customer.getCustomerName())
                    .setLastName(customer.getCustomerName());

        }

        this.setCustomerName(customer.getCustomerName())
                .setEmail(customer.getContactEmail())
                .setCustomerMobile(customer.getContactPhone());

        return thisType.cast(this);
    }

    default void autoFull(Customer customer) {
        autoFull(customer, this.getClass());
    }

    default ReportBeanCustomerAutoFull setCustomerMobile(String customerMobile) { return this;}

    default ReportBeanCustomerAutoFull setCustomerName(String customerName) { return this;}

    default ReportBeanCustomerAutoFull setDepartmentDivisionBranch(String departmentDivisionBranch) { return this;}

    default ReportBeanCustomerAutoFull setCustomerFullName(String customerFullName) { return this;}

    default ReportBeanCustomerAutoFull setFirstName(String firstName) { return this;}

    default ReportBeanCustomerAutoFull setLastName(String lastName) { return this;}

    default ReportBeanCustomerAutoFull setEmail(String email) { return this;}

    default ReportBeanCustomerAutoFull setCompanyName(String companyName) { return this;}

}
