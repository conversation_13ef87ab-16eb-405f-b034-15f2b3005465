package com.gtech.gvcore.service.report.impl;

import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.mapper.UserAccountMapper;
import com.gtech.gvcore.dao.model.SysLogger;
import com.gtech.gvcore.dao.model.UserAccount;
import com.gtech.gvcore.service.SystemLoggerService;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.PollReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.context.ReportContextHelper;
import com.gtech.gvcore.service.report.impl.bean.AuditTrailReportBean;
import com.gtech.gvcore.service.report.impl.param.AuditTrailReportQueryData;
import com.gtech.message.util.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:56
 * @Description:
 */
@Service
public class AuditTrailImpl extends ReportSupport
        implements BusinessReport<AuditTrailReportQueryData, AuditTrailReportBean>, PollReport {

    @Autowired
    private SystemLoggerService loggerService;

    @Value("${titan.gv.url:''}")
    private String gvUrl;

    @Autowired
    private UserAccountMapper userAccountMapper;

    @Override
    public AuditTrailReportQueryData builderQueryParam(CreateReportRequest reportParam) {

        // init param
        AuditTrailReportQueryData param = new AuditTrailReportQueryData();

        param.setOperateTimeBegin(reportParam.getOperateTimeBegin());
        param.setOperateTimeEnd(reportParam.getOperateTimeEnd());
        param.setUserCode(reportParam.getOperateUserCode());
        param.setRequestId(reportParam.getSysLoggerId());

        param.setFindEndTime(ReportContextHelper.reportBuilderTime());

        return param;
    }

    @Override
    public List<AuditTrailReportBean> getExportData(AuditTrailReportQueryData queryData) {

        //find logger
        List<SysLogger> loggers = this.loggerService.queryLoggerReport(queryData);
        if (CollectionUtils.isEmpty(loggers)) return new ArrayList<>();

        //user
        Map<String, String> userMap = getUserMap(loggers);

        //init result
        List<AuditTrailReportBean> beans = new ArrayList<>();

        //convert
        loggers.forEach(e -> {

            AuditTrailReportBean auditTrailReportBean = new AuditTrailReportBean();
            auditTrailReportBean.setOperator(userMap.get(e.getUserCode()));
            auditTrailReportBean.setOperatorTime(DateUtil.format(e.getCreateTime(),DateUtil.FORMAT_YYYYMMDDHHMISS));
            auditTrailReportBean.setRequestPath(gvUrl + e.getRequestPath());

            beans.add(auditTrailReportBean.setRequestId(String.valueOf(e.getId())));

        });

        return beans;
    }

    /**
     * get user map
     *
     * @param loggers
     * @return
     */
    private Map<String, String> getUserMap(List<SysLogger> loggers) {

        // get user code
        List<String> userCodeArray = loggers.stream()
                .map(SysLogger::getUserCode)
                .distinct()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        // empty
        if (CollectionUtils.isEmpty(userCodeArray)) return new HashMap<>();

        // find user
        Example exampleUserAccount = new Example(UserAccount.class, true);
        exampleUserAccount.createCriteria().andIn(UserAccount.C_USER_CODE, userCodeArray);
        List<UserAccount> userList = this.userAccountMapper.selectByCondition(exampleUserAccount);

        // to map
        return userList.stream().collect(Collectors.toMap(UserAccount::getUserCode, UserAccount::getFullName));
    }

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.AUDIT_TRAIL_REPORT;
    }

    @Override
    public int pageSize() {
        return 100;
    }
}
