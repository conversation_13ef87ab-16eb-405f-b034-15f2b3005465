package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName GcTransactionsDetailedBean
 * @Description Gift Card Transactions Detailed Bean
 * <AUTHOR>
 * @Date 2025-06-18
 * @Version V1.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class GcTransactionsDetailedBean {

    @ExcelProperty(value = "Transaction Type")
    private String transactionType;

    @ExcelProperty(value = "Merchant")
    private String merchant;

    @ExcelProperty(value = "Outlet")
    private String outlet;

    @ExcelProperty(value = "Transaction Date")
    private String transactionDate;

    @ExcelProperty(value = "Gift Card Number")
    private String cardNumber;

    @ExcelProperty(value = "Gift Card Program Group")
    private String cpgCode;

    @ExcelProperty(value = "Initiated By")
    private String createUser;

    @ExcelProperty(value = "POS Name")
    private String posName;

    @ExcelProperty(value = "Terminal")
    private String terminal;

    @ExcelProperty(value = "Organization Name")
    private String organizationName;

    @ExcelProperty(value = "Batch Number")
    private String batchNumber;

    @ExcelProperty(value = "Login Source")
    private String loginSource;

    @ReportAmountValue
    @ExcelProperty(value = "Denomination", converter = ExportExcelNumberConverter.class)
    private String denomination;

    @ReportAmountValue
    @ExcelProperty(value = "Amount(Redeem/Sales)", converter = ExportExcelNumberConverter.class)
    private String amount;

    @ExcelProperty(value = "Actual Outlet")
    private String actualOutlet;

    @ExcelProperty(value = "Forwarding Actual Id")
    private String forwardingActualId;

    @ExcelProperty(value = "Response Message")
    private String responseMessage;

    @ExcelProperty(value = "Transaction Mode")
    private String transactionMode;

    @ExcelProperty(value = "SBU/BUYER NAME")
    private String buyerName;

    @ExcelProperty(value = "Customer First Name")
    private String customerFirstName;

    @ExcelProperty(value = "Customer Last Name")
    private String customerLastName;

    @ExcelProperty(value = "Mobile")
    private String mobile;

    @ExcelProperty(value = "Invoice Number")
    private String invoiceNumber;

    @ExcelProperty(value = "Other Input Parameter")
    private String otherInputParameter;
}
