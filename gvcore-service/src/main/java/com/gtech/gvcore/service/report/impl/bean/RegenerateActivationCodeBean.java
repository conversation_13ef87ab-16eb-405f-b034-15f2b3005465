package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName VoucherReturnAndTransferBean
 * @Description voucher return and transfer bean
 * <AUTHOR>
 * @Date 2023/2/14 19:17
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class RegenerateActivationCodeBean {

    /**
     * Request ID
     * Voucher Number
     * Transaction Date(Request 申请日期）
     * Customer Email
     * Voucher Program Group
     * Denomination
     */
    @ExcelProperty(value = "Request #")
    private String issueHandlingCode;
    @ExcelProperty(value = "Voucher Number")
    private String voucherCode;
    @ExcelProperty(value = "Transaction Date")
    private Date transactionDate;
    @ExcelProperty(value = "Customer Email")
    private String receiverEmail;
    @ExcelProperty(value = "Voucher Program Group")
    private String voucherProgramGroup;
    @ExcelProperty(value = "Denomination")
    private BigDecimal denomination;



}