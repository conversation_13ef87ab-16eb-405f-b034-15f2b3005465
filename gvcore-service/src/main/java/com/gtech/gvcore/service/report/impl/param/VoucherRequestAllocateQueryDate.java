package com.gtech.gvcore.service.report.impl.param;

import com.gtech.commons.page.PageParam;
import com.gtech.gvcore.service.report.ReportQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * @ClassName VoucherRequestAllocateReceiveParam
 * @Description
 * <AUTHOR>
 * @Date 2023/2/10 16:53
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class VoucherRequestAllocateQueryDate extends PageParam implements ReportQueryParam {

    private Date createTimeStart;

    private Date createTimeEnd;

    private List<String> issuerCodeList;

    private List<String> cpgCodeList;

    private List<String> allocateStatusList;

    private String requestNo;

    private List<String> requestSourceList;

}