package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.service.report.extend.poll.GroupNewTransactionByVoucherCodeSupport;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @ClassName SalesBo
 * @Description sales report bo
 * <AUTHOR>
 * @Date 2023/3/22 14:52
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GcSalesBo implements GroupNewTransactionByVoucherCodeSupport {

    private String salesCode;
    private String cardNumber;
    private String cpgCode;
    private String issuerCode;
    private String merchantCode;
    private String outletCode;
    private String customerCode;
    private String invoiceNumber;
    private String purchaseOrderNumber;
    private Date salesTime;
    private Date cancelTime;
    private BigDecimal denomination;


    private String transactionCode;
    private String transactionDate;
    private BigDecimal transactionNumber;

    private String notes;
    private String approvalCode;

    public void setTransactionDate(String transactionDate){
        String format = DateUtil.format(DateUtil.parseDate(transactionDate, DateUtil.FORMAT_YYYYMMDDHHMISS), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        this.transactionNumber = new BigDecimal(format);
        this.transactionDate = transactionDate;
    }

    public String getVoucherCode(){
        return cardNumber;
    }



    public Date getTransactionDate() {
        return DateUtil.parseDate(transactionDate, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }
}
