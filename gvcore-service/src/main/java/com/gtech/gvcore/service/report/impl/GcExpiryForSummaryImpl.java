package com.gtech.gvcore.service.report.impl;

import com.gtech.gvcore.common.enums.CustomerTypeEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.GcExpiryForSummaryBean;
import com.gtech.gvcore.service.report.impl.bo.GcExpiryBo;
import com.gtech.gvcore.service.report.impl.param.GcExpiryQueryData;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:57
 * @Description: Gift Card Expiry Report Summary Implementation
 */
@Service
public class GcExpiryForSummaryImpl extends ReportSupport
        implements BusinessReport<GcExpiryQueryData, GcExpiryForSummaryBean>, SingleReport {

    @Override
    public GcExpiryQueryData builderQueryParam(CreateReportRequest reportParam) {

        GcExpiryQueryData queryData = new GcExpiryQueryData();

        queryData.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);

        // 过期时间范围
        queryData.setExpiryDateStart(reportParam.getVoucherEffectiveDateStart());
        queryData.setExpiryDateEnd(reportParam.getVoucherEffectiveDateEnd());

        queryData.setCpgCodeList(reportParam.getCpgCodes());
        queryData.setCustomerCodeList(reportParam.getCustomerCodes());
        if (StringUtils.isNotBlank(reportParam.getVoucherCode())) {
            queryData.setVoucherCode(Arrays.asList(reportParam.getVoucherCode().split(",")));
        }
        queryData.setPurchaseDateStart(reportParam.getPurchaseDateStart());
        queryData.setPurchaseDateEnd(reportParam.getPurchaseDateEnd());

        // 激活时间范围
        queryData.setActivationTimeStart(reportParam.getActivationDateStart());
        queryData.setActivationTimeEnd(reportParam.getActivationDateEnd());
        queryData.setInvoiceNumber(reportParam.getInvoiceNo());

        return queryData;
    }

    @Override
    public List<GcExpiryForSummaryBean> getExportData(GcExpiryQueryData queryData) {
        List<GcExpiryBo> boList = new ArrayList<>();
        PollPageHelper.pollSelect(gcReportBusinessMapper::selectGcExpiry, queryData, boList::addAll);
        //find
        Collection<GcExpirySummaryStatisticalBo> list =
                Optional.of(boList)
                        .map(e -> e.stream()
                                .collect(Collectors.toMap(
                                        GcExpirySummaryStatisticalBo::groupKey,
                                        GcExpirySummaryStatisticalBo::convert,
                                        GcExpirySummaryStatisticalBo::merge))
                        ).map(Map::values)
                        .orElseGet(Collections::emptyList);

        //empty
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        final JoinDataMap<GcCpg> cpgMap = super.getMapByCode(list, GcExpirySummaryStatisticalBo::getCpgCode, GcCpg.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(list, GcExpirySummaryStatisticalBo::getOutletCode, Outlet.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(outletMap.values(), Outlet::getMerchantCode, Merchant.class);
        final JoinDataMap<Customer> customerMap = super.getMapByCode(list, GcExpirySummaryStatisticalBo::getOwnerCustomer, Customer.class);

        //convert result
        List<GcExpiryForSummaryBean> collect = list.stream()
                .map(e -> {

                    Customer value = customerMap.findValue(e.getCustomerCode());
                    String customerName;
                    if (Objects.equals(value.getCustomerType(), CustomerTypeEnum.CORPORATE.code())) {
                        customerName = value.getCompanyName();
                    } else {
                        customerName = value.getCustomerName();
                    }
                    Outlet outlet = outletMap.findValue(e.getOutletCode());
                    return new GcExpiryForSummaryBean()
                            .setVoucherAmount(super.toAmount(e.getTotalAmount()))
                            .setNumberOfVouchers(String.valueOf(e.getNumberOfVouchers()))
                            .setVpg(cpgMap.findValue(e.getCpgCode()).getCpgName())
                            .setMerchant(merchantMap.findValue(outlet.getMerchantCode()).getMerchantName())
                            .setCustomerName(customerName)
                            .setExpiryDate(e.getExpiryDate())
                            .setNotes(e.getNotes());
                })
                .collect(Collectors.toList());
        GcExpiryForSummaryBean bean = new GcExpiryForSummaryBean();
        bean.setMerchant("Total");
        bean.setNumberOfVouchers(list.stream().map(GcExpirySummaryStatisticalBo::getNumberOfVouchers).reduce(0, Integer::sum).toString());
        bean.setVoucherAmount(list.stream().map(GcExpirySummaryStatisticalBo::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add).toString());
        collect.add(bean);
        return collect;
    }

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_EXPIRY_SUMMARY;
    }


    @Getter
    @Setter
    @Accessors(chain = true)
    public static class GcExpirySummaryStatisticalBo {

        /**
         * Outlet code.
         */
        private String outletCode;

        /**
         * Cpg code.
         */
        private String cpgCode;

        /**
         * Number of vouchers.
         */
        private int numberOfVouchers = 1;

        /**
         * Total amount.
         */
        private BigDecimal totalAmount;

        private String customerCode;

        private String ownerCustomer;

        private String expiryDate;

        private String notes;

        private String cardStatus;

        public static GcExpirySummaryStatisticalBo convert(GcExpiryBo expiryBo) {

            return new GcExpirySummaryStatisticalBo()
                    .setOutletCode(expiryBo.getOutletCode())
                    .setCpgCode(expiryBo.getCpgCode())
                    .setTotalAmount(expiryBo.getBalance() != null ? expiryBo.getBalance() : expiryBo.getDenomination())
                    .setCustomerCode(expiryBo.getCustomerCode())
                    .setOwnerCustomer(expiryBo.getOwnerCustomer())
                    .setExpiryDate(com.gtech.commons.utils.DateUtil.format(expiryBo.getExpiryTime(), "yyyy-MM-dd HH:mm:ss"))
                    .setCardStatus(expiryBo.getCardStatus());
//                    .setNotes(generateNotes(expiryBo));
        }

        public GcExpirySummaryStatisticalBo merge(GcExpirySummaryStatisticalBo bo) {

            this.numberOfVouchers += bo.getNumberOfVouchers();
            this.totalAmount = this.totalAmount.add(bo.getTotalAmount());

            return this;
        }

        public static String groupKey(GcExpiryBo expiryBo) {

            return StringUtils.join(",", expiryBo.getOutletCode(), expiryBo.getCpgCode(),
                    expiryBo.getOwnerCustomer(),
                    expiryBo.getExpiryTime());
        }

    }
}
