package com.gtech.gvcore.service.report.impl.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @ClassName GoodsInTransitDetailVoucherBo
 * @Description goods in transit detail bo
 * <AUTHOR>
 * @Date 2023/3/13 17:44
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GoodsInTransitDetailVoucherBo {

    private String requestId;

    private String receiveCode;

    private String outbound;

    private String inbound;

    private String cpgCode;

    private String voucherNumber;

    private String bookletNumber;

    private Date allLocationTime;

    private Date receiveTime;

    private Date expiryDate;

    public static GoodsInTransitDetailVoucherBo copyByGoodsInTransitDetailedImpl(GoodsInTransitDetailBo obj) {

        return new GoodsInTransitDetailVoucherBo()
                .setRequestId(obj.getRequestId())
                .setReceiveCode(obj.getReceiveCode())
                .setOutbound(obj.getOutbound())
                .setInbound(obj.getInbound())
                .setCpgCode(obj.getCpgCode())
                .setAllLocationTime(obj.getAllLocationTime());
    }

}
