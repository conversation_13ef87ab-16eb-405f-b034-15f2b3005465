package com.gtech.gvcore.service.report.impl.support.aging.builder;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.service.report.extend.ReportProportionDataFunction;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.aging.MonthSalesAndContributionBean;
import com.gtech.gvcore.service.report.impl.bo.AgingBo;
import com.gtech.gvcore.service.report.impl.param.AgingReportQueryParamData;
import com.gtech.gvcore.service.report.impl.support.aging.AgingSheet;
import com.gtech.gvcore.service.report.impl.support.aging.AgingSheetBuilder;
import com.gtech.gvcore.service.report.impl.support.aging.bo.MonthSalesAndContributionBo;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName AgingMonthSalesAndContributionReportBuilder
 * @Description Month Sales and Contribution
 *  月份销售 和 占比
 *  月份数据统计以销售时间纬度
 * <AUTHOR>
 * @Date 2022/10/31 19:53
 * @Version V1.0
 **/
@Component
public class MonthSalesAndContributionBuilder implements AgingSheetBuilder, ReportProportionDataFunction {

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.AGING_MONTH_SALES_AND_CONTRIBUTION_REPORT;
    }

    @Override
    public AgingSheet builder(final ReportContext context) {

        final Collection<MonthSalesAndContributionBo> group = this.group(context);

        return new AgingSheet()
                .setHead(null)
                .setSheetName(exportTypeEnum().getSheetName())
                .addSheetData("msc", exportTypeEnum(), this.getResult(group));
    }

    private Collection<MonthSalesAndContributionBo> group(final ReportContext context) {

        final AddFunction addFunction = this.getAddFunction((AgingReportQueryParamData) context.getQueryParam());
        final Map<String, MonthSalesAndContributionBo> resultMap = new HashMap<>();

        //get master sales data
        final List<AgingBo> agingSalesData = context.getCacheList(AgingSheetBuilder.SALES_DATA_KEY);
        final JoinDataMap<Customer> customerMap = context.getCacheJoinMap(AgingSheetBuilder.CUSTOMER_MAP_KEY);

        agingSalesData.stream()
                .filter(e -> customerMap.containsKey(e.getCustomerCode()))
                .forEach(e -> addFunction.add(e.getTransactionDate()
                        , resultMap.computeIfAbsent(e.getCustomerCode(), k -> new MonthSalesAndContributionBo().setCustomerCode(k).setCustomerName(customerMap.get(k).getCustomerName()))));

        return resultMap.values();
    }

    private List<MonthSalesAndContributionBean> getResult(Collection<MonthSalesAndContributionBo> boArray) {

        MonthSalesAndContributionBo total = new MonthSalesAndContributionBo().setCustomerName("Total");
        boArray.forEach(bo -> total.addJan(bo.getJan())
                .addFeb(bo.getFeb())
                .addMar(bo.getMar())
                .addApr(bo.getApr())
                .addMay(bo.getMay())
                .addJun(bo.getJun())
                .addJul(bo.getJul())
                .addAug(bo.getAug())
                .addSep(bo.getSep())
                .addOct(bo.getOct())
                .addNov(bo.getNov())
                .addDec(bo.getDec()));

        List<MonthSalesAndContributionBo> resultBo = new ArrayList<>(boArray);
        resultBo.add(total);

        return resultBo.stream()
                .map(bo -> new MonthSalesAndContributionBean()
                                .setDetail(bo.getCustomerName())
                                .setJan(this.convertValue(bo.getJan()))
                                .setFeb(this.convertValue(bo.getFeb()))
                                .setMar(this.convertValue(bo.getMar()))
                                .setApr(this.convertValue(bo.getApr()))
                                .setMay(this.convertValue(bo.getMay()))
                                .setJun(this.convertValue(bo.getJun()))
                                .setJul(this.convertValue(bo.getJul()))
                                .setAug(this.convertValue(bo.getAug()))
                                .setSep(this.convertValue(bo.getSep()))
                                .setOct(this.convertValue(bo.getOct()))
                                .setNov(this.convertValue(bo.getNov()))
                                .setDec(this.convertValue(bo.getDec()))
                                .setTotal(String.valueOf(bo.getTotal()))
                                .setContribution(getProportion(bo.getTotal(), total.getTotal(), 1))
                        ).collect(Collectors.toList());

    }

    private String convertValue(int val) {

        return 0 == val ? "-" : String.valueOf(val);
    }

    private AddFunction getAddFunction(final AgingReportQueryParamData request) {

        final String beginYear = DateUtil.format(request.getTransactionDateStart(), DateUtil.FORMAT_YYYY);

        final Date janTime = DateUtil.parseDate(beginYear + "02", DateUtil.FORMAT_YYYYMM);
        final Date febTime = DateUtil.parseDate(beginYear + "03", DateUtil.FORMAT_YYYYMM);
        final Date marTime = DateUtil.parseDate(beginYear + "04", DateUtil.FORMAT_YYYYMM);
        final Date aprTime = DateUtil.parseDate(beginYear + "05", DateUtil.FORMAT_YYYYMM);
        final Date mayTime = DateUtil.parseDate(beginYear + "06", DateUtil.FORMAT_YYYYMM);
        final Date junTime = DateUtil.parseDate(beginYear + "07", DateUtil.FORMAT_YYYYMM);
        final Date julTime = DateUtil.parseDate(beginYear + "08", DateUtil.FORMAT_YYYYMM);
        final Date augTime = DateUtil.parseDate(beginYear + "09", DateUtil.FORMAT_YYYYMM);
        final Date sepTime = DateUtil.parseDate(beginYear + "10", DateUtil.FORMAT_YYYYMM);
        final Date octTime = DateUtil.parseDate(beginYear + "11", DateUtil.FORMAT_YYYYMM);
        final Date novTime = DateUtil.parseDate(beginYear + "12", DateUtil.FORMAT_YYYYMM);
        final Date decTime = request.getTransactionDateEnd();

        //add function
        return (saleTime, executeObject) -> {

            if (saleTime.compareTo(janTime) < 0) executeObject.addJan();
            else if (saleTime.compareTo(febTime) < 0) executeObject.addFeb();
            else if (saleTime.compareTo(marTime) < 0) executeObject.addMar();
            else if (saleTime.compareTo(aprTime) < 0) executeObject.addApr();
            else if (saleTime.compareTo(mayTime) < 0) executeObject.addMay();
            else if (saleTime.compareTo(junTime) < 0) executeObject.addJun();
            else if (saleTime.compareTo(julTime) < 0) executeObject.addJul();
            else if (saleTime.compareTo(augTime) < 0) executeObject.addAug();
            else if (saleTime.compareTo(sepTime) < 0) executeObject.addSep();
            else if (saleTime.compareTo(octTime) < 0) executeObject.addOct();
            else if (saleTime.compareTo(novTime) < 0) executeObject.addNov();
            else if (saleTime.compareTo(decTime) < 0) executeObject.addDec();
        };
    }

    public interface AddFunction {

        void add (Date saleTime, MonthSalesAndContributionBo executeObject);
    }

    @Override
    public Class<?> getExportDataClass() {
        return MonthSalesAndContributionBean.class;
    }
}
