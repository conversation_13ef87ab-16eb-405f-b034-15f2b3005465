package com.gtech.gvcore.service.report.base;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.FrequencyEnum;
import com.gtech.gvcore.common.enums.RepeatEndEnum;
import com.gtech.gvcore.dao.mapper.SchedulerReportMapper;
import com.gtech.gvcore.dao.model.SchedulerReport;
import com.gtech.gvcore.service.report.ReportRequestService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.support.CronExpression;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * @ClassName SchedulerReportHelper
 * @Description
 * <AUTHOR>
 * @Date 2023/1/28 15:26
 * @Version V1.0
 **/
@Slf4j
@Component
public class SchedulerReportHelper {

    @Autowired
    private SchedulerReportMapper schedulerReportMapper;


    @Autowired
    private ReportRequestService orderReportRequestService;

    /**
     *
     * 执行指定的定时报表实例
     * 该方法执行实例进行基础检查操作随后调用
     * @see SchedulerReportHelper#execute(SchedulerReport, Date)
     * @param schedulerReport 定时报表实例
     */
    @Transactional
    public void execute(final SchedulerReport schedulerReport) {

        //now 当前时间
        final Date now = new Date();

        log.info("定时报表 [开始执行] => {}", JSON.toJSONString(schedulerReport));

        final boolean executeValidate = this.executeValidate(schedulerReport);

        if (!executeValidate) {
            log.warn("定时报表执行异常 [数据不满足执行条件] 退出=> " + JSON.toJSONString(schedulerReport));
            return;
        }

        this.execute(schedulerReport, now);
    }

    /**
     * 执行方法 - 根据报表实例以及当前时间 向报表组建请求生成一张新的报表
     * @param schedulerReport 定时报表实例
     * @param now 当前时间
     */
    public String execute(final SchedulerReport schedulerReport, final Date now) {

        // 数据做加锁操作 防止 单次[同次]重复执行
        // 乐观锁更新
        final int updateNumber = this.schedulerReportMapper.updateIncreasingNumberOfExecutions(schedulerReport.getId(),
                now, schedulerReport.getNumberOfExecutions());

        //防止如果存在同时执行 跳出当前情况重复数据
        if (updateNumber < 1) return StringUtils.EMPTY;

        log.info("定时报表 [请求创建报表] => {}", JSON.toJSONString(schedulerReport));

        final String result = orderReportRequestService.createReport(schedulerReport);

        log.info("定时报表 [创建报表结果] => {}", JSON.toJSONString(result));
        log.info("定时报表 [执行正常结束] => {}", JSON.toJSONString(schedulerReport));

        return result;
    }


    /**
     * 定时报表执行前置检查 - 根据定时报表实例检查其是否符合执行条件
     * @param schedulerReport 定时报表实例
     * @return 是否允许执行
     */
    private boolean executeValidate(SchedulerReport schedulerReport) {

        log.info("定时报表 [开始验证是否允许执行]");

        //init
        final FrequencyEnum frequencyEnum = FrequencyEnum.valueOfCode(schedulerReport.getFrequency());
        final RepeatEndEnum repeatEndEnum = RepeatEndEnum.valueOfCode(schedulerReport.getRepeatEnd());
        final long lastExecuteTimestamp = null == schedulerReport.getLastExecutionTime() ? 0L : schedulerReport.getLastExecutionTime().getTime();
        final String cron = schedulerReport.getSchedulerCron();
        final String code = schedulerReport.getSchedulerReportCode();

        if (FrequencyEnum.ONCE == frequencyEnum) return executeValidateOnce(schedulerReport, true);

        final LocalDateTime lastTime = this.timestampToLocalDatetime(lastExecuteTimestamp);

        final CronExpression cronExpression = CronExpression.parse(cron);
        final LocalDateTime next = cronExpression.next(lastTime);

        if (next == null) {
            log.error("定时报表验证错误警告！！！！！ json => {}", JSON.toJSONString(schedulerReport));
            return false;
        }

        //执行时间 小于 当前时间 则执行
        boolean executeTimeValidate = !next.isAfter(LocalDateTime.now());

        // 判断任务是否已经结束逻辑
        boolean executeRepeatEndValidate;
        if (RepeatEndEnum.AFTER == repeatEndEnum) executeRepeatEndValidate = executeValidateAfter(schedulerReport, true);
        else if (RepeatEndEnum.ON_DATE == repeatEndEnum) executeRepeatEndValidate = executeValidateOnDate(schedulerReport, true);
        else executeRepeatEndValidate = true;

        log.info("定时报表 [验证是否允许执行执行结果] code => {} executeRepeatEndValidate => {} executeTimeValidate => {}"
                , code, executeRepeatEndValidate, executeTimeValidate);

        return executeRepeatEndValidate && executeTimeValidate;
    }


    /**
     * 定时报表执行检查(按次执行) - 根据定时报表实例检查其是否符合执行条件
     * @param schedulerReport 定时报表实例
     * @param correction 是否进行数据修正
     * @return 是否允许执行
     */
    public boolean executeValidateAfter(final SchedulerReport schedulerReport, final boolean correction) {

        log.info("定时报表 [判断是否满足 AFTER 执行条件] code => {}", schedulerReport.getSchedulerReportCode());

        if (schedulerReport.getNumberOfExecutions() >= schedulerReport.getRepeatEndAfter()) {

            //如果不需要进行修正直接跳出
            if (!correction) return false;

            log.info("定时报表 [任务关闭] AFTER 超出执行次数未禁用 json => {}", JSON.toJSONString(schedulerReport));

            final SchedulerReport updateParam = new SchedulerReport();
            updateParam.setId(schedulerReport.getId());
            updateParam.setExecuteFlag(0);
            this.schedulerReportMapper.updateByPrimaryKeySelective(updateParam);

            return false;
        }

        return true;

    }

    /**
     * 定时报表执行检查(按时间执行) - 根据定时报表实例检查其是否符合执行条件
     * @param schedulerReport 定时报表实例
     * @param correction 是否进行数据修正
     * @return 是否允许执行
     */
    public boolean executeValidateOnDate(final SchedulerReport schedulerReport, final boolean correction) {

        final Date repeatEndTime = schedulerReport.getRepeatEndTime();

        log.info("定时报表 [判断是否满足 ON_DATE 执行条件] code => {} repeatEndTime => {}", schedulerReport.getSchedulerReportCode(), DateUtil.format(repeatEndTime, DateUtil.FORMAT_YYYYMMDDHHMISS));

        //结束时间为空 或者 当前时间超出结束时间
        if (null == repeatEndTime || repeatEndTime.getTime() < System.currentTimeMillis()) {

            //如果不需要进行修正直接跳出
            if (!correction) return false;

            log.warn("定时报表 [任务关闭] ON_DATE 超出结束时间未禁用 json => {}", JSON.toJSONString(schedulerReport));

            final SchedulerReport updateParam = new SchedulerReport();
            updateParam.setId(schedulerReport.getId());
            updateParam.setExecuteFlag(0);
            this.schedulerReportMapper.updateByPrimaryKeySelective(updateParam);

            return false;
        }

        return true;

    }

    /**
     * 定时报表执行检查(一次性执行) - 根据定时报表实例检查其是否符合执行条件
     * @param schedulerReport 定时报表实例
     * @param correction 是否进行数据修正
     * @return 是否允许执行
     */
    public boolean executeValidateOnce (SchedulerReport schedulerReport, boolean correction) {

        log.info("定时报表 [判断是否满足 ONCE 执行条件] code => {}, LastExecutionTime => {}, ExecutionTime=>{}"
                , schedulerReport.getSchedulerReportCode()
                , DateUtil.format(schedulerReport.getLastExecutionTime(), DateUtil.FORMAT_YYYYMMDDHHMISS)
                , DateUtil.format(schedulerReport.getExecutionTime(), DateUtil.FORMAT_YYYYMMDDHHMISS));

        //如果数据被执行过 应对数据进行更新操作
        if (schedulerReport.getLastExecutionTime() != null
                && schedulerReport.getLastExecutionTime().compareTo(schedulerReport.getExecutionTime()) >= 0) {

            if (!correction) return false;

            log.warn("定时报表 [任务关闭] ONCE 超出结束时间未禁用 json => {}", JSON.toJSONString(schedulerReport));

            this.schedulerReportMapper.updateByPrimaryKeySelective(SchedulerReport.builder()
                    .id(schedulerReport.getId())
                    .executeFlag(0).build());

            return false;
        }

        return true;
    }


    private LocalDateTime timestampToLocalDatetime(long timestamp) {

        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
    }

}
