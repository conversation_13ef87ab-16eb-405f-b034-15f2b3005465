package com.gtech.gvcore.service.impl;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.dao.mapper.VoucherAllocationBatchMapper;
import com.gtech.gvcore.dao.model.VoucherAllocationBatch;
import com.gtech.gvcore.service.VoucherAllocationBatchService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年3月11日
 */
@Service
public class VoucherAllocationBatchServiceImpl implements VoucherAllocationBatchService {

    @Autowired
    private VoucherAllocationBatchMapper voucherAllocationBatchMapper;

    //@Transactional(rollbackFor = Exception.class)
    @Override
    public int insertList(List<VoucherAllocationBatch> list) {
        return voucherAllocationBatchMapper.insertList(list);
    }

    @Override
    public List<VoucherAllocationBatch> queryByVoucherAllocationCode(String voucherAllocationCode) {

        if (StringUtils.isBlank(voucherAllocationCode)) {
            return Collections.emptyList();
        }

        VoucherAllocationBatch batch = new VoucherAllocationBatch();
        batch.setVoucherAllocationCode(voucherAllocationCode);
        List<VoucherAllocationBatch> list = voucherAllocationBatchMapper.select(batch);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }

    @Override
    public List<VoucherAllocationBatch> getAllocateByVoucherCode(String voucherCode) {
        return voucherAllocationBatchMapper.getAllocateByVoucherCode(voucherCode);
    }

    @Override
    public List<VoucherAllocationBatch> selectAllocationBatchByVoucherNums(List<String> voucherNumVCR) {
        return voucherAllocationBatchMapper.selectAllocationBatchByVoucherNums(voucherNumVCR);
    }

    @Override
    public List<VoucherAllocationBatch> queryByVoucherNum(final String voucherNumberStart,final String voucherNumberEnd) {

        if (StringUtils.isBlank(voucherNumberStart) && StringUtils.isBlank(voucherNumberEnd)) {
            return Collections.emptyList();
        }

        final Example example = new Example(VoucherAllocationBatch.class);
        final Example.Criteria criteria = example.createCriteria();
        criteria
                .andGreaterThanOrEqualTo(VoucherAllocationBatch.C_VOUCHER_END_NO, ConvertUtils.toString(voucherNumberStart, null))
                .andLessThanOrEqualTo(VoucherAllocationBatch.C_VOUCHER_END_NO, ConvertUtils.toString(voucherNumberEnd, null));

        example.or(example.createCriteria()
                .andGreaterThanOrEqualTo(VoucherAllocationBatch.C_VOUCHER_START_NO, ConvertUtils.toString(voucherNumberStart, null))
                .andLessThanOrEqualTo(VoucherAllocationBatch.C_VOUCHER_START_NO, ConvertUtils.toString(voucherNumberEnd, null))
        );

        return this.voucherAllocationBatchMapper.selectByCondition(example);
    }
}
