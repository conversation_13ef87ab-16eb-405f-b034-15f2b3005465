package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.ReportVoucherStatusEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.common.utils.GvConvertUtils;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.ReportTempLiabilityDStructure;
import com.gtech.gvcore.helper.GvPageHelper;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.PollReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.export.file.FileContext;
import com.gtech.gvcore.service.report.extend.ReportUploadHelper;
import com.gtech.gvcore.service.report.extend.context.ReportContextBuilder;
import com.gtech.gvcore.service.report.extend.context.ReportContextHelper;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.LiabilityDetailedBean;
import com.gtech.gvcore.service.report.impl.param.LiabilityDetailQueryData;
import com.gtech.gvcore.service.report.impl.support.liability.LiabilityDataScript;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 14:10
 * @Description:
 */
@Service
public class LiabilityDetailedImpl extends ReportSupport
        implements BusinessReport<LiabilityDetailQueryData, LiabilityDetailedBean>, PollReport {

    @Autowired private LiabilityDataScript script;

    @Override
    public LiabilityDetailQueryData builderQueryParam(CreateReportRequest reportParam) {

        LiabilityDetailQueryData param = new LiabilityDetailQueryData();

        // issuer merchant outlet
        param.setIssuerCode(reportParam.getIssuerCode());
        param.setMerchantCodeList(reportParam.getMerchantCodes());
        param.setOutletCodeList(reportParam.getOutletCodes());

        //cpg
        param.setCpgCodeList(reportParam.getCpgCodes());

        // voucher status
        param.setReportVoucherStatusList(reportParam.getVoucherStatus());

        // table code
        Date transactionDateStart = reportParam.getTransactionDateStart();
        final String tableCode = DateUtil.format(GvConvertUtils.toObject(transactionDateStart, new Date()), LiabilityDataScript.TABLE_CODE_DATE_FORMAT);
        param.setTableCode(tableCode);
        if (script.existReport(tableCode, LiabilityDataScript.LIABILITY_DETAIL_TABLE_TYPE)) ReportContextHelper.noData();

        return param;
    }

    @Override
    public List<LiabilityDetailedBean> getExportData(LiabilityDetailQueryData param) {

        final List<ReportTempLiabilityDStructure> detailList = reportBusinessMapper.liabilityDetailReport(param, GvPageHelper.getRowBounds(param));

        final JoinDataMap<Cpg> cpgJoinDataMap = super.getMapByCode(detailList, ReportTempLiabilityDStructure::getCpgCode, Cpg.class);

        return detailList.stream()
                .flatMap(e ->
                        Arrays.stream(e.getVoucherCodes().split(","))
                                .filter(StringUtils::isNotBlank)
                                .map(s -> new LiabilityDetailedBean()
                                        .setVoucherNumber(s)
                                        .setVoucherProgramGroup(cpgJoinDataMap.findValue(e.getCpgCode()).getCpgName())
                                        .setDenomination(super.toAmount(e.getDenomination()))
                                        .setExpiryDate(DateUtil.format(e.getEffectiveDate(), DateUtil.FORMAT_DEFAULT))
                                        .setVoucherStatus(ReportVoucherStatusEnum.valueOfCode(e.getVoucherStatus()).getDesc())
                                )
                ).collect(Collectors.toList());
    }

    @Override
    public void customContext(ReportContextBuilder builder) {
        builder.bindFileContext(new LiabilityFileContext());
    }

    public static class LiabilityFileContext implements FileContext {

        private final StringBuilder value = new StringBuilder();

        @Override
        public void init() {
            value.append("Voucher Number").append("\t")
                    .append("Voucher Program Group (VPG)").append("\t")
                    .append("Voucher Status").append("\t")
                    .append("Denomination").append("\t")
                    .append("Expiry Date").append("\n");
        }

        @Override
        public void doFill(List<?> beanList) {

            this.doFillLiabilityDetailBean(BeanCopyUtils.jsonCopyList(beanList, LiabilityDetailedBean.class));

        }

        public void doFillLiabilityDetailBean(List<LiabilityDetailedBean> beanList) {

            beanList.forEach(e -> value.append(e.getVoucherNumber()).append("\t")
                    .append(e.getVoucherProgramGroup()).append("\t")
                    .append(e.getVoucherStatus()).append("\t")
                    .append(e.getDenomination()).append("\t")
                    .append(e.getExpiryDate()).append("\n")
            );
        }

        @Override
        public String finish() {

            final String fileName = ReportExportTypeEnum.LIABILITY_DETAILED_REPORT.getExportName() + ReportContextHelper.findContext().getReportCode() + ".txt";
            final ByteArrayInputStream inputStream = new ByteArrayInputStream(value.toString().getBytes());
            return ReportUploadHelper.fileUpload(inputStream, fileName);
        }
    }

    @Override
    public int pageSize() {
        return 100;
    }


    @Override
    public ReportExportTypeEnum exportTypeEnum() {

        //ENUM
        return ReportExportTypeEnum.LIABILITY_DETAILED_REPORT;
    }

}
