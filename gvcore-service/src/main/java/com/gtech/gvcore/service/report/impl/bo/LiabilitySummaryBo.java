package com.gtech.gvcore.service.report.impl.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName LiabilitySummaryBo
 * @Description 责任概况报表模型
 * <AUTHOR>
 * @Date 2022/7/12 15:58
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class LiabilitySummaryBo {

    /**
     * Issuer code.
     */
    private String issuerCode;

    /**
     * Merchant code.
     */
    private String merchantCode;

    /**
     * Voucher code.
     */
    private String voucherCode;

    /**
     * Cpg code.
     */
    private String cpgCode;


}
