package com.gtech.gvcore.service.impl;

import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.request.poscpg.CreatePosCpgRequest;
import com.gtech.gvcore.common.request.poscpg.DeletePosCpgByPosCodeRequest;
import com.gtech.gvcore.common.response.pos.PosCpgResponse;
import com.gtech.gvcore.dao.mapper.PosCpgMapper;
import com.gtech.gvcore.dao.model.PosCpg;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.PosCpgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Service
public class PosCpgServiceImpl implements PosCpgService {

    @Autowired
    private PosCpgMapper posCpgMapper;

    @Autowired
    private GvCodeHelper gvCodeHelper;


    @Override
    public Result<String> createPosCpg(CreatePosCpgRequest request) {

        PosCpg posCpg = BeanCopyUtils.jsonCopyBean(request, PosCpg.class);
        posCpg.setStatus(GvcoreConstants.STATUS_ENABLE);
        posCpg.setPosCpgCode(gvCodeHelper.generatePosCpgCode());
        posCpgMapper.insertSelective(posCpg);

        return Result.ok(posCpg.getPosCpgCode());
    }

    @Override
    public void deletePosCpgByPosCode(DeletePosCpgByPosCodeRequest build) {

        Example example = new Example(PosCpg.class);
        example.createCriteria().andEqualTo(PosCpg.CONST_POS_CODE);

        posCpgMapper.deleteByCondition(example);
    }

    @Override
    public List<PosCpgResponse> queryPosCpgListByPos(String posCode) {

        return posCpgMapper.queryPosCpgListByPos(posCode);
    }

    @Override
    public List<PosCpgResponse> queryPosCpgListByMachineId(String machineId) {


        return posCpgMapper.queryPosCpgListByMachineId(machineId);
    }
}
