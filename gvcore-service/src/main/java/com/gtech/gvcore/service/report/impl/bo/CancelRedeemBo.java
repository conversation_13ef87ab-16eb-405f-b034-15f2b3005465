package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.service.report.extend.poll.GroupNewTransactionByVoucherCodeSupport;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName CancelRedeemBo
 * @Description cancel redeem bo
 * <AUTHOR>
 * @Date 2023/4/10 17:31
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class CancelRedeemBo implements GroupNewTransactionByVoucherCodeSupport {

    /**
     * Merchant Code
     */
    private String merchantCode;

    /**
     * Outlet Code
     */
    private String outletCode;

    /**
     * Cpg Code / VPG Code
     */
    private String cpgCode;

    /**
     * 券编码(唯一标识)
     */
    private String voucherCode;

    /**
     * Denomination 券金额/面额
     */
    private BigDecimal denomination;

    /**
     * 创建时间
     */
    private String transactionDate;

    /**
     * 发票号码
     */
    private String invoiceNumber;

    public Date getTransactionDate() {

        return DateUtil.parseDate(transactionDate, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

    private String transactionType;

    private String transactionCode;

    private BigDecimal transactionNumber;

    public void setTransactionCode(String transactionCode) {
        this.transactionNumber = new BigDecimal(transactionCode.substring(3));
        this.transactionCode = transactionCode;
    }


}
