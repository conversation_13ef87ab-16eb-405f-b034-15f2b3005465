package com.gtech.gvcore.service.report.extend.joindate.impl;

import com.gtech.gvcore.dao.mapper.MerchantMapper;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.service.report.extend.joindate.QuerySupport;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

/**
 * @ClassName MerchantQueryImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/1/16 15:46
 * @Version V1.0
 **/
@Component
public class MerchantQueryImpl implements QuerySupport<Merchant> {

    private static final Merchant EMPTY = new Merchant();

    @Autowired
    private MerchantMapper merchantMapper;

    @Override
    public List<Merchant> queryByCode(List<String> codes, String... selectFields) {

        if (CollectionUtils.isEmpty(codes)) return Collections.emptyList();

        Example example = new Example(Merchant.class);
        example.createCriteria().andIn(Merchant.C_MERCHANT_CODE, codes);

        if (ArrayUtils.isNotEmpty(selectFields)) example.selectProperties(selectFields);

        List<Merchant> list = merchantMapper.selectByCondition(example);
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        return list;
    }

    @Override
    public Function<Merchant, String> codeMapper() {
        return Merchant::getMerchantCode;
    }

    @Override
    public Merchant emptyObject() {
        return EMPTY;
    }

    @Override
    public Class<Merchant> supportType() {
        return Merchant.class;
    }
}
