package com.gtech.gvcore.service.report.impl.support.aging.builder;

import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.dao.model.Company;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.report.export.file.ReportExcelUtils;
import com.gtech.gvcore.service.report.extend.ReportProportionDataFunction;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import com.gtech.gvcore.service.report.extend.context.ReportContextHelper;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.aging.SummaryBean;
import com.gtech.gvcore.service.report.impl.bo.AgingBo;
import com.gtech.gvcore.service.report.impl.bo.AgingVoucherTransactionBoVoucher;
import com.gtech.gvcore.service.report.impl.support.aging.AgingSheet;
import com.gtech.gvcore.service.report.impl.support.aging.AgingSheetBuilder;
import com.gtech.gvcore.service.report.impl.support.aging.bo.SummaryBo;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName AgingSummaryReportBuilder
 * @Description 构建summary
 *  summary 时间区间统计使用卡券创建时间
 *  use 使用时间 - 创建时间 , unuse 当前时间 - 创建时间
 *  其余数据反推
 * <AUTHOR>
 * @Date 2022/10/27 16:52
 * @Version V1.0
 **/
@Component
public class SummaryBuilder implements AgingSheetBuilder, ReportProportionDataFunction {

    public static final String OTHERS_KEY = "Others";
    public static final String TOTAL_TITLE = "Sub Total Contract Commitment";
    public static final String ANU_KEY = UUID.randomUUID().toString();

    private final AddFunction addFunction;
    private final Map<AddFunction.FunctionMethod, Consumer<SummaryBo>> auFunctionMap = new EnumMap<>(AddFunction.FunctionMethod.class);
    private final Map<AddFunction.FunctionMethod, Consumer<SummaryBo>> anuFunctionMap = new EnumMap<>(AddFunction.FunctionMethod.class);

    public SummaryBuilder() {

        this.addFunction = this.getAddFunction();

        auFunctionMap.put(AddFunction.FunctionMethod.M30   , SummaryBo::addAuOne);
        auFunctionMap.put(AddFunction.FunctionMethod.M60   , SummaryBo::addAuTwo);
        auFunctionMap.put(AddFunction.FunctionMethod.M90   , SummaryBo::addAuThree);
        auFunctionMap.put(AddFunction.FunctionMethod.M180  , SummaryBo::addAuFour);
        auFunctionMap.put(AddFunction.FunctionMethod.M360  , SummaryBo::addAuSix);
        auFunctionMap.put(AddFunction.FunctionMethod.OTHER , SummaryBo::addAuYear);

        anuFunctionMap.put(AddFunction.FunctionMethod.M30   , SummaryBo::addAnuOne);
        anuFunctionMap.put(AddFunction.FunctionMethod.M60   , SummaryBo::addAnuTwo);
        anuFunctionMap.put(AddFunction.FunctionMethod.M90   , SummaryBo::addAnuThree);
        anuFunctionMap.put(AddFunction.FunctionMethod.M180  , SummaryBo::addAnuFour);
        anuFunctionMap.put(AddFunction.FunctionMethod.M360  , SummaryBo::addAnuSix);
        anuFunctionMap.put(AddFunction.FunctionMethod.OTHER , SummaryBo::addAnuYear);
    }

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.AGING_REPORT_SUMMARY_REPORT;
    }

    @Override
    public AgingSheet builder(final ReportContext context) {

        final List<SummaryBean> summaryTotal = this.builderSummary(context);

        return new AgingSheet()
                .setHead(null)
                .setCellWriteHandler(new AgingSummaryCellWriteHandler())
                .setSheetName(ReportExportTypeEnum.AGING_REPORT_SUMMARY_REPORT.getSheetName())
                .addSheetData("summary", exportTypeEnum(), summaryTotal);
    }

    public List<SummaryBean> builderSummary(final ReportContext context) {

        final Collection<SummaryBo> groupData = this.group(context);

        return this.getSummaryTotal(groupData);
    }

    private Collection<SummaryBo> group(final ReportContext context) {

        final Map<String, SummaryBo> result = new HashMap<>();

        //sales data
        final List<AgingBo> list = context.getCacheList(AgingSheetBuilder.SALES_DATA_KEY);


        final JoinDataMap<Customer> customerMap = context.getCacheJoinMap(AgingSheetBuilder.CUSTOMER_MAP_KEY);
        final JoinDataMap<Voucher> voucherMap = context.getCacheJoinMap(AgingSheetBuilder.VOUCHER_MAP_KEY);
        final List<AgingVoucherTransactionBoVoucher> transactionDataList = context.getCacheList(AgingSheetBuilder.REDEEM_DATA_KEY);
        final Map<String, AgingVoucherTransactionBoVoucher> transactionTimeMap = transactionDataList.stream().collect(Collectors.toMap(AgingVoucherTransactionBoVoucher::getVoucherCode, Function.identity() , (a, b) -> b));
        final Map<String, String> sbuMap = getSbuMap(context);

        list.stream()
                .filter(e -> voucherMap.containsKey(e.getVoucherCode()))
                .filter(e -> customerMap.containsKey(e.getCustomerCode()))
                .forEach(e -> {

                    final String voucherCode = e.getVoucherCode();

                    final Map<AddFunction.FunctionMethod, Consumer<SummaryBo>> functionMap;
                    final Date time;
                    final String subName; // sub name 如果是使用数据则用查询记录中的sub结果 , 非使用数据则使用默认的key来进行索引(后续统计后 会在结果集中删除该数据)
                    if (transactionTimeMap.containsKey(voucherCode)) {
                        AgingVoucherTransactionBoVoucher useTransactionData = transactionTimeMap.get(voucherCode);
                        functionMap = auFunctionMap;
                        time = useTransactionData.getTransactionDate();
                        subName = sbuMap.getOrDefault(useTransactionData.getMerchantCode(), OTHERS_KEY);
                    } else {
                        functionMap = anuFunctionMap;
                        time = ReportContextHelper.reportBuilderTime();
                        subName = ANU_KEY;
                    }

                    //后续需要隐藏从此处入手修改
                    final String customerCode = e.getCustomerCode();
                    final String key = customerCode + subName;

                    final SummaryBo agingReportSummaryBo = result.computeIfAbsent(key, k -> SummaryBo.newInstance()
                            .setCustomerCode(customerCode)
                            .setSbuName(subName)
                            .setCustomerName(customerMap.get(customerCode).getCustomerName()));

                    final Voucher voucher = voucherMap.get(e.getVoucherCode());

                    this.addFunction.add(voucher.getCreateTime(), time, functionMap, agingReportSummaryBo);
                });

        return result.values();
    }

    private List<SummaryBean> getSummaryTotal(final Collection<SummaryBo> groupResult) {

        final Map<String, List<SummaryBo>> collect = groupResult.stream().collect(Collectors.groupingBy(SummaryBo::getCustomerCode));

        final List<SummaryBean> resultList = new ArrayList<>();
        final List<SummaryBo> totalItem = new ArrayList<>();

        collect.forEach((k, v) -> {

            String customerName = v.get(0).getCustomerName();
            SummaryBean total = getTotal(customerName, v, totalItem);
            SummaryBean totalPercentage = getTotalPercentage(customerName, total);

            resultList.add(totalPercentage);
            resultList.add(total);

            List<SummaryBean> customerResultList = getSbuItem(v);
            resultList.addAll(customerResultList);

        });


        final SummaryBean total = getTotal(TOTAL_TITLE, totalItem, null);
        final SummaryBean totalPercentage = getTotalPercentage(TOTAL_TITLE, total);

        resultList.add(total);
        resultList.add(totalPercentage);

        return resultList;
    }

    private static List<SummaryBean> getSbuItem(final List<SummaryBo> v) {

        return v.stream()
                .filter(e -> !ANU_KEY.equals(e.getSbuName()))
                .map(e -> BeanCopyUtils.jsonCopyBean(e, SummaryBean.class)
                        .setVoucherDetails(e.getSbuName())
                        .setAnuOne(null).setAnuTwo(null).setAnuThree(null).setAnuFour(null).setAnuSix(null).setAnuYear(null)
                ).collect(Collectors.toList());
    }

    private static SummaryBean getTotal(final String voucherDetails, final List<SummaryBo> item, final List<SummaryBo> totalItem) {

        SummaryBo totalBo = SummaryBo.newInstance();
        item.forEach(e -> totalBo
                .addAuOne(e.getAuOne()).addAuTwo(e.getAuTwo()).addAuThree(e.getAuThree()).addAuFour(e.getAuFour()).addAuSix(e.getAuSix()).addAuYear(e.getAuYear())
                .addAnuOne(e.getAnuOne()).addAnuTwo(e.getAnuTwo()).addAnuThree(e.getAnuThree()).addAnuFour(e.getAnuFour()).addAnuSix(e.getAnuSix()).addAnuYear(e.getAnuYear()));

        if (null != totalItem) totalItem.add(totalBo);

        SummaryBean total = BeanCopyUtils.jsonCopyBean(totalBo, SummaryBean.class).setVoucherDetails(voucherDetails);

        Integer auUsage = ConvertUtils.toInteger(total.getAuUsageValue(), 0);
        Integer anUnredeem = ConvertUtils.toInteger(total.getAnuUnredeemValue(), 0);
        total.setSalesValue(String.valueOf(auUsage + anUnredeem));

        return total;
    }

    private SummaryBean getTotalPercentage(final String voucherDetails, final SummaryBean total) {

        final SummaryBean totalPercentage = new SummaryBean().setVoucherDetails(voucherDetails);

        final Integer auUsage = ConvertUtils.toInteger(total.getAuUsageValue(), 0);
        final Integer anUnredeem = ConvertUtils.toInteger(total.getAnuUnredeemValue(), 0);
        final int saleValue = auUsage + anUnredeem;

        totalPercentage.setSalesValue("100%")
                .setAuUsageValue(getProportion(auUsage, saleValue))
                .setAuOne(getProportion(ConvertUtils.toInteger(total.getAuOne(), 0), auUsage))
                .setAuTwo(getProportion(ConvertUtils.toInteger(total.getAuTwo(), 0), auUsage))
                .setAuThree(getProportion(ConvertUtils.toInteger(total.getAuThree(), 0), auUsage))
                .setAuFour(getProportion(ConvertUtils.toInteger(total.getAuFour(), 0), auUsage))
                .setAuSix(getProportion(ConvertUtils.toInteger(total.getAuSix(), 0), auUsage))
                .setAuYear(getProportion(ConvertUtils.toInteger(total.getAuYear(), 0), auUsage))

                .setAnuUnredeemValue(getProportion(anUnredeem, saleValue))
                .setAnuOne(getProportion(ConvertUtils.toInteger(total.getAnuOne(), 0), anUnredeem))
                .setAnuTwo(getProportion(ConvertUtils.toInteger(total.getAnuTwo(), 0), anUnredeem))
                .setAnuThree(getProportion(ConvertUtils.toInteger(total.getAnuThree(), 0), anUnredeem))
                .setAnuFour(getProportion(ConvertUtils.toInteger(total.getAnuFour(), 0), anUnredeem))
                .setAnuSix(getProportion(ConvertUtils.toInteger(total.getAnuSix(), 0), anUnredeem))
                .setAnuYear(getProportion(ConvertUtils.toInteger(total.getAnuYear(), 0), anUnredeem));
        return totalPercentage;
    }

    private Map<String, String> getSbuMap(final ReportContext context) {

        final JoinDataMap<Merchant> merchantMap = context.getCacheJoinMap(AgingSheetBuilder.REDEEM_MERCHANT_MAP_KEY);
        final JoinDataMap<Company> companyMap = context.getCacheJoinMap(AgingSheetBuilder.REDEEM_COMPANY_MAP_KEY);

        final Map<String, String> sbuMap = new HashMap<>();

        merchantMap.forEach((k, v) -> {
            String companyCode = v.getCompanyCode();

            String value = OTHERS_KEY;
            if (StringUtils.isNotBlank(companyCode)) value = companyMap.findValue(companyCode).getSbu();

            sbuMap.put(k, value);

        });
        return sbuMap;
    }


    private AddFunction getAddFunction() {

        //add function
        return (saleTime, intervalTime, functionMap, executeObject) ->  {

            final long time = intervalTime.getTime() - saleTime.getTime();
            final int day = (int) (time / (24 * 60 * 60 * 1000));

            if (day <= AddFunction.FunctionMethod.M30.getDay()) functionMap.get(AddFunction.FunctionMethod.M30).accept(executeObject);
            else if (day <= AddFunction.FunctionMethod.M60.getDay()) functionMap.get(AddFunction.FunctionMethod.M60).accept(executeObject);
            else if (day <= AddFunction.FunctionMethod.M90.getDay()) functionMap.get(AddFunction.FunctionMethod.M90).accept(executeObject);
            else if (day <= AddFunction.FunctionMethod.M180.getDay()) functionMap.get(AddFunction.FunctionMethod.M180).accept(executeObject);
            else if (day <= AddFunction.FunctionMethod.M360.getDay()) functionMap.get(AddFunction.FunctionMethod.M360).accept(executeObject);
            else functionMap.get(AddFunction.FunctionMethod.OTHER).accept(executeObject);
        };
    }

    public interface AddFunction {

        enum FunctionMethod {
            M30     (30),
            M60     (60),
            M90     (90),
            M180    (180),
            M360    (360),
            OTHER   (-1),
            ;
            private final int day;
            FunctionMethod(int day) {
                this.day = day;
            }

            public int getDay() {
                return day;
            }
        }

        void add (Date saleTime, Date intervalTime, Map<FunctionMethod, Consumer<SummaryBo>> functionMap, SummaryBo executeObject);
    }

    /**
     * AgingSummaryCellWriteHandler
     * <AUTHOR>
     * @Description AgingSummaryCellWriteHandler
     */
    public static class AgingSummaryCellWriteHandler implements CellWriteHandler {

        // max cell index
        private static final int ROW_INDEX_MAX = 16;
        // style map
        private final EnumMap<Style, CellStyle> styleMap = new EnumMap<>(Style.class);
        // style sheet name
        private static final String STYLE_SHEET_NAME = "SUMMARY_STYLE";
        /**
         * style type enum
         */
        private enum Style {
            CUSTOMER,
            CUSTOMER_AMOUNT,
            SALAS,
            USE,
            MONTH,
            CUSTOMER_CHILD_TITLE_WHITE,
            CUSTOMER_CHILD_TITLE_GREY,
            CUSTOMER_CHILD_DATA_WHITE,
            CUSTOMER_CHILD_DATA_GREY,
            SBU_TOTAL,
            SBU_TOTAL_AMOUNT,
        }

        //customer child data index
        private int customerChildDateIndex = 0;

        private void initStyle(SXSSFWorkbook workbook) {

            if (!styleMap.isEmpty()) return;

            //load style
            Sheet sheet = workbook.getXSSFWorkbook().getSheet(STYLE_SHEET_NAME);
            styleMap.put(Style.CUSTOMER, ReportExcelUtils.getCell(sheet, 0, 0).getCellStyle());
            styleMap.put(Style.CUSTOMER_AMOUNT, ReportExcelUtils.getCell(sheet, 1, 0).getCellStyle());
            styleMap.put(Style.SALAS, ReportExcelUtils.getCell(sheet, 2, 0).getCellStyle());
            styleMap.put(Style.USE, ReportExcelUtils.getCell(sheet, 3, 0).getCellStyle());
            styleMap.put(Style.MONTH, ReportExcelUtils.getCell(sheet, 4, 0).getCellStyle());
            styleMap.put(Style.CUSTOMER_CHILD_TITLE_WHITE, ReportExcelUtils.getCell(sheet, 5, 0).getCellStyle());
            styleMap.put(Style.CUSTOMER_CHILD_TITLE_GREY, ReportExcelUtils.getCell(sheet, 6, 0).getCellStyle());
            styleMap.put(Style.CUSTOMER_CHILD_DATA_WHITE, ReportExcelUtils.getCell(sheet, 7, 0).getCellStyle());
            styleMap.put(Style.CUSTOMER_CHILD_DATA_GREY, ReportExcelUtils.getCell(sheet, 8, 0).getCellStyle());
            styleMap.put(Style.SBU_TOTAL, ReportExcelUtils.getCell(sheet, 9, 0).getCellStyle());
            styleMap.put(Style.SBU_TOTAL_AMOUNT, ReportExcelUtils.getCell(sheet, 10, 0).getCellStyle());
        }


        private Style getCustoemrRowStyle(int cellIndex) {

            if (cellIndex == 1) return Style.SALAS;
            else if (cellIndex == 2 || cellIndex == 9) return Style.USE;
            else return Style.MONTH;
        }

        public Style getCustomerChildStyle (Cell cell) {

            boolean isTitle = cell.getColumnIndex() == 0;
            boolean color = customerChildDateIndex % 2 == 0;

            if (isTitle) return color ? Style.CUSTOMER_CHILD_TITLE_GREY : Style.CUSTOMER_CHILD_TITLE_WHITE;
            else return color ? Style.CUSTOMER_CHILD_DATA_GREY : Style.CUSTOMER_CHILD_DATA_WHITE;
        }


        @Override
        public void afterCellDispose(CellWriteHandlerContext context) {

            final Cell cell = context.getCell();
            final Sheet sheet = cell.getSheet();
            final Workbook workbook = context.getWriteWorkbookHolder().getWorkbook();

            this.initStyle((SXSSFWorkbook) workbook);

            //当前行
            final int curRowIndex = cell.getRowIndex();
            //当前列
            final int curColIndex = cell.getColumnIndex();

            if (curRowIndex < 1 || curColIndex > 0) return;

            final int topRwoIndex = curRowIndex - 1;

            final Row thisRow = cell.getRow();
            final Row topRow = sheet.getRow(topRwoIndex);

            //获取当前行的当前列的数据和上一行的当前列列数据，通过上一行数据是否相同进行合并
            final Cell topCurCell = topRow.getCell(curColIndex);

            final String cellValue = cell.getStringCellValue();
            final String topCellValue = topCurCell.getStringCellValue();

            settingStyle(sheet, curRowIndex, curColIndex, topRwoIndex, topCurCell, cellValue, topCellValue, topRow, thisRow);

        }

        private void settingStyle(Sheet sheet, int curRowIndex, int curColIndex, int topRwoIndex, Cell topCurCell, String cellValue, String topCellValue, Row topRow, Row thisRow) {//NOSONAR

            // 比较当前行的单元格与上一行是否相同，相同合并当前单元格与上一行
            if (Objects.equals(cellValue, topCellValue)) {

                // init customerChildDateIndex
                customerChildDateIndex = 0;

                // merge
                mergeAndSettingStyle(sheet, curRowIndex, curColIndex, topRwoIndex, topCurCell, topRow, thisRow);

            } else {

                //init param
                final Row btmRow = ReportExcelUtils.getRow(sheet, curRowIndex + 1);
                final Cell btmCell = ReportExcelUtils.getCell(btmRow, curColIndex);

                // 跳过下一行相同的数据 如果下一行相同则表示已经进入customer数据范围
                if (Objects.equals(cellValue, btmCell.getStringCellValue())) return;

                // customer index ++
                customerChildDateIndex++;

                for (int i = 0; i < ROW_INDEX_MAX; i++) {
                    // cell
                    Cell thisRowOtherCell = ReportExcelUtils.getCell(thisRow, i);
                    // get style
                    Style style = this.getCustomerChildStyle(thisRowOtherCell);
                    // get cell style
                    CellStyle cellStyle = styleMap.get(style);
                    // setting style
                    thisRowOtherCell.setCellStyle(cellStyle);
                }

            }
        }

        private void mergeAndSettingStyle(Sheet sheet, int curRowIndex, int curColIndex, int topRwoIndex, Cell topCurCell, Row topRow, Row thisRow) {//NOSONAR

            //merge
            ReportExcelUtils.mergeRow(sheet, topRwoIndex, curRowIndex, curColIndex);

            if (TOTAL_TITLE.equals(topCurCell.getStringCellValue())) {

                //setting style
                topCurCell.setCellStyle(styleMap.get(Style.SBU_TOTAL));

                //setting other style
                for (int i = 1; i < ROW_INDEX_MAX; i++) {

                    // get style
                    final Style style = this.getCustoemrRowStyle(i);

                    // setting top row style
                    final Cell topRowOtherCell = ReportExcelUtils.getCell(topRow, i);
                    topRowOtherCell.setCellStyle(styleMap.get(Style.SBU_TOTAL_AMOUNT));

                    // setting this row style
                    final Cell thisRowOtherCell = ReportExcelUtils.getCell(thisRow, i);
                    thisRowOtherCell.setCellStyle(styleMap.get(style));
                }

            } else {

                //setting style
                topCurCell.setCellStyle(styleMap.get(Style.CUSTOMER));

                //setting other style
                for (int i = 1; i < ROW_INDEX_MAX; i++) {

                    // get style
                    final Style style = this.getCustoemrRowStyle(i);

                    // setting top row style
                    final Cell topRowOtherCell = ReportExcelUtils.getCell(topRow, i);
                    topRowOtherCell.setCellStyle(styleMap.get(style));

                    // setting this row style
                    final Cell thisRowOtherCell = ReportExcelUtils.getCell(thisRow, i);
                    thisRowOtherCell.setCellStyle(styleMap.get(Style.CUSTOMER_AMOUNT));
                }
            }
        }


    }

    @Override
    public Class<?> getExportDataClass() {
        return SummaryBean.class;
    }

}

