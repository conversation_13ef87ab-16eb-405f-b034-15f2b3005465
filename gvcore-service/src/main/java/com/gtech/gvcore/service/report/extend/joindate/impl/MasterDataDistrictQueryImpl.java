package com.gtech.gvcore.service.report.extend.joindate.impl;

import com.gtech.basic.masterdata.web.dao.MasterDataDistrictMapper;
import com.gtech.basic.masterdata.web.entity.MasterDataDistrictEntity;
import com.gtech.gvcore.service.report.extend.joindate.QuerySupport;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

/**
 * @ClassName MasterDataDistrictEntityQueryImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/1/16 15:49
 * @Version V1.0
 **/
@Component
public class MasterDataDistrictQueryImpl implements QuerySupport<MasterDataDistrictEntity> {

    private static final MasterDataDistrictEntity EMPTY = new MasterDataDistrictEntity();


    @Autowired
    private MasterDataDistrictMapper masterDataDistrictMapper;

    private static final String DISTRICT_CODE = "districtCode";

    @Override
    public List<MasterDataDistrictEntity> queryByCode (List<String> codes, String... selectFields) {

        if (CollectionUtils.isEmpty(codes)) return Collections.emptyList();

        Example example = new Example(MasterDataDistrictEntity.class, true, true);
        example.createCriteria().andIn(DISTRICT_CODE, codes);

        if (ArrayUtils.isNotEmpty(selectFields)) example.selectProperties(selectFields);

        List<MasterDataDistrictEntity> masterDataDistrictEntities = masterDataDistrictMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(masterDataDistrictEntities)) return Collections.emptyList();

        return masterDataDistrictEntities;
    }

    @Override
    public Function<MasterDataDistrictEntity, String> codeMapper() {
        return MasterDataDistrictEntity::getDistrictCode;
    }

    @Override
    public MasterDataDistrictEntity emptyObject() {
        return EMPTY;
    }

    @Override
    public Class<MasterDataDistrictEntity> supportType() {
        return MasterDataDistrictEntity.class;
    }
}
