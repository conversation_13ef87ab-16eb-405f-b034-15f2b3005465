package com.gtech.gvcore.service.report.impl.support.liability;

import com.gtech.commons.page.PageParam;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.GcCardStatusReportEnum;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.common.utils.GvConvertUtils;
import com.gtech.gvcore.dao.mapper.*;
import com.gtech.gvcore.dao.model.*;
import com.gtech.gvcore.giftcard.domain.model.GcCardStatus;
import com.gtech.gvcore.giftcard.domain.model.GcMgtCardStatus;
import com.gtech.gvcore.helper.RedisLockHelper;
import com.gtech.gvcore.service.report.extend.joindate.GetJoinDateMapSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.support.liability.model.GcLiabilityDetailStatisticDto;
import com.gtech.gvcore.service.report.impl.support.liability.model.GcLiabilitySummaryStatisticDto;
import com.gtech.gvcore.service.report.impl.support.liability.model.GcLiabilityVoucherBo;
import com.gtech.gvcore.service.report.impl.support.liability.model.GcLiabilityVoucherMode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.Sqls;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
@EnableScheduling
public class GcLiabilityDataScript implements GetJoinDateMapSupport {

    @Autowired
    private GcReportLiabilityGenerateHistoryMapper reportLiabilityGenerateHistoryMapper;

    @Autowired
    private GcReportTempLiabilitySStructureMapper reportTempLiabilitySStructureMapper;

    @Autowired
    private GcReportTempLiabilityDStructureMapper reportTempLiabilityDStructureMapper;

    @Autowired
    private IssueHandlingMapper issueHandlingMapper;

    @Autowired
    private IssueHandlingDetailsMapper issueHandlingDetailsMapper;

    private static final String LIABILITY_GENERATE_REDIS_LOCK_KEY = "gc_liability_generate_lock_key";

    public static final String LIABILITY_SUMMARY_TABLE_TYPE = "s";
    public static final String LIABILITY_DETAIL_TABLE_TYPE = "d";
    public static final String TABLE_CODE_DATE_FORMAT = "yyMM";

    private static final String TABLE_NAME_TEMPLATE = "gc_report_temp_liability_%s_%s";

    /**
     * table mode [NONE, CREATE]
     */
    @Value("${gvcore.report.liability.table.mode:NONE}")
    private String tableMode;

    @Scheduled(cron = "0 0 0 1 * ?")
    public void scheduled() {

        // table code
        final String tableCode = DateUtil.format(new Date(), TABLE_CODE_DATE_FORMAT);
        // runner
        this.runner(tableCode);
    }

    public boolean existReport(final String tableCode, final String tableType) {

        final String tableNam = String.format(TABLE_NAME_TEMPLATE, tableType, tableCode);

        if (!this.tableExist(tableNam)) {

            return !this.runner(tableCode);
        }

        return false;
    }

    private boolean tableExist(String tableNam) {

        try {
            return reportLiabilityGenerateHistoryMapper.existTable(tableNam) > 0;
        } catch (Exception e) {
            return false;
        }
    }

    public boolean runner(final String tableCode) {

        // lock
        boolean lock = RedisLockHelper.tryLock(LIABILITY_GENERATE_REDIS_LOCK_KEY, tableCode, 3000L, 30 * 60 * 1000L);
        if (!lock) return false;

        try {
            this.execute(tableCode);
        } finally {

            //unlock
            RedisLockHelper.unLock(LIABILITY_GENERATE_REDIS_LOCK_KEY, tableCode);
        }

        return true;
    }

    /**
     * excute
     * @param tableCode
     */
    private void execute(String tableCode) {

        try {

            // validate
            if (!validate(tableCode)) return;

            // result
            final Map<String, GcLiabilitySummaryStatisticDto> summaryMap = new ConcurrentHashMap<>();
            final Map<String, GcLiabilityDetailStatisticDto> detailMap = new ConcurrentHashMap<>();

            // thread error message
            final AtomicReference<String> errorMessage = new AtomicReference<>("");

            this.group(tableCode, summaryMap, detailMap, errorMessage);

            if (StringUtils.isNotBlank(errorMessage.get())) {

                this.updateReportLiabilityGenerateHistory(tableCode, new GcReportLiabilityGenerateHistory()
                        .setErrorLog(errorMessage.get()).setLastSuccessTime(new Date()));

                log.warn("generate liability data error, table code: {}, error message : {}", tableCode, errorMessage.get());
            }

            this.callback(tableCode, summaryMap, detailMap);

        } catch (Exception e) {

            log.error("generate liability data error, table code: {}", tableCode, e);

            // update error log
            this.updateReportLiabilityGenerateHistory(tableCode, new GcReportLiabilityGenerateHistory().setErrorLog(e.getMessage()).setLastSuccessTime(new Date()));

        }
    }

    /**
     * get deactivate voucher code list
     * @return
     */
    private List<String> getDeactivateVoucherCodeList() {

        //issue handling code
        final List<IssueHandling> issueHandlingList = GvConvertUtils.toObject(issueHandlingMapper.select(new IssueHandling().setIssueType(IssueHandlingTypeEnum.BULK_DEACTIVATE.code())), new ArrayList<>());
        final List<String> issueHandlingCodeList = issueHandlingList.stream().map(IssueHandling::getIssueHandlingCode).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(issueHandlingCodeList)) return new ArrayList<>();

        //issue handling detail
        final List<IssueHandlingDetails> issueHandlingDetailList = GvConvertUtils.toObject(
                issueHandlingDetailsMapper.selectByCondition(Example.builder(IssueHandlingDetails.class)
                        .where(Sqls.custom().andIn(IssueHandlingDetails.C_ISSUE_HANDLING_CODE, issueHandlingCodeList))
                        .build()
                ), new ArrayList<>());

        //to voucher code list
        return issueHandlingDetailList.stream().map(IssueHandlingDetails::getVoucherCode).distinct().collect(Collectors.toList());
    }

    /**
     * callback
     * @param tableCode
     * @param summaryMap
     * @param detailMap
     */
    private void callback(final String tableCode,
                          final Map<String, GcLiabilitySummaryStatisticDto> summaryMap,
                          final Map<String, GcLiabilityDetailStatisticDto> detailMap) {

        // result
        final Collection<GcLiabilitySummaryStatisticDto> summaryList = summaryMap.values();
        final Collection<GcLiabilityDetailStatisticDto> detailList = detailMap.values();

        // empty
        if (CollectionUtils.isEmpty(summaryList) || CollectionUtils.isEmpty(detailList)) {

            this.updateReportLiabilityGenerateHistory(tableCode, new GcReportLiabilityGenerateHistory().setErrorLog("NOT FOUND DATA").setLastSuccessTime(new Date()));

            log.warn("not found data for liability table code: {}", tableCode);
            return;
        }

        // save
        this.saveResult(tableCode, summaryList, detailList);

        // update success time
        this.updateReportLiabilityGenerateHistory(tableCode, new GcReportLiabilityGenerateHistory().setErrorLog("").setLastSuccessTime(new Date()));
    }

    private void group(final String tableCode,
                       final Map<String, GcLiabilitySummaryStatisticDto> summaryMap,
                       final Map<String, GcLiabilityDetailStatisticDto> detailMap,
                       final AtomicReference<String> errorMessage
    ) {

        try {
            // time
            final Date now = DateUtil.parseDate(tableCode, TABLE_CODE_DATE_FORMAT);
            final Date lastMoonTime = DateUtil.addMonth(now, -1);

            // page param
            final PageParam param = new PageParam();
            param.setPageSize(500);
            param.setPageNum(0);

            // poll voucher and group
            for (List<GcLiabilityVoucherMode> vouchers = pollVoucher(now, param);
                 CollectionUtils.isNotEmpty(vouchers);
                 vouchers = pollVoucher(now, param)) {

                log.info("select voucher list , tableCode : {}, pageIndex : {}, voucher size :{}", tableCode,
                        (param.getPageNum() - 1) * param.getPageSize(), vouchers.size());
                JoinDataMap<Outlet> outletMap = getMapByCode(vouchers, GcLiabilityVoucherMode::getSalesOutlet, Outlet.class);
                // convert to voucher bo
                final List<GcLiabilityVoucherBo> liabilityVoucherBoList = vouchers.stream()
                        .map(e ->
                        {
                            String status = e.getStatus();
                            if (e.getExpiryTime().before(new Date()))
                                status = GcCardStatusReportEnum.EXPIRED.getStatus();
                            else if (Objects.equals(e.getManagementStatus(), GcMgtCardStatus.DISABLE.name()))
                                status = GcCardStatusReportEnum.DEACTIVATED.getStatus();
                            else if (Objects.equals(status, GcCardStatus.ACTIVATED.name()))
                                status = GcCardStatusReportEnum.ACTIVATED.getStatus();
                            else if (Objects.equals(status, GcCardStatus.PURCHASED.name()))
                                status = GcCardStatusReportEnum.PURCHASED.getStatus();

                            return new GcLiabilityVoucherBo(tableCode, lastMoonTime, e, outletMap.get(e.getSalesOutlet()).getMerchantCode(), status);
                        })
                        .collect(Collectors.toList());

                log.info("convert bo, tableCode : {}", tableCode);
                // group
                final Map<String, GcLiabilitySummaryStatisticDto> groupMap = liabilityVoucherBoList.stream()
                        .map(GcLiabilitySummaryStatisticDto::convert)
                        .collect(Collectors.toMap(
                                GcLiabilitySummaryStatisticDto::getGroupKey,
                                Function.identity(),
                                GcLiabilitySummaryStatisticDto::merge
                        ));

                //merge group result
                groupMap.forEach((k, v) -> summaryMap.merge(k, v, GcLiabilitySummaryStatisticDto::merge));

                // group detail
                liabilityVoucherBoList.stream()
                        .map(e -> GcLiabilityDetailStatisticDto.convert(e, this::refreshDetail))
                        .forEach(e -> detailMap.merge(GcLiabilityDetailStatisticDto.getGroupKey(e), e, GcLiabilityDetailStatisticDto::merge));
                log.info("group, tableCode : {}", tableCode);

            }
        } catch (Exception e) {

            log.error("generate liability data error, table code: {}", tableCode, e);

            errorMessage.updateAndGet(v -> v + e.getMessage());
        }
    }

    /**
     * save result
     * @param tableCode
     * @param summaryList
     * @param detailList
     */
    private void saveResult(final String tableCode,
                            final Collection<GcLiabilitySummaryStatisticDto> summaryList,
                            final Collection<GcLiabilityDetailStatisticDto> detailList) {

        //summary
        final List<GcReportTempLiabilitySStructure> summary = summaryList.stream()
                .map(GcLiabilitySummaryStatisticDto::toEntity)
                .collect(Collectors.toList());
        //detail
        final List<GcReportTempLiabilityDStructure> detail = detailList.stream()
                .filter(e -> StringUtils.isNotBlank(e.getVoucherCodes().get()))
                .map(GcLiabilityDetailStatisticDto::toEntity)
                .collect(Collectors.toList());

        // save
        ListUtils.partition(summary, 1000).forEach(s -> reportTempLiabilitySStructureMapper.insertBatch(tableCode, s));
        ListUtils.partition(detail, 1000).forEach(d -> reportTempLiabilityDStructureMapper.insertBatch(tableCode, d));

    }

    /**
     * refresh detail
     * 分段存储减少内存压力
     * @param dto
     */
    private void refreshDetail(GcLiabilityDetailStatisticDto dto) {

        // detail
        final GcReportTempLiabilityDStructure detail = dto.toEntity();

        // save
        this.reportTempLiabilityDStructureMapper.insertDetail(dto.getTableCode(), detail);

    }

    /**
     * update history
     * @param createTime
     * @param param
     * @return
     */
    private List<GcLiabilityVoucherMode> pollVoucher(final Date createTime, final PageParam param) {
        final List<GcLiabilityVoucherMode> liabilityVoucherModes = reportLiabilityGenerateHistoryMapper.selectVoucher(createTime, param.getPageNum(), param.getPageSize());

        if (CollectionUtils.isEmpty(liabilityVoucherModes)) return Collections.emptyList();

        final int maxId = (int) liabilityVoucherModes.stream().mapToLong(GcLiabilityVoucherMode::getId).max().orElseThrow(() -> new IllegalArgumentException("查询异常"));
        param.setPageNum(maxId);

        return liabilityVoucherModes;
    }

    /**
     * validate operation
     * @param tableCode
     * @return
     */
    private boolean validate(String tableCode) {

        // select history generate by table code
        GcReportLiabilityGenerateHistory liabilityGenerateHistory = reportLiabilityGenerateHistoryMapper.selectOne(new GcReportLiabilityGenerateHistory().setLiabilityTableCode(tableCode));

        // history not exist
        if (null == liabilityGenerateHistory) {

            // init table
            this.initTable(tableCode);

            // insert history
            reportLiabilityGenerateHistoryMapper.insert(new GcReportLiabilityGenerateHistory()
                    .setLiabilityTableCode(tableCode)
                    .setGenerateTime(new Date()));

        } else {

            // init time
            long lastSuccessTime = liabilityGenerateHistory.getLastSuccessTime() == null ? 0 : liabilityGenerateHistory.getLastSuccessTime().getTime();
            long lastRefreshTime = liabilityGenerateHistory.getLastRefreshTime() == null ? 0 : liabilityGenerateHistory.getLastRefreshTime().getTime();

            //完全没有成功过 或者 最后一次成功时间大于最后一次刷新时间 允许重新生成
            if (0 == lastSuccessTime && 0 == lastRefreshTime || lastSuccessTime > lastRefreshTime) {

                // init table
                this.initTable(tableCode);

                GcReportLiabilityGenerateHistory entity = new GcReportLiabilityGenerateHistory().setLastRefreshTime(new Date());
                updateReportLiabilityGenerateHistory(tableCode, entity);

            } else {

                // refuse operation
                return false;
            }

        }

        // allow operation
        return true;
    }

    /**
     * update report liability generate history
     * @param tableCode
     * @param entity
     */
    private void updateReportLiabilityGenerateHistory(final String tableCode,
                                                      final GcReportLiabilityGenerateHistory entity) {

        final Example example = Example.builder(ReportLiabilityGenerateHistory.class)
                .where(Sqls.custom().andEqualTo(ReportLiabilityGenerateHistory.C_LIABILITY_TABLE_CODE, tableCode))
                .build();

        reportLiabilityGenerateHistoryMapper.updateByConditionSelective(entity, example);
    }

    /**
     * create and truncate table
     * @param tableCode
     */
    private void initTable(String tableCode) {

        if ("NONE".equalsIgnoreCase(tableMode)) {

            reportLiabilityGenerateHistoryMapper.clearTable(tableCode, LIABILITY_SUMMARY_TABLE_TYPE);
            reportLiabilityGenerateHistoryMapper.clearTable(tableCode, LIABILITY_DETAIL_TABLE_TYPE);

        } else {

            try {
                reportLiabilityGenerateHistoryMapper.createLiabilityTable(tableCode, LIABILITY_SUMMARY_TABLE_TYPE);
                reportLiabilityGenerateHistoryMapper.createLiabilityTable(tableCode, LIABILITY_DETAIL_TABLE_TYPE);
            } catch (Exception e) {
                // ignore
            }

            reportLiabilityGenerateHistoryMapper.truncateLiabilityTable(tableCode, LIABILITY_SUMMARY_TABLE_TYPE);
            reportLiabilityGenerateHistoryMapper.truncateLiabilityTable(tableCode, LIABILITY_DETAIL_TABLE_TYPE);

        }

    }


}