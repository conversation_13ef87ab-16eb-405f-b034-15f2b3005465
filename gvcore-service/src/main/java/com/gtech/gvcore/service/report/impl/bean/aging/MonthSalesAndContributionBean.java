package com.gtech.gvcore.service.report.impl.bean.aging;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName AgingMonthSalesAndContributionBean
 * @Description aging month sales and contribution bean
 * <AUTHOR>
 * @Date 2022/11/1 14:31
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class MonthSalesAndContributionBean {

    @ExcelProperty("Detail")
    private String detail;

    @ReportAmountValue
    @ExcelProperty("Jan")
    private String jan;

    @ReportAmountValue
    @ExcelProperty("Feb")
    private String feb;

    @ReportAmountValue
    @ExcelProperty("Mar")
    private String mar;

    @ReportAmountValue
    @ExcelProperty("Apr")
    private String apr;

    @ReportAmountValue
    @ExcelProperty("May")
    private String may;

    @ReportAmountValue
    @ExcelProperty("Jun")
    private String jun;

    @ReportAmountValue
    @ExcelProperty("Jul")
    private String jul;

    @ReportAmountValue
    @ExcelProperty("Aug")
    private String aug;

    @ReportAmountValue
    @ExcelProperty("Sep")
    private String sep;

    @ReportAmountValue
    @ExcelProperty("Oct")
    private String oct;

    @ReportAmountValue
    @ExcelProperty("Nov")
    private String nov;

    @ReportAmountValue
    @ExcelProperty("Dec")
    private String dec;

    @ReportAmountValue
    @ExcelProperty("Total")
    private String total;

    @ExcelProperty("Contribution")
    private String contribution;

}
