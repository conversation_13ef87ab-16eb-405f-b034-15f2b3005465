package com.gtech.gvcore.service.report.extend;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;

/**
 * @ClassName ReportProportionDataFunction
 * @Description 报表百分比数据处理接口
 * <AUTHOR>
 * @Date 2023/1/4 17:12
 * @Version V1.0
 **/
public interface ReportProportionDataFunction {

    String PERCENT = "%";
    String EMPTY_PROPORTION = "-" + PERCENT;

    int DEFAULT_SCALE = 0;
    BigDecimal DENOMINATOR = new BigDecimal(100);

    default String getProportion(int a, int b) {

        if (b == 0) return EMPTY_PROPORTION;

        return getProportion(a, b, DEFAULT_SCALE);
    }

    default String getProportion(int a, int b, int scale) {

        if (b == 0) return EMPTY_PROPORTION;

        return getProportion(new BigDecimal(a), new BigDecimal(b), scale);
    }

    default String getProportion(BigDecimal a, BigDecimal b) {

        return getProportion(a, b, DEFAULT_SCALE);
    }

    default String getProportion(String a, String b) {

        return getProportion(new BigDecimal(a), new BigDecimal(b), DEFAULT_SCALE);
    }

    default String getProportion(BigDecimal a, BigDecimal b, int scale) {

        if (b == null || a == null || BigDecimal.ZERO.compareTo(b) == 0) return EMPTY_PROPORTION;

        return a.divide(b, MathContext.DECIMAL64)
                .multiply(DENOMINATOR)
                .setScale(scale, RoundingMode.HALF_UP)
                .toPlainString() + PERCENT;
    }

}
