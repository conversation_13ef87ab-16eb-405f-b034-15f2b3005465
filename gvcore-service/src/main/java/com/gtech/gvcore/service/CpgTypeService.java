package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.cpg.CreateCpgTypeRequest;
import com.gtech.gvcore.common.request.cpg.GetCpgTypeRequest;
import com.gtech.gvcore.common.request.cpg.QueryCpgTypeByPageRequest;
import com.gtech.gvcore.common.request.cpg.UpdateCpgTypeRequest;
import com.gtech.gvcore.common.request.cpg.UpdateCpgTypeStatusRequest;
import com.gtech.gvcore.common.response.cpg.QueryCpgTypeByPageResponse;
import com.gtech.gvcore.dao.model.CpgType;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022年2月16日
 */
public interface CpgTypeService {

    Result<String> createCpgType(CreateCpgTypeRequest createCpgTypeRequest);

    PageResult<QueryCpgTypeByPageResponse> queryCpgTypeDataByPage(QueryCpgTypeByPageRequest request);

    Result<Object> updateCpgTypeStatus(UpdateCpgTypeStatusRequest request);

    Result<Object> deleteCpgTypeById(String cpgTypeCode);

    Result<Object> updateCpgType(UpdateCpgTypeRequest request);

    Result<Object> getCpgType(GetCpgTypeRequest getCpgTypeRequest);

    Result<Object> cpgTypeList();

	CpgType getCpgTypeByPrefix(String prefix);

    /**
     * 
     * <AUTHOR>
     * @param cpgTypeCodeList
     * @return
     * @date 2022年3月17日
     */
    Map<String, CpgType> queryCpgTypeCodeNameByCpgTypeCodeList(List<String> cpgTypeCodeList);

    /**
     * 
     * <AUTHOR>
     * @param cpgTypeCode
     * @return
     * @date 2022年3月22日
     */
    CpgType queryByCpgTypeCode(String cpgTypeCode);
}
