package com.gtech.gvcore.service.report.impl.support.aging.bo;

import com.gtech.commons.utils.ConvertUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @ClassName VoucherUsedAgeBySBUBo
 * @Description voucher used age by sbu bo
 * <AUTHOR>
 * @Date 2022/11/3 15:49
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class VoucherUsedAgeBySBUBo {

    private String sbuName;

    private BigDecimal oneValue = BigDecimal.ZERO;

    private BigDecimal twoValue = BigDecimal.ZERO;

    private BigDecimal threeValue = BigDecimal.ZERO;

    private BigDecimal fourValue = BigDecimal.ZERO;

    private BigDecimal sixValue = BigDecimal.ZERO;

    public VoucherUsedAgeBySBUBo addOneValue(BigDecimal value) {
        oneValue = this.oneValue.add(ConvertUtils.toBigDecimal(value, BigDecimal.ZERO));
        return this;
    }
    public VoucherUsedAgeBySBUBo addTwoValue(BigDecimal value) {
        twoValue = this.twoValue.add(ConvertUtils.toBigDecimal(value, BigDecimal.ZERO));
        return this;
    }
    public VoucherUsedAgeBySBUBo addThreeValue(BigDecimal value) {
        threeValue = this.threeValue.add(ConvertUtils.toBigDecimal(value, BigDecimal.ZERO));
        return this;
    }
    public VoucherUsedAgeBySBUBo addFourValue(BigDecimal value) {
        fourValue = this.fourValue.add(ConvertUtils.toBigDecimal(value, BigDecimal.ZERO));
        return this;
    }
    public VoucherUsedAgeBySBUBo addSixValue(BigDecimal value) {
        sixValue = this.sixValue.add(ConvertUtils.toBigDecimal(value, BigDecimal.ZERO));
        return this;
    }

    public BigDecimal getTotal() {
        return oneValue.add(twoValue)
                .add(threeValue)
                .add(fourValue)
                .add(sixValue);
    }
}
