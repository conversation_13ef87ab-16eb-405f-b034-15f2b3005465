package com.gtech.gvcore.service.report.impl.support.liability.model;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.utils.GvConvertUtils;
import com.gtech.gvcore.dao.model.GcReportTempLiabilityDStructure;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;

/**
 * @ClassName LiabilityDetailStatisticDto
 * @Description LiabilityDetailStatisticDto
 * <AUTHOR>
 * @Date 2023/4/19 14:33
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GcLiabilityDetailStatisticDto {

    public static final int PAGE_SIZE = 1000;

    private String tableCode;

    private String issuerCode;

    private String merchantCode;

    private String outletCode;

    private String cpgCode;

    private String voucherStatus;

    private BigDecimal denomination;
    private BigDecimal balance;
    private String expiryDate;

    private AtomicInteger voucherCodePageIndex = new AtomicInteger(1);

    private AtomicReference<String> voucherCodes;

    private AtomicInteger refreshPageIndex = new AtomicInteger(0);

    private Consumer<GcLiabilityDetailStatisticDto> refreshFunction;

    public static GcLiabilityDetailStatisticDto convert(GcLiabilityVoucherBo bo, Consumer<GcLiabilityDetailStatisticDto> refreshFunction) {

        final GcLiabilityVoucherMode voucher = GvConvertUtils.toObject(bo.getVoucher(), new GcLiabilityVoucherMode());
        final String voucherCode = voucher.getCardNumber();
        return new GcLiabilityDetailStatisticDto()
                .setTableCode(bo.getTableCode())
                .setIssuerCode(GvConvertUtils.toString(voucher.getIssuerCode(), StringUtils.EMPTY))
                .setMerchantCode(GvConvertUtils.toString(bo.getMerchantCode(), StringUtils.EMPTY))
                .setOutletCode(GvConvertUtils.toString(voucher.getSalesOutlet(), StringUtils.EMPTY))
                .setCpgCode(GvConvertUtils.toString(voucher.getCpgCode(), StringUtils.EMPTY))
                .setVoucherStatus(bo.getStatus())
                .setBalance(bo.getVoucher().getBalance())
                .setDenomination(GvConvertUtils.toBigDecimal(voucher.getDenomination(), BigDecimal.ZERO))
                .setVoucherCodes(new AtomicReference<>(voucherCode))
                .setRefreshFunction(refreshFunction)
                .setExpiryDate(DateUtil.format(voucher.getExpiryTime(), DateUtil.FORMAT_YYYYMMDDHHMISS));

    }

    public static String getGroupKey(GcLiabilityDetailStatisticDto bean) {
        return StringUtils.join("_", bean.getIssuerCode(), bean.getMerchantCode(), bean.getOutletCode()
                , bean.getCpgCode(), bean.getVoucherStatus(), bean.getDenomination(),bean.getBalance(), bean.getExpiryDate());
    }

    public synchronized GcLiabilityDetailStatisticDto merge(GcLiabilityDetailStatisticDto bean) {
        this.voucherCodePageIndex.incrementAndGet();
        this.voucherCodes.updateAndGet(voucherCodeString -> voucherCodeString + "," + bean.getVoucherCodes());
        this.refresh();
        return this;
    }


    public void refresh() {
        if (voucherCodePageIndex.get() - refreshPageIndex.get() >= PAGE_SIZE) {

            // save
            refreshFunction.accept(this);
            // reset
            refreshPageIndex.set(voucherCodePageIndex.get());
            voucherCodes.set(StringUtils.EMPTY);
        }
    }

    public GcReportTempLiabilityDStructure toEntity() {

        final String voucherCodeString = this.getVoucherCodes().get();

        return new GcReportTempLiabilityDStructure()
                .setIssuerCode(this.getIssuerCode())
                .setMerchantCode(this.getMerchantCode())
                .setOutletCode(this.getOutletCode())
                .setCpgCode(this.getCpgCode())
                .setVoucherStatus(this.getVoucherStatus())
                .setDenomination(this.getDenomination())
                .setBalance(this.getBalance())
                .setExpiryDate(DateUtil.parseDate(this.getExpiryDate(), DateUtil.FORMAT_YYYYMMDDHHMISS))
                .setVoucherCodePageIndex(this.getVoucherCodePageIndex().get())
                .setOutletCode(this.getOutletCode())
                .setVoucherCodes(voucherCodeString);
    }
}
