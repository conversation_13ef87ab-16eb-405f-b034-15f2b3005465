package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.BookletStatusEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.common.utils.GvDateUtil;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Issuer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.helper.GvPageHelper;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.PollReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.ReportParamConvertHelper;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import com.gtech.gvcore.service.report.extend.context.ReportContextHelper;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.BookletInventoryDetailedBean;
import com.gtech.gvcore.service.report.impl.bo.BookletInventoryDetailedBo;
import com.gtech.gvcore.service.report.impl.param.BookletInventoryQueryData;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 12:06
 * @Description:
 */
@Service
public class BookletInventoryDetailedImpl extends ReportSupport
        implements BusinessReport<BookletInventoryQueryData, BookletInventoryDetailedBean>, PollReport {

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.BOOKLET_INVENTORY_DETAILED_REPORT;
    }

    @Override
    public BookletInventoryQueryData builderQueryParam(CreateReportRequest reportParam) {

        ReportParamConvertHelper.convertQueryDateMerchantCodeToOutletCode(reportParam);

        BookletInventoryQueryData bookletInventoryReport = new BookletInventoryQueryData();

        bookletInventoryReport.setIssuerCodes(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        bookletInventoryReport.setOutletCodes(reportParam.getOutletCodes());
        bookletInventoryReport.setCpgCodes(reportParam.getCpgCodes());

        bookletInventoryReport.setBookletStatus(reportParam.getBookletStatus());

        bookletInventoryReport.setStartVoucherNumber(reportParam.getVoucherCodeNumStart() + "");
        bookletInventoryReport.setEndVoucherNumber(reportParam.getVoucherCodeNumEnd() + "");

        bookletInventoryReport.setStartBookletNo(reportParam.getBookletStart());
        bookletInventoryReport.setEndBookletNo(reportParam.getBookletEnd());

        bookletInventoryReport.setVoucherCodeNumStart(reportParam.getVoucherCodeNumStart());
        bookletInventoryReport.setVoucherCodeNumEnd(reportParam.getVoucherCodeNumEnd());

        bookletInventoryReport.setVoucherEffectiveDateStart(reportParam.getVoucherEffectiveDateStart());
        bookletInventoryReport.setVoucherEffectiveDateEnd(reportParam.getVoucherEffectiveDateEnd());

        return bookletInventoryReport;
    }

    @Override
    public List<BookletInventoryDetailedBean> getExportData(BookletInventoryQueryData queryData) {

        List<BookletInventoryDetailedBo> detailedList = this.reportBusinessMapper.bookletInventoryDetailed(queryData, GvPageHelper.getRowBounds(queryData));
        if (CollectionUtils.isEmpty(detailedList)) return Collections.emptyList();

        JoinDataMap<Outlet> outletMap = super.getMapByCode(detailedList, BookletInventoryDetailedBo::getOutletCode, Outlet.class);
        JoinDataMap<Merchant> merchantMap = super.getMapByCode(outletMap.values(), Outlet::getMerchantCode, Merchant.class);
        JoinDataMap<Issuer> issuerMap = super.getMapByCode(detailedList, BookletInventoryDetailedBo::getIssuerCode, Issuer.class);
        JoinDataMap<Cpg> cpgMap = super.getMapByCode(detailedList, BookletInventoryDetailedBo::getCpgCode, Cpg.class);

        return detailedList.stream()
                .map(e -> {

                    final BookletStatusEnum status = BookletStatusEnum.getStatus(e.getBookletStatus());
                    final Outlet outlet = outletMap.findValue(e.getOutletCode());
                    final Merchant merchant = merchantMap.findValue(outlet.getMerchantCode());
                    final Issuer issuer = issuerMap.findValue(e.getIssuerCode());
                    final Cpg cpg = cpgMap.findValue(e.getCpgCode());

                    final BookletInventoryDetailedBean inventoryDetailedBean = new BookletInventoryDetailedBean();

                    inventoryDetailedBean.setIssuer(issuer.getIssuerName());
                    inventoryDetailedBean.setMerchant(merchant.getMerchantName());
                    inventoryDetailedBean.setOutlet(outlet.getOutletName());
                    inventoryDetailedBean.setVoucherProgramGroup(cpg.getCpgName());
                    inventoryDetailedBean.setBookletNumber(e.getBookletNumber());
                    inventoryDetailedBean.setStartCardNumber(e.getStartCardNumber());
                    inventoryDetailedBean.setEndCardNumber(e.getEndCardNumber());
                    inventoryDetailedBean.setCardCount(e.getCardCount());
                    if (null != status) inventoryDetailedBean.setBookletStatus(status.getDesc());
                    inventoryDetailedBean.setExpiryDate(DateUtil.format(e.getEffectiveTime(), DateUtil.FORMAT_YYYYMMDDHHMISS));

                    return inventoryDetailedBean;
                }).collect(Collectors.toList());
    }

    @Override
    public Object getHeadObject(ReportContext context) {

        return new Head().setGenerateDate(GvDateUtil.formatUs(ReportContextHelper.reportBuilderTime(), GvDateUtil.FORMAT_US_DATETIME_DD_MM_YY_HH_MM_A));
    }


    @Getter
    @Setter
    @Accessors(chain = true)
    public static class Head {
        private String generateDate;
    }

}
