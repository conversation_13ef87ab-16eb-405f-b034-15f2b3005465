package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.DateUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * @ClassName RedemptionSummaryStatisticalBo
 * @Description
 * <AUTHOR>
 * @Date 2023/4/6 16:46
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class RedemptionSummaryStatisticalBo {

    private String transactionDateDay;
    private String merchantCode;
    private String outletCode;
    private String invoiceNumber;
    private String cpgCode;
    private Integer redemptionCount = 1;
    private BigDecimal denomination = BigDecimal.ZERO;
    private String notes;

    public static RedemptionSummaryStatisticalBo convert(RedemptionBo bo) {

        return new RedemptionSummaryStatisticalBo()
                .setTransactionDateDay(DateUtil.format(bo.getTransactionDate(), DateUtil.FORMAT_DEFAULT))
                .setMerchantCode(bo.getMerchantCode())
                .setOutletCode(bo.getOutletCode())
                .setInvoiceNumber(bo.getInvoiceNumber())
                .setCpgCode(bo.getCpgCode())
                .setDenomination(bo.getDenomination())
                .setNotes(bo.getNotes());

    }

    public RedemptionSummaryStatisticalBo merge(RedemptionSummaryStatisticalBo bo) {

        redemptionCount += 1;
        denomination = denomination.add(bo.getDenomination());

        return this;
    }

    public String getGroupKey() {

        return StringUtils.join("_", this.getTransactionDateDay(), this.getMerchantCode(), this.getOutletCode(), this.getCpgCode(), this.getInvoiceNumber());
    }

    public static RedemptionSummaryStatisticalBo newInstance(RedemptionSummaryStatisticalBo t) {

        return new RedemptionSummaryStatisticalBo()
                .setTransactionDateDay(t.getTransactionDateDay())
                .setMerchantCode(t.getMerchantCode())
                .setOutletCode(t.getOutletCode())
                .setInvoiceNumber(t.getInvoiceNumber())
                .setCpgCode(t.getCpgCode())
                .setRedemptionCount(t.getRedemptionCount())
                .setDenomination(t.getDenomination())
                .setNotes(t.getNotes());
    }
}
