package com.gtech.gvcore.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.request.posaccount.CreatePosAccountRequest;
import com.gtech.gvcore.common.request.posaccount.DeletePosAccountRequest;
import com.gtech.gvcore.common.request.posaccount.QueryPosAccountListRequest;
import com.gtech.gvcore.common.request.posaccount.UpdatePosAccountRequest;
import com.gtech.gvcore.common.response.posaccount.PosAccountResponse;
import com.gtech.gvcore.dao.mapper.PosAccountMapper;
import com.gtech.gvcore.dao.model.PosAccount;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.PosAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

@Service
public class PosAccountServiceImpl implements PosAccountService {

    @Autowired
    private PosAccountMapper posAccountMapper;

    @Autowired
    private GvCodeHelper gvCodeHelper;

    @Override
    public Result<String> createPosAccount(CreatePosAccountRequest request) {
        PosAccount posAccount = BeanCopyUtils.jsonCopyBean(request, PosAccount.class);
        String code = gvCodeHelper.generatePosAccountCode();
        posAccount.setPosAccountCode(code);
        posAccount.setCreateTime(new Date());
        posAccountMapper.insert(posAccount);
        return Result.ok(code);
    }

    @Override
    public Result<String> updatePosAccount(UpdatePosAccountRequest request) {
        PosAccount posAccount = BeanCopyUtils.jsonCopyBean(request, PosAccount.class);
        Example example = new Example(PosAccount.class);
        example.createCriteria()
                .andEqualTo("posAccountCode",request.getPosAccountCode());
        posAccountMapper.updateByConditionSelective(posAccount,example);
        return Result.ok(request.getPosAccountCode());
    }

    @Override
    public Result<String> deletePosAccount(DeletePosAccountRequest request) {
        Example example = new Example(PosAccount.class);
        example.createCriteria()
                .andEqualTo("posAccountCode",request.getPosAccountCode());
        posAccountMapper.deleteByCondition(example);
        return Result.ok(request.getPosAccountCode());
    }

    @Override
    public PageResult<PosAccountResponse> queryPosAccountList(QueryPosAccountListRequest request) {
        PageHelper.startPage(request.getPageNum(),request.getPageSize());
        Example example = new Example(PosAccount.class);
        Example.Criteria criteria = example.createCriteria();
        List<PosAccount> posAccounts = posAccountMapper.selectByCondition(example);
        PageInfo<PosAccount> info = PageInfo.of(posAccounts);
        return new PageResult<>(BeanCopyUtils.jsonCopyList(info.getList(),PosAccountResponse.class),info.getTotal());
    }

    @Override
    public Result<PosAccountResponse> getPosAccount(QueryPosAccountListRequest request) {
        PosAccount posAccounts = posAccountMapper.selectOne(PosAccount.builder().outletType(request.getOutletType()).posAccount(request.getPosAccount()).posPassword(request.getPosPassword()).build());
        if (null == posAccounts){
            return Result.failed("Pos account is null.");
        }
        return Result.ok(BeanCopyUtils.jsonCopyBean(posAccounts,PosAccountResponse.class));
    }
}
