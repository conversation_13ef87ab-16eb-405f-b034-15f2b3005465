package com.gtech.gvcore.service.report.impl.support.bulk;

import com.gtech.basic.masterdata.core.controller.QueryResult;
import com.gtech.basic.masterdata.web.entity.MasterDataDdLangEntity;
import com.gtech.basic.masterdata.web.request.QueryDdLangRequest;
import com.gtech.basic.masterdata.web.service.MasterDataDdLangService;
import com.gtech.commons.result.PageResult;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.CustomerOrderStatusEnum;
import com.gtech.gvcore.common.request.vouchertype.QueryVoucherTypeRequest;
import com.gtech.gvcore.common.response.vouchertype.VoucherTypeResponse;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.MeansOfPayment;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.service.CustomerService;
import com.gtech.gvcore.service.GvUserAccountService;
import com.gtech.gvcore.service.MeansOfPaymentService;
import com.gtech.gvcore.service.MerchantService;
import com.gtech.gvcore.service.OutletService;
import com.gtech.gvcore.service.VoucherTypeService;
import com.gtech.gvcore.service.report.impl.bo.BulkOrderReportBasicDataContext;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName BulkOrderReportBasicDataContextFactory
 * @Description BulkOrderReportBasicDataContext 工厂类
 * <AUTHOR>
 * @Date 2022/7/13 11:43
 * @Version V1.0
 **/
@Component
public class BulkOrderReportBasicDataContextFactory {

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private VoucherTypeService voucherTypeService;

    @Autowired
    private MasterDataDdLangService masterDataDdLangService;

    @Autowired
    private MeansOfPaymentService meansOfPaymentService;

    @Autowired
    private GvUserAccountService userAccountService;

    @Autowired
    protected OutletService outletService;

    public BulkOrderReportBasicDataContext buildBasicDataContext(final Collection<CustomerOrder> customerOrderList) {

        final BulkOrderReportBasicDataContext.BulkOrderReportBasicDataContextBuilder builder = BulkOrderReportBasicDataContext.builder();

        if (CollectionUtils.isEmpty(customerOrderList)) {
            return builder.build();
        }

        // outlet
        final Map<String, Outlet> outletCodeOutletMap = this.outletService.queryOutletMapByOutletCodeList(customerOrderList
                .stream().map(CustomerOrder::getOutletCode).distinct().collect(Collectors.toList()));
        builder.outletMap(outletCodeOutletMap);

        // merchant 通过outlet获取
        final Map<String, Merchant> merchantCodeMerchantMap = this.merchantService.queryMerchantMapByMerchantCodeList(outletCodeOutletMap.values()
                .stream().map(Outlet::getMerchantCode).distinct().collect(Collectors.toList()));
        builder.merchantMap(merchantCodeMerchantMap);

        // customer 编码与customer映射
        final Map<String, Customer> customerCodeCustomerMap = this.customerService.queryCustomerByCodes(customerOrderList.stream().map(CustomerOrder::getCustomerCode).distinct().collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(Customer::getCustomerCode, Function.identity(), (v1, v2) -> v2));
        builder.customerMap(customerCodeCustomerMap);

        // 订单状态与描述映射
        final Map<String, String> orderStatusMap = this.queryOrderStatusMap();
        orderStatusMap.put(CustomerOrderStatusEnum.API.getStatus(), CustomerOrderStatusEnum.COMPLETED.getDesc());
        builder.orderStatusMap(orderStatusMap);

        // 折扣类型与描述映射
        final Map<String, String> discountTypeMap = this.queryDiscountTypeMap();
        builder.discountTypeMap(discountTypeMap);

        // 支付方式编码与支付方式映射
        final Map<String, String> meansOfPaymentCodeNameMap = this.meansOfPaymentService.queryByCodeList(customerOrderList
                        .stream().map(CustomerOrder::getMeansOfPaymentCode).distinct().collect(Collectors.toList())
                )
                .values().stream().collect(Collectors.toMap(MeansOfPayment::getMeansOfPaymentCode, MeansOfPayment::getMopName, (v1, v2) -> v2));
        builder.meansOfPaymentMap(meansOfPaymentCodeNameMap);

        final Map<String, String> userCodeUserNameMap = this.userAccountService.queryFullNameByCodeList(customerOrderList.stream().map(CustomerOrder::getCreateUser).distinct().collect(Collectors.toList()));
        builder.userNameMap(userCodeUserNameMap);

        final PageResult<VoucherTypeResponse> voucherTypeResponsePageResult = this.voucherTypeService.queryVoucherTypeList(QueryVoucherTypeRequest.builder().build());
        final Map<String, String> voucherTypeMap = voucherTypeResponsePageResult.getData().getList().stream().collect(Collectors.toMap(VoucherTypeResponse::getDdValue, VoucherTypeResponse::getDdText, (v1, v2) -> v2));
        builder.voucherTypeMap(voucherTypeMap);

        return builder.build();
    }

    private Map<String, String> queryDiscountTypeMap() {
        final QueryDdLangRequest queryDdLangRequest = new QueryDdLangRequest();
        queryDdLangRequest.setTenantCode(GvcoreConstants.SYSTEM_DEFAULT);
        queryDdLangRequest.setDdCode(GvcoreConstants.PRODUCT_CATEGORY_DISCOUNT_TYPE);
        queryDdLangRequest.setPageSize(100);
        final QueryResult<MasterDataDdLangEntity> masterDataDdLangEntityQueryResult = this.masterDataDdLangService.queryPagesAll(queryDdLangRequest);
        return masterDataDdLangEntityQueryResult.getList().stream().collect(Collectors.toMap(MasterDataDdLangEntity::getDdValue, MasterDataDdLangEntity::getDdText, (v1, v2) -> v2));
    }

    private Map<String, String> queryOrderStatusMap() {
        final QueryDdLangRequest queryDdLangRequest = new QueryDdLangRequest();
        queryDdLangRequest.setTenantCode(GvcoreConstants.SYSTEM_DEFAULT);
        queryDdLangRequest.setDdCode(GvcoreConstants.CUSTOMER_ORDER_STATUS);
        queryDdLangRequest.setPageSize(100);
        final QueryResult<MasterDataDdLangEntity> masterDataDdLangEntityQueryResult = this.masterDataDdLangService.queryPagesAll(queryDdLangRequest);
        return masterDataDdLangEntityQueryResult.getList().stream().collect(Collectors.toMap(MasterDataDdLangEntity::getDdValue, MasterDataDdLangEntity::getDdText, (v1, v2) -> v2));
    }

}
