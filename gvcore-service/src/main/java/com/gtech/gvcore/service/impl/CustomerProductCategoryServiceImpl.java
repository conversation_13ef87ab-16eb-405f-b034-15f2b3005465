package com.gtech.gvcore.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.customerproductcategory.*;
import com.gtech.gvcore.common.response.customerproductcategory.CustomerProductCategoryResponse;
import com.gtech.gvcore.dao.mapper.CustomerProductCategoryMapper;
import com.gtech.gvcore.dao.mapper.GcProductCategoryMapper;
import com.gtech.gvcore.dao.mapper.ProductCategoryMapper;
import com.gtech.gvcore.dao.model.CustomerProductCategory;
import com.gtech.gvcore.dao.model.GcProductCategory;
import com.gtech.gvcore.dao.model.ProductCategory;
import com.gtech.gvcore.dao.model.ProductCategoryCpg;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.CustomerProductCategoryService;
import com.gtech.gvcore.service.ProductCategoryCpgService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/2/11 15:27
 */
@Service
public class CustomerProductCategoryServiceImpl implements CustomerProductCategoryService {

    @Autowired
    private CustomerProductCategoryMapper customerProductCategoryMapper;

    @Autowired
    private GvCodeHelper codeHelper;
    
    @Autowired
    private ProductCategoryMapper productCategoryMapper;
    
    @Autowired
    private GcProductCategoryMapper gcProductCategoryMapper;

    @Override
    public Result<Void> createCustomerProductCategory(CreateCustomerProductCategoryRequest param) {

        //批量添加
        if (CollectionUtils.isNotEmpty(param.getProductCategoryCodeList())) {
            ArrayList<CustomerProductCategory> entities = new ArrayList<>();
            List<String> list = param.getProductCategoryCodeList();
            
            // 预先查询所有分类的类型
            Map<String, String> categoryTypeMap = getCategoryTypesMap(list);
            
            for (String categoryCode : list) {
                CustomerProductCategory entity = new CustomerProductCategory();
                entity.setCustomerProductCategoryCode(codeHelper.generateCustomerProductCategoryCode());
                entity.setCustomerCode(param.getCustomerCode());
                entity.setProductCategoryCode(categoryCode);
                entity.setStatus(GvcoreConstants.STATUS_ENABLE);
                entity.setCreateUser(param.getCreateUser());
                entity.setCreateTime(new Date());
                
                // 确定分类类型：优先使用请求中的类型，其次使用查询到的类型，最后使用默认值"voucher"
                String categoryType = categoryTypeMap.getOrDefault(categoryCode, "voucher");
                entity.setCategoryType(categoryType);
                
                entities.add(entity);
            }
            customerProductCategoryMapper.insertList(entities);
            return Result.ok();
        }

        CustomerProductCategory entity = BeanCopyUtils.jsonCopyBean(param, CustomerProductCategory.class);
        entity.setCustomerProductCategoryCode(codeHelper.generateCustomerProductCategoryCode());
        entity.setCreateTime(new Date());
        //默认开启
        entity.setStatus(GvcoreConstants.STATUS_ENABLE);
        
        // 如果requestCategoryType为空，查询该分类的类型
        if (StringUtils.isBlank(entity.getCategoryType())) {
            List<String> categoryCodeList = Collections.singletonList(entity.getProductCategoryCode());
            Map<String, String> categoryTypeMap = getCategoryTypesMap(categoryCodeList);
            String categoryType = categoryTypeMap.getOrDefault(entity.getProductCategoryCode(), "voucher");
            entity.setCategoryType(categoryType);
        }

        try {
            customerProductCategoryMapper.insertSelective(entity);
        } catch (DuplicateKeyException e) {
            return Result.failed(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                    ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
        }
        return Result.ok();
    }
    
    /**
     * 查询产品分类的类型
     * 通过查询ProductCategory表和GcProductCategory表来确定分类类型
     * 
     * @param categoryCodeList 产品分类编码列表
     * @return 分类编码->分类类型的映射
     */
    private Map<String, String> getCategoryTypesMap(List<String> categoryCodeList) {
        Map<String, String> result = new HashMap<>();
        if (CollectionUtils.isEmpty(categoryCodeList)) {
            return result;
        }
        
        // 批量查询voucher类型的分类
        Example voucherExample = new Example(ProductCategory.class);
        voucherExample.createCriteria().andIn("productCategoryCode", categoryCodeList);
        List<ProductCategory> voucherCategories = productCategoryMapper.selectByCondition(voucherExample);
        
        // 将所有voucher类型的分类加入结果
        if (CollectionUtils.isNotEmpty(voucherCategories)) {
            for (ProductCategory category : voucherCategories) {
                result.put(category.getProductCategoryCode(), "voucher");
            }
        }
        
        // 查找所有未匹配的分类编码
        List<String> unmatchedCodes = new ArrayList<>(categoryCodeList);
        unmatchedCodes.removeAll(result.keySet());
        
        // 如果还有未匹配的分类编码，查询gc类型的分类
        if (CollectionUtils.isNotEmpty(unmatchedCodes)) {
            Example gcExample = new Example(GcProductCategory.class);
            gcExample.createCriteria().andIn("productCategoryCode", unmatchedCodes);
            List<GcProductCategory> gcCategories = gcProductCategoryMapper.selectByCondition(gcExample);
            
            // 将所有gc类型的分类加入结果
            if (CollectionUtils.isNotEmpty(gcCategories)) {
                for (GcProductCategory gcCategory : gcCategories) {
                    result.put(gcCategory.getProductCategoryCode(), "gc");
                }
            }
        }
        
        return result;
    }

    @Override
    public Result<Void> updateCustomerProductCategory(UpdateCustomerProductCategoryRequest param) {
        CustomerProductCategory entity = BeanCopyUtils.jsonCopyBean(param, CustomerProductCategory.class);
        entity.setUpdateTime(new Date());

        Example example = new Example(CustomerProductCategory.class);
        example.createCriteria()
                .andEqualTo(CustomerProductCategory.C_CUSTOMER_PRODUCT_CATEGORY_CODE,param.getCustomerProductCategoryCode());

        try {
            customerProductCategoryMapper.updateByConditionSelective(entity,example);
        } catch (DuplicateKeyException e) {
            return Result.failed(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                    ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
        }
        return Result.ok();
    }

    @Override
    public Result<Void> deleteCustomerProductCategory(DeleteCustomerProductCategoryRequest param) {
        Example example = new Example(CustomerProductCategory.class);
        example.createCriteria()
                .andEqualTo(CustomerProductCategory.C_CUSTOMER_PRODUCT_CATEGORY_CODE,param.getCustomerProductCategoryCode());
        customerProductCategoryMapper.deleteByCondition(example);

        return Result.ok();
    }


    @Override
    public Result<Void> deleteCustomerProductCategoryByCustomer(String param) {
        Example example = new Example(CustomerProductCategory.class);
        example.createCriteria()
                .andEqualTo(CustomerProductCategory.C_CUSTOMER_CODE,param);
        customerProductCategoryMapper.deleteByCondition(example);

        return Result.ok();
    }

    @Override
    public PageResult<CustomerProductCategoryResponse> queryCustomerProductCategoryList(QueryCustomerProductCategoryRequest param) {

        PageHelper.startPage(param.getPageNum(),param.getPageSize());

        Example example = new Example(CustomerProductCategory.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo(CustomerProductCategory.C_CUSTOMER_PRODUCT_CATEGORY_CODE,param.getCustomerProductCategoryCode())
                .andEqualTo(CustomerProductCategory.C_PRODUCT_CATEGORY_CODE,param.getProductCategoryCode())
                .andEqualTo(CustomerProductCategory.C_CUSTOMER_CODE,param.getCustomerCode())
                .andEqualTo(CustomerProductCategory.C_STATUS,param.getStatus());
        
        // 添加分类类型条件
        if (StringUtils.isNotBlank(param.getCategoryType())) {
            criteria.andEqualTo(CustomerProductCategory.C_CATEGORY_TYPE, param.getCategoryType());
        }
        
        //创建时间倒序
        example.orderBy(CustomerProductCategory.C_CREATE_TIME).desc();

        List<CustomerProductCategory> gvCustomerProductCategoryEntities = customerProductCategoryMapper.selectByCondition(example);
        PageInfo<CustomerProductCategory> info = PageInfo.of(gvCustomerProductCategoryEntities);

        return new PageResult<>(BeanCopyUtils.jsonCopyList(info.getList(),CustomerProductCategoryResponse.class),info.getTotal());
    }

    @Override
    public List<CustomerProductCategoryResponse> queryCustomerProductCategoryListByCustomer(QueryCustomerProductCategoryRequest param) {

        Example example = new Example(CustomerProductCategory.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo(CustomerProductCategory.C_CUSTOMER_PRODUCT_CATEGORY_CODE,param.getCustomerProductCategoryCode())
                .andEqualTo(CustomerProductCategory.C_PRODUCT_CATEGORY_CODE,param.getProductCategoryCode())
                .andEqualTo(CustomerProductCategory.C_CUSTOMER_CODE,param.getCustomerCode())
                .andEqualTo(CustomerProductCategory.C_STATUS,param.getStatus());
        
        // 添加分类类型条件
        if (StringUtils.isNotBlank(param.getCategoryType())) {
            criteria.andEqualTo(CustomerProductCategory.C_CATEGORY_TYPE, param.getCategoryType());
        }
        
        //创建时间倒序
        example.orderBy(CustomerProductCategory.C_CREATE_TIME).desc();

        List<CustomerProductCategory> gvCustomerProductCategoryEntities = customerProductCategoryMapper.selectByCondition(example);

        return BeanCopyUtils.jsonCopyList(gvCustomerProductCategoryEntities,CustomerProductCategoryResponse.class);
    }

    @Override
    public List<CustomerProductCategoryResponse> queryCustomerProductCategoryListByCustomer(String customer) {
        return customerProductCategoryMapper.queryCustomerProductCategoryListByCustomer(customer);
    }

    @Override
    public List<CustomerProductCategoryResponse> queryCustomerProductCategoryListByCustomerList(List<String> customerList) {
        return customerProductCategoryMapper.queryCustomerProductCategoryListByCustomerList(customerList);
    }

    @Override
    public CustomerProductCategoryResponse getCustomerProductCategory(GetCustomerProductCategoryRequest param) {
        CustomerProductCategory entity = BeanCopyUtils.jsonCopyBean(param, CustomerProductCategory.class);
        CustomerProductCategory customerProductCategory = customerProductCategoryMapper.selectOne(entity);

        return BeanCopyUtils.jsonCopyBean(customerProductCategory,CustomerProductCategoryResponse.class);
    }
}
