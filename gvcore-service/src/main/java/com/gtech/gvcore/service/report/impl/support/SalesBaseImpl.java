package com.gtech.gvcore.service.report.impl.support;

import com.google.common.collect.Lists;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.enums.VoucherStatusEnableDisableEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bo.SalesBo;
import com.gtech.gvcore.service.report.impl.param.SalesQueryData;
import com.gtech.gvcore.service.report.impl.support.sales.CancelSalesDataRefresh;
import com.gtech.gvcore.service.report.impl.support.sales.bo.CancelSalesDataBo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * @ClassName SalesBaseImpl
 * @Description sales report base impl
 * <AUTHOR>
 * @Date 2023/3/22 15:19
 * @Version V1.0
 **/
public abstract class SalesBaseImpl<T> extends ReportSupport implements BusinessReport<SalesQueryData, T> {


    @Autowired
    private CancelSalesDataRefresh cancelSalesDataRefresh;

    @Override
    public final SalesQueryData builderQueryParam(CreateReportRequest reportParam) {

        SalesQueryData param = new SalesQueryData();

        param.setIssuerCode(reportParam.getIssuerCode());
        param.setMerchantCodeList(reportParam.getMerchantCodes());
        param.setOutletCodeList(reportParam.getOutletCodes());

        param.setTransactionDateStart(reportParam.getTransactionDateStart());
        param.setTransactionDateEnd(reportParam.getTransactionDateEnd());

        param.setCpgCodeList(reportParam.getCpgCodes());
        param.setInvoiceNumber(reportParam.getInvoiceNo());
        param.setVoucherCodeNumStart(reportParam.getVoucherCodeNumStart());
        param.setVoucherCodeNumEnd(reportParam.getVoucherCodeNumEnd());

        addParam(param, reportParam);

        return param;
    }

    protected abstract void addParam(SalesQueryData param, CreateReportRequest reportParam);

    /**
     * page size
     */
    private static final int PAGE_SIZE = 1000;

    /**
     * find bo list
     * @param param
     * @return
     */
    protected final List<SalesBo> getBoList (SalesQueryData param) {

        List<SalesBo> needCheckReissue = new CopyOnWriteArrayList<>();

        //存在交易id的情况则需要根据交易id进行查询
        List<String> transactionIdList = param.getTransactionIdList();
        if (CollectionUtils.isNotEmpty(transactionIdList)) {

            //result
            Map<String, SalesBo> voucherBoMap = new HashMap<>();


            //分页查询
            //使用交易id limit 1000 进行插叙 因transaction id eq customer order no 所以筛选结果会超出1000条 这里使用分野插叙
            ListUtils.partition(transactionIdList, PAGE_SIZE).forEach(e ->
                    PollPageHelper.pollSelect(reportBusinessMapper::salesReport, param.setTransactionIdList(e), l ->
                            l.forEach(d -> {

                                final String voucherCode = d.getVoucherCode();
                                final SalesBo bo = voucherBoMap.get(voucherCode);

                                //添加到列表中等待检查
                                if (d.getTransactionType().equals(TransactionTypeEnum.GIFT_CARD_REISSUE.getCode())){
                                    needCheckReissue.add(d);
                                }

                                //如果获取不到 或者 获取到的数据数据不是卡券最新的操作记录 则使用e的数据覆盖当前数据
                                if (null == bo || (bo.getTransactionNumber().compareTo(d.getTransactionNumber()) < 0)) voucherBoMap.put(voucherCode, d);

                            })));

            // empty
            if (CollectionUtils.isEmpty(voucherBoMap.values())) return Collections.emptyList();
            //checkReissue reissue的新券不能出现在销售报表里
            checkReissueVoucher(needCheckReissue,voucherBoMap);

            //result
            return voucherBoMap.values().stream().filter(e -> TransactionTypeEnum.GIFT_CARD_SELL.equalsCode(e.getTransactionType())).collect(Collectors.toList());

        } else {

            // 直接调用方法获取交易列表
            Collection<SalesBo> transactions = PollPageHelper.pollGroupNewTransactionByCodeSelect(reportBusinessMapper::salesReport, param);
            if(CollectionUtils.isEmpty(transactions)) return Collections.emptyList() ;

            transactions.forEach(x->{
                if (TransactionTypeEnum.GIFT_CARD_REISSUE.equalsCode(x.getTransactionType())){
                    needCheckReissue.add(x);
                }
            });
            checkReissueVoucher(needCheckReissue, Lists.newArrayList(transactions));
            // 如果transactions为null，返回一个空列表；否则，过滤并返回符合条件的列表
            return CollectionUtils.isEmpty(transactions) ? Collections.emptyList() : transactions.stream()
                    .filter(s -> TransactionTypeEnum.GIFT_CARD_SELL.equalsCode(s.getTransactionType()))
                    .collect(Collectors.toList());

        }


    }


    private void checkReissueVoucher(List<SalesBo> boList,Map<String, SalesBo> voucherBoMap){
        if (CollectionUtils.isEmpty(boList)) return;
        final JoinDataMap<Voucher> voucherMap = super.getMapByCode(boList, SalesBo::getVoucherCode, Voucher.class);

        boList.forEach(x->{
            Voucher value = voucherMap.findValue(x.getVoucherCode());
            if (VoucherStatusEnableDisableEnum.STATUS_ENABLE.getCode().equals(value.getVoucherStatus())){
                voucherBoMap.remove(value.getVoucherCode());
            }
        });
    }


    private void checkReissueVoucher(List<SalesBo> boList, List<SalesBo> voucherBoList) {
        if (CollectionUtils.isEmpty(boList)) return;
        final JoinDataMap<Voucher> voucherMap = super.getMapByCode(boList, SalesBo::getVoucherCode, Voucher.class);

        voucherBoList.removeIf(x -> {
            Voucher value = voucherMap.findValue(x.getVoucherCode());
            return VoucherStatusEnableDisableEnum.STATUS_DESTROY.getCode().equals(value.getVoucherStatus());
        });
    }



    @Override
    public final List<T> getExportData(SalesQueryData param) {

        List<SalesBo> boList = this.getBoList(param);

        boList = getSalesBos(param, boList);
        if (CollectionUtils.isEmpty(boList)) return Collections.emptyList();

        return getExportData(boList);
    }

    private List<SalesBo> getSalesBos(SalesQueryData param, List<SalesBo> boList) {
        if(CollectionUtils.isEmpty(boList)) return boList;

        List<CancelSalesDataBo> cancelSalesDataBos = cancelSalesDataRefresh.refresh(param.getTransactionDateEnd(),boList);
        if (cancelSalesDataBos != null && !cancelSalesDataBos.isEmpty()) {

            //cancelSalesDataBos组装map，key是voucherCode，value是最大的createTime
            Map<String, Date> cancelSalesDataMap = cancelSalesDataBos.stream()
                    .map(x -> new CancelSalesDataBo(x.getVoucherCode(), x.getCreateTime()))
                    .collect(Collectors.toMap(
                            CancelSalesDataBo::getVoucherCode,
                            CancelSalesDataBo::getCreateTime,
                            (a, b) -> a.compareTo(b) > 0 ? a : b
                    ));

            if (boList != null) {
                //检查销售记录的交易时间是否大于cancelSales的交易时间
                boList = boList.stream()
                        .filter(x -> {
                            Date date = cancelSalesDataMap.get(x.getVoucherCode());
                            if (null == date){
                                return true;
                            }
                            return  date.compareTo(x.getTransactionDate()) < 0;
                        })
                        .collect(Collectors.toList());
            }
        }
        return boList;
    }


    protected abstract List<T> getExportData(List<SalesBo> boList);
}
