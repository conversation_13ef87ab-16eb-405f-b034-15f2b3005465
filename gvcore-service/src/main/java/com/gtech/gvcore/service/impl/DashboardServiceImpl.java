package com.gtech.gvcore.service.impl;

import com.google.api.client.util.Lists;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.DashboardTypeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.enums.VoucherStatusEnum;
import com.gtech.gvcore.common.request.dashboard.DataRecordRequest;
import com.gtech.gvcore.common.request.dashboard.HistogramRequest;
import com.gtech.gvcore.common.request.dashboard.PieChartRequest;
import com.gtech.gvcore.common.request.dashboard.RedemptionOtherSumRequest;
import com.gtech.gvcore.common.request.dashboard.RedemptionProportionRequest;
import com.gtech.gvcore.common.request.dashboard.SalesBarChartRequest;
import com.gtech.gvcore.common.request.dashboard.TopTenRequest;
import com.gtech.gvcore.common.request.dashboard.TotalRedemptionRequest;
import com.gtech.gvcore.common.request.dashboard.TotalSalesRequest;
import com.gtech.gvcore.common.request.dashboard.VcrInStockRequest;
import com.gtech.gvcore.common.response.dashboard.HistogramResponse;
import com.gtech.gvcore.common.response.dashboard.PieChartResponse;
import com.gtech.gvcore.common.response.dashboard.RedemptionOtherSumResponse;
import com.gtech.gvcore.common.response.dashboard.RedemptionProportionResponse;
import com.gtech.gvcore.common.response.dashboard.SalesBarChartResponse;
import com.gtech.gvcore.common.response.dashboard.TopTenResponse;
import com.gtech.gvcore.common.response.dashboard.TotalSalesResponse;
import com.gtech.gvcore.dao.mapper.DashboardDetailMapper;
import com.gtech.gvcore.dao.mapper.DashboardMapper;
import com.gtech.gvcore.dao.mapper.TransactionDataMapper;
import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.dao.model.GvDashboard;
import com.gtech.gvcore.dao.model.GvDashboardDetail;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.dto.DashboardDateDto;
import com.gtech.gvcore.dto.HistogramDto;
import com.gtech.gvcore.dto.PieChartDto;
import com.gtech.gvcore.dto.RedemptionOutletDataDto;
import com.gtech.gvcore.dto.SalesBarChartDto;
import com.gtech.gvcore.dto.TopTenDto;
import com.gtech.gvcore.dto.TransactionDashboardDto;
import com.gtech.gvcore.dto.VcrInStockDto;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.DashboardService;
import com.gtech.gvcore.service.TransactionDataService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.DateTimeException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/8/29 13:48
 */
@Service
public class DashboardServiceImpl implements DashboardService {



    @Autowired
    private DashboardMapper dashboardMapper;


    @Autowired
    private DashboardDetailMapper dashboardDetailMapper;


    @Lazy
    @Autowired
    private TransactionDataService transactionDataService;

    @Autowired
    private GvCodeHelper codeHelper;

    @Autowired
    private TransactionDataMapper transactionDataMapper;

    @Autowired
    private VoucherMapper voucherMapper;

    @Value("${gv.outlet.warehouse.MV01:}")
    private String mv01;


    @Autowired
    private RedisTemplate redisTemplate;

    public static final String MONTH = "month";
    public static final String SYSTEM = "System";

    /**
     * 柱状图  每个月的数据----查询条件 年
     *
     * 条形图  当天  当月  当年 数据----查询条件 当前时间
     *
     */

    @Override
    public void dataRecord(DataRecordRequest request) {

        Date date = new Date();
        if (null != request.getDate()){
            date = request.getDate();
        }

        //统计当前时间的增量销售数据(小时)
        List<TransactionDashboardDto> salesData = transactionDataService.getTransactionDashboard(date, TransactionTypeEnum.GIFT_CARD_SELL);
        //统计当前时间的增量使用数据(小时)
        List<TransactionDashboardDto> redemptionData = transactionDataService.getTransactionDashboard(date, TransactionTypeEnum.GIFT_CARD_REDEEM);

        Map<String, List<TransactionDashboardDto>> issuerActivateData = salesData.stream().collect(Collectors.groupingBy(TransactionDashboardDto::getIssuerCode));
        Map<String, List<TransactionDashboardDto>> issuerRedemptionData = redemptionData.stream().collect(Collectors.groupingBy(TransactionDashboardDto::getIssuerCode));

        addDataRecord(issuerActivateData,DashboardTypeEnum.SALES,date);
        addDataRecord(issuerRedemptionData,DashboardTypeEnum.REDEMPTION,date);

        redisTemplate.opsForValue().set("dataRecordUpdateTime",DateUtil.format(new Date(),DateUtil.FORMAT_YYYYMMDDHHMISS));


    }



    static final  DecimalFormat df = new DecimalFormat("##.##");

    @Override
    public List<HistogramResponse> histogram(HistogramRequest request) {
        DashboardDateDto year = getStartAndEndDate(request.getDate(), "year");

        ArrayList<HistogramResponse> result = new ArrayList<>();
        List<HistogramDto> histogram = dashboardMapper.histogram(year, request.getType(),request.getIssuerCode());
        histogram.forEach(x->{
            HistogramResponse histogramResponse = HistogramResponse.builder()
                    .date(x.getDate())
                    .discount(df.format(x.getDiscount().compareTo(BigDecimal.ZERO)==0 ? BigDecimal.ZERO :
                            x.getDiscount().divide(x.getAmount(),4, BigDecimal.ROUND_DOWN)
                            .multiply(BigDecimal.valueOf(100))))
                    .voucherCount(x.getVoucherCount())
                    .build();
            result.add(histogramResponse);
        });
        return supplementDate(result);
    }

    @Override
    public String lastUpdateTime() {
        Object dataRecordUpdateTime = redisTemplate.opsForValue().get("dataRecordUpdateTime");
        if (null == dataRecordUpdateTime){
            return "";
        }
        return (String)dataRecordUpdateTime;
    }

    String[] monthAndDay = new String[]{"01","02","03","04","05","06","07","08","09","10","11","12"};

    private  List<HistogramResponse> supplementDate(List<HistogramResponse> params){

        List<HistogramResponse> list = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        //当前年份
        int year = calendar.get(Calendar.YEAR);
        for (HistogramResponse data:params){
            list.add(data);
            for (String s:monthAndDay){
                HistogramResponse dataTrans = new HistogramResponse();
                String date = year+"-"+s;
                if (!date.equals(data.getDate())){
                    dataTrans.setDate(date);
                    dataTrans.setVoucherCount(0);
                    dataTrans.setDiscount("0.00");
                    list.add(dataTrans);
                }
            }
        }
        // 集合中相同属性去重、值合并
        return merge(list);
    }
    private  List<HistogramResponse> merge(List<HistogramResponse> list) {
        return list.stream()
                // 表示name为key，接着如果有重复的，那么从DataTrans对象o1与o2中筛选出一个，这里选择o1，
                // 并把name重复，需要将value与o1进行合并的o2, 赋值给o1，最后返回o1
                .collect(Collectors.toMap(HistogramResponse::getDate, a -> a, (o1, o2)-> {
                    o1.setVoucherCount(o1.getVoucherCount() + o2.getVoucherCount());
                    if (!o2.getDiscount().equals("0.00")){
                        o1.setDiscount( o2.getDiscount());
                    }else {
                        o1.setDiscount(o1.getDiscount());
                    }
                    return o1;
                })).values().stream().sorted(Comparator.comparing(HistogramResponse::getDate)).collect(Collectors.toList());
    }




    @Override
    public List<SalesBarChartResponse> salesBarChart(SalesBarChartRequest request) {

        List<SalesBarChartResponse> redisResult = ( List<SalesBarChartResponse>)redisTemplate.opsForValue().get(request.getIssuerCode() + "-salesBarChart");
        if (null!= redisResult){
            return redisResult;
        }

        ArrayList<SalesBarChartResponse> responses = new ArrayList<>();


        /**
         * 获取时间的 (年 月 日)
         *
         */
        Date requestDate = request.getDate();
        DashboardDateDto yearStartAndEnd = getStartAndEndDate(requestDate, "year");
        DashboardDateDto monthStartAndEnd = getStartAndEndDate(requestDate, MONTH);
        DashboardDateDto dayStartAndEnd = getStartAndEndDate(requestDate, "day");
        SalesBarChartDto yearData = dashboardMapper.salesBarChart(yearStartAndEnd.getStartDate(), yearStartAndEnd.getEndDate(), "day", request.getIssuerCode());
        SalesBarChartDto monthData = dashboardMapper.salesBarChart(monthStartAndEnd.getStartDate(), monthStartAndEnd.getEndDate(), "day", request.getIssuerCode());
        SalesBarChartDto dayData = dashboardMapper.salesBarChart(dayStartAndEnd.getStartDate(), dayStartAndEnd.getEndDate(), "day", request.getIssuerCode());


        Date date = request.getDate();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateUtil.FORMAT_YYYYMMDDHHMISS);
        LocalDateTime ldt = LocalDateTime.parse(DateUtil.format(date,DateUtil.FORMAT_YYYYMMDDHHMISS),dateTimeFormatter);
        int year = ldt.getYear();
        int month = ldt.getMonth().getValue();
        int day = ldt.getDayOfMonth();
        int hour = ldt.getHour();



        //day
        Date dayStart = DateUtil.addDay(Date.from(LocalDateTime.of(year, month, day, hour, 0).atZone(ZoneId.systemDefault()).toInstant()),-1);
        SalesBarChartDto yesterdayData = dashboardMapper.salesBarChart(dayStart, dayStart, "hour", request.getIssuerCode());
        Date dayEnd = Date.from(LocalDateTime.of(year, month, day, hour, 0).atZone(ZoneId.systemDefault()).toInstant());
        SalesBarChartDto todayData = dashboardMapper.salesBarChart(dayEnd, dayEnd, "hour", request.getIssuerCode());

        Date monthStart = null;

        //month
        try {
            monthStart = DateUtil.addMonth(Date.from(LocalDateTime.of(year, month, day, hour, 0).atZone(ZoneId.systemDefault()).toInstant()),-1);
        } catch (DateTimeException e) {
            monthStart = Date.from(LocalDateTime.of(year, month, day, hour, 0).atZone(ZoneId.systemDefault()).toInstant());
        }

        SalesBarChartDto lastMonthData = null;

        if(monthStart == Date.from(LocalDateTime.of(year, month, day, hour, 0).atZone(ZoneId.systemDefault()).toInstant())){
            SalesBarChartDto salesBarChartDto = new SalesBarChartDto();
            salesBarChartDto.setDiscount(BigDecimal.ZERO);
            salesBarChartDto.setAmount(BigDecimal.ZERO);
            salesBarChartDto.setVoucherCount(0);

            lastMonthData = salesBarChartDto;

        }else {

            lastMonthData = dashboardMapper.salesBarChart(monthStart, monthStart, "hour", request.getIssuerCode());

        }

        Date monthEnd = Date.from(LocalDateTime.of(year, month, day, hour, 0).atZone(ZoneId.systemDefault()).toInstant());
        SalesBarChartDto currentMonthData = dashboardMapper.salesBarChart(monthEnd, monthEnd, "hour", request.getIssuerCode());

        //year
        Date yearStart = DateUtil.addMonth(Date.from(LocalDateTime.of(year, month, day, hour, 0).atZone(ZoneId.systemDefault()).toInstant()),-12);
        SalesBarChartDto lastYearData = dashboardMapper.salesBarChart(yearStart, yearStart, "hour", request.getIssuerCode());
        Date yearEnd = Date.from(LocalDateTime.of(year, month, day, hour, 0).atZone(ZoneId.systemDefault()).toInstant());
        SalesBarChartDto thisYearData = dashboardMapper.salesBarChart(yearEnd, yearEnd, "hour", request.getIssuerCode());




        SalesBarChartResponse dayResult = new SalesBarChartResponse();
        dayResult.setType("day");
        dayResult.setDiscount(dayData.getDiscount());
        dayResult.setGross(dayData.getAmount());
        dayResult.setNet(dayData.getAmount().subtract(dayData.getDiscount()));
        if(yesterdayData.getAmount().compareTo(BigDecimal.ZERO) == 0){
            dayResult.setGrossGrowthRate("100");
        }else {
            dayResult.setGrossGrowthRate(BigDecimal.ZERO.compareTo(todayData.getAmount()) == 0 ? "0" :
                    df.format((todayData.getAmount().divide(yesterdayData.getAmount(),4, BigDecimal.ROUND_DOWN).subtract(BigDecimal.valueOf(1))).multiply(BigDecimal.valueOf(100))));
        }

        if(yesterdayData.getDiscount().compareTo(BigDecimal.ZERO) == 0){
            dayResult.setDiscountGrowthRate("100");
        }else {
            dayResult.setDiscountGrowthRate(BigDecimal.ZERO.compareTo(yesterdayData.getDiscount()) == 0 ? "0" :
                    df.format((todayData.getDiscount().divide(yesterdayData.getDiscount(), 4, BigDecimal.ROUND_DOWN).subtract(BigDecimal.valueOf(1))).multiply(BigDecimal.valueOf(100))));
        }

        if((yesterdayData.getAmount().subtract(yesterdayData.getDiscount())).compareTo(BigDecimal.ZERO) == 0){
            dayResult.setNetGrowthRate("100");
        }else {
            dayResult.setNetGrowthRate(BigDecimal.ZERO.compareTo(todayData.getAmount().subtract(todayData.getDiscount())) == 0 ? "0" :
                    df.format(((todayData.getAmount().subtract(todayData.getDiscount())).divide((yesterdayData.getAmount().subtract(yesterdayData.getDiscount())), 4, BigDecimal.ROUND_DOWN).subtract(BigDecimal.valueOf(1))).multiply(BigDecimal.valueOf(100))));
        }


        SalesBarChartResponse monthResult = new SalesBarChartResponse();
        monthResult.setType(MONTH);
        monthResult.setDiscount(monthData.getDiscount());
        monthResult.setGross(monthData.getAmount());
        monthResult.setNet(monthData.getAmount().subtract(monthData.getDiscount()));

        if(lastMonthData.getAmount().compareTo(BigDecimal.ZERO) == 0){
            monthResult.setGrossGrowthRate("100");
        }else {
            monthResult.setGrossGrowthRate(BigDecimal.ZERO.compareTo(currentMonthData.getAmount()) == 0 ? "0" :
                    df.format((currentMonthData.getAmount().divide(lastMonthData.getAmount(), 4, BigDecimal.ROUND_DOWN).subtract(BigDecimal.valueOf(1))).multiply(BigDecimal.valueOf(100))));
        }

        if(lastMonthData.getDiscount().compareTo(BigDecimal.ZERO) == 0){
            monthResult.setDiscountGrowthRate("100");
        }else {
            monthResult.setDiscountGrowthRate(BigDecimal.ZERO.compareTo(currentMonthData.getDiscount()) == 0 ? "0" :
                    df.format((currentMonthData.getDiscount().divide(lastMonthData.getDiscount(), 4, BigDecimal.ROUND_DOWN).subtract(BigDecimal.valueOf(1))).multiply(BigDecimal.valueOf(100))));
        }

        if((lastMonthData.getAmount().subtract(lastMonthData.getDiscount())).compareTo(BigDecimal.ZERO) == 0){
            monthResult.setNetGrowthRate("100");
        }else {
            monthResult.setNetGrowthRate(BigDecimal.ZERO.compareTo(currentMonthData.getAmount().subtract(currentMonthData.getDiscount())) == 0 ? "0" :
                    df.format(((currentMonthData.getAmount().subtract(currentMonthData.getDiscount())).divide((lastMonthData.getAmount().subtract(lastMonthData.getDiscount())), 4, BigDecimal.ROUND_DOWN).subtract(BigDecimal.valueOf(1))).multiply(BigDecimal.valueOf(100))));
        }



        SalesBarChartResponse yearResult = new SalesBarChartResponse();
        yearResult.setType("year");
        yearResult.setDiscount(yearData.getDiscount());
        yearResult.setGross(yearData.getAmount());
        yearResult.setNet(yearData.getAmount().subtract(yearData.getDiscount()));

        if((lastYearData.getAmount().subtract(lastYearData.getDiscount())).compareTo(BigDecimal.ZERO) == 0){
            yearResult.setGrossGrowthRate("100");
        }else {
            yearResult.setGrossGrowthRate(BigDecimal.ZERO.compareTo(thisYearData.getAmount()) == 0 ? "0" :
                    df.format((thisYearData.getAmount().divide(lastYearData.getAmount(), 4, BigDecimal.ROUND_DOWN).subtract(BigDecimal.valueOf(1))).multiply(BigDecimal.valueOf(100))));
        }

        if((lastYearData.getDiscount().subtract(lastYearData.getDiscount())).compareTo(BigDecimal.ZERO) == 0){
            yearResult.setDiscountGrowthRate("100");
        }else {
            yearResult.setDiscountGrowthRate(BigDecimal.ZERO.compareTo(thisYearData.getDiscount()) == 0 ? "0" :
                    df.format((thisYearData.getDiscount().divide(lastYearData.getDiscount(), 4, BigDecimal.ROUND_DOWN).subtract(BigDecimal.valueOf(1))).multiply(BigDecimal.valueOf(100))));
        }

        if((lastYearData.getAmount().subtract(lastYearData.getDiscount())).compareTo(BigDecimal.ZERO) == 0){
            yearResult.setNetGrowthRate("100");
        }else {
            yearResult.setNetGrowthRate(BigDecimal.ZERO.compareTo(thisYearData.getAmount().subtract(thisYearData.getDiscount())) == 0 ? "0" :
                    df.format(((thisYearData.getAmount().subtract(thisYearData.getDiscount())).divide((lastYearData.getAmount().subtract(lastYearData.getDiscount())), 4, BigDecimal.ROUND_DOWN).subtract(BigDecimal.valueOf(1))).multiply(BigDecimal.valueOf(100))));
        }

        responses.add(dayResult);
        responses.add(monthResult);
        responses.add(yearResult);


        redisTemplate.opsForValue().set(request.getIssuerCode()+"-salesBarChart",responses,5, TimeUnit.DAYS);
        return responses;
    }


    /**
     * 饼状图
     * outletCode分组获取name对应的金额
     *
     */
    @Override
    public List<PieChartResponse> pieChart(PieChartRequest request) {

        List<PieChartResponse> redisResult = ( List<PieChartResponse>)redisTemplate.opsForValue().get(request.getIssuerCode() + "-pieChart");
        if (null!= redisResult){
            return redisResult;
        }

        DashboardDateDto year = getStartAndEndDate(request.getDate(), "year");

        ArrayList<PieChartResponse> result = new ArrayList<>();
        List<PieChartDto> pieChartDtos = dashboardMapper.pieChart(year,request.getIssuerCode());

        if (CollectionUtils.isEmpty(pieChartDtos)){
            return Lists.newArrayList();
        }

        Map<String, List<PieChartDto>> collect = pieChartDtos.stream().collect(Collectors.groupingBy(PieChartDto::getOutletType));

        List<PieChartDto> offline = collect.get("Offline");
        List<PieChartDto> mvStore = collect.get("MVStore");
        if (CollectionUtils.isNotEmpty(offline)){
            PieChartResponse b2CStore = PieChartResponse.builder()
                    .name("B2C Store")
                    .amount(offline.stream().map(PieChartDto::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
                    .build();
            result.add(b2CStore);
        }

        if (CollectionUtils.isNotEmpty(mvStore)){
            mvStore.forEach(x->{
                PieChartResponse pieChartResponse = PieChartResponse.builder()
                        .name(x.getOutletName())
                        .amount(x.getAmount())
                        .build();
                result.add(pieChartResponse);
            });
        }
        /*ArrayList<PieChartResponse> result = new ArrayList<>();
        result.add(PieChartResponse.builder().name("B2C Store").amount(BigDecimal.valueOf(683100)).build());
        result.add(PieChartResponse.builder().name("MVStore").amount(BigDecimal.valueOf(503100)).build());
        result.add(PieChartResponse.builder().name("Offline").amount(BigDecimal.valueOf(123100)).build());*/

        redisTemplate.opsForValue().set(request.getIssuerCode()+"-pieChart",result,5, TimeUnit.DAYS);

        return result;
    }

    @Override
    public List<TopTenResponse> topTen(TopTenRequest request) {

        List<TopTenResponse> redisResult = ( List<TopTenResponse>)redisTemplate.opsForValue().get(request.getIssuerCode() + "-topTen");
        if (null!= redisResult){
            return redisResult;
        }

        DashboardDateDto year = getStartAndEndDate(request.getDate(), "year");
        ArrayList<String> voucherCodes = new ArrayList<>();

        List<TransactionData> vcrData = transactionDataMapper.selectTransactionDataByDashboard(mv01, GvcoreConstants.MOP_CODE_VCR, TransactionTypeEnum.GIFT_CARD_ACTIVATE.getCode(),year,request.getIssuerCode());
        List<TransactionData> vceData = transactionDataMapper.selectTransactionDataByDashboard(mv01, GvcoreConstants.MOP_CODE_VCE, TransactionTypeEnum.GIFT_CARD_NEW_GENERATE.getCode(),year,request.getIssuerCode());
        voucherCodes.addAll(vcrData.stream().map(TransactionData::getVoucherCode).distinct().collect(Collectors.toList()));
        voucherCodes.addAll(vceData.stream().map(TransactionData::getVoucherCode).distinct().collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(voucherCodes)){
            return new ArrayList<>();
        }
        List<TopTenDto> topTenDtos = new ArrayList<>();

//        List<TopTenDto> topTenDtos = voucherMapper.topTen(voucherCodes);

        /*List<TopTenDto> topTenDtos = new ArrayList<>();
        topTenDtos.add(TopTenDto.builder()
                .name("PT Rojali Maju Jaya Tbk")
                .amount(new BigDecimal(8632100100L))
                .build());
        topTenDtos.add(TopTenDto.builder()
                .name("PT Equity")
                .amount(new BigDecimal(7236001000L))
                .build());
        topTenDtos.add(TopTenDto.builder()
                .name("PT BANK PAN INDONESIA")
                .amount(new BigDecimal(7068001000L))
                .build());
        topTenDtos.add(TopTenDto.builder()
                .name("MAP GV Commerce Customer")
                .amount(new BigDecimal(608760000L))
                .build());
        topTenDtos.add(TopTenDto.builder()
                .name("Cristiano Ronaldo")
                .amount(new BigDecimal(605030000L))
                .build());
        topTenDtos.add(TopTenDto.builder()
                .name("BRANCH JAKARTA")
                .amount(new BigDecimal(6021000000L))
                .build());
        topTenDtos.add(TopTenDto.builder()
                .name("Bank CIMB Niaga")
                .amount(new BigDecimal(401107000L))
                .build());
        topTenDtos.add(TopTenDto.builder()
                .name("PT SNAR SENJA PRATAMA")
                .amount(new BigDecimal(399200000L))
                .build());
        topTenDtos.add(TopTenDto.builder()
                .name("PT Tester")
                .amount(new BigDecimal(341700000L))
                .build());
        topTenDtos.add(TopTenDto.builder()
                .name("PT. Bank CIMB Niaga")
                .amount(new BigDecimal(311940000L))
                .build());*/
        if (CollectionUtils.isEmpty(topTenDtos)) return new ArrayList<>();
        redisTemplate.opsForValue().set(request.getIssuerCode()+"-topTen",topTenDtos,5, TimeUnit.DAYS);
        return BeanCopyUtils.jsonCopyList(topTenDtos, TopTenResponse.class);
    }

    @Override
    public List<TotalSalesResponse> totalSales(TotalSalesRequest request) {

        List<TotalSalesResponse> redisResult = ( List<TotalSalesResponse>)redisTemplate.opsForValue().get(request.getIssuerCode() + "-totalSales");
        if (null!= redisResult){
            return redisResult;
        }

        DashboardDateDto year = getStartAndEndDate(request.getDate(), "year");
        ArrayList<TotalSalesResponse> result = new ArrayList<>();

        List<TransactionData> vcrData = transactionDataMapper.selectTransactionDataByDashboard(null, GvcoreConstants.MOP_CODE_VCR, TransactionTypeEnum.GIFT_CARD_SELL.getCode(),year,request.getIssuerCode());
        List<TransactionData> vceData = transactionDataMapper.selectTransactionDataByDashboard(null, GvcoreConstants.MOP_CODE_VCE, TransactionTypeEnum.GIFT_CARD_SELL.getCode(),year,request.getIssuerCode());
        BigDecimal vcr = vcrData.stream().map(TransactionData::getDenomination).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal vce = vceData.stream().map(TransactionData::getDenomination).reduce(BigDecimal.ZERO, BigDecimal::add);
        result.add(TotalSalesResponse.builder().amount(vce).type("VCE").build());
        result.add(TotalSalesResponse.builder().amount(vcr).type("VCR").build());



        redisTemplate.opsForValue().set(request.getIssuerCode()+"-totalSales",result,5, TimeUnit.DAYS);
        return result;
    }

    @Override
    public BigDecimal totalRedemption(TotalRedemptionRequest request) {
        BigDecimal redisResult = ( BigDecimal)redisTemplate.opsForValue().get(request.getIssuerCode() + "-totalRedemption");
        if (null!= redisResult){
            return redisResult;
        }

        DashboardDateDto year = getStartAndEndDate(request.getDate(), "year");


        BigDecimal bigDecimal = transactionDataMapper.selectTotalRedemptionData(year, request.getIssuerCode());

        redisTemplate.opsForValue().set(request.getIssuerCode()+"-totalRedemption",bigDecimal,5, TimeUnit.DAYS);
        return bigDecimal;
    }

    @Override
    public List<RedemptionProportionResponse> redemptionProportion(RedemptionProportionRequest request) {
        List<RedemptionProportionResponse> redisResult = ( List<RedemptionProportionResponse>)redisTemplate.opsForValue().get(request.getIssuerCode() + "-redemptionProportion");
        if (null!= redisResult){
            return redisResult;
        }

        DashboardDateDto year = getStartAndEndDate(request.getDate(), "year");
        ArrayList<RedemptionProportionResponse> result = new ArrayList<>();

        List<RedemptionOutletDataDto> dataDtos = dashboardMapper.redemptionOutletData(year, request.getIssuerCode());
        dataDtos.forEach(x ->result.add(RedemptionProportionResponse.builder().amount(x.getAmount()).name(x.getSbu()).build()));

        /*result.add(RedemptionProportionResponse.builder().amount(new BigDecimal(51251212)).name("Food and Beverage").build());
        result.add(RedemptionProportionResponse.builder().amount(new BigDecimal(12512312)).name("Acrive").build());
        result.add(RedemptionProportionResponse.builder().amount(new BigDecimal(31363432)).name("Fashion").build());
        result.add(RedemptionProportionResponse.builder().amount(new BigDecimal(1515611)).name("AnB").build());
        */
        redisTemplate.opsForValue().set(request.getIssuerCode()+"-redemptionProportion",result,5, TimeUnit.DAYS);
        return result;
    }

    @Override
    public List<Map<String, String>> vcrInStock(VcrInStockRequest request) {

        ArrayList<Map<String, String>> redisResult = ( ArrayList<Map<String, String>>)redisTemplate.opsForValue().get(request.getIssuerCode() + "-vcrInStock");
        if (null!= redisResult){
            return redisResult;
        }


            ArrayList<Map<String, String>> responses = new ArrayList<>();
        List<VcrInStockDto> outlet = dashboardMapper.vcrInStock(request.getIssuerCode(),"outlet");
        List<VcrInStockDto> wareHouse = dashboardMapper.vcrInStock(request.getIssuerCode(),"Warehouse");
        List<VcrInStockDto> vcrInStockDtos = new  ArrayList<>();
        vcrInStockDtos.addAll(outlet);
        vcrInStockDtos.addAll(wareHouse);

        List<VcrInStockDto> collect = vcrInStockDtos.stream().filter(x -> StringUtil.isNotEmpty(x.getName())).collect(Collectors.toList());
        Map<String, List<VcrInStockDto>> map = collect.stream().collect(Collectors.groupingBy(VcrInStockDto::getName));

        map.forEach((k,v)->{
            HashMap<String, String> kvMap = new LinkedHashMap<>();

            kvMap.put("name",k);
            v.forEach(x->kvMap.put(x.getDenomination().stripTrailingZeros().toPlainString(),x.getVoucherCount().toString()));
            kvMap.put("total",String.valueOf(v.stream().map(VcrInStockDto::getVoucherCount).mapToInt(Integer::intValue).sum()));
            responses.add(kvMap);
        });

        HashMap<String, String> stringStringHashMap = new HashMap<>();
        stringStringHashMap.put("Bank CIMB Niaga","********");
        stringStringHashMap.put("BRANCH JAKARTA","582900");
        stringStringHashMap.put("Cristiano Ronaldo","********");
        HashMap<String, String> stringStringHashMap2 = new HashMap<>();
        stringStringHashMap2.put("MAP GV Commerce Customer","197500");
        stringStringHashMap2.put("PT BANK PAN INDONESIA","8500100");
        stringStringHashMap2.put("PT Equity","6088100");
        ArrayList<Map<String, String>> list = new ArrayList<>();
        list.add(stringStringHashMap);
        list.add(stringStringHashMap2);



        redisTemplate.opsForValue().set(request.getIssuerCode()+"-vcrInStock",responses,5, TimeUnit.DAYS);

        return responses;
    }

    @Override
    public RedemptionOtherSumResponse redemptionOtherSum(RedemptionOtherSumRequest request) {

        RedemptionOtherSumResponse redisResult = (RedemptionOtherSumResponse)redisTemplate.opsForValue().get(request.getIssuerCode() + "-redemptionOtherSum");
        if (null!= redisResult){
            return redisResult;
        }


        /*
        Potential GV Income：潜在的收入，统计当年的已使用券总金额×3%；

        Potential Expired in 2022：潜在的已过期，统计当年券所属人为非Issuer 且券的到期时间小于或等于当年12月31号；

        实体券：券状态为 Activated，包含Deactivated且券的到期时间小于或等于当年12月31号；

        电子券：券状态为 Purchased或Activated，包含Deactivated且券的到期时间小于或等于当年12月31号；

        Potential Total LIabilities：潜在的负债，统计当年所有券状态为Activated、Purchased、Deactivated的总金额；

        Total Inactive Voucher：总停用的券，统计当年所有券状态为Deactivated的总金额。
        */


        RedemptionOtherSumResponse response = new RedemptionOtherSumResponse();


        DashboardDateDto year = getStartAndEndDate(request.getDate(), "year");


        //Potential GV Income：潜在的收入，统计当年的已使用券总金额×3%；

        BigDecimal sumRedemption = dashboardMapper.otherSum(year, request.getIssuerCode(),null,null,null,TransactionTypeEnum.GIFT_CARD_REDEEM.getCode());




        //Potential Expired in 2022：潜在的已过期，统计当年券所属人为非Issuer 且券的到期时间小于或等于当年12月31号；

        BigDecimal activate = dashboardMapper.otherSum(year,request.getIssuerCode(),year.getEndDate(), String.valueOf(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode()), GvcoreConstants.STATUS_ENABLE.toString(),null);
        BigDecimal deActivate = dashboardMapper.otherSum(year,request.getIssuerCode(),year.getEndDate(), null, GvcoreConstants.STATUS_DISABLE.toString(),null);



        //Potential Total LIabilities：潜在的负债，统计当年所有券状态为Activated、Purchased、Deactivated的总金额；

        BigDecimal activateYear = dashboardMapper.otherSum(year,request.getIssuerCode(),null, String.valueOf(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode()), GvcoreConstants.STATUS_ENABLE.toString(),null);
        BigDecimal deActivateYear = dashboardMapper.otherSum(year,request.getIssuerCode(),null, null, GvcoreConstants.STATUS_DISABLE.toString(),null);


        //Total Inactive Voucher：总停用的券，统计当年所有券状态为Deactivated的总金额。

        BigDecimal deActivateData = dashboardMapper.otherSum(year,request.getIssuerCode(),null, null, GvcoreConstants.STATUS_DISABLE.toString(),null);


        sumRedemption = new BigDecimal(30052100);
        activate = new BigDecimal(50052100);
        deActivate = new BigDecimal(5000);
        activateYear = new BigDecimal(80052100);
        deActivateData = new BigDecimal(2000);
        deActivateYear = new BigDecimal(2000);


        BigDecimal potentialGVIncome = sumRedemption.multiply(BigDecimal.valueOf(0.03));
        response.setPotentialExpired(activate.add(deActivate).stripTrailingZeros().toPlainString());
        response.setPotentialGVIncome(potentialGVIncome.stripTrailingZeros().toPlainString());
        response.setPotentialTotalLIabilities(activateYear.add(deActivateYear).stripTrailingZeros().toPlainString());
        response.setTotalInactiveVoucher(deActivateData.stripTrailingZeros().toPlainString());


        redisTemplate.opsForValue().set(request.getIssuerCode()+"-redemptionOtherSum",response,5, TimeUnit.DAYS);

        return response;


    }

    private void addDataRecord(Map<String, List<TransactionDashboardDto>> issuerActivateData,DashboardTypeEnum dashboardTypeEnum ,Date date) {

        issuerActivateData.forEach((k, v) -> {
            String dashboardCode = codeHelper.generateDashboardCode();
            GvDashboard gvDashboard = GvDashboard.builder()
                    .dashboardCode(dashboardCode)
                    .amount(v.stream().map(TransactionDashboardDto::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
                    .discount(v.stream().map(TransactionDashboardDto::getDiscount).collect(Collectors.toList()).isEmpty() ? BigDecimal.ZERO : v.stream().map(TransactionDashboardDto::getDiscount).reduce(BigDecimal.ZERO, BigDecimal::add)  )
                    .dashboardType(dashboardTypeEnum.getCode())
                    .issuerCode(k)
                    .datetime(date)
                    .voucherCount(v.stream().collect(Collectors.summingInt(TransactionDashboardDto::getVoucherCount)))
                    .createUser(SYSTEM)
                    .updateUser(SYSTEM)
                    .createTime(new Date())
                    .updateTime(new Date())
                    .build();

            dashboardMapper.insertSelective(gvDashboard);

            v.forEach(x->{
                GvDashboardDetail detail = GvDashboardDetail.builder()
                        .dashboardCode(dashboardCode)
                        .dashboardDetailCode(codeHelper.generateDashboardDetailCode())
                        .amount(x.getAmount())
                        //.discount(x.getDiscount().compareTo(BigDecimal.ZERO) == 0 ? x.getDiscount() : BigDecimal.ZERO)
                        .dashboardType(dashboardTypeEnum.getCode())
                        .issuerCode(x.getIssuerCode())
                        .outletCode(x.getOutletCode())
                        .outletType(x.getOutletType())
                        .voucherCount(x.getVoucherCount())
                        .datetime(date)
                        .createUser(SYSTEM)
                        .updateUser(SYSTEM)
                        .createTime(new Date())
                        .updateTime(new Date())
                        .build();
                dashboardDetailMapper.insertSelective(detail);
            });

        });
    }






    /**
     * 时间处理
     *
     * type = year,month,day
     *
     */
    private DashboardDateDto getStartAndEndDate(Date date,String type){

        DashboardDateDto dateDto = new DashboardDateDto();

        switch (type){
            case "year":
                dateDto.setStartDate(DateUtil.addDay(DateUtil.getEndOfYear(DateUtil.addMonth(date,-12)),1));
                dateDto.setEndDate(DateUtil.getEndOfYear(date));
               return dateDto;
            case MONTH:
                dateDto.setStartDate(DateUtil.getStartDayOfMonth(date));
                dateDto.setEndDate(DateUtil.addDay(DateUtil.getStartDayOfMonth(DateUtil.addMonth(date,1)),-1));
                return dateDto;
            case "day":
                dateDto.setStartDate(date);
                dateDto.setEndDate(date);
                return dateDto;
            default:return dateDto;
        }

    }



}
