package com.gtech.gvcore.service.report.impl;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.IssueHandlingDetailsService;
import com.gtech.gvcore.service.VoucherRequestDetailsService;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.PollReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.RegenerateActivationCodeBean;
import com.gtech.gvcore.service.report.impl.param.RegenerateActivationCodeQueryDate;

import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName VoucherReturnTransferImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/2/14 16:19
 * @Version V1.0
 **/
@Service
@Slf4j
public class RegenerateActivationCodeImpl
extends ReportSupport
implements BusinessReport<RegenerateActivationCodeQueryDate, RegenerateActivationCodeBean>, PollReport {

    @Autowired
    private VoucherRequestDetailsService voucherRequestDetailsService;
    // voucher request status



    @Autowired
    private IssueHandlingDetailsService issueHandlingDetailsService;


    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        // return report type
        return ReportExportTypeEnum.REGENERATE_ACTIVATION_CODE_REPORT;
    }

    @Override
    public RegenerateActivationCodeQueryDate builderQueryParam(CreateReportRequest reportParam) {

        // create param
        RegenerateActivationCodeQueryDate param = new RegenerateActivationCodeQueryDate();
        // set  time
        param.setStartDate(reportParam.getTransactionDateStart());
        param.setEndDate(reportParam.getTransactionDateEnd());
		// param.setVoucherCodes(getUploadVoucherCode(reportParam));改为前置解析
		param.setVoucherCodes(reportParam.getVoucherCodeList());
        // set request id
        param.setIssueHandlingCode(reportParam.getVoucherRequestId());
        param.setStartVoucherCode(reportParam.getVoucherCodeNumStart());
        param.setEndVoucherCode(reportParam.getVoucherCodeNumEnd());
        param.setCustomerEmail(reportParam.getCustomerEmail());

        return param;
    }


    private static List<String> getUploadVoucherCode(CreateReportRequest reportParam) {

        final List<String> voucherCodeList = new ArrayList<>();
        final String uploadedFileUrl = reportParam.getUploadedFileUrl();

        if (StringUtil.isBlank(uploadedFileUrl)) return voucherCodeList;

        if (uploadedFileUrl.endsWith(".csv")) getUploadVoucherCodeByCSV(voucherCodeList, uploadedFileUrl);
        else getUploadVoucherCodeByEXCEL(voucherCodeList, uploadedFileUrl);

        return voucherCodeList;
    }

    private static void getUploadVoucherCodeByEXCEL(List<String> voucherCodeList, String uploadedFileUrl) {

        try(InputStream is = new URL(uploadedFileUrl).openStream();

            XSSFWorkbook workbook = new XSSFWorkbook(is)) {
            XSSFSheet sheet = workbook.getSheetAt(0);
            int rows = sheet.getPhysicalNumberOfRows();
            XSSFRow row = sheet.getRow(0);
            int cells = row.getPhysicalNumberOfCells();

            if (cells > 1) throw new GTechBaseException(ResultErrorCodeEnum.UPLOADED_FILE_MORE_COLUMNS.code(), ResultErrorCodeEnum.UPLOADED_FILE_MORE_COLUMNS.desc());

            for (int i = 1; i < rows; i++) {
                row = sheet.getRow(i);
                voucherCodeList.add(row.getCell(0).getStringCellValue());
            }

        } catch (IOException e) {
            log.warn(ResultErrorCodeEnum.UPLOADED_FILE_NOT_MEET_STANDARDS.desc() + e.getMessage(), e);
        }
    }

    private static void getUploadVoucherCodeByCSV(List<String> voucherCodeList, String uploadedFileUrl) {

        String[] header = new String[0];

        try (InputStream is = new URL(uploadedFileUrl).openStream();
             InputStreamReader isr = new InputStreamReader(is, StandardCharsets.UTF_8);
             BufferedReader br = new BufferedReader(isr);
             CSVParser csvParser = CSVFormat.DEFAULT.builder().setAllowDuplicateHeaderNames(false).setHeader(header).build().parse(br)) {

            for (CSVRecord csvRecord : csvParser) {

                int recordSize = csvRecord.size();

                if (recordSize > 1) throw new GTechBaseException(ResultErrorCodeEnum.UPLOADED_FILE_MORE_COLUMNS.code(), ResultErrorCodeEnum.UPLOADED_FILE_MORE_COLUMNS.desc());

                voucherCodeList.add(csvRecord.get(0));
            }

        } catch (IOException e) {
            log.warn(ResultErrorCodeEnum.UPLOADED_FILE_NOT_MEET_STANDARDS.desc() + e.getMessage(), e);
        }
    }


    @Override
    public List<RegenerateActivationCodeBean> getExportData(RegenerateActivationCodeQueryDate param) {

        // 获取数据
        List<RegenerateActivationCodeBean> boList = getBoList(param);

        // EMPTY
        if (CollectionUtils.isEmpty(boList)) return new ArrayList<>();
        final JoinDataMap<Voucher> voucherJoinDataMap = super.getMapByCode(boList, RegenerateActivationCodeBean::getVoucherCode, Voucher.class);
        final JoinDataMap<Cpg> cpgJoinDataMap = super.getMapByCode(voucherJoinDataMap.values(), Voucher::getCpgCode, Cpg.class);



        // 查询关联数据
        boList.forEach(bo -> {
            // cpg
            Optional<Cpg> optionalCpg = Optional.ofNullable(
                            voucherJoinDataMap.get(
                                    bo.getVoucherCode()
                            )
                    ).map(Voucher::getCpgCode)
                    .map(cpgJoinDataMap::get);
            Cpg cpg = optionalCpg.orElse(null);
            if (null != cpg) {
                bo.setVoucherProgramGroup(cpg.getCpgName());
                bo.setDenomination(cpg.getDenomination());
            }
        });








        // 转换数据
        return boList;
    }

    /**
     * 获取数据
     * @param param
     * @return
     */
    private List<RegenerateActivationCodeBean> getBoList(RegenerateActivationCodeQueryDate param) {

        // 查询数据
        List<IssueHandlingDetails> issueHandlingDetails = issueHandlingDetailsService.exportIssueHandlingDetails(
                IssueHandlingTypeEnum.RESET_ACTIVATION,
                param.getIssueHandlingCode(),
                param.getStartDate(),
                param.getEndDate(),
                param.getStartVoucherCode(),
                param.getEndVoucherCode(),
                param.getCustomerEmail(),
                param.getVoucherCodes(),
                param.getPageSize(),
                param.getPageNum());

        // return
        return issueHandlingDetails.stream()
                .map(issueHandlingDetail -> new RegenerateActivationCodeBean()
                        .setIssueHandlingCode(issueHandlingDetail.getIssueHandlingCode())
                        .setVoucherCode(issueHandlingDetail.getVoucherCode())
                        .setTransactionDate(issueHandlingDetail.getCreateTime())
                        .setReceiverEmail(issueHandlingDetail.getReceiverEmail()))
                .collect(Collectors.toList());
    }

}