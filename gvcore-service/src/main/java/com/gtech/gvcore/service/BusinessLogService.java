package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.businesslog.CreateBusinessLogRequest;
import com.gtech.gvcore.common.request.businesslog.QueryBusinessLogRequest;
import com.gtech.gvcore.common.response.businesslog.BusinessLogResponse;

/**
 * <AUTHOR>
 * @Date 2022/3/11 17:08
 */
public interface BusinessLogService {

    Result<Void> createBusinessLog(CreateBusinessLogRequest request);

    PageResult<BusinessLogResponse> queryBusinessLog(QueryBusinessLogRequest request);

}
