package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.ConvertUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2022/6/22 10:28
 */
@Getter
@Setter
@Accessors(chain = true)
public class FinanceRetailAdminRedemptionSummaryBo {

    private String merchantCode;

    private String outletCode;

    private String invoiceNumber;

    private String cpgCode;

    private Integer redemptionCount = 1;

    private BigDecimal denomination;

    public static FinanceRetailAdminRedemptionSummaryBo convert(FinanceRetailAdminRedemptionBo bo) {

        return new FinanceRetailAdminRedemptionSummaryBo()
                .setMerchantCode(bo.getMerchantCode())
                .setOutletCode(bo.getOutletCode())
                .setInvoiceNumber(bo.getInvoiceNumber())
                .setCpgCode(bo.getCpgCode())
                .setDenomination(bo.getDenomination());
    }

    public static String getGroupKey(FinanceRetailAdminRedemptionSummaryBo bean) {

        return StringUtils.join("_", bean.getMerchantCode() , bean.getOutletCode() , bean.getCpgCode() , bean.getInvoiceNumber());
    }

    public static FinanceRetailAdminRedemptionSummaryBo newInstance(FinanceRetailAdminRedemptionSummaryBo bean) {

        return new FinanceRetailAdminRedemptionSummaryBo()
                .setMerchantCode(bean.getMerchantCode())
                .setOutletCode(bean.getOutletCode())
                .setCpgCode(bean.getCpgCode())
                .setInvoiceNumber(bean.getInvoiceNumber())
                .setDenomination(bean.getDenomination());
    }

    public FinanceRetailAdminRedemptionSummaryBo merge(FinanceRetailAdminRedemptionSummaryBo bo) {

        redemptionCount += ConvertUtils.toInteger(bo.getRedemptionCount(), 0);
        //数量乘面额
        denomination = denomination.add(bo.getDenomination());

        return this;
    }
}
