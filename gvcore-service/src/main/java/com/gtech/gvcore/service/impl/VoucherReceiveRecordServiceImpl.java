package com.gtech.gvcore.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.request.receive.QueryReceiveRecordRequest;
import com.gtech.gvcore.common.request.receive.VoucherReceiveRecordRequest;
import com.gtech.gvcore.dao.dto.VoucherReceiveDto;
import com.gtech.gvcore.dao.mapper.VoucherReceiveRecordMapper;
import com.gtech.gvcore.dao.model.VoucherReceiveRecord;
import com.gtech.gvcore.service.VoucherReceiveRecordService;

@Service
public class VoucherReceiveRecordServiceImpl implements VoucherReceiveRecordService {

	@Autowired
	private VoucherReceiveRecordMapper voucherReceiveRecordMapper;
	
	@Override
	public void saveVoucherReceiveRecord(List<VoucherReceiveRecordRequest> receiveRecordList) {
		if (CollectionUtils.isEmpty(receiveRecordList)) {
			return;
		}
		List<VoucherReceiveRecord> list = BeanCopyUtils.jsonCopyList(receiveRecordList, VoucherReceiveRecord.class);
		voucherReceiveRecordMapper.insertList(list);
	}

	@Override
	public List<VoucherReceiveDto> queryReceiveRecord(QueryReceiveRecordRequest request) {
		return voucherReceiveRecordMapper.queryReceiveRecord(request);
	}

}
