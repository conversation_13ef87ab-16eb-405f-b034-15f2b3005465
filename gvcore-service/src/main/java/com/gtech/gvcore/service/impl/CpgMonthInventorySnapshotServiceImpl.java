package com.gtech.gvcore.service.impl;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.page.PageParam;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.request.distribution.QueryHistoricalInventoryChartRequest;
import com.gtech.gvcore.common.response.distribution.HistoricalInventoryChartResponse;
import com.gtech.gvcore.common.response.distribution.MonthInventoryResponse;
import com.gtech.gvcore.dao.mapper.CpgMonthInventorySnapshotMapper;
import com.gtech.gvcore.dao.mapper.DistributionItemVoucherMapper;
import com.gtech.gvcore.dao.model.CpgMonthInventorySnapshot;
import com.gtech.gvcore.dto.CpgHistoricalInventory;
import com.gtech.gvcore.dto.CustomerCpgGroup;
import com.gtech.gvcore.dto.GetCpgQuantityNumParam;
import com.gtech.gvcore.service.CpgMonthInventorySnapshotService;
import com.gtech.gvcore.service.CustomerOrderService;
import com.gtech.gvcore.service.VoucherService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName CpgMonthInventorySnapshotServiceImpl
 * @Description CPG月库存快照服务实现
 * <AUTHOR>
 * @Date 2022/7/6 15:28
 * @Version V1.0
 **/
@Slf4j
@Service
public class CpgMonthInventorySnapshotServiceImpl implements CpgMonthInventorySnapshotService {

    @Autowired
    private VoucherService voucherService;

    @Autowired
    private CustomerOrderService customerOrderService;

    @Autowired
    private CpgMonthInventorySnapshotMapper cpgMonthInventorySnapshotMapper;

    @Autowired
    private DistributionItemVoucherMapper distributionItemVoucherMapper;

    @Override
    public HistoricalInventoryChartResponse queryHistoricalInventoryChart(final QueryHistoricalInventoryChartRequest request) {

        final List<MonthInventoryResponse> availableData = new ArrayList<>();
        final List<MonthInventoryResponse> distributedData = new ArrayList<>();

        final HistoricalInventoryChartResponse response = new HistoricalInventoryChartResponse();
        response.setAvailableData(availableData);
        response.setDistributedData(distributedData);

        // 因为只统计当年的数据,所以当月份小于等于1时是不需要查询历史数据
        if (Integer.parseInt(DateUtil.format(new Date(), DateUtil.FORMAT_MM)) > 1) {
            this.addPastMonthInventory2Chart(request, response);
        }

        this.addCurrentMonthInventory2Chart(request, response);

        return response;
    }

    /**
     * 添加当月的库存信息到图标
     *
     * @param request 查询条件
     * @param chart   图标
     * <AUTHOR>
     * @date 2022/7/18 18:00
     * @since 1.0.0
     */
    private void addCurrentMonthInventory2Chart(final QueryHistoricalInventoryChartRequest request, final HistoricalInventoryChartResponse chart) {

        final List<MonthInventoryResponse> availableData = chart.getAvailableData();
        final List<MonthInventoryResponse> distributedData = chart.getDistributedData();

        // 添加当月数据
        final CpgMonthInventorySnapshot cpgMonthInventorySnapshot = this.buildMonthInventory(
                Integer.parseInt(DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMM)),
                request.getCustomerCode(),
                request.getCpgCode());

        final CpgHistoricalInventory currentMonthInventory = new CpgHistoricalInventory();
        currentMonthInventory
                .setMonth(Integer.parseInt(DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMM)))
                .setAvailable(cpgMonthInventorySnapshot.getAvailable())
                .setDistributed(cpgMonthInventorySnapshot.getDistributed());

        this.addMonthData(currentMonthInventory, availableData, distributedData);
    }

    /**
     * 添加历史月份的库存信息到图标中
     *
     * @param request 查询条件
     * @param chart   图标信息
     * <AUTHOR>
     * @date 2022/7/18 17:58
     * @since 1.0.0
     */
    private void addPastMonthInventory2Chart(final QueryHistoricalInventoryChartRequest request, final HistoricalInventoryChartResponse chart) {

        final List<MonthInventoryResponse> availableData = chart.getAvailableData();
        final List<MonthInventoryResponse> distributedData = chart.getDistributedData();

        // 计算月份查询条件 固定为当年1月: 年份*100+1
        final int selectAfterMonth = Integer.parseInt(DateUtil.format(new Date(), DateUtil.FORMAT_YYYY)) * 100 + 1;

        final List<CpgHistoricalInventory> inventoryList = this.cpgMonthInventorySnapshotMapper.queryHistoricalInventoryChart(request.getCustomerCode(), selectAfterMonth, request.getCpgCode());
        // 转换为月份-库存映射
        final Map<Integer, CpgHistoricalInventory> monthInventoryMap = inventoryList.stream().collect(Collectors.toMap(inventory -> inventory.getMonth() % 100, Function.identity(), (v1, v2) -> v2));

        // 获取今年已过的月份(1月到上个月),用于补全数据库空缺的月份
        final int lastMonth = Integer.parseInt(DateUtil.format(DateUtil.addMonth(new Date(), -1), DateUtil.FORMAT_YYYYMM)) % 100;

        CpgHistoricalInventory emptyInv = null;

        for (int month = 1; month <= lastMonth; month++) {
            final CpgHistoricalInventory inventory = monthInventoryMap.get(month);

            if (null == inventory) {

                if (null == emptyInv) {
                    emptyInv = new CpgHistoricalInventory()
                            .setAvailable(0)
                            .setDistributed(0);
                }

                // 设置为当前遍历到的月份
                emptyInv.setMonth(Integer.parseInt(DateUtil.format(new Date(), DateUtil.FORMAT_YYYY)) * 100 + month);

                this.addMonthData(emptyInv, availableData, distributedData);
            } else {

                this.addMonthData(inventory, availableData, distributedData);
            }
        }
    }

    private void addMonthData(final CpgHistoricalInventory inventory, final Collection<MonthInventoryResponse> availableData, final Collection<MonthInventoryResponse> distributedData) {

        final String yearMonthStr = String.valueOf(inventory.getMonth()).substring(0, 4) + "-" + String.valueOf(inventory.getMonth()).substring(4);

        final MonthInventoryResponse available = new MonthInventoryResponse();
        available.setMonth(yearMonthStr);
        available.setCount(inventory.getAvailable());
        availableData.add(available);

        final MonthInventoryResponse distributed = new MonthInventoryResponse();
        distributed.setMonth(yearMonthStr);
        distributed.setCount(inventory.getDistributed());
        distributedData.add(distributed);
    }

    @Override
    public void createMonthInventorySnapshot() {

        final int pageSize = 1000;

        final PageParam pageParam = new PageParam();
        pageParam.setPageNum(1);
        pageParam.setPageSize(pageSize);

        // 上个月的月份数值 yyyyMM
        final int lastMonth = Integer.parseInt(DateUtil.format(DateUtil.addMonth(new Date(), -1), DateUtil.FORMAT_YYYYMM));

        List<CustomerCpgGroup> customerCpgGroupList;

        do {
            customerCpgGroupList = this.voucherService.queryCustomerCpgGroupPage(pageParam);

            for (CustomerCpgGroup customerCpgGroup : customerCpgGroupList) {

                final CpgMonthInventorySnapshot cpgMonthInventorySnapshot =
                        this.buildMonthInventory(lastMonth, customerCpgGroup.getCustomerCode(), customerCpgGroup.getCpgCode());

                try {
                    this.cpgMonthInventorySnapshotMapper.insertSelective(cpgMonthInventorySnapshot);
                } catch (DuplicateKeyException e) {
                    log.info("A primary key conflict was found when creating a snapshot of the Customer's CPG inventory, which may be caused by repeated calls. The data insertion has been discarded. Data information: {}", JSON.toJSONString(cpgMonthInventorySnapshot));
                }
            }//

            pageParam.setPageNum(pageParam.getPageNum() + 1);
        } while (customerCpgGroupList.size() == pageSize);
    }

    /**
     * 创建月度库存记录
     *
     * @param month        要统计的月份 yyyyMM,为null时表示当前
     * @param customerCode 要统计的customerCode
     * @param cpgCode      要统计的cpgCode
     * @return com.gtech.gvcore.dao.model.CpgMonthInventorySnapshot 月度库存记录
     * <AUTHOR>
     * @date 2022/7/18 17:25
     * @since 1.0.0
     */
    private CpgMonthInventorySnapshot buildMonthInventory(final Integer month, final String customerCode, final String cpgCode) {
        final GetCpgQuantityNumParam getCpgQuantityNumParam = new GetCpgQuantityNumParam();
        getCpgQuantityNumParam
                .setCustomerCode(customerCode)
                .setCpgCode(cpgCode);

        if (null != month) getCpgQuantityNumParam.setBeforeTime(DateUtil.getStartDayOfMonth(DateUtil.parseDate(Integer.toString(month), DateUtil.FORMAT_YYYYMM)));

        // 总数量
        final int quantity = this.customerOrderService.getCpgQuantityNum(getCpgQuantityNumParam);

        // 获取已分发数量
        final int distributedQuantity = distributionItemVoucherMapper.countDistributedVoucher(customerCode, cpgCode);

        // 计算可用库存
        final int availableQuantity = quantity - distributedQuantity;

        final CpgMonthInventorySnapshot cpgMonthInventorySnapshot = new CpgMonthInventorySnapshot();
        cpgMonthInventorySnapshot
                .setCustomerCode(customerCode)
                .setCpgCode(cpgCode)
                .setMonth(month)
                .setAvailable(availableQuantity)
                .setDistributed(distributedQuantity);

        return cpgMonthInventorySnapshot;
    }

}
