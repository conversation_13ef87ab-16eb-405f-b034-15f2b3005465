package com.gtech.gvcore.service.report.impl.support.life;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.service.TransactionDataService;
import com.gtech.gvcore.service.VoucherService;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.impl.param.VoucherLifeCycleQueryData;
import com.gtech.gvcore.service.report.impl.support.life.excel.VoucherLifeCycleSheet;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName VoucherLifeCycleAb
 * @Description
 * <AUTHOR>
 * @Date 2023/1/5 20:26
 * @Version V1.0
 **/
public abstract class VoucherLifeCycleAbstract<T> extends ReportSupport implements VoucherLifeCycle {

    @Autowired protected TransactionDataService transactionDataService;
    @Autowired protected VoucherService voucherService;

    @Override
    public VoucherLifeCycleSheet builderSheet(VoucherLifeCycleQueryData param) {

        return VoucherLifeCycleSheet.newSheet(getFillKey(), exportTypeEnum(), builderSheetData(param));
    }

    protected List<T> builderSheetData (VoucherLifeCycleQueryData param) {

        List<T> list = new ArrayList<>();

        Long voucherNumberBegin = param.getVoucherCodeNumStart();
        Long voucherNumberEnd = param.getVoucherCodeNumEnd();
        //都不存在值
        if (null == voucherNumberBegin && null == voucherNumberEnd) return new ArrayList<>();
        //结束不存在值
        if (null == voucherNumberEnd) return builder(BeanCopyUtils.jsonCopyBean(param, VoucherLifeCycleQueryData.class));
        //开始不存在值
        if (null == voucherNumberBegin) {
            VoucherLifeCycleQueryData findParam = BeanCopyUtils.jsonCopyBean(param, VoucherLifeCycleQueryData.class);
            findParam.setVoucherCodeNumStart(param.getVoucherCodeNumEnd());
            findParam.setVoucherCodeNumEnd(null);
            return builder(findParam);
        }
        //都存在值
        for (long i = voucherNumberBegin; i <= voucherNumberEnd; i++) {
            VoucherLifeCycleQueryData reportBasicQueryData = BeanCopyUtils.jsonCopyBean(param, VoucherLifeCycleQueryData.class);
            reportBasicQueryData.setVoucherCodeNumStart(i);
            reportBasicQueryData.setVoucherCode(String.valueOf(i));
            reportBasicQueryData.setVoucherCodeNumEnd(null);
            list.addAll(builder(reportBasicQueryData));
        }

        return list;
    }

    protected abstract List<T> builder(VoucherLifeCycleQueryData queryData);

    protected abstract String getFillKey();

}
