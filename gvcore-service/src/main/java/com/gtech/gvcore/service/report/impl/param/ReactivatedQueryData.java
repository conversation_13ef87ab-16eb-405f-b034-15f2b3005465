package com.gtech.gvcore.service.report.impl.param;

import com.gtech.gvcore.service.report.ReportQueryParam;
import com.gtech.gvcore.service.report.extend.row.TransactionDataPageParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * @ClassName DeactivatedForDetailQueryData
 * @Description blocked deactivated for detail query data
 * <AUTHOR>
 * @Date 2022/9/22 19:30
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class ReactivatedQueryData extends TransactionDataPageParam implements ReportQueryParam {

    //transaction date start 交易时间 开始
    private Date transactionDateStart;

    //transaction date end 交易时间 结束
    private Date transactionDateEnd;

    //merchant code list
    private List<String> merchantCodeList;

    //cpg code list
    private List<String> cpgCodeList;

    //invoice number 发票编号
    private String invoiceNumber;

    private Date voucherEffectiveDateStart;

    private Date voucherEffectiveDateEnd;

    private List<String> issuerCodeList;

    private Long voucherCodeNumStart;

    private Long voucherCodeNumEnd;

    private List<String> outletCodeList;

}
