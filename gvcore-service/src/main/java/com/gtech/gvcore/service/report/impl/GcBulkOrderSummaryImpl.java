package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.ProductCategoryDiscountTypeEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.common.utils.GvConvertUtils;
import com.gtech.gvcore.dao.mapper.CustomerOrderDetailsMapper;
import com.gtech.gvcore.dao.mapper.CustomerOrderMapper;
import com.gtech.gvcore.dao.model.*;
import com.gtech.gvcore.giftcard.infrastructure.entity.GcSalesEntity;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GcSalesMapper;
import com.gtech.gvcore.helper.GvPageHelper;
import com.gtech.gvcore.service.CustomerOrderService;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.PollReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.ReportProportionDataFunction;
import com.gtech.gvcore.service.report.impl.bean.GcBulkOrderSummaryBean;
import com.gtech.gvcore.service.report.impl.bo.BulkOrderReportBasicDataContext;
import com.gtech.gvcore.service.report.impl.param.GcBulkOrderQueryData;
import com.gtech.gvcore.service.report.impl.support.bulk.BulkOrderReportBasicDataContextFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:57
 * @Description:
 */
@Service
public class GcBulkOrderSummaryImpl extends ReportSupport
        implements BusinessReport<GcBulkOrderQueryData, GcBulkOrderSummaryBean>, ReportProportionDataFunction, PollReport {

    @Autowired
    private BulkOrderReportBasicDataContextFactory bulkOrderReportBasicDataContextFactory;
    @Autowired
    private CustomerOrderMapper customerOrderMapper;
    @Autowired
    private CustomerOrderDetailsMapper customerOrderDetailsMapper;
    @Autowired
    private CustomerOrderService customerOrderService;
    @Autowired
    private GcSalesMapper gcSalesMapper;

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_BULK_ORDER_SUMMARY_REPORT;
    }

    @Override
    public GcBulkOrderQueryData builderQueryParam(final CreateReportRequest reportParam) {

        final GcBulkOrderQueryData queryData = new GcBulkOrderQueryData();
        queryData.setTransactionDateStart(reportParam.getTransactionDateStart());
        queryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());
        queryData.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        queryData.setCpgCodeList(reportParam.getCpgCodes());
        queryData.setInvoiceNo(reportParam.getInvoiceNo());
        queryData.setCustomerType(reportParam.getCustomerType());
        queryData.setCustomerCodeList(reportParam.getCustomerCodes());
        queryData.setPurchaseOrderNumber(reportParam.getPurchaseOrderNo());
        if (StringUtils.isNotBlank(reportParam.getVoucherCode())) {
            queryData.setCardNumbers((Arrays.asList(reportParam.getVoucherCode().split(","))));
        }
        return queryData;
    }

    @Override
    public List<GcBulkOrderSummaryBean> getExportData(final GcBulkOrderQueryData queryData) {

        final List<CustomerOrder> customerOrderList = this.customerOrderSummary(queryData);

        if (CollectionUtils.isEmpty(customerOrderList)) return Collections.emptyList();

        final BulkOrderReportBasicDataContext basicDataContext = this.bulkOrderReportBasicDataContextFactory.buildBasicDataContext(customerOrderList);

        List<GcBulkOrderSummaryBean> collect = customerOrderList.stream().map(order -> this.customerOrder2BulkOrderSummary(order, basicDataContext)).collect(Collectors.toList());
        GcBulkOrderSummaryBean bean = new GcBulkOrderSummaryBean();
        bean.setPoDate("Total");
        bean.setPoValue(customerOrderList.stream().map(CustomerOrder::getVoucherAmount).reduce(BigDecimal.ZERO, BigDecimal::add).toString());
        bean.setDiscountAmount(customerOrderList.stream().map(CustomerOrder::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add).toString());
        bean.setTotalCount(customerOrderList.stream().map(CustomerOrder::getVoucherNum).reduce(0, Integer::sum).toString());
        bean.setTotalAmount(bean.getPoValue());
        bean.setTotalNetAmount(collect.stream()
                .map(x -> new BigDecimal(x.getTotalNetAmount())) // String 转 BigDecimal
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .toString());
        collect.add(bean);
        return collect;
    }

    private GcBulkOrderSummaryBean customerOrder2BulkOrderSummary(final CustomerOrder order, final BulkOrderReportBasicDataContext basicDataContext) {

        final GcBulkOrderSummaryBean summary = new GcBulkOrderSummaryBean();
        summary.setPoDate(DateUtil.format(order.getCreateTime(), DateUtil.FORMAT_YYYYMMDDHHMISS));

        Merchant merchant = null;
        final Outlet outlet = basicDataContext.getOutlet(order.getOutletCode());
        if (null != outlet) {
            merchant = basicDataContext.getMerchant(outlet.getMerchantCode());
        }

        if (null != merchant) {
            summary.setMerchant(merchant.getMerchantName());
        }
        String customerName = basicDataContext.getCustomerName(order.getCustomerCode());
        if (StringUtils.isBlank(customerName)) {
            customerName = basicDataContext.getCompanyName(order.getCustomerCode());
        }
        summary.setCustomerName(customerName);
        summary.setGiftCardType(basicDataContext.getVoucherTypeText(order.getMopCode()));
        summary.setPoNumber(order.getPurchaseOrderNo());
        summary.setPoValue(super.toAmount(String.valueOf(order.getVoucherAmount().longValue())));
        summary.setDiscountType(basicDataContext.getDiscountTypeText(order.getDiscountType()));
        summary.setDiscountAmount(super.toAmount(order.getAmount()));
        summary.setTotalNetAmount(super.toAmount(order.getVoucherAmount().subtract(order.getAmount()).setScale(0, RoundingMode.UP)));
        summary.setTotalAmount(summary.getPoValue());
        //如果是百分比折扣，显示折扣值，否则显示0
        if (ProductCategoryDiscountTypeEnum.PERCENTAGE.equalsCode(order.getDiscountType())) {
            summary.setDiscountPercentage(order.getDiscount().setScale(1) + PERCENT);
        } else if (ProductCategoryDiscountTypeEnum.AMOUNT.equalsCode(order.getDiscountType())) {
            // AMOUNT 类型折扣值固定为 0
            summary.setDiscountPercentage("0" + PERCENT);
        } else {
            summary.setDiscountPercentage(EMPTY_PROPORTION);
        }


        summary.setInvoiceNumber(order.getInvoiceNo());
        summary.setPaymentMode(basicDataContext.getPaymentMode(order.getMeansOfPaymentCode()));
        summary.setActivatingUserLogin(basicDataContext.getActivatingUserLogin(order.getCreateUser()));
        summary.setTotalCount(Integer.toString(order.getVoucherNum()));

        final CustomerOrderReceiver customerOrderReceiver = this.customerOrderService.getOrderReceiver(order.getCustomerOrderCode());
        summary.setNotes(null == customerOrderReceiver ? null : customerOrderReceiver.getCustomerRemarks());

        List<GcSalesEntity> gcSalesEntities = selectTransactionByCustomerOrderCode(order.getCustomerOrderCode());
        if (!gcSalesEntities.isEmpty()) {
            BigDecimal amount = gcSalesEntities.stream().map(GcSalesEntity::getDenomination).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(1);
            summary.setTotalAmount(super.toAmount(amount));
        }
        return summary;
    }

    public List<CustomerOrder> customerOrderSummary(final GcBulkOrderQueryData bulkOrderReportCondition) {

        final Example condition = this.buildCustomerOrderSummaryCondition(bulkOrderReportCondition);
        if (null == condition) return Collections.emptyList();

        return this.customerOrderMapper.selectByExampleAndRowBounds(condition, GvPageHelper.getRowBounds(bulkOrderReportCondition));
    }

    /**
     * 组合 CustomerOrderSummary 报表的查询条件
     *
     * @param bulkOrderReportCondition 查询条件
     * <AUTHOR>
     * @date 2022/7/11 15:19
     * @since 1.0.0
     */
    private Example buildCustomerOrderSummaryCondition(final GcBulkOrderQueryData bulkOrderReportCondition) {

        // cpgCode 在 gv_customer_order 中不存在,需要先拿 cpgCode 在 gv_customer_order_details 查询出 customer_order_code 集
        if (!CollectionUtils.isEmpty(bulkOrderReportCondition.getCpgCodeList())) {
            final List<String> customerOrderCodeList = this.customerOrderDetailsMapper.queryOrderCodesByCpgCodeList(bulkOrderReportCondition.getCpgCodeList());
            if (CollectionUtils.isEmpty(customerOrderCodeList)) {
                return null;
            }
            bulkOrderReportCondition.setCustomerOrderCodeList(customerOrderCodeList);
        }

        final Example condition = new Example(CustomerOrder.class);
        condition.createCriteria()
                .andIn(CustomerOrder.C_ISSUER_CODE, GvConvertUtils.toCollection(bulkOrderReportCondition.getIssuerCodeList(), null))
                .andEqualTo(CustomerOrder.C_INVOICE_NO, GvConvertUtils.toString(bulkOrderReportCondition.getInvoiceNo(), null))
                .andEqualTo(CustomerOrder.C_CUSTOMER_TYPE, GvConvertUtils.toString(bulkOrderReportCondition.getCustomerType(), null))
                .andIn(CustomerOrder.C_CUSTOMER_CODE, GvConvertUtils.toCollection(bulkOrderReportCondition.getCustomerCodeList(), null))
                .andEqualTo(CustomerOrder.C_PURCHASE_ORDER_NO, GvConvertUtils.toString(bulkOrderReportCondition.getPurchaseOrderNumber(), null))
                .andIn(CustomerOrder.C_CUSTOMER_ORDER_CODE, GvConvertUtils.toCollection(bulkOrderReportCondition.getCustomerOrderCodeList(), null))
                .andGreaterThanOrEqualTo(CustomerOrder.C_CREATE_TIME, bulkOrderReportCondition.getTransactionDateStart())
                .andLessThanOrEqualTo(CustomerOrder.C_CREATE_TIME, bulkOrderReportCondition.getTransactionDateEnd())
                .andNotEqualTo(CustomerOrder.C_STATUS, "Source Api")
                .andEqualTo(CustomerOrder.C_MOP_CODE, "GC");
        return condition;
    }

    public List<GcSalesEntity> selectTransactionByCustomerOrderCode(final String customerOrderCode) {
        Weekend<GcSalesEntity> weekend = Weekend.of(GcSalesEntity.class);
        WeekendCriteria<GcSalesEntity, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(GcSalesEntity::getCustomerOrderCode, customerOrderCode);
        return gcSalesMapper.selectByCondition(weekend);
    }
}
