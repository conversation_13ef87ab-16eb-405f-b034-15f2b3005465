package com.gtech.gvcore.service.report.export.snapshoot.label;

import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import org.apache.commons.lang3.ArrayUtils;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.EnumMap;
import java.util.Map;

/**
 * @ClassName ReportResultSupportService
 * @Description 报表结果支持服务
 * <AUTHOR>
 * @Date 2022/9/22 14:30
 * @Version V1.0
 **/
public interface ReportLabelSupport {

    ReportExportTypeEnum exportTypeEnum();

    /**
     * 获得结果集类型映射关系
     * 当一个报表存在不止一个sheet (result) 时重写该方法以便于在网页上展示多个sheet 类型数据
     * key：reportType
     * value : result class
     * 默认情况下应取值当前报表类型 于其返回值类型
     *
     */
    default Map<ReportExportTypeEnum, Class<?>> getResultDateType() {

        //init
        EnumMap<ReportExportTypeEnum, Class<?>> typeMap = new EnumMap<>(ReportExportTypeEnum.class);
        typeMap.put(this.exportTypeEnum(), this.getExportDataClass());

        return typeMap;
    }

    /**
     * 使用该方法默认实现请注意类的接口声明需要明确的声明出 BusinessReport 或者 SimpleBusinessReport
     * @return
     */
    default Class<?> getExportDataClass() {

        Class<?> clazz;
        try {

            //type
            Type type = Arrays.stream(this.getClass().getGenericInterfaces())
                    .filter(i -> i.getTypeName().startsWith(BusinessReport.class.getTypeName()))
                    .findFirst().orElse(null);
            if (null == type) return null;
            if (!(type instanceof ParameterizedType)) return null;

            //actualTypeArguments
            Type[] actualTypeArguments = ((ParameterizedType) type).getActualTypeArguments();
            if (ArrayUtils.isEmpty(actualTypeArguments)) return null;

            clazz = Class.forName(actualTypeArguments[1].getTypeName());
        } catch (Exception e) {
            clazz = null;
        }
        return clazz;
    }

    /**
     * head html
     * 由于前端通用架构所以该表头html由后端返回
     * @return
     */
    default String headHtml(ReportContext context) {
        return null;
    }

}
