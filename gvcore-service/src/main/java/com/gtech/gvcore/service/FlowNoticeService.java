package com.gtech.gvcore.service;


import java.util.List;

import com.gtech.gvcore.common.request.flow.DeleteFlowNoticeRequest;
import com.gtech.gvcore.common.request.flow.FlowNoticeRequest;
import com.gtech.gvcore.common.request.flow.GetFlowNoticeRequest;
import com.gtech.gvcore.common.request.flow.SendNoticeRequest;
import com.gtech.gvcore.common.response.flow.FlowNoticeResponse;
import com.gtech.gvcore.dao.model.UserAccount;

public interface FlowNoticeService {

	String saveFlowNotice(FlowNoticeRequest request);
	
	void deleteFlowNotice(DeleteFlowNoticeRequest request);
	
	FlowNoticeResponse getFlowNotice(GetFlowNoticeRequest request);
	
	List<UserAccount> queryUserByFlowNotice(String flowCode, String flowNodeCode, String businessCode);

	void send(SendNoticeRequest request);

}
