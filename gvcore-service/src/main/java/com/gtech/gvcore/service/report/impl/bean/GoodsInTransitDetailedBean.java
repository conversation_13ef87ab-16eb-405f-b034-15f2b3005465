package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/21 11:19
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class GoodsInTransitDetailedBean {

    @ExcelProperty(index = 0,value = "Request ID")
    private String requestId;

    @ExcelProperty(index = 1,value = "Booklet Number")
    private String bookletNumber;

    @ExcelProperty(index = 2,value = "Voucher Number")
    private String voucherNumber;

    @ExcelProperty(index = 3,value = "Inbound")
    private String inbound;

    @ExcelProperty(index = 4,value = "Outbound")
    private String outbound;

    @ExcelProperty(index = 5,value = "Voucher Program Group")
    private String voucherProgramGroup;

    @ExcelProperty(index = 6,value = "Expiry Date")
    private String expiryDate;

    @ExcelProperty(index = 7,value = "Date GV Allocated")
    private String allocatedTime;

    @ExcelProperty(index = 8,value = "Date GV Received")
    private String receivedTime;

}
