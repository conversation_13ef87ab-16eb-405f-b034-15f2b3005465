package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import com.gtech.gvcore.service.report.extend.ReportBeanCustomerAutoFull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName PartnerSalesDetailBean
 * @Description
 * <AUTHOR>
 * @Date 2022/12/29 15:15
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class PartnerSalesDetailBean implements ReportBeanCustomerAutoFull {

    /**
     * 券号
     */
    @ExcelProperty(value = "Voucher Number")
    private String voucherNumber;

    /**
     * 包号
     */
    @ExcelProperty(value = "Booklet Number")
    private String bookletNumber;

    /**
     * Outlet Descriptive
     */
    @ExcelProperty(value = "SBU Company Name")
    private String sbuCompanyName;

    /**
     * Merchant
     */
    @ExcelProperty(value = "Merchant")
    private String merchant;

    /**
     * Outlet
     */
    @ExcelProperty(value = "Outlet")
    private String outlet;

    /**
     * outletCode
     */
    @ExcelProperty(value = "OutletCode")
    private String outletCode;

    /**
     * @deprecated 留空
     */
    @Deprecated
    @ExcelProperty(value = "Outlet Group")
    private String outletGroup;

    /**
     * Outlet Type
     */
    @ExcelProperty(value = "OutletType")
    private String outletType;

    /**
     * Outlet region
     */
    @ExcelProperty(value = "Region")
    private String region;

    /**
     * Branch Name
     */
    @ExcelProperty(value = "Client Name")
    private String customerName;

    /**
     * Company Name
     */
    @ExcelProperty(value = "Company Name")
    private String companyName;

    /**
     * @deprecated customer division
     */
    @Deprecated
    @ExcelProperty(value = "Department/Division/Branch")
    private String departmentDivisionBranch;

    /**
     * Contact First Name + Contact Last Name
     */
    @ExcelProperty(value = "Full Name (Customer Full Name)")
    private String customerFullName;

    @ExcelProperty(value = "First Name")
    private String firstName;

    @ExcelProperty(value = "Last Name")
    private String lastName;

    /**
     * customer email
     */
    @ExcelProperty(value = "Email")
    private String email;

    /**
     * Transaction Date
     */
    @ExcelProperty(value = "Transaction Date")
    private String transactionDate;

    /**
     * Transaction Time
     */
    @ExcelProperty(value = "Transaction Time")
    private String transactionTime;

    /**
     * pos name
     */
    @ExcelProperty(value = "POS Name")
    private String posName;

    /**
     * cpg
     */
    @ExcelProperty(value = "Voucher Program Group")
    private String voucherProgramGroup;

    /**
     * cpg type
     */
    @ExcelProperty(value = "Voucher Program Group Type")
    private String voucherProgramGroupType;

    /**
     * @deprecated 固定值GIFT CARD ACTIVATE
     */
    @Deprecated
    @ExcelProperty(value = "Transaction Type")
    private String transactionType = "GIFT VOUCHER SELL";

    /**
     * @deprecated (固定值 ： IDR)
     */
    @Deprecated
    @ExcelProperty(value = "Base Currency")
    private String baseCurrency = "IDR";

    /**
     * 同面额
     */
    @ReportAmountValue
    @ExcelProperty(value = "Amount (Transacting Merchant's Currency/ Pts)", converter = ExportExcelNumberConverter.class)
    private String amount;

    /**
     * @deprecated (固定值 ： IDR)
     */
    @Deprecated
    @ExcelProperty(value = "Transacting Merchant's Currency")
    private String transactingMerchantCurrency = "IDR";

    /**
     * Invoice Number
     */
    @ExcelProperty(value = "Invoice Number")
    private String invoiceNumber;

    /**
     * Transaction successful.
     */
    @ExcelProperty(value = "Response Message")
    private String responseMessage = "Transaction successful.";

    /**
     * 面额
     */
    @ReportAmountValue
    @ExcelProperty(value = "Denomination", converter = ExportExcelNumberConverter.class)
    private String denomination;

    /**
     * 过期时间
     */
    @ExcelProperty(value = "Expiry Date")
    private String expiryDate;

    /**
     * 发行年份
     */
    @ExcelProperty(value = "Issuance Year")
    private String issuanceYear;

    /**
     * referenceNumber
     */
    @ExcelProperty(value = "Reference Number")
    private String referenceNumber;

    /**
     * 补发前卡号，若卡号为补发，显示原始卡号
     */
    @ExcelProperty(value = "Original VoucherNumber Before Reissue")
    private String originalCardNumberBeforeReissue;

    /**
     * @deprecated 固定值：Swiped
     */
    @Deprecated
    @ExcelProperty(value = "Voucher Entry Mode")
    private String cardEntryMode = "GV POS";

    /**
     * Invoice Number
     */
    @ExcelProperty(value = "Batch Number")
    private String batchNumber;

    /**
     * 审批号
     */
    @ExcelProperty(value = "Approval Code")
    private String approvalCode;
}
