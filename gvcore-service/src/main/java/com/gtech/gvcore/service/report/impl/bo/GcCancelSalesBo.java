package com.gtech.gvcore.service.report.impl.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName CancelSalesBo
 * @Description Cancel Sales BO
 * <AUTHOR>
 * @Date 2023/4/14 15:33
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GcCancelSalesBo {
    private String cardNumber;
    private String cancelCode;
    private String merchantCode;
    private String cpgCode;
    private BigDecimal denomination;
    private Date cancelTime;
    private String invoiceNumber;
    private String customerCode;
    private String notes;
}
