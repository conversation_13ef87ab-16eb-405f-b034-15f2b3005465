package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.VoucherRequestStatusEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.UserAccount;
import com.gtech.gvcore.dao.model.VoucherRequestDetails;
import com.gtech.gvcore.helper.GvPageHelper;
import com.gtech.gvcore.service.VoucherRequestDetailsService;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.PollReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.VoucherRequestAllocateReceiveBean;
import com.gtech.gvcore.service.report.impl.bo.VoucherRequestAllocateReceiveBo;
import com.gtech.gvcore.service.report.impl.param.VoucherRequestAllocateQueryDate;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName VoucherRequestAllocateReceiveImpl
 * @Description
 *  Voucher Request Allocate Receive Report
 *  Poll Report
 *  Simple Business Report
 * <AUTHOR>
 * @Date 2023/2/8 11:00
 * @Version V1.0
 **/

@Service
public class VoucherRequestAllocateReceiveImpl
        extends ReportSupport
        implements BusinessReport<VoucherRequestAllocateQueryDate, VoucherRequestAllocateReceiveBean>, PollReport {

    @Autowired private VoucherRequestDetailsService voucherRequestDetailsService;
    // voucher request status
    private static final VoucherRequestStatusEnum[] ENUMS = {
            VoucherRequestStatusEnum.PENDING_APPROVAL
            , VoucherRequestStatusEnum.CANCELED
            , VoucherRequestStatusEnum.REJECTED
            , VoucherRequestStatusEnum.PENDING_ALLOCATION
    };

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.VOUCHER_REQUEST_ALLOCATE_RECEIVE_REPORT;
    }


    @Override
    public VoucherRequestAllocateQueryDate builderQueryParam(CreateReportRequest reportParam) {

        // 查询参数
        VoucherRequestAllocateQueryDate param = new VoucherRequestAllocateQueryDate();

        // create time
        param.setCreateTimeStart(reportParam.getTransactionDateStart());
        param.setCreateTimeEnd(reportParam.getTransactionDateEnd());

        // issuer code
        param.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);

        // cpg code
        param.setCpgCodeList(reportParam.getCpgCodes());
        // voucher request status
        param.setAllocateStatusList(reportParam.getVoucherAllocationStatusList());
        // voucher request source
        param.setRequestSourceList(reportParam.getVoucherRequestSourceList());
        // voucher request id
        param.setRequestNo(reportParam.getVoucherRequestId());

        // return
        return param;
    }

    @Override
    public List<VoucherRequestAllocateReceiveBean> getExportData(VoucherRequestAllocateQueryDate param) {

        // 获取数据
        List<VoucherRequestAllocateReceiveBo> boList = getBoList(param);

        // EMPTY
        if (CollectionUtils.isEmpty(boList)) return new ArrayList<>();

        // 查询关联数据
        JoinDataMap<UserAccount> userMap = super.getMapByCode(boList, VoucherRequestAllocateReceiveBo::getCreateUser, UserAccount.class);
        JoinDataMap<Cpg> cpgMap = super.getMapByCode(boList, VoucherRequestAllocateReceiveBo::getCpgCode, Cpg.class);

        // 转换数据
        return boList.stream()
                // convert
                .map(e -> new VoucherRequestAllocateReceiveBean()
                        // voucher request id
                        .setRequestId(e.getRequestId())
                        // voucher request source
                        .setRequestSourceName(e.getRequestSource())
                        // cpg
                        .setVpgName(cpgMap.findValue(e.getCpgCode()).getCpgName())
                        // number of voucher
                        .setNumberOfVoucher(String.valueOf(e.getNumberOfVoucher().intValue()))
                        // voucher amount
                        .setVoucherAmount(super.toAmount(e.getVoucherAmount()))
                        // create on
                        .setCreateOn(DateUtil.format(e.getCreateTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_2))
                        // create by
                        .setCreateBy(e.getCreateBy(userMap))
                        // booklet number
                        .setBookletNumber(e.getBookletNumber())
                        // voucher number
                        .setVoucherNumber(e.getVoucherNumber())
                        // voucher request status
                        .setStatus(VoucherRequestStatusEnum.valueOfCode(e.getAllocateStatus()).getDesc())
                ).collect(Collectors.toList());
    }

    /**
     * 获取数据
     * @param param
     * @return
     */
    private List<VoucherRequestAllocateReceiveBo> getBoList(VoucherRequestAllocateQueryDate param) {

        // 查询数据
        List<VoucherRequestAllocateReceiveBo> selectResult = super.reportBusinessMapper.selectVoucherRequestAllocateReceive(param, GvPageHelper.getRowBounds(param));
        if (CollectionUtils.isEmpty(selectResult)) return selectResult;// EMPTY

        // 筛选request数据并查询其明细数据进行合并
        List<VoucherRequestAllocateReceiveBo> requestResult = new ArrayList<>();
        List<VoucherRequestAllocateReceiveBo> requestList = selectResult.stream().filter(e -> StringUtils.isBlank(e.getCpgCode()) || ArrayUtils.contains(ENUMS, VoucherRequestStatusEnum.valueOfCode(e.getAllocateStatus()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(requestList)) return selectResult; // EMPTY
        requestList
                .forEach(e -> {
                    // 查询明细数据
                    List<VoucherRequestDetails> voucherRequestDetails = voucherRequestDetailsService.queryByVoucherRequestCode(e.getRequestId());
                    // 合并数据
                    voucherRequestDetails
                            .forEach(d -> requestResult.add(new VoucherRequestAllocateReceiveBo()
                                    .setRequestId(e.getRequestId())
                                    .setRequestSource(e.getRequestSource())
                                    .setCpgCode(d.getCpgCode())
                                    .setNumberOfVoucher(ConvertUtils.toBigDecimal(d.getVoucherNum(), BigDecimal.ZERO))
                                    .setVoucherAmount(d.getVoucherAmount())
                                    .setCreateTime(e.getSourceCreateTime())
                                    .setCreateUser(e.getCreateUser())
                                    .setAllocateStatus(e.getAllocateStatus()))
                            );
                });

        // 移除request数据
        selectResult.removeAll(requestList);

        // 合并数据
        List<VoucherRequestAllocateReceiveBo> boList = new ArrayList<>(selectResult);
        boList.addAll(requestResult);

        // return
        return boList;
    }
}

