package com.gtech.gvcore.service;

import com.gtech.gvcore.dao.model.VoucherRequestDetails;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022年3月10日
 */
public interface VoucherRequestDetailsService {

    /**
     * 
     * <AUTHOR>
     * @param voucherRequestCode
     * @return
     * @date 2022年3月10日
     */
    List<VoucherRequestDetails> queryByVoucherRequestCode(String voucherRequestCode);

    /**
     * 
     * <AUTHOR>
     * @param voucherRequestCodeList
     * @return
     * @date 2022年3月11日
     */
    Map<String, List<String>> queryDenominationByVoucherRequestCodeList(List<String> voucherRequestCodeList);

}
