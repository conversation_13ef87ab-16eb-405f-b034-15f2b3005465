package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.dao.model.*;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.GcCancelSalesDetailBean;
import com.gtech.gvcore.service.report.impl.bo.GcCancelSalesBo;
import com.gtech.gvcore.service.report.impl.param.GcCancelSalesQueryData;
import com.gtech.gvcore.service.report.impl.support.GcCancelSalesBaseImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:58
 * @Description:
 */
@Service
public class GcCancelSalesDetailImpl extends GcCancelSalesBaseImpl<GcCancelSalesDetailBean>
        implements BusinessReport<GcCancelSalesQueryData, GcCancelSalesDetailBean>, SingleReport {

    @Override
    public List<GcCancelSalesDetailBean> getExportData(GcCancelSalesQueryData queryData) {

        //find
        List<GcCancelSalesBo> list = super.getBoList(queryData);

        //empty
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        //init map
        final JoinDataMap<GcCpg> cpgMap = super.getMapByCode(list, GcCancelSalesBo::getCpgCode, GcCpg.class);
        final JoinDataMap<Customer> customerMap = super.getMapByCode(list, GcCancelSalesBo::getCustomerCode, Customer.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(list, GcCancelSalesBo::getMerchantCode, Merchant.class);

        //convert result
        return list.stream().map(e -> {

            //cancelSalesDetailBean
            return new GcCancelSalesDetailBean()
                    .setTransactionDate(DateUtil.format(e.getCancelTime(), DateUtil.FORMAT_YYYYMMDDHHMISS))
                    .setVoucherNumber(e.getCardNumber())
                    .setMerchant(merchantMap.findValue(e.getMerchantCode()).getMerchantName())
                    .setVoucherProgramGroup(cpgMap.findValue(e.getCpgCode()).getCpgName())
                    .setTransactionAmount(super.toAmount(ConvertUtils.toBigDecimal(e.getDenomination(), BigDecimal.ZERO)))
                    .setCustomerName(customerMap.findValue(e.getCustomerCode()).getCustomerName())
                    .setInvoiceNumber(e.getInvoiceNumber());

        }).collect(Collectors.toList());
    }

    @Override
    public ReportExportTypeEnum exportTypeEnum() {

        //ENUM
        return ReportExportTypeEnum.GC_CANCEL_SALES_DETAILED_REPORT;
    }


}
