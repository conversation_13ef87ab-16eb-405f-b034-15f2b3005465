package com.gtech.gvcore.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.utils.UUIDUtils;
import com.gtech.gvcore.dao.mapper.CpgMapper;
import com.gtech.gvcore.dao.mapper.ProductCategoryCpgMapper;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.ProductCategoryCpg;
import com.gtech.gvcore.giftcard.masterdata.gcpg.dao.GcCpgMapper;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.service.ProductCategoryCpgService;

import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @date 2022年2月16日
 */
@Service
public class ProductCategoryCpgServiceImpl implements ProductCategoryCpgService {

    @Autowired
    private ProductCategoryCpgMapper productCategoryCpgMapper;
    
    @Autowired
    private CpgMapper cpgMapper;
    
    @Autowired
    private GcCpgMapper gcCpgMapper;

    @Override
    public List<ProductCategoryCpg> queryByProductCategoryCode(String productCategoryCode, Integer deleteStatus,String cpgType) {
        if (StringUtils.isBlank(productCategoryCode)) {
            return Collections.emptyList();
        }

        ProductCategoryCpg categoryCpg = new ProductCategoryCpg();
        categoryCpg.setProductCategoryCode(productCategoryCode);
        categoryCpg.setDeleteStatus(deleteStatus);
        categoryCpg.setCpgType(cpgType);
        List<ProductCategoryCpg> categoryCpgs = productCategoryCpgMapper.select(categoryCpg);
        if (CollectionUtils.isEmpty(categoryCpgs)) {
            return Collections.emptyList();
        }
        return categoryCpgs;
    }

    @Transactional
    @Override
    public int insertList(List<ProductCategoryCpg> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        
        // 收集所有的CPG代码，用于批量查询类型
        List<String> cpgCodes = new ArrayList<>();
        for (ProductCategoryCpg categoryCpg : list) {
            if (StringUtils.isNotBlank(categoryCpg.getCpgCode())) {
                cpgCodes.add(categoryCpg.getCpgCode());
            }
        }
        
        // 批量查询CPG类型
        Map<String, String> cpgTypeMap = getCpgTypesMap(cpgCodes);
        
        // 设置每个关联的代码和类型
        for (ProductCategoryCpg productCategoryCpg : list) {
            productCategoryCpg.setProductCategoryCpgCode(UUIDUtils.generateCode());
            productCategoryCpg.setDeleteStatus(GvcoreConstants.DELETE_STATUS_DISABLE);
            
            // 如果cpgType为空，根据查询结果设置
            if (StringUtils.isBlank(productCategoryCpg.getCpgType())) {
                String cpgType = cpgTypeMap.getOrDefault(productCategoryCpg.getCpgCode(), "voucher");
                productCategoryCpg.setCpgType(cpgType);
            }
        }

        return productCategoryCpgMapper.insertList(list);
    }
    
    /**
     * 查询CPG的类型
     * 通过查询Cpg表和GcCpg表来确定CPG类型
     * 
     * @param cpgCodeList CPG编码列表
     * @return CPG编码->类型的映射
     */
    private Map<String, String> getCpgTypesMap(List<String> cpgCodeList) {
        Map<String, String> result = new HashMap<>();
        if (CollectionUtils.isEmpty(cpgCodeList)) {
            return result;
        }
        
        // 批量查询voucher类型的CPG
        Example voucherExample = new Example(Cpg.class);
        voucherExample.createCriteria().andIn("cpgCode", cpgCodeList);
        List<Cpg> voucherCpgs = cpgMapper.selectByCondition(voucherExample);
        
        // 将所有voucher类型的CPG加入结果
        if (CollectionUtils.isNotEmpty(voucherCpgs)) {
            for (Cpg cpg : voucherCpgs) {
                result.put(cpg.getCpgCode(), "voucher");
            }
        }
        
        // 查找所有未匹配的CPG编码
        List<String> unmatchedCodes = new ArrayList<>(cpgCodeList);
        unmatchedCodes.removeAll(result.keySet());
        
        // 如果还有未匹配的CPG编码，查询gc类型的CPG
        if (CollectionUtils.isNotEmpty(unmatchedCodes)) {
            Example gcExample = new Example(GcCpg.class);
            gcExample.createCriteria().andIn("cpgCode", unmatchedCodes);
            List<GcCpg> gcCpgs = gcCpgMapper.selectByCondition(gcExample);
            
            // 将所有gc类型的CPG加入结果
            if (CollectionUtils.isNotEmpty(gcCpgs)) {
                for (GcCpg gcCpg : gcCpgs) {
                    result.put(gcCpg.getCpgCode(), "gc");
                }
            }
        }
        
        return result;
    }

    @Transactional
    @Override
    public int updateByPrimaryKeySelective(ProductCategoryCpg productCategoryCpg) {
        productCategoryCpg.setDeleteStatus(GvcoreConstants.DELETE_STATUS_DISABLE);
        
        // 如果更新时cpgType为空，但有cpgCode，则自动查询并设置类型
        if (StringUtils.isBlank(productCategoryCpg.getCpgType()) && StringUtils.isNotBlank(productCategoryCpg.getCpgCode())) {
            Map<String, String> cpgTypeMap = getCpgTypesMap(Collections.singletonList(productCategoryCpg.getCpgCode()));
            String cpgType = cpgTypeMap.getOrDefault(productCategoryCpg.getCpgCode(), "voucher");
            productCategoryCpg.setCpgType(cpgType);
        }
        
        return productCategoryCpgMapper.updateByPrimaryKeySelective(productCategoryCpg);
    }

    @Transactional
    @Override
    public int deleteStatusByPrimaryKey(ProductCategoryCpg productCategoryCpg) {

        productCategoryCpg.setStatus(GvcoreConstants.STATUS_DISABLE);
        productCategoryCpg.setDeleteStatus(GvcoreConstants.DELETE_STATUS_ENABLE);
        return productCategoryCpgMapper.updateByPrimaryKeySelective(productCategoryCpg);
    }
    
    @Override
    public List<ProductCategoryCpg> selectByCriteria(ProductCategoryCpg criteria) {
        if (criteria == null) {
            return Collections.emptyList();
        }
        
        List<ProductCategoryCpg> categoryCpgs = productCategoryCpgMapper.select(criteria);
        if (CollectionUtils.isEmpty(categoryCpgs)) {
            return Collections.emptyList();
        }
        return categoryCpgs;
    }
}
