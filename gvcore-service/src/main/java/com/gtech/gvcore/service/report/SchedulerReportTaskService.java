package com.gtech.gvcore.service.report;

import com.gtech.gvcore.common.request.flow.SendEmailRequest;

/**
 * @ClassName SchedulerReportTaskService
 * @Description 定时报表任务组件
 * <AUTHOR>
 * @Date 2022/10/9 16:30
 * @Version V1.0
 **/
public interface SchedulerReportTaskService {


    /**
     * 启动任务
     */
    void runTask();

    /**
     * 强制执行
     */
    String enforce(String code);

    /**
     * 执行回调
     */
    void rollbackSchedulerReport(SendEmailRequest request, String schedulerReportCode, String createUserEmail, SendEmailRequest.FileVo fileVo);


}
