package com.gtech.gvcore.service.impl.issuehandle;

import com.gtech.gvcore.common.enums.BookletStatusEnum;
import com.gtech.gvcore.common.enums.IssueHandlingProcessStatusEnum;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.common.enums.VoucherStatusEnum;
import com.gtech.gvcore.common.request.voucherbooklet.VoucherActivateUpdateBookletStatusDto;
import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.IssueHandlerBaseService;
import com.gtech.gvcore.service.VoucherBookletService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class IssueHandlerBulkActiveService extends IssueHandlerValidateService implements IssueHandlerBaseService {
    @Autowired
    private VoucherMapper voucherMapper;

    @Autowired
    private VoucherBookletService voucherBookletService;

    @Override
    public IssueHandlingTypeEnum getIssueHandlingType() {
        return IssueHandlingTypeEnum.BULK_ACTIVATION;
    }


    @Override
	public List<IssueHandlingDetails> validate(List<IssueHandlingDetails> details, String issuerCode) {

		return check(details, issuerCode);
    }

    @Override
	public List<IssueHandlingDetails> execute(List<IssueHandlingDetails> details, String issuerCode) {

		List<IssueHandlingDetails> check = check(details, issuerCode);

        List<IssueHandlingDetails> successVoucherCodes = check.stream()
                .filter(detail -> IssueHandlingProcessStatusEnum.SUCCESS.code().equals(detail.getProcessStatus()))

                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(successVoucherCodes)){
            Map<String, List<IssueHandlingDetails>> orderByOutlet =
                    successVoucherCodes.stream().collect(Collectors.groupingBy(IssueHandlingDetails::getOutletName));
            orderByOutlet.forEach((k,v)->{
                List<String> voucherCodes = v.stream().map(x -> x.getVoucherCode()).collect(Collectors.toList());
                performAction(voucherCodes,IssueHandlerValidateService.outletMapFinal.get(k));
            });
        }

        return check;
    }


	private List<IssueHandlingDetails> check(List<IssueHandlingDetails> details, String issuerCode) {

        if (CollectionUtils.isEmpty(details)) {
            return Collections.emptyList();
        }
		checkIfExist(details, getIssueHandlingType(), issuerCode);
        //checkInvoiceNumber(details, TransactionTypeEnum.GIFT_CARD_NEW_GENERATE);
		checkOutletName(details, issuerCode);
        return details;
    }




    private int performAction(List<String> voucherCodes,String outletCode) {
        Example example = new Example(Voucher.class);
        example.createCriteria().andIn(Voucher.C_VOUCHER_CODE, voucherCodes);
        Voucher voucher = new Voucher();
        voucher.setStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
        voucher.setSalesOutlet(outletCode);
        voucher.setSalesTime(new Date());
        //激活册子
        voucherBookletService.voucherActivateUpdateBookletStatus(
                VoucherActivateUpdateBookletStatusDto.builder().type("0")
                        .voucherCode(voucherCodes)
                        .statusEnum(BookletStatusEnum.PARTIALLY_ACTIVATED)
                        .build());


        return voucherMapper.updateByConditionSelective(voucher, example);
    }


}


