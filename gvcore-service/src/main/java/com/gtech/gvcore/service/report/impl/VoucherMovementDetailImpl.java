package com.gtech.gvcore.service.report.impl;

import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.ReportVoucherStatusEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.CustomerService;
import com.gtech.gvcore.service.VoucherService;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.VoucherMovementDetailedBean;
import com.gtech.gvcore.service.report.impl.bo.VoucherMovementBo;
import com.gtech.gvcore.service.report.impl.param.VoucherMovementSummaryQueryData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @ClassName PerformanceImpl
 * @Description Performance report
 * <AUTHOR>
 * @Date 2022/9/20 16:20
 * @Version V1.0
 **/
@Slf4j
@Service
public class VoucherMovementDetailImpl extends ReportSupport
        implements BusinessReport<VoucherMovementSummaryQueryData, VoucherMovementDetailedBean>, SingleReport {

    @Autowired protected CustomerService customerService;
    @Autowired protected VoucherService voucherService;

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.VOUCHER_MOVEMENT_DETAILED_REPORT;
    }

    @Override
    public VoucherMovementSummaryQueryData builderQueryParam(CreateReportRequest reportParam) {

        VoucherMovementSummaryQueryData voucherMovementQueryData = new VoucherMovementSummaryQueryData();

        voucherMovementQueryData.setTransactionDateStart(reportParam.getTransactionDateStart());
        voucherMovementQueryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());
        voucherMovementQueryData.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        voucherMovementQueryData.setMerchantCodeList(reportParam.getMerchantCodes());
        voucherMovementQueryData.setOutletCodeList(reportParam.getOutletCodes());
        voucherMovementQueryData.setTransactionType(TransactionTypeEnum.GIFT_CARD_SELL.getCode());
        voucherMovementQueryData.setVoucherCodeNumStart(reportParam.getVoucherCodeNumStart());
        voucherMovementQueryData.setVoucherCodeNumEnd(reportParam.getVoucherCodeNumEnd());

        if (CollectionUtils.isNotEmpty(reportParam.getCustomerCodes())) {
            voucherMovementQueryData.setCustomerCode(reportParam.getCustomerCodes().get(0));
        }
        voucherMovementQueryData.setCpgCodeList(reportParam.getCpgCodes());
        voucherMovementQueryData.setVoucherStatusList(reportParam.getVoucherStatus());

        voucherMovementQueryData.setCustomerCode(CollectionUtils.isNotEmpty(reportParam.getCustomerCodes()) ? reportParam.getCustomerCodes().get(0) : null);

        return voucherMovementQueryData;
    }

    @Override
    public List<VoucherMovementDetailedBean> getExportData(VoucherMovementSummaryQueryData queryData) {

        //init file
        Customer customer = super.nonNullGetByCode(queryData.getCustomerCode(), Customer.class);

        // init container
        final Map<Voucher, Cpg> detail = new HashMap<>();

        //find data
        getVoucher(queryData, detail);

        //package
        return packageDetailBeanList(detail, customer);
    }

    private List<VoucherMovementDetailedBean> packageDetailBeanList(Map<Voucher, Cpg> detail, Customer customer) {

        List<VoucherMovementDetailedBean> rel = new ArrayList<>();

        //package
        detail.forEach((v, c) -> rel.add(new VoucherMovementDetailedBean()
                    .setCardNumber(v.getVoucherCode())
                    .setCustomerName(customer.getCustomerName())
                    .setCompanyName(customer.getCompanyName())
                    .setDenomination(super.toAmount(v.getDenomination()))
                    .setStatus(this.getVoucherStatusDesc(v))
                    .setVoucherProgramGroup(c.getCpgName())));

        return rel;
    }

    private void getVoucher(final VoucherMovementSummaryQueryData queryData, final Map<Voucher, Cpg> detailMap) {

        //find
        final List<String> salesArray = this.selectVoucherMovement(queryData);
        final List<Voucher> vouchers = new ArrayList<>(this.getMapByCode(salesArray, Voucher.class).values());
        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(vouchers, Voucher::getCpgCode, Cpg.class);

        List<ReportVoucherStatusEnum> voucherStatusEnums = getReportVoucherStatusEnums(queryData);

        //for group
        vouchers.stream()
                .filter(v -> cpgMap.containsKey(v.getCpgCode()))
                .forEach(v -> {

                    Cpg cpg = cpgMap.findValue(v.getCpgCode());

                    //detail
                    if (CollectionUtils.isEmpty(voucherStatusEnums) || voucherStatusEnums.contains(this.getVoucherStatus(v))) detailMap.put(v, cpg);
                });
    }

    private static List<ReportVoucherStatusEnum> getReportVoucherStatusEnums(VoucherMovementSummaryQueryData queryData) {

        List<String> voucherStatusCodes = queryData.getVoucherStatusList();
        if (CollectionUtils.isEmpty(voucherStatusCodes)) return Collections.emptyList();

        return voucherStatusCodes.stream()
                .filter(StringUtils::isNotBlank)
                .map(ReportVoucherStatusEnum::valueOfCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public List<String> selectVoucherMovement(VoucherMovementSummaryQueryData request) {

        return Optional.of(PollPageHelper.pollGroupNewTransactionByCodeSelect(reportBusinessMapper::selectVoucherMovement, request))
                // filter gift voucher sell
                .map(e -> e.stream().filter(s -> TransactionTypeEnum.GIFT_CARD_SELL.equalsCode(s.getTransactionType())).map(VoucherMovementBo::getVoucherCode).collect(Collectors.toList()))
                .orElse(new ArrayList<>());
    }

}
