package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/21 11:19
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class GoodsInTransitSummaryBean {

    @ExcelProperty(index = 0, value = "Request ID")
    private String requestId;

    @ExcelProperty(index = 1, value = "Outbound Outlet")
    private String outboundOutlet;

    @ExcelProperty(index = 2, value = "Inbound Outlet")
    private String inboundOutlet;

    @ExcelProperty(index = 3, value = "GV Allocation Date")
    private String allocationDate;

    @ExcelProperty(index = 4, value = "GV Receive Date")
    private String receiveDate;

    @ExcelProperty(index = 5, value = "Voucher Program Group")
    private String vpg;

    @ExcelProperty(index = 6, value = "Number of Vouchers", converter = ExportExcelNumberConverter.class)
    private String numberOfVouchers;

    @ReportAmountValue
    @ExcelProperty(index = 7, value = "Total Amount", converter = ExportExcelNumberConverter.class)
    private String voucherAmount;

    @ExcelProperty(index = 8, value = "Transit & Delivery Interval")
    private String transitDeliveryInterval;
}
