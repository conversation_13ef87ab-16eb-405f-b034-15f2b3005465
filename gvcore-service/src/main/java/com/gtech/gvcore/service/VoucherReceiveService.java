package com.gtech.gvcore.service;


import com.gtech.commons.result.PageResult;
import com.gtech.gvcore.common.request.receive.CreateVoucherReceiveRequest;
import com.gtech.gvcore.common.request.receive.GetReceiveByCustomerOrderRequest;
import com.gtech.gvcore.common.request.receive.GetVoucherReceiveRequest;
import com.gtech.gvcore.common.request.receive.QueryVoucherReceiveRequest;
import com.gtech.gvcore.common.request.receive.ReceiveVoucherRequest;
import com.gtech.gvcore.common.response.voucherreceive.VoucherReceiveResponse;
import com.gtech.gvcore.dao.model.VoucherReceive;

public interface VoucherReceiveService {

	int createVoucherReceive(CreateVoucherReceiveRequest request);

	VoucherReceiveResponse getVoucherReceive(GetVoucherReceiveRequest request);
	
	VoucherReceiveResponse getVoucherReceiveByCustomerOrder(GetReceiveByCustomerOrderRequest request);


	VoucherReceive getVoucherReceiveBySourceDataCode(String  dataCode);

	PageResult<VoucherReceiveResponse> queryVoucherReceivePage(QueryVoucherReceiveRequest request);

	void receive(ReceiveVoucherRequest receiveVoucherRequest);

	Integer customerOrderReceive(String customerOrderCode, String updateUser);
}
