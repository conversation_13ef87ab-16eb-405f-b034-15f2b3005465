package com.gtech.gvcore.service.report.export.file;

import com.alibaba.excel.support.ExcelTypeEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.dao.model.ReportRequest;
import com.gtech.gvcore.helper.GvExcelHelper;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.ReportQueryParam;
import com.gtech.gvcore.service.report.extend.ReportUploadHelper;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import com.gtech.gvcore.service.report.extend.context.ReportContextHelper;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.InputStream;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * excel 上下文
 */
public class DefaultFileContext implements FileContext {

    /**
     * export data class
     */
    private final Class<?> exportDataClass;

    /**
     * report export type enum
     */
    private final ReportExportTypeEnum reportExportTypeEnum;

    /**
     * file list
     */
    private final Map<String, String> fileMap = new LinkedHashMap<>();

    /**
     * file max size
     */
    private final int fileMaxSize = ReportUploadHelper.getExcelFileMaxSize();

    /**
     * need head
     */
    private final boolean needHead;

    /**
     * excel writer
     */
    private GvExcelHelper.AppendTableWriter excelAppendTableWriter;

    /**
     * head object
     */
    private Object headObject;

    /**
     * index
     */
    private int index = 0;

    /**
     *
     */
    private int page = 1;


    /**
     * constructor
     *
     * @param orderReport    orderReport
     * @param businessReport businessReport
     */
    public DefaultFileContext(ReportRequest orderReport,
                              BusinessReport<? extends ReportQueryParam, ?> businessReport) {

        this.reportExportTypeEnum = ReportExportTypeEnum.getExportTypeEnumByType(orderReport.getReportType());
        this.exportDataClass = businessReport.getExportDataClass();
        this.needHead = StringUtils.isBlank(Objects.requireNonNull(reportExportTypeEnum).getTemplateName());
    }


    @Override
    public void init() {

        ReportContext context = ReportContextHelper.findContext();
        this.headObject = context.getBusinessReport().getHeadObject(context);

        this.initExcelWriter();
    }

    private void initExcelWriter() {

        this.excelAppendTableWriter = GvExcelHelper.buildAppendTableWriter(this.reportExportTypeEnum.getTemplateName(), exportDataClass);
        this.excelAppendTableWriter.doFill(headObject, this.reportExportTypeEnum.getSheetName());
    }

    @Override
    public void doFill(List<?> list) {

        index += list.size();

        if (index == fileMaxSize) {

            excelAppendTableWriter.doWrite(list, this.reportExportTypeEnum.getSheetName(), needHead);

            this.flush();

        } else if (index > fileMaxSize) {

            int subIndex = list.size() - (index - fileMaxSize);

            List<?> odlDate = list.subList(0, subIndex);
            excelAppendTableWriter.doWrite(odlDate, this.reportExportTypeEnum.getSheetName(), needHead);

            this.flush();

            this.doFill(list.subList(subIndex, list.size()));

        } else {

            excelAppendTableWriter.doWrite(list, this.reportExportTypeEnum.getSheetName(), needHead);
        }

    }

    @Override
    public String finish() {

        if (index > 0) {
            String fileName = ReportUploadHelper.getTempFileName(ReportContextHelper.findContext(), this.page, ExcelTypeEnum.XLSX.getValue());
            fileMap.put(fileName, ReportUploadHelper.inputStreamToUrl(this.excelAppendTableWriter.getStream(), fileName));
        }

        if (MapUtils.isEmpty(fileMap)) return StringUtils.EMPTY;

        return ReportUploadHelper.fileUpload(ReportContextHelper.findContext(), fileMap);
    }

    public void flush() {

        this.index = 0;

        InputStream inputStream = this.excelAppendTableWriter.getStream();

        String fileName = ReportUploadHelper.getTempFileName(ReportContextHelper.findContext(), this.page++, ExcelTypeEnum.XLSX.getValue());
        fileMap.put(fileName, ReportUploadHelper.inputStreamToUrl(inputStream, fileName));

        this.initExcelWriter();
    }

}