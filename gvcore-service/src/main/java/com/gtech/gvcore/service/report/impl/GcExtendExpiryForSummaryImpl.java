package com.gtech.gvcore.service.report.impl;

import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.GcExtendExpiryForSummaryBean;
import com.gtech.gvcore.service.report.impl.bo.GcExtendExpiryBo;
import com.gtech.gvcore.service.report.impl.param.GcExtendExpiryQueryData;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:57
 * @Description: Gift Card Extend Expiry Report Summary Implementation
 */
@Service
public class GcExtendExpiryForSummaryImpl extends ReportSupport
        implements BusinessReport<GcExtendExpiryQueryData, GcExtendExpiryForSummaryBean>, SingleReport {

    @Override
    public GcExtendExpiryQueryData builderQueryParam(CreateReportRequest reportParam) {

        GcExtendExpiryQueryData queryData = new GcExtendExpiryQueryData();

        queryData.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        queryData.setMerchantCodeList(reportParam.getMerchantCodes());
        queryData.setOutletCodeList(reportParam.getOutletCodes());

        // 交易时间范围对应延长时间
        queryData.setTransactionDateStart(reportParam.getTransactionDateStart());
        queryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());

        queryData.setCpgCodeList(reportParam.getCpgCodes());

        return queryData;
    }

    @Override
    public List<GcExtendExpiryForSummaryBean> getExportData(GcExtendExpiryQueryData queryData) {
        List<GcExtendExpiryBo> boList = new ArrayList<>();
        PollPageHelper.pollSelect(gcReportBusinessMapper::selectGcExtendExpiry, queryData, boList::addAll);
        //find
        Collection<GcExtendExpirySummaryStatisticalBo> list =
                Optional.of(boList)
                        .map(e -> e.stream()
                                .collect(Collectors.toMap(
                                        GcExtendExpirySummaryStatisticalBo::groupKey,
                                        GcExtendExpirySummaryStatisticalBo::convert,
                                        GcExtendExpirySummaryStatisticalBo::merge))
                        ).map(Map::values)
                        .orElseGet(Collections::emptyList);

        //empty
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        final JoinDataMap<GcCpg> cpgMap = super.getMapByCode(list, GcExtendExpirySummaryStatisticalBo::getCpgCode, GcCpg.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(list, GcExtendExpirySummaryStatisticalBo::getOutletCode, Outlet.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(outletMap.values(), Outlet::getMerchantCode, Merchant.class);

        //convert result
        List<GcExtendExpiryForSummaryBean> collect = list.stream()
                .map(e -> {
                    Outlet outlet = outletMap.findValue(e.getOutletCode());
                    return new GcExtendExpiryForSummaryBean()
                            .setTotalAmount(super.toAmount(e.getTotalAmount()))
                            .setCardsCount(String.valueOf(e.getCardsCount()))
                            .setVpg(cpgMap.findValue(e.getCpgCode()).getCpgName())
                            .setMerchant(merchantMap.findValue(outlet.getMerchantCode()).getMerchantName())
                            .setMerchantOutlet(outlet.getOutletName())
                            .setTransactionDate(e.getTransactionDate())
                            .setSource(e.getSource());
                })
                .collect(Collectors.toList());
        GcExtendExpiryForSummaryBean bean = new GcExtendExpiryForSummaryBean();
        bean.setTotalAmount(super.toAmount(list.stream().map(GcExtendExpirySummaryStatisticalBo::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add)));
        bean.setCardsCount(list.stream().map(GcExtendExpirySummaryStatisticalBo::getCardsCount).reduce(0, Integer::sum).toString());
        collect.add(bean);
        return collect;
    }

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_EXTEND_EXPIRY_SUMMARY;
    }


    @Getter
    @Setter
    @Accessors(chain = true)
    public static class GcExtendExpirySummaryStatisticalBo {

        /**
         * Outlet code.
         */
        private String outletCode;

        /**
         * Cpg code.
         */
        private String cpgCode;

        /**
         * Cards count.
         */
        private int cardsCount = 1;

        /**
         * Total amount.
         */
        private BigDecimal totalAmount;

        /**
         * Transaction date.
         */
        private String transactionDate;

        /**
         * Source.
         */
        private String source;

        public static GcExtendExpirySummaryStatisticalBo convert(GcExtendExpiryBo extendExpiryBo) {

            return new GcExtendExpirySummaryStatisticalBo()
                    .setOutletCode(extendExpiryBo.getOutletCode())
                    .setCpgCode(extendExpiryBo.getCpgCode())
                    .setTotalAmount(extendExpiryBo.getAmount() != null ? extendExpiryBo.getAmount() : BigDecimal.ZERO)
                    .setTransactionDate(extendExpiryBo.getExtensionTime())
                    .setSource(extendExpiryBo.getSource());
        }

        public GcExtendExpirySummaryStatisticalBo merge(GcExtendExpirySummaryStatisticalBo bo) {

            this.cardsCount += bo.getCardsCount();
            this.totalAmount = this.totalAmount.add(bo.getTotalAmount());

            return this;
        }

        public static String groupKey(GcExtendExpiryBo extendExpiryBo) {

            return StringUtils.join(",",
                    extendExpiryBo.getCpgCode(),
                    extendExpiryBo.getExtensionTime(),
                    extendExpiryBo.getOutletCode(),
                    extendExpiryBo.getSource());
        }

    }
}
