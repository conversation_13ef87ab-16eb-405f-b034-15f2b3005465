package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.CustomerTypeEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.mapper.GcReportBusinessMapper;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.giftcard.util.PeriodCalculator;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.GcRegenerateActivationCodeDetailBean;
import com.gtech.gvcore.service.report.impl.bo.GcRegenerateActivationCodeDetailBo;
import com.gtech.gvcore.service.report.impl.param.GcRegenerateActivationCodeQueryData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName GcRegenerateActivationCodeImpl
 * @Description Gift Card Regenerate Activation Code Report Implementation
 * <AUTHOR> based on RegenerateActivationCodeImpl
 * @Date 2025/6/19
 * @Version V1.0
 **/
@Service
@Slf4j
public class GcRegenerateActivationCodeForDetailImpl
        extends ReportSupport
        implements BusinessReport<GcRegenerateActivationCodeQueryData, GcRegenerateActivationCodeDetailBean>, SingleReport {


    private final GcReportBusinessMapper gcReportBusinessMapper;

    public GcRegenerateActivationCodeForDetailImpl(GcReportBusinessMapper gcReportBusinessMapper) {
        super();
        this.gcReportBusinessMapper = gcReportBusinessMapper;
    }

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_REGENERATE_ACTIVATION_CODE_DETAIL_REPORT;
    }

    @Override
    public GcRegenerateActivationCodeQueryData builderQueryParam(CreateReportRequest reportParam) {
        GcRegenerateActivationCodeQueryData param = new GcRegenerateActivationCodeQueryData();

        // 设置时间范围
        param.setStartDate(reportParam.getTransactionDateStart());
        param.setEndDate(reportParam.getTransactionDateEnd());

        // 设置请求ID
        param.setIssueHandlingCode(reportParam.getVoucherRequestId());
        param.setFileName(reportParam.getUploadedFileName());
        param.setStatus(reportParam.getOrderStatuses());
        param.setCpgCodes(reportParam.getCpgCodes());

        List<String> cardNumbers = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(reportParam.getVoucherCodeList())) {
            cardNumbers.addAll(reportParam.getVoucherCodeList());
        }
        if (reportParam.getVoucherCode() != null) {
            cardNumbers.addAll(Arrays.stream(reportParam.getVoucherCode().split(",")).distinct().collect(Collectors.toList()));
        }
        param.setCardNumbers(cardNumbers);
        return param;
    }

    @Override
    public List<GcRegenerateActivationCodeDetailBean> getExportData(GcRegenerateActivationCodeQueryData param) {
        List<GcRegenerateActivationCodeDetailBo> detailBeanList = gcReportBusinessMapper.selectGcRegenerateActivationCodeDetail(param);
        final JoinDataMap<Customer> customerMap = super.getMapByCode(detailBeanList, GcRegenerateActivationCodeDetailBo::getCustomerName, Customer.class);
        final JoinDataMap<GcCpg> cpgMap = super.getMapByCode(detailBeanList, GcRegenerateActivationCodeDetailBo::getCpgCode, GcCpg.class);
        return detailBeanList.stream().map(x -> {
            GcRegenerateActivationCodeDetailBean bean = BeanCopyUtils.jsonCopyBean(x, GcRegenerateActivationCodeDetailBean.class);
            bean.setCpgCode(cpgMap.findValue(x.getCpgCode()).getCpgName());
            bean.setTotalAmount(toAmount(x.getTotalAmount()));
            Customer value = customerMap.findValue(x.getCustomerName());
            String customerName;
            if (Objects.equals(value.getCustomerType(), CustomerTypeEnum.CORPORATE.code())) {
                customerName = value.getCompanyName();
            } else {
                customerName = value.getCustomerName();
            }
            bean.setCustomerName(customerName);
            Date date = DateUtil.parseDate(x.getExpiryDate(), DateUtil.FORMAT_YYYYMMDDHHMISS);
            bean.setExpiryDate(DateUtil.format(date, DateUtil.FORMAT_YYYYMMDDHHMISS));

            String activationPeriodEnded;
            String gracePeriodEnded;
            if (x.getActivationExtensionCount() == 0) {
                activationPeriodEnded = x.getActivationDeadline();
                Date start = DateUtil.parseDate(x.getActivationDeadline(), DateUtil.FORMAT_YYYYMMDDHHMISS);
                gracePeriodEnded = DateUtil.format(PeriodCalculator.calculateDate(x.getActivationGracePeriod(), start), DateUtil.FORMAT_YYYYMMDDHHMISS);
            } else {
                activationPeriodEnded = DateUtil.format(PeriodCalculator.calculateSubDate(x.getActivationGracePeriod(), DateUtil.parseDate(x.getActivationDeadline(), DateUtil.FORMAT_YYYYMMDDHHMISS)), DateUtil.FORMAT_YYYYMMDDHHMISS);
                gracePeriodEnded = x.getActivationDeadline();
            }
            bean.setActivationEnded(activationPeriodEnded);
            bean.setGracePeriodEnded(gracePeriodEnded);
            return bean;
        }).collect(Collectors.toList());
    }
}
