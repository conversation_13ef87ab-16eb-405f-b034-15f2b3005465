package com.gtech.gvcore.service;

import java.util.List;

import com.gtech.gvcore.dao.model.MeansOfPaymentOutlet;

/**
 * 
 * <AUTHOR>
 * @date 2022年4月22日
 */
public interface MeansOfPaymentOutletService {

    /**
     * 
     * @param meansOfPaymentCode
     * @param outletCodeList
     * @param createUser
     * @return
     * <AUTHOR>
     * @date 2022年4月22日
     */
    int insesrt(String meansOfPaymentCode, List<String> outletCodeList, String createUser);

    /**
     * 
     * @param meansOfPaymentCode
     * @return
     * <AUTHOR>
     * @date 2022年4月22日
     */
    List<MeansOfPaymentOutlet> queryByMeansOfPaymentCode(String meansOfPaymentCode);

    /**
     * 
     * @param meansOfPaymentOutlet
     * @return
     * <AUTHOR>
     * @date 2022年4月22日
     */
    int updateByPrimaryKeySelective(MeansOfPaymentOutlet meansOfPaymentOutlet);

    /**
     * 
     * @param meansOfPaymentCodeList
     * @param deleteStatus
     * @return
     * <AUTHOR>
     * @date 2022年4月22日
     */
    List<MeansOfPaymentOutlet> queryByMeansOfPaymentCodeList(List<String> meansOfPaymentCodeList, Integer deleteStatus);

}


