package com.gtech.gvcore.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.customerpaymentmethod.*;
import com.gtech.gvcore.common.response.customerpaymentmethod.CustomerPaymentMethodResponse;
import com.gtech.gvcore.dao.mapper.CustomerPaymentMethodMapper;
import com.gtech.gvcore.dao.model.CustomerPaymentMethod;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.CustomerPaymentMethodService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/11 15:27
 */
@Service
public class CustomerPaymentMethodServiceImpl implements CustomerPaymentMethodService {

    @Autowired   
    private CustomerPaymentMethodMapper customerPaymentMethodMapper;

    @Autowired
    private GvCodeHelper codeHelper;

    @Override
    public Result<Void> createCustomerPaymentMethod(CreateCustomerPaymentMethodRequest param) {
        CustomerPaymentMethod entity = BeanCopyUtils.jsonCopyBean(param, CustomerPaymentMethod.class);
        entity.setCustomerPaymentMethodCode(codeHelper.generateCustomerPaymentMethodCode());
        entity.setCreateTime(new Date());
        //默认开启
        entity.setStatus(GvcoreConstants.STATUS_ENABLE);
        try {
            customerPaymentMethodMapper.insertSelective(entity);
        } catch (DuplicateKeyException e) {
            return Result.failed(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                    ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
        }
        return Result.ok();
    }

    @Override
    public Result<Void> updateCustomerPaymentMethod(UpdateCustomerPaymentMethodRequest param) {
        CustomerPaymentMethod entity = BeanCopyUtils.jsonCopyBean(param, CustomerPaymentMethod.class);
        entity.setUpdateTime(new Date());

        Example example = new Example(CustomerPaymentMethod.class);
        example.createCriteria()
                .andEqualTo(CustomerPaymentMethod.C_CUSTOMER_PAYMENT_METHOD_CODE,param.getCustomerPaymentMethodCode());

        try {
            customerPaymentMethodMapper.updateByConditionSelective(entity,example);
        } catch (DuplicateKeyException e) {
            return Result.failed(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                    ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
        }
        return Result.ok();
    }

    @Override
    public Result<Void> deleteCustomerPaymentMethod(DeleteCustomerPaymentMethodRequest param) {
        Example example = new Example(CustomerPaymentMethod.class);
        example.createCriteria()
                .andEqualTo(CustomerPaymentMethod.C_CUSTOMER_PAYMENT_METHOD_CODE,param.getCustomerPaymentMethodCode());
        customerPaymentMethodMapper.deleteByCondition(example);

        return Result.ok();
    }

    @Override
    public Result<Void> deleteCustomerPaymentMethodByCustomer(String param) {
        Example example = new Example(CustomerPaymentMethod.class);
        example.createCriteria()
                .andEqualTo(CustomerPaymentMethod.C_CUSTOMER_CODE,param);
        customerPaymentMethodMapper.deleteByCondition(example);

        return Result.ok();
    }

    @Override
    public PageResult<CustomerPaymentMethodResponse> queryCustomerPaymentMethodList(QueryCustomerPaymentMethodRequest param) {

        PageHelper.startPage(param.getPageNum(),param.getPageSize());

        Example example = new Example(CustomerPaymentMethod.class);
        example.createCriteria()
                .andEqualTo(CustomerPaymentMethod.C_CUSTOMER_PAYMENT_METHOD_CODE,param.getCustomerPaymentMethodCode())
                .andEqualTo(CustomerPaymentMethod.C_CUSTOMER_CODE,param.getCustomerCode())
                .andEqualTo(CustomerPaymentMethod.C_MOP_GROUP,param.getMopGroup())
                .andEqualTo(CustomerPaymentMethod.C_STATUS,param.getStatus());
        //创建时间倒序
        example.orderBy(CustomerPaymentMethod.C_CREATE_TIME).desc();

        List<CustomerPaymentMethod> gvCustomerPaymentMethodEntities = customerPaymentMethodMapper.selectByCondition(example);
        PageInfo<CustomerPaymentMethod> info = PageInfo.of(gvCustomerPaymentMethodEntities);

        return new PageResult<>(BeanCopyUtils.jsonCopyList(info.getList(),CustomerPaymentMethodResponse.class),info.getTotal());
    }

    @Override
    public CustomerPaymentMethodResponse getCustomerPaymentMethod(GetCustomerPaymentMethodRequest param) {
        CustomerPaymentMethod entity = BeanCopyUtils.jsonCopyBean(param, CustomerPaymentMethod.class);
        CustomerPaymentMethod customerPaymentMethod = customerPaymentMethodMapper.selectOne(entity);

        return BeanCopyUtils.jsonCopyBean(customerPaymentMethod,CustomerPaymentMethodResponse.class);
    }
}
