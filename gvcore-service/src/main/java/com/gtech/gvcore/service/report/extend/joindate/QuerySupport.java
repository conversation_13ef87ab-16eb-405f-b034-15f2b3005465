package com.gtech.gvcore.service.report.extend.joindate;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

/**
 * @ClassName QueryFunction
 * @Description
 * <AUTHOR>
 * @Date 2023/1/16 15:41
 * @Version V1.0
 **/
public interface QuerySupport<T> {

    /**
     * 查询list 方法
     * @param codes
     * @return
     */
    List<T> queryByCode(List<String> codes, String... selectFields);

    /**
     * 实体主键
     * @return
     */
    Function<T, String> codeMapper();

    /**
     * 默认空对象
     * @return
     */
    T emptyObject();

    /**
     * 支持类型
     * @return
     */
    Class<T> supportType();

    /**
     * 根据编码获得单个对象
     * @param code
     * @return
     */
    default T getByCode (String code) {

        if (StringUtils.isBlank(code)) return null;

        List<T> list = queryByCode(Collections.singletonList(code));
        if (CollectionUtils.isEmpty(list)) return null;

        return list.get(0);
    }

}
