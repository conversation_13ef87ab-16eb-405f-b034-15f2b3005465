package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName VoucherReturnAndTransferBean
 * @Description voucher return and transfer bean
 * <AUTHOR>
 * @Date 2023/2/14 19:17
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class VoucherReturnAndTransferBean {

    @ExcelProperty(value = "Request #")
    private String requestId;

    @ExcelProperty(value = "From Store Name")
    private String fromStoreName;

    @ExcelProperty(value = "To Store Name")
    private String toStoreName;

    @ExcelProperty(value = "Voucher Program Group")
    private String cpgName;

    @ExcelProperty(value = "Number of Vouchers", converter = ExportExcelNumberConverter.class)
    private String numberOfVoucher;

    @ReportAmountValue
    @ExcelProperty(value = "Voucher Amount", converter = ExportExcelNumberConverter.class)
    private String voucherAmount;

    @ExcelProperty(value = "Created On")
    private String createTime;

    @ExcelProperty(value = "Created by")
    private String createdBy;

    @ExcelProperty(value = "Booklet Number (Number of Booklets)")
    private String bookletNumber;

    @ExcelProperty(value = "Voucher Number (Number of Vouchers)")
    private String voucherNumber;

    @ExcelProperty(value = "Status")
    private String status;

}