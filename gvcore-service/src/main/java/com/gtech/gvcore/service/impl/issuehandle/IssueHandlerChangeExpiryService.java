package com.gtech.gvcore.service.impl.issuehandle;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.gtech.gvcore.common.enums.IssueHandlingProcessStatusEnum;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.IssueHandlerBaseService;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

@Service
@Slf4j
public class IssueHandlerChangeExpiryService extends IssueHandlerValidateService implements IssueHandlerBaseService {

	@Override
	public IssueHandlingTypeEnum getIssueHandlingType() {
		return IssueHandlingTypeEnum.CHANGE_EXPIRY;
	}

	@Override
	public List<IssueHandlingDetails> validate(List<IssueHandlingDetails> details, String issuerCode) {
		checkIfExist(details, getIssueHandlingType(), issuerCode);
		checkInvoiceNumber(details,null);
		return details;
	}

	@Override
	public List<IssueHandlingDetails> execute(List<IssueHandlingDetails> details, String issuerCode) {
		validate(details, issuerCode);
		if (CollectionUtils.isEmpty(details)) {
			return Collections.emptyList();
		}
		CountDownLatch downlatch = new CountDownLatch(details.size());
		List<IssueHandlingDetails> returnList = new CopyOnWriteArrayList<>();
		for (IssueHandlingDetails issueHandlingDetails : details) {
			EXECUTOR.execute(() -> {
				try {
					returnList.add(issueHandlingDetails);
					makeIssueHandling(issueHandlingDetails);
				} catch (Exception e) {
					String msg = e.getMessage();
					log.error(e.getMessage(), e);
					issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
					issueHandlingDetails.setResult(msg.length() > 500 ? msg.substring(0, 499) : msg);
				} finally {
					downlatch.countDown();
				}
			});
		}
		try {
			downlatch.await();
		} catch (InterruptedException e) {
			log.error(e.getMessage(), e);
			Thread.currentThread().interrupt();
		}
		return returnList;
	}

	public void makeIssueHandling(IssueHandlingDetails issueHandlingDetails) {
		if (IssueHandlingProcessStatusEnum.FAILED.code().equals(issueHandlingDetails.getProcessStatus())) {
			return;
		}
		String voucherCode = issueHandlingDetails.getVoucherCode();
		Date voucherEffectiveDate = issueHandlingDetails.getVoucherEffectiveDate();
		if (voucherEffectiveDate == null) {
			issueHandlingDetails.setResult("Voucher effective date null");
			issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
			return;
		}
		Example example = new Example(Voucher.class);
		example.createCriteria().andEqualTo(Voucher.C_VOUCHER_CODE, voucherCode);
		Voucher voucher = new Voucher();
		voucher.setVoucherEffectiveDate(voucherEffectiveDate);
		int count = voucherMapper.updateByConditionSelective(voucher, example);
		if (count == 0) {
			issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
			issueHandlingDetails.setResult(ResultErrorCodeEnum.DATA_MISS.code() + "【" + voucherCode + "】");
		} else {
			issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.SUCCESS.code());
		}
	}

}
