package com.gtech.gvcore.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ApproveNodeRecordTypeEnum;
import com.gtech.gvcore.common.enums.ApproveTypeEnum;
import com.gtech.gvcore.common.enums.FlowEnum;
import com.gtech.gvcore.common.enums.FlowNodeEnum;
import com.gtech.gvcore.common.enums.MessageEnventEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.enums.VoucherRequestStatusEnum;
import com.gtech.gvcore.common.request.cpg.GetCpgRequest;
import com.gtech.gvcore.common.request.cpg.QueryCpgVoucherInventory;
import com.gtech.gvcore.common.request.flow.SendEmailRequest;
import com.gtech.gvcore.common.request.flow.SendNoticeRequest;
import com.gtech.gvcore.common.request.outlet.QueryOutletRequest;
import com.gtech.gvcore.common.request.releaseapprove.CreateLogRecode;
import com.gtech.gvcore.common.request.releaseapprove.ReleaseApproveAbleRequest;
import com.gtech.gvcore.common.request.voucherrequest.ApproveVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.BulkApproveVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.CancelVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.CreateVoucherRequestDetailsRequest;
import com.gtech.gvcore.common.request.voucherrequest.CreateVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.GetVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.QueryHeadOfficeAndWareHouseRequest;
import com.gtech.gvcore.common.request.voucherrequest.QueryVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.UpdateVoucherRequestDetailsRequest;
import com.gtech.gvcore.common.request.voucherrequest.UpdateVoucherRequestRequest;
import com.gtech.gvcore.common.response.cpg.GetCpgResponse;
import com.gtech.gvcore.common.response.cpg.QueryCpgVoucherInventoryResponse;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.common.response.useraccount.PermissionCodeResponse;
import com.gtech.gvcore.common.response.voucherrequest.GetVoucherRequestResponse;
import com.gtech.gvcore.common.response.voucherrequest.QueryHeadOfficeAndWareHouseResponse;
import com.gtech.gvcore.common.response.voucherrequest.QueryVoucherRequestResponse;
import com.gtech.gvcore.dao.mapper.ApproveNodeRecordMapper;
import com.gtech.gvcore.dao.mapper.CpgMapper;
import com.gtech.gvcore.dao.mapper.OutletCpgMapper;
import com.gtech.gvcore.dao.mapper.OutletMapper;
import com.gtech.gvcore.dao.mapper.VoucherRequestDetailsMapper;
import com.gtech.gvcore.dao.mapper.VoucherRequestMapper;
import com.gtech.gvcore.dao.model.ApproveNodeRecord;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.OutletCpg;
import com.gtech.gvcore.dao.model.VoucherBatch;
import com.gtech.gvcore.dao.model.VoucherRequest;
import com.gtech.gvcore.dao.model.VoucherRequestDetails;
import com.gtech.gvcore.dto.QueryVoucherRequestPermissionDto;
import com.gtech.gvcore.dto.QueryVoucherRequestPermissionRequest;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.helper.PermissionHelper;
import com.gtech.gvcore.service.CpgService;
import com.gtech.gvcore.service.FlowNoticeService;
import com.gtech.gvcore.service.GvUserAccountService;
import com.gtech.gvcore.service.ReleaseApproveService;
import com.gtech.gvcore.service.VoucherAllocationService;
import com.gtech.gvcore.service.VoucherRequestService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.threads.ThreadPoolExecutor;
import org.junit.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/8 9:55
 */
@Slf4j
@Service
public class VoucherRequestServiceImpl implements VoucherRequestService {
    @Value("#{${gv.issuer.warehouse}}")
    private Map<String, String> issuerWarehouseMap;

    @Value("#{${gv.issuer.businesswarehouse}}")
    private Map<String, String> issuerBusinessWarehouseMap;

    @Value("${gv.outlet.warehouse.MV01:}")
    private String mv01;
    @Value("${gv.outlet.warehouse.MV03:}")
    private String mv03;
    @Value("${gv.outlet.warehouse.MV04:}")
    private String mv04;

    @Autowired
    private VoucherRequestMapper voucherRequestMapper;
    @Autowired
    private VoucherRequestDetailsMapper voucherRequestDetailsMapper;
    @Autowired
    private GvCodeHelper gvCodeHelper;
    @Autowired
    private VoucherAllocationService voucherAllocationService;
    @Autowired
    private ApproveNodeRecordMapper approveNodeRecordMapper;
    @Autowired
    private ReleaseApproveService releaseApproveService;
    @Autowired
    private CpgMapper cpgMapper;
    @Autowired
    private OutletCpgMapper outletCpgMapper;
    @Autowired
    private OutletMapper outletMapper;
    @Autowired
    private FlowNoticeService flowNoticeService;
    @Autowired
    private GvUserAccountService userAccountService;
	@Autowired
	private MessageComponent messageComponent;

	@Autowired
    private CpgService cpgService;

	@Value("#{${gv.issuer.warehouse:}}")
	private Map<String, String> issuerWarehouse;

    @Autowired
    private PermissionHelper permissionHelper;

    private static final ThreadPoolExecutor EXECUTOR =
            new ThreadPoolExecutor(20, 50, 100, TimeUnit.SECONDS, new LinkedBlockingDeque<>(1000),
                    new ThreadPoolExecutor.CallerRunsPolicy());

    @Override
    @Transactional
    public Result<String> addVoucherRequest(CreateVoucherRequestRequest createVoucherRequestRequest, String requestOrReturn) {

        /*for (CreateVoucherRequestDetailsRequest detailsRequest : createVoucherRequestRequest.getDetailsRequests()) {
            GetCpgRequest cpgRequest = new GetCpgRequest();
            cpgRequest.setCpgCode(detailsRequest.getVpgCode());
            Result<GetCpgResponse> cpg = cpgService.getCpg(cpgRequest);
            if (cpg.getData().getDisableGeneration().equals(VpgDisableGenerationEnum.DISABLED.code())){
                return Result.failed("This vpg cannot be used to generate coupons and sales");
            }
        }*/


        String receiverCode = createVoucherRequestRequest.getReceiverCode();


        //检查HO01向WH01发起请求库存限制
        String HO01 = issuerBusinessWarehouseMap.get(createVoucherRequestRequest.getIssuerCode());
        if (StringUtil.isNotEmpty(HO01) && receiverCode.equals(HO01) && requestOrReturn.equals("request")) {
            //如果是HO01 向WH01请求 数据权限限制为WH01
            createVoucherRequestRequest.setPermissionCode(issuerWarehouseMap.get(createVoucherRequestRequest.getIssuerCode()));


            List<Future<Result<String>>> futures = new ArrayList<>();

            //判断库存
            List<CreateVoucherRequestDetailsRequest> detailsRequests = createVoucherRequestRequest.getDetailsRequests();
            for (CreateVoucherRequestDetailsRequest detailsRequest : detailsRequests) {

                Future<Result<String>> future = EXECUTOR.submit(() -> {
                    try {
                        //每一项都要检查库存
                        Result<QueryCpgVoucherInventoryResponse> inventory = cpgService.queryCpgVoucherInventory(QueryCpgVoucherInventory
                                .builder()
                                .cpgCode(detailsRequest.getCpgCode())
                                .issuerCode(createVoucherRequestRequest.getIssuerCode())
                                .build());
                        //如果库存不足
                        if (inventory.getData().getCount() < detailsRequest.getVoucherNum()) {
                            return Result.failed(ResultErrorCodeEnum.VOUCHER_INVENTORY_SHORTAGE.code(), ResultErrorCodeEnum.VOUCHER_INVENTORY_SHORTAGE.desc());
                        } else {
                            return Result.ok();
                        }
                    } catch (GTechBaseException e) {
                        throw e;
                    } catch (Exception e) {
                        throw new GTechBaseException(e);
                    }
                });

                futures.add(future);

            }
            List<Result<String>> results = new ArrayList<>();
            for (Future<Result<String>> future : futures) {
                try {
                    Result result = future.get();
                    if (!result.isSuccess()) {
                        results.add(result);
                    }
                } catch (ExecutionException e) {
                    Throwable cause = e.getCause();
                    if (cause instanceof GTechBaseException) {
                        throw (GTechBaseException) cause;
                    } else {
                        throw new GTechBaseException(cause);
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new GTechBaseException(e);
                }
            }

            if (results.size() > 0){
                return results.get(0);
            }


        }else {
            createVoucherRequestRequest.setPermissionCode(HO01);


        }

        //检查详情的条数和金额总和是否和请求传入的相同
        //Check that the number and sum of details are the same as those passed in by the request
        if (!checkAmount(createVoucherRequestRequest)) {
            return Result.failed(ResultErrorCodeEnum.VOUCHER_AMOUNT_DIFFERENT.code(), ResultErrorCodeEnum.VOUCHER_AMOUNT_DIFFERENT.desc());
        }
        //检查是否有相同面额的数据
        //Check to see if there are values of the same denomination
        /*if (!checkDetailRepeatForAdd(createVoucherRequestRequest.getDetailsRequests())) {
            return Result.failed(ResultErrorCodeEnum.VOUCHER_DETAIL_DENOMINATION_REPEAT.code(), ResultErrorCodeEnum.VOUCHER_DETAIL_DENOMINATION_REPEAT.desc());
        }*/

        if (createVoucherRequestRequest.getVoucherOwnerCode().equals(createVoucherRequestRequest.getReceiverCode())) {
            return Result.failed(ResultErrorCodeEnum.VOUCHER_OWNER_EQUALS_RECEIVER.code(),
                    ResultErrorCodeEnum.VOUCHER_OWNER_EQUALS_RECEIVER.desc());
        }

        String voucherRequestCode = gvCodeHelper.generateVoucherRequestCode();
        if (createVoucherRequestRequest.getBusinessType() == null) {
            createVoucherRequestRequest.setBusinessType(GvcoreConstants.SALES);
        }

        Result stringResult = checkOutlet(createVoucherRequestRequest);
        if (!stringResult.getCode().equals("0")) {
            return stringResult;
        }

        VoucherRequest voucherRequest = BeanCopyUtils.jsonCopyBean(createVoucherRequestRequest, VoucherRequest.class);
        voucherRequest.setVoucherRequestCode(voucherRequestCode);
        voucherRequest.setIssuerCode(createVoucherRequestRequest.getIssuerCode());
        voucherRequest.setCurrencyCode("IDR");
        voucherRequest.setCreateTime(new Date());
        voucherRequest.setStatus(VoucherRequestStatusEnum.PENDING_APPROVAL.getCode());

        List<VoucherRequestDetails> gvVoucherRequestDetailsEntities = BeanCopyUtils.jsonCopyList(createVoucherRequestRequest.getDetailsRequests(), VoucherRequestDetails.class);
        List<VoucherRequestDetails> voucherRequestDetails1 = new ArrayList<>(gvVoucherRequestDetailsEntities.size());
        for (VoucherRequestDetails voucherRequestDetails : gvVoucherRequestDetailsEntities) {
            String voucherRequestDetailsCode = gvCodeHelper.generateVoucherRequestDetailsCode();

            voucherRequestDetails.setVoucherRequestDetailsCode(voucherRequestDetailsCode);
            voucherRequestDetails.setVoucherRequestCode(voucherRequestCode);
            voucherRequestDetails.setCreateUser(createVoucherRequestRequest.getCreateUser());
            voucherRequestDetails.setPermissionCode(createVoucherRequestRequest.getPermissionCode());

            voucherRequestDetails1.add(voucherRequestDetails);
        }

        EXECUTOR.execute(()->{
            createVoucherRequestNote(voucherRequest.getVoucherRequestCode(), voucherRequest.getRequestRemarks(), voucherRequest.getCreateUser(), ApproveNodeRecordTypeEnum.SUBMIT.getType());
            voucherRequestMapper.insertSelective(voucherRequest);

            voucherRequestDetailsMapper.batchInsert(voucherRequestDetails1);

            if (GvcoreConstants.SALES.equals(voucherRequest.getBusinessType())) {
                sendCreateEmail(voucherRequest);
            }
        });

        return Result.ok(voucherRequestCode);
    }

	private void sendCreateEmail(VoucherRequest voucherRequest) {
        SendNoticeRequest sendNoticeRequest = new SendNoticeRequest();
		sendNoticeRequest.setFlowCode(FlowEnum.SALES_VOUCHER_FLOW.getCode());
        sendNoticeRequest.setFlowNodeCode(FlowNodeEnum.CREATED.getCode());
        sendNoticeRequest.setBusinessCode(voucherRequest.getVoucherRequestCode());
		Map<String, Object> params = getExtendparams(voucherRequest);
		sendNoticeRequest.setExtendParams(params);
        flowNoticeService.send(sendNoticeRequest);
    }

	@SuppressWarnings("unchecked")
	public Map<String, Object> getExtendparams(VoucherRequest voucherRequest) {
		Map<String, Object> params = new HashMap<>();
		params.putAll(JSONObject.parseObject(JSON.toJSONString(voucherRequest), Map.class));
		params.remove("email");
		params.put(VoucherBatch.C_CREATE_TIME, DateUtil.format(voucherRequest.getCreateTime(), GvcoreConstants.EMAIL_DATE_FORMAT));
		params.put(VoucherBatch.C_UPDATE_TIME, DateUtil.format(voucherRequest.getUpdateTime(), GvcoreConstants.EMAIL_DATE_FORMAT));
		GetVoucherRequestResponse getVoucherRequestResponse = getVoucherRequestByCode(voucherRequest.getVoucherRequestCode());
		if (getVoucherRequestResponse == null) {
			return params;
		}
		List<CreateVoucherRequestDetailsRequest> createVoucherRequestDetailsRequests = getVoucherRequestResponse.getCreateVoucherRequestDetailsRequests();
		String cpgNames = "";
		if (!CollectionUtils.isEmpty(createVoucherRequestDetailsRequests)) {
			for (CreateVoucherRequestDetailsRequest createVoucherRequestDetailsRequest : createVoucherRequestDetailsRequests) {
				String vpgName = createVoucherRequestDetailsRequest.getVpgName();
				if (StringUtil.isNotEmpty(vpgName)) {
					cpgNames = StringUtil.isEmpty(cpgNames) ? vpgName : cpgNames + "," + vpgName;
				}
			}
		}
		params.put("cpgName", cpgNames);
		return params;
	}

    private void createVoucherRequestNote(String voucherRequestCode, String requestRemarks, String createUser, String requestType) {
        releaseApproveService.createLogRecord(
                CreateLogRecode.builder()
                        .approveUser(createUser)
                        .approveType(requestType)
                        .note(requestRemarks)
                        .status(true)
                        .approveRoleCode(StringUtil.EMPTY)
                        .businessCode(voucherRequestCode).build());
    }

    private Result checkOutlet(CreateVoucherRequestRequest createVoucherRequestRequest) {
        String outletCodeWh = issuerWarehouseMap.get(createVoucherRequestRequest.getIssuerCode());
        if (StringUtil.isEmpty(outletCodeWh)) {
            return Result.failed(ResultErrorCodeEnum.ISSUER_NOT_CONFIG_WH.code(), ResultErrorCodeEnum.ISSUER_NOT_CONFIG_WH.desc());
        }
        Outlet wh = outletMapper.selectOne(Outlet.builder().outletCode(outletCodeWh).build());
        if (wh == null) {
            return Result.failed(ResultErrorCodeEnum.ISSUER_NOT_CONFIG_WH.code(), ResultErrorCodeEnum.ISSUER_NOT_CONFIG_WH.desc());
        }
        String outletCodeHo = issuerBusinessWarehouseMap.get(createVoucherRequestRequest.getIssuerCode());
        if (StringUtil.isEmpty(outletCodeHo)) {
            return Result.failed(ResultErrorCodeEnum.ISSUER_NOT_CONFIG_HO.code(), ResultErrorCodeEnum.ISSUER_NOT_CONFIG_HO.desc());
        }
        Outlet ho = outletMapper.selectOne(Outlet.builder().outletCode(outletCodeHo).build());
        if (ho == null) {
            return Result.failed(ResultErrorCodeEnum.ISSUER_NOT_CONFIG_HO.code(), ResultErrorCodeEnum.ISSUER_NOT_CONFIG_HO.desc());
        }

        if (createVoucherRequestRequest.getBusinessType().equals(GvcoreConstants.SALES)) {
            //MER-1884
            /*if (Boolean.FALSE.equals(outletMapper.ifExist(createVoucherRequestRequest.getIssuerCode(), createVoucherRequestRequest.getReceiverCode()))) {
                return Result.failed(ResultErrorCodeEnum.ISSUER_NOT_CONFIG_OUTLET.code(), ResultErrorCodeEnum.ISSUER_NOT_CONFIG_OUTLET.desc());
            }*/
            //如果是request 获取传入的接收者的outlet
            //If it is the request. Gets an outlet for the incoming receiver
            if (createVoucherRequestRequest.getReceiverCode().equals(wh.getOutletCode())) {
                //WH01不能是接收者 /WH01 cannot be the receiver
                return Result.failed(ResultErrorCodeEnum.REQUEST_OBJECT_ERROR_WH.code(), ResultErrorCodeEnum.REQUEST_OBJECT_ERROR_WH.desc());
            } else if (createVoucherRequestRequest.getReceiverCode().equals(ho.getOutletCode())) {
                //接收者是HO01时，券的持有者是WH01。/When the receiver is HO01, the holder of the coupon is WH01
                createVoucherRequestRequest.setVoucherOwnerCode(wh.getOutletCode());
                createVoucherRequestRequest.setVoucherOwnerName(wh.getOutletName());
            } else {
                //接收者是其他人，持有者就是HO/The recipient is someone else, and the holder is HO
                createVoucherRequestRequest.setVoucherOwnerCode(ho.getOutletCode());
                createVoucherRequestRequest.setVoucherOwnerName(ho.getOutletName());
            }
        } else if (createVoucherRequestRequest.getBusinessType().equals(GvcoreConstants.RETURN)) {
            /*if (Boolean.TRUE.equals(!outletMapper.ifExist(createVoucherRequestRequest.getIssuerCode(), createVoucherRequestRequest.getReceiverCode()))
                    || Boolean.TRUE.equals(!outletMapper.ifExist(createVoucherRequestRequest.getIssuerCode(), createVoucherRequestRequest.getVoucherOwnerCode()))) {
                return Result.failed(ResultErrorCodeEnum.ISSUER_NOT_CONFIG_OUTLET.code(), ResultErrorCodeEnum.ISSUER_NOT_CONFIG_OUTLET.desc());
            }*/
            if (!createVoucherRequestRequest.getReceiverCode().equals(ho.getOutletCode())) {
                //回退的券接收者必须是HO01/The recipient of the refunded voucher must be HO01
                return Result.failed(ResultErrorCodeEnum.REQUEST_OBJECT_ERROR_HO01.code(), ResultErrorCodeEnum.REQUEST_OBJECT_ERROR_HO01.desc());
            }
			String ownerCode = createVoucherRequestRequest.getVoucherOwnerCode();
			if (outletCodeWh.equals(ownerCode)) {
                //回退的券持有者不能是warehouse
                return Result.failed(ResultErrorCodeEnum.REQUEST_OBJECT_ERROR_RETURN.code(), ResultErrorCodeEnum.REQUEST_OBJECT_ERROR_RETURN.desc());
            }
        } else {
            /*if (Boolean.TRUE.equals(!outletMapper.ifExist(createVoucherRequestRequest.getIssuerCode(), createVoucherRequestRequest.getReceiverCode()))
                    || Boolean.TRUE.equals(!outletMapper.ifExist(createVoucherRequestRequest.getIssuerCode(), createVoucherRequestRequest.getVoucherOwnerCode()))) {
                return Result.failed(ResultErrorCodeEnum.ISSUER_NOT_CONFIG_OUTLET.code(), ResultErrorCodeEnum.ISSUER_NOT_CONFIG_OUTLET.desc());
            }*/
			if (outletCodeWh.equals(createVoucherRequestRequest.getReceiverCode())) {
                //转移的券接收者不能是warehouse
                return Result.failed(ResultErrorCodeEnum.REQUEST_OBJECT_ERROR_RETURN.code(), ResultErrorCodeEnum.REQUEST_OBJECT_ERROR_RETURN.desc());
            }
        }
        return Result.ok();
    }

    private boolean checkDetailRepeatForAdd(List<CreateVoucherRequestDetailsRequest> detailsRequests) {
        Set<BigDecimal> collect = detailsRequests.stream()
                .map(CreateVoucherRequestDetailsRequest::getDenomination)
                .collect(Collectors.toSet());
        return collect.size() == detailsRequests.size();
    }

    private boolean checkAmount(CreateVoucherRequestRequest createVoucherRequestRequest) {
        BigDecimal amount = BigDecimal.ZERO;
        int num = 0;
        for (CreateVoucherRequestDetailsRequest detailsRequest : createVoucherRequestRequest.getDetailsRequests()) {
            num += detailsRequest.getVoucherNum();
            amount = amount.add(detailsRequest.getVoucherAmount());
        }
        return num == createVoucherRequestRequest.getVoucherNum() && amount.compareTo(createVoucherRequestRequest.getVoucherAmount()) == 0;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Void> updateRequestStatus(String requestCode, Integer status, String updateUser) {
        try {
            voucherRequestMapper.updateStatus(requestCode, status, updateUser);
        } catch (Exception e) {
            return Result.failed(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(), ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.desc());
        }

        return Result.ok();
    }

    @Override
    @Transactional
    public Result<Void> updateVoucherRequest(UpdateVoucherRequestRequest updateVoucherRequestRequest) {
        //检查详情的条数和金额总和是否和请求传入的相同
        //Check that the number and sum of details are the same as those passed in by the request
		if (!checkAmount(updateVoucherRequestRequest)) {
			return Result.failed(ResultErrorCodeEnum.VOUCHER_AMOUNT_DIFFERENT.code(), ResultErrorCodeEnum.VOUCHER_AMOUNT_DIFFERENT.desc());
		}

        //检查是否有相同面额的数据
        //Check to see if there are values of the same denomination
		/*if (!checkDetailRepeatForUpdate(updateVoucherRequestRequest.getUpdateVoucherRequestDetailsRequests())) {
			return Result.failed(ResultErrorCodeEnum.VOUCHER_DETAIL_DENOMINATION_REPEAT.code(), ResultErrorCodeEnum.VOUCHER_DETAIL_DENOMINATION_REPEAT.desc());
		}*/

        List<UpdateVoucherRequestDetailsRequest> updateVoucherRequestDetailsRequests = updateVoucherRequestRequest.getUpdateVoucherRequestDetailsRequests();
		if (CollectionUtils.isEmpty(updateVoucherRequestDetailsRequests)) {
			return Result.failed(ResultErrorCodeEnum.VOUCHER_DETAIL_BLANK.code(), ResultErrorCodeEnum.VOUCHER_DETAIL_BLANK.desc());
		}

        CreateVoucherRequestRequest createVoucherRequestRequest = BeanCopyUtils.jsonCopyBean(updateVoucherRequestRequest, CreateVoucherRequestRequest.class);
        Result stringResult = checkOutlet(createVoucherRequestRequest);
        if (!stringResult.getCode().equals("0")) {
            return stringResult;
        }

        VoucherRequest voucherRequest = voucherRequestMapper.selectOne(new VoucherRequest(updateVoucherRequestRequest.getVoucherRequestCode()));
        if (voucherRequest.getStatus() != 1 && voucherRequest.getStatus() != 3) {
            return Result.failed(ResultErrorCodeEnum.VOUCHER_STATUS_ERROR.code(), ResultErrorCodeEnum.VOUCHER_STATUS_ERROR.desc());
        }
        BeanCopyUtils.copyProps(updateVoucherRequestRequest, voucherRequest);
        voucherRequest.setUpdateTime(new Date());
        voucherRequest.setIssuerCode(updateVoucherRequestRequest.getIssuerCode());
        voucherRequest.setStatus(1);
        voucherRequestMapper.updateByPrimaryKeySelective(voucherRequest);
        createVoucherRequestNote(voucherRequest.getVoucherRequestCode(), voucherRequest.getRequestRemarks(), updateVoucherRequestRequest.getUpdateUser(), ApproveNodeRecordTypeEnum.EDIT.getType());
        Example example = new Example(VoucherRequestDetails.class);
        example.createCriteria().andEqualTo(VoucherRequestDetails.C_VOUCHER_REQUEST_CODE, voucherRequest.getVoucherRequestCode());
        List<VoucherRequestDetails> voucherRequestDetails1 = voucherRequestDetailsMapper.selectByCondition(example);

        //Verify the existence of the passed detail data to be modified and delete the existing but not passed detail data from the database
        //校验传入的要修改的详情数据是否存在，同时删除数据库存在但没传入的详情数据
        Assert.assertTrue("The modified voucher request detailed data does not exist", checkAndUpdateData(voucherRequestDetails1, updateVoucherRequestDetailsRequests));
        //Modified and added voucher request details
        //修改和新增券请求详情
        for (UpdateVoucherRequestDetailsRequest requestDetailsRequest : updateVoucherRequestDetailsRequests) {
            if (requestDetailsRequest.getVoucherRequestDetailsCode() != null) {
                VoucherRequestDetails voucherRequestDetails = voucherRequestDetailsMapper.selectOne(new VoucherRequestDetails(requestDetailsRequest.getVoucherRequestDetailsCode()));
                BeanCopyUtils.copyProps(requestDetailsRequest, voucherRequestDetails);
                voucherRequestDetails.setUpdateUser(updateVoucherRequestRequest.getUpdateUser());
                voucherRequestDetails.setPermissionCode(updateVoucherRequestRequest.getPermissionCode());
                voucherRequestDetails.setUpdateTime(new Date());
                voucherRequestDetailsMapper.updateByPrimaryKeySelective(voucherRequestDetails);
            } else {
                String voucherRequestDetailsCode = gvCodeHelper.generateVoucherRequestDetailsCode();
                VoucherRequestDetails voucherRequestDetails = BeanCopyUtils.jsonCopyBean(requestDetailsRequest, VoucherRequestDetails.class);
                voucherRequestDetails.setVoucherRequestDetailsCode(voucherRequestDetailsCode);
                voucherRequestDetails.setVoucherRequestCode(voucherRequest.getVoucherRequestCode());
                voucherRequestDetails.setPermissionCode(updateVoucherRequestRequest.getPermissionCode());
                voucherRequestDetails.setCreateUser(updateVoucherRequestRequest.getUpdateUser());
                voucherRequestDetails.setUpdateUser(updateVoucherRequestRequest.getUpdateUser());
                voucherRequestDetailsMapper.insertSelective(voucherRequestDetails);
            }
        }
        return Result.ok();
    }

    private boolean checkDetailRepeatForUpdate(List<UpdateVoucherRequestDetailsRequest> updateVoucherRequestDetailsRequests) {
        Set<BigDecimal> collect = updateVoucherRequestDetailsRequests.stream()
                .map(UpdateVoucherRequestDetailsRequest::getDenomination)
                .collect(Collectors.toSet());
        return collect.size() == updateVoucherRequestDetailsRequests.size();
    }

    private boolean checkAmount(UpdateVoucherRequestRequest updateVoucherRequestRequest) {
        BigDecimal amount = BigDecimal.ZERO;
        int num = 0;
        for (UpdateVoucherRequestDetailsRequest updateVoucherRequestDetailsRequest : updateVoucherRequestRequest.getUpdateVoucherRequestDetailsRequests()) {
            num += updateVoucherRequestDetailsRequest.getVoucherNum();
            amount = amount.add(updateVoucherRequestDetailsRequest.getVoucherAmount());
        }
        return num == updateVoucherRequestRequest.getVoucherNum() && amount.compareTo(updateVoucherRequestRequest.getVoucherAmount()) == 0;
    }

    @Transactional
    public boolean checkAndUpdateData(List<VoucherRequestDetails> voucherRequestDetails1, List<UpdateVoucherRequestDetailsRequest> updateVoucherRequestDetailsRequests) {
        List<UpdateVoucherRequestDetailsRequest> collect = updateVoucherRequestDetailsRequests.stream()
                .filter(updateVoucherRequestDetailsRequest -> updateVoucherRequestDetailsRequest.getVoucherRequestDetailsCode() != null).collect(Collectors.toList());

        Iterator<VoucherRequestDetails> iterator = voucherRequestDetails1.iterator();
        while (iterator.hasNext()) {
            VoucherRequestDetails next = iterator.next();
            Iterator<UpdateVoucherRequestDetailsRequest> iterator1 = collect.iterator();
            while (iterator1.hasNext()) {
                UpdateVoucherRequestDetailsRequest next1 = iterator1.next();
                if (next.getVoucherRequestDetailsCode().equals(next1.getVoucherRequestDetailsCode())) {
                    iterator.remove();
                    iterator1.remove();
                }
            }
        }
        if (!CollectionUtils.isEmpty(collect)) {
            return false;
        }

        if (!CollectionUtils.isEmpty(voucherRequestDetails1)) {
            for (VoucherRequestDetails requestDetails : voucherRequestDetails1) {
                voucherRequestDetailsMapper.delete(requestDetails);
            }
        }
        return true;
    }

    @Override
    public VoucherRequest queryByVoucherRequestCode(String voucherRequestCode) {

        if (StringUtils.isBlank(voucherRequestCode)) {
            return null;
        }
        return voucherRequestMapper.queryByVoucherRequestCode(voucherRequestCode);
    }

    @Override
    @Transactional
    public Result<Void> cancelVoucherRequest(CancelVoucherRequestRequest cancelVoucherRequestRequest) {
        return updateRequestStatus(cancelVoucherRequestRequest.getVoucherRequestCode(), VoucherRequestStatusEnum.CANCELED.getCode(), cancelVoucherRequestRequest.getCancelUser());
    }

    @Override
	public Result<GetVoucherRequestResponse> getVoucherRequest(GetVoucherRequestRequest getVoucherRequestRequest) {
		GetVoucherRequestResponse getVoucherRequestResponse = getVoucherRequestByCode(getVoucherRequestRequest.getVoucherRequestCode());
		if (getVoucherRequestResponse == null) {
			return Result.ok();
		}
		getVoucherRequestResponse.setApproveAble(false);
		if (VoucherRequestStatusEnum.PENDING_APPROVAL.getCode() == getVoucherRequestResponse.getStatus()
				&& !StringUtil.isEmpty(getVoucherRequestRequest.getRoleList()) && !StringUtil.isEmpty(getVoucherRequestRequest.getUserCode())) {
            ReleaseApproveAbleRequest build = ReleaseApproveAbleRequest.builder()
                    .releaseType(ApproveNodeRecordTypeEnum.APPROVE.getType())
					.issuerCode(getVoucherRequestResponse.getIssuerCode())
					.approveRoleCode(getVoucherRequestRequest.getRoleList()).approveUser(getVoucherRequestRequest.getUserCode())
                    .voucherAmount(getVoucherRequestResponse.getVoucherAmount())
					.releaseApproveAmountType(ApproveTypeEnum.APPROVE_TYPE_02.getType())
                    .businessCode(getVoucherRequestResponse.getVoucherRequestCode()).build();

            Result<Integer> booleanResult = releaseApproveService.approveAble(build);
            getVoucherRequestResponse.setApproveAble(booleanResult.isSuccess());
		}
		getVoucherRequestResponse.setShowStatus(getVoucherRequestResponse.getStatus().toString());
		if (VoucherRequestStatusEnum.PENDING_ALLOCATION.getCode() == getVoucherRequestResponse.getStatus()) {
			getVoucherRequestResponse.setShowStatus(getVoucherRequestResponse.getBusinessType() + getVoucherRequestResponse.getStatus().toString());
		}
        return Result.ok(getVoucherRequestResponse);
    }

	@Override
	public GetVoucherRequestResponse getVoucherRequestByCode(String voucherRequestCode) {
		VoucherRequest voucherRequest = voucherRequestMapper.selectOne(new VoucherRequest(voucherRequestCode.trim()));
        if (voucherRequest == null) {
            return null;
        }
        String outletCode;
		if (GvcoreConstants.SALES.equals(voucherRequest.getBusinessType())) {
            outletCode = voucherRequest.getReceiverCode();
        } else {
            outletCode = voucherRequest.getVoucherOwnerCode();
        }
        Example example = new Example(OutletCpg.class);
        example.createCriteria().andEqualTo(OutletCpg.C_OUTLET_CODE, outletCode);
        List<OutletCpg> outletCpgs = outletCpgMapper.selectByCondition(example);
        List<String> cpgCode = outletCpgs.stream().map(OutletCpg::getCpgCode).collect(Collectors.toList());
        Example example1 = new Example(Cpg.class);
        example1.createCriteria().andIn("cpgCode", cpgCode);
        List<Cpg> cpgs = cpgMapper.selectByCondition(example1);
        GetVoucherRequestResponse getVoucherRequestResponse = BeanCopyUtils.jsonCopyBean(voucherRequest, GetVoucherRequestResponse.class);
        getVoucherRequestResponse.setRequestTime(voucherRequest.getCreateTime());
        List<VoucherRequestDetails> select = voucherRequestDetailsMapper.select(VoucherRequestDetails.builder().voucherRequestCode(voucherRequestCode).build());
        List<CreateVoucherRequestDetailsRequest> createVoucherRequestDetailsRequests = BeanCopyUtils.jsonCopyList(select, CreateVoucherRequestDetailsRequest.class);
        List<CreateVoucherRequestDetailsRequest> voucherRequestDetailsRequestList = new ArrayList<>(createVoucherRequestDetailsRequests.size());
        for (CreateVoucherRequestDetailsRequest createVoucherRequestDetailsRequest : createVoucherRequestDetailsRequests) {
            for (Cpg cpg : cpgs) {
                if (StringUtil.isNotEmpty(createVoucherRequestDetailsRequest.getCpgCode()) && createVoucherRequestDetailsRequest.getCpgCode().equals(cpg.getCpgCode())) {
                    createVoucherRequestDetailsRequest.setCpgCode(cpg.getCpgCode());
                    createVoucherRequestDetailsRequest.setVpgName(cpg.getCpgName());
                }
            }
            voucherRequestDetailsRequestList.add(createVoucherRequestDetailsRequest);
        }
        getVoucherRequestResponse.setCreateVoucherRequestDetailsRequests(voucherRequestDetailsRequestList);
		return getVoucherRequestResponse;
	}

    @Override
    public PageResult<QueryVoucherRequestResponse> queryVoucherRequest(QueryVoucherRequestRequest queryVoucherRequestRequest) {

        final PermissionCodeResponse permission = this.permissionHelper.getPermission(queryVoucherRequestRequest.getUserCode(), queryVoucherRequestRequest.getIssuerCode());
        if (!PermissionCodeResponse.hasOutlet(permission)) {
            return new PageResult<>(Collections.emptyList(), 0L);
        }

        // owner_code , receiver_code 应当其中一个包含在outletCodeList
        final QueryVoucherRequestPermissionDto queryCondition = BeanCopyUtils.jsonCopyBean(queryVoucherRequestRequest, QueryVoucherRequestPermissionDto.class);
        queryCondition.setOutletCodeRangeList(permission.getOutletCodeList());

        PageMethod.startPage(queryVoucherRequestRequest.getPageNum(), queryVoucherRequestRequest.getPageSize());
        List<QueryVoucherRequestResponse> queryVoucherRequestResponseList;
        if (queryVoucherRequestRequest.getBusinessType() == null || !queryVoucherRequestRequest.getBusinessType().equals(GvcoreConstants.SALES)) {
            // Return & Transfer
            boolean hasApprovePermission = false;
            if (queryVoucherRequestRequest instanceof QueryVoucherRequestPermissionRequest) {
                hasApprovePermission = ((QueryVoucherRequestPermissionRequest) queryVoucherRequestRequest).getHasApprovePermission();
            }

            queryVoucherRequestResponseList = voucherRequestMapper.queryVoucherReturnOrTransfer(queryCondition, hasApprovePermission);
        } else {
            // Voucher Request
            queryVoucherRequestResponseList = voucherRequestMapper.queryVoucherRequestList(queryCondition);
        }
        PageInfo<QueryVoucherRequestResponse> info = PageInfo.of(queryVoucherRequestResponseList);
        List<QueryVoucherRequestResponse> queryVoucherRequestResponses = new ArrayList<>(info.getList().size());

        if (info.getList().size() == 0){
            return new PageResult<>(queryVoucherRequestResponses, info.getTotal());
        }


        List<String> collect = info.getList().stream().map(QueryVoucherRequestResponse::getCreatedBy).collect(Collectors.toList());
        Map<String, String> stringStringMap = userAccountService.queryFullNameByCodeList(collect);

        List<String> outletCodes = info.getList().stream().map(QueryVoucherRequestResponse::getOutletCode).collect(Collectors.toList());
        List<OutletResponse> outletResponses = outletMapper.queryOutletList(QueryOutletRequest.builder().outletCodeList(outletCodes).build());
        Map<String, OutletResponse> outletMap = outletResponses.stream().collect(Collectors.toMap(OutletResponse::getOutletCode, Function.identity()));



        for (QueryVoucherRequestResponse queryVoucherRequestResponse : info.getList()) {
            //outlet 拼接
            try {
                queryVoucherRequestResponse.setBusinessOutletCode(outletMap.get(queryVoucherRequestResponse.getOutletCode()).getBusinessOutletCode());
            } catch (Exception e) {
                queryVoucherRequestResponse.setVoucherRequestCode("");
            }


            Example example = new Example(VoucherRequestDetails.class);
            example.createCriteria().andEqualTo(VoucherRequestDetails.C_VOUCHER_REQUEST_CODE, queryVoucherRequestResponse.getVoucherRequestCode());
            List<VoucherRequestDetails> voucherRequestDetails = voucherRequestDetailsMapper.selectByCondition(example);




            StringBuilder stringBuilder = new StringBuilder();
            voucherRequestDetails.forEach(voucherRequestDetails1 -> {
                if(StringUtil.isNotEmpty(voucherRequestDetails1.getCpgCode())){
                    Result<GetCpgResponse> cpg = cpgService.getCpg(GetCpgRequest.builder().cpgCode(voucherRequestDetails1.getCpgCode()).build());
                    if (StringUtil.isNotEmpty(queryVoucherRequestResponse.getCpgName())){
                        queryVoucherRequestResponse.setCpgName(queryVoucherRequestResponse.getCpgName() + "," + cpg.getData().getCpgName());

                    }else {
                        queryVoucherRequestResponse.setCpgName(cpg.getData().getCpgName());
                    }
                }
                stringBuilder.append(voucherRequestDetails1.getDenomination().setScale(0, 1).toString()).append("|");
            });

            if (stringBuilder.length()>0){
                stringBuilder.deleteCharAt(stringBuilder.lastIndexOf("|"));

            }

            queryVoucherRequestResponse.setDenomination(stringBuilder.toString());
            if (stringStringMap.get(queryVoucherRequestResponse.getCreatedBy()) != null) {
                queryVoucherRequestResponse.setCreatedBy(stringStringMap.get(queryVoucherRequestResponse.getCreatedBy()));
            }

            queryVoucherRequestResponse.setShowStatus(queryVoucherRequestResponse.getStatus().toString());
            if (VoucherRequestStatusEnum.PENDING_ALLOCATION.getCode() == queryVoucherRequestResponse.getStatus()) {
                queryVoucherRequestResponse.setShowStatus(queryVoucherRequestResponse.getBusinessType()
                        + queryVoucherRequestResponse.getStatus().toString());
            }
            queryVoucherRequestResponses.add(queryVoucherRequestResponse);
        }
        log.info("Result: {}",JSON.toJSONString(queryVoucherRequestResponses));
        return new PageResult<>(queryVoucherRequestResponses, info.getTotal());
    }

    @Override
    public List<VoucherRequest> queryByVoucherRequestCodeList(List<String> voucherRequestCodeList) {

        List<VoucherRequest> list = voucherRequestMapper.queryByVoucherRequestCodeList(voucherRequestCodeList);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }

    @Override
    public Result<List<GetVoucherRequestResponse>> bulkGetVoucherRequests(List<GetVoucherRequestRequest> bulkGetVoucherRequestRequest) {
        List<GetVoucherRequestResponse> getVoucherRequestResponses = new ArrayList<>(bulkGetVoucherRequestRequest.size());
        for (GetVoucherRequestRequest getVoucherRequestRequest : bulkGetVoucherRequestRequest) {
			GetVoucherRequestResponse getVoucherRequestResponse = getVoucherRequestByCode(getVoucherRequestRequest.getVoucherRequestCode());
			if (getVoucherRequestResponse != null) {
				getVoucherRequestResponses.add(getVoucherRequestResponse);
            }
        }
        return Result.ok(getVoucherRequestResponses);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<String> approveVoucherRequest(ApproveVoucherRequestRequest approveVoucherRequestRequest) {

        VoucherRequest voucherRequest = voucherRequestMapper.selectOne(new VoucherRequest(approveVoucherRequestRequest.getVoucherRequestCode()));
        if (voucherRequest == null) {
            return Result.failed(ResultErrorCodeEnum.NO_VOUCHER_REQUEST_DETAIL_FOUND.code(), ResultErrorCodeEnum.NO_VOUCHER_REQUEST_DETAIL_FOUND.desc());
        }
        if (voucherRequest.getStatus() != 1) {
            return Result.failed(ResultErrorCodeEnum.VOUCHER_STATUS_ERROR.code(), ResultErrorCodeEnum.VOUCHER_STATUS_ERROR.desc());
        }
        voucherRequest.setUpdateTime(new Date());
        voucherRequest.setUpdateUser(approveVoucherRequestRequest.getUpdateUser());
        voucherRequest.setStatus(approveVoucherRequestRequest.getStatus());

        ApproveNodeRecord build = ApproveNodeRecord.builder()
                .approveUser(approveVoucherRequestRequest.getUpdateUser())
                .businessCode(voucherRequest.getVoucherRequestCode())
				.approveRoleCode(ApproveTypeEnum.APPROVE_TYPE_03.getType())
                .approveNodeRecordCode(gvCodeHelper.generateApproveNodeRecordCode())
                .createUser(approveVoucherRequestRequest.getUpdateUser())
                .createTime(new Date())
                .updateTime(new Date())
                .note(approveVoucherRequestRequest.getApproveRemarks())
                .releaseApproveAmountType("Approve Note")
                .releaseApproveNodeName(0)
                .status(approveVoucherRequestRequest.getStatus() == 4)
                .deleteStatus(GvcoreConstants.DELETE_STATUS_DISABLE)
                .build();
		approveNodeRecordMapper.insertSelective(build);
		voucherRequestMapper.updateByPrimaryKeySelective(voucherRequest);
		voucherRequest.setRequestRemarks(approveVoucherRequestRequest.getApproveRemarks());

		String alloctionCode = "";
        if (VoucherRequestStatusEnum.PENDING_ALLOCATION.getCode() == approveVoucherRequestRequest.getStatus()) {
			alloctionCode = voucherAllocationService.createByVoucherRequest(voucherRequest);
        }
		sendApproveEmail(voucherRequest, alloctionCode);

        return Result.ok(build.getApproveNodeRecordCode());
    }

    @Override
    @Transactional
    public Result<Void> bulkApproveVoucherRequest(BulkApproveVoucherRequestRequest bulkApproveVoucherRequestRequest) {
        List<VoucherRequest> voucherRequests = voucherRequestMapper.queryByVoucherRequestCodeList(bulkApproveVoucherRequestRequest.getVoucherRequestCode());
        List<ApproveNodeRecord> approveNodeRecords = new ArrayList<>(voucherRequests.size());
        for (VoucherRequest voucherRequest : voucherRequests) {
            if (voucherRequest.getStatus() == 1) {

                voucherRequest.setStatus(bulkApproveVoucherRequestRequest.getStatus());
                voucherRequest.setUpdateTime(new Date());
                voucherRequest.setUpdateUser(bulkApproveVoucherRequestRequest.getUpdateUser());
                voucherRequestMapper.updateByPrimaryKeySelective(voucherRequest);
				String alloctionCode = "";
                if (VoucherRequestStatusEnum.PENDING_ALLOCATION.getCode() == voucherRequest.getStatus()) {
					alloctionCode = voucherAllocationService.createByVoucherRequest(voucherRequest);
                }
				sendApproveEmail(voucherRequest, alloctionCode);
                ApproveNodeRecord build = ApproveNodeRecord.builder()
                        .approveUser(bulkApproveVoucherRequestRequest.getUpdateUser())
                        .businessCode(voucherRequest.getVoucherRequestCode())
						.approveRoleCode(ApproveTypeEnum.APPROVE_TYPE_03.getType())
                        .approveNodeRecordCode(gvCodeHelper.generateApproveNodeRecordCode())
                        .createUser(bulkApproveVoucherRequestRequest.getUpdateUser())
                        .createTime(new Date())
                        .updateTime(new Date())
                        .note(bulkApproveVoucherRequestRequest.getApproveRemarks())
						.releaseApproveAmountType(ApproveTypeEnum.APPROVE_TYPE_03.getType())
                        .releaseApproveNodeName(0)
                        .status(bulkApproveVoucherRequestRequest.getStatus() == 4)
                        .deleteStatus(GvcoreConstants.DELETE_STATUS_DISABLE)
                        .build();
                approveNodeRecords.add(build);
            } else {
                return Result.failed(ResultErrorCodeEnum.VOUCHER_STATUS_ERROR.code(), ResultErrorCodeEnum.VOUCHER_STATUS_ERROR.desc());
            }
        }
        approveNodeRecordMapper.insertList(approveNodeRecords);
        return Result.ok();
    }

    @Override
    public Result<QueryHeadOfficeAndWareHouseResponse> queryHeadOfficeAndWareHouse(QueryHeadOfficeAndWareHouseRequest request) {
        return Result.ok(QueryHeadOfficeAndWareHouseResponse.builder()
                .headOffice(issuerWarehouse.get(request.getIssuerCode()))
                .wareHouse(issuerWarehouseMap.get(request.getIssuerCode()))
                .mv01(mv01).mv03(mv03).mv04(mv04).build());
    }

    private void sendApproveEmail(VoucherRequest voucherRequest, String alloctionCode) {
        SendNoticeRequest sendNoticeRequest = new SendNoticeRequest();
		Map<String, Object> params = getExtendparams(voucherRequest);
        if (voucherRequest.getStatus() == 4) {
			params.put("flowNode", FlowNodeEnum.APPROVE.getDesc());
			// 通过后发送给下个节点
            sendNoticeRequest.setFlowCode(FlowEnum.SALES_VOUCHER_FLOW.getCode());
            sendNoticeRequest.setFlowNodeCode(FlowNodeEnum.APPROVE.getCode());
            sendNoticeRequest.setBusinessCode(voucherRequest.getVoucherRequestCode());
			params.put("alloctionCode", alloctionCode);
			sendNoticeRequest.setExtendParams(params);
            flowNoticeService.send(sendNoticeRequest);
        } else {
            sendNoticeRequest.setFlowCode(FlowEnum.SALES_VOUCHER_FLOW.getCode());
			sendNoticeRequest.setFlowNodeCode(FlowNodeEnum.REJECTED.getCode());
			params.put("flowNode", FlowNodeEnum.REJECTED.getDesc());
        }
		SendEmailRequest request = new SendEmailRequest();
		// 发送给请求者
		String createUser = voucherRequest.getCreateUser();
		String email = userAccountService.getUserEmail(createUser);
		if (!StringUtil.isEmpty(email)) {
			request.setEmails(Arrays.asList(email));
		}
		request.setBusinessCode(voucherRequest.getVoucherRequestCode());
		request.setExtendParams(params);
		messageComponent.sendEmail(request, MessageEnventEnum.REJECT_VOUCHER_REQUEST.getCode());
    }
}
