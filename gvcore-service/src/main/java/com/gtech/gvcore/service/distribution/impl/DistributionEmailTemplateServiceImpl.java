package com.gtech.gvcore.service.distribution.impl;

import com.alibaba.fastjson.JSONObject;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.common.enums.DisEmailTemplateStatusEnum;
import com.gtech.gvcore.common.enums.MessageEnventEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.exception.GvcoreParamValidateException;
import com.gtech.gvcore.common.request.customer.GetCustomerRequest;
import com.gtech.gvcore.common.request.distribution.ChangeEmailTemplateStatusRequest;
import com.gtech.gvcore.common.request.distribution.CreateDistributionEmailTemplateRequest;
import com.gtech.gvcore.common.request.distribution.PreviewSendEmailTemplateRequest;
import com.gtech.gvcore.common.request.distribution.QueryEmailTemplateRequest;
import com.gtech.gvcore.common.request.distribution.UpdateDistributionEmailTemplateRequest;
import com.gtech.gvcore.common.response.customer.CustomerResponse;
import com.gtech.gvcore.common.response.distribution.DistributionEmailTemplateResponse;
import com.gtech.gvcore.dao.mapper.DistributionEmailTemplateMapper;
import com.gtech.gvcore.dao.model.DistributionEmailTemplate;
import com.gtech.gvcore.dto.DistributionEmailDataModel;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.helper.GvHtmlHelper;
import com.gtech.gvcore.service.CustomerService;
import com.gtech.gvcore.service.distribution.DistributionEmailTemplateService;
import com.gtech.gvcore.service.impl.MessageComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName DistributionEmailTemplateServiceImpl
 * @Description 分发系统邮件模板Service实现
 * <AUTHOR>
 * @Date 2022/7/5 15:17
 * @Version V1.0
 **/
@Slf4j
@Service
public class DistributionEmailTemplateServiceImpl implements DistributionEmailTemplateService {

    /**
     * 匹配域名的正则表达式.注意,如果该表达式修改了,相应的代码可能也需要调整
     */
    public static final String DOMAIN_NAME_REGULAR_EXPRESSION = "^(?![${])[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\\.?(?<![}])$";

    private final Pattern domainNamePattern = Pattern.compile(DOMAIN_NAME_REGULAR_EXPRESSION);

    @Autowired
    private DistributionEmailTemplateMapper distributionEmailTemplateMapper;

    @Autowired
    private GvCodeHelper gvCodeHelper;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private MessageComponent messageComponent;

    /**
     * 富文本中允许出现的域名集
     */
    @Value("#{'${gv.distribution.email.domain.white:}'.split(',')}")
    private Set<String> domainNameWhitelist;

    /**
     * 创建分发邮件模板
     * 模板富文本和主题中不允许存在第三方域名
     *
     * @param request 邮件模板参数
     * @return java.lang.String 邮件模板code
     * <AUTHOR>
     * @date 2022/7/5 16:50
     * @since 1.0.0
     */
    @Override
    @Transactional
    public String createEmailTemplate(final CreateDistributionEmailTemplateRequest request) {

        final DistributionEmailTemplate emailTemplate = BeanCopyUtils.jsonCopyBean(request, DistributionEmailTemplate.class);

        this.validateEmailTemplate(emailTemplate);

        emailTemplate.setTemplateCode(this.gvCodeHelper.generateDistributionEmailTemplateCode());
        emailTemplate.setCreateUser(request.getUserCode());
        emailTemplate.setUpdateUser(request.getUserCode());

        this.distributionEmailTemplateMapper.insertSelective(emailTemplate);

        return emailTemplate.getTemplateCode();
    }

    /**
     * 更新分发邮件模板
     * 模板富文本和主题中不允许存在第三方域名
     *
     * @param request 新邮件模板内容
     * @return java.lang.String 邮件模板编码
     * <AUTHOR>
     * @date 2022/7/5 16:52
     * @since 1.0.0
     */
    @Override
    @Transactional
    public String updateEmailTemplate(final UpdateDistributionEmailTemplateRequest request) {

        final DistributionEmailTemplate emailTemplate = BeanCopyUtils.jsonCopyBean(request, DistributionEmailTemplate.class);

        this.validateEmailTemplate(emailTemplate);

        emailTemplate.setUpdateUser(request.getUserCode());

        final Example updateCondition = new Example(DistributionEmailTemplate.class);
        updateCondition.createCriteria()
                .andEqualTo(DistributionEmailTemplate.C_TEMPLATE_CODE, ConvertUtils.toString(request.getTemplateCode(), ""))
                .andEqualTo(DistributionEmailTemplate.C_CUSTOMER_CODE, ConvertUtils.toString(request.getCustomerCode(), ""));

        this.distributionEmailTemplateMapper.updateByConditionSelective(emailTemplate, updateCondition);

        return emailTemplate.getTemplateCode();
    }

    /**
     * 修改邮件模板的状态
     *
     * @param request 最新状态信息
     * @return java.lang.String 邮件模板编码
     * <AUTHOR>
     * @date 2022/7/5 16:53
     * @since 1.0.0
     */
    @Override
    @Transactional
    public String changeStatus(final ChangeEmailTemplateStatusRequest request) {

        final DistributionEmailTemplate changeStatusParam = new DistributionEmailTemplate();
        changeStatusParam.setStatus(request.getStatus());

        final Example changeStatusCondition = new Example(DistributionEmailTemplate.class);
        changeStatusCondition.createCriteria()
                .andEqualTo(DistributionEmailTemplate.C_TEMPLATE_CODE, ConvertUtils.toString(request.getTemplateCode(), ""))
                .andEqualTo(DistributionEmailTemplate.C_CUSTOMER_CODE, ConvertUtils.toString(request.getCustomerCode(), ""));

        this.distributionEmailTemplateMapper.updateByConditionSelective(changeStatusParam, changeStatusCondition);

        return request.getTemplateCode();
    }

    /**
     * 查询全部模板
     *
     * @param request 查询条件
     * @return java.util.List<com.gtech.gvcore.common.response.distribution.DistributionEmailTemplateResponse> 模板数据集
     * <AUTHOR>
     * @date 2022/7/5 18:19
     * @since 1/0/0
     */
    @Override
    public List<DistributionEmailTemplateResponse> queryEmailTemplates(final QueryEmailTemplateRequest request) {

        final Example queryCondition = new Example(DistributionEmailTemplate.class);
        queryCondition.createCriteria()
                .andEqualTo(DistributionEmailTemplate.C_CUSTOMER_CODE, ConvertUtils.toString(request.getCustomerCode(), ""))
                .andEqualTo(DistributionEmailTemplate.C_STATUS, ConvertUtils.toInteger(request.getStatus(), null))
                .andEqualTo(DistributionEmailTemplate.C_TEMPLATE_TYPE, ConvertUtils.toInteger(request.getTemplateType(), null));

        queryCondition.orderBy(DistributionEmailTemplate.C_CREATE_TIME).asc();

        final List<DistributionEmailTemplate> templateList = this.distributionEmailTemplateMapper.selectByCondition(queryCondition);

        return BeanCopyUtils.jsonCopyList(templateList, DistributionEmailTemplateResponse.class);
    }

    @Override
    public void previewSend(final PreviewSendEmailTemplateRequest request) {

        if (StringUtils.isBlank(request.getCustomerCode())) return;

        GetCustomerRequest param = new GetCustomerRequest();
        param.setCustomerCode(request.getCustomerCode());
        CustomerResponse customer = customerService.getCustomer(param);
        if (null == customer) return;

        final String emailContent;

        try {
            String value = "...";
            emailContent = GvHtmlHelper.fillWithTemplateContent(request.getRichText(), new DistributionEmailDataModel()
                    .setVoucherList(Collections.singletonList(new DistributionEmailDataModel.EmailVoucherInfo()
                            .setActivationCode(value)
                            .setActivationURL(value)
                            .setBarCode(value)
                            .setDenomination(value)
                            .setPinCode(value)
                            .setVoucherExpiryDate(value)
                            .setVoucherNumber(value))));

        } catch (Exception e) {
            log.error("分发邮件填充邮件模板失败.", e);
            return;
        }

        final JSONObject jsonParam = new JSONObject();
        jsonParam.put("subject", request.getSubject());
        jsonParam.put("content", emailContent);
        jsonParam.put("email", customer.getUserEmail());

        final JSONObject emailRequest = new JSONObject();
        emailRequest.put("eventCode", MessageEnventEnum.DISTRIBUTION_EMAIL.getCode());
        emailRequest.put("param", jsonParam);

        this.messageComponent.sendEmail(emailRequest);
    }

    /**
     * 验证邮件模板是否符合规则
     *
     * @param emailTemplate 邮件模板
     * @throws GvcoreParamValidateException 邮件模板内容不合规则时抛出异常
     * <AUTHOR>
     * @date 2022/7/5 18:16
     * @since 1.0.0
     */
    private void validateEmailTemplate(final DistributionEmailTemplate emailTemplate) {
        this.validateDomainName(emailTemplate);
        this.validateTemplateType(emailTemplate);
    }

    @Override
    public void validateEmailContent(final String subject, final String richText) {
        final DistributionEmailTemplate distributionEmailTemplate = new DistributionEmailTemplate();
        distributionEmailTemplate.setSubject(subject);
        distributionEmailTemplate.setRichText(richText);

        this.validateDomainName(distributionEmailTemplate);
    }

    /**
     * 校验邮件模板类型,同customer下模板类型不允许重复
     *
     * @param emailTemplate 邮件信息
     * @throws GvcoreParamValidateException 邮件模板类型错误或重复时抛出异常
     * <AUTHOR>
     * @date 2022/7/6 9:28
     * @since 1.0.0
     */
    private void validateTemplateType(final DistributionEmailTemplate emailTemplate) {

        // 限制是正确的模板类型
        if (null == DisEmailTemplateStatusEnum.valueOfCode(emailTemplate.getTemplateType())) {
            throw new GvcoreParamValidateException(ResultErrorCodeEnum.DISTRIBUTION_EMAIL_TEMPLATE_TYPE_NULL.code(), ResultErrorCodeEnum.DISTRIBUTION_EMAIL_TEMPLATE_TYPE_NULL.desc());
        }

        final Example countCondition = new Example(DistributionEmailTemplate.class);
        countCondition.createCriteria()
                .andNotEqualTo(DistributionEmailTemplate.C_TEMPLATE_CODE, emailTemplate.getTemplateCode())
                .andEqualTo(DistributionEmailTemplate.C_CUSTOMER_CODE, emailTemplate.getCustomerCode())
                .andEqualTo(DistributionEmailTemplate.C_TEMPLATE_TYPE, emailTemplate.getTemplateType());

        // 限制当前类型的模板数量小于等于0
        final int count = this.distributionEmailTemplateMapper.selectCountByCondition(countCondition);
        if (count > 0) {
            throw new GvcoreParamValidateException(ResultErrorCodeEnum.DISTRIBUTION_EMAIL_TEMPLATE_TYPE_REPEAT.code(), ResultErrorCodeEnum.DISTRIBUTION_EMAIL_TEMPLATE_TYPE_REPEAT.desc());
        }
    }

    /**
     * 校验非法域名
     *
     * @param emailTemplate 邮件信息
     * @throws GvcoreParamValidateException 主题和富文本含有不被允许的域名时抛出该异常
     * <AUTHOR>
     * @date 2022/7/5 18:15
     * @since 1.0.0
     */
    private void validateDomainName(final DistributionEmailTemplate emailTemplate) {

        final String subject = emailTemplate.getSubject();
        final String richText = emailTemplate.getRichText();

        // 目前邮件模板中包含的域名集
        final Set<String> domainNameSet = new HashSet<>();
        domainNameSet.addAll(this.getDomainNameSet(subject));
        domainNameSet.addAll(this.getDomainNameSet(richText));

        for (String domainName : domainNameSet) {
            if (!this.domainNameWhitelist.contains(domainName)) {
                throw new GvcoreParamValidateException(ResultErrorCodeEnum.ILLEGAL_DOMAIN_NAME.code(), ResultErrorCodeEnum.ILLEGAL_DOMAIN_NAME.desc());
            }
        }
    }

    /**
     * 从文本中获取全部域名.仅包含域名名称
     *
     * @param text 待匹配文本
     * @return java.util.Set<java.lang.String> 所有的域名集
     * <AUTHOR>
     * @date 2022/7/5 17:36
     * @since 1.0.0
     */
    private Set<String> getDomainNameSet(final String text) {

        if (StringUtils.isBlank(text)) {
            return Collections.emptySet();
        }

        final Matcher matcher = this.domainNamePattern.matcher(text);

        final Set<String> domainNameSet = new HashSet<>();

        while (matcher.find()) {
            // group(0): 仅获取域名
            domainNameSet.add(matcher.group(0));
        }

        return domainNameSet;
    }

}
