package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.gvcore.common.utils.GvConvertUtils;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import lombok.Builder;
import lombok.Getter;

import java.util.Map;

@Builder
@Getter
public class BulkOrderReportBasicDataContext {

    private final Map<String, Outlet> outletMap;
    private final Map<String, Merchant> merchantMap;
    private final Map<String, Customer> customerMap;
    private final Map<String, String> orderStatusMap;
    private final Map<String, String> discountTypeMap;
    private final Map<String, String> meansOfPaymentMap;
    private final Map<String, String> userNameMap;
    private final Map<String, String> voucherTypeMap;

    public Outlet getOutlet(final String outletCode) {
        return this.outletMap.get(outletCode);
    }

    public Merchant getMerchant(final String merchantCode) {
        return this.merchantMap.get(merchantCode);
    }

    public String getOrderStatusText(final String status) {
        return GvConvertUtils.toString(this.orderStatusMap.get(status), status);
    }

    public String getDiscountTypeText(final String discountType) {
        return GvConvertUtils.toString(this.discountTypeMap.get(discountType), discountType);
    }

    public String getPaymentMode(final String meansOfPaymentCode) {
        return this.meansOfPaymentMap.get(meansOfPaymentCode);
    }

    public String getActivatingUserLogin(final String createUser) {
        return this.userNameMap.get(createUser);
    }

    public String getSubmitAccount(final String createUser) {
        return this.userNameMap.get(createUser);
    }

    public String getCustomerName(final String customerCode) {
        final Customer customer = this.customerMap.get(customerCode);
        // CompanyName 为 Customer 的 customerName
        return null == customer ? null : customer.getCustomerName();
    }

    public String getCompanyName(final String customerCode) {
        final Customer customer = this.customerMap.get(customerCode);
        // Corporate 为 Customer 的 CompanyName
        return null == customer ? null : customer.getCompanyName();
    }

    public String getVoucherTypeText(final String mopCode) {
        return GvConvertUtils.toString(this.voucherTypeMap.get(mopCode), mopCode);
    }
}