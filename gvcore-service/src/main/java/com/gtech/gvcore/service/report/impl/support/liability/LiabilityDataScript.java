package com.gtech.gvcore.service.report.impl.support.liability;

import com.gtech.commons.page.PageParam;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.common.enums.ReportVoucherStatusEnum;
import com.gtech.gvcore.common.utils.GvConvertUtils;
import com.gtech.gvcore.dao.mapper.IssueHandlingDetailsMapper;
import com.gtech.gvcore.dao.mapper.IssueHandlingMapper;
import com.gtech.gvcore.dao.mapper.ReportLiabilityGenerateHistoryMapper;
import com.gtech.gvcore.dao.mapper.ReportTempLiabilityDStructureMapper;
import com.gtech.gvcore.dao.mapper.ReportTempLiabilitySStructureMapper;
import com.gtech.gvcore.dao.model.IssueHandling;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.dao.model.ReportLiabilityGenerateHistory;
import com.gtech.gvcore.dao.model.ReportTempLiabilityDStructure;
import com.gtech.gvcore.dao.model.ReportTempLiabilitySStructure;
import com.gtech.gvcore.helper.GvPageHelper;
import com.gtech.gvcore.helper.RedisLockHelper;
import com.gtech.gvcore.service.report.extend.voucherstatus.ReportVoucherStatusConvertUtils;
import com.gtech.gvcore.service.report.impl.support.liability.model.LiabilityDetailStatisticDto;
import com.gtech.gvcore.service.report.impl.support.liability.model.LiabilitySummaryStatisticDto;
import com.gtech.gvcore.service.report.impl.support.liability.model.LiabilityTransactionModel;
import com.gtech.gvcore.service.report.impl.support.liability.model.LiabilityVoucherBo;
import com.gtech.gvcore.service.report.impl.support.liability.model.LiabilityVoucherMode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.Sqls;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @ClassName LiabilitySummarySupport
 * @Description liability summary support<br>
 * <p style="color: #57bb8a;">设计思路:</p>
 * <p style="color: #57bb8a;margin-left: 15px;">
 *      1. 通过redis锁定操作<br>
 *      2. 查询历史生成记录 没有则创建 并且设置为生成中 随后初始化相关表<br>
 *      3. 通过64位分表分页查询所有有责卡券数据
 *           使用 parallelStream 并行流进行64位数据分组 使每一个并行流中查询各自的分表数据
 *           并进行数据归并分组 (summary 为全程内存统计, detail 为段落内存统计)<br>
 *      4. 保存分组结果更新生成记录<br>
 * </p>
 * <p>
 * <p style="color: #616161;">ps:</p>
 * <p style="color: #616161;margin-left: 15px;">
 *      1.使用TEXT 数据类型 为避免卡券数据过长导致插入失败 (最后一次测试为 1600条数据存储在 16,384 字节的 VARCHAR 字段中存储失败)<br>
 *      2.相关分段大小设置<br>
 *          @see com.gtech.gvcore.service.report.impl.support.liability.model.LiabilityDetailStatisticDto#PAGE_SIZE
 * </p>
 * <AUTHOR>
 * @Date 2023/4/15 10:29
 * @Version V1.0
 */
@Slf4j
@Component
@EnableScheduling
public class LiabilityDataScript {

    @Autowired
    private ReportLiabilityGenerateHistoryMapper reportLiabilityGenerateHistoryMapper;

    @Autowired
    private ReportTempLiabilitySStructureMapper reportTempLiabilitySStructureMapper;

    @Autowired
    private ReportTempLiabilityDStructureMapper reportTempLiabilityDStructureMapper;

    @Autowired
    private IssueHandlingMapper issueHandlingMapper;

    @Autowired
    private IssueHandlingDetailsMapper issueHandlingDetailsMapper;

    private static final String LIABILITY_GENERATE_REDIS_LOCK_KEY = "liability_generate_lock_key";

    public static final String LIABILITY_SUMMARY_TABLE_TYPE = "s";
    public static final String LIABILITY_DETAIL_TABLE_TYPE = "d";
    public static final String TABLE_CODE_DATE_FORMAT = "M"; // 改为月份格式 1-12

    private static final String TABLE_NAME_TEMPLATE = "gv_report_temp_liability_%s_%s";

    /**
     * table mode [NONE, CREATE]
     */
    @Value("${gvcore.report.liability.table.mode:NONE}")
    private String tableMode;

    @Value("${liability.switch:false}")
    private Boolean sqlSwitch;

    @Scheduled(cron = "0 0 0 1 * ?")
    public void scheduled() {

        // table code
        final String tableCode = DateUtil.format(new Date(), TABLE_CODE_DATE_FORMAT);

        // runner
        this.runner(tableCode);

    }

    public boolean existReport(final String tableCode, final String tableType) {

        final String tableNam = String.format(TABLE_NAME_TEMPLATE, tableType, tableCode);

        if (!this.tableExist(tableNam)) {

            return !this.runner(tableCode);
        }

        return false;
    }

    private boolean tableExist(String tableNam) {

        try {
            return reportLiabilityGenerateHistoryMapper.existTable(tableNam) > 0;
        } catch (Exception e) {
            return false;
        }
    }

    public boolean runner (final String tableCode) {

        // lock
        boolean lock = RedisLockHelper.tryLock(LIABILITY_GENERATE_REDIS_LOCK_KEY, tableCode, 3000L, 30 * 60 * 1000L);
        if (!lock) return false;

        try {
            this.execute(tableCode);
        } finally {

            //unlock
            RedisLockHelper.unLock(LIABILITY_GENERATE_REDIS_LOCK_KEY, tableCode);
        }

        return true;
    }

    /**
     * excute
     * @param tableCode
     */
    private void execute(String tableCode) {

        try {

            // validate
            if (!validate(tableCode)) return;

            // result
            final Map<String, LiabilitySummaryStatisticDto> summaryMap = new ConcurrentHashMap<>();
            final Map<String, LiabilityDetailStatisticDto> detailMap = new ConcurrentHashMap<>();

            // thread error message
            final AtomicReference<String> errorMessage = new AtomicReference<>("");

            // final List<String> deactivateVoucherCodeList = getDeactivateVoucherCodeList();

            // parallel for
            IntStream.rangeClosed(0, 63)
                    .parallel()
                    .forEach(i -> this.group(tableCode, summaryMap, detailMap, errorMessage, i));

            if (StringUtils.isNotBlank(errorMessage.get())) {

                this.updateReportLiabilityGenerateHistory(tableCode, new ReportLiabilityGenerateHistory()
                        .setErrorLog(errorMessage.get()).setLastSuccessTime(new Date()));

                log.warn("generate liability data error, table code: {}, error message : {}", tableCode, errorMessage.get());
            }

            this.callback(tableCode, summaryMap, detailMap);

        } catch (Exception e) {

            log.error("generate liability data error, table code: {}", tableCode, e);

            // update error log
            this.updateReportLiabilityGenerateHistory(tableCode, new ReportLiabilityGenerateHistory().setErrorLog(e.getMessage()).setLastSuccessTime(new Date()));

        }
    }

    /**
     * get deactivate voucher code list
     * @return
     */
    private List<String> getDeactivateVoucherCodeList() {

        //issue handling code
        final List<IssueHandling> issueHandlingList = GvConvertUtils.toObject(issueHandlingMapper.select(new IssueHandling().setIssueType(IssueHandlingTypeEnum.BULK_DEACTIVATE.code())), new ArrayList<>());
        final List<String> issueHandlingCodeList = issueHandlingList.stream().map(IssueHandling::getIssueHandlingCode).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(issueHandlingCodeList)) return new ArrayList<>();

        //issue handling detail
        final List<IssueHandlingDetails> issueHandlingDetailList = GvConvertUtils.toObject(
                issueHandlingDetailsMapper.selectByCondition(Example.builder(IssueHandlingDetails.class)
                        .where(Sqls.custom().andIn(IssueHandlingDetails.C_ISSUE_HANDLING_CODE, issueHandlingCodeList))
                        .build()
                ), new ArrayList<>());

        //to voucher code list
        return issueHandlingDetailList.stream().map(IssueHandlingDetails::getVoucherCode).distinct().collect(Collectors.toList());
    }

    /**
     * callback
     * @param tableCode
     * @param summaryMap
     * @param detailMap
     */
    private void callback(final String tableCode,
                          final Map<String, LiabilitySummaryStatisticDto> summaryMap,
                          final Map<String, LiabilityDetailStatisticDto> detailMap) {

        // result
        final Collection<LiabilitySummaryStatisticDto> summaryList  = summaryMap.values();
        final Collection<LiabilityDetailStatisticDto> detailList    = detailMap.values();

        // empty
        if (CollectionUtils.isEmpty(summaryList) || CollectionUtils.isEmpty(detailList)) {

            this.updateReportLiabilityGenerateHistory(tableCode, new ReportLiabilityGenerateHistory().setErrorLog("NOT FOUND DATA").setLastSuccessTime(new Date()));

            log.warn("not found data for liability table code: {}", tableCode);
            return;
        }

        // save
        this.saveResult(tableCode, summaryList, detailList);

        // update success time
        this.updateReportLiabilityGenerateHistory(tableCode, new ReportLiabilityGenerateHistory().setErrorLog("").setLastSuccessTime(new Date()));
    }

    private void group(final String tableCode,
                       final Map<String, LiabilitySummaryStatisticDto> summaryMap,
                       final Map<String, LiabilityDetailStatisticDto> detailMap,
                       final AtomicReference<String> errorMessage,
                       final int tableIndex) {

        try {

            // time - 使用当前日期而不是解析 tableCode
            final Date now = new Date();
            final Date lastMoonTime = DateUtil.addMonth(now, -1);

            // page param
            final PageParam param = new PageParam();
            param.setPageSize(500);
            param.setPageNum(0);

            // poll voucher and group
            for (List<LiabilityVoucherMode> vouchers = pollVoucher(now, tableIndex, param);
                 CollectionUtils.isNotEmpty(vouchers);
                 vouchers = pollVoucher(now, tableIndex, param)) {

                log.info("select voucher list , tableCode : {}, voucher table index : {}, pageIndex : {}, voucher size :{}", tableCode, tableIndex,
                        (param.getPageNum() - 1) * param.getPageSize(), vouchers.size());

                // convert to voucher bo
                final List<LiabilityVoucherBo> liabilityVoucherBoList = vouchers.stream()
                        .map(e -> new LiabilityVoucherBo(tableCode, lastMoonTime, e, ReportVoucherStatusConvertUtils.getVoucherStatus(e.convertVoucher(), now)))
                        // filter deactivate voucher
                        // case issue handling deactivate voucher
                        // .filter(e -> ReportVoucherStatusEnum.VOUCHER_DEACTIVATED != e.getVoucherStatusEnum() || deactivateVoucherCodeList.contains(e.getVoucherCode()))
                        .collect(Collectors.toList());

                log.info("convert bo, tableCode : {}, voucher table index : {}", tableCode, tableIndex);

                // select transaction
                this.settingTransaction(liabilityVoucherBoList, tableIndex);

                log.info("setting transaction, tableCode : {}, voucher table index : {}", tableCode, tableIndex);

                // group
                final Map<String, LiabilitySummaryStatisticDto> groupMap = liabilityVoucherBoList.stream()
                        .map(LiabilitySummaryStatisticDto::convert)
                        .collect(Collectors.toMap(
                                LiabilitySummaryStatisticDto::getGroupKey,
                                Function.identity(),
                                LiabilitySummaryStatisticDto::merge
                        ));

                //merge group result
                groupMap.forEach((k, v) -> summaryMap.merge(k , v, LiabilitySummaryStatisticDto::merge));

                // group detail
                liabilityVoucherBoList.stream()
                        .map(e -> LiabilityDetailStatisticDto.convert(e, this::refreshDetail))
                        .forEach(e -> detailMap.merge(LiabilityDetailStatisticDto.getGroupKey(e), e, LiabilityDetailStatisticDto::merge));

                log.info("group, tableCode : {}, voucher table index : {}", tableCode, tableIndex);

            }
        } catch (Exception e) {

            log.error("generate liability data error, table code: {}, voucher table index : {}", tableCode, tableIndex, e);

            errorMessage.updateAndGet(v -> v + e.getMessage());
        }
    }

    /**
     * save result
     * @param tableCode
     * @param summaryList
     * @param detailList
     */
    private void saveResult(final String tableCode,
                            final Collection<LiabilitySummaryStatisticDto> summaryList,
                            final Collection<LiabilityDetailStatisticDto> detailList) {

        //summary
        final List<ReportTempLiabilitySStructure> summary = summaryList.stream()
                .map(LiabilitySummaryStatisticDto::toEntity)
                .collect(Collectors.toList());
        //detail
        final List<ReportTempLiabilityDStructure> detail = detailList.stream()
                .filter(e -> StringUtils.isNotBlank(e.getVoucherCodes().get()))
                .map(LiabilityDetailStatisticDto::toEntity)
                .collect(Collectors.toList());

        // save
        ListUtils.partition(summary, 1000).forEach(s -> reportTempLiabilitySStructureMapper.insertBatch(tableCode, s));
        ListUtils.partition(detail, 1000).forEach(d -> reportTempLiabilityDStructureMapper.insertBatch(tableCode, d));

    }

    /**
     * refresh detail
     * 分段存储减少内存压力
     * @param dto
     */
    private void refreshDetail(LiabilityDetailStatisticDto dto) {

        // detail
        final ReportTempLiabilityDStructure detail = dto.toEntity();

        // save
        this.reportTempLiabilityDStructureMapper.insertDetail(dto.getTableCode(), detail);

    }

    private void settingTransaction(final List<LiabilityVoucherBo> liabilityVoucherBoList, final int voucherTableIndex) {

        // find transaction
        Optional.ofNullable(reportLiabilityGenerateHistoryMapper.selectTransaction(liabilityVoucherBoList.stream().map(LiabilityVoucherBo::getVoucherCode).collect(Collectors.toList()), voucherTableIndex))
                //to Map
                .map(e -> e.stream().collect(Collectors.toMap(LiabilityTransactionModel::getVoucherCode, Function.identity(), (k1, k2) -> k1)))
                // set transaction data
                .ifPresent(e -> liabilityVoucherBoList.forEach(v -> v.setStatusTransactionData(e.get(v.getVoucherCode()))));

    }

    /**
     * update history
     * @param createTime
     * @param tableIndex
     * @param param
     * @return
     */
    private List<LiabilityVoucherMode> pollVoucher(final Date createTime, final int tableIndex, final PageParam param) {
        final List<LiabilityVoucherMode> liabilityVoucherModes;

        if (sqlSwitch){
            liabilityVoucherModes = reportLiabilityGenerateHistoryMapper.selectVoucherBySalesTime(createTime, tableIndex, param.getPageNum(), param.getPageSize());
        }else {
            liabilityVoucherModes = reportLiabilityGenerateHistoryMapper.selectVoucher(createTime, tableIndex, param.getPageNum(), param.getPageSize());
        }

        if (CollectionUtils.isEmpty(liabilityVoucherModes)) return Collections.emptyList();

        final int maxId = (int) liabilityVoucherModes.stream().mapToLong(LiabilityVoucherMode::getId).max().orElseThrow(() -> new IllegalArgumentException("查询异常"));
        param.setPageNum(maxId);

        return liabilityVoucherModes;
    }

    /**
     * validate operation
     * @param tableCode
     * @return
     */
    private boolean validate(String tableCode) {

        // select history generate by table code
        ReportLiabilityGenerateHistory liabilityGenerateHistory = reportLiabilityGenerateHistoryMapper.selectOne(new ReportLiabilityGenerateHistory().setLiabilityTableCode(tableCode));

        // history not exist
        if (null == liabilityGenerateHistory) {

            // init table
            this.initTable(tableCode);

            // insert history
            reportLiabilityGenerateHistoryMapper.insert(new ReportLiabilityGenerateHistory()
                    .setLiabilityTableCode(tableCode)
                    .setGenerateTime(new Date()));

        } else {

            // init time
            long lastSuccessTime = liabilityGenerateHistory.getLastSuccessTime() == null ? 0 : liabilityGenerateHistory.getLastSuccessTime().getTime();
            long lastRefreshTime = liabilityGenerateHistory.getLastRefreshTime() == null ? 0 : liabilityGenerateHistory.getLastRefreshTime().getTime();

            //完全没有成功过 或者 最后一次成功时间大于最后一次刷新时间 允许重新生成
            if (0 == lastSuccessTime && 0 == lastRefreshTime || lastSuccessTime > lastRefreshTime) {

                // init table
                this.initTable(tableCode);

                ReportLiabilityGenerateHistory entity = new ReportLiabilityGenerateHistory().setLastRefreshTime(new Date());
                updateReportLiabilityGenerateHistory(tableCode, entity);

            } else {

                // refuse operation
                return false;
            }

        }

        // allow operation
        return true;
    }

    /**
     * update report liability generate history
     * @param tableCode
     * @param entity
     */
    private void updateReportLiabilityGenerateHistory(final String tableCode,
                                                      final ReportLiabilityGenerateHistory entity) {

        final Example example = Example.builder(ReportLiabilityGenerateHistory.class)
                .where(Sqls.custom().andEqualTo(ReportLiabilityGenerateHistory.C_LIABILITY_TABLE_CODE, tableCode))
                .build();

        reportLiabilityGenerateHistoryMapper.updateByConditionSelective(entity, example);
    }

    /**
     * create and truncate table
     * @param tableCode
     */
    private void initTable(String tableCode) {

        if ("NONE".equalsIgnoreCase(tableMode)) {

            reportLiabilityGenerateHistoryMapper.clearTable(tableCode, LIABILITY_SUMMARY_TABLE_TYPE);
            reportLiabilityGenerateHistoryMapper.clearTable(tableCode, LIABILITY_DETAIL_TABLE_TYPE);

        } else {

            try {
                reportLiabilityGenerateHistoryMapper.createLiabilityTable(tableCode, LIABILITY_SUMMARY_TABLE_TYPE);
                reportLiabilityGenerateHistoryMapper.createLiabilityTable(tableCode, LIABILITY_DETAIL_TABLE_TYPE);
            } catch (Exception e) {
                // ignore
            }

            reportLiabilityGenerateHistoryMapper.truncateLiabilityTable(tableCode, LIABILITY_SUMMARY_TABLE_TYPE);
            reportLiabilityGenerateHistoryMapper.truncateLiabilityTable(tableCode, LIABILITY_DETAIL_TABLE_TYPE);

        }

    }


}