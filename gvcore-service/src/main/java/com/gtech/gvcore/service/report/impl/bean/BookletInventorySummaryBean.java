package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/20 14:25
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class BookletInventorySummaryBean {
    /**
     * issuer
     */
    @ExcelProperty(index = 0,value = "Issuer")
    private String issuer;

    /**
     * merchant
     */
    @ExcelProperty(index = 1,value = "Merchant Name")
    private String merchant;

    /**
     * merchantOutletName
     */
    @ExcelProperty(index = 2,value = "Outlet Name")
    private String merchantOutletName;

    /**
     * vpg
     */
    @ExcelProperty(index = 3,value = "Voucher Program Group")
    private String vpg;

    /**
     * vpgType
     */
    @ExcelProperty(index = 4,value = "Voucher Program Group Type")
    private String vpgType;

    @ExcelProperty(index = 5,value = "Booklet Status")
    private String bookletStatus;

    @ExcelProperty(index = 6,value = "Booklet Count", converter = ExportExcelNumberConverter.class)
    private String bookletCount;

    /**
     * cardsCount
     */
    @ExcelProperty(index = 7,value = "Expiry Date")
    private String expiryDate;




}
