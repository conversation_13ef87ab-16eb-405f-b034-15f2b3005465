package com.gtech.gvcore.service.report.impl.support.aging.builder;

import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.dao.model.Company;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.aging.AgeBySbuDetailsOneBean;
import com.gtech.gvcore.service.report.impl.bo.AgingBo;
import com.gtech.gvcore.service.report.impl.bo.AgingVoucherTransactionBoVoucher;
import com.gtech.gvcore.service.report.impl.support.aging.AgingSheet;
import com.gtech.gvcore.service.report.impl.support.aging.AgingSheetBuilder;
import com.gtech.gvcore.service.report.impl.support.aging.bo.AgeBySbuDetailsOneBo;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName AgeBySbuDetailsOneBuilder
 * @Description Age by SBU Details_1
 * <AUTHOR>
 * @Date 2022/11/4 14:43
 * @Version V1.0
 **/
@Component
public class AgeBySbuDetailsOneBuilder implements AgingSheetBuilder {

    private final AddFunction addFunction = getAddFunction();

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.AGING_AGE_BY_SBU_DETAILS_ONE_REPORT;
    }

    @Override
    public AgingSheet builder(final ReportContext context) {

        final List<AgeBySbuDetailsOneBean> beans = new ArrayList<>();

        final AgeBySbuDetailsOneBo allTotalBo = new AgeBySbuDetailsOneBo().setCustomerName("Grand Total");

        //customer group
        //此处group 只是区分数据不进行数据计算
        final Map<String, List<AgeBySbuDetailsOneBo>> resultCustomerMap = this.group(context)
                .stream().collect(Collectors.groupingBy(AgeBySbuDetailsOneBo::getCustomerCode));

        resultCustomerMap.forEach((k, v) -> {

            final String customerName = v.get(0).getCustomerName();
            final AgeBySbuDetailsOneBo customerTotal = new AgeBySbuDetailsOneBo().setCustomerName(customerName + "Total");

            final List<AgeBySbuDetailsOneBean> customerBeans = new ArrayList<>();
            v.forEach(i -> {
                AgeBySbuDetailsOneBo bo = i.setCustomerName(null);
                customerTotal.add(bo);
                allTotalBo.add(bo);
                customerBeans.add(bo.bean());
            });

            customerBeans.add(customerTotal.bean());
            customerBeans.get(0).setCustomerName(customerName);

            beans.addAll(customerBeans);
        });

        beans.add(allTotalBo.bean());

        return new AgingSheet()
                .setHead(null)
                .addSheetData("asd1", exportTypeEnum(), beans)
                .setSheetName(exportTypeEnum().getSheetName());
    }

    /**
     * group data
     * @param context
     * @return
     */
    private Collection<AgeBySbuDetailsOneBo> group(final ReportContext context) {

        final Map<String, AgeBySbuDetailsOneBo> result = new HashMap<>();

        //sales data
        final List<AgingBo> list = context.getCacheList(AgingSheetBuilder.SALES_DATA_KEY);

        //join data
        final JoinDataMap<Customer> customerMap = context.getCacheJoinMap(AgingSheetBuilder.CUSTOMER_MAP_KEY);
        final JoinDataMap<Voucher> voucherMap = context.getCacheJoinMap(AgingSheetBuilder.VOUCHER_MAP_KEY);
        final List<AgingVoucherTransactionBoVoucher> transactionDataList = context.getCacheList(AgingSheetBuilder.REDEEM_DATA_KEY);
        final Map<String, String> sbuMap = getSbuMap(context);
        final Map<String, AgingVoucherTransactionBoVoucher> useTransactionMap = transactionDataList.stream().collect(Collectors.toMap(AgingVoucherTransactionBoVoucher::getVoucherCode, Function.identity() , (a, b) -> b));

        list.stream()
                // filter voucher exist and use
                .filter(e -> useTransactionMap.containsKey(e.getVoucherCode()))
                // filter customer exist
                .filter(e -> customerMap.containsKey(e.getCustomerCode()))
                // filter voucher exist
                .filter(e -> voucherMap.containsKey(e.getVoucherCode()))
                .forEach(e -> {

                    final Voucher voucher = voucherMap.findValue(e.getVoucherCode());
                    final AgingVoucherTransactionBoVoucher useTransactionData = useTransactionMap.get(e.getVoucherCode());

                    //后续需要隐藏从此处入手修改
                    final String customerCode = e.getCustomerCode();
                    final String subName = sbuMap.get(useTransactionData.getMerchantCode());
                    final String key = customerCode + subName;

                    AgeBySbuDetailsOneBo bo = result.computeIfAbsent(key, k -> new AgeBySbuDetailsOneBo()
                            .setCustomerCode(customerCode)
                            .setSbuName(subName)
                            .setCustomerName(customerMap.get(customerCode).getCustomerName()));

                    this.addFunction.add(voucher.getCreateTime(), useTransactionData.getTransactionDate(), bo, voucher.getDenomination());
                });

        return result.values();
    }


    private Map<String, String> getSbuMap(final ReportContext context) {

        final Map<String, String> sbuMap = new HashMap<>();
        final JoinDataMap<Merchant> merchantMap = context.getCacheJoinMap(AgingSheetBuilder.REDEEM_MERCHANT_MAP_KEY);
        final JoinDataMap<Company> companyMap = context.getCacheJoinMap(AgingSheetBuilder.REDEEM_COMPANY_MAP_KEY);

        merchantMap.forEach((k, v) -> sbuMap.put(k, companyMap.findValue(v.getCompanyCode()).getSbu()));
        return sbuMap;
    }

    private AddFunction getAddFunction () {

        final BiConsumer<AgeBySbuDetailsOneBo, BigDecimal> addM30Function    = AgeBySbuDetailsOneBo::addOneValue;
        final BiConsumer<AgeBySbuDetailsOneBo, BigDecimal> addM60Function    = AgeBySbuDetailsOneBo::addTwoValue;
        final BiConsumer<AgeBySbuDetailsOneBo, BigDecimal> addM90Function    = AgeBySbuDetailsOneBo::addThreeValue;
        final BiConsumer<AgeBySbuDetailsOneBo, BigDecimal> addM180Function   = AgeBySbuDetailsOneBo::addFourValue;
        final BiConsumer<AgeBySbuDetailsOneBo, BigDecimal> addM360Function   = AgeBySbuDetailsOneBo::addSixValue;

        //add function
        return (saleTime, useTime, executeObject, value) ->  {

            long time = useTime.getTime() - saleTime.getTime();
            int day   = (int) (time / (24 * 60 * 60 * 1000));

            if      (day <= AddFunction.FunctionMethod.M30.getDay())  addM30Function  .accept(executeObject, value);
            else if (day <= AddFunction.FunctionMethod.M60.getDay())  addM60Function  .accept(executeObject, value);
            else if (day <= AddFunction.FunctionMethod.M90.getDay())  addM90Function  .accept(executeObject, value);
            else if (day <= AddFunction.FunctionMethod.M180.getDay()) addM180Function .accept(executeObject, value);
            else if (day <= AddFunction.FunctionMethod.M360.getDay()) addM360Function .accept(executeObject, value);
        };
    }

    public interface AddFunction {

        enum FunctionMethod {
            M30     (30),
            M60     (60),
            M90     (90),
            M180    (180),
            M360    (360),
            ;
            private final int day;
            FunctionMethod(int day) {
                this.day = day;
            }

            public int getDay() {
                return day;
            }
        }

        void add (Date saleTime, Date useTime, AgeBySbuDetailsOneBo executeObject, BigDecimal value);
    }


    @Override
    public Class<?> getExportDataClass() {
        return AgeBySbuDetailsOneBean.class;
    }
}
