package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Company;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.CancelRedeemDetailedBean;
import com.gtech.gvcore.service.report.impl.bo.CancelRedeemBo;
import com.gtech.gvcore.service.report.impl.param.CancelRedeemQueryData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:58
 * @Description:
 */
@Service
public class CancelRedeemDetailedImpl extends ReportSupport
        implements BusinessReport<CancelRedeemQueryData, CancelRedeemDetailedBean>, SingleReport {

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.CANCEL_REDEEMED_DETAILED_REPORT;
    }

    @Override
    public CancelRedeemQueryData builderQueryParam(final CreateReportRequest reportParam) {

        final CancelRedeemQueryData queryData = new CancelRedeemQueryData();
        queryData.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        queryData.setTransactionDateStart(reportParam.getTransactionDateStart());
        queryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());
        queryData.setMerchantCodeList(reportParam.getMerchantCodes());
        queryData.setOutletCodeList(reportParam.getOutletCodes());
        queryData.setCpgCodeList(reportParam.getCpgCodes());
        queryData.setVoucherCodeNumStart(reportParam.getVoucherCodeNumStart());
        queryData.setVoucherCodeNumEnd(reportParam.getVoucherCodeNumEnd());
        queryData.setInvoiceNo(reportParam.getInvoiceNo());

        return queryData;

    }

    @Override
    public List<CancelRedeemDetailedBean> getExportData(final CancelRedeemQueryData queryData) {

        final Collection<CancelRedeemBo> detailList = getBoList(queryData);
        if (CollectionUtils.isEmpty(detailList)) return Collections.emptyList();

        final JoinDataMap<Merchant> merchantCodeMerchantMap = super.getMapByCode(detailList, CancelRedeemBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Outlet> outletCodeOutletMap = super.getMapByCode(detailList, CancelRedeemBo::getOutletCode, Outlet.class);
        final JoinDataMap<Cpg> cpgCodeCpgMap = super.getMapByCode(detailList, CancelRedeemBo::getCpgCode, Cpg.class);
        final JoinDataMap<Voucher> voucherCodeVoucherMap = super.getMapByCode(detailList, CancelRedeemBo::getVoucherCode, Voucher.class);
        final JoinDataMap<Company> companyCodeCompanyMap = super.getMapByCode(merchantCodeMerchantMap.values(), Merchant::getCompanyCode, Company.class);

        return detailList.stream()
                .map(e -> {
                    final CancelRedeemDetailedBean detail = new CancelRedeemDetailedBean();
                    final Merchant merchant = merchantCodeMerchantMap.findValue(e.getMerchantCode());

                    detail.setVoucherNumber(e.getVoucherCode());
                    detail.setMerchant(merchant.getMerchantName());
                    detail.setOutlet(outletCodeOutletMap.findValue(e.getOutletCode()).getOutletName());
                    detail.setVoucherProgramGroup(cpgCodeCpgMap.findValue(e.getCpgCode()).getCpgName());
                    detail.setExpiryDate(DateUtil.format(voucherCodeVoucherMap.findValue(e.getVoucherCode()).getVoucherEffectiveDate(), DateUtil.FORMAT_YYYYMMDDHHMISS));
                    detail.setTransactionAmount(toAmount(e.getDenomination()));
                    detail.setTransactionDate(DateUtil.format(e.getTransactionDate(), DateUtil.FORMAT_YYYYMMDDHHMISS));
                    detail.setInvoiceNumber(e.getInvoiceNumber());
                    detail.setCorporateName(companyCodeCompanyMap.findValue(merchant.getCompanyCode()).getCompanyName());

                    return detail;
                }).collect(Collectors.toList());
    }


    public Collection<CancelRedeemBo> getBoList(final CancelRedeemQueryData queryData) {

        return Optional.of(PollPageHelper.pollGroupNewTransactionByCodeSelect(this.reportBusinessMapper::selectCancelRedeem, queryData))
                // filter gift voucher cancel redeem
                .map(e -> e.stream().filter(s -> TransactionTypeEnum.GIFT_CARD_BULK_CANCEL_REDEEM.equalsCode(s.getTransactionType())).collect(Collectors.toList()))
                .orElse(Collections.emptyList());

    }

}
