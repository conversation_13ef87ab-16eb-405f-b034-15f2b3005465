package com.gtech.gvcore.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.outletproductcategory.*;
import com.gtech.gvcore.common.response.outletproductcategory.OutletProductCategoryResponse;
import com.gtech.gvcore.dao.mapper.OutletProductCategoryMapper;
import com.gtech.gvcore.dao.model.OutletProductCategory;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.OutletProductCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/21 11:48
 */
@Service
public class OutletProductCategoryServiceImpl implements OutletProductCategoryService {
    @Autowired
    private OutletProductCategoryMapper outletProductCategoryMapper;

    @Autowired
    private GvCodeHelper codeHelper;

    @Override
    public Result<Void> createOutletProductCategory(CreateOutletProductCategoryRequest param) {
        OutletProductCategory entity = BeanCopyUtils.jsonCopyBean(param, OutletProductCategory.class);
        entity.setOutletProductCategoryCode(codeHelper.generateOutletCpgCode());
        entity.setCreateTime(new Date());
        //默认开启
        entity.setStatus(GvcoreConstants.STATUS_ENABLE);
        try {
            outletProductCategoryMapper.insertSelective(entity);
        } catch (DuplicateKeyException e) {
            return Result.failed(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                    ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
        }
        return Result.ok();
    }

    @Override
    public Result<Void> updateOutletProductCategory(UpdateOutletProductCategoryRequest param) {
        OutletProductCategory entity = BeanCopyUtils.jsonCopyBean(param, OutletProductCategory.class);
        entity.setUpdateTime(new Date());

        Example example = new Example(OutletProductCategory.class);
        example.createCriteria()
                .andEqualTo(OutletProductCategory.C_OUTLET_PRODUCT_CATEGORY_CODE,param.getOutletProductCategoryCode());

        try {
            outletProductCategoryMapper.updateByConditionSelective(entity,example);
        } catch (DuplicateKeyException e) {
            return Result.failed(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                    ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
        }
        return Result.ok();
    }

    @Override
    public Result<Void> deleteOutletProductCategory(DeleteOutletProductCategoryRequest param) {
        Example example = new Example(OutletProductCategory.class);
        example.createCriteria()
                .andEqualTo(OutletProductCategory.C_OUTLET_PRODUCT_CATEGORY_CODE,param.getOutletProductCategoryCode());
        outletProductCategoryMapper.deleteByCondition(example);

        return Result.ok();
    }

    @Override
    public PageResult<OutletProductCategoryResponse> queryOutletProductCategoryList(QueryOutletProductCategoryRequest param) {

        PageHelper.startPage(param.getPageNum(),param.getPageSize());

        Example example = new Example(OutletProductCategory.class);
        example.createCriteria()
                .andEqualTo(OutletProductCategory.C_OUTLET_PRODUCT_CATEGORY_CODE,param.getOutletProductCategoryCode())
                .andEqualTo(OutletProductCategory.C_PRODUCT_CATEGORY_CODE,param.getProductCategoryCode())
                .andEqualTo(OutletProductCategory.C_OUTLET_CODE,param.getOutletCode())
                .andEqualTo(OutletProductCategory.C_STATUS,param.getStatus());
        //创建时间倒序
        example.orderBy(OutletProductCategory.C_CREATE_TIME).desc();

        List<OutletProductCategory> gvOutletProductCategoryEntities = outletProductCategoryMapper.selectByCondition(example);
        PageInfo<OutletProductCategory> info = PageInfo.of(gvOutletProductCategoryEntities);

        return new PageResult<>(BeanCopyUtils.jsonCopyList(info.getList(),OutletProductCategoryResponse.class),info.getTotal());
    }

    @Override
    public OutletProductCategoryResponse getOutletProductCategory(GetOutletProductCategoryRequest param) {
        OutletProductCategory entity = BeanCopyUtils.jsonCopyBean(param, OutletProductCategory.class);
        OutletProductCategory outletProductCategory = outletProductCategoryMapper.selectOne(entity);

        return BeanCopyUtils.jsonCopyBean(outletProductCategory,OutletProductCategoryResponse.class);
    }
}
