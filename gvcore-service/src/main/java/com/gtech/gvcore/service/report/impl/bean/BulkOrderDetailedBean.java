package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName BulkOrderDetailedBean
 * @Description BulkOrderDetailedBean
 * <AUTHOR>
 * @Date 2022/7/12 16:54
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class BulkOrderDetailedBean {

    /**
     * Merchant name
     */
    @ExcelProperty(value = "Merchant")
    private String merchant;

    /**
     * Merchant Outlet name
     */
    @ExcelProperty(value = "Merchant Outlet")
    private String merchantOutlet;

    /**
     * Merchant Outlet code(非系统唯一值,指用户在Create Merchant Outlet 页面输入的Outlet Code)
     */
    @ExcelProperty(value = "Merchant Outlet Code")
    private String merchantOutletCode;

    @ExcelProperty(value = "Region")
    private String region;

    /**
     * 公司名称.指用户在 Create Customer 录入的 Corporate(页面为Corporate,落库字段为company_name), ,数据在 gc_customer 表 company_name
     */
    @ExcelProperty(value = "Client Name")
    private String customerName;

    /**
     * 券类型,其值为 gv_customer_order 表中 mop_code(VCR | VCE),描述信息从 dd_lang 获取,值包括:Physical Vouchers | Digital Vouchers
     */
    @ExcelProperty(value = "Voucher Type")
    private String voucherType;

    /**
     * 订单号. gv_customer_order 表 purchase_order_no
     */
    @ExcelProperty(value = "PO Number")
    private String poNumber;

    /**
     * 订单创建时间
     */
    @ExcelProperty(value = "PO Date")
    private String poDate;

    /**
     * 订单状态.其值为 gv_customer_order 表中 status,描述信息从 dd_lang 获取.DD_CODE = 'CUSTOMER_ORDER_STATUS'
     */
    @ExcelProperty(value = "Status")
    private String status;

    /**
     * 该VPG的全部 voucher amount(面额) 和, 直接取gv_customer_order_details 表的voucher_amount
     */
    @ReportAmountValue
    @ExcelProperty(value = "PO Value", converter = ExportExcelNumberConverter.class)
    private String poValue;

    /**
     * 折扣类型,描述值取dd_lang,DD_CODE = 'product_category_discount_type'
     */
    @ExcelProperty(value = "Discount Type")
    private String discountType;

    /**
     * 该vpg的比例总金额, 直接取gv_customer_order_details 表的discount
     */
    @ReportAmountValue
    @ExcelProperty(value = "Discount Amount", converter = ExportExcelNumberConverter.class)
    private String discountAmount;

    /**
     * 折扣百分比, 其值等于gv_customer_order 表 discount.
     */
    @ExcelProperty(value = "Discount")
    private String discount;

    /**
     * 发票号码.gv_customer_order 表 invoice_no
     */
    @ExcelProperty(value = "Invoice Number")
    private String invoiceNumber;

    /**
     * VPG Name
     */
    @ExcelProperty(value = "Order Item")
    private String orderItem;

    /**
     * 客户邮箱 gv_customer_order 表 contact_email
     */
    @ExcelProperty(value = "Email Recipient")
    private String emailRecipient;

    /**
     * Mop 名, 通过 gv_customer_order 表 means_of_payment_code 从 gv_means_of_payment 表 获取 Mop name
     */
    @ExcelProperty(value = "Payment Mode")
    private String paymentMode;

    /**
     * 创建人名称
     */
    @ExcelProperty(value = "Submit Account")
    private String submitAccount;

    /**
     * 该VPG总数量,直接取gv_customer_order_details 表的 voucher_num
     */
    @ExcelProperty(value = "Total Quantity", converter = ExportExcelNumberConverter.class)
    private String totalQuantity;

    /**
     * 该VPG激活过的总面额.使用vpg_code,batch,transaction_type 在 gv_transaction_data 表中定位,从 gv_transaction_data 获取 [激活过的] 券总面额
     */
    @ReportAmountValue
    @ExcelProperty(value = "Activation Amount", converter = ExportExcelNumberConverter.class)
    private String activationAmount;

    /**
     * 该VPG Cancel过的总面额.使用vpg_code,batch,transaction_type 在 gv_transaction_data 表中定位,统计被 [cancel过的] 券的总面额
     */
    @ReportAmountValue
    @ExcelProperty(value = "Cancel Activation", converter = ExportExcelNumberConverter.class)
    private String cancelActivation;

    /**
     * 该VPG激活过的总数量.使用vpg_code,batch,transaction_type 在 gv_transaction_data 表中定位,从 gv_transaction_data 获取 [激活过的] 券数量
     */
    @ExcelProperty(value = "Activation Count", converter = ExportExcelNumberConverter.class)
    private String activationCount;

    /**
     * 根据 gv_customer_order.voucher_batch_code 获取 gv_voucher_batch.status. 如果为成功,填充该字段,值为该VPG的券总数
     */
    @ExcelProperty(value = "Success Count", converter = ExportExcelNumberConverter.class)
    private String successCount;

    /**
     * 根据 gv_customer_order.voucher_batch_code 获取 gv_voucher_batch.status. 如果为失败,填充该字段,值为该VPG的券总数
     */
    @ExcelProperty(value = "Failed Count", converter = ExportExcelNumberConverter.class)
    private String failedCount;

    /**
     * 固定为0
     */
    @ExcelProperty(value = "Skipped Count")
    private String skippedCount;

    /**
     * 该VPG Cancel过的总数量.使用vpg_code,batch,transaction_type 在 gv_transaction_data 表中定位，统计被 [cancel过的] 券的数量
     */
    @ExcelProperty(value = "Cancel Activation Count", converter = ExportExcelNumberConverter.class)
    private String cancelActivationCount;


    @ExcelProperty(value = "Company Name")
    private String companyName;

    /**
     * Total Net Amount = PO Amount - Discount Amount
     */
    @ReportAmountValue
    @ExcelProperty(value = "Total Net Amount", converter = ExportExcelNumberConverter.class)
    private String totalNetAmount;
}
