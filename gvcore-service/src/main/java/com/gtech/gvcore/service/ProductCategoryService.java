package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.productcategory.CalculateDiscountAmountRequest;
import com.gtech.gvcore.common.request.productcategory.CreateOrUpdateProductCategoryCpgRequest;
import com.gtech.gvcore.common.request.productcategory.CreateOrUpdateProductCategoryDisscountRequest;
import com.gtech.gvcore.common.request.productcategory.CreateProductCategoryRequest;
import com.gtech.gvcore.common.request.productcategory.QueryProductCategoryByPageRequest;
import com.gtech.gvcore.common.request.productcategory.QueryProductCategoryCpgRequest;
import com.gtech.gvcore.common.request.productcategory.QueryProductCategoryDisscountRequest;
import com.gtech.gvcore.common.request.productcategory.UpdateProductCategoryRequest;
import com.gtech.gvcore.common.request.productcategory.UpdateProductCategoryStatusRequest;
import com.gtech.gvcore.common.response.productcategory.CreateProductCategoryResponse;
import com.gtech.gvcore.common.response.productcategory.DiscountInfoResponse;
import com.gtech.gvcore.common.response.productcategory.QueryProductCategoryByPageResponse;
import com.gtech.gvcore.common.response.productcategory.QueryProductCategoryCpgResponse;
import com.gtech.gvcore.common.response.productcategory.QueryProductCategoryDisscountResponse;

/**
 * <AUTHOR>
 * @date 2022年2月16日
 */
public interface ProductCategoryService {

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月23日
     */
    Result<CreateProductCategoryResponse> createProductCategory(CreateProductCategoryRequest request);

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月23日
     */
    Result<Void> updateProductCategory(UpdateProductCategoryRequest request);

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月23日
     */
    Result<Void> updateProductCategoryStatus(UpdateProductCategoryStatusRequest request);
    
    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月23日
     */
    PageResult<QueryProductCategoryByPageResponse> queryProductCategoryByPage(
            QueryProductCategoryByPageRequest request);

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月23日
     */
    Result<QueryProductCategoryCpgResponse> queryProductCategoryCpg(QueryProductCategoryCpgRequest request);

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月23日
     */
    Result<Void> createOrUpdateProductCategoryCpg(CreateOrUpdateProductCategoryCpgRequest request);

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月24日
     */
    Result<QueryProductCategoryDisscountResponse> queryProductCategoryDisscount(
            QueryProductCategoryDisscountRequest request);

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月24日
     */
    Result<Void> createOrUpdateProductCategoryDisscount(CreateOrUpdateProductCategoryDisscountRequest request);

	DiscountInfoResponse calculateDiscountAmount(CalculateDiscountAmountRequest request);

}
