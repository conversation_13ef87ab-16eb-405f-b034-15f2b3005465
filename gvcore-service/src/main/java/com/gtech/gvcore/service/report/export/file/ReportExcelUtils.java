package com.gtech.gvcore.service.report.export.file;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.List;

/**
 * @ClassName ReportExcelUtils
 * @Description report excel utils
 * <AUTHOR>
 * @Date 2022/12/30 16:22
 * @Version V1.0
 **/
public class ReportExcelUtils {

    private ReportExcelUtils() {
    }

    /**
     * 获得sheet内的一个Cell对象根据index坐标
     */
    public static Cell getCell(final Sheet sheet, final int rowIndex, final int colIndex) {

        final Row row = getRow(sheet, rowIndex);

        return ReportExcelUtils.getCell(row, colIndex);
    }

    /**
     * 获得sheet内的一个行对象Row 根据index 坐标
     */
    public static Row getRow(final Sheet sheet, final int rowIndex) {

        final Row row = sheet.getRow(rowIndex);

        if (null != row) return row;

        return sheet.createRow(rowIndex);
    }

    /**
     * 获取row 行的某一cell 根据index 坐标
     */
    public static Cell getCell(final Row row, final int colIndex) {

        final Cell cell = row.getCell(colIndex);

        if (null != cell) return cell;

        return row.createCell(colIndex);

    }

    /**
     * 合并sheet 中的行 需要提供起始与结束行坐标 与具体地列坐标
     */
    public static void mergeRow(final Sheet sheet, final int rowBegin, final int rowEnd, final int colIndex) {

        if (rowBegin == rowEnd) return;

        ReportExcelUtils.removeMerge(sheet, colIndex, colIndex, rowBegin, rowEnd);

        final CellRangeAddress cellRangeAddress = new CellRangeAddress(rowBegin, rowEnd, colIndex, colIndex);
        sheet.addMergedRegion(cellRangeAddress);

    }

    /**
     * 合并sheet 中的列 需要提供起始与结束列坐标 与具体地行坐标
     */
    public static void mergeCol(final Sheet sheet, final int colBegin, final int colEnd, final int rowIndex) {

        if (colBegin == colEnd) return;

        ReportExcelUtils.removeMerge(sheet, colBegin, colEnd, rowIndex, rowIndex);

        final CellRangeAddress cellRangeAddress = new CellRangeAddress(rowIndex, rowIndex, colBegin, colEnd);
        sheet.addMergedRegion(cellRangeAddress);

    }

    /**
     * 删除col * row内所有的merge 信息
     * @param sheet sheet 页
     * @param colBegin 列起始
     * @param colEnd 列结束
     * @param rowBegin 行起始
     * @param rowEnd 行结束
     */
    public static void removeMerge(final Sheet sheet, final int colBegin, final int colEnd, final int rowBegin, final int rowEnd) {

        final List<CellRangeAddress> mergeRegions = sheet.getMergedRegions();

        for (int i = 0; i < mergeRegions.size(); i++) {

            final CellRangeAddress cellRangeAddr = mergeRegions.get(i);

            runRemove: for (int r = rowBegin; r <= rowEnd; r++) {//NOSONAR
                for (int c = colBegin; c <= colEnd; c++) {
                    if (cellRangeAddr.isInRange(r, c)) {
                        sheet.removeMergedRegion(i);
                        break runRemove;
                    }
                }
            }

        }

    }

}
