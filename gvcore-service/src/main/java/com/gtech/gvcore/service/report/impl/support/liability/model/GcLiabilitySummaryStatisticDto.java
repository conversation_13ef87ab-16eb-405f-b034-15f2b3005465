package com.gtech.gvcore.service.report.impl.support.liability.model;


import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.common.utils.GvConvertUtils;
import com.gtech.gvcore.dao.model.GcReportTempLiabilitySStructure;
import com.gtech.gvcore.giftcard.domain.model.GcCardStatus;
import com.gtech.gvcore.giftcard.domain.model.GcMgtCardStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

/**
 * @ClassName LiabilitySummaryStatisticDto
 * @Description LiabilitySummaryStatisticDto
 * <AUTHOR>
 * @Date 2023/4/15 10:29
 * @Version V1.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class GcLiabilitySummaryStatisticDto {

    private Date lastMoonTime;

    private String issuerCode;

    private String merchantCode;

    private String cpgCode;

    private Date expiryDate;
    private String outletCode;

    private AtomicReference<BigDecimal> activatedAmount = new AtomicReference<>(BigDecimal.ZERO);

    private AtomicReference<BigDecimal> purchasedAmount = new AtomicReference<>(BigDecimal.ZERO);

    private AtomicReference<BigDecimal> deactivatedAmount = new AtomicReference<>(BigDecimal.ZERO);

    private AtomicReference<BigDecimal> expiredAmount = new AtomicReference<>(BigDecimal.ZERO);

    private AtomicReference<BigDecimal> totalAmount = new AtomicReference<>(BigDecimal.ZERO);

    public static GcLiabilitySummaryStatisticDto convert(final GcLiabilityVoucherBo bo) {

        // init
        final GcLiabilityVoucherMode voucher = GvConvertUtils.toObject(bo.getVoucher(), new GcLiabilityVoucherMode());
        final BigDecimal balance = GvConvertUtils.toBigDecimal(voucher.getBalance(), BigDecimal.ZERO);
        final String status = voucher.getStatus();
        final String managementStatus = voucher.getManagementStatus();
        final Date expiryTime = voucher.getExpiryTime();
        final GcLiabilitySummaryStatisticDto dto = new GcLiabilitySummaryStatisticDto()
                .setIssuerCode(ConvertUtils.toString(voucher.getIssuerCode(), ""))
                .setMerchantCode(ConvertUtils.toString(bo.getMerchantCode(), ""))
                .setCpgCode(voucher.getCpgCode())
                .setLastMoonTime(bo.getLastMoonTime());

        if (expiryTime.before(new Date()))
            dto.expiredAmount.updateAndGet(v -> v.add(balance));
        else if (Objects.equals(managementStatus, GcMgtCardStatus.DISABLE.name()))
            dto.deactivatedAmount.updateAndGet(v -> v.add(balance));
        else if (Objects.equals(status, GcCardStatus.ACTIVATED.name()))
            dto.activatedAmount.updateAndGet(v -> v.add(balance));
        else if (Objects.equals(status, GcCardStatus.PURCHASED.name()))
            dto.purchasedAmount.updateAndGet(v -> v.add(balance));

        dto.totalAmount.updateAndGet(v -> v.add(balance));
        dto.setExpiryDate(expiryTime);
        dto.setOutletCode(voucher.getSalesOutlet());
        return dto;
    }

    public static String getGroupKey(GcLiabilitySummaryStatisticDto bean) {

        return StringUtils.join("_", bean.getIssuerCode(), bean.getMerchantCode(), bean.getCpgCode(), bean.getExpiryDate(), bean.getOutletCode());
    }

    public GcLiabilitySummaryStatisticDto merge(GcLiabilitySummaryStatisticDto bo) {

        this.deactivatedAmount.updateAndGet(v -> v.add(bo.deactivatedAmount.get()));
        this.purchasedAmount.updateAndGet(v -> v.add(bo.purchasedAmount.get()));
        this.activatedAmount.updateAndGet(v -> v.add(bo.activatedAmount.get()));
        this.expiredAmount.updateAndGet(v -> v.add(bo.expiredAmount.get()));
        this.totalAmount.updateAndGet(v -> v.add(bo.getTotalAmount().get()));
        return this;
    }

    public GcReportTempLiabilitySStructure toEntity() {

        return new GcReportTempLiabilitySStructure()
                .setIssuerCode(this.issuerCode)
                .setMerchantCode(this.merchantCode)
                .setCpgCode(this.cpgCode)
                .setActivatedAmount(this.activatedAmount.get())
                .setPurchasedAmount(this.purchasedAmount.get())
                .setDeactivatedAmount(this.deactivatedAmount.get())
                .setExpiredAmount(this.expiredAmount.get())
                .setTotalAmount(this.getTotalAmount().get())
                .setExpiryDate(this.getExpiryDate())
                .setOutletCode(this.getOutletCode());
    }

}
