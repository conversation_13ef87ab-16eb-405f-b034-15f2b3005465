package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.ReportProportionDataFunction;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.GcBalanceCorrectionSummaryBean;
import com.gtech.gvcore.service.report.impl.bo.GcBalanceCorrectionBo;
import com.gtech.gvcore.service.report.impl.param.GcBalanceCorrectionQueryData;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:54
 * @Description:
 */
@Service
public class GcBalanceCorrectionSummaryImpl extends ReportSupport
        implements BusinessReport<GcBalanceCorrectionQueryData, GcBalanceCorrectionSummaryBean>, SingleReport, ReportProportionDataFunction {

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_BALANCE_CORRECTION_REPORT;
    }

    @Override
    public GcBalanceCorrectionQueryData builderQueryParam(final CreateReportRequest reportParam) {

        final GcBalanceCorrectionQueryData queryData = new GcBalanceCorrectionQueryData();
        queryData.setTransactionDateStart(reportParam.getTransactionDateStart());
        queryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());
        queryData.setCpgCodeList(reportParam.getCpgCodes());
        queryData.setOutletCodeList(reportParam.getOutletCodes());
        queryData.setTransactionType(reportParam.getTransactionTypes());
        queryData.setMerchantCodeList(reportParam.getMerchantCodes());
        queryData.setCustomerCodeList(reportParam.getCustomerCodes());
        if (StringUtils.isNotBlank(reportParam.getVoucherCode())) {
            queryData.setCardNumbers((Arrays.asList(reportParam.getVoucherCode().split(","))));
        }
        return queryData;
    }

    @Override
    public List<GcBalanceCorrectionSummaryBean> getExportData(final GcBalanceCorrectionQueryData queryData) {
        List<GcBalanceCorrectionBo> list = new ArrayList<>();
        PollPageHelper.pollSelect(gcReportBusinessMapper::selectBalanceCorrection, queryData, list::addAll);
        Collection<CorrectionSummaryBo> collection = list.stream()
                .map(CorrectionSummaryBo::convert)
                .collect(Collectors.toMap(
                                // key
                                CorrectionSummaryBo::getGroupKey,
                                // value
                                CorrectionSummaryBo::newInstance,
                                // merge
                                CorrectionSummaryBo::merge)
                        //to map
                ).values();
        if (CollectionUtils.isEmpty(collection)) return Collections.emptyList();
        final JoinDataMap<GcCpg> cpgMap = super.getMapByCode(collection, CorrectionSummaryBo::getCpgCode, GcCpg.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(collection, CorrectionSummaryBo::getMerchant, Merchant.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(collection, CorrectionSummaryBo::getOutlet, Outlet.class);
        //convert result
        List<GcBalanceCorrectionSummaryBean> collect = collection.stream()
                .map(e -> {
                    GcBalanceCorrectionSummaryBean bean = new GcBalanceCorrectionSummaryBean();
                    bean.setTotalAmount(toAmount(ConvertUtils.toBigDecimal(e.getTotalAmount(), BigDecimal.ZERO)));
                    bean.setCpgCode(cpgMap.findValue(e.getCpgCode()).getCpgName());
                    bean.setTransactionDate(DateUtil.format(e.getTransactionDate(), DateUtil.FORMAT_YYYYMMDDHHMISS));
                    bean.setTransactionType(e.getTransactionType());
                    bean.setTotalCards(e.getTotalCards());
                    bean.setMerchant(merchantMap.findValue(e.getMerchant()).getMerchantName());
                    bean.setOutlet(outletMap.findValue(e.getOutlet()).getOutletName());
                    bean.setNotes(e.getNotes());
                    return bean;
                })
                .collect(Collectors.toList());
        GcBalanceCorrectionSummaryBean bean = new GcBalanceCorrectionSummaryBean();
        bean.setMerchant("Total");
        bean.setTotalCards(collect.stream().map(GcBalanceCorrectionSummaryBean::getTotalCards).reduce(0, Integer::sum));
        bean.setTotalAmount(collect.stream().map(x -> new BigDecimal(x.getTotalAmount())).reduce(BigDecimal.ZERO, BigDecimal::add).toString());
        collect.add(bean);
        return collect;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class CorrectionSummaryBo {
        private String issueHandlingCode;
        private String merchant;
        private String outlet;
        private Date transactionDate;
        private String cpgCode;
        private Integer totalCards = 1;
        private BigDecimal totalAmount = BigDecimal.ZERO;
        private String transactionType;
        private String notes;


        public static CorrectionSummaryBo convert(GcBalanceCorrectionBo bo) {
            return new CorrectionSummaryBo()
                    .setCpgCode(bo.getCpgCode())
                    .setMerchant(bo.getMerchantCode())
                    .setOutlet(bo.getOutletCode())
                    .setTransactionDate(bo.getCreateTime())
                    .setTransactionType(bo.getTransactionType())
                    .setTotalAmount(bo.getCorrectionBalance())
                    .setNotes(bo.getNotes());
        }

        public static String getGroupKey(CorrectionSummaryBo bean) {

            return StringUtils.join("_", bean.getMerchant(), bean.getCpgCode(), bean.getOutlet(), bean.getTransactionType(), bean.getTransactionDate(), bean.getNotes());
        }

        public static CorrectionSummaryBo newInstance(CorrectionSummaryBo bo) {
            return new CorrectionSummaryBo()
                    .setCpgCode(bo.getCpgCode())
                    .setMerchant(bo.getMerchant())
                    .setOutlet(bo.getOutlet())
                    .setTotalAmount(bo.getTotalAmount())
                    .setTransactionType(bo.getTransactionType())
                    .setTransactionDate(bo.getTransactionDate())
                    .setIssueHandlingCode(bo.getIssueHandlingCode())
                    .setNotes(bo.getNotes());
        }

        public CorrectionSummaryBo merge(CorrectionSummaryBo bo) {
            totalCards += ConvertUtils.toInteger(bo.getTotalCards(), 0);
            totalAmount = totalAmount.add(bo.getTotalAmount());
            return this;
        }
    }

}
