package com.gtech.gvcore.service.report.impl.param;

import com.gtech.gvcore.service.report.ReportQueryParam;
import com.gtech.gvcore.service.report.extend.row.TransactionDataPageParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * @ClassName Cancel Sales Query Data
 * @Description Cancel Sales Query Data
 * <AUTHOR>
 * @Date 2023/4/14 15:34
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GcCancelSalesQueryData extends TransactionDataPageParam implements ReportQueryParam {

    //transaction date start 交易时间 开始
    private Date transactionDateStart;

    //transaction date end 交易时间 结束
    private Date transactionDateEnd;

    //issuer code
    private String issuerCode;

    //merchant code list
    private List<String> merchantCodeList;

    //outlet code list
    private List<String> outletCodeList;

    //cpg code list
    private List<String> cpgCodeList;

    //invoice number
    private String invoiceNumber;

    private String purchaseOrderNo;

    private List<String> voucherCode;


    private List<String> customerCodeList;
}
