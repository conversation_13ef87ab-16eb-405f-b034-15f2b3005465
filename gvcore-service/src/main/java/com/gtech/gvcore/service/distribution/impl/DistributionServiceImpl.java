package com.gtech.gvcore.service.distribution.impl;

import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSONObject;
import com.gtech.commons.redis.GTechRedisTemplate;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.DistributionItemVoucherStatusEnum;
import com.gtech.gvcore.common.enums.DistributionProgressStatusEnum;
import com.gtech.gvcore.common.enums.DistributionStatusEnum;
import com.gtech.gvcore.common.enums.DistributionTypeEnum;
import com.gtech.gvcore.common.enums.MessageEnventEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.enums.VoucherStatusEnum;
import com.gtech.gvcore.common.request.distribution.ConfirmDistributionRequest;
import com.gtech.gvcore.common.request.distribution.DistributionItemBean;
import com.gtech.gvcore.common.request.distribution.QueryCustomerDistributionRequest;
import com.gtech.gvcore.common.request.distribution.QueryDistributionDetailRequest;
import com.gtech.gvcore.common.request.distribution.SaveDistributionRequest;
import com.gtech.gvcore.common.request.merchant.GetMerchantRequest;
import com.gtech.gvcore.common.request.outlet.GetOutletRequest;
import com.gtech.gvcore.common.response.distribution.CustomerCpgEffectiveDateResult;
import com.gtech.gvcore.common.response.distribution.GetCustomerDistributionResult;
import com.gtech.gvcore.common.response.distribution.GetDistributionItemResult;
import com.gtech.gvcore.common.response.distribution.QueryCustomerDistributionResult;
import com.gtech.gvcore.common.response.distribution.QueryDistributionDetailResult;
import com.gtech.gvcore.common.response.merchant.MerchantResponse;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.common.utils.AmountUtils;
import com.gtech.gvcore.common.utils.ExceptionBuilder;
import com.gtech.gvcore.common.utils.GvConvertUtils;
import com.gtech.gvcore.dao.dto.CountStatusByItemCodeDto;
import com.gtech.gvcore.dao.dto.DistributionRecipientInfoDto;
import com.gtech.gvcore.dao.mapper.DistributionItemMapper;
import com.gtech.gvcore.dao.mapper.DistributionItemVoucherMapper;
import com.gtech.gvcore.dao.mapper.DistributionMapper;
import com.gtech.gvcore.dao.mapper.DistributionResendMapper;
import com.gtech.gvcore.dao.mapper.TransactionDataMapper;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Distribution;
import com.gtech.gvcore.dao.model.DistributionItem;
import com.gtech.gvcore.dao.model.DistributionItemVoucher;
import com.gtech.gvcore.dao.model.DistributionResend;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.dto.DistributionDetail;
import com.gtech.gvcore.dto.DistributionEmailData;
import com.gtech.gvcore.dto.DistributionEmailDataModel;
import com.gtech.gvcore.dto.ResponseMessageBean;
import com.gtech.gvcore.dto.SubmittedDistributionDetail;
import com.gtech.gvcore.helper.FileHelper;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.helper.GvExcelHelper;
import com.gtech.gvcore.helper.GvHtmlHelper;
import com.gtech.gvcore.helper.OssHelper;
import com.gtech.gvcore.service.CpgService;
import com.gtech.gvcore.service.MerchantService;
import com.gtech.gvcore.service.OutletService;
import com.gtech.gvcore.service.VoucherService;
import com.gtech.gvcore.service.distribution.DistributionEmailTemplateService;
import com.gtech.gvcore.service.distribution.DistributionService;
import com.gtech.gvcore.service.impl.MessageComponent;
import com.gtech.message.dao.RequestRecordMapper;
import com.gtech.message.model.RequestRecord;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Queue;
import java.util.Set;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @ClassName DistributionServiceImpl
 * @Description 分发核心逻辑概览
 * web交互:
 * 1.用户点击确认分发按钮
 * 2.使用customer_code尝试加锁
 * ---2.1. 失败-1s内重试,否则提示系统繁忙稍后再试
 * ---2.2. 更新voucher物流状态为分发中
 * --------2.2.1. 计算各个cpg需要的数量: cpg_sum
 * --------2.2.2. 从数据库获取cpg_sum条指定cpg的voucher: voucher_list,查询条件至少包含: customer_code,cpg_code,expiry_date,voucher_status==1
 * --------2.2.3. 检查voucher_list.size()是否等于cpg_num.
 * ---------------******* 不相等: 抛出异常,说明库存不够
 * ---------------******* 相等: 更新voucher_list的circulation_status->VOUCHER_DISTRIBUTING
 * ---------------******* 相等: 更新gv_distribution分发状态为待分发
 * 3. 释放锁
 * 4. 由controller层调度异步分发逻辑,之所以在controller层是为了确保service层事务提交完成
 * 后端异步分发逻辑:
 * 1.controller调用时传入distribution_code
 * 2.使用distribution_code加锁
 * ---2.1. 加锁失败,跳出
 * ---2.2. 加锁成功,执行分发步骤
 * --------2.2.1 begin 事务
 * --------2.2.2 更新distribution分发状态为分发中
 * --------2.2.3 创建gv_distribution_item_voucher,标记为分发中
 * --------2.2.4 commit 事务
 * --------2.2.5 遍历gv_distribution_item_voucher,调用邮件,获取message_id,将message_id更新到gv_distribution_item_voucher,(这一步不需要事务,注意区分相同的邮件地址不应该合并发送邮件)
 * 3. 释放锁
 * -------------------第二部分-----------------------
 * 1.定时任务获取distribution状态为分发中的数据
 * 2.使用distribution_code加锁
 * 3.查询distribution下gv_distribution_item_voucher包含message_id的记录
 * 4.使用message_id查询消息状态(t_msg_request_record)
 * ---4.1 半小时内无数据 - 忽略
 * ---4.3 半小时外无有数据 - 修改分发状态为FAIL,错误信息为邮件发送失败(或超时)
 * ---4.2 查询时有数据 - 根据status得到结果,如果成功,修改分发状态为已分发,否则更新为FAIL
 * 5.检查distribution所有gv_distribution_item_voucher是否成功
 * ---5.1 半小时内未全部成功 - 忽略
 * ---5.3 超过小时内未全部成功 - 更新distribution等gv_distribution_item_voucher以外的带分发状态的数据为FAIL
 * ---5.2 查询时全部成功 - 更新distribution等全部带分发状态的数据为已分发
 * 6. 释放锁
 * <AUTHOR>
 * @Date 2022/8/9 10:57
 * @Version V1.0
 **/
@Slf4j
@Service
public class DistributionServiceImpl implements DistributionService {

    /**
     * 收件人文本格式正则
     */
    private static final Pattern RECIPIENT_PATTERN = Pattern.compile("([a-zA-Z ]+?<\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*>(\\s)*?;(\\s)*?)+?");

    /**
     * 收件人提取正则
     */
    private static final Pattern RECIPIENT_EXTRACT_PATTERN = Pattern.compile("[a-zA-Z][a-zA-Z ]+?<\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*>?");

    /**
     * 电子邮箱地址正则
     */
    private static final Pattern E_MAIL_PATTERN = Pattern.compile("\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*");

    /**
     * 收件人名称正则,注意,使用该正则获取收件人名称后需要手动去除'<'
     */
    private static final Pattern END_CUSTOMER_NAME_PATTERN = Pattern.compile("[a-zA-Z ]+?<");

    @Autowired
    private DistributionMapper distributionMapper;

    @Autowired
    private DistributionItemMapper distributionItemMapper;

    @Autowired
    private DistributionItemVoucherMapper distributionItemVoucherMapper;

    @Autowired
    private DistributionResendMapper distributionResendMapper;

    @Autowired
    private DistributionEmailTemplateService distributionEmailTemplateService;

    @Autowired
    private GvCodeHelper codeHelper;

    @Autowired
    private CpgService cpgService;

    @Autowired
    private GTechRedisTemplate redisTemplate;

    @Autowired
    private VoucherService voucherService;

    @Autowired
    private MessageComponent messageComponent;

    @Autowired
    private RequestRecordMapper requestRecordMapper;

    @Autowired
    private TransactionDataMapper transactionDataMapper;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private OutletService outletService;

    /**
     * 每次刷新时获取的最大条数
     */
    @Value("${gvcore.distribution.numberOfRefreshes:100}")
    private Integer numberOfRefreshes;

    @Override
    @Transactional
    public String saveDistribution(final SaveDistributionRequest request) {

        // 校验业务参数
        this.checkDistributionRequest(request);

        // 持久化
        return this.save(request);
    }

    private String save(final SaveDistributionRequest request) {

        if (StringUtils.isBlank(request.getDistributionCode())) {
            // 新增时,创建编码
            request.setDistributionCode(this.codeHelper.generateDistributionCode());
        } else {
            // 更新时,删除历史数据
            this.deleteDistribution(request.getDistributionCode(), request.getCustomerCode());
        }

        // 分发主体部分
        final Distribution distribution = this.buildDistribution(request);
        this.distributionMapper.insertSelective(distribution);

        // 分发item
        final List<DistributionItem> distributionItemList = this.buildDistributionItem(request);
        distributionItemList.forEach(this.distributionItemMapper::insertSelective);

        // 日志由bff完成

        return request.getDistributionCode();
    }

    private List<DistributionItem> buildDistributionItem(final SaveDistributionRequest request) {

        // 查询cpg信息
        final Map<String, Cpg> cpgCodeCpgMap = this.cpgService.queryCpgMapByCpgCodeList(request.getItemList().stream().map(DistributionItemBean::getCpgCode).distinct().collect(Collectors.toList()));

        final List<DistributionItemBean> itemList = request.getItemList();

        return itemList.stream().map(item -> {
                    final Cpg cpg = cpgCodeCpgMap.get(item.getCpgCode());
                    if (null == cpg) {
                        throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.NO_CPG_DATA_FOUND);
                    }
                    final DistributionItem distributionItem = new DistributionItem();
                    int recipientNumber = item.getRecipients().split(";").length;
                    distributionItem.setDistributionItemCode(this.codeHelper.generateDistributionItemCode())
                            .setDistributionCode(request.getDistributionCode())
                            .setCustomerCode(request.getCustomerCode())
                            .setCpgCode(item.getCpgCode())
                            .setCpgNameSnapshot(cpg.getCpgName())
                            .setCpgExpiryDateSnapshot(DateUtil.parseDate(item.getExpiryDate(), DateUtil.FORMAT_YYYYMMDDHHMISS))
                            .setVouchersPerEmail(item.getVouchersPerEmail())
                            .setRecipients(item.getRecipients())
                            .setRecipientsNum(recipientNumber)
                            // 价值 = CPG面额 * 收件人数 * 每封邮件券数量
                            .setVoucherAmount(cpg.getDenomination().multiply(new BigDecimal(recipientNumber)).multiply(new BigDecimal(item.getVouchersPerEmail())))
                            .setCreateUser(request.getUserCode());

                    return distributionItem;
                })
                .collect(Collectors.toList());
    }

    private Distribution buildDistribution(final SaveDistributionRequest request) {

        final Distribution distribution = new Distribution();
        distribution.setDistributionCode(request.getDistributionCode())
                .setCustomerCode(request.getCustomerCode())
                .setDistributionType(request.getDistributionType())
                .setEmailTemplateCode(request.getEmailTemplateCode())
                .setEmailSubject(request.getEmailSubject())
                .setEmailRichText(request.getEmailRichText())
                .setStatus(DistributionStatusEnum.DRAFT.code())
                // 初始化必须为UNCONFIRMED
                .setProgressStatus(DistributionProgressStatusEnum.UNCONFIRMED.code())
                .setCreateUser(request.getUserCode())
                .setUpdateUser(request.getUserCode());

        return distribution;
    }

    /**
     * 解析收件人信息.重复信息不会被合并
     *
     * @param request 分发信息
     * @return java.util.List<com.gtech.gvcore.dao.dto.DistributionRecipientInfoDto> 解析得到的收件人信息
     * <AUTHOR>
     * @date 2022/8/10 13:47
     * @since 1.0.0
     */
    private List<DistributionRecipientInfoDto> analyzeRecipient(final SaveDistributionRequest request) {

        final List<DistributionRecipientInfoDto> recipientInfoDtoList = new ArrayList<>();

        for (DistributionItemBean itemRequest : request.getItemList()) {

            recipientInfoDtoList.addAll(this.analyzeRecipient(itemRequest.getRecipients(), request.getCustomerCode()));
        }

        return recipientInfoDtoList;
    }

    private List<DistributionRecipientInfoDto> analyzeRecipient(final String recipients, final String customerCode) {

        final List<DistributionRecipientInfoDto> recipientInfoDtoList = new ArrayList<>();

        final Matcher extractMatcher = DistributionServiceImpl.RECIPIENT_EXTRACT_PATTERN.matcher(recipients);

        while (extractMatcher.find()) {
            final String recipient = extractMatcher.group();

            final Matcher eMailMatcher = DistributionServiceImpl.E_MAIL_PATTERN.matcher(recipient);
            if (eMailMatcher.find()) {
                final Matcher endCustomerNameMatcher = DistributionServiceImpl.END_CUSTOMER_NAME_PATTERN.matcher(recipient);
                if (endCustomerNameMatcher.find()) {
                    final String emailAddress = eMailMatcher.group().trim();
                    // END_CUSTOMER_NAME_PATTERN 解析的endCustomerName包含一个<,需要手动去除
                    final String endCustomerName = endCustomerNameMatcher.group().replace("<", "").trim();
                    final DistributionRecipientInfoDto recipientInfo = new DistributionRecipientInfoDto();
                    recipientInfo.setEmailAddress(emailAddress);
                    recipientInfo.setCustomerCode(customerCode);
                    recipientInfo.setEndCustomerName(endCustomerName);
                    recipientInfoDtoList.add(recipientInfo);
                }
            }
        }

        return recipientInfoDtoList;
    }

    private void deleteDistribution(final String distributionCode, final String customerCode) {

        if (StringUtils.isBlank(distributionCode) || StringUtils.isBlank(customerCode)) {
            return;
        }

        this.distributionMapper.delete(new Distribution().setDistributionCode(GvConvertUtils.toString(distributionCode, "")).setCustomerCode(GvConvertUtils.toString(customerCode, "")));
        this.distributionItemMapper.delete(new DistributionItem().setDistributionCode(GvConvertUtils.toString(distributionCode, "")).setCustomerCode(GvConvertUtils.toString(customerCode, "")));
    }

    private void checkDistributionRequest(final SaveDistributionRequest request) {

        // 收件人校验
        this.checkRecipients(request);

        // 验证邮件信息
        this.validateEmailContent(request);
    }

    private void validateEmailContent(final SaveDistributionRequest request) {

        this.distributionEmailTemplateService.validateEmailContent(request.getEmailSubject(), request.getEmailRichText());

    }

    private void checkRecipients(final SaveDistributionRequest request) {
        // 根据不同distributionType,对邮箱地址的校验规则不同
        // Individual: 至少录入1个及以上邮箱，且邮箱不允许重复，正则校验格式.格式：endCustomerName <<EMAIL>>;
        // Bulk: 邮箱允许重复，正则校验格式.重复邮箱接收券的数量不允许合并发送
        // 注意,必须先验证格式正确,才验证是否重复

        // 格式校验
        final List<Boolean> recipientsValidateList = request.getItemList().stream()
                .map(item -> DistributionServiceImpl.RECIPIENT_PATTERN.matcher(item.getRecipients()).matches())
                .distinct()
                .collect(Collectors.toList());

        if (1 != recipientsValidateList.size() || Boolean.FALSE.equals(recipientsValidateList.get(0))) {
            throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.DISTRIBUTION_RECIPIENT_IS_MALFORMED);
        }

        // 邮箱重复校验
        if (DistributionTypeEnum.INDIVIDUAL.equalsCode(request.getDistributionType())) {
            // 至少录入1个及以上邮箱，且邮箱不允许重复，正则校验格式
            final List<String> allEmailAddressList = this.analyzeRecipient(request).stream().map(DistributionRecipientInfoDto::getEmailAddress).collect(Collectors.toList());
            final Set<String> deduplicatedEmailAddressSet = new HashSet<>(allEmailAddressList);
            if (allEmailAddressList.size() != deduplicatedEmailAddressSet.size()) {
                throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.DISTRIBUTION_DUPLICATE_RECIPIENT);
            }
        }
    }

    @Override
    @Transactional
    public void confirmDistribution(final ConfirmDistributionRequest request) {

        final ConfirmDistributionLock confirmDistributionLock = new ConfirmDistributionLock(this.redisTemplate);
        if (!confirmDistributionLock.tryLock(request.getCustomerCode(), 60 * 1000L, 30 * 60 * 1000L)) {
            throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.DISTRIBUTION_OPERATION_BUSY);
        }

        try {
            /* ---2.2. 更新voucher物流状态为分发中
             * --------2.2.1. 计算各个cpg需要的数量: cpg_sum
             * --------2.2.2. 从数据库获取cpg_sum条指定cpg的voucher: voucher_list,查询条件至少包含: customer_code,cpg_code,expiry_date,voucher_status==1
             * --------2.2.3. 检查voucher_list.size()是否等于cpg_num.
             * ---------------******* 不相等: 抛出异常,说明库存不够
             * ---------------******* 相等: 更新voucher_list的circulation_status->VOUCHER_DISTRIBUTING
             * ---------------******* 相等: 更新gv_distribution分发状态为待分发
             */
            final DistributionDetail draftDistributionDetail = this.findAndValidateDraftDistributionDetail(request.getCustomerCode(), request.getDistributionCode());

            // 计算各个cpg-expiry_date需要的数量
            final Map<CpgExpiryDateInfo, Integer> cpgExpiryDateRequiredQuantity = draftDistributionDetail.getItemList().stream()
                    // save接口保证getCpgExpiryDateSnapshot() >= 1
                    .collect(Collectors.groupingBy(item -> new CpgExpiryDateInfo(item.getCpgCode(), item.getCpgExpiryDateSnapshot()), Collectors.summingInt(e -> e.getVouchersPerEmail() * e.getRecipientsNum())));

            final Map<CpgExpiryDateInfo, Queue<Voucher>> cpgExpiryDateVoucherMap = new HashMap<>();

            for (Map.Entry<CpgExpiryDateInfo, Integer> entry : cpgExpiryDateRequiredQuantity.entrySet()) {
                final CpgExpiryDateInfo expiryDateInfo = entry.getKey();
                final int requiredQuantity = entry.getValue();

                final Set<Voucher> distributionAbleVoucherSet = this.voucherService.queryDistributableVoucher(expiryDateInfo.getCpgCode(), expiryDateInfo.getExpiryDate(), request.getCustomerCode(), requiredQuantity);
                if (distributionAbleVoucherSet.size() != requiredQuantity) {
                    throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.DISTRIBUTION_INSUFFICIENT_STOCK, expiryDateInfo.getCpgCode());
                }
                // 更新voucher的circulation_status为VOUCHER_DISTRIBUTING
                final Set<String> voucherCodeSet = distributionAbleVoucherSet.stream().map(Voucher::getVoucherCode).collect(Collectors.toSet());
                this.voucherService.ready2Distribute(voucherCodeSet, requiredQuantity, request.getCustomerCode(), request.getUserCode());

                cpgExpiryDateVoucherMap.put(expiryDateInfo, new LinkedList<>(distributionAbleVoucherSet));
            }

            for (DistributionItem distributionItem : draftDistributionDetail.getItemList()) {

                final List<DistributionItemVoucher> itemVoucherList = new ArrayList<>();

                final Queue<Voucher> voucherQueue = cpgExpiryDateVoucherMap.get(new CpgExpiryDateInfo(distributionItem.getCpgCode(), distributionItem.getCpgExpiryDateSnapshot()));

                for (int i = 0; i < distributionItem.getVouchersPerEmail(); i++) {
                    final List<DistributionRecipientInfoDto> recipientInfoList = this.analyzeRecipient(distributionItem.getRecipients(), request.getCustomerCode());
                    recipientInfoList.forEach(recipientInfo -> itemVoucherList.add(this.buildItemVoucher(request.getCustomerCode(), distributionItem, recipientInfo, voucherQueue.poll())));
                }

                this.distributionItemVoucherMapper.insertList(itemVoucherList);
            }

            // 更新distribution分发进行状态为待分发
            this.ready2Distribute(draftDistributionDetail.getDistribution().getDistributionCode(), request.getUserCode());

        } finally {
            confirmDistributionLock.unLock(request.getCustomerCode());
        }
    }

    private DistributionItemVoucher buildItemVoucher(final String customerCode, final DistributionItem distributionItem, final DistributionRecipientInfoDto recipientInfo, final Voucher voucher) {

        if (null == voucher) throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.VOUCHER_INVENTORY_SHORTAGE);

        final DistributionItemVoucher itemVoucher = new DistributionItemVoucher();
        final Date createTime = new Date();
        itemVoucher.setDistributionCode(distributionItem.getDistributionCode())
                .setDistributionItemCode(distributionItem.getDistributionItemCode())
                .setCustomerCode(customerCode)
                .setCpgCode(voucher.getCpgCode())
                .setVoucherCode(voucher.getVoucherCode())
                .setVoucherNumber(voucher.getVoucherCodeNum())
                .setEmailAddress(recipientInfo.getEmailAddress())
                .setEmailEndCustomerName(recipientInfo.getEndCustomerName())
                .setStatus(DistributionItemVoucherStatusEnum.AVAILABLE.code())
                .setErrorMsg(null)
                .setCreateTime(createTime)
                .setUpdateTime(createTime);
        return itemVoucher;
    }

    /**
     * 查询分发草稿详情
     *
     * @param customerCode     客户编码
     * @param distributionCode 分发编码
     * @return com.gtech.gvcore.dto.DraftDistributionDetail 分发信息详情
     * <AUTHOR>
     * @date 2022/8/11 16:00
     * @since 1.0.0
     */
    private DistributionDetail findAndValidateDraftDistributionDetail(final String customerCode, final String distributionCode) {

        final Distribution distribution = this.findDraftDistribution(customerCode, distributionCode);
        if (null == distribution) {
            throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.DISTRIBUTION_CONFIRM_ERROR);
        }

        final List<DistributionItem> itemList = this.queryDistributionItem(distributionCode);

        final DistributionDetail draftDistributionDetail = new DistributionDetail();
        draftDistributionDetail.setDistribution(distribution);
        draftDistributionDetail.setItemList(itemList);

        return draftDistributionDetail;
    }

    /**
     * 准备分发.
     *
     * @param distributionCode 分发编码
     * @param updateUserCode   操作人
     * @return com.gtech.gvcore.dto.DraftDistributionDetail 最新状态的分发信息,和入参draftDistributionDetail不是同一个实例
     * <AUTHOR>
     * @date 2022/8/11 16:21
     * @since 1.0.0
     */
    private void ready2Distribute(final String distributionCode, final String updateUserCode) {

        if (StringUtils.isBlank(distributionCode)) {
            throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.PARAMTER_ERROR);
        }

        final Example updateExample = new Example(Distribution.class);
        updateExample.createCriteria()
                .andEqualTo(Distribution.C_DISTRIBUTION_CODE, ConvertUtils.toString(distributionCode, ""))
                .andEqualTo(Distribution.C_STATUS, DistributionStatusEnum.DRAFT.code())
                .andEqualTo(Distribution.C_PROGRESS_STATUS, DistributionProgressStatusEnum.UNCONFIRMED.code());

        final Distribution updateDistribution = new Distribution();
        updateDistribution
                // 面向用户的分发状态变更为诶分发中
                .setStatus(DistributionStatusEnum.DISTRIBUTING.code())
                // 面向程序的分发执行状态变更为已提交,用于程序执行识别
                .setProgressStatus(DistributionProgressStatusEnum.SUBMITTED.code())
                .setConfirmDistributionTime(new Date())
                .setUpdateUser(updateUserCode);

        final int updateRow = this.distributionMapper.updateByConditionSelective(updateDistribution, updateExample);
        if (updateRow != 1) {
            throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.DISTRIBUTION_STATUS_NOT_MATCH);
        }
    }

    /**
     * @ClassName ConfirmDistributionLock
     * @Description 确认分发使用的不可重入的锁
     * <AUTHOR>
     * @Date 2022/8/9 10:57
     * @Version V1.0
     **/
    public static class ConfirmDistributionLock {

        private static final String CONFIRM_DISTRIBUTION_LOCK_KEY_TEMPLATE = "Distribution:Confirm:Distribution:Lock:Key:%s";

        private final GTechRedisTemplate redisTemplate;

        public ConfirmDistributionLock(final GTechRedisTemplate redisTemplate) {
            this.redisTemplate = Objects.requireNonNull(redisTemplate);
        }

        /**
         * 获取锁
         *
         * @param lock        锁key
         * @param waiteTime   尝试获取锁的时间(ms),如果尝试获取锁失败,将在waiteTime时间范围内重试
         * @param lockTimeout 锁的超时时间(ms),如果获取锁成功,将在未来lockTimeout时间内保持锁的持有.除非手动释放锁
         * @return boolean true-获取成功
         * <AUTHOR>
         * @date 2022/8/11 11:25
         * @since 1.0.0
         */
        public boolean tryLock(final String lock, final long waiteTime, final long lockTimeout) {

            if (StringUtils.isBlank(lock) || waiteTime < 0L || lockTimeout < 0L) {
                throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.PARAMTER_ERROR);
            }

            final long startTimestamp = System.currentTimeMillis();

            do {
                if (Boolean.TRUE.equals(this.redisTemplate.opsValueSetIfAbsent(GvcoreConstants.APP_CODE, this.buildLockKey(lock), 1, lockTimeout))) {
                    return true;
                }
            } while (System.currentTimeMillis() - startTimestamp <= waiteTime);

            return false;
        }

        public void unLock(final String lock) {
            if (StringUtils.isBlank(lock)) {
                throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.PARAMTER_ERROR);
            }
            this.redisTemplate.delete(GvcoreConstants.APP_CODE, this.buildLockKey(lock));
        }

        private String buildLockKey(final String lock) {
            return String.format(ConfirmDistributionLock.CONFIRM_DISTRIBUTION_LOCK_KEY_TEMPLATE, lock);
        }
    }

    /**
     * 查询分发草稿
     *
     * @param customerCode     客户编码
     * @param distributionCode 分发编码
     * @return com.gtech.gvcore.dao.model.Distribution 分发信息
     * <AUTHOR>
     * @date 2022/8/11 15:57
     * @since 1.0.0
     */
    private Distribution findDraftDistribution(final String customerCode, final String distributionCode) {

        final Distribution findCondition = new Distribution();
        findCondition.setCustomerCode(ConvertUtils.toString(customerCode, ""))
                .setDistributionCode(ConvertUtils.toString(distributionCode, ""))
                .setStatus(DistributionStatusEnum.DRAFT.code())
                .setProgressStatus(DistributionProgressStatusEnum.UNCONFIRMED.code());

        return this.distributionMapper.selectOne(findCondition);
    }

    /**
     * 根据分发编码查询分发项
     *
     * @param distributionCode 分发编码
     * @return java.util.List<com.gtech.gvcore.dao.model.DistributionItem> 分发项集
     * <AUTHOR>
     * @date 2022/8/11 15:58
     * @since 1.0.0
     */
    private List<DistributionItem> queryDistributionItem(final String distributionCode) {

        if (StringUtils.isBlank(distributionCode)) {
            return Collections.emptyList();
        }

        final DistributionItem queryCondition = new DistributionItem();
        queryCondition.setDistributionCode(ConvertUtils.toString(distributionCode, ""));

        return this.distributionItemMapper.select(queryCondition);
    }

    @Getter
    public static class CpgExpiryDateInfo {
        private final String cpgCode;
        private final Date expiryDate;

        public CpgExpiryDateInfo(final String cpgCode, final Date expiryDate) {
            this.cpgCode = Objects.requireNonNull(cpgCode);
            this.expiryDate = Objects.requireNonNull(expiryDate);
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            CpgExpiryDateInfo that = (CpgExpiryDateInfo) o;
            return Objects.equals(this.cpgCode, that.cpgCode) && Objects.equals(this.expiryDate, that.expiryDate);
        }

        @Override
        public int hashCode() {
            return Objects.hash(this.cpgCode, this.expiryDate);
        }
    }

    /**
     * 执行分发逻辑
     * 注意,该方法本身不应该添加事务
     *
     * @param request             确认分发参数
     * @param distributionService 分发服务实例,用于隔离事务
     * <AUTHOR>
     * @date 2022/8/11 17:44
     * @since 1.0.0
     */
    @Override
    public void progressDistribution(final ConfirmDistributionRequest request, final DistributionService distributionService) {

        final SubmittedDistributionDetail submittedDistributionDetail = this.findAndValidateSubmittedDistributionDetail(request.getDistributionCode(), request.getCustomerCode());

        final Distribution distribution = submittedDistributionDetail.getDistribution();

        final ProgressDistributionLock progressDistributionLock = new ProgressDistributionLock(this.redisTemplate);
        if (!progressDistributionLock.tryLock(distribution.getDistributionCode(), 30 * 60 * 1000L)) {
            return;
        }

        try {

            // distributionService 用来分隔事务
            distributionService.beforeProgressDistribution(submittedDistributionDetail, request.getUserCode());

            // 发送邮件
            this.sendDistributionEmail(submittedDistributionDetail);

        } finally {
            progressDistributionLock.unLock(distribution.getDistributionCode());
        }
    }

    private SubmittedDistributionDetail findAndValidateSubmittedDistributionDetail(final String distributionCode, final String customerCode) {

        final Distribution findCondition = new Distribution();
        findCondition.setCustomerCode(ConvertUtils.toString(customerCode, ""))
                .setDistributionCode(ConvertUtils.toString(distributionCode, ""))
                // progressStatus 状态的 Distribution 的status 应该就是 DISTRIBUTING
                .setStatus(DistributionStatusEnum.DISTRIBUTING.code())
                .setProgressStatus(DistributionProgressStatusEnum.SUBMITTED.code());

        final Distribution distribution = this.distributionMapper.selectOne(findCondition);

        final DistributionItem findItemCondition = new DistributionItem();
        findItemCondition.setCustomerCode(ConvertUtils.toString(customerCode, ""))
                .setDistributionCode(ConvertUtils.toString(distributionCode, ""));

        final List<DistributionItem> itemList = this.distributionItemMapper.select(findItemCondition);

        final DistributionItemVoucher findItemVoucherCondition = new DistributionItemVoucher();
        findItemVoucherCondition.setCustomerCode(ConvertUtils.toString(customerCode, ""))
                .setDistributionCode(ConvertUtils.toString(distributionCode, ""));

        final List<DistributionItemVoucher> itemVoucherList = this.distributionItemVoucherMapper.select(findItemVoucherCondition);

        final SubmittedDistributionDetail submittedDistributionDetail = new SubmittedDistributionDetail();
        submittedDistributionDetail.setDistribution(distribution);
        submittedDistributionDetail.setItemList(itemList);
        submittedDistributionDetail.setItemVoucherList(itemVoucherList);

        return submittedDistributionDetail;
    }

    private void sendDistributionEmail(final SubmittedDistributionDetail submittedDistributionDetail) {
        // 遍历gv_distribution_item_voucher,调用邮件,获取message_id,将message_id更新到gv_distribution_item_voucher,(这一步不需要事务,另外需要考虑是否需要将分发状态更新为待检查)

        final Distribution distribution = submittedDistributionDetail.getDistribution();

        final Map<String, List<DistributionItemVoucher>> itemCodeVoucherMap = submittedDistributionDetail.getItemVoucherList().stream().collect(Collectors.groupingBy(DistributionItemVoucher::getDistributionItemCode));

        final Map<String, DistributionItem> itemCodeItemMap = submittedDistributionDetail.getItemList().stream().collect(Collectors.toMap(DistributionItem::getDistributionItemCode, Function.identity()));

        // 每个item为一组进行操作
        for (Map.Entry<String, List<DistributionItemVoucher>> entry : itemCodeVoucherMap.entrySet()) {
            final DistributionItem distributionItem = itemCodeItemMap.get(entry.getKey());

            // 获取该item下所有的券信息
            final Map<String, Voucher> voucherCodeVoucherMap = this.voucherService.queryByVoucherCodeList(null, entry.getValue().stream().map(DistributionItemVoucher::getVoucherCode).collect(Collectors.toList()))
                    .stream().collect(Collectors.toMap(Voucher::getVoucherCode, Function.identity(), (k, v) -> v));

            // 获取该item的所有itemVoucher,并按邮件地址分组queue
            final Map<String, Queue<DistributionItemVoucher>> emailAddressItemVoucherQueueMap = entry.getValue().stream().collect(Collectors.groupingBy(DistributionItemVoucher::getEmailAddress, Collectors.toCollection(LinkedList::new)));

            // 获取该item的收件人列表,遍历收件人发送邮件
            final List<DistributionRecipientInfoDto> recipientInfoList = this.analyzeRecipient(distributionItem.getRecipients(), distribution.getCustomerCode());
            for (DistributionRecipientInfoDto recipientInfo : recipientInfoList) {
                final Queue<DistributionItemVoucher> itemVoucherQueue = emailAddressItemVoucherQueueMap.get(recipientInfo.getEmailAddress());

                final List<DistributionEmailDataModel.EmailVoucherInfo> emailVoucherInfoList = new ArrayList<>();

                // 从queue中取每封邮件券数量的itemVoucher,转换为EmailVoucherInfo,同时记录itemVoucherCode
                for (int i = 0; i < distributionItem.getVouchersPerEmail(); i++) {
                    final DistributionItemVoucher itemVoucher = itemVoucherQueue.poll();
                    final Voucher voucher = voucherCodeVoucherMap.get(itemVoucher.getVoucherCode());
                    final DistributionEmailDataModel.EmailVoucherCodeInfo emailVoucherInfo = new DistributionEmailDataModel.EmailVoucherCodeInfo();
                    emailVoucherInfo.setVoucherNumber(Long.toString(voucher.getVoucherCodeNum()))
                            .setActivationCode(voucher.getVoucherActiveCode())
                            .setVoucherExpiryDate(DateUtil.format(voucher.getVoucherEffectiveDate(), DateUtil.FORMAT_YYYYMMDDHHMISS))
                            .setActivationURL(voucher.getVoucherActiveUrl())
                            .setPinCode(voucher.getVoucherPin())
                            .setDenomination(AmountUtils.idrFormat(voucher.getDenomination()))
                            .setBarCode(voucher.getVoucherBarcode());
                    // 记录voucherCode,用于后续关联messageId,虽然item和itemVoucher之间有itemCode关联,但一个item会包含多个messageId,
                    // 而因为邮箱地址允许重复,所以在同一个item中同一个邮箱地址也可能会有多个messageId,能够唯一区分的只有voucherCode,它与messageId之间是多对一关系
                    emailVoucherInfo.setVoucherCode(voucher.getVoucherCode());
                    emailVoucherInfoList.add(emailVoucherInfo);
                }

                final DistributionEmailDataModel distributionEmailDataModel = new DistributionEmailDataModel();
                distributionEmailDataModel.setVoucherList(emailVoucherInfoList);

                final DistributionEmailData emailData = new DistributionEmailData();
                emailData.setEmailDataModel(distributionEmailDataModel)
                        .setDistributionCode(distribution.getDistributionCode())
                        .setDistributionItemCode(distributionItem.getDistributionItemCode())
                        .setCustomerCode(distribution.getCustomerCode())
                        .setMessageId(null)
                        .setEvent(MessageEnventEnum.DISTRIBUTION_EMAIL)
                        .setEmailAddress(recipientInfo.getEmailAddress())
                        .setSubject(distribution.getEmailSubject())
                        .setEmailTemplateContent(distribution.getEmailRichText());

                // 发送邮件,将获取的messageId记录到对应的itemVoucher,使用itemVoucherCode关联
                this.sendEmail(emailData);

                // 保存消息id
                this.saveMessageId(emailData);
            }
        }
    }

    private void saveMessageId(final DistributionEmailData emailData) {

        final List<String> voucherCodeList = emailData.getEmailDataModel().getVoucherList()
                .stream()
                .map(info -> (DistributionEmailDataModel.EmailVoucherCodeInfo) info)
                .map(DistributionEmailDataModel.EmailVoucherCodeInfo::getVoucherCode)
                .collect(Collectors.toList());

        final Example example = new Example(DistributionItemVoucher.class, true, true);
        example.createCriteria()
                .andEqualTo(DistributionItemVoucher.C_DISTRIBUTION_CODE, ConvertUtils.toString(emailData.getDistributionCode(), null))
                .andEqualTo(DistributionItemVoucher.C_DISTRIBUTION_ITEM_CODE, ConvertUtils.toString(emailData.getDistributionItemCode(), null))
                .andEqualTo(DistributionItemVoucher.C_CUSTOMER_CODE, ConvertUtils.toString(emailData.getCustomerCode(), null))
                .andIn(DistributionItemVoucher.C_VOUCHER_CODE, voucherCodeList);

        final DistributionItemVoucher updateItemVoucher;

        if (emailData.isSendSuccess()) {
            // 发送成功,更新messageId
            updateItemVoucher = new DistributionItemVoucher()
                    .setMessageId(emailData.getMessageId());
        } else {
            // 发送失败,更新错误信息和itemVoucher状态
            updateItemVoucher = new DistributionItemVoucher()
                    .setStatus(DistributionItemVoucherStatusEnum.FAIL.code())
                    .setErrorMsg(emailData.getErrorMsg());
        }

        this.distributionItemVoucherMapper.updateByConditionSelective(updateItemVoucher, example);
    }

    private void sendEmail(final DistributionEmailData emailData) {

        final String emailContent;

        try {
            emailContent = GvHtmlHelper.fillWithTemplateContent(emailData.getEmailTemplateContent(), emailData.getEmailDataModel());

        } catch (Exception e) {
            emailData.setSendSuccess(false).setErrorMsg("Failed to populate email template.");
            log.error("分发邮件填充邮件模板失败.", e);
            return;
        }

        sendEmail(emailData, emailContent);
    }

    private void sendEmail(DistributionEmailData emailData, String emailContent) {

        final JSONObject jsonParam = new JSONObject();
        jsonParam.put("email", emailData.getEmailAddress());
        jsonParam.put("subject", emailData.getSubject());
        jsonParam.put("content", emailContent);

        final JSONObject emailRequest = new JSONObject();
        emailRequest.put("eventCode", emailData.getEvent().getCode());
        emailRequest.put("param", jsonParam);

        final ResponseMessageBean responseMessageBean = this.messageComponent.sendEmail(emailRequest);
        if (!Boolean.TRUE.equals(responseMessageBean.getSuccess())) {
            emailData.setSendSuccess(false).setErrorMsg("Email sending failed");
        } else {
            emailData.setSendSuccess(true).setMessageId(responseMessageBean.getData().getMessageId());
        }
    }

    /**
     * 执行分发的前置工作
     *
     * @param distributionDetail 分发信息详情.该方法将会修改该实例的属性
     * @param updateUserCode     操作人
     * <AUTHOR>
     * @date 2022/8/11 18:08
     * @since 1.0.0
     */
    @Override
    @Transactional
    public void beforeProgressDistribution(final SubmittedDistributionDetail distributionDetail, final String updateUserCode) {
        final Distribution distribution = distributionDetail.getDistribution();

        final Example updateDistributionCondition = new Example(Distribution.class);
        updateDistributionCondition.createCriteria()
                .andEqualTo(Distribution.C_DISTRIBUTION_CODE, ConvertUtils.toString(distribution.getDistributionCode()))
                .andEqualTo(Distribution.C_STATUS, DistributionStatusEnum.DISTRIBUTING.code())
                .andEqualTo(Distribution.C_PROGRESS_STATUS, DistributionProgressStatusEnum.SUBMITTED.code());

        final Distribution updateDistribution = new Distribution();
        final Date distributeTime = new Date();
        updateDistribution
                .setProgressStatus(DistributionProgressStatusEnum.DISTRIBUTING.code())
                .setDistributeTime(distributeTime)
                .setUpdateUser(updateUserCode);

        this.distributionMapper.updateByConditionSelective(updateDistribution, updateDistributionCondition);

        // item voucher 更新为分发中
        final Example itemVoucherExample = new Example(DistributionItemVoucher.class);
        itemVoucherExample.createCriteria()
                .andEqualTo(DistributionItemVoucher.C_DISTRIBUTION_CODE, ConvertUtils.toString(distribution.getDistributionCode()));

        final DistributionItemVoucher updateItemVoucher = new DistributionItemVoucher();
        updateItemVoucher.setStatus(DistributionItemVoucherStatusEnum.DISTRIBUTING.code());
        this.distributionItemVoucherMapper.updateByConditionSelective(updateItemVoucher, itemVoucherExample);

        // 将distributionDetail同步为最新状态
        distribution
                .setProgressStatus(DistributionProgressStatusEnum.DISTRIBUTING.code())
                .setDistributeTime(distributeTime)
                .setUpdateUser(updateUserCode);
        distributionDetail.getItemVoucherList().forEach(itemVoucher -> itemVoucher.setStatus(DistributionItemVoucherStatusEnum.DISTRIBUTING.code()));
    }

    /**
     * @ClassName ProgressDistributionLock
     * @Description 执行分发使用的不可重入的锁
     * <AUTHOR>
     * @Date 2022/8/9 10:57
     * @Version V1.0
     **/
    public static class ProgressDistributionLock extends DistributionLock {

        private static final String PROGRESS_DISTRIBUTING_LOCK_KEY_TEMPLATE = "Distribution:Progress:Distributing:Lock:Key:%s";

        public ProgressDistributionLock(final GTechRedisTemplate redisTemplate) {
            super(redisTemplate);
        }

        @Override
        protected String buildLockKey(final String lock) {
            return String.format(ProgressDistributionLock.PROGRESS_DISTRIBUTING_LOCK_KEY_TEMPLATE, lock);
        }
    }

    public static abstract class DistributionLock {

        protected final GTechRedisTemplate redisTemplate;

        public DistributionLock(final GTechRedisTemplate redisTemplate) {
            this.redisTemplate = Objects.requireNonNull(redisTemplate);
        }

        /**
         * 获取锁
         *
         * @param lock        锁key
         * @param lockTimeout 锁的超时时间(ms),如果获取锁成功,将在未来lockTimeout时间内保持锁的持有.除非手动释放锁
         * @return boolean true-获取成功
         * <AUTHOR>
         * @date 2022/8/11 11:25
         * @since 1.0.0
         */
        public boolean tryLock(final String lock, final long lockTimeout) {

            if (StringUtils.isBlank(lock) || lockTimeout < 0L) {
                throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.PARAMTER_ERROR);
            }

            return Boolean.TRUE.equals(this.redisTemplate.opsValueSetIfAbsent(GvcoreConstants.APP_CODE, this.buildLockKey(lock), 1, lockTimeout));
        }

        public void unLock(final String lock) {
            if (StringUtils.isBlank(lock)) {
                throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.PARAMTER_ERROR);
            }
            this.redisTemplate.delete(GvcoreConstants.APP_CODE, this.buildLockKey(lock));
        }

        protected abstract String buildLockKey(final String lock);
    }


    /**
     * -------------------第二部分-----------------------
     * 1.定时任务获取distribution状态为分发中的数据
     * 2.使用distribution_code加锁
     * 3.查询distribution下gv_distribution_item_voucher包含message_id的记录
     * 4.使用message_id查询消息状态(t_msg_request_record)
     * ---4.1 半小时内无数据 - 忽略
     * ---4.3 半小时外无有数据 - 修改分发状态为FAIL,错误信息为邮件发送失败(或超时)
     * ---4.2 查询时有数据 - 根据status得到结果,如果成功,修改分发状态为已分发,否则更新为FAIL
     * 5.检查distribution所有gv_distribution_item_voucher是否成功
     * ---5.1 半小时内未全部成功 - 忽略
     * ---5.3 超过小时内未全部成功 - 更新distribution等gv_distribution_item_voucher以外的带分发状态的数据为FAIL
     * ---5.2 查询时全部成功 - 更新distribution等全部带分发状态的数据为已分发
     * 6. 释放锁
     */
    @Override
    public void refreshDistributionCompletion() {
        // 查询至多numberOfRefreshes个分发中的记录
        final List<Distribution> distributionList = this.queryDistributingRecords(this.numberOfRefreshes);

        final Date currentTime = new Date();

        for (Distribution distribution : distributionList) {

            final RefreshDistributionStatusLock refreshDistributionStatusLock = new RefreshDistributionStatusLock(this.redisTemplate);
            if (!refreshDistributionStatusLock.tryLock(distribution.getDistributionCode(), 30 * 60 * 1000L)) {
                continue;
            }

            try {
                final Date distributeTime = distribution.getDistributeTime();

                // 该分发的所有itemVoucher,预估数据量不多,如果后续数据量多需要考虑分页,分页时需要注意判断分发执行状态的逻辑
                final List<DistributionItemVoucher> distributionItemVoucherList = this.queryItemVouchers(distribution.getDistributionCode());

                // 筛选分发中的itemVoucher
                final List<String> messageIdSet = distributionItemVoucherList.stream()
                        .filter(item -> DistributionItemVoucherStatusEnum.DISTRIBUTING.equalsCode(item.getStatus()))
                        .map(DistributionItemVoucher::getMessageId).distinct()
                        .collect(Collectors.toList());

                // 将还处于分发中的itemVoucher按照messageId分组,方便后面查询
                final Map<String, List<DistributionItemVoucher>> groupByMessageIdList = distributionItemVoucherList.stream()
                        .filter(item -> DistributionItemVoucherStatusEnum.DISTRIBUTING.equalsCode(item.getStatus()))
                        .collect(Collectors.groupingBy(DistributionItemVoucher::getMessageId));

                final Set<DistributionItemVoucher> waitUpdateItemVoucherList = new HashSet<>();

                for (String messageId : messageIdSet) {
                    final RequestRecord messageInfo = this.findMessageInfo(messageId);
                    if (null == messageInfo) {
                        // 半小时内无数据 -忽略
                        if (!this.withinHalfAnHour(currentTime, distributeTime)) {
                            final List<DistributionItemVoucher> itemVoucherList = groupByMessageIdList.getOrDefault(messageId, Collections.emptyList());
                            itemVoucherList
                                    .forEach(itemVoucher -> itemVoucher.setStatus(DistributionItemVoucherStatusEnum.FAIL.code()).setErrorMsg("Sending email timed out.").setUpdateTime(currentTime));
                            waitUpdateItemVoucherList.addAll(itemVoucherList);
                        }
                    } else {
                        final List<DistributionItemVoucher> itemVoucherList = groupByMessageIdList.getOrDefault(messageId, Collections.emptyList());

                        if (Boolean.TRUE.equals(messageInfo.getResultStatus())) {
                            // 发送成功
                            itemVoucherList
                                    .forEach(itemVoucher -> itemVoucher.setStatus(DistributionItemVoucherStatusEnum.DISTRIBUTED.code()).setUpdateTime(currentTime));
                        } else {
                            itemVoucherList
                                    .forEach(itemVoucher -> itemVoucher.setStatus(DistributionItemVoucherStatusEnum.FAIL.code()).setErrorMsg("Failed to send email.").setUpdateTime(currentTime));
                        }
                        waitUpdateItemVoucherList.addAll(itemVoucherList);
                    }
                }

                boolean changeDistribute = false;

                // 如果不包含分发中的状态,Distribution 更新为分发完成
                final long distributingCount = distributionItemVoucherList.stream()
                        .filter(itemVoucher -> DistributionItemVoucherStatusEnum.DISTRIBUTING.equalsCode(itemVoucher.getStatus()))
                        .count();
                if (distributingCount == 0) {
                    distribution.setStatus(DistributionStatusEnum.DISTRIBUTED.code())
                            .setProgressStatus(DistributionProgressStatusEnum.DISTRIBUTED.code())
                            .setUpdateTime(currentTime);
                    changeDistribute = true;
                }
                // 如果超过半个小时该Distribution还有进行中的itemVoucher,应当被更新为失败.但这里无需进行判断.因为在itemVoucher的刷新逻辑中,超过半个小时即会被更新为失败.
                // 所以半小时后该Distribution不存在还处于DISTRIBUTING的itemVoucher

                if (changeDistribute) {
                    this.saveRefreshDistributionCompletion(distribution);
                }
                this.saveRefreshDistributionItemVoucherCompletion(waitUpdateItemVoucherList);
            } finally {
                refreshDistributionStatusLock.unLock(distribution.getDistributionCode());
            }
        }
    }

    private List<DistributionItemVoucher> queryItemVouchers(final String distributionCode) {

        if (StringUtils.isBlank(distributionCode)) {
            return Collections.emptyList();
        }

        final DistributionItemVoucher distributionItemVoucher = new DistributionItemVoucher();
        distributionItemVoucher.setDistributionCode(distributionCode);

        return this.distributionItemVoucherMapper.select(distributionItemVoucher);
    }

    private List<Distribution> queryDistributingRecords(final int queryNum) {
        final Example example = new Example(Distribution.class);
        example.createCriteria()
                .andEqualTo(Distribution.C_PROGRESS_STATUS, DistributionProgressStatusEnum.DISTRIBUTING.code());

        return this.distributionMapper.selectByExampleAndRowBounds(example, new RowBounds(0, queryNum));
    }

    private void saveRefreshDistributionItemVoucherCompletion(final Collection<DistributionItemVoucher> waitUpdateItemVoucherList) {

        if (CollectionUtils.isEmpty(waitUpdateItemVoucherList)) {
            return;
        }

        for (DistributionItemVoucher distributionItemVoucher : waitUpdateItemVoucherList) {
            final Example example = new Example(DistributionItemVoucher.class);
            example.createCriteria()
                    .andEqualTo(DistributionItemVoucher.C_DISTRIBUTION_CODE, ConvertUtils.toString(distributionItemVoucher.getDistributionCode(), ""))
                    .andEqualTo(DistributionItemVoucher.C_DISTRIBUTION_ITEM_CODE, ConvertUtils.toString(distributionItemVoucher.getDistributionItemCode(), ""))
                    .andEqualTo(DistributionItemVoucher.C_CUSTOMER_CODE, ConvertUtils.toString(distributionItemVoucher.getCustomerCode(), ""))
                    .andEqualTo(DistributionItemVoucher.C_VOUCHER_CODE, ConvertUtils.toString(distributionItemVoucher.getVoucherCode(), ""));

            this.distributionItemVoucherMapper.updateByConditionSelective(distributionItemVoucher, example);
        }
    }

    private void saveRefreshDistributionCompletion(final Distribution distribution) {

        if (StringUtils.isBlank(distribution.getDistributionCode())) {
            throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.PARAMTER_ERROR);
        }

        final Example example = new Example(Distribution.class);
        example.createCriteria()
                .andEqualTo(Distribution.C_DISTRIBUTION_CODE, ConvertUtils.toString(distribution.getDistributionCode()));

        this.distributionMapper.updateByConditionSelective(distribution, example);
    }

    private boolean withinHalfAnHour(final Date currentTime, final Date startTime) {
        return currentTime.getTime() - startTime.getTime() <= 30 * 60 * 1000L;
    }

    private RequestRecord findMessageInfo(final String messageId) {
        final RequestRecord requestRecord = new RequestRecord();
        requestRecord.setMessageId(ConvertUtils.toString(messageId, ""));
        return this.requestRecordMapper.selectOne(requestRecord);
    }

    /**
     * @ClassName ProgressDistributionLock
     * @Description 刷新分发状态使用的不可重入的锁
     * <AUTHOR>
     * @Date 2022/8/9 10:57
     * @Version V1.0
     **/
    public static class RefreshDistributionStatusLock extends DistributionLock {

        private static final String REFRESH_DISTRIBUTION_STATUS_LOCK_KEY = "Distribution:Refresh:Distributing:Status:Lock:Key:%s";

        public RefreshDistributionStatusLock(final GTechRedisTemplate redisTemplate) {
            super(redisTemplate);
        }

        @Override
        protected String buildLockKey(final String lock) {
            return String.format(RefreshDistributionStatusLock.REFRESH_DISTRIBUTION_STATUS_LOCK_KEY, lock);
        }
    }

    // list resend changeEmail


    @Override
    public void resendVoucherEmail(final String distributionItemCode, final String voucherCode) {

        sendDistributionItemVoucherEmail(distributionItemCode, voucherCode, null);
    }

    @Override
    public void changeResendVoucherEmail(final String distributionItemCode, final String voucherCode, final String email) {

        sendDistributionItemVoucherEmail(distributionItemCode, voucherCode, email);
    }

    private void sendDistributionItemVoucherEmail(final String distributionItemCode , final String voucherCode, final String email) {

        if (StringUtils.isBlank(distributionItemCode)) throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.NO_DATA_FOUND);
        if (StringUtils.isBlank(voucherCode)) throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.NO_VOUCHER_DATA_FOUND);

        //distributionItemVoucher
        final DistributionItemVoucher distributionItemVoucher = distributionItemVoucherMapper.selectOne(new DistributionItemVoucher()
                .setVoucherCode(voucherCode).setDistributionItemCode(distributionItemCode));
        if (null == distributionItemVoucher) throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.NO_DATA_FOUND);

        //distributionCode
        final String distributionCode = distributionItemVoucher.getDistributionCode();
        if (StringUtils.isBlank(distributionCode)) throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.NO_DATA_FOUND);

        //findDistribution
        final Distribution findDistribution = new Distribution();
        findDistribution.setDistributionCode(distributionCode);
        final Distribution distribution = this.distributionMapper.selectOne(findDistribution);
        if (null == distribution) throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.NO_DATA_FOUND);

        //voucher
        final Voucher voucher = this.voucherService.getVoucherByCode(voucherCode);
        if (null == voucher) throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.NO_VOUCHER_DATA_FOUND);

        //init
        final String emailSubject = distribution.getEmailSubject();
        final String emailRichText = distribution.getEmailRichText();
        final String emailAddress = StringUtils.isNotBlank(email) ? email : distributionItemVoucher.getEmailAddress();
        final String voucherActiveCode = voucher.getVoucherActiveCode();
        final String voucherActiveUrl = voucher.getVoucherActiveUrl();
        final String voucherNumber = String.valueOf(distributionItemVoucher.getVoucherNumber());
        final String voucherExpiryDate = DateUtil.format(voucher.getVoucherEffectiveDate(), DateUtil.FORMAT_YYYYMMDDHHMISS);
        final String denomination = AmountUtils.idrFormat(voucher.getDenomination());
        final String voucherBarcode = voucher.getVoucherBarcode();
        final String voucherPin = voucher.getVoucherPin();
        final String customerCode = distribution.getCustomerCode();
        final DistributionEmailDataModel.EmailVoucherInfo emailVoucherInfo = new DistributionEmailDataModel.EmailVoucherCodeInfo()
                .setVoucherCode(voucherCode)
                .setActivationCode(voucherActiveCode)
                .setActivationURL(voucherActiveUrl)
                .setVoucherNumber(voucherNumber)
                .setVoucherExpiryDate(voucherExpiryDate)
                .setDenomination(denomination)
                .setBarCode(voucherBarcode)
                .setPinCode(voucherPin);
        final DistributionEmailDataModel emailDataModel = new DistributionEmailDataModel()
                .setVoucherList(Collections.singletonList(emailVoucherInfo));
        final DistributionEmailData emailData = new DistributionEmailData();
        emailData.setEmailDataModel(emailDataModel)
                .setDistributionCode(distributionCode)
                .setDistributionItemCode(distributionItemCode)
                .setCustomerCode(customerCode)
                .setMessageId(null)
                .setEvent(MessageEnventEnum.DISTRIBUTION_EMAIL)
                .setEmailAddress(emailAddress)
                .setSubject(emailSubject)
                .setEmailTemplateContent(emailRichText);

        final String emailContent;
        try {
            emailContent = GvHtmlHelper.fillWithTemplateContent(emailRichText, emailDataModel);

        } catch (Exception e) {
            log.error("分发邮件填充邮件模板失败.", e);
            throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.DISTRIBUTION_FAILED_POPULATE_EMAIL_TEMPLATE);
        }

        sendEmail(emailData, emailContent);

        final DistributionItemVoucherStatusEnum status = emailData.isSendSuccess()
                ? DistributionItemVoucherStatusEnum.DISTRIBUTED : DistributionItemVoucherStatusEnum.FAIL;
        final String errorMsg = emailData.getErrorMsg();

        DistributionResend distributionResend = new DistributionResend();
        distributionResend.setDistributionCode(distributionCode);
        distributionResend.setDistributionItemCode(distributionItemCode);
        distributionResend.setVoucherCode(voucherCode);
        distributionResend.setEmailAddress(emailAddress);
        distributionResend.setCustomerCode(customerCode);
        distributionResend.setEmailEndCustomerName(distributionItemVoucher.getEmailEndCustomerName());
        distributionResend.setStatus(status.code());
        distributionResend.setErrorMsg(errorMsg);

        distributionResendMapper.insertSelective(distributionResend);

        this.distributionItemVoucherMapper.updateByPrimaryKeySelective(new DistributionItemVoucher()
                .setId(distributionItemVoucher.getId())
                .setEmailAddress(emailAddress)
                .setStatus(emailData.isSendSuccess() ? DistributionItemVoucherStatusEnum.DISTRIBUTED.code() : DistributionItemVoucherStatusEnum.FAIL.code())
                .setErrorMsg(emailData.getErrorMsg()));

    }

    @Override
    public PageResult<QueryCustomerDistributionResult> queryDistributionList(QueryCustomerDistributionRequest param) {

        final Example queryExample = new Example(Distribution.class);
        Example.Criteria criteria = queryExample.createCriteria();
        criteria.andEqualTo(Distribution.C_CUSTOMER_CODE, ConvertUtils.toString(param.getCustomerCode(), ""))
                .andEqualTo(Distribution.C_DISTRIBUTION_CODE, ConvertUtils.toString(param.getDistributionCode(), null))
                .andGreaterThanOrEqualTo(Distribution.C_CREATE_TIME, param.getCreateTimeBegin())
                .andLessThan(Distribution.C_CREATE_TIME, param.getCreateTimeEnd())
                .andEqualTo(Distribution.C_STATUS, ConvertUtils.toString(param.getStatus(), null));

        if (StringUtils.isNotBlank(param.getCpgCode())) {
            List<DistributionItem> itemDistribution = this.distributionItemMapper.select(new DistributionItem()
                    .setCustomerCode(param.getCustomerCode())
                    .setCpgCode(param.getCpgCode()));

            if (CollectionUtils.isEmpty(itemDistribution)) return PageResult.ok();

            criteria.andIn(Distribution.C_DISTRIBUTION_CODE, itemDistribution.stream().map(DistributionItem::getDistributionCode)
                    .collect(Collectors.toSet()));
        }
        queryExample.orderBy(Distribution.C_CREATE_TIME).desc();

        //count
        final int count = this.distributionMapper.selectCountByCondition(queryExample);
        if (count <= 0) return new PageResult<>();

        RowBounds rowBounds = new RowBounds((param.getPageNum() - 1) * param.getPageSize(), param.getPageNum() * param.getPageSize());
        final List<Distribution> distributionList = this.distributionMapper.selectByExampleAndRowBounds(queryExample,
                rowBounds);

        return new PageResult<>(distributionList.stream().map(distribution -> {

            List<DistributionItem> itemList = this.distributionItemMapper.select(new DistributionItem()
                    .setDistributionCode(distribution.getDistributionCode()));

            return new QueryCustomerDistributionResult()
                    .setDistributionCode(distribution.getDistributionCode())
                    .setDistributionType(distribution.getDistributionType())
                    .setStatus(distribution.getStatus())
                    .setRecipientsSum(itemList.stream().mapToInt(DistributionItem::getRecipientsNum).sum())
                    .setCreateUser(distribution.getCreateUser())
                    .setCpgDesc(StringUtils.join(itemList.stream()
                    .map(DistributionItem::getCpgNameSnapshot).collect(Collectors.toList()), "|"))
                    .setCreateTime(distribution.getCreateTime());
        }).collect(Collectors.toList()), (long) count);
    }

    @Override
    public GetCustomerDistributionResult getCustomerDistribution (String distributionCode) {

        Distribution distribution = this.distributionMapper.selectOne(new Distribution().setDistributionCode(distributionCode));
        if (null == distribution) return null;

        List<DistributionItem> itemList = this.distributionItemMapper.select(new DistributionItem().setDistributionCode(distributionCode));

        List<GetDistributionItemResult> items = itemList.stream().map(item -> {

            final Example queryFailExample = new Example(DistributionItemVoucher.class);
            queryFailExample.createCriteria()
                    .andEqualTo(DistributionItemVoucher.C_DISTRIBUTION_ITEM_CODE, ConvertUtils.toString(item.getDistributionItemCode(), ""))
                    .andEqualTo(DistributionItemVoucher.C_STATUS, DistributionItemVoucherStatusEnum.FAIL.code());

            //fail
            List<DistributionItemVoucher> failList = this.distributionItemVoucherMapper.selectByCondition(queryFailExample);

            //inventory
            List<CustomerCpgEffectiveDateResult> inventoryList = this.voucherService.queryCustomerCpgInventoryDetail(distribution.getCustomerCode(), item.getCpgCode());
            CustomerCpgEffectiveDateResult inventoryBean = CollectionUtils.isEmpty(inventoryList) ? null
                    : inventoryList.stream().filter(i -> item.getCpgExpiryDateSnapshot().equals(i.getVoucherEffectiveDate())).findFirst().orElse(null);

            return new GetDistributionItemResult()
                    .setStartDistribute(this.distributionItemVoucherMapper.minCreateTimeByItemCode(item.getDistributionItemCode()))
                    .setFailure(failList.size())
                    .setStatus(this.getStatusDesc(item))
                    .setCpgCode(item.getCpgCode())
                    .setRecipients(item.getRecipients())
                    .setRecipientsNum(item.getRecipientsNum())
                    .setVpgName(item.getCpgNameSnapshot())
                    .setInventory(null != inventoryBean ? inventoryBean.getInventory(): 0)
                    .setVouchersPerEmail(item.getVouchersPerEmail())
                    .setExpiryDate(item.getCpgExpiryDateSnapshot())
                    .setVoucherAmount(item.getVoucherAmount())
                    .setFailureArray(failList.stream().map(f -> new GetDistributionItemResult.GetDistributionItemDetailResult()
                            .setEmailAddress(f.getEmailAddress())
                            .setErrorMsg(f.getErrorMsg())).collect(Collectors.toList()));


        }).collect(Collectors.toList());

        return new GetCustomerDistributionResult().setDistributeTime(distribution.getCreateTime())
                .setDistributionCode(distribution.getDistributionCode())
                .setCustomerCode(distribution.getCustomerCode())
                .setDistributionType(distribution.getDistributionType())
                .setEmailTemplateCode(distribution.getEmailTemplateCode())
                .setEmailSubject(distribution.getEmailSubject())
                .setEmailRichText(distribution.getEmailRichText())
                .setStatus(distribution.getStatus())
                .setProgressStatus(distribution.getProgressStatus())
                .setCreateUser(distribution.getCreateUser())
                .setConfirmDistributionTime(distribution.getConfirmDistributionTime())
                .setItemList(items);


    }

    private String getStatusDesc(DistributionItem item) {

        List<CountStatusByItemCodeDto> statusList = this.distributionItemVoucherMapper.countStatusByItemCode(item.getDistributionItemCode());
        if (CollectionUtils.isEmpty(statusList)) return "";

        Map<String, Integer> statusMap = statusList.stream().collect(Collectors.toMap(CountStatusByItemCodeDto::getStatus, CountStatusByItemCodeDto::getNum));

        int sum = statusList.stream().mapToInt(CountStatusByItemCodeDto::getNum).sum();
        boolean status = ConvertUtils.toInteger(statusMap.get(DistributionItemVoucherStatusEnum.DISTRIBUTED.code()), 0)
                + ConvertUtils.toInteger(statusMap.get(DistributionItemVoucherStatusEnum.FAIL.code()), 0) == sum;

        return status ? DistributionItemVoucherStatusEnum.DISTRIBUTED.code()
                : DistributionItemVoucherStatusEnum.DISTRIBUTING.code();
    }

    @Override
    public void deleteCustomerDistribution(ConfirmDistributionRequest request) {

        this.distributionMapper.delete(new Distribution().setDistributionCode(request.getDistributionCode()).setStatus(DistributionStatusEnum.DRAFT.code()));
    }


    @Override
    public PageResult<QueryDistributionDetailResult> queryDistributionDetailList(QueryDistributionDetailRequest param) {

        final Example queryExample = new Example(DistributionItemVoucher.class);
        Example.Criteria criteria = queryExample.createCriteria();
        criteria.andEqualTo(DistributionItemVoucher.C_CUSTOMER_CODE, ConvertUtils.toString(param.getCustomerCode(), ""))
                .andEqualTo(DistributionItemVoucher.C_DISTRIBUTION_CODE, ConvertUtils.toString(param.getDistributionCode(), null))
                .andGreaterThanOrEqualTo(DistributionItemVoucher.C_CREATE_TIME, param.getCreateTimeBegin())
                .andLessThan(DistributionItemVoucher.C_CREATE_TIME, param.getCreateTimeEnd())
                .andEqualTo(DistributionItemVoucher.C_STATUS, ConvertUtils.toString(param.getStatus(), null))
                .andEqualTo(DistributionItemVoucher.C_CPG_CODE, ConvertUtils.toString(param.getCpgCode(), null))
                .andEqualTo(DistributionItemVoucher.C_VOUCHER_CODE, ConvertUtils.toString(param.getVoucherCode(), null))
                .andEqualTo(DistributionItemVoucher.C_EMAIL_END_CUSTOMER_NAME, ConvertUtils.toString(param.getEmailEndCustomerName(), null))
                .andEqualTo(DistributionItemVoucher.C_EMAIL_ADDRESS, ConvertUtils.toString(param.getEmailAddress(), null));

        queryExample.orderBy(DistributionItemVoucher.C_CREATE_TIME).desc();

        RowBounds rowBounds = new RowBounds((param.getPageNum() - 1) * param.getPageSize(), param.getPageNum() * param.getPageSize());
        List<DistributionItemVoucher> detailList = this.distributionItemVoucherMapper.selectByExampleAndRowBounds(queryExample, rowBounds);

        Map<String, DistributionItem> distributionItemMap = detailList.stream()
                .map(DistributionItemVoucher::getDistributionItemCode)
                .distinct()
                .map(e -> new DistributionItem().setDistributionItemCode(e))
                .map(distributionItemMapper::selectOne)
                .collect(Collectors.toMap(DistributionItem::getDistributionItemCode, e -> e));

        int total = this.distributionItemVoucherMapper.selectCountByCondition(queryExample);


        return PageResult.ok(detailList.stream()
                .map(e -> {

                    final String voucherCode = e.getVoucherCode();
                    final Voucher voucher = voucherService.getVoucherByCode(voucherCode);
                    final DistributionItem distributionItem = distributionItemMap.get(e.getDistributionItemCode());

                    final Example queryTransactionDataExample = new Example(TransactionData.class);
                    Example.Criteria queryTransactionDataCriteria = queryTransactionDataExample.createCriteria();
                    queryTransactionDataCriteria.andEqualTo(TransactionData.C_VOUCHER_CODE, ConvertUtils.toString(voucherCode, ""));
                    queryTransactionDataExample.orderBy(TransactionData.C_ID).desc();
                    List<TransactionData> transactionDataArray = this.transactionDataMapper.selectByExampleAndRowBounds(queryTransactionDataExample, new RowBounds(0, 1));

                    final TransactionData transactionData = CollectionUtils.isEmpty(transactionDataArray) ? new TransactionData() : transactionDataArray.get(0);

                    String merchantName = "";
                    String outletName = "";
                    if (TransactionTypeEnum.GIFT_CARD_REDEEM.getCode().equals(transactionData.getTransactionType())) {

                        if (StringUtils.isNotBlank(transactionData.getMerchantCode())) {

                            MerchantResponse merchant = merchantService.getMerchant(GetMerchantRequest.builder().merchantCode(transactionData.getMerchantCode()).build());
                            merchantName = null == merchant ? "" : merchant.getMerchantName();
                        }

                        if (StringUtils.isNotBlank(transactionData.getOutletCode())) {

                            OutletResponse outlet = outletService.getOutlet(GetOutletRequest.builder().outletCode(transactionData.getOutletCode()).build());
                            outletName = null == outlet ? "" : outlet.getOutletName();
                        }
                    }

                    return new QueryDistributionDetailResult().setCreateTime(e.getCreateTime())
                            .setExpiryDate(distributionItem.getCpgExpiryDateSnapshot())
                            .setEmailAddress(e.getEmailAddress())
                            .setEmailEndCustomerName(e.getEmailEndCustomerName())
                            .setDistributionCode(e.getDistributionCode())
                            .setCpgName(distributionItem.getCpgNameSnapshot())
                            .setDistributionItemCode(e.getDistributionItemCode())
                            .setStatus(e.getStatus())
                            .setVoucherCode(voucherCode)
                            .setErrorMsg(e.getErrorMsg())
                            .setLastActionDate(transactionData.getTransactionDate())
                            .setLastTransactionType(TransactionTypeEnum.getTypeDesc(transactionData.getTransactionType()))
                            .setVoucherStatus(null != voucher ? VoucherStatusEnum.getByCode(voucher.getStatus()) : "")
                            .setMerchantName(merchantName)
                            .setOutletName(outletName);

                }).collect(Collectors.toList()), (long) total);
    }

    @Autowired
    private OssHelper ossHelper;
    @Autowired
    private FileHelper fileHelper;
    @Override
    public String downloadDistributionDetail(QueryDistributionDetailRequest param) {

        param.setPageSize(100000);
        param.setPageNum(1);

        List<QueryDistributionDetailResult> list = this.queryDistributionDetailList(param).getData().getList();

        if (CollectionUtils.isEmpty(list)) return "";

        GvExcelHelper.AppendTableWriter writer = GvExcelHelper.buildAppendTableWriter("DistributionDetail.xlsx", QueryDistributionDetailResult.class);
        writer.doWrite(list, "sheet1");
        return this.ossHelper.upload(writer.getStream(), fileHelper.generateOssFileName("distributionDetail",  ExcelTypeEnum.XLSX.getValue()));
    }

}
