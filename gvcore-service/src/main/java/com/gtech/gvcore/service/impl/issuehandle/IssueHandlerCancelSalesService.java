package com.gtech.gvcore.service.impl.issuehandle;

import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.*;
import com.gtech.gvcore.dao.mapper.ApproveNodeRecordMapper;
import com.gtech.gvcore.dao.mapper.CustomerOrderMapper;
import com.gtech.gvcore.dao.mapper.OutletMapper;
import com.gtech.gvcore.dao.mapper.VoucherAllocationMapper;
import com.gtech.gvcore.dao.mapper.VoucherBatchMapper;
import com.gtech.gvcore.dao.mapper.VoucherReceiveBatchMapper;
import com.gtech.gvcore.dao.mapper.VoucherReceiveMapper;
import com.gtech.gvcore.dao.model.ApproveNodeRecord;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.dao.model.VoucherAllocation;
import com.gtech.gvcore.dao.model.VoucherAllocationBatch;
import com.gtech.gvcore.dao.model.VoucherBatch;
import com.gtech.gvcore.dao.model.VoucherReceive;
import com.gtech.gvcore.dao.model.VoucherReceiveBatch;
import com.gtech.gvcore.service.IssueHandlerBaseService;
import com.gtech.gvcore.service.VoucherAllocationBatchService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class IssueHandlerCancelSalesService extends IssueHandlerValidateService implements IssueHandlerBaseService {

    @Autowired
    private VoucherAllocationBatchService voucherAllocationBatchService;

    @Autowired
    private VoucherAllocationMapper voucherAllocationMapper;

    @Autowired
    private ApproveNodeRecordMapper approveNodeRecordMapper;

    @Autowired
    private OutletMapper outletMapper;

    @Autowired
    private CustomerOrderMapper customerOrderMapper;

    @Autowired
    private VoucherBatchMapper voucherBatchMapper;

    @Autowired
    private VoucherReceiveMapper voucherReceiveMapper;

    @Autowired
    private VoucherReceiveBatchMapper voucherReceiveBatchMapper;

    @Override
    public IssueHandlingTypeEnum getIssueHandlingType() {
        return IssueHandlingTypeEnum.CANCEL_SALES;
    }

    @Override
	public List<IssueHandlingDetails> validate(List<IssueHandlingDetails> details, String issuerCode) {
        if (CollectionUtils.isEmpty(details)) {
            return Collections.emptyList();
        }
		checkIfExist(details, getIssueHandlingType(), issuerCode);
        checkInvoiceNumber(details,TransactionTypeEnum.GIFT_CARD_SELL);
        checkApprovalCode(details, TransactionTypeEnum.GIFT_CARD_SELL);
		checkOutletName(details, issuerCode);
        //检查是否是同一天 MER-1956
        //checkIsItTheSameDay(details);

        return details;

    }

    @Override
	public List<IssueHandlingDetails> execute(List<IssueHandlingDetails> details, String issuerCode) {
        if (CollectionUtils.isEmpty(details)) {
            return Collections.emptyList();
        }
        // 判断实体券还是电子券

        // 如果是电子券的话，创建状态和激活状态的都可以取消
        // 如果是实体券的话，只有激活状态的可以取消


		List<IssueHandlingDetails> validate = validate(details, issuerCode);

        List<String> successVoucherCodes = validate.stream()
                .filter(detail -> IssueHandlingProcessStatusEnum.SUCCESS.code().equals(detail.getProcessStatus()))
                .map(IssueHandlingDetails::getVoucherCode)
                .collect(Collectors.toList());

        Map<String, String> voucherCodeOutlet = details.stream().collect(Collectors.toMap(IssueHandlingDetails::getVoucherCode, IssueHandlingDetails::getOutletName));



        if (CollectionUtils.isNotEmpty(successVoucherCodes)){
            performAction(successVoucherCodes,voucherCodeOutlet);
        }

        return validate;
    }


    private void   performAction(List<String> voucherCodes, Map<String, String> voucherCodeOutlet) {

        //根据voucherCodes 查询出voucher
        Example voucherList = new Example(Voucher.class);
        voucherList.createCriteria().andIn(Voucher.C_VOUCHER_CODE, voucherCodes);

        List<Voucher> vouchers = voucherMapper.selectByCondition(voucherList);
        List<String> vceList = vouchers.stream()
                .filter(voucherResponse -> "VCE".equals(voucherResponse.getMopCode()))
                .map(Voucher::getVoucherCode)
                .collect(Collectors.toList());
        List<String> vcrList = vouchers.stream()
                .filter(voucherResponse -> "VCR".equals(voucherResponse.getMopCode()))
                .map(Voucher::getVoucherCode)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(vcrList)) {
            Example example = new Example(Voucher.class);
            example.createCriteria().andIn(Voucher.C_VOUCHER_CODE, vcrList);
            Voucher voucher = new Voucher();
            voucher.setStatus(VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
            voucher.setSalesTime(null);
            voucher.setSalesOutlet(null);
            voucherMapper.setUsedOrSalesNull(voucher, example,1);
        }
        if (CollectionUtils.isNotEmpty(vceList)) {
            Example example = new Example(Voucher.class);
            example.createCriteria().andIn(Voucher.C_VOUCHER_CODE, vceList);
            Voucher voucher = new Voucher();
            voucher.setStatus(VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
            voucher.setVoucherStatus(VoucherStatusEnableDisableEnum.STATUS_DESTROY.getCode());
            voucherMapper.updateByConditionSelective(voucher, example);
        }


        Map<String, Outlet> outletMap = new HashMap<>();
        Example outletExample = new Example(Outlet.class);
        outletExample.createCriteria().andIn("outletName", voucherCodeOutlet.values());
        List<Outlet> outlets = outletMapper.selectByCondition(outletExample);
        if (CollectionUtils.isNotEmpty(outlets)){
            outletMap = outlets.stream().collect(Collectors.toMap(Outlet::getOutletName, x->x));
        }


        HashMap<String, List<String>> voucherReceiveCode = new HashMap<>();
        vcrList.forEach(x->{
            //根据voucherCodes 查询出voucherReceive
            Example voucherReceiveList = new Example(VoucherReceiveBatch.class);

            voucherReceiveList.createCriteria()
                    .andLessThanOrEqualTo("voucherStartNo",x)
                    .andGreaterThanOrEqualTo("voucherEndNo",x);
            voucherReceiveList.orderBy("createTime").desc();
            List<VoucherReceiveBatch> voucherReceiveBatches = voucherReceiveBatchMapper.selectByCondition(voucherReceiveList);
            if (CollectionUtils.isEmpty(voucherReceiveBatches)){
                return;
            }
            voucherReceiveCode.put(x,voucherReceiveBatches.stream().map(VoucherReceiveBatch::getVoucherReceiveCode).collect(Collectors.toList()));
        });


        //检查vcrList 在voucherReceiveCode中不存在的key
        List<String> dbOutlet = vcrList.stream().filter(x->!voucherReceiveCode.containsKey(x)).collect(Collectors.toList());


        voucherReceiveCode.forEach((k,v)->{

            Example receiveExample = new Example(VoucherReceive.class);
            receiveExample.createCriteria()
                    .andIn("voucherReceiveCode",v)
                    .andEqualTo("sourceType","sales");
            receiveExample.orderBy("createTime").desc();
            List<VoucherReceive> voucherReceives = voucherReceiveMapper.selectByCondition(receiveExample);

            Outlet outlet = new Outlet();
            outlet.setOutletName(voucherReceives.get(0).getInbound());
            Outlet owner = outletMapper.selectOne(outlet);
            if (owner == null){
                return;
            }

            updateOwnerCode(k, owner);
        });


        Map<String, Outlet> finalOutletMap = outletMap;
        dbOutlet.forEach(x->{
            String outletName = voucherCodeOutlet.get(x);
            Outlet outlet = finalOutletMap.get(outletName);
            updateOwnerCode(x,outlet);
        });
    }

    private void updateOwnerCode(String k, Outlet owner) {
        Example example = new Example(Voucher.class);
        example.createCriteria().andEqualTo("voucherCode", k);
        Voucher voucher = new Voucher();
        voucher.setVoucherOwnerCode(owner.getOutletCode());
        voucher.setVoucherOwnerType("outlet");
        voucherMapper.updateByConditionSelective(voucher,example);
    }


    private void checkIsItTheSameDay(List<IssueHandlingDetails> details) {
        for (IssueHandlingDetails detail : details) {

            if (detail.getProcessStatus().equals(IssueHandlingProcessStatusEnum.FAILED.code())){
                continue;
            }

            Voucher voucherByCode = voucherService.getVoucherByCode(detail.getVoucherCode());
            if (voucherByCode == null) {
                detail.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
                detail.setResult("The voucher was not found");
            } else if (voucherByCode.getMopCode().equals(GvcoreConstants.MOP_CODE_VCR)) {
                voucherVCR(detail);
            } else if (voucherByCode.getMopCode().equals(GvcoreConstants.MOP_CODE_VCE)) {
                voucherVCE(detail);
            } else {
                detail.setProcessStatus(3);
                detail.setResult(voucherByCode.getMopCode() + " type is not supported");
            }
        }
    }

    private void setOrderStatusVCE(List<String> voucherNumVCE) {
        if (CollectionUtils.isEmpty(voucherNumVCE)) {
            return;
        }
        List<String> voucherBatchCodes = voucherMapper.selectVoucherBatchCodesByVoucherNums(voucherNumVCE);
        for (String voucherBatchCode : voucherBatchCodes) {
            if (checkVoucherAndBatch(voucherBatchCode, voucherNumVCE)) {
                VoucherBatch voucherBatch = voucherBatchMapper.selectOne(VoucherBatch.builder().voucherBatchCode(voucherBatchCode).build());
                CustomerOrder customerOrder = customerOrderMapper.selectOne(CustomerOrder.builder().customerOrderCode(voucherBatch.getPurchaseOrderNo()).build());
                if (customerOrder == null) {
                    return;
                }
                customerOrder.setStatus(CustomerOrderStatusEnum.CANCELED.getStatus());
                customerOrderMapper.updateByPrimaryKey(customerOrder);
            }

        }
        for (String voucherCode : voucherNumVCE) {
            Voucher voucherByCode = voucherService.getVoucherByCode(voucherCode);
            if (voucherByCode != null) {
                voucherMapper.delete(voucherByCode);
            }
        }
    }

    private boolean checkVoucherAndBatch(String voucherBatchCode, List<String> voucherNumVCE) {
        Voucher voucher = new Voucher();
        voucher.setVoucherBatchCode(voucherBatchCode);
        List<Voucher> vouchers = voucherMapper.select(voucher);
        for (Voucher voucher1 : vouchers) {
            boolean ok = false;
            for (String s : voucherNumVCE) {
                if (s.equals(voucher1.getVoucherCode())) {
                    ok = true;
                    break;
                }

            }
            if (!ok) {
                return false;
            }
        }
        return true;
    }

    private void setOrderStatusVCR(List<String> voucherNumVCR) {
        if (CollectionUtils.isEmpty(voucherNumVCR)) {
            return;
        }
        List<VoucherAllocationBatch> voucherAllocationBatches = voucherAllocationBatchService.selectAllocationBatchByVoucherNums(voucherNumVCR);
        for (VoucherAllocationBatch voucherAllocationBatch : voucherAllocationBatches) {
            if (Boolean.TRUE.equals(checkVoucherAndRange(voucherNumVCR, Long.parseLong(voucherAllocationBatch.getVoucherStartNo()), Long.parseLong(voucherAllocationBatch.getVoucherEndNo())))) {
                customerOrderMapper.updateStatusByAllocationCode(voucherAllocationBatch.getVoucherAllocationCode());
            }
        }

    }

    private Boolean checkVoucherAndRange(List<String> voucherNumVCR, long start, long end) {
        for (long i = start; i <= end; i++) {
            boolean ok = false;
            for (String s : voucherNumVCR) {
                if (i == Long.parseLong(s)) {
                    ok = true;
                    break;
                }
            }
            if (!ok) {
                return false;
            }
        }
        return true;
    }

    private void voucherVCE(IssueHandlingDetails detail) {
        ApproveNodeRecord approveNodeRecord = approveNodeRecordMapper.selectNewNoteVCE(detail.getVoucherCode(), ApproveNodeRecordTypeEnum.RELEASE.getType());
        if (approveNodeRecord == null) {
            detail.setProcessStatus(3);
            detail.setResult("The voucher have not been allocated yet");
            return;
        }
        //if (Integer.parseInt(DateUtil.compareDateWithToday(approveNodeRecord.getCreateTime())) == -1) {
            //如果是今天以前
            detail.setProcessStatus(2);
            detail.setResult("Cancel success");
        //} else {
        //    detail.setProcessStatus(3);
        //    detail.setResult("The release time of this voucher does not support cancel");
        //}
    }

    private void voucherVCR(IssueHandlingDetails detail) {


        //判断是否是api来源的券,如果是api来源的券,则跳过校验MER-1956
        List<TransactionData> transactionData = transactionDataService.queryTransactionDataByVoucherCode(detail.getVoucherCode());
        //按照创建时间排序,获取最新一条transactionType为销售的数据
        transactionData.sort(Comparator.comparing(TransactionData::getCreateTime).reversed());
        TransactionData sellData = transactionData.stream().filter(x -> x.getTransactionType().equals(TransactionTypeEnum.GIFT_CARD_SELL.getCode())).findFirst().orElse(null);
        if (null != sellData ) {
            CustomerOrder customerOrder = customerOrderMapper.selectOne(CustomerOrder.builder().customerOrderCode(sellData.getTransactionId()).build());
            if (customerOrder != null && customerOrder.getStatus().equals(CustomerOrderStatusEnum.API.getStatus())) {
                detail.setProcessStatus(2);
                detail.setResult("Cancel success");
                return;
            }
        }



        String result = "This voucher is not released";
        List<VoucherAllocationBatch> allocateByVoucherCodes = voucherAllocationBatchService.getAllocateByVoucherCode(detail.getVoucherCode());
        if (CollectionUtils.isEmpty(allocateByVoucherCodes)) {
            detail.setProcessStatus(3);
            detail.setResult(result);
            return;
        }

        VoucherAllocation voucherAllocation1 = null;
        for (VoucherAllocationBatch allocateByVoucherCode : allocateByVoucherCodes) {
            VoucherAllocation voucherAllocation = new VoucherAllocation();
            voucherAllocation.setVoucherAllocationCode(allocateByVoucherCode.getVoucherAllocationCode());
            VoucherAllocation voucherAllocation2 = voucherAllocationMapper.selectOne(voucherAllocation);
            if (voucherAllocation2.getBusinessType().equals(VoucherAllocationBusinessTypeEnum.CUSTOMERORDER.code())) {
                voucherAllocation1 = voucherAllocation2;
                break;
            }
        }
        if (voucherAllocation1 == null || voucherAllocation1.getSourceDataCode() == null) {
            detail.setProcessStatus(3);
            detail.setResult(result);
            return;
        }
        ApproveNodeRecord approveNodeRecord = approveNodeRecordMapper.selectNewNote(voucherAllocation1.getSourceDataCode(), ApproveNodeRecordTypeEnum.RELEASE.getType());

        if (approveNodeRecord == null) {
            detail.setProcessStatus(3);
            detail.setResult(result);
            return;
        }
        //if (Integer.parseInt(DateUtil.compareDateWithToday(approveNodeRecord.getCreateTime())) == -1) {
            //如果是今天以前
//            Outlet ownerOutlet = outletMapper.selectOne(Outlet.builder().outletCode(voucherAllocation1.getVoucherOwnerCode()).build());
//			voucherService.cancelSales(ownerOutlet.getOutletCode(), ownerOutlet.getOutletType(), Collections.singletonList(detail.getVoucherCode()),
//					GvcoreConstants.STATUS_DISABLE, null);
            detail.setProcessStatus(2);
            detail.setResult("Cancel success");
        //} else {
        //    detail.setProcessStatus(3);
        //    detail.setResult("The release time of this voucher does not support cancel");
        //}
    }
}


