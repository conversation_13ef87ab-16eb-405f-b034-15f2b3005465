package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.customerpaymentmethod.*;
import com.gtech.gvcore.common.response.customerpaymentmethod.CustomerPaymentMethodResponse;

/**
 * <AUTHOR>
 * @Date 2022/2/11 15:27
 */
public interface CustomerPaymentMethodService {

    Result<Void> createCustomerPaymentMethod(CreateCustomerPaymentMethodRequest param);

    Result<Void> updateCustomerPaymentMethod(UpdateCustomerPaymentMethodRequest param);

    Result<Void> deleteCustomerPaymentMethod(DeleteCustomerPaymentMethodRequest param);

    Result<Void> deleteCustomerPaymentMethodByCustomer(String customer);

    PageResult<CustomerPaymentMethodResponse> queryCustomerPaymentMethodList(QueryCustomerPaymentMethodRequest param);

    CustomerPaymentMethodResponse getCustomerPaymentMethod(GetCustomerPaymentMethodRequest param);



}
