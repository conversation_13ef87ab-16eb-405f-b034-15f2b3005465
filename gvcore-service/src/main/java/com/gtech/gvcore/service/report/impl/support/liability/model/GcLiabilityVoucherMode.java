package com.gtech.gvcore.service.report.impl.support.liability.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName LiabilityVoucherMode
 * @Description LiabilityVoucherMode
 * <AUTHOR>
 * @Date 2023/4/20 15:55
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GcLiabilityVoucherMode {

    @Column(name = "id")
    private Long id;

    @Column(name = "card_number")
    private String cardNumber;

    @Column(name = "cpg_code")
    private String cpgCode;

    @Column(name = "denomination")
    private BigDecimal denomination;

    @Column(name = "expiry_time")
    private Date expiryTime;

    @Column(name = "balance")
    private BigDecimal balance;

    @Column(name = "status")
    private String status;

    @Column(name = "management_status")
    private String managementStatus;

    @Column(name = "activation_deadline")
    private Date activationDeadline;

    @Column(name = "activation_extension_count")
    private Integer activationExtensionCount;

    @Column(name = "sales_outlet")
    private String salesOutlet;

    @Column(name = "issuer_code")
    private String issuerCode;
}
