package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/20 14:25
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class BookletInventoryDetailedBean {

    @ExcelProperty(index = 0,value = "Issuer")
    private String issuer;

    @ExcelProperty(index = 1,value = "Merchant Name")
    private String merchant;

    @ExcelProperty(index = 2,value = "Outlet Name")
    private String outlet;

    @ExcelProperty(index = 3,value = "Voucher Program Group")
    private String voucherProgramGroup;

    @ExcelProperty(index = 4,value = "Booklet Number")
    private String bookletNumber;

    @ExcelProperty(index = 5,value = "Start Voucher Number")
    private String startCardNumber;

    @ExcelProperty(index = 6,value = "End Voucher Number")
    private String endCardNumber;

    @ExcelProperty(index = 7,value = "Voucher Count", converter = ExportExcelNumberConverter.class)
    private String cardCount;

    @ExcelProperty(index = 8,value = "Booklet Status")
    private String bookletStatus;

    @ExcelProperty(index = 9,value = "Expiry Date")
    private String expiryDate;

}
