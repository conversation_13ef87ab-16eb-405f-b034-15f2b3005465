package com.gtech.gvcore.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.enums.BookletStatusEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.enums.VoucherCirculationStatusEnum;
import com.gtech.gvcore.common.enums.VoucherStatusEnum;
import com.gtech.gvcore.common.request.transactiondata.CreateTransactionDataRequest;
import com.gtech.gvcore.common.request.voucher.CreateVoucherRequest;
import com.gtech.gvcore.common.request.voucherbatch.CreateVoucherBatchRequest;
import com.gtech.gvcore.common.request.voucherbooklet.VoucherActivateUpdateBookletStatusDto;
import com.gtech.gvcore.dao.mapper.VoucherBatchMapper;
import com.gtech.gvcore.dao.mapper.VoucherBookletMapper;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.dao.model.VoucherBatch;
import com.gtech.gvcore.dao.model.VoucherBooklet;
import com.gtech.gvcore.helper.VoucherNumberHelper;
import com.gtech.gvcore.service.TransactionDataService;
import com.gtech.gvcore.service.VoucherBookletService;
import com.gtech.gvcore.service.VoucherService;
import com.gtech.gvcore.threadpool.ThreadPoolCenter;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @Date 2022/3/2 13:53
 */
@Service
@Slf4j
public class VoucherBookletServiceImpl implements VoucherBookletService {

    @Autowired
    private VoucherBookletMapper voucherBookletMapper;


    @Lazy
    @Autowired
    private VoucherService voucherService;

    @Autowired
    private VoucherNumberHelper voucherBatchHelper;

    @Autowired
    private VoucherBatchMapper voucherBatchMapper;

    @Autowired
    private RedisTemplate redisTemplate;


    @Autowired
    private TransactionDataService transactionDataService;


    public static final String RE = "[a-zA-Z]";

    public static final String YEAR = "2022";
    public static final String HEAD = "9001";
    public static final String AUTO_INCR = "0000000001";
    public static final String REDIS_HEAD = "GV:VOUCHER:";


    public static final Integer BATCH_GENERATING = 0;
    public static final Integer BATCH_GENERATED = 1;
    public static final Integer BATCH_PUBLISHED = 2;
    public static final Integer BATCH_COMPLETED = 3;
    public static final Integer BATCH_FAILURE = 4;


    public static final Integer VOUCHER_NEWLY_GENERATED = 0;
    public static final Integer VOUCHER_ACTIVATED = 1;
    public static final Integer VOUCHER_USED = 2;
    public static final Integer VOUCHER_CANCELLED = 3;
    public static final Integer VOUCHER_EXPIRED = 4;


    //@Async("voucherBatch")
    @Override
    public void createVoucherBooklet(CreateVoucherBatchRequest batchRequest, String bookletCode,String createUser) {
        VoucherBooklet voucherBooklet = BeanCopyUtils.jsonCopyBean(batchRequest, VoucherBooklet.class);

        String barCode = voucherBatchHelper.barCode27Bit(bookletCode);
        voucherBooklet.setBookletCode(bookletCode);
        voucherBooklet.setBookletBarcode(barCode);
        voucherBooklet.setCreateTime(new Date());
        voucherBooklet.setStatus(BookletStatusEnum.GENERATED.getCode());
        voucherBookletMapper.insert(voucherBooklet);

        Integer bookletPerNum = batchRequest.getBookletPerNum();
        //每包voucher数量
        for (int i = 0; i < bookletPerNum; i++) {
            try {
                createVoucher(batchRequest, voucherBooklet,createUser);
            }catch (Exception e){
                log.error("Create Voucher Error:{}",e.getMessage());
                //将状态更新为失败
                updateBatchStatusFailure(batchRequest);
            }
        }
    }

    private void updateBatchStatusFailure(CreateVoucherBatchRequest batchRequest) {
        VoucherBatch voucherBatch = new VoucherBatch();
        voucherBatch.setStatus(BATCH_FAILURE);
        Example example = new Example(VoucherBatch.class);
        example.createCriteria().andEqualTo(VoucherBatch.C_VOUCHER_BATCH_CODE, batchRequest.getVoucherBatchCode());
        voucherBatchMapper.updateByConditionSelective(voucherBatch,example);
    }

    @Override
    public int deleteByCondition(String voucherBatchCode) {

        Example example = new Example(VoucherBooklet.class);
        example.createCriteria().andEqualTo(VoucherBooklet.C_VOUCHER_BATCH_CODE,voucherBatchCode);

        return voucherBookletMapper.deleteByCondition(example);

    }

    private void createTransactionData(CreateVoucherBatchRequest batch,CreateVoucherRequest voucher,String createUser){
        //TODO invoiceNumber  outletCode
        CreateTransactionDataRequest request = new CreateTransactionDataRequest();
        request.setTransactionId(batch.getVoucherBatchCode());
        request.setApproveCode(batch.getApprovalCode());
        request.setInvoiceNumber(batch.getInvoiceNo());
        request.setIssuerCode(batch.getIssuerCode());
        request.setTransactionType(TransactionTypeEnum.GIFT_CARD_NEW_GENERATE.getCode());
        request.setMerchantCode("");
        request.setMopCode(voucher.getMopCode());
        request.setOutletCode("");
        request.setCpgCode(voucher.getCpgCode());
        request.setTransactionDate(new Date());
        request.setVoucherCode(voucher.getVoucherCode());
        request.setVoucherCodeNum(Long.valueOf(voucher.getVoucherCode().replaceAll("[a-zA-Z]", "")));
        request.setInitiatedBy("");
        request.setPosCode("");
        request.setBatchCode(batch.getVoucherBatchCode());
        request.setLoginSource("");
        request.setDenomination(voucher.getDenomination());
        request.setActualOutlet("");
        request.setForwardingEntityId("");
        request.setResponseMessage("Transaction successful.");
        request.setTransactionMode("");
        request.setCustomerCode("");
        request.setCustomerSalutation("");
        request.setCustomerFirstName("");
        request.setCustomerLastName("");
        request.setMobile("");
//        request.setOtherInputParameter("");
        request.setSuccessOrFailure("0");
        request.setCreateUser(createUser);
        request.setCreateTime(new Date());
        request.setVoucherEffectiveDate(batch.getVoucherEffectiveDate());
        transactionDataService.createTransactionData(request);
    }


    private void createVoucher(CreateVoucherBatchRequest batchRequest, VoucherBooklet voucherBooklet,String createUser)  {
        //从redis中根据bookletCode获取起始值
        String voucherCode = voucherBatchHelper.voucherCodePhysical(voucherBooklet.getBookletCode());
        CreateVoucherRequest voucher = null;
//        try {
            createVoucherRequest(batchRequest, voucherBooklet, voucherCode,createUser);

//        } catch (PersistenceException e) {
//            log.error("Create voucherLog or transactionData PersistenceException error:{}",e.getMessage());
//        } catch (Exception e){
//            log.error("Create voucherLog or transactionData error:{}",e.getMessage());
//        }

        //统计数量
        redisTemplate.opsForValue().increment(REDIS_HEAD + batchRequest.getVoucherBatchCode());


        //判断是否完成，如果完成，改变状态为   已生成
        if (batchRequest.getVoucherNum().equals(redisTemplate.opsForValue().get(REDIS_HEAD + batchRequest.getVoucherBatchCode()))) {
            VoucherBatch voucherBatch = new VoucherBatch();
            voucherBatch.setStatus(BATCH_GENERATED);
            Example example = new Example(VoucherBatch.class);
            example.createCriteria().andEqualTo(VoucherBatch.C_VOUCHER_BATCH_CODE, batchRequest.getVoucherBatchCode());
            voucherBatchMapper.updateByConditionSelective(voucherBatch, example);
            //删除用于判断是否完成的key
            redisTemplate.delete(REDIS_HEAD + batchRequest.getVoucherBatchCode());
        }

    }


    private CreateVoucherRequest createVoucherRequest(CreateVoucherBatchRequest batchRequest, VoucherBooklet voucherBooklet, String voucherCode,String createUser) {
        CreateVoucherRequest voucherRequest = new CreateVoucherRequest();
        voucherRequest.setIssuerCode(batchRequest.getIssuerCode());
        voucherRequest.setVoucherBatchCode(batchRequest.getVoucherBatchCode());
        voucherRequest.setBookletCode(voucherBooklet.getBookletCode());
        voucherRequest.setVoucherCode(voucherCode);
        voucherRequest.setCpgCode(batchRequest.getCpgCode());
        voucherRequest.setMopCode(batchRequest.getMopCode());
        voucherRequest.setDenomination(batchRequest.getDenomination());
        voucherRequest.setVoucherBarcode(voucherBatchHelper.barCode27Bit(voucherCode));
        voucherRequest.setVoucherEffectiveDate(batchRequest.getVoucherEffectiveDate());
        //新生成
        voucherRequest.setStatus(VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
        //新生成
        voucherRequest.setCirculationStatus(VoucherCirculationStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
        voucherRequest.setVoucherActiveCode(batchRequest.getArticleCode());
        voucherRequest.setPermissionCode(batchRequest.getPermissionCode());
        voucherRequest.setCreateTime(new Date());
        voucherRequest.setCreateUser(batchRequest.getCreateUser());
        voucherRequest.setVoucherCodeNum(Long.valueOf(voucherRequest.getVoucherCode().replaceAll(RE,"")));
        voucherRequest.setBookletCodeNum(Long.valueOf(voucherRequest.getBookletCode().replaceAll(RE,"")));
        voucherService.createVoucher(voucherRequest);

        ThreadPoolCenter.commonThreadPoolExecute(() -> {
            //交易流水
            createTransactionData(batchRequest, voucherRequest, createUser);
        });
        return voucherRequest;
    }

    @Override
	public List<VoucherBooklet> queryBookletByCodeList(List<String> bookletCodeList) {
		if (CollectionUtils.isEmpty(bookletCodeList)) {
			return new ArrayList<>();
		}
		try {
			Example example = new Example(VoucherBooklet.class);
			example.createCriteria().andIn(VoucherBooklet.C_BOOKLET_CODE, bookletCodeList);
			return voucherBookletMapper.selectByCondition(example);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return new ArrayList<>();
	}

    @Override
    public String queryMaxBooklet(String bookletCode) {

        return voucherBookletMapper.queryMaxBooklet(bookletCode);
    }

    @Override
    public void voucherActivateUpdateBookletStatus(VoucherActivateUpdateBookletStatusDto dto) {


        String voucherCode = StringUtil.EMPTY;
        String bookletCode = StringUtil.EMPTY;
        String voucherBatch = StringUtil.EMPTY;

        ArrayList<String> bookletCodes = new ArrayList<>();




        if (dto.getType().equals("0")){
            List<Voucher> vouchers = voucherService.queryByVoucherCodeList(null, dto.getVoucherCode());
            List<String> collect = vouchers.stream().map(x -> x.getBookletCode()).distinct().collect(Collectors.toList());
            bookletCodes.addAll(collect);
        }else if (dto.getType().equals("1")){
            bookletCode = dto.getBookletCode();
            bookletCodes.add(bookletCode);
        }else if (dto.getType().equals("2")){
            voucherBatch = dto.getVoucherBatchCode();
        }else if (dto.getType().equals("3")){
            bookletCodes.addAll(dto.getBookletCodeList());
        }

        Example example = new Example(VoucherBooklet.class);
        Example.Criteria criteria = example.createCriteria();

        if (StringUtil.isNotEmpty(voucherBatch)){
            criteria.andEqualTo(VoucherBooklet.C_VOUCHER_BATCH_CODE,voucherBatch);
        } else{
            criteria.andIn(VoucherBooklet.C_BOOKLET_CODE,bookletCodes);
        }

        voucherBookletMapper.updateByConditionSelective(VoucherBooklet.builder().status(dto.getStatusEnum().getCode()).build(),example);


    }

    @Override
    public VoucherBooklet getBookletByCode(String bookletCode) {
        if(StringUtil.isBlank(bookletCode)){
            return null;
        }
        VoucherBooklet voucher = new VoucherBooklet();
        voucher.setBookletCode(bookletCode);
        return voucherBookletMapper.selectOne(voucher);
    }


}
