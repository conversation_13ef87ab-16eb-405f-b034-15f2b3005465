package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/20 13:43
 * @Description: Gift Card Extend Expiry Report Detail Bean
 */
@Getter
@Setter
@Accessors(chain = true)
public class GcExtendExpiryForDetailBean {

    /**
     * Gift Card Number
     */
    @ExcelProperty(value = "Gift Card Number")
    private String giftCardNumber;

    /**
     * Merchant
     */
    @ExcelProperty(value = "Merchant")
    private String merchant;

    /**
     * Merchant Outlet
     */
    @ExcelProperty(value = "Merchant Outlet")
    private String merchantOutlet;

    /**
     * Transaction Type
     */
    @ExcelProperty(value = "Transaction Type")
    private String transactionType;

    /**
     * Gift Card Program Group
     */
    @ExcelProperty(value = "Gift Card Program Group")
    private String giftCardProgramGroup;

    /**
     * Transaction Date
     */
    @ExcelProperty(value = "Transaction Date")
    private String transactionDate;

    /**
     * Issuance Date
     */
    @ExcelProperty(value = "Issuance Date")
    private String issuanceDate;

    /**
     * Activation Period Ended
     */
    @ExcelProperty(value = "Activation Period Ended")
    private String activationPeriodEnded;

    /**
     * Grace Period Ended
     */
    @ExcelProperty(value = "Grace Period Ended")
    private String gracePeriodEnded;

    /**
     * Customer Name
     */
    @ExcelProperty(value = "Customer Name")
    private String customerName;

    /**
     * Source
     */
    @ExcelProperty(value = "Source")
    private String source;
}
