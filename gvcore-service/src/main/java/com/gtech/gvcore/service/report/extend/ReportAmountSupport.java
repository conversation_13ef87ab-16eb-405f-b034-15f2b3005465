package com.gtech.gvcore.service.report.extend;

import com.gtech.gvcore.common.utils.GvConvertUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @ClassName ReportAmountSupport
 * @Description
 * <AUTHOR>
 * @Date 2023/2/15 18:43
 * @Version V1.0
 **/
public interface ReportAmountSupport {

    default String toAmount(String amount) {

        if (StringUtils.isBlank(amount)) return "0";

        return toAmount(new BigDecimal(amount));
    }

    default String toAmount(BigDecimal bigDecimal) {

        return GvConvertUtils.toBigDecimal(bigDecimal, BigDecimal.ZERO).setScale(0, RoundingMode.DOWN).toPlainString();
    }

    default String toAmount(Number amount) {

        return toAmount(String.valueOf(amount));
    }

}
