package com.gtech.gvcore.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.CategoryTypeEnum;
import com.gtech.gvcore.common.enums.ProductCategoryDiscountTypeEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.enums.VpgDisableGenerationEnum;
import com.gtech.gvcore.common.request.productcategory.*;
import com.gtech.gvcore.common.response.productcategory.*;
import com.gtech.gvcore.common.utils.UUIDUtils;
import com.gtech.gvcore.dao.mapper.GcProductCategoryMapper;
import com.gtech.gvcore.dao.model.*;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.giftcard.masterdata.gcpg.service.GcCpgService;
import com.gtech.gvcore.service.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * GC商品类别Service实现类
 */
@Service
public class GcProductCategoryServiceImpl implements GcProductCategoryService {

    @Autowired
    private GcProductCategoryMapper gcProductCategoryMapper;
    @Autowired
    private ProductCategoryCpgService productCategoryCpgService;

    @Autowired
    private GcCpgService cpgService;

    @Autowired
    private ProductCategoryDisscountService productCategoryDisscountService;

    @Autowired
    private ProductCategoryDisscountDetailsService productCategoryDisscountDetailsService;

    @Autowired
    private GcArticleMopService articleMopService;

    @Override
    public Result<CreateProductCategoryResponse> createProductCategory(CreateProductCategoryRequest request) {

        GcProductCategory productCategory = new GcProductCategory();
        productCategory.setCategoryName(request.getCategoryName());
        productCategory.setIssuerCode(request.getIssuerCode());
        int i = gcProductCategoryMapper.selectCount(productCategory);
        if (i > 0) {
            return Result.failed(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                    ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
        }

        BeanUtils.copyProperties(request, productCategory);
        productCategory.setProductCategoryCode(UUIDUtils.generateCode());
        productCategory.setStatus(GvcoreConstants.STATUS_DISABLE);
        productCategory.setCreateTime(new Date());
        try {
            gcProductCategoryMapper.insertSelective(productCategory);
        } catch (DuplicateKeyException e) {
            return Result.failed(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                    ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
        }

        GcProductCategory category = new GcProductCategory();
        category.setProductCategoryCode(productCategory.getProductCategoryCode());
        category = gcProductCategoryMapper.selectOne(category);
        CreateProductCategoryResponse response = new CreateProductCategoryResponse();
        BeanUtils.copyProperties(category, response);
        return Result.ok(response);
    }

    @Override
    public Result<Void> updateProductCategory(UpdateProductCategoryRequest request) {

        GcProductCategory productCategory = gcProductCategoryMapper.selectByPrimaryKey(request.getId());
        if (productCategory == null) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }

        GcProductCategory category = new GcProductCategory();
        if (!productCategory.getCategoryName().equalsIgnoreCase(request.getCategoryName())) {
            category.setCategoryName(request.getCategoryName());
            int i = gcProductCategoryMapper.selectCount(category);
            if (i > 0) {
                return Result.failed(ResultErrorCodeEnum.NAME_ALREADY_USED.code(),
                        ResultErrorCodeEnum.NAME_ALREADY_USED.desc());
            }
        }

        BeanUtils.copyProperties(request, category);
        category.setUpdateTime(new Date());
        int i = gcProductCategoryMapper.updateByPrimaryKeySelective(category);
        if (i == 0) {
            return Result.failed(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(),
                    ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.desc());
        }

        return Result.ok();
    }

    @Override
    public Result<Void> updateProductCategoryStatus(UpdateProductCategoryStatusRequest request) {

        GcProductCategory productCategory = new GcProductCategory();
        productCategory.setId(request.getId());
        int i = gcProductCategoryMapper.selectCount(productCategory);
        if (i == 0) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }

        productCategory.setStatus(request.getStatus());
        productCategory.setUpdateUser(request.getUpdateUser());
        productCategory.setUpdateTime(new Date());
        i = gcProductCategoryMapper.updateByPrimaryKeySelective(productCategory);
        if (i == 0) {
            return Result.failed(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(),
                    ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.desc());
        }

        return Result.ok();
    }

    @Override
    public PageResult<QueryProductCategoryByPageResponse> queryProductCategoryByPage(
            QueryProductCategoryByPageRequest request) {

        GcProductCategory productCategory = new GcProductCategory();
        productCategory.setIssuerCode(request.getIssuerCode());
        productCategory.setCategoryName(request.getCategoryName());

        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<GcProductCategory> list = gcProductCategoryMapper.selectSelective(productCategory);
        PageInfo<GcProductCategory> pageInfo = new PageInfo<>(list);

        List<QueryProductCategoryByPageResponse> responses = new ArrayList<>(request.getPageSize());
        list.stream().forEach(item -> {
            QueryProductCategoryByPageResponse response = new QueryProductCategoryByPageResponse();
            BeanUtils.copyProperties(item, response);
            responses.add(response);
        });

        return PageResult.ok(responses, pageInfo.getTotal());
    }

    @Override
    public Result<QueryProductCategoryCpgResponse> queryProductCategoryCpg(QueryProductCategoryCpgRequest request) {

        GcProductCategory productCategory = new GcProductCategory();
        productCategory.setProductCategoryCode(request.getProductCategoryCode());
        productCategory = gcProductCategoryMapper.selectOne(productCategory);
        if (productCategory == null) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }

        List<ProductCategoryCpg> categoryCpgs = productCategoryCpgService.queryByProductCategoryCode(
                productCategory.getProductCategoryCode(), GvcoreConstants.DELETE_STATUS_DISABLE,"gc");


        Map<String, GcCpg> cpgMap = cpgService.queryCpgMapByCpgCodeList(
                categoryCpgs.stream().map(ProductCategoryCpg::getCpgCode).collect(Collectors.toList()));

        QueryProductCategoryCpgResponse response = new QueryProductCategoryCpgResponse();
        response.setProductCategoryCode(productCategory.getProductCategoryCode());
        response.setCategoryName(productCategory.getCategoryName());
        List<ProductCategoryCpgVo> cpgList = new ArrayList<>(categoryCpgs.size());
        for (ProductCategoryCpg productCategoryCpg : categoryCpgs) {
            ProductCategoryCpgVo categoryCpgVo = new ProductCategoryCpgVo();
            categoryCpgVo.setId(productCategoryCpg.getId());
            categoryCpgVo.setProductCategoryCpgCode(productCategoryCpg.getProductCategoryCpgCode());
            categoryCpgVo.setCpgCode(productCategoryCpg.getCpgCode());
            categoryCpgVo.setStatus(productCategoryCpg.getStatus());
            categoryCpgVo.setCpgType(productCategoryCpg.getCpgType());
            GcCpg cpg = cpgMap.get(productCategoryCpg.getCpgCode());
            if (cpg != null) {
                categoryCpgVo.setCpgName(cpg.getCpgName());
                categoryCpgVo.setDenomination(cpg.getDenomination());


                /*if (StringUtils.isNotBlank(cpg.getDisableGeneration())){
                    categoryCpgVo.setDisableGeneration(cpg.getDisableGeneration());
                } else {
                    categoryCpgVo.setDisableGeneration(VpgDisableGenerationEnum.ENABLE.code());
                }*/

                GcArticleMop articleMop = articleMopService.queryByArticleMopCode(cpg.getArticleMopCode());
                if (articleMop != null) {
                    categoryCpgVo.setMopCode(articleMop.getMopCode());
                }
            }
            cpgList.add(categoryCpgVo);
        }
        response.setCpgList(cpgList);
        return Result.ok(response);
    }


    @Transactional
    @Override
    public Result<Void> createOrUpdateProductCategoryCpg(CreateOrUpdateProductCategoryCpgRequest request) {

        GcProductCategory productCategory = new GcProductCategory();
        productCategory.setProductCategoryCode(request.getProductCategoryCode());
        productCategory = gcProductCategoryMapper.selectOne(productCategory);
        if (productCategory == null) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }

        if (CollectionUtils.isNotEmpty(request.getCpgList())) {
            Map<String, GcCpg> cpgMap = cpgService.queryCpgMapByCpgCodeList(request.getCpgList().stream()
                    .map(ProductCategoryCpgParamter::getCpgCode).collect(Collectors.toList()));
            for (ProductCategoryCpgParamter categoryCpg : request.getCpgList()) {
                if (!cpgMap.containsKey(categoryCpg.getCpgCode())) {
                    return Result.failed(ResultErrorCodeEnum.NO_CPG_DATA_FOUND.code(),
                            ResultErrorCodeEnum.NO_CPG_DATA_FOUND.desc());
                }
            }
        }


        List<ProductCategoryCpg> categoryCpgs = productCategoryCpgService
                .queryByProductCategoryCode(productCategory.getProductCategoryCode(), null,"gc");

        Map<String, ProductCategoryCpg> cpgCategoryMap = categoryCpgs.stream()
                .collect(Collectors.toMap(ProductCategoryCpg::getCpgCode, v -> v));
        Date now = new Date();
        List<ProductCategoryCpg> insertList = new ArrayList<>(request.getCpgList().size());
        for (ProductCategoryCpgParamter categoryCpg : request.getCpgList()) {
            ProductCategoryCpg productCategoryCpg = cpgCategoryMap.remove(categoryCpg.getCpgCode());
            if (productCategoryCpg == null) {
                ProductCategoryCpg insertCategoryCpg = new ProductCategoryCpg();
                insertCategoryCpg.setProductCategoryCode(request.getProductCategoryCode());
                insertCategoryCpg.setCpgCode(categoryCpg.getCpgCode());
                insertCategoryCpg.setStatus(categoryCpg.getStatus());
                insertCategoryCpg.setCreateTime(now);
                insertCategoryCpg.setCreateUser(request.getCreateUser());
                insertCategoryCpg.setCpgType(CategoryTypeEnum.GC.getCode());
                insertList.add(insertCategoryCpg);
            } else if (productCategoryCpg.getStatus().intValue() != categoryCpg.getStatus()) {
                ProductCategoryCpg updateCategoryCpg = new ProductCategoryCpg();
                updateCategoryCpg.setId(productCategoryCpg.getId());
                updateCategoryCpg.setStatus(categoryCpg.getStatus());
                updateCategoryCpg.setUpdateTime(now);
                updateCategoryCpg.setUpdateUser(request.getCreateUser());
                updateCategoryCpg.setCpgType(CategoryTypeEnum.GC.getCode());
                productCategoryCpgService.updateByPrimaryKeySelective(updateCategoryCpg);
            }
        }

        if (!insertList.isEmpty()) {
            productCategoryCpgService.insertList(insertList);
        }

        for (Map.Entry<String, ProductCategoryCpg> entry : cpgCategoryMap.entrySet()) {
            if (GvcoreConstants.DELETE_STATUS_ENABLE.equals(entry.getValue().getDeleteStatus())) {
                continue;
            }
            ProductCategoryCpg deleteCategoryCpg = new ProductCategoryCpg();
            deleteCategoryCpg.setId(entry.getValue().getId());
            deleteCategoryCpg.setUpdateTime(now);
            deleteCategoryCpg.setUpdateUser(request.getCreateUser());
            productCategoryCpgService.deleteStatusByPrimaryKey(deleteCategoryCpg);
        }

        return Result.ok();
    }

    @Override
    public Result<QueryProductCategoryDisscountResponse> queryProductCategoryDisscount(
            QueryProductCategoryDisscountRequest request) {

        GcProductCategory productCategory = new GcProductCategory();
        productCategory.setProductCategoryCode(request.getProductCategoryCode());
        productCategory = gcProductCategoryMapper.selectOne(productCategory);
        if (productCategory == null) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }

        QueryProductCategoryDisscountResponse response = new QueryProductCategoryDisscountResponse();
        response.setProductCategoryCode(productCategory.getProductCategoryCode());
        response.setCategoryName(productCategory.getCategoryName());
        response.setDisscountDetailList(Collections.emptyList());
        ProductCategoryDisscount disscount = productCategoryDisscountService
                .queryByProductCategoryCode(request.getProductCategoryCode());
        if (disscount == null) {
            return Result.ok(response);
        }

        response.setValidFrom(disscount.getValidFrom());
        response.setValidUpto(disscount.getValidUpto());
        response.setStatus(disscount.getStatus());

        List<ProductCategoryDisscountDetails> detailLilst = productCategoryDisscountDetailsService
                .queryByProductCategoryDisscountCode(disscount.getProductCategoryDisscountCode(),
                        GvcoreConstants.DELETE_STATUS_DISABLE);
        if (CollectionUtils.isEmpty(detailLilst)) {
            return Result.ok(response);
        }

        detailLilst.sort(Comparator.comparing(ProductCategoryDisscountDetails::getFromPurchaseValue));
        List<ProductCategoryDisscountDetailVo> disscountDetailList = new ArrayList<>(detailLilst.size());
        for (ProductCategoryDisscountDetails productCategoryDisscountDetail : detailLilst) {
            ProductCategoryDisscountDetailVo detailVo = new ProductCategoryDisscountDetailVo();
            BeanUtils.copyProperties(productCategoryDisscountDetail, detailVo);
            disscountDetailList.add(detailVo);
        }
        response.setDisscountDetailList(disscountDetailList);

        return Result.ok(response);
    }

    @Transactional
    @Override
    public Result<Void> createOrUpdateProductCategoryDisscount(CreateOrUpdateProductCategoryDisscountRequest request) {

        Result<Void> result = checkDisscountDetailList(request);
        if (!result.isSuccess()) {
            return result;
        }

        GcProductCategory productCategory = new GcProductCategory();
        productCategory.setProductCategoryCode(request.getProductCategoryCode());
        productCategory = gcProductCategoryMapper.selectOne(productCategory);
        if (productCategory == null) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }

        ProductCategoryDisscount disscount = productCategoryDisscountService
                .queryByProductCategoryCode(request.getProductCategoryCode());
        Map<Long, ProductCategoryDisscountDetails> idMap = null;
        if (disscount == null) {
            idMap = Collections.emptyMap();
        } else {
            List<ProductCategoryDisscountDetails> detailLilst = productCategoryDisscountDetailsService
                    .queryByProductCategoryDisscountCode(disscount.getProductCategoryDisscountCode(), null);
            idMap = detailLilst.stream().collect(Collectors.toMap(ProductCategoryDisscountDetails::getId, v -> v));
        }


        Date now = new Date();
        disscount = insertOrUpdateDisscount(request, disscount, now);

        List<ProductCategoryDisscountDetails> insertList = new ArrayList<>(request.getDisscountDetailList().size());
        for (DisscountDetailParamter detailParamter : request.getDisscountDetailList()) {
            ProductCategoryDisscountDetails detail = idMap.remove(detailParamter.getId());
            if (detail == null) {
                ProductCategoryDisscountDetails saveDetail = new ProductCategoryDisscountDetails();
                BeanUtils.copyProperties(detailParamter, saveDetail);
                saveDetail.setProductCategoryDisscountCode(disscount.getProductCategoryDisscountCode());
                saveDetail.setCreateTime(now);
                saveDetail.setCreateUser(request.getCreateUser());
                insertList.add(saveDetail);
            } else if (isUpdateDisscountDetail(detailParamter, detail)) {
                ProductCategoryDisscountDetails updateDetail = new ProductCategoryDisscountDetails();
                BeanUtils.copyProperties(detailParamter, updateDetail);
                updateDetail.setId(detail.getId());
                updateDetail.setUpdateTime(now);
                updateDetail.setUpdateUser(request.getCreateUser());
                productCategoryDisscountDetailsService.updateById(updateDetail);
            }
        }

        if (!insertList.isEmpty()) {
            productCategoryDisscountDetailsService.insertList(insertList);
        }

        for (Map.Entry<Long, ProductCategoryDisscountDetails> entry : idMap.entrySet()) {
            if (GvcoreConstants.DELETE_STATUS_ENABLE.equals(entry.getValue().getDeleteStatus())) {
                continue;
            }
            ProductCategoryDisscountDetails delete = new ProductCategoryDisscountDetails();
            delete.setId(entry.getValue().getId());
            delete.setUpdateTime(now);
            delete.setUpdateUser(request.getCreateUser());
            productCategoryDisscountDetailsService.deleteStatusByPrimaryKey(delete);
        }

        return Result.ok();
    }

    /**
     *
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月25日
     */
    private Result<Void> checkDisscountDetailList(CreateOrUpdateProductCategoryDisscountRequest request) {

        if (checkValidDate(request)) {
            return Result.failed(ResultErrorCodeEnum.DISSCOUNT_VALID_DATE_ERROR.code(),
                    ResultErrorCodeEnum.DISSCOUNT_VALID_DATE_ERROR.desc());
        }

        if (CollectionUtils.isNotEmpty(request.getDisscountDetailList())) {
            DisscountDetailParamter paramter = null;
            for (DisscountDetailParamter detailParamter : request.getDisscountDetailList()) {
                if (paramter != null
                        && paramter.getUptoPurchaseValue().compareTo(detailParamter.getFromPurchaseValue()) >= 0) {
                    return Result.failed(ResultErrorCodeEnum.DISSCOUNT_DETAIL_FROM_VALUE_ERROR.code(),
                            ResultErrorCodeEnum.DISSCOUNT_DETAIL_FROM_VALUE_ERROR.desc());
                }
                if (detailParamter.getUptoPurchaseValue().compareTo(detailParamter.getFromPurchaseValue()) <= 0) {
                    return Result.failed(ResultErrorCodeEnum.DISSCOUNT_DETAIL_UPTO_VALUE_ERROR.code(),
                            ResultErrorCodeEnum.DISSCOUNT_DETAIL_UPTO_VALUE_ERROR.desc());
                }

                detailParamter.setDiscountType(detailParamter.getDiscountType().toLowerCase());
                if (ProductCategoryDiscountTypeEnum.PERCENTAGE.equalsCode(detailParamter.getDiscountType())
                        && detailParamter.getMaximumDiscountValue() == null) {
                    return Result.failed(ResultErrorCodeEnum.PARAMTER_ERROR.code(),
                            "maximumDiscountValue can not be empty");
                }
                paramter = detailParamter;
            }
        }
        return Result.ok();
    }

    private boolean checkValidDate(CreateOrUpdateProductCategoryDisscountRequest request) {
        return request.getValidFrom() != null && request.getValidUpto() != null
                && request.getValidFrom().after(request.getValidUpto());
    }

    /**
     *
     * <AUTHOR>
     * @param request
     * @param disscount
     * @param now
     * @return
     * @date 2022年2月25日
     */
    private ProductCategoryDisscount insertOrUpdateDisscount(CreateOrUpdateProductCategoryDisscountRequest request,
                                                             ProductCategoryDisscount disscount, Date now) {
        if (disscount == null) {
            ProductCategoryDisscount insertDisscount = new ProductCategoryDisscount();
            BeanUtils.copyProperties(request, insertDisscount);
            insertDisscount.setCreateTime(now);
            insertDisscount.setCreateUser(request.getCreateUser());
            productCategoryDisscountService.insertOrUpdate(insertDisscount);
            disscount = insertDisscount;
        } else if (isUpdateDisscount(request, disscount)) {
            ProductCategoryDisscount updateDisscount = new ProductCategoryDisscount();
            BeanUtils.copyProperties(disscount, updateDisscount);
            updateDisscount.setId(disscount.getId());
            updateDisscount.setStatus(request.getStatus());
            updateDisscount.setValidFrom(request.getValidFrom());
            updateDisscount.setValidUpto(request.getValidUpto());
            updateDisscount.setUpdateTime(now);
            updateDisscount.setUpdateUser(request.getCreateUser());
            productCategoryDisscountService.insertOrUpdate(updateDisscount);
        }

        return disscount;
    }

    /**
     *
     * <AUTHOR>
     * @param request
     * @param disscount
     * @return
     * @date 2022年2月25日
     */
    private boolean isUpdateDisscount(CreateOrUpdateProductCategoryDisscountRequest request,
                                      ProductCategoryDisscount disscount) {
        StringUtils.equals("", "");
        return !dateEquals(request.getValidFrom(), disscount.getValidFrom())
                || !dateEquals(request.getValidUpto(), disscount.getValidUpto())
                || request.getStatus().compareTo(disscount.getStatus()) != 0;
    }

    /**
     *
     * <AUTHOR>
     * @param date1
     * @param date2
     * @return
     * @date 2022年3月17日
     */
    private boolean dateEquals(Date date1, Date date2) {
        if (date1 == date2) {
            return true;
        }
        if (date1 == null || date2 == null) {
            return false;
        }
        return date1.compareTo(date2) == 0;
    }

    /**
     *
     * <AUTHOR>
     * @param detailParamter
     * @param detail
     * @return
     * @date 2022年2月25日
     */
    private boolean isUpdateDisscountDetail(DisscountDetailParamter detailParamter,
                                            ProductCategoryDisscountDetails detail) {
        return detailParamter.getFromPurchaseValue().compareTo(detail.getFromPurchaseValue()) != 0
                || detailParamter.getUptoPurchaseValue().compareTo(detail.getUptoPurchaseValue()) != 0
                || !detailParamter.getDiscountType().equals(detail.getDiscountType())
                || detailParamter.getDiscount().compareTo(detail.getDiscount()) != 0
                || !((detailParamter.getMaximumDiscountValue() == null && detail.getMaximumDiscountValue() == null)
                || (detailParamter.getMaximumDiscountValue() != null && detail.getMaximumDiscountValue() != null
                && detailParamter.getMaximumDiscountValue().compareTo(detail
                .getMaximumDiscountValue()) == 0))
                || detailParamter.getStatus().compareTo(detail.getStatus()) != 0;
    }

    @Override
    public DiscountInfoResponse calculateDiscountAmount(CalculateDiscountAmountRequest request) {
        ProductCategoryDisscount disscount = productCategoryDisscountService.queryByProductCategoryCode(request.getProductCategoryCode());
        if (disscount == null) {
            throw new GTechBaseException(ResultErrorCodeEnum.CUSTOMERORDER_CALCULATEDISCOUNT_01.code(),
                    ResultErrorCodeEnum.CUSTOMERORDER_CALCULATEDISCOUNT_01.desc());
        }
        if (disscount.getStatus().intValue() != GvcoreConstants.STATUS_ENABLE) {
            throw new GTechBaseException(ResultErrorCodeEnum.CUSTOMERORDER_CALCULATEDISCOUNT_02.code(),
                    ResultErrorCodeEnum.CUSTOMERORDER_CALCULATEDISCOUNT_02.desc());
        }
        if (disscount.getValidFrom() != null && "1".equals(DateUtil.compareDateWithToday(disscount.getValidFrom()))) {
            throw new GTechBaseException(ResultErrorCodeEnum.CUSTOMERORDER_CALCULATEDISCOUNT_03.code(),
                    ResultErrorCodeEnum.CUSTOMERORDER_CALCULATEDISCOUNT_03.desc());
        }
        if (disscount.getValidUpto() != null && "-1".equals(DateUtil.compareDateWithToday(disscount.getValidUpto()))) {
            throw new GTechBaseException(ResultErrorCodeEnum.CUSTOMERORDER_CALCULATEDISCOUNT_04.code(),
                    ResultErrorCodeEnum.CUSTOMERORDER_CALCULATEDISCOUNT_04.desc());
        }
        List<ProductCategoryDisscountDetails> detailLilst = productCategoryDisscountDetailsService
                .queryByProductCategoryDisscountCode(disscount.getProductCategoryDisscountCode(), null);
        detailLilst = detailLilst.stream().filter(vo -> vo.getStatus().intValue() == GvcoreConstants.STATUS_ENABLE).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(detailLilst)) {
            throw new GTechBaseException(ResultErrorCodeEnum.CUSTOMERORDER_CALCULATEDISCOUNT_05.code(),
                    ResultErrorCodeEnum.CUSTOMERORDER_CALCULATEDISCOUNT_05.desc());
        }
        DiscountInfoResponse discountInfoResponse = new DiscountInfoResponse();
        BigDecimal amount = request.getAmount();
        for (ProductCategoryDisscountDetails productCategoryDisscountDetails : detailLilst) {
            BigDecimal from = productCategoryDisscountDetails.getFromPurchaseValue();
            BigDecimal to = productCategoryDisscountDetails.getUptoPurchaseValue();
            if (amount.compareTo(from) > -1 && amount.compareTo(to) < 1) {
                calculate(discountInfoResponse, amount, productCategoryDisscountDetails);
                break;
            }

        }
        return discountInfoResponse;
    }

    private void calculate(DiscountInfoResponse discountInfoResponse, BigDecimal amount, ProductCategoryDisscountDetails productCategoryDisscountDetails) {
        BigDecimal discount = productCategoryDisscountDetails.getDiscount();
        discountInfoResponse.setDiscount(productCategoryDisscountDetails.getDiscount());
        String discountType = productCategoryDisscountDetails.getDiscountType();
        discountInfoResponse.setDiscountType(discountType);
        if (discountType.equals(ProductCategoryDiscountTypeEnum.PERCENTAGE.code())) {
            BigDecimal discountAmount = amount.multiply(discount).divide(BigDecimal.valueOf(100), 0, RoundingMode.HALF_DOWN);
            BigDecimal max = productCategoryDisscountDetails.getMaximumDiscountValue();
            discountAmount = max == null || max.compareTo(discountAmount) > 0 ? discountAmount : max;
            discountInfoResponse.setDiscountAmount(discountAmount);
        } else {
            discountInfoResponse.setDiscountAmount(discount);
        }
    }
} 