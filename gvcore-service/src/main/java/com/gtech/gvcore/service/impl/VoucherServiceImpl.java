package com.gtech.gvcore.service.impl;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.SimpleTimeZone;
import java.util.TimeZone;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.gtech.gvcore.common.externalmapping.request.BulkCancelRedeemRequest;

import com.gtech.gvcore.common.request.transaction.*;
import com.gtech.gvcore.common.response.cancelredeem.APIBulkCancelRedeemResponse;
import com.gtech.gvcore.common.response.transaction.BulkCancelRedeemResponseV2;

import com.gtech.gvcore.dao.model.*;
import com.gtech.gvcore.service.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.gtech.basic.masterdata.web.entity.MasterDataValueEntity;
import com.gtech.basic.masterdata.web.service.MasterDataValueService;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.page.PageParam;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.cache.MasterDataCache;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.BookletStatusEnum;
import com.gtech.gvcore.common.enums.CardCurrencySymbolEnum;
import com.gtech.gvcore.common.enums.CpgTypeAutomaticActivateEnum;
import com.gtech.gvcore.common.enums.CustomerOrderStatusEnum;
import com.gtech.gvcore.common.enums.GvPosCardCreationTypeEnum;
import com.gtech.gvcore.common.enums.GvPosCommonResponseCodesEnum;
import com.gtech.gvcore.common.enums.GvPosTransactionTypesEnum;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.common.enums.OutletTypeEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.enums.SystemVoucherStatusEnum;
import com.gtech.gvcore.common.enums.TransactionChannelEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.enums.VerifyVoucherTypeEnum;
import com.gtech.gvcore.common.enums.VoucherCirculationStatusEnum;
import com.gtech.gvcore.common.enums.VoucherEnableDisablePosEnum;
import com.gtech.gvcore.common.enums.VoucherLogTypeEnum;
import com.gtech.gvcore.common.enums.VoucherOwnerTypeEnum;
import com.gtech.gvcore.common.enums.VoucherReceiveSourceTypeEnum;
import com.gtech.gvcore.common.enums.VoucherStatusEnableDisableEnum;
import com.gtech.gvcore.common.enums.VoucherStatusEnum;
import com.gtech.gvcore.common.enums.VoucherStatusPosEnum;
import com.gtech.gvcore.common.request.activateonly.ActivateOnlyParam;
import com.gtech.gvcore.common.request.cancelactivate.CancelActivateParam;
import com.gtech.gvcore.common.request.cancelredeem.CancelRedeemRequest;
import com.gtech.gvcore.common.request.cpg.GetCpgRequest;
import com.gtech.gvcore.common.request.cpg.GetCpgTypeRequest;
import com.gtech.gvcore.common.request.createandissue.CreateAndIssueParam;
import com.gtech.gvcore.common.request.customer.GetCustomerRequest;
import com.gtech.gvcore.common.request.customerorder.CreateCustomerOrderDetailsRequest;
import com.gtech.gvcore.common.request.customerorder.CreateCustomerOrderRequest;
import com.gtech.gvcore.common.request.distribution.QueryCpgInventoryDetailRequest;
import com.gtech.gvcore.common.request.distribution.QueryOwnedCpgInfoListRequest;
import com.gtech.gvcore.common.request.outlet.GetOutletRequest;
import com.gtech.gvcore.common.request.pos.GetPosRequest;
import com.gtech.gvcore.common.request.redemption.RedemptionRequest;
import com.gtech.gvcore.common.request.redemption.StartAndEndVoucher;
import com.gtech.gvcore.common.request.transactiondata.CreateTransactionDataRequest;
import com.gtech.gvcore.common.request.v3.TransactionsRequest;
import com.gtech.gvcore.common.request.voucher.CheckDynamicBarCodeRequest;
import com.gtech.gvcore.common.request.voucher.CreateVoucherRequest;
import com.gtech.gvcore.common.request.voucher.GetStartAndEndVoucherRequest;
import com.gtech.gvcore.common.request.voucher.GetVoucherInformationRequest;
import com.gtech.gvcore.common.request.voucher.UpdateVoucherStatusRequest;
import com.gtech.gvcore.common.request.voucher.VerifyVoucherInfo;
import com.gtech.gvcore.common.request.voucher.VerifyVoucherRequest;
import com.gtech.gvcore.common.request.voucherbatch.BarCodeToCodeRequest;
import com.gtech.gvcore.common.request.voucherbatch.GenerateDigitalVouchersRequest;
import com.gtech.gvcore.common.request.voucherbatch.GetVoucherBatchRequest;
import com.gtech.gvcore.common.request.voucherbatch.UpdateVoucherBatchStatusRequest;
import com.gtech.gvcore.common.request.voucherbooklet.VoucherActivateUpdateBookletStatusDto;
import com.gtech.gvcore.common.response.activateonly.ActivateOnlyGinseng;
import com.gtech.gvcore.common.response.cancelactivate.CancelActivateGinseng;
import com.gtech.gvcore.common.response.cancelcreateandissue.CancelCreateAndIssueGinseng;
import com.gtech.gvcore.common.response.cancelredeem.CancelRedeemResponse;
import com.gtech.gvcore.common.response.cpg.GetCpgResponse;
import com.gtech.gvcore.common.response.cpg.GetCpgTypeResponse;
import com.gtech.gvcore.common.response.createandissue.CreateAndIssueGinseng;
import com.gtech.gvcore.common.response.customer.CustomerResponse;
import com.gtech.gvcore.common.response.customerorder.GetCustomerOrderDetailsResponse;
import com.gtech.gvcore.common.response.distribution.CpgInventoryDetailResponse;
import com.gtech.gvcore.common.response.distribution.CpgInventoryResponse;
import com.gtech.gvcore.common.response.distribution.CustomerCpgEffectiveDateResult;
import com.gtech.gvcore.common.response.distribution.OwnedCpgInfoResponse;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.common.response.outletcpg.OutletCpgResponse;
import com.gtech.gvcore.common.response.pos.PosResponse;
import com.gtech.gvcore.common.response.redemption.RedemptionResponse;
import com.gtech.gvcore.common.response.redemption.StartAndEndVoucherResponse;
import com.gtech.gvcore.common.response.transaction.OnetimebarcodeResponse;
import com.gtech.gvcore.common.response.v3.CardFormats;
import com.gtech.gvcore.common.response.v3.CardsResponse;
import com.gtech.gvcore.common.response.v3.CreateAndIssueResponse;
import com.gtech.gvcore.common.response.v3.Holder;
import com.gtech.gvcore.common.response.voucher.CheckDynamicBarCodeResponse;
import com.gtech.gvcore.common.response.voucher.GetStartAndEndVoucherResponse;
import com.gtech.gvcore.common.response.voucher.GetVoucherInformationResponse;
import com.gtech.gvcore.common.response.voucher.VerifyResponse;
import com.gtech.gvcore.common.response.voucher.VerifyVoucherDto;
import com.gtech.gvcore.common.response.voucher.VerifyVoucherGinseng;
import com.gtech.gvcore.common.response.voucher.VerifyVoucherResponse;
import com.gtech.gvcore.common.response.voucher.VoucherInfo;
import com.gtech.gvcore.common.response.voucher.VoucherResponse;
import com.gtech.gvcore.common.response.voucherbatch.VoucherBatchResponse;
import com.gtech.gvcore.common.utils.ExceptionBuilder;
import com.gtech.gvcore.common.utils.GvConvertUtils;
import com.gtech.gvcore.components.CheckReceiveAndIssuanceComponent;
import com.gtech.gvcore.components.TaskProgressManager;
import com.gtech.gvcore.dao.dto.CpgVoucherCodeNumDto;
import com.gtech.gvcore.dao.dto.VoucherCodeNumDto;
import com.gtech.gvcore.dao.dto.VoucherDto;
import com.gtech.gvcore.dao.mapper.CustomerOrderDetailsMapper;
import com.gtech.gvcore.dao.mapper.CustomerOrderMapper;
import com.gtech.gvcore.dao.mapper.DistributionItemVoucherMapper;
import com.gtech.gvcore.dao.mapper.TransactionDataMapper;
import com.gtech.gvcore.dao.mapper.VoucherBatchMapper;
import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.dto.CpgCodeCount;
import com.gtech.gvcore.dto.CustomerCpgGroup;
import com.gtech.gvcore.dto.GetCpgQuantityNumParam;
import com.gtech.gvcore.dto.GetCreateVoucherRequestInactivatedDto;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.helper.VoucherNumberHelper;
import com.gtech.gvcore.service.context.TransactionApiAction;
import com.gtech.gvcore.service.context.TransactionContext;
import com.gtech.gvcore.service.report.impl.param.LatestGvStatusQueryData;
import com.gtech.gvcore.common.request.voucher.VoucherRangeCheckRequest;
import com.gtech.gvcore.common.response.voucher.VoucherRangeCheckResponse;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.Sqls;
import tk.mybatis.mapper.weekend.Weekend;

/**
 * <AUTHOR>
 * @Date 2022/3/2 14:47
 */
@Slf4j
@Service
public class VoucherServiceImpl implements VoucherService {


    public static final String SWIPED = "Swiped";
    public static final String YYYY_MM_DD_T_HH_MM_SS = "yyyy-MM-dd'T'HH:mm:ss";
    public static final String DDMMYYYY = "DDMMYYYY";
    public static final String VIRTUAL = "Virtual";
    public static final String TRANSACTION_SUCCESSFUL1 = "Transaction successful";
    public static final String YYYY_M_MDD = "yyyyMMdd";
    public static final String T_00_00_00 = "0001-01-01T00:00:00";
    public static final String CUSTOMER = "customer";
    public static final String OUTLET = "outlet";
    public static final String API_CUSTOMER = "api customer";
    public static final String DIGITAL1 = "DIGITAL";
    @Value("${gv.active.url:}")
    private String activeUrl;

    @Value("#{${gv.issuer.warehouse:}}")
    private Map<String, String> issuerWarehouseMap;

    @Autowired
    private VoucherMapper voucherMapper;


    @Autowired
    private CpgService cpgService;

    @Lazy
    @Autowired
    private VoucherBookletService voucherBookletService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private VoucherBatchService voucherBatchService;

    @Autowired
    private PosService posService;

    @Autowired
    private PosCpgService posCpgService;

    @Autowired
    private Map<String, IssueHandlerBaseService> serviceMap;

    @Autowired
    private TransactionDataMapper transactionDataMapper;

    @Autowired
    private MasterDataValueService masterDataValueService;


    @Autowired
    private CustomerOrderService customerOrderService;
    @Autowired
    private CustomerOrderDetailsMapper customerOrderDetailsMapper;

    @Autowired
    private CustomerOrderMapper customerOrderMapper;

    @Autowired
    private CpgTypeService cpgTypeService;

    @Autowired
    private VoucherBatchMapper voucherBatchMapper;

    @Autowired
    private GvCodeHelper gvCodeHelper;

    @Autowired
    private TransactionDataService transactionDataService;

    @Autowired
    private OutletCpgService outletCpgService;

    @Autowired
    private OutletService outletService;

    @Lazy
    @Autowired
    private VoucherService voucherService;

    @Autowired
    private DistributionItemVoucherMapper distributionItemVoucherMapper;

    @Autowired
    private ArticleMopService articleMopService;

    @Autowired
    private CustomerService customerService;

    @Autowired
    @Qualifier("verifyVoucher")
    private Executor executor;

    @Autowired
    @Qualifier("verifyVoucher")
    private Executor verifyVoucherExecutor;

    @Value("${gvcore.voucher.querySize:500}")
    private Integer querySize;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private TaskProgressManager taskProgressManager;


    @Autowired
    private TransactionApiAction transactionApiAction;

    public static final String DIGITAL = "101";
    public static final String A_Z_A_Z = "[a-zA-Z]";
    public static final String RE = "[a-zA-Z]";
    public static final String AUTO_INCR = "0000001";
    public static final String REDIS_HEAD = "GV:VOUCHER:";
    public static final Integer GENERATING = 0;
    public static final Integer GENERATED = 1;
    public static final Integer MERGE = 9;
    public static final Integer PUBLISHED = 2;
    public static final Integer COMPLETED = 3;
    public static final String VOUCHER_CODE = "voucherCode ";
    public static final String REDIS_DIGITAL_HEAD = "GV:VOUCHER:DIGITAL";

    public static final String TRANSACTION_SUCCESSFUL = "Transaction successful.";

    @Autowired
    private VoucherNumberHelper voucherNumberHelper;

    @Autowired
    private CheckReceiveAndIssuanceComponent checkReceiveAndIssuanceComponent;

    @Autowired
    private MeansOfPaymentService meansOfPaymentService;

    private static final ThreadPoolExecutor commonThreadPool = new ThreadPoolExecutor(20, 300, 60, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(5000),
            new ThreadFactoryBuilder().setNameFormat("ThreadPoolCenter-pos-pool-%d").build(),
            new ThreadPoolExecutor.CallerRunsPolicy());

    private static final ThreadPoolExecutor releaseThreadPool = new ThreadPoolExecutor(10, 300, 60, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(5000),
            new ThreadFactoryBuilder().setNameFormat("ReleaseVoucher-pool-%d").build(),
            new ThreadPoolExecutor.CallerRunsPolicy());



    @Override
    public int createVoucher(CreateVoucherRequest request) {
        Voucher voucher = BeanCopyUtils.jsonCopyBean(request, Voucher.class);
        setDefaultValue(voucher);
        return voucherMapper.insertSelective(voucher);
    }

    private void setDefaultValue(Voucher voucher) {
        voucher.setVoucherStatus(GvcoreConstants.STATUS_ENABLE);
        if (voucher.getStatus() == null) {
            voucher.setStatus(VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
        }
        if (voucher.getCirculationStatus() == null) {
            voucher.setCirculationStatus(VoucherCirculationStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
        }
    }

    @Override
    public int createVoucherList(List<CreateVoucherRequest> request) {
        List<Voucher> vouchers = BeanCopyUtils.jsonCopyList(request, Voucher.class);
        vouchers.forEach(this::setDefaultValue);
        return voucherMapper.insertList(vouchers);
    }

    @Override
    public Voucher getVoucherByCode(String voucherCode) {
        if(StringUtil.isBlank(voucherCode)){
            return null;
        }
        Voucher voucher = new Voucher();
        voucher.setVoucherCode(voucherCode);
        return voucherMapper.selectOne(voucher);
    }

    @Override
    public List<Voucher> queryByVoucherCodeList(String issuerCode, List<String> voucherCodeList) {

        if (CollectionUtils.isEmpty(voucherCodeList)) {
            return Collections.emptyList();
        }

        voucherCodeList.removeAll(Collections.singleton(null));
        voucherCodeList.removeAll(Collections.singleton(""));
        VoucherDto voucherDto = new VoucherDto();
        voucherDto.setIssuerCode(issuerCode);
        voucherDto.setVoucherCodeList(voucherCodeList);
        List<Voucher> list = voucherMapper.queryByVoucherCodeList(voucherDto);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }


    @Override
    public List<Voucher> queryByVoucherCodeList(String issuerCode, String voucherOwnerCode,
                                                List<String> voucherCodeList) {
        if (StringUtils.isEmpty(issuerCode) || StringUtils.isEmpty(voucherOwnerCode)
                || CollectionUtils.isEmpty(voucherCodeList)) {
            return Collections.emptyList();
        }

        VoucherDto voucherDto = new VoucherDto();
        voucherDto.setIssuerCode(issuerCode);
        voucherDto.setVoucherOwnerCode(voucherOwnerCode);
        voucherDto.setVoucherCodeList(voucherCodeList);
        List<Voucher> list = voucherMapper.queryByVoucherCodeList(voucherDto);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }

    @Override
    public int deleteByVoucherBatch(String voucherBatchCode) {

        Example example = new Example(Voucher.class);
        example.createCriteria().andEqualTo(Voucher.C_VOUCHER_BATCH_CODE, voucherBatchCode);
        return voucherMapper.deleteByCondition(example);

    }

    @Override
    public int updateVoucherStatus(UpdateVoucherStatusRequest request) {

        Voucher voucher = new Voucher();
        if (null != request.getStatus()) {
            voucher.setStatus(request.getStatus());
        }
        if (null != request.getCirculationStatus()) {
            voucher.setCirculationStatus(request.getCirculationStatus());
        }
        if (null != request.getVoucherStatus()) {
            voucher.setVoucherStatus(request.getVoucherStatus());
        }
        if (null != request.getStatus()) {
            voucher.setStatus(request.getStatus());
        }
        if (StringUtil.isNotEmpty(request.getUpdateUser())) {
            voucher.setUpdateUser(request.getUpdateUser());
        }
        if (StringUtil.isNotEmpty(request.getSalesOutlet())){
            voucher.setSalesOutlet(request.getSalesOutlet());
        }

        if (null !=request.getSalesTime()){
            voucher.setSalesTime(request.getSalesTime());
        }

        if (StringUtil.isNotEmpty(request.getUsedOutlet())){
            voucher.setUsedOutlet(request.getUsedOutlet());
        }

        if (null !=request.getUsedTime()){
            voucher.setUsedTime(request.getUsedTime());
        }
        Example example = new Example(Voucher.class);
        Example.Criteria criteria = example.createCriteria();

        if (StringUtil.isNotEmpty(request.getVoucherStartNo())) {
			criteria.andGreaterThanOrEqualTo(Voucher.C_VOUCHER_CODE, request.getVoucherStartNo());
        }
        if (StringUtil.isNotEmpty(request.getVoucherEndNo())) {
			criteria.andLessThanOrEqualTo(Voucher.C_VOUCHER_CODE, request.getVoucherEndNo());
        }
        if (StringUtil.isNotEmpty(request.getVoucherCode())) {
            criteria.andEqualTo(Voucher.C_VOUCHER_CODE, request.getVoucherCode());
        }
        if (null != request.getOldStatus()) {
            criteria.andEqualTo(Voucher.C_STATUS, request.getOldStatus());
        }
        if (null != request.getOldCirculationStatus()) {
            criteria.andEqualTo(Voucher.C_CIRCULATION_STATUS, request.getOldCirculationStatus());
        }
        if (null != request.getOldVoucherStatus()) {
            criteria.andEqualTo(Voucher.C_VOUCHER_STATUS, request.getOldVoucherStatus());
        }
        if (CollectionUtils.isNotEmpty(request.getVoucherCodeList())) {
            criteria.andIn(Voucher.C_VOUCHER_CODE, request.getVoucherCodeList());
        }





        int count = voucherMapper.updateByConditionSelective(voucher, example);
        if (count > 0) {
            return count;
        } else {
            throw new GTechBaseException(ResultErrorCodeEnum.VOUCHER_UPDATE_STATUS.code(), ResultErrorCodeEnum.VOUCHER_UPDATE_STATUS.desc());
        }


    }


    @Override
    public int updateVoucherStatusByTable(UpdateVoucherStatusRequest request,Integer index) {

        int count = voucherMapper.updateVoucherStatusByTable(request, index);
        if (count > 0) {
            return count;
        } else {
            throw new GTechBaseException(ResultErrorCodeEnum.VOUCHER_UPDATE_STATUS.code(), ResultErrorCodeEnum.VOUCHER_UPDATE_STATUS.desc());
        }


    }
    @Override
	public List<Voucher> queryNotReceiveVoucher(String voucherStartNo, String voucherEndNo, String sourceType) {

        try {
			if (StringUtil.	isEmpty(voucherStartNo) || StringUtil.isEmpty(voucherEndNo)) {
				 return Collections.emptyList();
			}
			Example example = new Example(Voucher.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andGreaterThanOrEqualTo(Voucher.C_VOUCHER_CODE, voucherStartNo);
            criteria.andLessThanOrEqualTo(Voucher.C_VOUCHER_CODE, voucherEndNo);

            //TODO 链式判断
            criteria.andNotEqualTo(Voucher.C_CIRCULATION_STATUS, VoucherCirculationStatusEnum.VOUCHER_TO_BE_RECEIVED.getCode());
            criteria.andEqualTo(Voucher.C_VOUCHER_STATUS, GvcoreConstants.STATUS_ENABLE);

            if (VoucherReceiveSourceTypeEnum.GENERATE.getCode().equals(sourceType)) {
                criteria.andEqualTo(Voucher.C_STATUS,VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
            }else if (VoucherReceiveSourceTypeEnum.CUSTOMER_ORDER.getCode().equals(sourceType)) {
                criteria.andEqualTo(Voucher.C_STATUS,VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
            }

            return voucherMapper.selectByCondition(example);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Collections.emptyList();
        }
    }
    @Override
    public List<Voucher> queryVoucherByInterval(String voucherStartNo, String voucherEndNo) {
		if (StringUtil.isEmpty(voucherStartNo) || StringUtil.isEmpty(voucherEndNo)) {
			return Collections.emptyList();
		}
        Example example = new Example(Voucher.class);
        example.createCriteria().andGreaterThanOrEqualTo(Voucher.C_VOUCHER_CODE,voucherStartNo)
                .andLessThanOrEqualTo(Voucher.C_VOUCHER_CODE, voucherEndNo);
        return voucherMapper.selectByCondition(example);
    }

    @Override
    public List<VoucherDto> countGroupByDenominationAndCirculationStatus(
            List<VoucherCodeNumDto> voucherCodeNumDtoList) {

        List<VoucherDto> list = voucherMapper.countGroupByDenominationAndCirculationStatus(voucherCodeNumDtoList);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }

    @Override
    public List<VoucherDto> countGroupByCustomerOrderAllocation(List<VoucherCodeNumDto> voucherCodeNumDtoList) {

        List<VoucherDto> list = voucherMapper.countGroupByCustomerOrderAllocation(voucherCodeNumDtoList);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }

    @Override
    public List<Voucher> queryMismatchByAllocationCountDto(List<VoucherDto> voucherDtoList, Integer pageSize) {

        List<Voucher> list = voucherMapper.queryMismatchByAllocationCountDto(voucherDtoList, pageSize);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }

    @Override
    public List<CpgVoucherCodeNumDto> groupByDenominationAndCpg(String issuerCode,
                                                                List<VoucherCodeNumDto> voucherCodeNumDtoList) {


        List<CpgVoucherCodeNumDto> list = new ArrayList<>();
        for (VoucherCodeNumDto voucherCodeNumDto : voucherCodeNumDtoList) {
            VoucherDto voucherDto = new VoucherDto();
            voucherDto.setIssuerCode(issuerCode);
            voucherDto.setVoucherCodeNumDtoList(Arrays.asList(voucherCodeNumDto));
			voucherDto.setVoucherCodeStart(voucherCodeNumDto.getVoucherCodeStart());
			voucherDto.setVoucherCodeEnd(voucherCodeNumDto.getVoucherCodeEnd());
            List<CpgVoucherCodeNumDto> detail = voucherMapper.groupByDenominationAndCpg(voucherDto);
            list.addAll(detail);

        }
        List<CpgVoucherCodeNumDto> collect = list.stream().sorted(Comparator.comparing(CpgVoucherCodeNumDto::getDenomination)
                .thenComparing(CpgVoucherCodeNumDto::getCpgCode)
                .thenComparing(CpgVoucherCodeNumDto::getVoucherCodeNumStart)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(collect)) {
            return Collections.emptyList();
        }
        return collect;
    }

    @Override
	public Map<Long, Voucher> queryByVoucherCodeNumList(String issuerCode, List<String> voucherCodeList) {

        VoucherDto voucherDto = new VoucherDto();
        voucherDto.setIssuerCode(issuerCode);
		voucherDto.setVoucherCodeList(voucherCodeList);
        List<Voucher> list = voucherMapper.queryByVoucherCodeNumList(voucherDto);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(Voucher::getVoucherCodeNum, v -> v));
    }

    //@Transactional(rollbackFor = Exception.class)
    @Override
    public int allocateVoucher(String issuerCode, String oldVoucherOwnerCode, List<CpgVoucherCodeNumDto> voucherCodeNumLlist,
                               String newVoucherOwnerCode, String newVoucherOwnerType) {

        VoucherDto voucherDto = new VoucherDto();
        voucherDto.setIssuerCode(issuerCode);
        voucherDto.setOldVoucherOwnerCode(oldVoucherOwnerCode);
        voucherDto.setCirculationStatus(VoucherCirculationStatusEnum.VOUCHER_TO_BE_RECEIVED.getCode());
        voucherDto.setOldCirculationStatus(VoucherCirculationStatusEnum.VOUCHER_RECEIVED.getCode());
        return allocateVoucher(voucherDto, voucherCodeNumLlist, newVoucherOwnerCode, newVoucherOwnerType, new Date());
    }

    private int allocateVoucher(VoucherDto dto, List<CpgVoucherCodeNumDto> voucherCodeNumLlist,
                                String newVoucherOwnerCode, String newVoucherOwnerType, Date voucherEffectiveDate) {

        Objects.requireNonNull(dto.getIssuerCode());
        Objects.requireNonNull(dto.getOldVoucherOwnerCode());
        Objects.requireNonNull(dto.getCirculationStatus());
        Objects.requireNonNull(dto.getOldCirculationStatus());

        VoucherDto voucherDto = new VoucherDto();
        voucherDto.setIssuerCode(dto.getIssuerCode());
        voucherDto.setVoucherOwnerCode(newVoucherOwnerCode);
        voucherDto.setVoucherOwnerType(newVoucherOwnerType);
        voucherDto.setOldVoucherOwnerCode(dto.getOldVoucherOwnerCode());
        voucherDto.setStatus(VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
        voucherDto.setVoucherStatus(GvcoreConstants.STATUS_ENABLE);
        voucherDto.setCirculationStatus(dto.getCirculationStatus());
        voucherDto.setOldCirculationStatus(dto.getOldCirculationStatus());
        voucherDto.setVoucherEffectiveDate(voucherEffectiveDate);
        voucherDto.setUpdateTime(new Date());


        voucherCodeNumLlist.forEach(vo -> {
            vo.setVoucherCodeStart(vo.getVoucherCodeNumStart().toString());
            vo.setVoucherCodeEnd(vo.getVoucherCodeNumEnd().toString());
        });


        int update = 0;

        for (CpgVoucherCodeNumDto cpgVoucherCodeNum : voucherCodeNumLlist)
            update += voucherMapper.allocateVoucher(voucherDto, cpgVoucherCodeNum);

        log.info("allocateVoucher update:{}", update);

        return update;
    }

    @Override
    public int activatPhysicalVoucherByCustomerOrder(String issuerCode, String oldVoucherOwnerCode,
                                                     List<CpgVoucherCodeNumDto> voucherCodeNumLlist, String newVoucherOwnerCode, String newVoucherOwnerType, CustomerOrder customerOrder) {

        VoucherDto voucherDto = new VoucherDto();
        voucherDto.setOldStatus(VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
        voucherDto.setOldVoucherOwnerCode(oldVoucherOwnerCode);
        voucherDto.setIssuerCode(issuerCode);
        voucherDto.setVoucherOwnerCode(newVoucherOwnerCode);
        voucherDto.setVoucherOwnerType(newVoucherOwnerType);
        voucherDto.setStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
        voucherDto.setVoucherStatus(GvcoreConstants.STATUS_ENABLE);
        voucherDto.setCirculationStatus(VoucherCirculationStatusEnum.VOUCHER_TO_BE_RECEIVED.getCode());
        voucherDto.setUpdateTime(new Date());
        voucherDto.setVoucherEffectiveDate(voucherDto.getUpdateTime());

        voucherDto.setSalesOutlet(customerOrder.getOutletCode());
        voucherDto.setSalesTime(new Date());

        return voucherMapper.activatPhysicalVoucherByCustomerOrder(voucherDto, voucherCodeNumLlist);
    }

    @Override
    public int updateByCustomerOrderReleaseReject(String issuerCode, String voucherOwnerCode,
                                                  List<CpgVoucherCodeNumDto> voucherCodeNumLlist) {

        VoucherDto voucherDto = new VoucherDto();
        voucherDto.setIssuerCode(issuerCode);
        voucherDto.setOldVoucherOwnerCode(voucherOwnerCode);
        voucherDto.setCirculationStatus(VoucherCirculationStatusEnum.VOUCHER_RECEIVED.getCode());
        voucherDto.setOldCirculationStatus(VoucherCirculationStatusEnum.VOUCHER_TO_BE_RECEIVED.getCode());
        return allocateVoucher(voucherDto, voucherCodeNumLlist, null, null, null);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int activatDigitalVoucherByCustomerOrder(CustomerOrder customerOrder, String updateUser, String approvalCode) {

        //查询voucher信息，获取所有券号，和对应cpg
        //根据cpg循环，并且分割为多个线程执行
        //每个线程判断cpg是否需要自动激活


        String issuerCode = customerOrder.getIssuerCode();
        String customerOrderCode = customerOrder.getCustomerOrderCode();
        String voucherBatchCode = customerOrder.getVoucherBatchCode();
        String customerCode = customerOrder.getCustomerCode();
        List<CpgCodeCount> cpgCodeCountList = voucherMapper.queryCpgCodeCountByVoucherBatchCode(voucherBatchCode);
        if (CollectionUtils.isEmpty(cpgCodeCountList)) {
            throw new GTechBaseException(ResultErrorCodeEnum.NO_VOUCHER_DATA_FOUND.code(),
                    "No voucher data found. customerOrderCode={0}, voucherBatchCode={1} ", customerOrderCode,
                    voucherBatchCode);
        }

        int listSize = cpgCodeCountList.size();

        List<String> activateCpgList = queryActivateCpgList(cpgCodeCountList, listSize, issuerCode);

        // 创建线程池
        ExecutorService executor = Executors.newFixedThreadPool(10); // 例如: 10个线程
        log.info("customerOrderCode : {} 已经获取到券号，需要release数量为:{}",customerOrderCode,cpgCodeCountList.size());
        AtomicInteger voucherCount = new AtomicInteger();
        List<Future<?>> futures = new ArrayList<>();
        Map<String, List<CpgCodeCount>> byCpgCode = cpgCodeCountList.stream().collect(Collectors.groupingBy(x -> x.getCpgCode()));
        int totalRows = byCpgCode.values().stream().mapToInt(List::size).sum();
        log.info("customerOrder:{}根据cpg分割为多个集合:{},总数为:{}",customerOrderCode,JSON.toJSONString(byCpgCode.keySet()),totalRows);
        byCpgCode.forEach(
                (cpgCode, v) -> {
                    Map<Integer, List<CpgCodeCount>> bySharding = splitVouchersByCode(v);
                    int totalByShardingRows = bySharding.values().stream().mapToInt(List::size).sum();
                    log.info("customerOrder:{}根据取模分表分割为多个集合:{},总数为:{}",customerOrderCode,JSON.toJSONString(bySharding.keySet()),totalByShardingRows);
                    bySharding.forEach((index, cpgCodeCounts) -> {
                        CustomerResponse customer = customerService.getCustomer(GetCustomerRequest.builder().customerCode(customerCode).build());
                        List<List<CpgCodeCount>> lists = splitIntoChunks(cpgCodeCounts, 1000);
                        //统计lists内集合总数
                        int totalListsRows = lists.stream().mapToInt(List::size).sum();
                        log.info("customerOrder:{}根据1000条分割为多个集合:{},总数为:{}",customerOrderCode,JSON.toJSONString(lists.size()),totalListsRows);
                        AtomicInteger xIndex = new AtomicInteger();
                        lists.forEach(
                                splitIntoChunk -> {
                                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                                        log.info("分割券列表cpg：{} 第{}个文件 数量为：{} ", cpgCode, xIndex.incrementAndGet(), splitIntoChunk.size());
                                        releaseVoucher(customerOrder, updateUser, approvalCode, cpgCode, splitIntoChunk,
                                                issuerCode, voucherBatchCode, customerCode,
                                                activateCpgList, customerOrderCode, customer,
                                                splitIntoChunk.size(), voucherCount,index);
                                    }, executor);
                                    futures.add(future);
                                }
                        );
                    });
                }
        );

        // 等待所有任务完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
        } catch (InterruptedException e) {
            log.error("release 失败 {}",e.getMessage());
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            log.error("release 失败 {}",e.getMessage());
            throw new RuntimeException(e);
        }finally {
            executor.shutdown();
        }

        log.info("电子券Release完成{}",JSON.toJSONString(customerOrder));

        return voucherCount.get();
    }


    public static Map<Integer, List<CpgCodeCount>> splitVouchersByCode(List<CpgCodeCount> cpgCodeCountList) {
        Map<Integer, List<CpgCodeCount>> resultMap = new HashMap<>();

        for (CpgCodeCount cpgVoucher : cpgCodeCountList) {

            int bucket = (int) (Long.valueOf(cpgVoucher.getVoucherCode().replaceAll("[a-zA-Z]", ""))%64);

            resultMap.computeIfAbsent(bucket, k -> new ArrayList<>()).add(cpgVoucher);
        }

        return resultMap;
    }

    private void releaseVoucher(CustomerOrder customerOrder, String updateUser,
                                String approvalCode, String cpg, List<CpgCodeCount> v,
                                String issuerCode, String voucherBatchCode,
                                String customerCode, List<String> activateCpgList,
                                String customerOrderCode, CustomerResponse customer, int totalRows
            ,AtomicInteger voucherCount,Integer index) {
        List<String> voucherCodeList = v.stream().map(x->x.getVoucherCode()).collect(Collectors.toList());
        VoucherDto dto = new VoucherDto();
        int i = 0;
        Date now = new Date();
        dto.setCirculationStatus(VoucherCirculationStatusEnum.VOUCHER_RECEIVED.getCode());
        dto.setUpdateTime(now);
        dto.setUpdateUser(updateUser);
        dto.setIssuerCode(issuerCode);
        dto.setVoucherBatchCode(voucherBatchCode);
        dto.setVoucherOwnerCode(customerCode);
        dto.setVoucherOwnerType(VoucherOwnerTypeEnum.CUSTOMER.code());
        dto.setOldStatus(VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
        dto.setVoucherStatus(GvcoreConstants.STATUS_ENABLE);
        dto.setOldCirculationStatus(VoucherCirculationStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
        dto.setVoucherEffectiveDate(new Date());
        dto.setVoucherCodeList(voucherCodeList);
        dto.setSalesOutlet(customerOrder.getOutletCode());
        dto.setSalesTime(new Date());
        if (activateCpgList.contains(cpg)) {
            dto.setStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
            dto.setOldStatus(VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
            dto.setCpgCodeList(activateCpgList);
            //i = voucherMapper.activatDigitalVoucherByCustomerOrder(dto);
            addTransactionData(customerOrder, customerOrderCode, dto, activateCpgList, TransactionTypeEnum.GIFT_CARD_ACTIVATE, customer, approvalCode, voucherCodeList);
        }
        i = voucherMapper.receiveDigitalVoucherByCustomerOrder(dto,index);
        int releaseCount = voucherCount.addAndGet(i);
        log.info("customerOrder已经release的电子券数量{}",releaseCount);
        if (i != totalRows) {
            throw new GTechBaseException(ResultErrorCodeEnum.RECEIVE_DIGITAL_VOUCHER_ERROR.code(),
                    "update voucher fail. customerOrderCode={0}, voucherBatchCode={1} ", customerOrderCode,
                    voucherBatchCode);
        }
        addTransactionData(customerOrder, customerOrderCode, dto, Lists.newArrayList(), TransactionTypeEnum.GIFT_CARD_SELL, customer, approvalCode, voucherCodeList);
    }

    private  List<List<CpgCodeCount>> splitIntoChunks(List<CpgCodeCount> list, int chunkSize) {
        List<List<CpgCodeCount>> chunks = new ArrayList<>();
        for (int i = 0; i < list.size(); i += chunkSize) {
            chunks.add(new ArrayList<>(list.subList(i, Math.min(i + chunkSize, list.size()))));
        }
        return chunks;
    }



    private void addTransactionData(CustomerOrder customerOrder, String customerOrderCode, VoucherDto dto, List<String> cpgCodes, TransactionTypeEnum typeEnum, CustomerResponse customer, String approvalCode,List<String> voucherCodeList) {

		if (StringUtil.isEmpty(dto.getVoucherBatchCode())) {
    		return;
    	}
		Example example = new Example(Voucher.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Voucher.C_VOUCHER_BATCH_CODE, dto.getVoucherBatchCode())
                .andEqualTo(Voucher.C_VOUCHER_OWNER_CODE, dto.getVoucherOwnerCode())
                .andEqualTo(Voucher.C_VOUCHER_OWNER_TYPE, dto.getVoucherOwnerType())
                .andIn(Voucher.C_VOUCHER_CODE,voucherCodeList);
        if (CollectionUtils.isNotEmpty(cpgCodes)) {
            criteria.andIn(Voucher.C_CPG_CODE, cpgCodes);
        }

        List<Voucher> vouchers = voucherMapper.selectByCondition(example);

        activateAddTransactionData(customerOrder, customerOrderCode, vouchers, typeEnum,customer,approvalCode );
    }

    private void activateAddTransactionData(CustomerOrder customerOrder, String customerOrderCode, List<Voucher> vouchers, TransactionTypeEnum typeEnum, CustomerResponse customer, String approvalCode) {
        ArrayList<TransactionData> transactionDatas = new ArrayList<>();
        String merchantCode = outletService.getOutlet(GetOutletRequest.builder().outletCode(customerOrder.getOutletCode()).build()).getMerchantCode();
        vouchers.forEach(x -> {
            //插入transactionData
            TransactionData transactionData = new TransactionData();
            transactionData.setTransactionId(customerOrderCode);
            transactionData.setApproveCode(approvalCode);
            transactionData.setTransactionType(typeEnum.getCode());
            transactionData.setMerchantCode(merchantCode);
            transactionData.setIssuerCode(customerOrder.getIssuerCode());
            transactionData.setBatchId("");
            transactionData.setBillNumber("");
            transactionData.setOutletCode(customerOrder.getOutletCode());
            transactionData.setCpgCode(x.getCpgCode());
            transactionData.setTransactionDate(new Date());
            transactionData.setVoucherCode(x.getVoucherCode());
            transactionData.setVoucherCodeNum(Long.valueOf(x.getVoucherCode().replaceAll(RE, "")));
            transactionData.setInitiatedBy("");
            transactionData.setPosCode("");
            transactionData.setBatchCode("");
            transactionData.setLoginSource("");
            transactionData.setDenomination(x.getDenomination());
            transactionData.setPaidAmount(new BigDecimal("0"));
            transactionData.setPaymentMethod("");
            transactionData.setDiscountAmount(new BigDecimal("0"));
            transactionData.setActualOutlet("");
            transactionData.setCardEntryMode("GV POS");
            transactionData.setForwardingEntityId("");
            transactionData.setResponseMessage(TRANSACTION_SUCCESSFUL);
            transactionData.setTransactionMode("");
            transactionData.setCorporateName("");
            transactionData.setDepartmentDivisionBranch("");
            transactionData.setCustomerSalutation("");
            transactionData.setCustomerFirstName(customer.getContactFirstName());
            transactionData.setCustomerLastName(customer.getContactLastName());
            transactionData.setMobile(customer.getContactPhone());
            transactionData.setEmail(customerOrder.getContactEmail());
            transactionData.setInvoiceNumber(customerOrder.getInvoiceNo());
            transactionData.setOtherInputParameter("{}");
            transactionData.setCustomerType("");
            transactionData.setSuccessOrFailure("0");
            transactionData.setPurchaseOrderNo(customerOrder.getPurchaseOrderNo());
            transactionData.setVoucherEffectiveDate(x.getVoucherEffectiveDate());
            transactionData.setCreateUser("");
            transactionData.setCreateTime(new Date());
            transactionData.setUpdateUser("");
            transactionData.setUpdateTime(new Date());
            transactionData.setCustomerCode(customerOrder.getCustomerCode());
            transactionData.setReferenceNumber(customerOrderCode);
            transactionData.setMopCode(x.getMopCode());
            transactionDatas.add(transactionData);
        });

        log.info("transactionDataService.insertList 预计插入数量 :{}",JSON.toJSONString(transactionDatas.size()));
        transactionDataService.insertList(transactionDatas);
    }

    private List<String> queryActivateCpgList(List<CpgCodeCount> cpgCodeCountList, int listSize, String issuerCode) {
        List<String> cpgCodeList = new ArrayList<>(listSize);
        HashMap<String, CpgCodeCount> cpgCodeMap = new HashMap<>((int) (listSize / .75f) + 1);

        for (CpgCodeCount cpgCodeCount : cpgCodeCountList) {
            cpgCodeList.add(cpgCodeCount.getCpgCode());
            cpgCodeMap.put(cpgCodeCount.getCpgCode(), cpgCodeCount);
        }

        return cpgService.queryAutomaticActivateCpg(cpgCodeList, issuerCode);
    }

    @Override
    public List<GetStartAndEndVoucherResponse> getStartAndEndVoucher(GetStartAndEndVoucherRequest request) {
        checkVoucher(request);
        List<GetStartAndEndVoucherResponse> responses = new ArrayList<>();
        if (!StringUtil.isEmpty(request.getBookletStartNo()) && !StringUtil.isEmpty(request.getBookletEndNo())) {
            checkStartAndEnd(request.getBookletStartNo(),request.getBookletEndNo());

            responses= getStartAndEndByBooklet(request);
        } else if (!StringUtil.isEmpty(request.getVoucherStartNo()) && !StringUtil.isEmpty(request.getVoucherEndNo())) {
            /*CheckVoucherHelper.checkVoucher(request.getVoucherStartNo());
            CheckVoucherHelper.checkVoucher(request.getVoucherEndNo());*/
            checkStartAndEnd(request.getVoucherStartNo(),request.getVoucherEndNo());

            responses= getStartAndEndByVoucher(request);
        } else if (!CollectionUtils.isEmpty(request.getVoucherCodeList())) {
            responses= getStartAndEndByVoucherList(request);
        } else if (!CollectionUtils.isEmpty(request.getBookletCodeList())) {
            responses= getStartAndEndByBookletList(request);
        }


        //校验是否接收或者发放
        if (StringUtil.isEmpty(request.getCheckType()) || CollectionUtils.isEmpty(responses)) {
            return responses;
        }else if ("receive".equals(request.getCheckType())){
            return checkReceiveAndIssuanceComponent.checkIfReceive(responses,request);
        }else if ("issuance".equals(request.getCheckType())){
            return checkReceiveAndIssuanceComponent.checkIfIssuance(responses,request);
        }

        return responses;
    }


    public void checkStartAndEnd(String start ,String end){
        //检查起始结束
        if(Long.valueOf(start)>Long.valueOf(end)){
            throw new GTechBaseException(ResultErrorCodeEnum.VOUCHER_START_AND_END_ERROR.code(), ResultErrorCodeEnum.VOUCHER_START_AND_END_ERROR.desc());
        }

    }





    private List<GetStartAndEndVoucherResponse> getStartAndEndByBookletList(GetStartAndEndVoucherRequest request) {
        List<VoucherBooklet> voucherBookletList = voucherBookletService.queryBookletByCodeList(request.getBookletCodeList());
        if (CollectionUtils.isEmpty(voucherBookletList)) {
            throw new GTechBaseException(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }
        List<String> batchCodeList = voucherBookletList.stream().map(VoucherBooklet::getVoucherBatchCode).distinct().collect(Collectors.toList());
        List<VoucherBatch> voucherBatchList = voucherBatchService.queryVoucherBatchByCodeList(batchCodeList);
        Map<String, BigDecimal> denominationMap = voucherBatchList.stream().collect(Collectors.toMap(VoucherBatch::getVoucherBatchCode, VoucherBatch::getDenomination));
        List<GetStartAndEndVoucherResponse> responseList = new ArrayList<>();
        for (VoucherBooklet voucherBooklet : voucherBookletList) {
            GetStartAndEndVoucherResponse response = new GetStartAndEndVoucherResponse();
            response.setVoucherStartNo(voucherBooklet.getVoucherStartNo());
            response.setVoucherEndNo(voucherBooklet.getVoucherEndNo());
            response.setVoucherNum(Long.parseLong(voucherBooklet.getBookletPerNum().toString()));
            response.setDenomination(denominationMap.get(voucherBooklet.getVoucherBatchCode()));
            responseList.add(response);
        }
        return responseList;
    }

    private List<GetStartAndEndVoucherResponse> getStartAndEndByVoucherList(GetStartAndEndVoucherRequest request) {
        List<Voucher> voucherList = queryByVoucherCodeList(null, request.getVoucherCodeList());
        if (CollectionUtils.isEmpty(voucherList)) {
            throw new GTechBaseException(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }
        List<GetStartAndEndVoucherResponse> resonseList = new ArrayList<>();
        Collections.sort(voucherList, Comparator.comparing(Voucher::getDenomination));
        for (Voucher voucher : voucherList) {
            GetStartAndEndVoucherResponse response = new GetStartAndEndVoucherResponse();
            response.setDenomination(voucher.getDenomination());
            response.setVoucherStartNo(voucher.getVoucherCode());
            response.setVoucherEndNo(voucher.getVoucherCode());
            response.setVoucherNum(1L);
            resonseList.add(response);
        }
        return resonseList;
    }


    private List<GetStartAndEndVoucherResponse> getStartAndEndByVoucher(GetStartAndEndVoucherRequest request) {
        List<Voucher> voucherList = queryByVoucherCodeList(null, Arrays.asList(request.getVoucherStartNo(), request.getVoucherEndNo()));
        if (CollectionUtils.isEmpty(voucherList)) {
            throw new GTechBaseException(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }

        List<String> voucherCodeList = voucherList.stream().map(Voucher::getVoucherCode).distinct().collect(Collectors.toList());
        if (!voucherCodeList.contains(request.getVoucherStartNo())) {
            throw new GTechBaseException(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc() + ":" + VoucherBooklet.C_VOUCHER_NO_START);
        }
        if (!voucherCodeList.contains(request.getVoucherEndNo())) {
            throw new GTechBaseException(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc() + ":" + VoucherBooklet.C_VOUCHER_NO_END);
        }



        /*if(!request.getCheckType().equals("receive")){
            List<String> batchCodeList = voucherList.stream().map(Voucher::getVoucherBatchCode).distinct().collect(Collectors.toList());
            if (batchCodeList.size() > 1) {
                throw new GTechBaseException(ResultErrorCodeEnum.VOUCHER_BATCH_ERROR.code(), ResultErrorCodeEnum.VOUCHER_BATCH_ERROR.desc());
            }
        }*/

        GetStartAndEndVoucherResponse response = new GetStartAndEndVoucherResponse();
        Long startNo = Long.parseLong(request.getVoucherStartNo());
        Long endNo = Long.parseLong(request.getVoucherEndNo());
        Long count = endNo - startNo;
        response.setDenomination(voucherList.get(0).getDenomination());
        if (count > 0) {
            response.setVoucherNum(count + 1);
            response.setVoucherStartNo(request.getVoucherStartNo());
            response.setVoucherEndNo(request.getVoucherEndNo());
        } else {
            response.setVoucherNum(-count + 1);
            response.setVoucherStartNo(request.getVoucherEndNo());
            response.setVoucherEndNo(request.getVoucherStartNo());
        }
        return Arrays.asList(response);
    }


    private List<GetStartAndEndVoucherResponse> getStartAndEndByBooklet(GetStartAndEndVoucherRequest request) {
        GetStartAndEndVoucherResponse response = new GetStartAndEndVoucherResponse();
        List<VoucherBooklet> bookletList = voucherBookletService.queryBookletByCodeList(Arrays.asList(request.getBookletStartNo(), request.getBookletEndNo()));
        if (CollectionUtils.isEmpty(bookletList)) {
            throw new GTechBaseException(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }
        List<String> bookletCodeList = bookletList.stream().map(VoucherBooklet::getBookletCode).distinct().collect(Collectors.toList());
        if (!bookletCodeList.contains(request.getBookletStartNo())) {
            throw new GTechBaseException(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc() + ":" + VoucherBatch.C_BOOKLET_START_NO);
        }
        if (!bookletCodeList.contains(request.getBookletEndNo())) {
            throw new GTechBaseException(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc() + ":" + VoucherBatch.C_BOOKLET_END_NO);
        }
        List<String> batchCodeList = bookletList.stream().map(VoucherBooklet::getVoucherBatchCode).distinct().collect(Collectors.toList());
        /*if (batchCodeList.size() > 1) {
            throw new GTechBaseException(ResultErrorCodeEnum.VOUCHER_BOOKLET_BATCH_ERROR.code(), ResultErrorCodeEnum.VOUCHER_BOOKLET_BATCH_ERROR.desc());
        }*/
        GetVoucherBatchRequest getVoucherBatchRequest = new GetVoucherBatchRequest();
        getVoucherBatchRequest.setVoucherBatchCode(batchCodeList.get(0));
        VoucherBatchResponse voucherBatchResponse = voucherBatchService.getVoucherBatch(getVoucherBatchRequest);
        if (voucherBatchResponse == null) {
            return Collections.emptyList();
        }
        List<Long> voucherCodeList = new ArrayList<>();
        for (VoucherBooklet vo : bookletList) {
            voucherCodeList.add(Long.parseLong(vo.getVoucherStartNo()));
            voucherCodeList.add(Long.parseLong(vo.getVoucherEndNo()));
        }
        Collections.sort(voucherCodeList);
        response.setDenomination(voucherBatchResponse.getDenomination());
        response.setVoucherStartNo(voucherCodeList.get(0).toString());
        response.setVoucherEndNo(voucherCodeList.get(voucherCodeList.size() - 1).toString());
        response.setVoucherNum(Long.parseLong(response.getVoucherEndNo()) - Long.parseLong(response.getVoucherStartNo()) + 1);
        return Arrays.asList(response);
    }

    private void checkVoucher(GetStartAndEndVoucherRequest request) {
        if ((!StringUtil.isEmpty(request.getBookletStartNo()) && StringUtil.isEmpty(request.getBookletEndNo()))
                || (StringUtil.isEmpty(request.getBookletStartNo()) && !StringUtil.isEmpty(request.getBookletEndNo()))) {
            throw new GTechBaseException(ResultErrorCodeEnum.PARAMTER_ERROR.code(), "Booklet start no and booklet end code must have values at the same time");
        }
        if ((!StringUtil.isEmpty(request.getVoucherStartNo()) && StringUtil.isEmpty(request.getVoucherEndNo()))
                || (StringUtil.isEmpty(request.getVoucherStartNo()) && !StringUtil.isEmpty(request.getVoucherEndNo()))) {
            throw new GTechBaseException(ResultErrorCodeEnum.PARAMTER_ERROR.code(), "Voucher start no and voucher end code must have values at the same time");
        }
    }


    @Override
    public VerifyResponse verifyVoucher(VerifyVoucherRequest request) {
        List<VerifyVoucherInfo> verifyVoucherInfo = request.getVerifyVoucherInfo();
        //ArrayList<VerifyResponse> verifyResponses = new ArrayList<>();

        //判断券是否重复
        if(verifyVoucherInfo.stream().distinct().count() != verifyVoucherInfo.size()){
            return voucherDuplicate(verifyVoucherInfo);
        }

        VerifyResponse limitCount = limitedNumberOfCoupons(request);
        if (CollectionUtils.isNotEmpty(limitCount.getFailVoucherList())) return limitCount;


        List<CompletableFuture<VerifyVoucherDto>> completableFutures = new ArrayList<>();

        for (VerifyVoucherInfo voucher : verifyVoucherInfo) {
            CompletableFuture<VerifyVoucherDto> verifyResponseCompletableFuture = CompletableFuture.supplyAsync(() -> getVerifyVoucherResponses(request, voucher));
            completableFutures.add(verifyResponseCompletableFuture);
        }

        List<VerifyVoucherResponse> verifySuccess  = new ArrayList<>();
        List<VerifyVoucherResponse> verifyFail  = new ArrayList<>();

        try {
            List<VerifyVoucherDto> verifyResponses = completableFutures.stream()
                    .map(CompletableFuture::join)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            verifySuccess = verifyResponses.stream()
                    .flatMap(response -> response.getSuccessVerify().stream())
                    .collect(Collectors.toList());

            verifyFail = verifyResponses.stream()
                    .flatMap(response -> response.getFailVerify().stream())
                    .collect(Collectors.toList());
            transactionApiAction.updateVoucherStatusAndAddTransactionData(verifyResponses);
        } catch (Exception  e) {
            Throwable rootCause = ExceptionUtils.getRootCause(e);
            //捕获到异常，将所有券都挪到失败
            verifySuccess.stream().forEach(x->{
                x.setIsSuccess(false);
                x.setResponseMessage(GvPosCommonResponseCodesEnum.VALIDATION_FAILED.getResponseMessage());
                x.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.VALIDATION_FAILED.getResponseCode()));
            });

            if (CollectionUtils.isEmpty(verifySuccess) && CollectionUtils.isEmpty(verifyFail)){
                this.buildingErrorInfo(verifyFail,request, rootCause);
            }
            verifyFail.addAll(verifySuccess);
            verifySuccess.clear();
        }

        VerifyResponse verifyResponse = new VerifyResponse();
        verifyResponse.setSuccessVoucherList(this.getVerifyVoucherGinseng(verifySuccess));
        verifyResponse.setFailVoucherList(this.getVerifyVoucherGinseng(verifyFail));
        return verifyResponse;
    }

    //异常报错场景
    private static void buildingErrorInfo(List<VerifyVoucherResponse> verifyFail, VerifyVoucherRequest request, Throwable cause) {
        GTechBaseException baseException;
        if (cause instanceof GTechBaseException) {
            baseException = (GTechBaseException) cause;
        } else {
            baseException = new GTechBaseException(String.valueOf(GvPosCommonResponseCodesEnum.VALIDATION_FAILED.getResponseCode()), GvPosCommonResponseCodesEnum.VALIDATION_FAILED.getResponseMessage());
        }


        request.getVerifyVoucherInfo().forEach(x->{
            VerifyVoucherResponse verifyVoucherResponse = new VerifyVoucherResponse();
            verifyVoucherResponse.setResponseMessage(baseException.getMessage());
            verifyVoucherResponse.setResponseCode(baseException.getCode());
            verifyVoucherResponse.setIsSuccess(false);

            if (x.getInputType().equals("1") || x.getInputType().equals("3")) {
                verifyVoucherResponse.setVoucherInfo(VoucherInfo.builder().
                        voucherCode(x.getCardInfo().getCardNumber())
                        .voucherCodeNum(Long.valueOf(x.getCardInfo().getCardNumber().replaceAll("[a-zA-Z]", "")))
                        .status(0)
                        .denomination(BigDecimal.ZERO)
                        .build());
                verifyFail.add(verifyVoucherResponse);
            } else if (x.getInputType().equals("2")) {

                String startCardNumber = x.getStartCardInfo().getCardNumber();
                String prefix = startCardNumber.replaceAll("[0-9]", ""); // 得到 "ABC"
                String startNumStr = startCardNumber.replaceAll("[^0-9]", ""); // 得到 "009"
                String endCardNumber = x.getEndCardInfo().getCardNumber();
                String endNumStr = endCardNumber.replaceAll("[^0-9]", ""); // 得到 "101"
                if (!endCardNumber.startsWith(prefix)) {
                    throw new IllegalArgumentException("The prefixes for card number range are inconsistent！");
                }
                long currentNum = Long.parseLong(startNumStr);
                long endNum = Long.parseLong(endNumStr);
                int numberPartLength = startNumStr.length();
                while (currentNum <= endNum) {
                    String currentCardNumber = String.format("%s%0" + numberPartLength + "d", prefix, currentNum);
                    VoucherInfo voucherInfo = VoucherInfo.builder()
                            .voucherCode(currentCardNumber)
                            .voucherCodeNum(currentNum)
                            .status(0)
                            .denomination(BigDecimal.ZERO)
                            .build();
                    VerifyVoucherResponse newResponse = new VerifyVoucherResponse();
                    newResponse.setVoucherInfo(voucherInfo);
                    verifyFail.add(newResponse);
                    currentNum++;
                }

            }

        });

    }

    private VerifyResponse limitedNumberOfCoupons(VerifyVoucherRequest request) {
        final List<String> voucherCodes = new ArrayList<>();
        List<VerifyVoucherResponse> successVerify = new ArrayList<>();
        List<VerifyVoucherResponse> failVerify = new ArrayList<>();
        VerifyResponse response = new VerifyResponse();
        request.getVerifyVoucherInfo().forEach(voucherInfo->{
            if (voucherInfo.getInputType().equals("2")) {

                String start = voucherInfo.getStartCardInfo().getCardNumber();
                String end = voucherInfo.getEndCardInfo().getCardNumber();

                if (StringUtil.isNotBlank(voucherInfo.getStartCardInfo().getTrackData())
                        && StringUtil.isNotBlank(voucherInfo.getEndCardInfo().getTrackData())
                        && voucherInfo.getStartCardInfo().getTrackData().matches("\\d{16}")
                        && voucherInfo.getEndCardInfo().getTrackData().matches("\\d{16}")
                ) {
                    start = voucherInfo.getStartCardInfo().getTrackData();
                    end = voucherInfo.getEndCardInfo().getTrackData();
                } else if (StringUtil.isBlank(start) && StringUtil.isBlank(end)
                        && StringUtil.isNotBlank(voucherInfo.getStartCardInfo().getTrackData())
                        && StringUtil.isNotBlank(voucherInfo.getEndCardInfo().getTrackData())
                ) {
                    start = voucherNumberHelper.barCodeToVoucher(voucherInfo.getStartCardInfo().getTrackData());
                    end = voucherNumberHelper.barCodeToVoucher(voucherInfo.getEndCardInfo().getTrackData());
                }
                if(countInRange(start,end)>5000) {
                    List<String> successCode = new ArrayList<>();
                    VerifyVoucherResponse verifyVoucherResponse = new VerifyVoucherResponse();
                    verifyVoucherResponse.setVoucherInfo(new VoucherInfo());
                    verifyVoucherResponse.setIsSuccess(false);
                    verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.EXCEEDING_THE_MAXIMUM_COUPON_QUANTITY_LIMIT.getResponseCode()));
                    verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.EXCEEDING_THE_MAXIMUM_COUPON_QUANTITY_LIMIT.getResponseMessage());
                    verifyVoucherResponse.setVoucherInfo(VoucherInfo.builder().
                           status(0)
                            .denomination(BigDecimal.ZERO)
                            .build());
                    //失败
                    failVerify.add(verifyVoucherResponse);
                }
            }
        });
        response.setFailVoucherList(getVerifyVoucherGinseng(failVerify));
        response.setSuccessVoucherList(getVerifyVoucherGinseng(successVerify));
        return response;
    }


    public  Integer countInRange(String startValue, String endValue) {
        BigInteger start = new BigInteger(startValue.replaceAll("[a-zA-Z]", ""));
        BigInteger end = new BigInteger(endValue.replaceAll("[a-zA-Z]", ""));

        // 确保起始值小于等于结束值
        if (start.compareTo(end) > 0) {
            throw new GTechBaseException(ResultErrorCodeEnum.VOUCHER_START_END.code(),ResultErrorCodeEnum.VOUCHER_START_END.desc() );
        }

        // 计算范围内的整数数量
        return end.subtract(start).add(BigInteger.ONE).intValue();
    }

    @Transactional
    public void updateVoucherStatusAndAddTransactionData(List<VerifyVoucherDto> verifyResponses) {

        List<VerifyVoucherDto> activityCollection = verifyResponses.stream().filter(x -> x.getActionType().equals(VerifyVoucherTypeEnum.VOUCHER_ACTIVATION.getCode())).collect(Collectors.toList());
        List<VerifyVoucherDto> redemptionCollection = verifyResponses.stream().filter(x -> x.getActionType().equals(VerifyVoucherTypeEnum.VOUCHER_REDEMPTION.getCode())).collect(Collectors.toList());

        Boolean flag = false;

        if (CollectionUtils.isNotEmpty(activityCollection))
            flag = updateStatus(activityCollection,VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());


        if (CollectionUtils.isNotEmpty(redemptionCollection))
            flag = updateStatus(redemptionCollection,VoucherStatusEnum.VOUCHER_USED.getCode());


        if (!flag)
            throw new GTechBaseException();

    }

    public Boolean updateStatus(List<VerifyVoucherDto> vouchers,Integer actionType){
        if (CollectionUtils.isEmpty(vouchers)) return false;

        List<String> allVouchers = vouchers.stream()
                .flatMap(vd -> vd.getVoucherList().stream())
                .collect(Collectors.toList()).stream().map(VoucherResponse::getVoucherCode).collect(Collectors.toList());

        List<Voucher> voucherList = BeanCopyUtils.jsonCopyList(vouchers.stream()
                .flatMap(vd -> vd.getVoucherList().stream())
                .collect(Collectors.toList()), Voucher.class);

        UpdateVoucherStatusRequest request = new UpdateVoucherStatusRequest();

        request.setOldStatus(VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
        request.setStatus(actionType);
        request.setVoucherCodeList(allVouchers);
        request.setUpdateUser(gvCodeHelper.generateCustomerCode());
        boolean allVoucher = this.updateVoucherStatus(request) == allVouchers.size();

        if (!allVoucher){
            throw new GTechBaseException();
        }

        if (Objects.equals(actionType, VoucherStatusEnum.VOUCHER_ACTIVATED.getCode())) {
            //订单
            createCustomerOrder(voucherList);
        }

        //添加交易记录
        String transactionType = StringUtil.EMPTY;

        if(actionType.equals(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode())){
            transactionType = TransactionTypeEnum.GIFT_CARD_ACTIVATE.getCode();
        }else if (actionType.equals(VoucherStatusEnum.VOUCHER_USED.getCode())){
            transactionType = TransactionTypeEnum.GIFT_CARD_REDEEM.getCode();
        }

        Integer i = addTransactionData(BeanCopyUtils.jsonCopyList(voucherList, Voucher.class), transactionType);

        if (!i.equals(allVouchers.size())){
            throw new GTechBaseException();
        }

        return allVoucher;
    }

    public Integer addTransactionData(List<Voucher> vouchers,String transactionType){
        TransactionRequest request = TransactionContext.getRequest();
        TransactionContext.TransactionInfoContext context = TransactionContext.getContext();
        String batchId = context.getBatchId();
        String invoiceNumber = context.getInvoiceNumber();
        OutletResponse outlet = context.getOutlet();
        PosResponse pos = context.getPos();
        String approvalCode = context.getApprovalCode();

        List<CreateTransactionDataRequest> transactionDataRequests = new ArrayList<>();
        for (Voucher voucher : vouchers) {
            CreateTransactionDataRequest logRequest = new CreateTransactionDataRequest();


            logRequest.setTransactionId(String.valueOf(request.getTransactionId())+String.valueOf(batchId)+invoiceNumber);
            logRequest.setTransactionType(String.valueOf(transactionType));
            logRequest.setMerchantCode(outlet.getMerchantCode());
            logRequest.setOutletCode(pos.getOutletCode());
            logRequest.setIssuerCode(voucher.getIssuerCode());
            logRequest.setCpgCode(voucher.getCpgCode());
            logRequest.setMopCode(voucher.getMopCode());
            //.transactionDate(request.getDateAtClient())
            logRequest.setTransactionDate(new Date());
            //根据类型获取范围或者单张
            logRequest.setVoucherCode(voucher.getVoucherCode());
            logRequest.setVoucherCodeNum(Long.valueOf(voucher.getVoucherCode().replaceAll("[a-zA-Z]", "")));
            logRequest.setInitiatedBy("");
            logRequest.setPosCode(pos.getPosCode());
            logRequest.setBatchCode(voucher.getVoucherBatchCode());
            logRequest.setLoginSource("");
            logRequest.setDenomination(voucher.getDenomination());
            logRequest.setActualOutlet(voucher.getMopCode());
            logRequest.setForwardingEntityId("");
            logRequest.setTransactionMode(request.getTransactionModeId());
            logRequest.setVoucherEffectiveDate(voucher.getVoucherEffectiveDate());
            CustomerInfo customerInfo = new CustomerInfo();
            if (null != request.getCardholderInfo()) {
                customerInfo = request.getCardholderInfo();
            } else if (null != request.getPurchaserInfo()) {
                customerInfo = request.getPurchaserInfo();
            }

            if (null != customerInfo) {
                logRequest.setCustomerSalutation(customerInfo.getSalutation());
                logRequest.setCustomerFirstName(customerInfo.getFirstName());
                logRequest.setCustomerLastName(customerInfo.getLastName());
                logRequest.setMobile(customerInfo.getMobile());
                logRequest.setCorporateName(customerInfo.getCorporatename());
                logRequest.setEmail(customerInfo.getEmail());
                logRequest.setCustomerType(customerInfo.getCustomerType());
            }

            if (StringUtil.isEmpty(request.getInvoiceNumber())) {
                logRequest.setInvoiceNumber(invoiceNumber);
            } else {
                logRequest.setInvoiceNumber(request.getInvoiceNumber());
            }

//            logRequest.setOtherInputParameter(JSON.toJSONString(request));


            /*logRequest.setSuccessOrFailure(voucherSuccessStatus.getOrDefault(voucher.getVoucherCode(),success));
            logRequest.setResponseMessage(responseMessage);*/
            //这里默认全部成功
            logRequest.setSuccessOrFailure("0");
            logRequest.setResponseMessage("Transaction Successful.");

            logRequest.setCreateUser("");
            logRequest.setCreateTime(new Date());
            logRequest.setReferenceNumber(String.valueOf(request.getTransactionId()));
            logRequest.setTransactionChannel(TransactionChannelEnum.API.getCode());

            //MER-1844
            if (logRequest.getSuccessOrFailure().equals("0")) {
                logRequest.setApproveCode(approvalCode);
            } else {
                logRequest.setApproveCode(" ");
            }
            logRequest.setReferenceNumber(gvCodeHelper.generateReferenceNumber());
            logRequest.setBatchId(String.valueOf(batchId));
            logRequest.setCardEntryMode("Swiped");
            if (StringUtil.isNotEmpty(request.getNotes())) {
                logRequest.setNotes(request.getNotes());
            }
            log.info("logRequest:{}", JSON.toJSONString(logRequest));
            transactionDataRequests.add(logRequest);
        }
        Result<Integer> transactionDataList = transactionDataService.createTransactionDataList(transactionDataRequests);


        return transactionDataList.getData();
    }


    private void createCustomerOrder(List<Voucher> vouchers) {

        TransactionRequest request = TransactionContext.getRequest();
        TransactionContext.TransactionInfoContext context = TransactionContext.getContext();
        String batchId = context.getBatchId();
        String invoiceNumber = context.getInvoiceNumber();
        OutletResponse outlet = context.getOutlet();
        PosResponse pos = context.getPos();
        String approvalCode = context.getApprovalCode();

        //查询是否有销售记录 有则不创建
        //查询是否有销售记录 有则不创建
        Example tExample = new Example(TransactionData.class);
        tExample.createCriteria()
                .andIn(TransactionData.C_VOUCHER_CODE, vouchers.stream().map(Voucher::getVoucherCode).collect(Collectors.toList()))
                .andEqualTo(TransactionData.C_SUCCESS_OR_FAILURE, "0");
        List<TransactionData> transactionData = transactionDataMapper.selectByCondition(tExample);
        //分别查询transactionData中transactionType为销售的记录和transactionType为取消销售的记录
        List<TransactionData> sellList = transactionData.stream().filter(x -> x.getTransactionType().equals(TransactionTypeEnum.GIFT_CARD_SELL.getCode())).collect(Collectors.toList());
        List<TransactionData> cancelSell = transactionData.stream().filter(x -> x.getTransactionType().equals(TransactionTypeEnum.GIFT_CARD_CANCEL_SELL.getCode())).collect(Collectors.toList());

        //如果是取消销售再销售的，不会再创建订单
        if (CollectionUtils.isNotEmpty(sellList) && cancelSell.size() == sellList.size()) {
            //插入销售记录
            log.info("执行添加销售记录");
            addTransactionData(vouchers, TransactionTypeEnum.GIFT_CARD_SELL.getCode());
            log.info("执行添加销售记录结束");
            return;
        }

        addTransactionData(vouchers, TransactionTypeEnum.GIFT_CARD_SELL.getCode());
        //插入销售记录
        log.info("执行添加销售记录");
        log.info("执行添加销售记录结束");
        List<GetCpgResponse> cpgByCpgNameList = new ArrayList<>();
        List<CreateCustomerOrderDetailsRequest> detailsRequestList = new ArrayList<>();
        try {
            for (Voucher voucher : vouchers) {
                GetCpgResponse data = cpgService.getCpg(GetCpgRequest.builder().cpgCode(voucher.getCpgCode()).build()).getData();
                if (null != data) {
                    cpgByCpgNameList.add(data);
                }
                detailsRequestList.add(CreateCustomerOrderDetailsRequest
                        .builder()
                        .cpgCode(voucher.getCpgCode())
                        .voucherNum(1)
                        .deleteStatus(2)//自动生成
                        .denomination(voucher.getDenomination())
                        .build());

            }

        } catch (Exception e) {
            //cpg error
            throw new GTechBaseException(ResultErrorCodeEnum.GET_CPG_ERROR.code(), ResultErrorCodeEnum.GET_CPG_ERROR.desc());
        }
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (GetCpgResponse getCpgResponse : cpgByCpgNameList) {
            totalAmount = totalAmount.add(getCpgResponse.getDenomination());
        }

        createCustomerOrderByCreateAndIssue(invoiceNumber, outlet, null,
                Voucher.builder()
                        .mopCode(cpgByCpgNameList.get(0).getMopCode())
                        .issuerCode(cpgByCpgNameList.get(0).getIssuerCode())
                        .voucherBatchCode(vouchers.get(0).getVoucherBatchCode())
                        .build(),
                String.valueOf(request.getTransactionId()) + String.valueOf(batchId) + invoiceNumber, ""
                , Integer.valueOf(vouchers.size()), detailsRequestList, totalAmount);

        //修改booklet状态
        if (CollectionUtils.isNotEmpty(vouchers)) {
            voucherBookletService.voucherActivateUpdateBookletStatus(VoucherActivateUpdateBookletStatusDto.builder()
                    .voucherCode(vouchers.stream().map(x -> x.getVoucherCode()).collect(Collectors.toList()))
                    .statusEnum(BookletStatusEnum.PARTIALLY_ACTIVATED)
                    .type("0")
                    .build());
        }

    }

    public CreateCustomerOrderRequest createCustomerOrderByCreateAndIssue(String invoiceNumber, OutletResponse outlet, CustomerInfo customerInfo
            , Voucher voucher, String transactionId, String notes, Integer numOfCards
            , List<CreateCustomerOrderDetailsRequest> detailsRequestList, BigDecimal voucherAmount) {
        CreateCustomerOrderRequest customerOrderRequest = new CreateCustomerOrderRequest();
        //customerOrderRequest.setCustomerOrderCode(transactionId);
        customerOrderRequest.setIssuerCode(voucher.getIssuerCode());
        customerOrderRequest.setOutletCode(outlet.getOutletCode());
        customerOrderRequest.setInvoiceNo(invoiceNumber);
        customerOrderRequest.setPurchaseOrderNo(outlet.getOutletName() + System.currentTimeMillis());
        customerOrderRequest.setMopCode(voucher.getMopCode());
        customerOrderRequest.setVoucherNum(numOfCards);
        customerOrderRequest.setVoucherAmount(voucherAmount);
        customerOrderRequest.setDiscount(new BigDecimal("0"));
        customerOrderRequest.setAmount(BigDecimal.ZERO);
        if (voucher.getMopCode().equals(GvcoreConstants.MOP_CODE_VCE)) {
            customerOrderRequest.setVoucherBatchCode(voucher.getVoucherBatchCode());
        }

        customerOrderRequest.setContactFirstName(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getFirstName()) ?
                customerInfo.getFirstName() : API_CUSTOMER);
        customerOrderRequest.setContactLastName(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getLastName()) ?
                customerInfo.getLastName() : API_CUSTOMER);
        customerOrderRequest.setContactPhone(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getMobile()) ?
                customerInfo.getMobile() : API_CUSTOMER);
        customerOrderRequest.setCompanyName(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getCorporatename()) ?
                customerInfo.getCorporatename() : API_CUSTOMER);
        customerOrderRequest.setContactEmail(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getEmail()) ?
                customerInfo.getEmail() : API_CUSTOMER);


        customerOrderRequest.setCurrencyCode(API_CUSTOMER);
        customerOrderRequest.setCustomerCode(API_CUSTOMER);
        customerOrderRequest.setCustomerName(API_CUSTOMER);
        customerOrderRequest.setCustomerType(API_CUSTOMER);
        customerOrderRequest.setProductCategoryCode("");
        customerOrderRequest.setDiscountType("");
        customerOrderRequest.setCreateUser("api");
        customerOrderRequest.setMeansOfPaymentCode("");
        customerOrderRequest.setShippingAddress("");
        customerOrderRequest.setCustomerRemarks(notes);
        customerOrderRequest.setStatus(CustomerOrderStatusEnum.API.getStatus());
        customerOrderRequest.setReleaseTime(new Date());

        //detailsRequestList将相同cpgCode的数据的voucherNum相加
        Map<String, CreateCustomerOrderDetailsRequest> map = new HashMap<>();
        for (CreateCustomerOrderDetailsRequest detailsRequest : detailsRequestList) {
            if (map.containsKey(detailsRequest.getCpgCode())) {
                CreateCustomerOrderDetailsRequest createCustomerOrderDetailsRequest = map.get(detailsRequest.getCpgCode());
                createCustomerOrderDetailsRequest.setVoucherNum(createCustomerOrderDetailsRequest.getVoucherNum() + detailsRequest.getVoucherNum());
            } else {
                map.put(detailsRequest.getCpgCode(), detailsRequest);
            }
        }
        //map转list
        List<CreateCustomerOrderDetailsRequest> list = new ArrayList<>();
        for (Map.Entry<String, CreateCustomerOrderDetailsRequest> entry : map.entrySet()) {
            list.add(entry.getValue());
        }



        customerOrderRequest.setCreateCustomerOrderDetailsRequests(list/*Lists.newArrayList(
                CreateCustomerOrderDetailsRequest
                        .builder()
                        .cpgCode(voucher.getCpgCode())
                        .voucherNum(1)
                        .deleteStatus(2)//自动生成
                        .denomination(voucher.getDenomination())
                        .build()
        )*/);
        log.info("createCustomerOrder:{}", JSON.toJSONString(customerOrderRequest));
        Result<String> customerOrder = customerOrderService.createCustomerOrder(customerOrderRequest);
        if (!customerOrder.isSuccess()) {
            throw new GTechBaseException(customerOrder.getCode(), customerOrder.getMessage());
        }
        log.info("createCustomerOrder Result :{}", JSON.toJSONString(customerOrder));
        return customerOrderRequest;
    }





    @Override
    @Transactional
    public CancelRedeemResponse cancelRedeem(CancelRedeemRequest request) {



        String batchId = request.getBatchID();
                /*"TransactionId":1245,
                "CardNumber":"9990000200000048",
                "Notes":"cancel redeem ",
                "OriginalAmount":500,
                "OriginalInvoiceNumber":"12345",
                "OriginalTransactionId":1008,
                "OriginalBatchNumber":61725,
                "DateAtClient":"2018-10-18T17:08:14",
                "OriginalApprovalCode":"7860196515"*/


        String message = "Transaction successful.";
        String success = "1";
        getService(IssueHandlingTypeEnum.CANCEL_REDEEM);

        if (null == request.getOriginalTransactionId()) {
            return CancelRedeemResponse.builder().responseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.code())).responseMessage(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.desc()).build();
        }


        Example example1 = new Example(TransactionData.class);
        example1.createCriteria()
                .andEqualTo("voucherCode", request.getCardNumber())
                .andEqualTo("transactionType",TransactionTypeEnum.GIFT_CARD_REDEEM.getCode());
        List<TransactionData> transactionData = transactionDataMapper.selectByCondition(example1);
        if (CollectionUtils.isEmpty(transactionData)) {
            //异常暂无
            return CancelRedeemResponse.builder().responseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.code())).responseMessage(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.desc()).build();
        }
        Map<String, TransactionData> dataMap = transactionData.stream().collect(Collectors.toMap(TransactionData::getTransactionType, x -> x, (k1, k2) -> k2));

        TransactionData redeemData = dataMap.get(TransactionTypeEnum.GIFT_CARD_REDEEM.getCode());

        if (null == redeemData) {
            return CancelRedeemResponse.builder().responseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_STATUS_ERROR.code())).responseMessage(ResultErrorCodeEnum.VOUCHER_STATUS_ERROR.desc()).build();
        }


		Voucher toBeVerified = getVoucherByCode(redeemData.getVoucherCode());
        if (null == toBeVerified) {
            return CancelRedeemResponse.builder().responseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.code())).responseMessage(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.desc()).build();
        }

        GetCpgRequest cpgRequest = new GetCpgRequest();
        cpgRequest.setCpgCode(toBeVerified.getCpgCode());
        Cpg cpg  = masterDataCache.getCpg(toBeVerified.getCpgCode());
        if (null == cpg){
            throw new GTechBaseException(ResultErrorCodeEnum.NO_CPG_DATA_FOUND.code(), ResultErrorCodeEnum.NO_CPG_DATA_FOUND.desc());
        }

        GetCpgTypeRequest cpgTypeRequest = new GetCpgTypeRequest();
        cpgTypeRequest.setCpgTypeCode(cpg.getCpgTypeCode());
        Result<Object> cpgType = cpgTypeService.getCpgType(cpgTypeRequest);
        GetCpgTypeResponse cpgTypeData = (GetCpgTypeResponse) cpgType.getData();
        Date voucherEffectiveDate = toBeVerified.getVoucherEffectiveDate();
        String notes = StringUtil.EMPTY;
        if (StringUtil.isNotEmpty(request.getNotes())){
            notes = request.getNotes();
        }
        String approveCode = gvCodeHelper.generateApproveCode();



        CancelRedeemResponse cancelRedeemResponse = new CancelRedeemResponse();

        //状态未使用 暂无错误码
        if (!toBeVerified.getStatus().equals(VoucherStatusEnum.VOUCHER_USED.getCode())) {
            cancelRedeemResponse.setResponseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.code()));
            cancelRedeemResponse.setResponseMessage(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.desc());
            message = ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.desc();

        }


        //cancelRedeemResponse = checkCancelRedeemVoucher(request);

        if (null!= cancelRedeemResponse.getResponseCode() && !cancelRedeemResponse.getResponseCode().equals(0)) {
            return cancelRedeemResponse;
        }


        Pos pos = masterDataCache.getPos(request.getTerminalId());
        if (null == pos){
            throw new GTechBaseException(ResultErrorCodeEnum.NO_POS_DATA_FOUND.code(), ResultErrorCodeEnum.NO_POS_DATA_FOUND.desc());
        }

        Outlet outlet = masterDataCache.getOutlet(pos.getOutletCode());
       if(null == outlet) {
            throw new GTechBaseException(ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.code(), ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.desc());
        }


        VoucherBatchResponse voucherBatch;
        try {
            voucherBatch = voucherBatchService.getVoucherBatch(GetVoucherBatchRequest.builder().voucherBatchCode(toBeVerified.getVoucherBatchCode()).build());
        } catch (Exception e) {
            //cpg error
            throw new GTechBaseException();
        }


        if (null == cancelRedeemResponse.getResponseCode() || cancelRedeemResponse.getResponseCode().equals(0)) {
            Voucher voucher = voucherMapper.selectOne(Voucher.builder().voucherCode(request.getCardNumber()).build());
            voucher.setStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
            voucher.setUsedOutlet(null);
            voucher.setUsedTime(null);
            Example voucherExample = new Example(Voucher.class);
            voucherExample.createCriteria().andEqualTo(Voucher.C_VOUCHER_CODE, request.getCardNumber());
            voucherMapper.updateByCondition(voucher, voucherExample);
            success = "0";
        }
        //插入交易数据
        insertTransactionData(request.getTransactionId(), toBeVerified, voucherBatch, cpg, pos,
                outlet, TransactionTypeEnum.GIFT_CARD_BULK_CANCEL_REDEEM,
                null, approveCode, "",notes, success, message, batchId);


        return makeResponse(request, Integer.valueOf(batchId.substring(batchId.length()-4)), dataMap, toBeVerified, cpg, cpgTypeData, voucherEffectiveDate, cancelRedeemResponse, approveCode);

    }

    private static CancelRedeemResponse makeResponse(CancelRedeemRequest request, Integer batchId, Map<String, TransactionData> dataMap, Voucher toBeVerified, Cpg cpg, GetCpgTypeResponse cpgTypeData, Date voucherEffectiveDate, CancelRedeemResponse cancelRedeemResponse, String approveCode) {
        cancelRedeemResponse.setCardExpiry(DateUtil.format(voucherEffectiveDate, YYYY_MM_DD_T_HH_MM_SS));
        cancelRedeemResponse.setInvoiceNumber(request.getOriginalInvoiceNumber());
        cancelRedeemResponse.setAmount(toBeVerified.getDenomination());
        cancelRedeemResponse.setSettlementDate(DateUtil.format(voucherEffectiveDate, DDMMYYYY));

        DateUtil.format(voucherEffectiveDate, DDMMYYYY);

        cancelRedeemResponse.setExpiry(DateUtil.format(voucherEffectiveDate, DDMMYYYY));
        cancelRedeemResponse.setCardStatusId(toBeVerified.getVoucherStatus() == 0 ?
                Integer.valueOf(VoucherEnableDisablePosEnum.getStatusIdByCode(toBeVerified.getVoucherStatus())) :
                Integer.valueOf(VoucherStatusPosEnum.getStatusIdByCode(toBeVerified.getStatus())));
        cancelRedeemResponse.setCardStatus(toBeVerified.getVoucherStatus() == 0 ?
                VoucherEnableDisablePosEnum.getDescByCode(toBeVerified.getVoucherStatus()) :
                VoucherStatusPosEnum.getDescByCode(toBeVerified.getStatus()));
        cancelRedeemResponse.setCardCurrencySymbol(CardCurrencySymbolEnum.getByCode(cpg.getCurrencyCode()));
        TransactionData tData = null;
        if (null == dataMap.get(TransactionTypeEnum.GIFT_CARD_ACTIVATE.getCode())) {
            tData = dataMap.get(TransactionTypeEnum.GIFT_CARD_ACTIVATE_ONLY.getCode());

        } else {
            tData = dataMap.get(TransactionTypeEnum.GIFT_CARD_ACTIVATE.getCode());

        }

        if(null != tData){
            cancelRedeemResponse.setActivationDate(DateUtil.format(tData.getTransactionDate(), YYYY_MM_DD_T_HH_MM_SS));
        }
        cancelRedeemResponse.setCardType("GB-GOLD");
        cancelRedeemResponse.setPreviousBalance(toBeVerified.getDenomination());

        if (toBeVerified.getMopCode().equals("VCE")) {
            cancelRedeemResponse.setCardCreationType(VIRTUAL);
        } else if (toBeVerified.getMopCode().equals("VCR")) {
            cancelRedeemResponse.setCardCreationType("Physical");
        }
        cancelRedeemResponse.setCardProgramGroupType(cpgTypeData.getCpgTypeName());
        cancelRedeemResponse.setCurrentBatchNumber(batchId);
        cancelRedeemResponse.setApprovalCode(approveCode);
        if (null == cancelRedeemResponse.getResponseCode()){
            cancelRedeemResponse.setResponseCode(0);
            cancelRedeemResponse.setResponseMessage(TRANSACTION_SUCCESSFUL1);
        }
        cancelRedeemResponse.setTransactionId(request.getTransactionId());
        cancelRedeemResponse.setTransactionType(String.valueOf(GvPosTransactionTypesEnum.GIFT_CARD_CANCEL_REDEEM.getCode()));
        return cancelRedeemResponse;
    }






    @Override
    public CancelActivateGinseng cancelActivate(CancelActivateParam request, String batchId) {


        /**

         {
         "TransactionId":1,
         "CardNumber":"7777771115687867",
         "DateAtClient":"2018-10-18T17:08:14",
         "OriginalTransactionId": 1,
         "OriginalBatchNumber": 450796
         }

         */


        String message = "Transaction successful.";



        //根据cardNumber查找交易记录




        //获取交易数据
        Example example1 = new Example(TransactionData.class);
        Example.Criteria criteria = example1.createCriteria();
        criteria.andEqualTo("voucherCode", request.getCardNumber());

        Example.Criteria criteria1 = example1.createCriteria();
        criteria1.andEqualTo("transactionType", TransactionTypeEnum.GIFT_CARD_ACTIVATE.getCode());
        criteria1.orEqualTo("transactionType", TransactionTypeEnum.GIFT_CARD_ACTIVATE_ONLY.getCode());


        example1.and(criteria1);
        List<TransactionData> transactionData = transactionDataMapper.selectByCondition(example1);
        example1.orderBy("id");
        Map<String, TransactionData> dataMap = null;
        if (CollectionUtils.isNotEmpty(transactionData)) {
            //异常暂无
            dataMap = transactionData.stream().collect(Collectors.toMap(TransactionData::getTransactionType, x -> x, (k1, k2) -> k2));
        }





		Voucher toBeVerified = getVoucherByCode(request.getCardNumber());
        if (null == toBeVerified) {
            return CancelActivateGinseng.builder().responseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.code())).responseMessage(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.desc()).build();
        }
        VoucherBatchResponse voucherBatch;
        try {
            voucherBatch = voucherBatchService.getVoucherBatch(GetVoucherBatchRequest.builder().voucherBatchCode(toBeVerified.getVoucherBatchCode()).build());
        } catch (Exception e) {
            //cpg error
            throw new GTechBaseException();
        }

        Cpg cpg = masterDataCache.getCpg(toBeVerified.getCpgCode());
        Result<Object> cpgTypeResult;
        try {
            cpgTypeResult = cpgTypeService.getCpgType(GetCpgTypeRequest.builder().cpgTypeCode(cpg.getCpgTypeCode()).build());
        } catch (Exception e) {
            //cpg error
            throw new GTechBaseException(ResultErrorCodeEnum.GET_CPG_ERROR.code(), ResultErrorCodeEnum.GET_CPG_ERROR.desc());
        }
        GetCpgTypeResponse cpgType = (GetCpgTypeResponse) cpgTypeResult.getData();

        Pos pos = masterDataCache.getPos(request.getTerminalId());
        if (null == pos) {
            throw new GTechBaseException(ResultErrorCodeEnum.NO_POS_DATA_FOUND.code(), ResultErrorCodeEnum.NO_POS_DATA_FOUND.desc());
        }
        Outlet  outlet = masterDataCache.getOutlet(pos.getOutletCode());
        if(null == outlet) {
           throw new GTechBaseException(ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.code(), ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.desc());
        }
        CancelActivateGinseng cancelActivateGinseng = new CancelActivateGinseng();
        cancelActivateGinseng.setResponseMessage(message);
        cancelActivateGinseng.setResponseCode(0);

        if (new Date().compareTo(toBeVerified.getVoucherEffectiveDate()) >= 0){
            cancelActivateGinseng.setResponseCode(Integer.valueOf(GvPosCommonResponseCodesEnum.CARD_EXPIRED.getResponseCode()));
            cancelActivateGinseng.setResponseMessage(GvPosCommonResponseCodesEnum.CARD_EXPIRED.getResponseMessage());

        }else if (VoucherStatusEnum.VOUCHER_USED.getCode().equals(toBeVerified.getStatus()) && !cpgType.getPrefix().equals("105")) {
            cancelActivateGinseng.setResponseMessage(GvPosCommonResponseCodesEnum.CARD_ALREADY_REDEEMED.getResponseMessage());
            cancelActivateGinseng.setResponseCode(Integer.valueOf(GvPosCommonResponseCodesEnum.CARD_ALREADY_REDEEMED.getResponseCode()));
        }  else if (VoucherStatusEnum.VOUCHER_EXPIRED.getCode().equals(toBeVerified.getStatus())) {
            cancelActivateGinseng.setResponseMessage(GvPosCommonResponseCodesEnum.CARD_EXPIRED.getResponseMessage());
            cancelActivateGinseng.setResponseCode(Integer.valueOf(GvPosCommonResponseCodesEnum.CARD_EXPIRED.getResponseCode()));
        } else if (VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode().equals(toBeVerified.getStatus()) && !cpgType.getPrefix().equals("105")) {
            cancelActivateGinseng.setResponseMessage(GvPosCommonResponseCodesEnum.CARD_NOT_ACTIVATED.getResponseMessage());
            cancelActivateGinseng.setResponseCode(Integer.valueOf(GvPosCommonResponseCodesEnum.CARD_NOT_ACTIVATED.getResponseCode()));
        }else if (toBeVerified.getVoucherStatus().equals(GvcoreConstants.STATUS_DISABLE)) {
            cancelActivateGinseng.setResponseMessage(GvPosCommonResponseCodesEnum.CARD_IS_DEACTIVATED.getResponseMessage());
            cancelActivateGinseng.setResponseCode(Integer.valueOf(GvPosCommonResponseCodesEnum.CARD_IS_DEACTIVATED.getResponseCode()));
        }


        //cancelActivateGinseng = checkCancelActivateVoucher(request);
        if (!cancelActivateGinseng.getResponseCode().equals(0)) {
            message = cancelActivateGinseng.getResponseMessage();
        }

        String notes = StringUtil.EMPTY;
        if (StringUtil.isNotEmpty(request.getNotes())){
            notes = request.getNotes();
        }
        String success = "1";


        String approveCode = gvCodeHelper.generateApproveCode();
        if (cancelActivateGinseng.getResponseCode().equals(0)) {

            Voucher updateVoucher = voucherMapper.selectOne(Voucher.builder().voucherCode(request.getCardNumber()).build());
            if (toBeVerified.getMopCode().equals(GvcoreConstants.MOP_CODE_VCE)){
                updateVoucher.setVoucherStatus(VoucherStatusEnableDisableEnum.STATUS_DESTROY.getCode());
            }else {
                updateVoucher.setStatus(VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
            }
            updateVoucher.setSalesOutlet(null);
            updateVoucher.setSalesTime(null);
            Example voucherExample = new Example(Voucher.class);
            voucherExample.createCriteria().andEqualTo(Voucher.C_VOUCHER_CODE, request.getCardNumber());
            voucherMapper.updateByCondition(updateVoucher, voucherExample);
            success = "0";
            cancelActivateGinseng.setResponseCode(0);
            cancelActivateGinseng.setResponseMessage(TRANSACTION_SUCCESSFUL);
        }
        insertTransactionData(request.getTransactionId(), toBeVerified, voucherBatch, cpg, pos,
                outlet, TransactionTypeEnum.GIFT_CARD_CANCEL_ACTIVATE, null,
                approveCode, "", notes, success, message,batchId);

        insertTransactionData(request.getTransactionId(), toBeVerified, voucherBatch, cpg, pos,
                outlet, TransactionTypeEnum.GIFT_CARD_CANCEL_SELL, null,
                approveCode, "", notes, success, message,batchId);

        cancelActivateGinseng.setTransactionId(request.getTransactionId());
        cancelActivateGinseng.setApprovalCode(approveCode);
        cancelActivateGinseng.setCardExpiry(DateUtil.format(toBeVerified.getVoucherEffectiveDate(), YYYY_MM_DD_T_HH_MM_SS));
        cancelActivateGinseng.setSettlementDate(DateUtil.format(toBeVerified.getVoucherEffectiveDate(), DDMMYYYY));
        cancelActivateGinseng.setExpiry(DateUtil.format(toBeVerified.getVoucherEffectiveDate(), DDMMYYYY));
        cancelActivateGinseng.setCardCurrencySymbol(cpg.getCurrencyCode());
        if(null != dataMap){
            cancelActivateGinseng.setActivationDate(DateUtil.format(null == dataMap.get(TransactionTypeEnum.GIFT_CARD_ACTIVATE_ONLY.getCode()) ? dataMap.get(TransactionTypeEnum.GIFT_CARD_ACTIVATE.getCode()).getTransactionDate():dataMap.get(TransactionTypeEnum.GIFT_CARD_ACTIVATE_ONLY.getCode()).getTransactionDate(), YYYY_MM_DD_T_HH_MM_SS));

        }
        cancelActivateGinseng.setCardType(cpg.getCpgName());
        cancelActivateGinseng.setCurrentBatchNumber(RandomUtils.nextInt(0, 9999999));
        cancelActivateGinseng.setTransactionType("28");

        return cancelActivateGinseng;
    }

    private void insertTransactionData(Integer transactionId, Voucher toBeVerified, VoucherBatchResponse voucherBatch, Cpg cpg, Pos pos
            , Outlet outlet, TransactionTypeEnum typeEnum, CustomerInfo customerInfo, String approveCode
            , String invoiceNumber, String notes, String success, String message, String batchId) {
        TransactionData trData = new TransactionData();
        trData.setTransactionId(String.valueOf(transactionId));
        trData.setTransactionType(typeEnum.getCode());
        trData.setMerchantCode(outlet.getMerchantCode());
        trData.setIssuerCode(toBeVerified.getIssuerCode());
        trData.setBatchId(batchId);
        trData.setBillNumber("");
        trData.setOutletCode(outlet.getOutletCode());
        trData.setCpgCode(cpg.getCpgCode());
        trData.setTransactionDate(new Date());
        trData.setVoucherCode(toBeVerified.getVoucherCode());
        trData.setVoucherCodeNum(Long.valueOf(toBeVerified.getVoucherCode().replaceAll(A_Z_A_Z, "")));
        trData.setInitiatedBy("");
        trData.setPosCode(pos.getPosCode());
        trData.setBatchCode(toBeVerified.getVoucherBatchCode());
        trData.setLoginSource("");
        trData.setDenomination(toBeVerified.getDenomination());
        trData.setPaidAmount(new BigDecimal("0"));
        trData.setPaymentMethod("");
        trData.setDiscountAmount(new BigDecimal("0"));
        trData.setActualOutlet("");
        trData.setForwardingEntityId("");
        trData.setResponseMessage(message);
        trData.setTransactionMode(TRANSACTION_SUCCESSFUL1);
        trData.setDepartmentDivisionBranch("");

        if (null != customerInfo) {
            trData.setCustomerSalutation(customerInfo.getSalutation());
            trData.setCustomerFirstName(customerInfo.getFirstName());
            trData.setCustomerLastName(customerInfo.getLastName());
            trData.setMobile(customerInfo.getMobile());
            trData.setCorporateName(customerInfo.getCorporatename());
            trData.setEmail(customerInfo.getEmail());
            trData.setCustomerType(customerInfo.getCustomerType());
        }

        //TODO getInvoiceNumber
        if(StringUtils.isNotEmpty(invoiceNumber)){
            trData.setInvoiceNumber(invoiceNumber);

        }
        //trData.setOtherInputParameter("");
        trData.setCustomerType("");
        trData.setSuccessOrFailure(success);
        trData.setPurchaseOrderNo(Optional.ofNullable(voucherBatch.getPurchaseOrderNo()).orElse(""));
        trData.setVoucherEffectiveDate(toBeVerified.getVoucherEffectiveDate());
        trData.setCreateUser("");
        trData.setCreateTime(new Date());
        trData.setUpdateUser("");
        trData.setUpdateTime(new Date());
        trData.setCustomerCode("");
        trData.setReferenceNumber(String.valueOf(transactionId));
        trData.setCardEntryMode(SWIPED);
        trData.setMopCode(toBeVerified.getMopCode());
        trData.setTransactionChannel(TransactionChannelEnum.API.getCode());
        trData.setApproveCode(approveCode);
        trData.setReferenceNumber(gvCodeHelper.generateReferenceNumber());
        if (StringUtil.isNotEmpty(notes)){
            trData.setNotes(notes);
        }


        transactionDataService.insertList(Lists.newArrayList(trData));

        if (typeEnum == TransactionTypeEnum.GIFT_CARD_ACTIVATE){
            ArrayList<Voucher> voucherArrayList = Lists.newArrayList(toBeVerified);
            //修改booklet状态
            if (CollectionUtils.isNotEmpty(Lists.newArrayList(toBeVerified.getVoucherCode()))){
                voucherBookletService.voucherActivateUpdateBookletStatus(VoucherActivateUpdateBookletStatusDto.builder()
                        .voucherCode(voucherArrayList.stream().map(x->x.getVoucherCode()).collect(Collectors.toList()))
                        .statusEnum(BookletStatusEnum.PARTIALLY_ACTIVATED)
                        .type("0")
                        .build());
            }
        }


    }


    private void insertTransactionData(Integer transactionId, List<Voucher> toBeVerifiedList, Map<String,VoucherBatch> voucherBatchMap, Cpg cpg, Pos pos
            , Outlet outletResponse, TransactionTypeEnum typeEnum, CustomerInfo customerInfo, String approveCode
            , String invoiceNumber, String notes, String success, String message, String batchId) {

        List<TransactionData> dataArrayList = new ArrayList<>();
        toBeVerifiedList.forEach(toBeVerified->{
            TransactionData trData = new TransactionData();
            trData.setIssuerCode(toBeVerified.getIssuerCode());
            trData.setTransactionId(String.valueOf(transactionId));
            trData.setTransactionType(typeEnum.getCode());
            trData.setMerchantCode(outletResponse.getMerchantCode());
            trData.setBatchId(batchId);
            trData.setBillNumber("");
            trData.setOutletCode(outletResponse.getOutletCode());
            trData.setCpgCode(cpg.getCpgCode());
            trData.setTransactionDate(new Date());
            trData.setVoucherCode(toBeVerified.getVoucherCode());
            trData.setVoucherCodeNum(Long.valueOf(toBeVerified.getVoucherCode().replaceAll(A_Z_A_Z, "")));
            trData.setInitiatedBy("");
            trData.setPosCode(pos.getPosCode());
            trData.setBatchCode(toBeVerified.getVoucherBatchCode());
            trData.setLoginSource("");
            trData.setDenomination(toBeVerified.getDenomination());
            trData.setPaidAmount(new BigDecimal("0"));
            trData.setPaymentMethod("");
            trData.setDiscountAmount(new BigDecimal("0"));
            trData.setActualOutlet("");
            trData.setForwardingEntityId("");
            trData.setResponseMessage(message);
            trData.setTransactionMode(TRANSACTION_SUCCESSFUL1);
            trData.setDepartmentDivisionBranch("");

            if (null != customerInfo) {
                trData.setCustomerSalutation(customerInfo.getSalutation());
                trData.setCustomerFirstName(customerInfo.getFirstName());
                trData.setCustomerLastName(customerInfo.getLastName());
                trData.setMobile(customerInfo.getMobile());
                trData.setCorporateName(customerInfo.getCorporatename());
                trData.setEmail(customerInfo.getEmail());
                trData.setCustomerType(customerInfo.getCustomerType());
            }

            //TODO getInvoiceNumber
            if(StringUtils.isNotEmpty(invoiceNumber)){
                trData.setInvoiceNumber(invoiceNumber);

            }
            //trData.setOtherInputParameter("");
            trData.setCustomerType("");
            trData.setSuccessOrFailure(success);
            trData.setPurchaseOrderNo(Optional.ofNullable(voucherBatchMap.get(toBeVerified.getVoucherBatchCode()).getPurchaseOrderNo()).orElse(""));
            trData.setVoucherEffectiveDate(toBeVerified.getVoucherEffectiveDate());
            trData.setCreateUser("");
            trData.setCreateTime(new Date());
            trData.setUpdateUser("");
            trData.setUpdateTime(new Date());
            trData.setCustomerCode("");
            trData.setReferenceNumber(String.valueOf(transactionId));
            trData.setCardEntryMode(SWIPED);
            trData.setMopCode(toBeVerified.getMopCode());
            trData.setTransactionChannel(TransactionChannelEnum.API.getCode());
            trData.setApproveCode(approveCode);
            trData.setReferenceNumber(gvCodeHelper.generateReferenceNumber());
            if (StringUtil.isNotEmpty(notes)){
                trData.setNotes(notes);
            }
            dataArrayList.add(trData);
        });



        transactionDataService.insertList(dataArrayList);

        if (typeEnum == TransactionTypeEnum.GIFT_CARD_ACTIVATE){
            //修改booklet状态
            if (CollectionUtils.isNotEmpty(toBeVerifiedList)){
                voucherBookletService.voucherActivateUpdateBookletStatus(VoucherActivateUpdateBookletStatusDto.builder()
                        .voucherCode(toBeVerifiedList.stream().map(x->x.getVoucherCode()).collect(Collectors.toList()))
                        .statusEnum(BookletStatusEnum.PARTIALLY_ACTIVATED)
                        .type("0")
                        .build());
            }
        }


    }

    @Override
    public CreateAndIssueGinseng createAndIssue(CreateAndIssueParam request) {

        /**
         {
         "TransactionId":1245,
         "CardProgramGroupName": "CPG",
         "Notes": "Create and issue",
         "Amount": 500,
         "DateAtClient": "2018-10-18T17:08:14",
         "ThemeId" : "Birthday01"
         }
         */

        /**
         *
         * 1.数据字典配置  VCR 0 面额
         *
         * 2.createAndIssue(不激活)   先判断vpg的面额,
         * 							如果不是0,按vpg的面额创建新的券;
         * 							如果是0,按照入参的面额创建.
         * 								如果入参面额为0,报错;
         *
         * 3.测试是否能正常使用
         *
         *
         */
        CreateAndIssueGinseng andIssueGinseng = new CreateAndIssueGinseng();
        andIssueGinseng.setOriginalTransactionId(0);
        andIssueGinseng.setPaymentMode(0);
        andIssueGinseng.setPreviousBalance(BigDecimal.ZERO);
        andIssueGinseng.setPromotionalValue(BigDecimal.ZERO);
        andIssueGinseng.setPurchaseOrderValue(BigDecimal.ZERO);
        andIssueGinseng.setReloadableAmount(BigDecimal.ZERO);
        andIssueGinseng.setCumulativeAmountSpent(BigDecimal.ZERO);
        andIssueGinseng.setCurrencyConversionRate(BigDecimal.ZERO);
        andIssueGinseng.setCurrencyConvertedAmount(BigDecimal.ZERO);
        andIssueGinseng.setDiscountAmount(BigDecimal.ZERO);
        andIssueGinseng.setDiscountPercentage(BigDecimal.ZERO);
        andIssueGinseng.setEarnedValue(BigDecimal.ZERO);
        andIssueGinseng.setNewBatchNumber(0);
        andIssueGinseng.setOriginalActivationAmount(BigDecimal.ZERO);
        andIssueGinseng.setTransferCardBalance(BigDecimal.ZERO);
        andIssueGinseng.setXactionAmountConvertedValue(BigDecimal.ZERO);
        andIssueGinseng.setOriginalBatchNumber(0);
        andIssueGinseng.setOriginalTransactionId(0);


        Pos pos = masterDataCache.getPos(request.getTerminalId());
        if (null == pos) {
            throw new GTechBaseException(ResultErrorCodeEnum.NO_POS_DATA_FOUND.code(), ResultErrorCodeEnum.NO_POS_DATA_FOUND.desc());
        }


        //生成一个已激活的电子券

        Result<GetCpgResponse> cpgByCpgName;
        try {
            cpgByCpgName = cpgService.getCpgByCpgName(request.getCardProgramGroupName());
        } catch (Exception e) {
            //cpg error
            throw new GTechBaseException(ResultErrorCodeEnum.GET_CPG_ERROR.code(), ResultErrorCodeEnum.GET_CPG_ERROR.desc());
        }



        //MER-1944 已经禁用的cpg不能生成券
        if(cpgByCpgName.getData().getStatus().equals(GvcoreConstants.STATUS_DISABLE)){
            //cpg error
            throw new GTechBaseException(ResultErrorCodeEnum.GET_CPG_ERROR.code(), ResultErrorCodeEnum.GET_CPG_ERROR.desc());
        }




        Result<Object> cpgTypeResult;
        try {
            cpgTypeResult = cpgTypeService.getCpgType(GetCpgTypeRequest.builder().cpgTypeCode(cpgByCpgName.getData().getCpgTypeCode()).build());
        } catch (Exception e) {
            //cpg error
            throw new GTechBaseException(ResultErrorCodeEnum.GET_CPG_ERROR.code(), ResultErrorCodeEnum.GET_CPG_ERROR.desc());
        }

        GetCpgTypeResponse cpgType = (GetCpgTypeResponse) cpgTypeResult.getData();

        Outlet outlet = masterDataCache.getOutlet(pos.getOutletCode());
        if(null == outlet) {
            //cpg error
            throw new GTechBaseException(ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.code(), ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.desc());
        }


        GetCpgResponse cpg = cpgByCpgName.getData();
        String voucherBatchCode = gvCodeHelper.generateVoucherBatchCode();
        String next = StringUtil.EMPTY;
        String createUser = request.getTerminalId();
        Set<String> digitalVoucherCodes = voucherNumberHelper.voucherCodeElectronicList(cpgType.getPrefix(), cpg.getDenomination(), String.valueOf(Calendar.getInstance().get(Calendar.YEAR)), 1, cpg.getIssuerCode());
        Iterator<String> iterator = digitalVoucherCodes.stream().iterator();
        next = (String) iterator.next();
        if (StringUtil.isNotEmpty(request.getCardNumber())){
            next = request.getCardNumber();
            createUser = "DataMigration";
        }

        String pinCode = voucherNumberHelper.pinCode();
        String barCode = voucherNumberHelper.barCode27Bit(next);
        String approveCode = gvCodeHelper.generateApproveCode();
        Integer status = VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode();

        //cpgType非立即激活||面额为0 && MOP是电子券
        if ((cpgType.getAutomaticActivate().equals(CpgTypeAutomaticActivateEnum.YES.code())
                || cpg.getDenomination().equals(new BigDecimal(0)))) {
            status = VoucherStatusEnum.VOUCHER_ACTIVATED.getCode();
        }else if (cpg.getMopCode().equals(GvcoreConstants.MOP_CODE_VCE)) {
            barCode = StringUtils.EMPTY;
        }


        VoucherBatch batch = new VoucherBatch();
        batch.setPurchaseOrderNo(String.valueOf(request.getTransactionId()));
        batch.setIssuerCode(cpg.getIssuerCode());
        batch.setVoucherBatchCode(voucherBatchCode);
        batch.setCpgCode(cpg.getCpgCode());
        batch.setMopCode(cpg.getMopCode());
        batch.setArticleCode(cpg.getArticleCode());
        batch.setVoucherNum(1);

        if (cpgByCpgName.getData().getDenomination().compareTo(BigDecimal.ZERO) == 0 && null != request.getAmount()) {

            batch.setDenomination(request.getAmount());

        } else if (cpgByCpgName.getData().getDenomination().compareTo(BigDecimal.ZERO) > 0) {

            batch.setDenomination(cpgByCpgName.getData().getDenomination());

        } else {

            throw new GTechBaseException(ResultErrorCodeEnum.DENOMINATION_DOES_NOT_NULL.code(), ResultErrorCodeEnum.DENOMINATION_DOES_NOT_NULL.desc());

        }


        Date parse;

        if (StringUtil.isNotEmpty(request.getExpiry())) {
            try {
                parse = new SimpleDateFormat("ddMMyyyy").parse(request.getExpiry());
            } catch (Exception e) {
                throw new GTechBaseException();
            }
        } else {
            parse = voucherNumberHelper.cpgEffectiveDateToDate(cpg);
        }

        String notes;
        if(StringUtil.isNotEmpty(request.getNotes())){
            notes = request.getNotes();
        } else {
            notes = StringUtil.EMPTY;
        }

        batch.setVoucherEffectiveDate(parse);
        batch.setFileName("");
        batch.setFileFormat("Excel");
        batch.setStatus(MERGE);
        batch.setCreateTime(new Date());
        batch.setCreateUser(request.getTerminalId());

        commonThreadPool.execute(()->{
            voucherBatchMapper.insert(batch);
            //自动创建customerOrder
            createCustomerOrderByCreateAndIssue(request.getInvoiceNumber(), outlet, null,
                    Voucher.builder()
                            .mopCode(cpg.getMopCode())
                            .issuerCode(cpg.getIssuerCode())
                            .voucherBatchCode(voucherBatchCode)
                            .denomination(request.getAmount())
                            .cpgCode(cpg.getCpgCode())
                            .build(),
                    String.valueOf(request.getTransactionId())+String.valueOf(request.getBatchId()), notes, 1, StringUtil.EMPTY);
        });

        Date createDate = new Date();

        //添加voucher
        CustomerInfo customerInfo = null;
        if (null != request.getCustomer()) {
            customerInfo = request.getCustomer();
        }

        Voucher voucher = insterVoucher(request.getInvoiceNumber(), String.valueOf(request.getTransactionId())+String.valueOf(request.getBatchId()),
                createUser, request.getAmount(), "",
                cpg, pos, voucherBatchCode, parse, next,
                pinCode, barCode, createDate, outlet,
                batch, customerInfo, approveCode,notes,status ,request.getBatchId());



        //生成返回值

        return makeResponse(request, cpg, next, barCode, createDate, voucher, cpgType, andIssueGinseng, approveCode);
    }


    @Override
    public CancelCreateAndIssueGinseng cancelCreateAndIssue(CancelCreateandissueRequest request) {


        Result<PosResponse> posResponseResult;
        try {
            posResponseResult = posService.getPos(GetPosRequest.builder().machineId(request.getTerminalId()).build());
        } catch (Exception e) {
            //pos error
            throw new GTechBaseException();
        }


        Voucher voucherResponse;
        try {
            voucherResponse = voucherService.getVoucherByCode(request.getCardNumber());
        } catch (Exception e) {
            //cpg error
            throw new GTechBaseException(ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.code(), ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.desc());
        }


        //生成一个已激活的电子券

        Result<GetCpgResponse> cpgByCpgName;
        try {
            cpgByCpgName = cpgService.getCpg(GetCpgRequest.builder().cpgCode(voucherResponse.getCpgCode()).build());
        } catch (Exception e) {
            //cpg error
            throw new GTechBaseException(ResultErrorCodeEnum.GET_CPG_ERROR.code(), ResultErrorCodeEnum.GET_CPG_ERROR.desc());
        }


        GetCpgResponse cpg = cpgByCpgName.getData();


        //删除voucher
        voucherService.deleteByVoucherBatch(voucherResponse.getVoucherBatchCode());


        //生成返回值
        CancelCreateAndIssueGinseng cancelCreateAndIssueGinseng = makeResponse(request, voucherResponse, cpg, String.valueOf(request.getTransactionId()));


        return cancelCreateAndIssueGinseng;
    }

    private CancelCreateAndIssueGinseng makeResponse(CancelCreateandissueRequest request, Voucher voucherResponse, GetCpgResponse cpg, String approveCode) {
        CancelCreateAndIssueGinseng cancelCreateAndIssueGinseng = new CancelCreateAndIssueGinseng();
        cancelCreateAndIssueGinseng.setCurrentBatchNumber(RandomUtils.nextInt(0, 9999999));
        cancelCreateAndIssueGinseng.setTransactionId(request.getTransactionId());
        cancelCreateAndIssueGinseng.setTrackData(voucherResponse.getVoucherBarcode());
        cancelCreateAndIssueGinseng.setCardNumber(voucherResponse.getVoucherCode());
        cancelCreateAndIssueGinseng.setCardPIN(voucherResponse.getVoucherPin());
        cancelCreateAndIssueGinseng.setInvoiceNumber("");
        cancelCreateAndIssueGinseng.setAmount(BigDecimal.ZERO.setScale(1));
        cancelCreateAndIssueGinseng.setBillAmount(BigDecimal.ZERO.setScale(1));
        cancelCreateAndIssueGinseng.setOriginalInvoiceNumber(null);
        cancelCreateAndIssueGinseng.setOriginalTransactionId(0);
        cancelCreateAndIssueGinseng.setOriginalBatchNumber(0);
        cancelCreateAndIssueGinseng.setOriginalApprovalCode(null);
        cancelCreateAndIssueGinseng.setOriginalAmount(null);
        cancelCreateAndIssueGinseng.setApprovalCode(approveCode);
        cancelCreateAndIssueGinseng.setAddonCardNumber(null);
        cancelCreateAndIssueGinseng.setAddonCardTrackData(null);
        cancelCreateAndIssueGinseng.setTransferCardNumber(null);
        cancelCreateAndIssueGinseng.setMerchantName(null);
        cancelCreateAndIssueGinseng.setAdjustmentAmount(0);
        cancelCreateAndIssueGinseng.setCardExpiry(voucherResponse.getVoucherEffectiveDate());
        cancelCreateAndIssueGinseng.setOriginalCardNumber(null);
        cancelCreateAndIssueGinseng.setOriginalCardPin(null);
        cancelCreateAndIssueGinseng.setCardProgramID(null);
        cancelCreateAndIssueGinseng.setCorporateName(null);
        cancelCreateAndIssueGinseng.setNotes(null);
        cancelCreateAndIssueGinseng.setSettlementDate(T_00_00_00);
        cancelCreateAndIssueGinseng.setExpiry(T_00_00_00);
        cancelCreateAndIssueGinseng.setPurchaseOrderNumber(null);
        cancelCreateAndIssueGinseng.setPurchaseOrderValue(0);
        cancelCreateAndIssueGinseng.setDiscountPercentage(0);
        cancelCreateAndIssueGinseng.setDiscountAmount(0);
        cancelCreateAndIssueGinseng.setPaymentMode(0);
        cancelCreateAndIssueGinseng.setPaymentDetails(null);
        cancelCreateAndIssueGinseng.setBulkType(false);
        cancelCreateAndIssueGinseng.setExternalCorporateId(null);
        cancelCreateAndIssueGinseng.setExternalCardNumber(null);
        cancelCreateAndIssueGinseng.setResponseCode(0);
        cancelCreateAndIssueGinseng.setResponseMessage(TRANSACTION_SUCCESSFUL);
        cancelCreateAndIssueGinseng.setMerchantOutletName(null);
        cancelCreateAndIssueGinseng.setMerchantOutletAddress1(null);
        cancelCreateAndIssueGinseng.setMerchantOutletAddress2(null);
        cancelCreateAndIssueGinseng.setMerchantOutletCity(null);
        cancelCreateAndIssueGinseng.setMerchantOutletState(null);
        cancelCreateAndIssueGinseng.setMerchantOutletPinCode(null);
        cancelCreateAndIssueGinseng.setMerchantOutletPhone(null);
        cancelCreateAndIssueGinseng.setMaskCard(null);
        cancelCreateAndIssueGinseng.setPrintMerchantCopy(null);
        cancelCreateAndIssueGinseng.setInvoiceNumberMandatory(null);
        cancelCreateAndIssueGinseng.setNumericUserPwd(null);
        cancelCreateAndIssueGinseng.setIntegerAmount(null);
        cancelCreateAndIssueGinseng.setCulture(null);
        cancelCreateAndIssueGinseng.setCurrencySymbol(null);
        cancelCreateAndIssueGinseng.setCurrencyPosition(null);
        cancelCreateAndIssueGinseng.setCurrencyDecimalDigits(null);
        cancelCreateAndIssueGinseng.setDisplayUnitForPoints(null);
        cancelCreateAndIssueGinseng.setReceiptFooterLine1(null);
        cancelCreateAndIssueGinseng.setReceiptFooterLine2(null);
        cancelCreateAndIssueGinseng.setReceiptFooterLine3(null);
        cancelCreateAndIssueGinseng.setReceiptFooterLine4(null);
        cancelCreateAndIssueGinseng.setResult(false);
        cancelCreateAndIssueGinseng.setTransferCardExpiry(T_00_00_00);
        cancelCreateAndIssueGinseng.setTransferCardBalance(0);
        cancelCreateAndIssueGinseng.setCardStatusId(voucherResponse.getVoucherStatus() == 0 ?
                Integer.valueOf(VoucherEnableDisablePosEnum.getStatusIdByCode(voucherResponse.getVoucherStatus())) :
                Integer.valueOf(VoucherStatusPosEnum.getStatusIdByCode(voucherResponse.getStatus())));
        cancelCreateAndIssueGinseng.setCardStatus(null);
        cancelCreateAndIssueGinseng.setCardCurrencySymbol(CardCurrencySymbolEnum.getByCode(cpg.getCurrencyCode()));
        cancelCreateAndIssueGinseng.setActivationDate(T_00_00_00);
        cancelCreateAndIssueGinseng.setSVRecentTransactions(null);
        cancelCreateAndIssueGinseng.setCardType(cpg.getCpgName());
        cancelCreateAndIssueGinseng.setTransferCardTrackData(null);
        cancelCreateAndIssueGinseng.setTransferCardPin(null);
        cancelCreateAndIssueGinseng.setCumulativeAmountSpent(0);
        cancelCreateAndIssueGinseng.setCurrencyConversionRate(0);
        cancelCreateAndIssueGinseng.setCurrencyConvertedAmount(0);
        cancelCreateAndIssueGinseng.setCardHolderName(null);
        cancelCreateAndIssueGinseng.setStoredValueUnitID(0);
        cancelCreateAndIssueGinseng.setXactionAmountConvertedValue(0);
        cancelCreateAndIssueGinseng.setStoredValueConvertedAmount(0);
        cancelCreateAndIssueGinseng.setPromotionalValue(0);
        cancelCreateAndIssueGinseng.setEarnedValue(0);
        cancelCreateAndIssueGinseng.setTransactionAmount(0);
        cancelCreateAndIssueGinseng.setPreviousBalance(0);
        cancelCreateAndIssueGinseng.setUpgradedCardProgramGroupName(null);
        cancelCreateAndIssueGinseng.setNewBatchNumber(0);
        cancelCreateAndIssueGinseng.setOriginalActivationAmount(0);
        cancelCreateAndIssueGinseng.setCardCreationType(null);
        cancelCreateAndIssueGinseng.setErrorCode(null);
        cancelCreateAndIssueGinseng.setErrorDescription(null);
        cancelCreateAndIssueGinseng.setCardProgramGroupType(null);
        cancelCreateAndIssueGinseng.setActivationCode(null);
        cancelCreateAndIssueGinseng.setActivationURL(null);
        cancelCreateAndIssueGinseng.setMerchantID(0);
        cancelCreateAndIssueGinseng.setReloadableAmount(0);
        cancelCreateAndIssueGinseng.setBarcode(null);
        cancelCreateAndIssueGinseng.setCustomer(null);
        cancelCreateAndIssueGinseng.setTransactionType("28");
        return cancelCreateAndIssueGinseng;
    }


    @Override
    public CreateAndIssueResponse transactionsCreateAndIssue(TransactionsRequest request) {

        CreateAndIssueResponse response = new CreateAndIssueResponse();
        BigDecimal totalAmount = BigDecimal.ZERO;
        CopyOnWriteArrayList<CardsResponse> cards = new CopyOnWriteArrayList<>();


        Pos pos = masterDataCache.getPos(request.getTerminalId());
        if (pos == null){
            throw new  GTechBaseException(ResultErrorCodeEnum.NO_POS_DATA_FOUND.code(), ResultErrorCodeEnum.NO_POS_DATA_FOUND.desc());
        }



        String numberOfCards = request.getNumberOfCards();

        //获取cpg
        Result<GetCpgResponse> cpgByCpgName;
        try {
            cpgByCpgName = cpgService.getCpgByCpgName(request.getCards().get(0).getCardProgramGroupName());
        } catch (Exception e) {
            //cpg error
            throw new GTechBaseException(ResultErrorCodeEnum.GET_CPG_ERROR.code(), ResultErrorCodeEnum.GET_CPG_ERROR.desc());
        }
        Result<Object> cpgTypeResult;
        try {
            cpgTypeResult = cpgTypeService.getCpgType(GetCpgTypeRequest.builder().cpgTypeCode(cpgByCpgName.getData().getCpgTypeCode()).build());
        } catch (Exception e) {
            //cpg error
            throw new GTechBaseException(ResultErrorCodeEnum.GET_CPG_ERROR.code(), ResultErrorCodeEnum.GET_CPG_ERROR.desc());
        }
        GetCpgTypeResponse cpgType = (GetCpgTypeResponse) cpgTypeResult.getData();
        Outlet outletResponse;
        try {
            outletResponse = masterDataCache.getOutlet(pos.getOutletCode());
        } catch (Exception e) {
            //cpg error
            throw new GTechBaseException(ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.code(), ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.desc());
        }
        GetCpgResponse cpg = cpgByCpgName.getData();

        String notes = StringUtil.EMPTY;
        if (StringUtil.isNotEmpty(request.getNotes())){
            notes = request.getNotes();
        }
        Date createDate = new Date();


        String invoiceNo = request.getInvoiceNumber();
        if (StringUtils.isEmpty(invoiceNo)) {
            invoiceNo = gvCodeHelper.generateInvoiceNumber();
        }

        String key = String.valueOf(request.getTransactionId())
                + String.valueOf(request.getBatchId())
                + String.valueOf(invoiceNo);

        String approvalCode = (String)redisTemplate.opsForValue().get(key);
        if (StringUtils.isEmpty(approvalCode)) {
            approvalCode = gvCodeHelper.generateApproveCode();
            redisTemplate.opsForValue().set(key,approvalCode,2, TimeUnit.DAYS);
        }

        response.setApprovalCode(approvalCode);


        // 判断是否有相同的transactionId

        CustomerOrder customerOrder = customerOrderMapper.selectOne(CustomerOrder.builder().customerOrderCode(key).build());
        String meansOfPaymentCode = StringUtil.EMPTY;
        Date date = voucherNumberHelper.cpgEffectiveDateToDate(cpg);
        if (customerOrder != null) {
            // 如果有

            //合并订单
            combinedOrder(key, numberOfCards, cpg, customerOrder);

            VoucherBatchResponse voucherBatch = voucherBatchService.getVoucherBatch(GetVoucherBatchRequest.builder().purchaseOrderNo(key).build());
            VoucherBatch batch = BeanCopyUtils.jsonCopyBean(voucherBatch, VoucherBatch.class);

            totalAmount = addVoucher(request, cards, numberOfCards, cpgType, outletResponse, cpg, pos, notes, createDate, invoiceNo, key, approvalCode, voucherBatch.getVoucherBatchCode(), date, batch);


        }else {
            // 如果没有，创建新的订单数据

            //TODO 如果是并发场景,可能会导致重复创建订单,判断异常后,需要合并订单数据
            String voucherBatchCode = gvCodeHelper.generateVoucherBatchCode();


            //添加批次信息
            VoucherBatch batch = getVoucherBatch(request, cpg, pos, voucherBatchCode, date, key);

            if (StringUtil.isNotEmpty(request.getNotes()) && request.getNotes().contains("{") && request.getNotes().contains("}")) {
                //获取request.getNotes参数${}中的内容,再根据-符号分割,获取-符号后面的内容
                MeansOfPayment meansOfPaymentsByName = meansOfPaymentService.getMeansOfPaymentsByName(request.getNotes().replace("{", "").replace("}", "").split("~")[1]);
                if (meansOfPaymentsByName != null) {
                    meansOfPaymentCode = meansOfPaymentsByName.getMeansOfPaymentCode();
                }
            }


            //自动创建customerOrder
            createCustomerOrderByCreateAndIssue(request.getInvoiceNumber(), outletResponse, null,
                    Voucher.builder()
                            .mopCode(cpg.getMopCode())
                            .issuerCode(cpg.getIssuerCode())
                            .voucherBatchCode(voucherBatchCode)
                            .denomination(cpg.getDenomination())
                            .cpgCode(cpg.getCpgCode())
                            .build(),
                    key, notes,Integer.valueOf(numberOfCards),meansOfPaymentCode);

            totalAmount = addVoucher(request, cards, numberOfCards, cpgType, outletResponse, cpg, pos, notes, createDate, invoiceNo, key, approvalCode, voucherBatchCode, date, batch);
        }


        return makeResponse(request, response, totalAmount, cards);

    }

    private BigDecimal addVoucher(TransactionsRequest request, CopyOnWriteArrayList<CardsResponse> cards, String numberOfCards, GetCpgTypeResponse cpgType, Outlet outletResponse, GetCpgResponse cpg, Pos pos, String notes, Date createDate, String invoiceNo, String key, String approvalCode, String voucherBatchCode, Date date, VoucherBatch batch) {
        BigDecimal totalAmount;
        ArrayList<Voucher> voucherList = new ArrayList<>();
        ArrayList<String> voucherCodeList = new ArrayList<>();
        if(CollectionUtils.isEmpty(request.getCardNumbers())){
            for (Integer i = 0; i < Integer.valueOf(numberOfCards); i++) {
                Set<String> digitalVoucherCodes = voucherNumberHelper.voucherCodeElectronicList(cpgType.getPrefix(), cpg.getDenomination(), String.valueOf(Calendar.getInstance().get(Calendar.YEAR)), 1, "MAP");
                Iterator<String> iterator = digitalVoucherCodes.stream().iterator();
                String next = (String) iterator.next();
                voucherCodeList.add(next);
            }
        }else {
            voucherCodeList.addAll(request.getCardNumbers());
        }

        String email = StringUtil.EMPTY;
        if (null != request.getPurchaser() && StringUtil.isNotBlank(request.getPurchaser().getEmail())){
            email =request.getPurchaser().getEmail() ;
        }


        for (String voucherCode : voucherCodeList) {
            //添加voucher
            Voucher voucher = insterVoucher(request.getInvoiceNumber(), key,
                    request.getTerminalId(), cpg.getDenomination(), email,
                    cpg, pos, voucherBatchCode, date, voucherCode, voucherNumberHelper.pinCode(), voucherNumberHelper.barCode27Bit(voucherCode),
                    createDate, outletResponse, batch, null, approvalCode, notes
                    ,VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode() ,request.getBatchId());
            voucherList.add(voucher);
        }
        String finalApprovalCode = approvalCode;
        String finalInvoiceNo = invoiceNo;
        voucherList.forEach(voucher -> {
            //返回值card
            CardsResponse card = getCardsResponse(request, cpgType, cpg, finalApprovalCode, voucher, finalInvoiceNo);
            cards.add(card);
        });
        //总金额
        totalAmount = cpg.getDenomination().multiply(new BigDecimal(numberOfCards));
        return totalAmount;
    }

    private void insterVoucher(TransactionsRequest request, String numberOfCards, GetCpgTypeResponse cpgType, Outlet outletResponse, GetCpgResponse cpg, Pos pos, String notes, Date createDate, String key) {
        VoucherBatchResponse voucherBatch = voucherBatchService.getVoucherBatch(GetVoucherBatchRequest.builder().purchaseOrderNo(key).build());

        // 添加券信息
        for (Integer i = 0; i < Integer.valueOf(numberOfCards); i++) {
            Date date = voucherNumberHelper.cpgEffectiveDateToDate(cpg);
            Set<String> digitalVoucherCodes = voucherNumberHelper.voucherCodeElectronicList(cpgType.getPrefix(), cpg.getDenomination(), String.valueOf(Calendar.getInstance().get(Calendar.YEAR)), 1, "MAP");
            Iterator<String> iterator = digitalVoucherCodes.stream().iterator();
            String next = (String) iterator.next();
            String pinCode = voucherNumberHelper.pinCode();
            String barCode = voucherNumberHelper.barCode27Bit(next);
            String approveCode = gvCodeHelper.generateApproveCode();

            //查询transactionId = key的最新一条交易数据
            Example example = new Example(TransactionData.class);
            example.createCriteria().andEqualTo("transactionId",key);
            List<TransactionData> transactionData = transactionDataMapper.selectByCondition(example);
            if (CollectionUtils.isNotEmpty(transactionData)){
                //根据创建时间获取最新一条交易数据
                TransactionData resultData
                        = transactionData.stream().max(Comparator.comparing(TransactionData::getCreateTime)).get();
                approveCode = resultData.getApproveCode();
            }


            Voucher voucher = insterVoucher(request.getInvoiceNumber(), key,
                    request.getTerminalId(), cpg.getDenomination(), "",
                    cpg, pos, voucherBatch.getVoucherBatchCode(), date, next, pinCode, barCode,
                    createDate, outletResponse, BeanCopyUtils.jsonCopyBean(voucherBatch,VoucherBatch.class)
                    , null, approveCode, notes
                    ,VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode(),request.getBatchId() );
        }
    }

    private void combinedOrder(String key, String numberOfCards, GetCpgResponse cpg, CustomerOrder customerOrder) {
        // 合并订单数据
        customerOrder.setVoucherNum(customerOrder.getVoucherNum() + Integer.parseInt(numberOfCards));
        customerOrder.setVoucherAmount(customerOrder.getVoucherAmount().add(cpg.getDenomination().multiply(new BigDecimal(numberOfCards))));
        // 更新订单数据
        Example customerExample = new Example(CustomerOrder.class);
        customerExample.createCriteria().andEqualTo("customerOrderCode", key);
        customerOrderMapper.updateByConditionSelective(customerOrder,customerExample);
        //详情信息
        //更新订单详情
        CustomerOrderDetails customerOrderDetails = customerOrderDetailsMapper.selectOne(CustomerOrderDetails
                .builder()
                .customerOrderCode(key)
                .cpgCode(cpg.getCpgCode())
                .build());

        customerOrderDetails.setDenomination(cpg.getDenomination());
        customerOrderDetails.setVoucherNum(customerOrderDetails.getVoucherNum() + Integer.valueOf(numberOfCards));
        customerOrderDetails.setVoucherAmount(customerOrderDetails.getVoucherAmount().add(cpg.getDenomination().multiply(new BigDecimal(numberOfCards))));
        customerOrderDetails.setAmount(customerOrderDetails.getAmount().add(cpg.getDenomination().multiply(new BigDecimal(numberOfCards))));

        Example detailExample = new Example(CustomerOrderDetails.class);
        detailExample.createCriteria()
                .andEqualTo("customerOrderCode", key)
                .andEqualTo("cpgCode", cpg.getCpgCode());

        customerOrderDetailsMapper.updateByConditionSelective(customerOrderDetails,detailExample);
    }

    private CreateAndIssueResponse makeResponse(TransactionsRequest request, CreateAndIssueResponse response, BigDecimal totalAmount, List<CardsResponse> cards) {
        response.setTransactionId(Long.valueOf(request.getTransactionId()));
        response.setCurrentBatchNumber(Long.valueOf(RandomUtils.nextInt(0, 9999999)));
        response.setTransactionTypeId(Integer.valueOf(request.getTransactionTypeId()));
        response.setTotalAmount(totalAmount);
        response.setNotes(request.getNotes());
        response.setResponseCode(0);
        response.setResponseMessage("Transaction Successful.");
        response.setErrorCode(null);
        response.setErrorDescription(null);
        response.setInputType(request.getInputType());
        response.setTotalCards(Integer.valueOf(request.getNumberOfCards()));
        response.setNumberOfCards(Integer.valueOf(request.getNumberOfCards()));
        response.setCards(cards);
        response.setBusinessReferenceNumber(request.getBusinessReferenceNumber());
        response.setIdempotencyKey(null);
        response.setGeneralLedger(null);
        response.setCostCentre(null);
        response.setExecutionMode(0);
        return response;
    }

    private CardsResponse getCardsResponse(TransactionsRequest request, GetCpgTypeResponse cpgType, GetCpgResponse cpg, String approveCode, Voucher voucher, String invoiceNumber) {
        CardsResponse card = new CardsResponse();
        card.setVoucherNumber(voucher.getVoucherCode());
        card.setVoucherPIN(voucher.getVoucherPin());
        card.setCorporateName(null);
        card.setBalance(voucher.getDenomination());
        card.setVoucherType(cpg.getCpgName());
        card.setExpiryDate(voucher.getVoucherEffectiveDate());
        card.setVoucherStatus("Purchased");
        card.setTransferVoucherNumber(null);
        card.setTransferVoucherBalance(0);
        card.setTransferVoucherExpiryDate(null);
        card.setVoucherStatusId(voucher.getVoucherStatus() == 0 ?
                Integer.valueOf(VoucherEnableDisablePosEnum.getStatusIdByCode(voucher.getVoucherStatus()))
                : Integer.valueOf(VoucherStatusPosEnum.getStatusIdByCode(voucher.getStatus())));
        card.setVoucherCurrencySymbol(CardCurrencySymbolEnum.getByCode(cpg.getCurrencyCode()));
        card.setCurrency("IDR");
        card.setVoucherCreationType(VIRTUAL);
        Date isoDate = dateToISODate(DateUtil.parseDate(DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS), DateUtil.FORMAT_YYYYMMDDHHMISS));
        card.setActivationDate(isoDate);


        Holder holder = new Holder();
        holder.setSalutation("Not Specified");
        holder.setFirstName(null == request.getPurchaser() ? null : request.getPurchaser().getFirstName());
        holder.setLastName(null == request.getPurchaser() ? null : request.getPurchaser().getLastName());


        card.setHolder(holder);
        card.setAdjustmentAmount(0);
        card.setInvoiceNumber(invoiceNumber);
        card.setVpgType(cpgType.getCpgTypeName());
        card.setActivationAmount(voucher.getDenomination());
        card.setPreXactionCardBalance(BigDecimal.ZERO);
        card.setTransactionAmount(voucher.getDenomination());
        card.setIssuerName("MAP");
        card.setEmployeeId(null);
        card.setReloadableAmount(BigDecimal.ZERO);
        card.setTransactionTimestamp(isoDate);
        card.setNativeVoucherBalance(CardCurrencySymbolEnum.getByCode(cpg.getCurrencyCode()) + " " + voucher.getDenomination());
        card.setNativeBalance(voucher.getDenomination().toString());
        card.setCurrencyConvertedXactionAmount(BigDecimal.ZERO);
        card.setCurrencyConversionRate(null);
        card.setPreXactionCardBalanceInNativeCurrency("0.0000");
        card.setPreXactionCardBalanceInNativeCurrencyWithSymbol(CardCurrencySymbolEnum.getByCode(cpg.getCurrencyCode()) + " 0.0000");
        card.setThemeId(null);
        card.setTrackData(voucher.getVoucherBarcode() + "=" + voucher.getVoucherBarcode() + "?");
        card.setActivationCode(voucher.getVoucherActiveCode());
        card.setVoucherIssuingMode("STORE");
        card.setActivationURL(voucher.getVoucherActiveUrl());
        card.setBarcode(voucher.getVoucherBarcode());
        card.setApprovalCode(approveCode);
//        card.setTotalPreAuthAmount(BigDecimal.ZERO);
//        card.setTotalReloadedAmount(BigDecimal.ZERO);
//        card.setTotalRedeemedAmount(BigDecimal.ZERO);
        card.setResponseCode(0);
        card.setResponseMessage(TRANSACTION_SUCCESSFUL);
        card.setErrorCode(null);
        card.setErrorDescription(null);
        card.setRedeemStartDate(null);
//        card.setPreAuthCode(null);
//        card.setRecentTransactions(null);
        card.setTransferable(true);
        card.setReusable(false);
        card.setExtendedParameters(null);
//        card.setBeneficiaries(null);
//        card.setNotes(request.getNotes());
//        card.setReason(null);

        CardFormats CNONLY = new CardFormats();
        CNONLY.setKey("CNONLY");
        CNONLY.setValue(voucher.getVoucherCode());

        CardFormats qcbarcode26V1 = new CardFormats();
        qcbarcode26V1.setKey("QCBARCODE-26-V1");
        qcbarcode26V1.setValue(voucher.getVoucherBarcode());

        card.setVoucherFormat(Lists.newArrayList(CNONLY, qcbarcode26V1));
//        card.setSequenceNumber(null);
        return card;
    }

    private VoucherBatch getVoucherBatch(TransactionsRequest request, GetCpgResponse cpg, Pos pos, String voucherBatchCode, Date date, String key) {
        VoucherBatch batch = new VoucherBatch();
        batch.setPurchaseOrderNo(key);
        batch.setIssuerCode(cpg.getIssuerCode());
        batch.setVoucherBatchCode(voucherBatchCode);
        batch.setCpgCode(cpg.getCpgCode());
        batch.setMopCode(cpg.getMopCode());
        batch.setArticleCode(cpg.getArticleCode());
        batch.setVoucherNum(Integer.valueOf(request.getNumberOfCards()));
        batch.setDenomination(cpg.getDenomination());


        batch.setVoucherEffectiveDate(date);
        batch.setFileName("");
        batch.setFileFormat("Excel");
        batch.setStatus(MERGE);
        batch.setCreateTime(new Date());
        batch.setCreateUser(request.getTerminalId());
        voucherBatchMapper.insert(batch);
        return batch;
    }


    public static Date dateToISODate(Date date) {
        //T代表后面跟着时间，Z代表UTC统一时间
        SimpleDateFormat format =
                new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        format.setCalendar(new GregorianCalendar(new SimpleTimeZone(0, "GMT")));
        String isoDate = format.format(date);
        try {
            return format.parse(isoDate);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    public static Date formatD(String dateStr, String format) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        Date ret = null;
        try {
            ret = simpleDateFormat.parse(dateStr);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return ret;
    }


    public static String getISO8601Time(Date date) {
        TimeZone tz = TimeZone.getTimeZone("UTC");
        DateFormat dft = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        dft.setTimeZone(tz);
        return dft.format(date);
    }


    public Voucher insterVoucher(String invoiceNumber,
                                 String transactionId,
                                 String terminalId,
                                 BigDecimal amount,
                                 String customerEmail,
                                 GetCpgResponse cpg,
                                 Pos pos,
                                 String voucherBatchCode,
                                 Date effectiveDate,
                                 String next,
                                 String pinCode,
                                 String barCode,
                                 Date createDate,
                                 Outlet outlet,
                                 VoucherBatch batch,
                                 CustomerInfo customerInfo,
                                 String approveCode, String notes, Integer status,String batchId) {
        CreateVoucherRequest createVoucherRequest = new CreateVoucherRequest();
        createVoucherRequest.setIssuerCode(cpg.getIssuerCode());
        createVoucherRequest.setVoucherBatchCode(voucherBatchCode);
        createVoucherRequest.setVoucherCode(next);
        createVoucherRequest.setVoucherCodeNum(Long.valueOf(next.replaceAll(RE, "")));
        createVoucherRequest.setCpgCode(cpg.getCpgCode());
        createVoucherRequest.setMopCode(cpg.getMopCode());
        createVoucherRequest.setDenomination(amount);
        createVoucherRequest.setVoucherPin(pinCode);
        createVoucherRequest.setVoucherEffectiveDate(effectiveDate);
        if (StringUtils.isNotBlank(barCode))
            createVoucherRequest.setVoucherBarcode(barCode);


        //卡券不激活 MER-1883已经更改,修改为根据cpg和面额判断是否激活
        createVoucherRequest.setStatus(status);
        createVoucherRequest.setCirculationStatus(VoucherCirculationStatusEnum.VOUCHER_RECEIVED.getCode());
        createVoucherRequest.setCreateTime(createDate);
        createVoucherRequest.setLogType(VoucherLogTypeEnum.DIGITAL_VOUCHER.code());
        createVoucherRequest.setCreateUser(terminalId);
        String activationCode = voucherNumberHelper.activationCode();
        createVoucherRequest.setVoucherActiveCode(activationCode);
        createVoucherRequest.setVoucherActiveUrl(activeUrl + activationCode);

        if (null != customerInfo && StringUtils.isNotBlank(customerInfo.getEmail())) {
            createVoucherRequest.setVoucherOwnerCode(customerInfo.getEmail());
        }
        if (StringUtil.isNotBlank(customerEmail)){
            createVoucherRequest.setVoucherOwnerCode(customerEmail);
        }
        createVoucherRequest.setVoucherOwnerType(CUSTOMER);
        createVoucherRequest.setSalesTime(new Date());
        createVoucherRequest.setSalesOutlet(outlet.getOutletCode());
        /*else {
            createVoucherRequest.setVoucherOwnerCode(outlet.getOutletCode());
            createVoucherRequest.setVoucherOwnerType(OUTLET);
        }*/
        createVoucherRequest.setVoucherStatus(GvcoreConstants.STATUS_ENABLE);

        Voucher voucher = BeanCopyUtils.jsonCopyBean(createVoucherRequest, Voucher.class);
        setDefaultValue(voucher);


        //添加transactionData数据
        CompletableFuture.runAsync(() ->
        {
            voucherMapper.insert(voucher);
            createTransactionData(transactionId, invoiceNumber, cpg, pos, voucherBatchCode, createDate,
                    outlet, batch, voucher, TransactionTypeEnum.GIFT_CARD_NEW_GENERATE, customerInfo, approveCode, notes,batchId);
            createTransactionData(transactionId, invoiceNumber, cpg, pos, voucherBatchCode, createDate,
                    outlet, batch, voucher, TransactionTypeEnum.GIFT_CARD_SELL, customerInfo, approveCode, notes,batchId);
            if (voucher.getStatus().equals(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode())) {
                //激活记录
                createTransactionData(transactionId, invoiceNumber, cpg, pos, voucherBatchCode, createDate,
                        outlet, batch, voucher, TransactionTypeEnum.GIFT_CARD_ACTIVATE, customerInfo, approveCode, notes,batchId);
            }
        }, executor);







        /*
        voucherMapper.insert(voucher);

        //添加transactionData数据
        createTransactionData(transactionId, invoiceNumber, cpg, pos, voucherBatchCode, createDate,
                outlet, batch, voucher, TransactionTypeEnum.GIFT_CARD_NEW_GENERATE, customerInfo, approveCode, notes);
        //记录销售
        createTransactionData(transactionId, invoiceNumber, cpg, pos, voucherBatchCode, createDate,
                outlet, batch, voucher, TransactionTypeEnum.GIFT_CARD_SELL, customerInfo, approveCode,notes );

        if (voucher.getStatus()== VoucherStatusEnum.VOUCHER_ACTIVATED.getCode()){
            //激活记录
            createTransactionData(transactionId, invoiceNumber, cpg, pos, voucherBatchCode, createDate,
                    outlet, batch, voucher, TransactionTypeEnum.GIFT_CARD_ACTIVATE, customerInfo, approveCode,notes );
        }

        */


       


        return voucher;
    }

    public CreateCustomerOrderRequest createCustomerOrderByCreateAndIssue(String invoiceNumber, Outlet outlet, CustomerInfo customerInfo, Voucher voucher, String transactionId, String notes, Integer voucherNum, String meansOfPaymentCode) {
        CreateCustomerOrderRequest customerOrderRequest = new CreateCustomerOrderRequest();
        customerOrderRequest.setCustomerOrderCode(transactionId);
        customerOrderRequest.setIssuerCode(voucher.getIssuerCode());
        customerOrderRequest.setOutletCode(outlet.getOutletCode());
        customerOrderRequest.setInvoiceNo(invoiceNumber);
        customerOrderRequest.setPurchaseOrderNo(outlet.getOutletName() + System.currentTimeMillis());
        customerOrderRequest.setMopCode(voucher.getMopCode());
        customerOrderRequest.setVoucherNum(voucherNum);
        customerOrderRequest.setVoucherAmount(voucher.getDenomination().multiply(new BigDecimal(voucherNum)));
        customerOrderRequest.setDiscount(new BigDecimal("0"));
        customerOrderRequest.setAmount(BigDecimal.ZERO);
        if (voucher.getMopCode().equals(GvcoreConstants.MOP_CODE_VCE)) {
            customerOrderRequest.setVoucherBatchCode(voucher.getVoucherBatchCode());
        }

        customerOrderRequest.setContactFirstName(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getFirstName()) ?
                customerInfo.getFirstName() : API_CUSTOMER);
        customerOrderRequest.setContactLastName(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getLastName()) ?
                customerInfo.getLastName() : API_CUSTOMER);
        customerOrderRequest.setContactPhone(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getMobile()) ?
                customerInfo.getMobile() : API_CUSTOMER);
        customerOrderRequest.setCompanyName(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getCorporatename()) ?
                customerInfo.getCorporatename() : API_CUSTOMER);
        customerOrderRequest.setContactEmail(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getEmail()) ?
                customerInfo.getEmail() : API_CUSTOMER);


        customerOrderRequest.setCurrencyCode(API_CUSTOMER);
        customerOrderRequest.setCustomerCode(API_CUSTOMER);
        customerOrderRequest.setCustomerName(API_CUSTOMER);
        customerOrderRequest.setCustomerType(API_CUSTOMER);
        customerOrderRequest.setProductCategoryCode("");
        customerOrderRequest.setDiscountType("");
        customerOrderRequest.setCreateUser("api");
        customerOrderRequest.setMeansOfPaymentCode(meansOfPaymentCode);
        customerOrderRequest.setShippingAddress("");
        customerOrderRequest.setCustomerRemarks(notes);
        customerOrderRequest.setStatus(CustomerOrderStatusEnum.API.getStatus());
        customerOrderRequest.setReleaseTime(new Date());
        customerOrderRequest.setCreateCustomerOrderDetailsRequests(Lists.newArrayList(
                CreateCustomerOrderDetailsRequest
                        .builder()
                        .cpgCode(voucher.getCpgCode())
                        .voucherNum(voucherNum)
                        .deleteStatus(2)//自动生成
                        .denomination(voucher.getDenomination())
                        .build()
        ));
        Result<String> customerOrder = customerOrderService.createCustomerOrder(customerOrderRequest);
        return customerOrderRequest;
    }

    private void createTransactionData(String transactionId,
                                       String invoiceNumber,
                                       GetCpgResponse cpg,
                                       Pos pos,
                                       String voucherBatchCode,
                                       Date createDate,
                                       Outlet outlet,
                                       VoucherBatch batch,
                                       Voucher voucher,
                                       TransactionTypeEnum typeEnum,
                                       CustomerInfo customerInfo,
                                       String approveCode, String notes,String batchId) {
        TransactionData dataRequest = new TransactionData();
        dataRequest.setTransactionId(transactionId);
        dataRequest.setTransactionType(typeEnum.getCode());
        dataRequest.setMerchantCode(outlet.getMerchantCode());
        dataRequest.setIssuerCode(voucher.getIssuerCode());
        dataRequest.setBatchId(batchId);
        dataRequest.setBillNumber("");
        dataRequest.setOutletCode(outlet.getOutletCode());
        dataRequest.setCpgCode(cpg.getCpgCode());
        dataRequest.setTransactionDate(createDate);
        dataRequest.setVoucherCode(voucher.getVoucherCode());
        dataRequest.setVoucherCodeNum(Long.valueOf(voucher.getVoucherCode().replaceAll(A_Z_A_Z, "")));
        dataRequest.setInitiatedBy("");
        dataRequest.setPosCode(pos.getPosCode());
        dataRequest.setBatchCode(voucherBatchCode);
        dataRequest.setLoginSource("");
        dataRequest.setDenomination(voucher.getDenomination());
        dataRequest.setPaidAmount(new BigDecimal("0"));
        dataRequest.setPaymentMethod("");
        dataRequest.setDiscountAmount(new BigDecimal("0"));
        dataRequest.setActualOutlet("");
        dataRequest.setForwardingEntityId("");
        dataRequest.setResponseMessage("Transaction successful.");
        dataRequest.setTransactionMode(TRANSACTION_SUCCESSFUL1);
        dataRequest.setCorporateName("");
        dataRequest.setDepartmentDivisionBranch("");
        dataRequest.setCustomerSalutation("");
        dataRequest.setCustomerFirstName("");
        dataRequest.setCustomerLastName("");
        dataRequest.setMobile("");
        dataRequest.setEmail("");
        dataRequest.setInvoiceNumber(invoiceNumber);
        dataRequest.setOtherInputParameter("{}");
        dataRequest.setCustomerType("");
        dataRequest.setSuccessOrFailure("0");
        dataRequest.setPurchaseOrderNo(batch.getPurchaseOrderNo());
        dataRequest.setVoucherEffectiveDate(voucher.getVoucherEffectiveDate());
        dataRequest.setCreateUser("");
        dataRequest.setCreateTime(new Date());
        dataRequest.setUpdateUser("");
        dataRequest.setUpdateTime(new Date());
        dataRequest.setCustomerCode("");
        dataRequest.setReferenceNumber(gvCodeHelper.generateReferenceNumber());
        dataRequest.setCardEntryMode(SWIPED);
        dataRequest.setMopCode(voucher.getMopCode());
        dataRequest.setApproveCode(approveCode);
        dataRequest.setTransactionChannel(TransactionChannelEnum.API.getCode());
        if (StringUtil.isNotEmpty(notes)){
            dataRequest.setNotes(notes);
        }
        if (null != customerInfo) {
            dataRequest.setCustomerSalutation(customerInfo.getSalutation());
            dataRequest.setCustomerFirstName(customerInfo.getFirstName());
            dataRequest.setCustomerLastName(customerInfo.getLastName());
            dataRequest.setMobile(customerInfo.getMobile());
            dataRequest.setCorporateName(customerInfo.getCorporatename());
            dataRequest.setEmail(customerInfo.getEmail());
            dataRequest.setCustomerType(customerInfo.getCustomerType());
        }
        transactionDataService.insertList(Lists.newArrayList(dataRequest));

        if (typeEnum == TransactionTypeEnum.GIFT_CARD_ACTIVATE){
            List<Voucher> strings = Lists.newArrayList(voucher);
            //修改booklet状态
            if (CollectionUtils.isNotEmpty(Lists.newArrayList(voucher.getVoucherCode()))){
                voucherBookletService.voucherActivateUpdateBookletStatus(VoucherActivateUpdateBookletStatusDto.builder()
                        .voucherCode(strings.stream().map(x->x.getVoucherCode()).collect(Collectors.toList()))
                        .statusEnum(BookletStatusEnum.PARTIALLY_ACTIVATED)
                        .type("0")
                        .build());
            }
        }


    }

    private CreateAndIssueGinseng makeResponse(CreateAndIssueParam request, GetCpgResponse cpg, String next, String barCode, Date createDate, Voucher voucher, GetCpgTypeResponse cpgType, CreateAndIssueGinseng andIssueGinseng, String approveCode) {


        andIssueGinseng.setCardNumber(next);
        //QC Mercury api checking
        andIssueGinseng.setCardPin(null);
        andIssueGinseng.setInvoiceNumber(request.getInvoiceNumber());
        andIssueGinseng.setOriginalBatchNumber(RandomUtils.nextInt(0, 99999));
        andIssueGinseng.setBulkType(false);
        andIssueGinseng.setResult(false);
        andIssueGinseng.setCardStatusId(voucher.getVoucherStatus() == 0 ?
                Integer.valueOf(VoucherEnableDisablePosEnum.getStatusIdByCode(voucher.getVoucherStatus()))
                : Integer.valueOf(VoucherStatusPosEnum.getStatusIdByCode(voucher.getStatus())));
        andIssueGinseng.setCardStatus("Purchased");
        andIssueGinseng.setCardCreationType(VIRTUAL);
        andIssueGinseng.setActivationCode(voucher.getVoucherActiveCode());
        andIssueGinseng.setActivationURL(voucher.getVoucherActiveUrl());
        andIssueGinseng.setBarcode(barCode);
        andIssueGinseng.setThemeId(null);

        andIssueGinseng.setTransactionId(request.getTransactionId());
        andIssueGinseng.setTrackData(barCode);
        andIssueGinseng.setAmount(voucher.getDenomination());
        andIssueGinseng.setApprovalCode(approveCode);
        andIssueGinseng.setCardExpiry(DateUtil.format(voucher.getVoucherEffectiveDate(), YYYY_MM_DD_T_HH_MM_SS));
        andIssueGinseng.setSettlementDate(T_00_00_00);
        andIssueGinseng.setExpiry(T_00_00_00);
        andIssueGinseng.setResponseCode(0);
        andIssueGinseng.setResponseMessage(TRANSACTION_SUCCESSFUL);
        //map
        andIssueGinseng.setCardCurrencySymbol(cpg.getCurrencyCode());
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.FFFFFFFXXX");
        df.setTimeZone(TimeZone.getDefault());
        String format = df.format(createDate);

        andIssueGinseng.setActivationDate(format);
        andIssueGinseng.setCardType(cpg.getCpgName());
        //batch id
        andIssueGinseng.setCurrentBatchNumber(RandomUtils.nextInt(0, 9999999));
        andIssueGinseng.setTransactionType("GIFT CARD ACTIVATE");
        andIssueGinseng.setTransferCardExpiry(T_00_00_00);
        //https://jira.gtech.asia/browse/MER-732
        andIssueGinseng.setTransactionAmount(BigDecimal.ZERO);
        andIssueGinseng.setCardProgramGroupType(cpgType.getCpgTypeName());
        andIssueGinseng.setMerchantId(0);
        andIssueGinseng.setMID(null);
        andIssueGinseng.setCustomer(null);

        return andIssueGinseng;
    }

    @Override
    public ActivateOnlyGinseng activateOnly(ActivateOnlyParam request) {


        if (StringUtil.isEmpty(request.getCardNumber()) &&
                StringUtil.isEmpty(request.getActivationCode())) {
            //异常暂无
            return ActivateOnlyGinseng.builder().responseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED_CHICK.code())).responseMessage(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED_CHICK.desc()).build();
        }


        /**
         {
         "TransactionId":1245,
         "CardNumber": "7777771112204471",
         "Notes": "activate only",
         "Amount": 500,
         "DateAtClient": "2018-10-18T17:08:14"
         }

         */
        ActivateOnlyGinseng activateOnlyGinseng = new ActivateOnlyGinseng();
        activateOnlyGinseng.setAmount(new BigDecimal("0"));
        activateOnlyGinseng.setBillAmount(new BigDecimal("0"));
        activateOnlyGinseng.setOriginalAmount(new BigDecimal("0"));
        activateOnlyGinseng.setAdjustmentAmount(new BigDecimal("0.0"));
        activateOnlyGinseng.setPurchaseOrderValue(new BigDecimal("0.0"));
        activateOnlyGinseng.setDiscountPercentage(new BigDecimal("0.0"));
        activateOnlyGinseng.setDiscountAmount(new BigDecimal("0.0"));
        activateOnlyGinseng.setTransferCardBalance(new BigDecimal("0.0"));
        activateOnlyGinseng.setCumulativeAmountSpent(new BigDecimal("0.0"));
        activateOnlyGinseng.setCurrencyConversionRate(new BigDecimal("0.0"));
        activateOnlyGinseng.setCurrencyConvertedAmount(new BigDecimal("0.0"));
        activateOnlyGinseng.setXactionAmountConvertedValue(new BigDecimal("0.0"));
        activateOnlyGinseng.setStoredValueConvertedAmount(new BigDecimal("0.0"));
        activateOnlyGinseng.setPromotionalValue(new BigDecimal("0.0"));
        activateOnlyGinseng.setEarnedValue(new BigDecimal("0.0"));
        activateOnlyGinseng.setTransactionAmount(new BigDecimal("0.0"));
        activateOnlyGinseng.setPreviousBalance(new BigDecimal("0.0"));
        activateOnlyGinseng.setUpgradedCardProgramGroupName("");
        activateOnlyGinseng.setOriginalActivationAmount(new BigDecimal("0"));
        activateOnlyGinseng.setReloadableAmount(new BigDecimal("0"));
        activateOnlyGinseng.setTotalPreauthAmount(new BigDecimal("0"));
        activateOnlyGinseng.setTotalRedeemedAmount(new BigDecimal("0"));
        activateOnlyGinseng.setTotalReloadedAmount(new BigDecimal("0"));

        activateOnlyGinseng.setActivationDate(T_00_00_00);
        activateOnlyGinseng.setBulkType(false);
        activateOnlyGinseng.setCardExpiry(T_00_00_00);
        activateOnlyGinseng.setSettlementDate(T_00_00_00);
        activateOnlyGinseng.setExpiry(T_00_00_00);
        activateOnlyGinseng.setStoredValueUnitID(0);

        activateOnlyGinseng.setTransferCardExpiry(T_00_00_00);


        String message = "Transaction successful.";

        Voucher example = new Voucher();
        if (StringUtil.isNotEmpty(request.getCardNumber())) {
            example.setVoucherCode(request.getCardNumber());

        }
        if (StringUtil.isNotEmpty(request.getActivationCode())) {
            example.setVoucherActiveCode(request.getActivationCode());

        }
        example.setVoucherStatus(VoucherStatusEnableDisableEnum.STATUS_ENABLE.getCode());
        Voucher toBeVerified = null;
        try {
            toBeVerified = voucherMapper.selectOne(example);
        } catch (Exception e) {
            activateOnlyGinseng.setResponseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.code()))
                    .setResponseMessage(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.desc());
        }
        if (null == toBeVerified) {
            activateOnlyGinseng.setResponseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.code()))
                    .setResponseMessage(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.desc());
            return activateOnlyGinseng;
        }

        if (StringUtil.isEmpty(activateOnlyGinseng.getResponseMessage()) && !toBeVerified.getStatus().equals(VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode())) {
            activateOnlyGinseng.setResponseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_AlREADY_ACTIVATE.code()))
                    .setResponseMessage(ResultErrorCodeEnum.VOUCHER_AlREADY_ACTIVATE.desc());

            message = ResultErrorCodeEnum.VOUCHER_AlREADY_ACTIVATE.desc();

        }

        if (StringUtil.isEmpty(activateOnlyGinseng.getResponseMessage()) && DateUtil.compareDateWithToday(toBeVerified.getVoucherEffectiveDate()).equals("-1")) {
            activateOnlyGinseng.setResponseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_AlREADY_EXPIRED.code()))
                    .setResponseMessage(ResultErrorCodeEnum.VOUCHER_AlREADY_EXPIRED.desc());
            message = ResultErrorCodeEnum.VOUCHER_AlREADY_EXPIRED.desc();
        }


        VoucherBatchResponse voucherBatch = new VoucherBatchResponse();
        try {
            voucherBatch = voucherBatchService.getVoucherBatch(GetVoucherBatchRequest.builder().voucherBatchCode(toBeVerified.getVoucherBatchCode()).build());
        } catch (Exception e) {
            //cpg error
            activateOnlyGinseng.setResponseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.code()))
                    .setResponseMessage(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.desc());
            log.error(e.getMessage());
        }
        Cpg cpg = new Cpg();
        if (null != toBeVerified) {
            cpg = masterDataCache.getCpg(toBeVerified.getCpgCode());

            GetCpgTypeRequest cpgTypeRequest = new GetCpgTypeRequest();
            cpgTypeRequest.setCpgTypeCode(cpg.getCpgTypeCode());
            activateOnlyGinseng.setDenomination(cpg.getDenomination());
        }

        ArticleMop articleMop = articleMopService.queryByArticleMopCode(cpg.getArticleMopCode());


        Pos pos = masterDataCache.getPos(request.getTerminalId());
        if(null == pos) {
            throw new GTechBaseException(ResultErrorCodeEnum.NO_POS_DATA_FOUND.code(), ResultErrorCodeEnum.NO_POS_DATA_FOUND.desc());
        }


        Outlet outlet = masterDataCache.getOutlet(pos.getOutletCode());
        if(null == outlet){
            throw new GTechBaseException(ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.code(), ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.desc());
        }

        String success = "1";

        String approveCode = gvCodeHelper.generateApproveCode();
        if (StringUtil.isEmpty(activateOnlyGinseng.getResponseMessage())) {

            Voucher updateVoucher = Voucher.builder().status(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode()).build();
            Example voucherExample = new Example(Voucher.class);
            voucherExample.createCriteria().andEqualTo(Voucher.C_VOUCHER_CODE, toBeVerified.getVoucherCode());
            voucherMapper.updateByConditionSelective(updateVoucher, voucherExample);
            success = "0";

        }
        CustomerInfo customerInfo = new CustomerInfo();
        if (null != request.getCustomer()) {
            customerInfo = request.getCustomer();

        }
        String invoiceNumber = StringUtils.EMPTY;
        if (StringUtils.isNotEmpty(request.getInvoiceNumber())){
            invoiceNumber = request.getInvoiceNumber();
        }
        String notes = StringUtils.EMPTY;
        if (StringUtils.isNotEmpty(request.getNotes())){
            notes = request.getNotes();
        }


        insertTransactionData(request.getTransactionId(), toBeVerified, voucherBatch, cpg, pos,
                outlet, TransactionTypeEnum.GIFT_CARD_ACTIVATE_ONLY,
                customerInfo, approveCode, invoiceNumber, notes, success, message,request.getBatchNumber() );


        if (StringUtil.isEmpty(activateOnlyGinseng.getResponseMessage())) {
            activateOnlyGinseng.setResponseCode(0)
                    .setResponseMessage(TRANSACTION_SUCCESSFUL);
        }


        activateOnlyGinseng.setTransactionType("22");
        activateOnlyGinseng.setCurrentBatchNumber(RandomUtils.nextInt(0, 9999999));
        activateOnlyGinseng.setTransactionId(request.getTransactionId());
        if (StringUtil.isNotEmpty(request.getNotes())) {
            activateOnlyGinseng.setNotes(request.getNotes());

        }

        if (StringUtil.isNotEmpty(activateOnlyGinseng.getResponseMessage()) && activateOnlyGinseng.getResponseCode().equals(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.code()))) {
            return activateOnlyGinseng;
        }

        activateOnlyGinseng.setOriginalTransactionId(0);
        activateOnlyGinseng.setOriginalBatchNumber(0);

        activateOnlyGinseng.setPaymentMode(0);
        if (0 == activateOnlyGinseng.getResponseCode()) {
            activateOnlyGinseng.setCardStatus(VoucherStatusPosEnum.VOUCHER_ACTIVATED.getDesc());
        } else {
            activateOnlyGinseng.setCardStatus(VoucherStatusPosEnum.getDescByCode(toBeVerified.getStatus()));
        }
        activateOnlyGinseng.setCardStatusId(140);


        //展示IDR
        activateOnlyGinseng.setCardCurrencySymbol(cpg.getCurrencyCode());
        activateOnlyGinseng.setNewBatchNumber(0);
        activateOnlyGinseng.setCardCreationType(GvPosCardCreationTypeEnum
                .getDescByVoucherCardCreationTypeCode(articleMop.getMopCode()));
        //硬编码
        activateOnlyGinseng.setCardProgramGroupType("Retail Gift Cards");
        activateOnlyGinseng.setMerchantID("0");
        activateOnlyGinseng.setBarcode(toBeVerified.getVoucherBarcode());
        activateOnlyGinseng.setCardNumber(toBeVerified.getVoucherCode());
        activateOnlyGinseng.setAmount(toBeVerified.getDenomination());
        activateOnlyGinseng.setApprovalCode(approveCode);
        activateOnlyGinseng.setCardExpiry(DateUtil.format(toBeVerified.getVoucherEffectiveDate(), YYYY_MM_DD_T_HH_MM_SS));

        activateOnlyGinseng.setActivationDate(DateUtil.format(new Date(), YYYY_MM_DD_T_HH_MM_SS));
        activateOnlyGinseng.setCardType(cpg.getCpgName());
        activateOnlyGinseng.setThemeId("Birthday");
        activateOnlyGinseng.setResult(false);


        return activateOnlyGinseng;
    }


    private CancelActivateGinseng checkCancelActivateVoucher(CancelActivateParam request) {
        //校验transaction ID
        //校验originalBatchNumber
        //校验invoiceNumber
        /*TransactionData transactionData = new TransactionData();
        transactionData.setTransactionId(String.valueOf(request.getOriginalTransactionId()));
        transactionData.setVoucherCode(request.getCardNumber());
        transactionData.setTransactionType(String.valueOf(TransactionTypeEnum.GIFT_CARD_ACTIVATE.getCode()));
        //transactionData.setBatchId(String.valueOf(request.getOriginalBatchNumber()));
        //transactionData.setInvoiceNumber(request.getOriginalInvoiceNumber());
        TransactionData transaction = transactionDataMapper.selectOne(transactionData);
        if (null != transaction) {
            if (!transaction.getTransactionId().equals(String.valueOf(request.getOriginalTransactionId()))) {
                return CancelActivateGinseng.builder().responseCode(Integer.valueOf(ResultErrorCodeEnum.TRANSACTION_ID_ERROR.code())).responseMessage(ResultErrorCodeEnum.TRANSACTION_ID_ERROR.desc()).build();
            } else if (!transaction.getBatchId().equals(String.valueOf(request.getOriginalBatchNumber()))) {
                return CancelActivateGinseng.builder().responseCode(Integer.valueOf(ResultErrorCodeEnum.BATCH_NUMBER_ERROR.code())).responseMessage(ResultErrorCodeEnum.BATCH_NUMBER_ERROR.desc()).build();
            }*//*else if (!transaction.getInvoiceNumber().equals(request.getOriginalInvoiceNumber())) {
                return CancelActivateGinseng.builder().responseCode(Integer.valueOf(ResultErrorCodeEnum.TRANSACTION_ID_ERROR.code())).responseMessage(ResultErrorCodeEnum.TRANSACTION_ID_ERROR.desc()).build();
            } *//*
        } else {
            return CancelActivateGinseng.builder().responseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_NO_TRANSACTION.code())).responseMessage(ResultErrorCodeEnum.VOUCHER_NO_TRANSACTION.desc()).build();
        }*/


        //校验voucherPin
        /*Voucher voucher = new Voucher();
        voucher.setVoucherPin(request.getCardPIN());
        voucher.setVoucherCode(request.getCardNumber());
        Voucher voucherPin = voucherMapper.selectOne(voucher);
        if (null != voucherPin) {
            if (!voucherPin.getVoucherPin().equals(request.getCardPIN())) {
                return CancelActivateGinseng.builder().responseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_PIN_CODE_ERROR.code())).responseMessage(ResultErrorCodeEnum.VOUCHER_PIN_CODE_ERROR.desc()).build();
            }
        } else {
            return CancelActivateGinseng.builder().responseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_PIN_CODE_ERROR.code())).responseMessage(ResultErrorCodeEnum.VOUCHER_PIN_CODE_ERROR.desc()).build();
        }*/

        return CancelActivateGinseng.builder().responseCode(0).responseMessage("CancelActivate successful").build();
    }


    private CancelRedeemResponse checkCancelRedeemVoucher(CancelRedeemRequest request) {
        //校验transaction ID
        //校验originalBatchNumber
        //校验invoiceNumber
        /*TransactionData transactionData = new TransactionData();
        transactionData.setTransactionId(String.valueOf(request.getOriginalTransactionId()));
        transactionData.setVoucherCode(request.getCardNumber());
        transactionData.setTransactionType(TransactionTypeEnum.GIFT_CARD_REDEEM.getCode());
        transactionData.setBatchId(String.valueOf(request.getOriginalBatchNumber()));
        transactionData.setInvoiceNumber(request.getOriginalInvoiceNumber());
        TransactionData transaction = transactionDataMapper.selectOne(transactionData);
        if (null != transaction) {
            if (!transaction.getTransactionId().equals(String.valueOf(request.getOriginalTransactionId()))) {
                return CancelRedeemResponse.builder().responseCode(Integer.valueOf(ResultErrorCodeEnum.TRANSACTION_ID_ERROR.code())).responseMessage(ResultErrorCodeEnum.TRANSACTION_ID_ERROR.desc()).build();
            } else if (!transaction.getInvoiceNumber().equals(request.getOriginalInvoiceNumber())) {
                return CancelRedeemResponse.builder().responseCode(Integer.valueOf(ResultErrorCodeEnum.TRANSACTION_ID_ERROR.code())).responseMessage(ResultErrorCodeEnum.TRANSACTION_ID_ERROR.desc()).build();
            } else if (!transaction.getBatchId().equals(String.valueOf(request.getOriginalBatchNumber()))) {
                return CancelRedeemResponse.builder().responseCode(Integer.valueOf(ResultErrorCodeEnum.BATCH_NUMBER_ERROR.code())).responseMessage(ResultErrorCodeEnum.BATCH_NUMBER_ERROR.desc()).build();
            }
        } else {
            return CancelRedeemResponse.builder().responseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_NO_TRANSACTION.code())).responseMessage(ResultErrorCodeEnum.VOUCHER_NO_TRANSACTION.desc()).build();
        }*/


        //校验voucherPin
        /*Voucher voucher = new Voucher();
        voucher.setVoucherPin(request.getVoucherPin());
        voucher.setVoucherCode(request.getCardNumber());
        Voucher voucherPin = voucherMapper.selectOne(voucher);
        if (null != voucherPin) {
            if (!voucherPin.getVoucherPin().equals(request.getVoucherPin())) {
                return CancelRedeemResponse.builder().responseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_PIN_CODE_ERROR.code())).responseMessage(ResultErrorCodeEnum.VOUCHER_PIN_CODE_ERROR.desc()).build();
            }
        } else {
            return CancelRedeemResponse.builder().responseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_PIN_CODE_ERROR.code())).responseMessage(ResultErrorCodeEnum.VOUCHER_PIN_CODE_ERROR.desc()).build();
        }*/

        return CancelRedeemResponse.builder().responseCode(0).responseMessage("CancelRedeem successful").build();
    }


    private IssueHandlerBaseService getService(IssueHandlingTypeEnum typeEnum) {

        for (Map.Entry<String, IssueHandlerBaseService> entry : serviceMap.entrySet()) {
            IssueHandlingTypeEnum handlingTypeEnum = entry.getValue().getIssueHandlingType();
            if (handlingTypeEnum != null && typeEnum.code().equals(handlingTypeEnum.code())) {
                return entry.getValue();
            }
        }
        throw new GTechBaseException(ResultErrorCodeEnum.FIND_MORE_THAN_ONE_DATA.code(), ResultErrorCodeEnum.FIND_MORE_THAN_ONE_DATA.desc());
    }

    @Override
    public Result<RedemptionResponse> validateRedemption(RedemptionRequest request) {

        /**
         * 校验outlet的vpg是否存在当前的vpg
         * 状态禁用
         * 是否激活
         * 是否过期
         *
         */

        RedemptionResponse response = new RedemptionResponse();
        List<StartAndEndVoucherResponse> startAndEndVoucherResponses = new ArrayList<>();
        List<StartAndEndVoucher> startAndEndVouchers = request.getStartAndEndVouchers();
        for (StartAndEndVoucher startAndEndVoucher : startAndEndVouchers) {

            String startCode = startAndEndVoucher.getStartCode();
            String endCode = startAndEndVoucher.getEndCode();

            List<Voucher> vouchers = queryStartAndEndVoucher(startCode, endCode);
            if (CollectionUtils.isEmpty(vouchers)) {
                return Result.failed("Voucher does not exist.");
            }
            Boolean aBoolean = new Boolean(true);

            for (Voucher voucher : vouchers) {
                VoucherResponse voucherResponse = BeanCopyUtils.jsonCopyBean(voucher, VoucherResponse.class);
                VerifyVoucherResponse verifyVoucherResponse = new VerifyVoucherResponse();
                verifyVoucherResponse.setVoucherInfo(new VoucherInfo());

                verifyVoucherResponse = checkVoucherIsThisStoreAvailableAgainNoPos(voucherResponse, verifyVoucherResponse, request.getOutletCode());
                verifyVoucherResponse = checkVoucherStatus(voucherResponse, verifyVoucherResponse, GvcoreConstants.STATUS_ENABLE);
                verifyVoucherResponse = checkVoucherIsItNew(voucherResponse, verifyVoucherResponse, VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
                verifyVoucherResponse = checkVoucherIsItExpired(voucherResponse, verifyVoucherResponse);
                if (StringUtils.isEmpty(verifyVoucherResponse.getVoucherInfo().getVoucherCode())) {
                    //成功的券
                    aBoolean = true;
                } else {
                    aBoolean = false;
                }
                startAndEndVoucherResponses.add(StartAndEndVoucherResponse.builder()
                        .startCode(startCode)
                        .endCode(endCode)
                        .whetherSucceed(aBoolean)
                        .voucherStatus(SystemVoucherStatusEnum.getByCode(voucher.getStatus()))
                        .build());
            }

        }
        response.setVouchers(startAndEndVoucherResponses);
        return Result.ok(response);
    }


    public List<Voucher> queryStartAndEndVoucher(String startCode, String endCode) {
    	if(StringUtil.isEmpty(startCode) ||StringUtil.isEmpty(endCode)) {
			return Collections.emptyList();
		}

		Example example = new Example(Voucher.class);
		example.createCriteria().andGreaterThanOrEqualTo(Voucher.C_VOUCHER_CODE, startCode).andLessThanOrEqualTo(Voucher.C_VOUCHER_CODE, endCode);
        return voucherMapper.selectByCondition(example);
    }

    @Override
    public Result<Void> redemption(RedemptionRequest request) {

        Result<RedemptionResponse> redemptionResponseResult = validateRedemption(request);
        String approveCode = gvCodeHelper.generateApproveCode();
        for (StartAndEndVoucherResponse voucher : redemptionResponseResult.getData().getVouchers()) {
            if (!voucher.getWhetherSucceed()) {
                continue;
            }
            List<Voucher> vouchers = queryStartAndEndVoucher(voucher.getStartCode(), voucher.getEndCode());
            List<String> successCode = vouchers.stream().map(Voucher::getVoucherCode).collect(Collectors.toList());

            OutletResponse outlet = null;
            Outlet cacheOutlet = masterDataCache.getOutlet(request.getOutletCode());
            if (null != cacheOutlet){
                outlet = BeanCopyUtils.jsonCopyBean(cacheOutlet,OutletResponse.class);
            }else {
                outlet = outletService.getOutlet(GetOutletRequest.builder().outletCode(request.getOutletCode()).build());
            }

            //修改状态
            if (CollectionUtils.isNotEmpty(successCode)) {
                UpdateVoucherStatusRequest updateRequest = new UpdateVoucherStatusRequest();
                updateRequest.setVoucherCodeList(successCode);
                updateRequest.setStatus(VoucherStatusEnum.VOUCHER_USED.getCode());
                updateRequest.setUsedTime(new Date());
                updateRequest.setUsedOutlet(outlet.getOutletCode());
                this.updateVoucherStatus(updateRequest);
            }



            //OutletResponse outlet = outletService.getOutlet(GetOutletRequest.builder().outletCode(request.getOutletCode()).build());

            Map<String, Voucher> voucherMap = vouchers.stream().collect(Collectors.toMap(Voucher::getVoucherCode, x -> x));

            final String transactionId = gvCodeHelper.generateTransactionDataCode();
            List<TransactionData> transactionData = BeanCopyUtils.jsonCopyList(vouchers, TransactionData.class);
            OutletResponse finalOutlet = outlet;
            transactionData.forEach(x -> {
                x.setTransactionId(transactionId);
                x.setBillNumber(request.getBillNumber());
                x.setInvoiceNumber(request.getBillNumber());
                x.setApproveCode(approveCode);
                x.setBatchId(transactionId);
                x.setBatchCode(voucherMap.get(x.getVoucherCode()).getVoucherBatchCode());
                x.setTransactionType(TransactionTypeEnum.GIFT_CARD_REDEEM.getCode());
                x.setOutletCode(request.getOutletCode());
                x.setMerchantCode(finalOutlet.getMerchantCode());
                x.setTransactionDate(new Date());
                x.setSuccessOrFailure("0");
                x.setTransactionMode("Transaction successful");
                x.setResponseMessage("Transaction successful.");
                x.setReferenceNumber(transactionId);
                x.setCardEntryMode(SWIPED);
                x.setReferenceNumber(gvCodeHelper.generateReferenceNumber());
                x.setCreateTime(null);
                x.setUpdateTime(null);
                x.setNotes(request.getNote());
                x.setCreateTime(null);
                x.setUpdateTime(null);
            });
            transactionDataService.insertList(transactionData);

        }
        return Result.ok();

    }

    /*@Override
    public CheckDynamicBarCodeResponse checkDynamicBarCode(CheckDynamicBarCodeRequest request) {

        String voucherCode = voucherNumberHelper.barCodeChangeVoucherCode(request.getBarCode());
        String barCode = (String) redisTemplate.opsForValue().get(REDIS_HEAD + "VoucherBarCode:" + voucherCode);

        if (StringUtil.isEmpty(barCode)) {
            Voucher voucher = voucherService.getVoucherByCode(voucherCode);
            if (null != voucher) {
                barCode = voucher.getVoucherBarcode();
            }
            if (StringUtil.isEmpty(barCode)) {
                return CheckDynamicBarCodeResponse.builder().isSuccess(false).build();
            }
        }


        if (!request.getBarCode().equals(barCode)) {
            return CheckDynamicBarCodeResponse.builder().isSuccess(false).build();
        }

        return CheckDynamicBarCodeResponse.builder().isSuccess(true).build();
    }*/

    @Override
    public CheckDynamicBarCodeResponse checkDynamicBarCode(CheckDynamicBarCodeRequest request) {
        String voucherCode;
        if (request.getBarCode().length()!=27){
            voucherCode = voucherNumberHelper.barCodeChangeVoucherCode(request.getBarCode());
        }else{
            voucherCode = voucherNumberHelper.barCode27BitChangeVoucherCode(request.getBarCode());
        }

        Voucher voucher = voucherService.getVoucherByCode(voucherCode);

        if(null == voucher){
            return CheckDynamicBarCodeResponse.builder().isSuccess(false).build();
        }
        if (voucher.getCreateUser().equals("DataMigration")) {
            return CheckDynamicBarCodeResponse.builder().isSuccess(true).build();
        }


        String barCode = (String) redisTemplate.opsForValue().get(REDIS_HEAD + "VoucherBarCode:" + voucherCode);

        if (StringUtil.isEmpty(barCode)) {

            if (null != voucher) {
                barCode = voucher.getVoucherBarcode();
            }
        }
        if (request.getBarCode().length() !=27){
            return CheckDynamicBarCodeResponse.builder().isSuccess(true).build();
        }
        if (StringUtil.isEmpty(barCode) || !request.getBarCode().equals(barCode)) {
            return CheckDynamicBarCodeResponse.builder().isSuccess(false).build();
        }
        return CheckDynamicBarCodeResponse.builder().isSuccess(true).build();
    }


    private VerifyVoucherDto getVerifyVoucherResponses(VerifyVoucherRequest request, VerifyVoucherInfo voucherInfo) {

        VerifyVoucherDto response = new VerifyVoucherDto();
        response.setFailVerify(Lists.newArrayList());
        response.setSuccessVerify(Lists.newArrayList());




        //根据inpuType判断 1-单独  2-范围
        List<String> voucherCodes = new ArrayList<>();

        if (voucherInfo.getInputType().equals("1") || voucherInfo.getInputType().equals("3")) {

            if (StringUtils.isBlank(voucherInfo.getCardInfo().getCardNumber())) {
                throw new GTechBaseException(String.valueOf(GvPosCommonResponseCodesEnum.CARD_NUMBER_PASSED_IS_INVALID.getResponseCode()), GvPosCommonResponseCodesEnum.CARD_NUMBER_PASSED_IS_INVALID.getResponseMessage());
            }

            Voucher voucher = voucherService.getVoucherByCode(voucherInfo.getCardInfo().getCardNumber());

            if (null == voucher) {
                throw new GTechBaseException(String.valueOf(GvPosCommonResponseCodesEnum.CARD_NUMBER_PASSED_IS_INVALID.getResponseCode()), GvPosCommonResponseCodesEnum.CARD_NUMBER_PASSED_IS_INVALID.getResponseMessage());
            }


            //  这段代码是检验voucherInfo.getCardInfo().getTrackData()是否有效.
            // 如果有效，则返回voucherInfo.getCardInfo().getCardNumber()，否则返回null
            // 如果条形码长度小于16，则说明条形码不合法，返回错误；
            // 如果转义后的条形码不等于voucherInfo.getCardInfo().getCardNumber()，说明两个不相等，返回错误；
            // 如果检验动态条形码失败，说明条形码不合法，返回错误。
            // 如果以上三种情况都不满足，则说明条形码合法，继续执行。
            if (StringUtil.isNotEmpty(voucherInfo.getCardInfo().getTrackData()) &&
                    !voucherInfo.getCardInfo().getCardNumber().equals(voucherInfo.getCardInfo().getTrackData()) &&
                    //null ==voucherService.getVoucherByCode(voucherInfo.getCardInfo().getCardNumber()) &&
                    (       voucherInfo.getCardInfo().getTrackData().length() < 16 ||
                            !voucherService.checkDynamicBarCode(CheckDynamicBarCodeRequest.builder().barCode(voucherInfo.getCardInfo().getTrackData()).build()).getIsSuccess()

                    )
            ) {

                //校验非立即激活的电子券 barCode是否存在缓存中(是否过期)
                //Voucher voucher = voucherMapper.selectOne(Voucher.builder().voucherCode(voucherInfo.getCardInfo().getCardNumber()).build());
                List<VerifyVoucherResponse> failVerify = new ArrayList<>();
                VerifyVoucherResponse verifyVoucherResponse = new VerifyVoucherResponse();
                verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.INVALID_BARCODE.getResponseMessage());
                verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.INVALID_BARCODE.getResponseCode()));
                verifyVoucherResponse.setIsSuccess(false);
                verifyVoucherResponse.setVoucherInfo(VoucherInfo.builder().voucherCode(voucherInfo.getCardInfo().getCardNumber()).voucherCodeNum(Long.valueOf(voucherInfo.getCardInfo().getCardNumber().replaceAll("[a-zA-Z]", ""))).denomination(voucher.getDenomination()).voucherStatus(voucher.getVoucherStatus()).status(voucher.getStatus()).mopCode(voucher.getMopCode()).cardCreationType(voucher.getMopCode()).build());
                failVerify.add(verifyVoucherResponse);
                response.getFailVerify().addAll(failVerify);
                response.setActionType(voucherInfo.getType());
                return response;
            }
            voucherCodes.add(voucherInfo.getCardInfo().getCardNumber());


        } else if (voucherInfo.getInputType().equals("2")) {
            String start = voucherInfo.getStartCardInfo().getCardNumber();
            String end = voucherInfo.getEndCardInfo().getCardNumber();
            List<Voucher> vouchers = new ArrayList<>();
            if (StringUtil.isNotBlank(voucherInfo.getStartCardInfo().getTrackData())//覆盖barCode为券号的场景
                    && StringUtil.isNotBlank(voucherInfo.getEndCardInfo().getTrackData())
                    && voucherInfo.getStartCardInfo().getTrackData().matches("\\d{16}")
                    && voucherInfo.getEndCardInfo().getTrackData().matches("\\d{16}")
            ) {
                start = voucherInfo.getStartCardInfo().getTrackData();
                end = voucherInfo.getEndCardInfo().getTrackData();
            } else if (StringUtil.isBlank(start) && StringUtil.isBlank(end)
                    && StringUtil.isNotBlank(voucherInfo.getStartCardInfo().getTrackData())
                    && StringUtil.isNotBlank(voucherInfo.getEndCardInfo().getTrackData())
            ) {
                start = voucherNumberHelper.barCodeToVoucher(voucherInfo.getStartCardInfo().getTrackData());
                end = voucherNumberHelper.barCodeToVoucher(voucherInfo.getEndCardInfo().getTrackData());
            }


            if (StringUtil.isNotBlank(start) && StringUtil.isNotBlank(end)){
                countInRangeNoException(start,end);
                vouchers = queryVoucherListByStartAndEnd(start, end);
            }else {
                log.error("------------------------- voucherInfo.getStartCardInfo().getCardNumber() voucherInfo.getEndCardInfo().getCardNumber() 为空{}", JSONObject.toJSONString(request));
            }

            if (CollectionUtils.isEmpty(vouchers)) {
                throw new GTechBaseException(String.valueOf(GvPosCommonResponseCodesEnum.CARD_NUMBER_PASSED_IS_INVALID.getResponseCode()), GvPosCommonResponseCodesEnum.CARD_NUMBER_PASSED_IS_INVALID.getResponseMessage());
            }

            voucherCodes = vouchers.stream().map(Voucher::getVoucherCode).collect(Collectors.toList());
        }





        if (CollectionUtils.isEmpty(voucherCodes)){
            log.error("----------------------------------------------券为空{}", JSONObject.toJSONString(request));

            return vouchersThatDoNotExist(voucherCodes);
        }

        List<VoucherResponse> vouchers = voucherMapper.queryVoucherList(voucherCodes);

        if (CollectionUtils.isEmpty(vouchers)) {
            return vouchersThatDoNotExist(voucherCodes);
        }


        if (voucherInfo.getType().equals(VerifyVoucherTypeEnum.VOUCHER_ACTIVATION.getCode())) {
            return activationVoucher(vouchers, request);
        } else if (voucherInfo.getType().equals(VerifyVoucherTypeEnum.VOUCHER_REDEMPTION.getCode())) {
            return redemptionVoucher(vouchers, request.getMachineId());
        } else if (voucherInfo.getType().equals(VerifyVoucherTypeEnum.VOUCHER_VERIFY_ACTIVATION.getCode())) {
            return verifyActivationVoucher(vouchers, request.getMachineId());
        } else if (voucherInfo.getType().equals(VerifyVoucherTypeEnum.VOUCHER_VERIFY_REDEMPTION.getCode())) {
            return verifyRedemptionVoucher(vouchers, request.getMachineId());
        }
        return null;
    }


    public void countInRangeNoException(String startValue, String endValue) {
        BigInteger start = new BigInteger(startValue.replaceAll("[a-zA-Z]", ""));
        BigInteger end = new BigInteger(endValue.replaceAll("[a-zA-Z]", ""));
        // 计算范围内的整数数量
        int intValue = end.subtract(start).add(BigInteger.ONE).intValue();
        log.info("bulk process范围券数量:{} start:{} end{}",intValue,start,end);
    }

    private VerifyVoucherDto verifyRedemptionVoucher(List<VoucherResponse> vouchers, String posCode) {
        /**
         * 校验pos的vpg,如果不存在校验outlet的vpg
         * 校验是否禁用
         * 校验券状态是否是 ACTIVATED
         * 校验是否过期
         */


        List<VerifyVoucherResponse> successVerify = new ArrayList<>();
        List<VerifyVoucherResponse> failVerify = new ArrayList<>();
        for (VoucherResponse voucher : vouchers) {
            VerifyVoucherResponse verifyVoucherResponse = new VerifyVoucherResponse();
            verifyVoucherResponse.setVoucherInfo(new VoucherInfo());
            verifyVoucherResponse = checkOutletType(voucher, posCode, verifyVoucherResponse);
            verifyVoucherResponse = checkVoucherIsTheTerminalAvailable(voucher, posCode, verifyVoucherResponse);
            verifyVoucherResponse = checkVoucherStatus(voucher, verifyVoucherResponse, GvcoreConstants.STATUS_ENABLE);
            verifyVoucherResponse = checkVoucherIsItNew(voucher, verifyVoucherResponse, VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
            verifyVoucherResponse = checkVoucherIsItExpired(voucher, verifyVoucherResponse);

            if (StringUtils.isEmpty(verifyVoucherResponse.getVoucherInfo().getVoucherCode())) {
                verifyVoucherResponse.setIsSuccess(true);
                verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_SUCCESS.getResponseCode()));
                verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.TRANSACTION_SUCCESS.getResponseMessage());
                //https://jira.gtech.asia/browse/MER-750  cardStatus : should follow original QC Card status (ACTIVATED) eventhough the voucher been redeemed
                /*if(voucher.getStatus().equals(VoucherStatusEnum.VOUCHER_USED.getCode())){
                    voucher.setStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
                }*/

                verifyVoucherResponse.setVoucherInfo(BeanCopyUtils.jsonCopyBean(voucher, VoucherInfo.class));
                successVerify.add(verifyVoucherResponse);
            } else {
                //https://jira.gtech.asia/browse/MER-750  cardStatus : should follow original QC Card status (ACTIVATED) eventhough the voucher been redeemed
                /*if(voucher.getStatus().equals(VoucherStatusEnum.VOUCHER_USED.getCode())){
                    voucher.setStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
                }*/
                verifyVoucherResponse.setIsSuccess(false);
                verifyVoucherResponse.setVoucherInfo(BeanCopyUtils.jsonCopyBean(voucher, VoucherInfo.class));
                failVerify.add(verifyVoucherResponse);
            }


        }


        VerifyVoucherDto response = new VerifyVoucherDto();
        response.setFailVerify(failVerify);
        response.setSuccessVerify(successVerify);
        response.setVoucherList(vouchers);
        response.setActionType(VerifyVoucherTypeEnum.VOUCHER_VERIFY_REDEMPTION.getCode());
        return response;
    }


    private VerifyVoucherDto verifyActivationVoucher(List<VoucherResponse> vouchers, String posCode) {

        /**
         *
         * 券所属outlet 为当前outlet
         * 校验pos的vpg,如果不存在校验outlet的vpg
         * 校验是否禁用
         * 校验券状态是否是 NEWLY_GENERATED
         * 校验是否过期
         */

        List<VerifyVoucherResponse> successVerify = new ArrayList<>();
        List<VerifyVoucherResponse> failVerify = new ArrayList<>();
        for (VoucherResponse voucher : vouchers) {
            VerifyVoucherResponse verifyVoucherResponse = new VerifyVoucherResponse();
            verifyVoucherResponse.setVoucherInfo(new VoucherInfo());
            verifyVoucherResponse = checkVoucherIsThisStoreAvailableAgain(voucher, posCode, verifyVoucherResponse);
            verifyVoucherResponse = checkVoucherIsTheTerminalAvailable(voucher, posCode, verifyVoucherResponse);
            verifyVoucherResponse = checkVoucherStatus(voucher, verifyVoucherResponse, GvcoreConstants.STATUS_ENABLE);
            verifyVoucherResponse = checkVoucherIsItNew(voucher, verifyVoucherResponse, VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
            verifyVoucherResponse = checkVoucherIsItExpired(voucher, verifyVoucherResponse);


            if (StringUtils.isEmpty(verifyVoucherResponse.getVoucherInfo().getVoucherCode())) {
                verifyVoucherResponse.setIsSuccess(true);
                verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_SUCCESS.getResponseCode()));
                verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.TRANSACTION_SUCCESS.getResponseMessage());
                verifyVoucherResponse.setVoucherInfo(BeanCopyUtils.jsonCopyBean(voucher, VoucherInfo.class));
                successVerify.add(verifyVoucherResponse);
            } else {
                verifyVoucherResponse.setIsSuccess(false);
                verifyVoucherResponse.setVoucherInfo(BeanCopyUtils.jsonCopyBean(voucher, VoucherInfo.class));
                verifyVoucherResponse.setResponseCode(verifyVoucherResponse.getResponseCode());
                verifyVoucherResponse.setResponseMessage(verifyVoucherResponse.getResponseMessage());
                failVerify.add(verifyVoucherResponse);
            }


        }

        VerifyVoucherDto response = new VerifyVoucherDto();
        response.setFailVerify(failVerify);
        response.setSuccessVerify(successVerify);
        response.setVoucherList(vouchers);
        response.setActionType(VerifyVoucherTypeEnum.VOUCHER_VERIFY_ACTIVATION.getCode());
        return response;
    }


    private VerifyVoucherDto redemptionVoucher(List<VoucherResponse> vouchers, String posCode) {

        /**
         *
         * 校验pos的vpg,如果不存在校验outlet的vpg
         * 校验是否禁用
         * 校验券状态是否是 ACTIVATED
         * 校验是否过期
         *
         * 修改券状态为USED
         */

        List<VerifyVoucherResponse> successVerify = new ArrayList<>();
        List<VerifyVoucherResponse> failVerify = new ArrayList<>();
        UpdateVoucherStatusRequest request = new UpdateVoucherStatusRequest();
        List<String> successCode = new ArrayList<>();
        for (VoucherResponse voucher : vouchers) {
            VerifyVoucherResponse verifyVoucherResponse = new VerifyVoucherResponse();
            verifyVoucherResponse.setVoucherInfo(new VoucherInfo());

            verifyVoucherResponse = checkOutletType(voucher, posCode, verifyVoucherResponse);
            verifyVoucherResponse = checkVoucherIsTheTerminalAvailable(voucher, posCode, verifyVoucherResponse);
            verifyVoucherResponse = checkVoucherStatus(voucher, verifyVoucherResponse, GvcoreConstants.STATUS_ENABLE);
            verifyVoucherResponse = checkVoucherIsItNew(voucher, verifyVoucherResponse, VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
            verifyVoucherResponse = checkVoucherIsItExpired(voucher, verifyVoucherResponse);


            long ifStart702 = System.currentTimeMillis();
            if (StringUtils.isEmpty(verifyVoucherResponse.getVoucherInfo().getVoucherCode())) {
                successCode.add(voucher.getVoucherCode());
                //https://jira.gtech.asia/browse/MER-750  cardStatus : should follow original QC Card status (ACTIVATED) eventhough the voucher been redeemed
                //verifyVoucherResponse.getVoucherInfo().setStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
                verifyVoucherResponse.setIsSuccess(true);
                verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_SUCCESS.getResponseCode()));
                verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.TRANSACTION_SUCCESS.getResponseMessage());
            }
            //https://jira.gtech.asia/browse/MER-750  cardStatus : should follow original QC Card status (ACTIVATED) eventhough the voucher been redeemed
            /*if(voucher.getStatus().equals(VoucherStatusEnum.VOUCHER_USED.getCode())){
                voucher.setStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
            }*/
            verifyVoucherResponse.setVoucherInfo(BeanCopyUtils.jsonCopyBean(voucher, VoucherInfo.class));

            if (verifyVoucherResponse.getIsSuccess()) {
                //https://jira.gtech.asia/browse/MER-750  cardStatus : should follow original QC Card status (ACTIVATED) eventhough the voucher been redeemed
                //https://jira.gtech.asia/browse/MER-1874
                verifyVoucherResponse.getVoucherInfo().setStatus(VoucherStatusEnum.VOUCHER_USED.getCode());

                request.setStatus(VoucherStatusEnum.VOUCHER_USED.getCode());
                request.setVoucherCodeList(Collections.singletonList(voucher.getVoucherCode()));
                request.setOldStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
                /*try {
                    int i = this.updateVoucherStatus(request);
                    if (i!=1){
                        //失败
                        verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.VALIDATION_FAILED.getResponseCode()));
                        verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.VALIDATION_FAILED.getResponseMessage());
                        failVerify.add(verifyVoucherResponse);
                    }else {
                        successVerify.add(verifyVoucherResponse);
                    }
                } catch (Exception e) {
                    //失败
                    verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.VALIDATION_FAILED.getResponseCode()));
                    verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.VALIDATION_FAILED.getResponseMessage());
                    failVerify.add(verifyVoucherResponse);
                }*/

                successVerify.add(verifyVoucherResponse);
            } else {
                verifyVoucherResponse.setIsSuccess(false);
                verifyVoucherResponse.getVoucherInfo().setStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
                failVerify.add(verifyVoucherResponse);
            }

        }


        //修改券状态
        /*if (CollectionUtils.isNotEmpty(successCode)) {
            updateVoucherStatusAddThreadLocal(
                    successCode,
                    request,VoucherStatusEnum.VOUCHER_USED,
                    VoucherStatusEnum.VOUCHER_ACTIVATED);

        }*/



        VerifyVoucherDto response = new VerifyVoucherDto();
        response.setFailVerify(failVerify);
        response.setSuccessVerify(successVerify);
        response.setVoucherList(vouchers);
        response.setActionType(VerifyVoucherTypeEnum.VOUCHER_REDEMPTION.getCode());
        //8-11取消隐藏cards信息
        //https://jira.gtech.asia/browse/MER-591
       /* response.getFailVoucherList().stream().forEach(x->{
            x.getVerifyVoucherResponses().stream().forEach(y->{
                y.setVoucherInfo(null);
            });
        });

        response.getSuccessVoucherList().stream().forEach(x->{
            x.getVerifyVoucherResponses().stream().forEach(y->{
                y.setVoucherInfo(null);
            });
        });*/

        return response;
    }

    /*private void updateVoucherStatusAddThreadLocal(List<String> successCode, UpdateVoucherStatusRequest request,VoucherStatusEnum voucherStatusEnum,VoucherStatusEnum oldVoucherStatusEnum) {
        successCode.forEach(x->{
            CompletableFuture<Map<String,Integer>> integerCompletableFuture = CompletableFuture.supplyAsync(() -> {
                request.setStatus(voucherStatusEnum.getCode());
                request.setVoucherCodeList(Collections.singletonList(x));
                request.setOldStatus(oldVoucherStatusEnum.getCode());
                Integer result = 0;
                try {
                    result = this.updateVoucherStatus(request);
                } catch (Exception e) {
                    result = 0;
                }
                Map<String, Integer> resultMap = new HashMap<>();
                resultMap.put(x,result);
                return resultMap;
            }*//*, verifyVoucherExecutor*//*);
            TransactionContext.addFuture(integerCompletableFuture);
        });
    }*/


    private VerifyVoucherDto vouchersThatDoNotExist(List<String> vouchers) {

        /**
         * 全不存在包装出参

         */

        VerifyVoucherDto response = new VerifyVoucherDto();

        List<VerifyVoucherResponse> successVerify = new ArrayList<>();
        List<VerifyVoucherResponse> failVerify = new ArrayList<>();

        List<String> successCode = new ArrayList<>();

        for (String voucher : vouchers) {
            VerifyVoucherResponse verifyVoucherResponse = new VerifyVoucherResponse();
            verifyVoucherResponse.setVoucherInfo(new VoucherInfo());
            successCode.add(voucher);
            verifyVoucherResponse.setIsSuccess(false);
            verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED_1.getResponseCode()));
            verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED_1.getResponseMessage());

            verifyVoucherResponse.setVoucherInfo(VoucherInfo.builder().voucherCode(voucher).status(0).denomination(BigDecimal.ZERO).build());

            //失败
            failVerify.add(verifyVoucherResponse);
        }

        response.setFailVerify(failVerify);
        response.setSuccessVerify(successVerify);

        return response;
    }


    private VerifyResponse voucherDuplicate(List<VerifyVoucherInfo> voucherInfos) {

        /**
         * 券重复包装出参

         */

        final List<String> voucherCodes = new ArrayList<>();

        voucherInfos.forEach(voucherInfo->{
            if (voucherInfo.getInputType().equals("1") || voucherInfo.getInputType().equals("3")) {
                voucherCodes.add(voucherInfo.getCardInfo().getCardNumber());
            } else if (voucherInfo.getInputType().equals("2")) {
                String start = voucherInfo.getStartCardInfo().getCardNumber();
                String end = voucherInfo.getEndCardInfo().getCardNumber();
                List<Voucher> vouchers = queryVoucherListByStartAndEnd(start, end);
                voucherCodes.addAll(vouchers.stream().map(Voucher::getVoucherCode).collect(Collectors.toList()));
            }
        });






        VerifyResponse response = new VerifyResponse();

        List<VerifyVoucherResponse> successVerify = new ArrayList<>();
        List<VerifyVoucherResponse> failVerify = new ArrayList<>();

        List<String> successCode = new ArrayList<>();

        for (String  voucher : voucherCodes) {
            if (StringUtils.isEmpty(voucher)) continue;
            VerifyVoucherResponse verifyVoucherResponse = new VerifyVoucherResponse();
            verifyVoucherResponse.setVoucherInfo(new VoucherInfo());
            verifyVoucherResponse.setIsSuccess(false);
            successCode.add(voucher);
            verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED_1.getResponseCode()));
            verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED_1.getResponseMessage());
            verifyVoucherResponse.setVoucherInfo(VoucherInfo.builder().
                    voucherCode(voucher)
                    .voucherCodeNum(Long.valueOf(voucher))
                    .status(0)
                    .denomination(BigDecimal.ZERO)
                    .build());
            //失败
            failVerify.add(verifyVoucherResponse);
        }

        response.setFailVoucherList(getVerifyVoucherGinseng(failVerify));
        response.setSuccessVoucherList(getVerifyVoucherGinseng(successVerify));

        return response;
    }



    private VerifyVoucherDto activationVoucher(List<VoucherResponse> vouchers, VerifyVoucherRequest param) {

        /**
         * 券所属outlet 为当前outlet
         * 校验pos的vpg,如果不存在校验outlet的vpg
         * 校验是否禁用
         * 校验券状态是否是 NEWLY_GENERATED
         * 校验是否过期
         *
         * 激活
         */


        List<VerifyVoucherResponse> successVerify = new ArrayList<>();
        List<VerifyVoucherResponse> failVerify = new ArrayList<>();
        UpdateVoucherStatusRequest request = new UpdateVoucherStatusRequest();

        List<String> successCode = new ArrayList<>();

        for (VoucherResponse voucher : vouchers) {
            VerifyVoucherResponse verifyVoucherResponse = new VerifyVoucherResponse();
            verifyVoucherResponse.setVoucherInfo(new VoucherInfo());

            long CheckStart705 = System.currentTimeMillis();

            verifyVoucherResponse = checkVoucherIsThisStoreAvailableAgain(voucher, param.getMachineId(), verifyVoucherResponse);
            verifyVoucherResponse = checkVoucherIsTheTerminalAvailable(voucher, param.getMachineId(), verifyVoucherResponse);
            verifyVoucherResponse = checkVoucherStatus(voucher, verifyVoucherResponse, GvcoreConstants.STATUS_ENABLE);
            verifyVoucherResponse = checkVoucherIsItNew(voucher, verifyVoucherResponse, VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
            verifyVoucherResponse = checkVoucherIsItExpired(voucher, verifyVoucherResponse);


            long ifStart705 = System.currentTimeMillis();
            if (StringUtils.isEmpty(verifyVoucherResponse.getVoucherInfo().getVoucherCode())) {
                successCode.add(voucher.getVoucherCode());
                verifyVoucherResponse.setIsSuccess(true);
                verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_SUCCESS.getResponseCode()));
                verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.TRANSACTION_SUCCESS.getResponseMessage());
            }
            verifyVoucherResponse.setVoucherInfo(BeanCopyUtils.jsonCopyBean(voucher, VoucherInfo.class));

            if (verifyVoucherResponse.getIsSuccess()) {
                //成功
                verifyVoucherResponse.getVoucherInfo().setStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());

                request.setStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
                request.setVoucherCodeList(Collections.singletonList(voucher.getVoucherCode()));
                request.setOldStatus(VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
                /*try {
                    int i = this.updateVoucherStatus(request);
                    if (i!=1){
                        //失败
                        verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.VALIDATION_FAILED.getResponseCode()));
                        verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.VALIDATION_FAILED.getResponseMessage());
                        failVerify.add(verifyVoucherResponse);
                    }else {
                        successVerify.add(verifyVoucherResponse);
                    }
                } catch (Exception e) {
                    //失败
                    verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.VALIDATION_FAILED.getResponseCode()));
                    verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.VALIDATION_FAILED.getResponseMessage());
                    failVerify.add(verifyVoucherResponse);
                }*/
                successVerify.add(verifyVoucherResponse);

            } else {
                verifyVoucherResponse.setIsSuccess(false);
                verifyVoucherResponse.setResponseCode(verifyVoucherResponse.getResponseCode());
                verifyVoucherResponse.setResponseMessage(verifyVoucherResponse.getResponseMessage());
                //失败
                failVerify.add(verifyVoucherResponse);
            }
        }


        //修改券状态
        /*if (CollectionUtils.isNotEmpty(successCode)) {
            updateVoucherStatusAddThreadLocal(
                    successCode,request,
                    VoucherStatusEnum.VOUCHER_ACTIVATED,
                    VoucherStatusEnum.VOUCHER_NEWLY_GENERATED);
        }*/
        VerifyVoucherDto response = new VerifyVoucherDto();
        response.setFailVerify(failVerify);
        response.setSuccessVerify(successVerify);
        response.setVoucherList(vouchers);
        response.setActionType(VerifyVoucherTypeEnum.VOUCHER_ACTIVATION.getCode());
        return response;
    }

    private List<VerifyVoucherGinseng> getVerifyVoucherGinseng(List<VerifyVoucherResponse> verifyVoucherResponses) {
        List<VerifyVoucherGinseng> sectionVoucherGinseng = new ArrayList<>();
        /*if (verifyVoucherResponses.size() ==
                verifyVoucherResponses.stream()
                        .map(VerifyVoucherResponse::getVoucherInfo)
                        .collect(Collectors.toList())
                .stream()
                        .map(VoucherInfo::getVoucherCode)
                        .distinct()
                        .count()){



        }*/

        //合并
        sectionVoucherGinseng = convert(verifyVoucherResponses, 0);



        //统计
        for (VerifyVoucherGinseng ginseng : sectionVoucherGinseng) {
            ginseng.setSuccessCardCount((int) ginseng.getVerifyVoucherResponses().stream()
                    .filter(success -> null != success.getIsSuccess() )
                    .filter(VerifyVoucherResponse::getIsSuccess).count());
            ginseng.setTotalCardCount(ginseng.getVerifyVoucherResponses().size());
            ginseng.setDesignCode(ginseng.getVerifyVoucherResponses().get(0).getVoucherInfo().getMopCode());

            //https://jira.gtech.asia/browse/MER-746 productCode 从cpgCode修改为articleCode

            ginseng.getVerifyVoucherResponses().forEach(v -> {
                String articleCode = "";
                try {
                    articleCode = cpgService.getCpg(GetCpgRequest.builder().cpgCode(v.getVoucherInfo().getCpgCode()).build()).getData().getArticleCode();
                } catch (Exception e) {
                    log.error("获取 articleCode失败");
                }
                VoucherInfo voucherInfo = v.getVoucherInfo();
                voucherInfo.setProductCode(articleCode);
            });

            String articleCode = "";
            try {
                articleCode = cpgService.getCpg(GetCpgRequest.builder().cpgCode(ginseng.getVerifyVoucherResponses().get(0).getVoucherInfo().getCpgCode()).build()).getData().getArticleCode();
            } catch (Exception e) {
                log.error("获取 articleCode失败");
            }
            ginseng.setProductCode(articleCode);
            ginseng.setCardProgramGroupName(ginseng.getVerifyVoucherResponses().get(0).getVoucherInfo().getCpgName());
            //2022 08 23
            ginseng.setTotalAmount(ginseng.getVerifyVoucherResponses().stream().map(VerifyVoucherResponse::getVoucherInfo).collect(Collectors.toList()).stream().map(VoucherInfo::getDenomination).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        return sectionVoucherGinseng;
    }


    //合并连续的券
    public static List<VerifyVoucherGinseng> convert(List<VerifyVoucherResponse> verifyVoucherResponses, int index) {
        int end = index;
        if (verifyVoucherResponses.size() == index) {
            return new ArrayList<>();
        } else {
            for (int i = index; i < verifyVoucherResponses.size(); i++) {
                if (i < verifyVoucherResponses.size() - 1) {
                    if (verifyVoucherResponses.get(i).getVoucherInfo().getVoucherCodeNum() + 1 == verifyVoucherResponses.get(i + 1).getVoucherInfo().getVoucherCodeNum()) {
                        end = i;
                    } else {
                        if (i > index) end = end + 1;
                        break;
                    }
                } else {
                    if (end == verifyVoucherResponses.size() - 2) {
                        end = verifyVoucherResponses.size() - 1;
                        break;
                    }
                }
            }

            List<VerifyVoucherGinseng> ginsengs = new ArrayList<>();

            //相等说明不连续
            if (index == end) {

                VerifyVoucherGinseng ginseng = new VerifyVoucherGinseng();
                ginseng.setStartCardNumber(verifyVoucherResponses.get(index).getVoucherInfo().getVoucherCode());
                ginseng.setEndCardNumber(verifyVoucherResponses.get(index).getVoucherInfo().getVoucherCode());
                ginseng.setVerifyVoucherResponses(Lists.newArrayList(verifyVoucherResponses.get(index)));
                ginsengs.add(ginseng);
                ginsengs.addAll(convert(verifyVoucherResponses, end + 1));
                return ginsengs;
            } else {

                VerifyVoucherGinseng ginseng = new VerifyVoucherGinseng();
                ginseng.setStartCardNumber(verifyVoucherResponses.get(index).getVoucherInfo().getVoucherCode());
                ginseng.setEndCardNumber(verifyVoucherResponses.get(end).getVoucherInfo().getVoucherCode());
                ginseng.setVerifyVoucherResponses(Lists.newArrayList(verifyVoucherResponses.subList(index, end + 1)));
                ginsengs.add(ginseng);
                ginsengs.addAll(convert(verifyVoucherResponses, end + 1));
                return ginsengs;

            }
        }
    }


    @Async
    @Override
    public void regenerateDigitalVoucher(GetCustomerOrderDetailsResponse detail, String voucherBatchCode) {


        Result<GetCpgResponse> cpgByCpgName;
        try {
            cpgByCpgName = cpgService.getCpg(GetCpgRequest.builder().cpgCode(detail.getCpgCode()).build());
        } catch (Exception e) {
            //cpg error
            throw new GTechBaseException(ResultErrorCodeEnum.GET_CPG_ERROR.code(), ResultErrorCodeEnum.GET_CPG_ERROR.desc());
        }


        Result<Object> cpgTypeResult;
        try {
            cpgTypeResult = cpgTypeService.getCpgType(GetCpgTypeRequest.builder().cpgTypeCode(cpgByCpgName.getData().getCpgTypeCode()).build());
        } catch (Exception e) {
            //cpg error
            throw new GTechBaseException(ResultErrorCodeEnum.GET_CPG_ERROR.code(), ResultErrorCodeEnum.GET_CPG_ERROR.desc());
        }

        GetCpgTypeResponse cpgType = (GetCpgTypeResponse) cpgTypeResult.getData();


        //删除mysql已存在的电子券
        deleteByVoucherBatchAndCpg(voucherBatchCode, detail.getCpgCode());
        //删除缓存中已经存在的电子券
        Set digitalVoucher = (Set) redisTemplate.opsForValue().get(REDIS_HEAD + DIGITAL1 + ":" + voucherBatchCode + ":" + detail.getCpgCode());
        Set redisSet = (Set) redisTemplate.opsForValue().get(REDIS_DIGITAL_HEAD + voucherNumberHelper.voucherType(DIGITAL, detail.getDenomination(), String.valueOf(Calendar.getInstance().get(Calendar.YEAR))));
        Iterator it = redisSet.iterator();
        while (it.hasNext()) {
            String next = (String) it.next();
            if (digitalVoucher.contains(next)) {
                it.remove();
            }
        }
        //更改redis中的数据
        redisTemplate.opsForValue().set(voucherNumberHelper.voucherType(DIGITAL, detail.getDenomination(), String.valueOf(Calendar.getInstance().get(Calendar.YEAR))), redisSet);


        VoucherBatchResponse voucherBatch = voucherBatchService.getVoucherBatch(GetVoucherBatchRequest.builder().voucherBatchCode(voucherBatchCode).build());

        //重新生成
        Set<String> digitalVoucherCodes = voucherNumberHelper.voucherCodeElectronicList(cpgType.getPrefix(), detail.getDenomination(), String.valueOf(Calendar.getInstance().get(Calendar.YEAR)), detail.getVoucherNum(), voucherBatch.getIssuerCode());
        GenerateDigitalVouchersRequest request = BeanCopyUtils.jsonCopyBean(voucherBatch, GenerateDigitalVouchersRequest.class);
        generateECouponsAsynchronously(request, detail, voucherBatchCode, digitalVoucherCodes);

    }

    private VerifyVoucherResponse checkVoucherStatus(VoucherResponse voucher, VerifyVoucherResponse verifyVoucherResponse, Integer voucherStatus) {
        if (StringUtils.isNotEmpty(verifyVoucherResponse.getVoucherInfo().getVoucherCode())) {
            return verifyVoucherResponse;
        }
        if (!voucher.getVoucherStatus().equals(voucherStatus)) {
            verifyVoucherResponse.getVoucherInfo().setVoucherCode(voucher.getVoucherCode());
            //Voucher has been disabled
            verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.CARD_IS_DEACTIVATED.getResponseMessage());
            verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.CARD_IS_DEACTIVATED.getResponseCode()));
            verifyVoucherResponse.setIsSuccess(false);
        }
        if (voucher.getMopCode().equals(GvcoreConstants.MOP_CODE_VCR)
                && !voucher.getCirculationStatus().equals(VoucherCirculationStatusEnum.VOUCHER_RECEIVED.getCode())) {
            verifyVoucherResponse.getVoucherInfo().setVoucherCode(voucher.getVoucherCode());
            verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.VOUCHER_NO_GR_OR_NO_ALLOCATED.getResponseMessage());
            verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.VOUCHER_NO_GR_OR_NO_ALLOCATED.getResponseCode()));
            verifyVoucherResponse.setIsSuccess(false);
        }
        return verifyVoucherResponse;
    }

    private VerifyVoucherResponse checkVoucherIsItNew(VoucherResponse voucher, VerifyVoucherResponse verifyVoucherResponse, Integer status) {
        if (StringUtils.isNotEmpty(verifyVoucherResponse.getVoucherInfo().getVoucherCode())) {
            return verifyVoucherResponse;
        }
        if (!voucher.getStatus().equals(status)) {
            verifyVoucherResponse.getVoucherInfo().setVoucherCode(voucher.getVoucherCode());
            //Voucher status unavailable
            verifyVoucherResponse.setIsSuccess(false);
            if (VoucherStatusEnum.VOUCHER_USED.getCode().equals(voucher.getStatus())) {
                verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.CARD_ALREADY_REDEEMED.getResponseMessage());
                verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.CARD_ALREADY_REDEEMED.getResponseCode()));
            } else if (VoucherStatusEnum.VOUCHER_ACTIVATED.getCode().equals(voucher.getStatus())) {
                verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.CARD_ALREADY_ACTIVE.getResponseMessage());
                verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.CARD_ALREADY_ACTIVE.getResponseCode()));
            } else if (VoucherStatusEnum.VOUCHER_EXPIRED.getCode().equals(voucher.getStatus())) {
                verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.CARD_EXPIRED.getResponseMessage());
                verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.CARD_EXPIRED.getResponseCode()));
            } else if (VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode().equals(voucher.getStatus())) {
                verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.CARD_NOT_ACTIVATED.getResponseMessage());
                verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.CARD_NOT_ACTIVATED.getResponseCode()));
            }
        }
        return verifyVoucherResponse;
    }

    private VerifyVoucherResponse checkVoucherIsItExpired(VoucherResponse voucher, VerifyVoucherResponse verifyVoucherResponse) {
        if (StringUtils.isNotEmpty(verifyVoucherResponse.getVoucherInfo().getVoucherCode())) {
            return verifyVoucherResponse;
        }
        if (this.checkVoucherIsExpired(voucher.getVoucherEffectiveDate())) {
            verifyVoucherResponse.getVoucherInfo().setVoucherCode(voucher.getVoucherCode());
            //Voucher expired
            verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.CARD_EXPIRED.getResponseMessage());
            verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.CARD_EXPIRED.getResponseCode()));
            verifyVoucherResponse.setIsSuccess(false);
        }
        return verifyVoucherResponse;
    }
    //校验是否过期
    private Boolean checkVoucherIsExpired(Date effectiveDate) {
        if (effectiveDate == null) {
            return false;
        }
        LocalDateTime today = LocalDateTime.now();
        LocalDateTime voucherDate = effectiveDate.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        if (voucherDate.isBefore(today)) {
            return true;
        }
        return false;
    }




    private VerifyVoucherResponse checkOutletType(VoucherResponse voucher, String posCode, VerifyVoucherResponse verifyVoucherResponse){
        if (StringUtils.isNotEmpty(verifyVoucherResponse.getVoucherInfo().getVoucherCode())) {
            return verifyVoucherResponse;
        }
        Pos pos = null;
        try {
            pos = masterDataCache.getPos(posCode);
        } catch (Exception e) {
            verifyVoucherResponse.getVoucherInfo().setVoucherCode(voucher.getVoucherCode());
            //This terminal does not exist
            verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.INVALID_TERMINALID.getResponseMessage());
            verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.INVALID_TERMINALID.getResponseCode()));
            verifyVoucherResponse.setIsSuccess(false);
        }
        if (null != pos ) {
            Outlet outlet = masterDataCache.getOutlet(pos.getOutletCode());
            //OutletResponse outlet = outletService.getOutlet(GetOutletRequest.builder().outletCode(pos.getData().getOutletCode()).build());
            if (null == outlet
                    || OutletTypeEnum.MV_STORE.code().equals(outlet.getOutletType())) {
                verifyVoucherResponse.getVoucherInfo().setVoucherCode(voucher.getVoucherCode());
                //Voucher not available on this terminal
                verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.INVALID_TERMINALID.getResponseMessage());
                verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.INVALID_TERMINALID.getResponseCode()));
                verifyVoucherResponse.setIsSuccess(false);
            }
        }
        return verifyVoucherResponse;

    }

    private VerifyVoucherResponse checkVoucherIsTheTerminalAvailable(VoucherResponse voucher, String posCode, VerifyVoucherResponse verifyVoucherResponse) {
        // 如果已有凭证编码，直接返回结果
        if (StringUtils.isNotEmpty(verifyVoucherResponse.getVoucherInfo().getVoucherCode())) {
            return verifyVoucherResponse;
        }
        Pos pos;
        try {
            pos = masterDataCache.getPos(posCode);
        } catch (Exception e) {
            setErrorResponse(verifyVoucherResponse, GvPosCommonResponseCodesEnum.INVALID_TERMINALID,voucher);
            return verifyVoucherResponse;
        }
        // 检查POS终端绑定的CPG
        if (masterDataCache.hasPosCpg(pos.getPosCode())) {
            // 如果POS有绑定CPG，检查是否包含voucher的CPG代码
            if (!masterDataCache.hasPosCpgWithCpgCode(pos.getPosCode(), voucher.getCpgCode())) {
                setErrorResponse(verifyVoucherResponse, GvPosCommonResponseCodesEnum.INVALID_TERMINALID,voucher);
            }
            return verifyVoucherResponse;
        }

        // POS没有绑定CPG，检查outlet是否绑定CPG
        if (masterDataCache.hasOutletCpg(pos.getOutletCode())) {
            // 如果outlet有绑定CPG，但不包含voucher的CPG代码
            if (!masterDataCache.hasOutletCpgWithCpgCode(pos.getOutletCode(), voucher.getCpgCode())) {
                setErrorResponse(verifyVoucherResponse, GvPosCommonResponseCodesEnum.INVALID_TERMINALID,voucher);
            }
        } else {
            // outlet没有绑定CPG，检查CPG是否为空
            checkCpgNull(voucher, verifyVoucherResponse);
        }

        return verifyVoucherResponse;

    }
    private void setErrorResponse(VerifyVoucherResponse response, GvPosCommonResponseCodesEnum errorCode, VoucherResponse voucher) {
        response.getVoucherInfo().setVoucherCode(voucher.getVoucherCode());
        response.setResponseMessage(errorCode.getResponseMessage());
        response.setResponseCode(String.valueOf(errorCode.getResponseCode()));
        response.setIsSuccess(false);
    }


    private void checkCpgNull(VoucherResponse voucher, VerifyVoucherResponse verifyVoucherResponse) {
        verifyVoucherResponse.getVoucherInfo().setVoucherCode(voucher.getVoucherCode());
        //Voucher not available on this terminal
        verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.INVALID_TERMINALID.getResponseMessage());
        verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.INVALID_TERMINALID.getResponseCode()));
        verifyVoucherResponse.setIsSuccess(false);
    }


    private VerifyVoucherResponse checkVoucherIsThisStoreAvailableAgain(VoucherResponse voucher, String posCode, VerifyVoucherResponse verifyVoucherResponse) {
        if (StringUtils.isNotEmpty(verifyVoucherResponse.getVoucherInfo().getVoucherCode())) {
            return verifyVoucherResponse;
        }
        Pos pos = null;
        try {
            pos = masterDataCache.getPos(posCode);
        } catch (Exception e) {
            verifyVoucherResponse.getVoucherInfo().setVoucherCode(voucher.getVoucherCode());
            //This terminal does not exist
            verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.INVALID_TERMINALID.getResponseMessage());
            verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.INVALID_TERMINALID.getResponseCode()));
            verifyVoucherResponse.setIsSuccess(false);
        }

        if (null == pos) {
            verifyVoucherResponse.getVoucherInfo().setVoucherCode(voucher.getVoucherCode());
            //This terminal does not exist
            verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.INVALID_TERMINALID.getResponseMessage());
            verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.INVALID_TERMINALID.getResponseCode()));
            verifyVoucherResponse.setIsSuccess(false);
        }

        if (!pos.getOutletCode().equals(voucher.getVoucherOwnerCode())) {
            verifyVoucherResponse.getVoucherInfo().setVoucherCode(voucher.getVoucherCode());
            //Not available at this store
            verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.MERCHANT_OUTLET_AUTH_FAILED.getResponseMessage());
            verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.MERCHANT_OUTLET_AUTH_FAILED.getResponseCode()));
            verifyVoucherResponse.setIsSuccess(false);
        }


        return verifyVoucherResponse;
    }


    private VerifyVoucherResponse checkVoucherIsThisStoreAvailableAgainNoPos(VoucherResponse voucher, VerifyVoucherResponse verifyVoucherResponse, String outletCode) {
        if (StringUtils.isNotEmpty(verifyVoucherResponse.getVoucherInfo().getVoucherCode())) {
            return verifyVoucherResponse;
        }
        List<OutletCpgResponse> data = new ArrayList<>();
        try {
            data = outletCpgService.queryOutletCpgListByOutlet(outletCode);
        } catch (Exception e) {
            verifyVoucherResponse.getVoucherInfo().setVoucherCode(voucher.getVoucherCode());
            //Not available at this store
            verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.MERCHANT_OUTLET_AUTH_FAILED.getResponseMessage());
            verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.MERCHANT_OUTLET_AUTH_FAILED.getResponseCode()));
            verifyVoucherResponse.setIsSuccess(false);
        }


        if (!data.stream().filter(w -> String.valueOf(w.getCpgCode()).equals(voucher.getCpgCode())).findAny().isPresent()) {
            verifyVoucherResponse.getVoucherInfo().setVoucherCode(voucher.getVoucherCode());
            //Voucher not available on this terminal
            verifyVoucherResponse.setResponseMessage(GvPosCommonResponseCodesEnum.INVALID_TERMINALID.getResponseMessage());
            verifyVoucherResponse.setResponseCode(String.valueOf(GvPosCommonResponseCodesEnum.INVALID_TERMINALID.getResponseCode()));
            verifyVoucherResponse.setIsSuccess(false);
        }


        return verifyVoucherResponse;
    }


    public List<Voucher> queryVoucherListByStartAndEnd(String startVoucherCode, String endVoucherCode) {
		if (StringUtil.isEmpty(startVoucherCode) || StringUtil.isEmpty(endVoucherCode)) {
			return Collections.emptyList();
		}
        Example example = new Example(Voucher.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andBetween(Voucher.C_VOUCHER_CODE, startVoucherCode, endVoucherCode);
        return voucherMapper.selectByCondition(example);
    }


    @Async("voucherBatch")
    @Override
    public void generateECouponsAsynchronously(GenerateDigitalVouchersRequest request, GetCustomerOrderDetailsResponse detail, String voucherBatchCode, Set<String> digitalVoucherCodes) {
        CustomerOrder customerCode;
        try {
            customerCode = customerOrderService.getCustomerByCustomerOrderDetailCode(detail.getCustomerOrderDetailsCode());
        } catch (Exception e) {
            throw new GTechBaseException(ResultErrorCodeEnum.GET_CUSTOMER_ORDER_ERROR.code(), ResultErrorCodeEnum.GET_CUSTOMER_ORDER_ERROR.desc());
        }
        GetCpgTypeResponse data = null;


        try {
            data = getCpgType(detail.getCpgCode());
        } catch (Exception e) {
            throw new GTechBaseException(ResultErrorCodeEnum.GET_CPG_TYPE_ERROR.code(), ResultErrorCodeEnum.GET_CPG_TYPE_ERROR.desc());
        }
        String automaticActivate = data.getAutomaticActivate();


        GetCpgRequest getCpgRequest = new GetCpgRequest();
        getCpgRequest.setCpgCode(detail.getCpgCode());
        Result<GetCpgResponse> cpgResponse = cpgService.getCpg(getCpgRequest);
        GetCpgResponse cpg = cpgResponse.getData();


        Date cpgEffectiveDate = voucherNumberHelper.cpgEffectiveDateToDate(cpg);


        BigDecimal detailBigDecimal = detail.getDenomination();

        String cpgCode = detail.getCpgCode();
        String issuerCode = request.getIssuerCode();
        String mopCode = request.getMopCode();
        Date voucherEffectiveDate = cpgEffectiveDate;
        String createUser = request.getCreateUser();
        String invoiceNumber = request.getInvoiceNumber();
        ArrayList<CreateVoucherRequest> createVoucherRequests = new ArrayList<>();

        //保存该cpg电子券 用于重新生成
        redisTemplate.opsForValue().set(REDIS_HEAD + DIGITAL1 + ":" + voucherBatchCode + ":" + detail.getCpgCode(), digitalVoucherCodes);


        for (Iterator iterator = digitalVoucherCodes.iterator(); iterator.hasNext(); ) {


            CreateVoucherRequest createVoucherRequest = null;
            if (automaticActivate.equals(CpgTypeAutomaticActivateEnum.YES.code())) {
                createVoucherRequest = getCreateVoucherRequestActivate(request, voucherBatchCode, customerCode, detailBigDecimal, cpgCode, issuerCode, mopCode, voucherEffectiveDate, createUser, iterator);


            } else {
                createVoucherRequest = getCreateVoucherRequestInactivated(GetCreateVoucherRequestInactivatedDto.builder()
                        .request(request)
                        .voucherBatchCode(voucherBatchCode)
                        .customerOrder(customerCode)
                        .detailBigDecimal(detailBigDecimal)
                        .cpgCode(cpgCode)
                        .issuerCode(issuerCode)
                        .mopCode(mopCode)
                        .voucherEffectiveDate(voucherEffectiveDate)
                        .createUser(createUser)
                        .iterator(iterator)
                        .build());


            }


            createVoucherRequests.add(createVoucherRequest);

            if (createVoucherRequests.size() >= 300) {
                judgingWhetherToEnd(detail, voucherBatchCode, createVoucherRequests, invoiceNumber, customerCode, request);
                //更新进度条
                taskProgressManager.incrementSubtaskProgress(request.getCustomerOrderCode(),detail.getCustomerOrderDetailsCode(),createVoucherRequests.size());
            }
        }

        if (CollectionUtils.isNotEmpty(createVoucherRequests)) {
            judgingWhetherToEnd(detail, voucherBatchCode, createVoucherRequests, invoiceNumber, customerCode,request );
            //更新进度条
            taskProgressManager.incrementSubtaskProgress(request.getCustomerOrderCode(),detail.getCustomerOrderDetailsCode(),createVoucherRequests.size());

        }

        //删除子任务
        taskProgressManager.deleteSubTaskProgress(request.getCustomerOrderCode(),detail.getCustomerOrderDetailsCode());

    }

    private CreateVoucherRequest getCreateVoucherRequestInactivated(GetCreateVoucherRequestInactivatedDto dto) {
        //电子券生成
        String next = (String) dto.getIterator().next();
        String activationCode = voucherNumberHelper.activationCode();
        CreateVoucherRequest createVoucherRequest = new CreateVoucherRequest();
        createVoucherRequest.setIssuerCode(dto.getIssuerCode());
        createVoucherRequest.setVoucherBatchCode(dto.getVoucherBatchCode());
        createVoucherRequest.setVoucherCode(next);
        createVoucherRequest.setVoucherCodeNum(Long.valueOf(next.replaceAll(RE, "")));
        createVoucherRequest.setCpgCode(dto.getCpgCode());
        createVoucherRequest.setMopCode(dto.getMopCode());
        createVoucherRequest.setDenomination(dto.getDetailBigDecimal());
        createVoucherRequest.setVoucherPin(voucherNumberHelper.pinCode());
        createVoucherRequest.setVoucherEffectiveDate(dto.getVoucherEffectiveDate());
        createVoucherRequest.setStatus(VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
        createVoucherRequest.setCirculationStatus(0);
        createVoucherRequest.setVoucherActiveCode(activationCode);
        createVoucherRequest.setVoucherActiveUrl(activeUrl + activationCode);
        createVoucherRequest.setCreateTime(new Date());
        createVoucherRequest.setCreateUser(dto.getCreateUser());
        createVoucherRequest.setLogType(VoucherLogTypeEnum.DIGITAL_VOUCHER.code());
        createVoucherRequest.setCreateUser(dto.getRequest().getCreateUser());
        createVoucherRequest.setVoucherOwnerCode(dto.getCustomerOrder().getCustomerCode());
        createVoucherRequest.setVoucherOwnerType(CUSTOMER);
        return createVoucherRequest;
    }


    private CreateVoucherRequest getCreateVoucherRequestActivate(GenerateDigitalVouchersRequest request, String voucherBatchCode, CustomerOrder customerOrder, BigDecimal detailBigDecimal, String cpgCode, String issuerCode, String mopCode, Date voucherEffectiveDate, String createUser, Iterator iterator) {
        //电子券生成
        //激活要生成barCode
        //不需要activateCode 和 activateUrl


        //激活在release节点激活
        String next = (String) iterator.next();
        CreateVoucherRequest createVoucherRequest = new CreateVoucherRequest();
        createVoucherRequest.setIssuerCode(issuerCode);
        createVoucherRequest.setVoucherBatchCode(voucherBatchCode);
        createVoucherRequest.setVoucherCode(next);
        createVoucherRequest.setVoucherCodeNum(Long.valueOf(next.replaceAll(RE, "")));
        createVoucherRequest.setCpgCode(cpgCode);
        createVoucherRequest.setMopCode(mopCode);
        createVoucherRequest.setDenomination(detailBigDecimal);
        createVoucherRequest.setVoucherPin(voucherNumberHelper.pinCode());
        createVoucherRequest.setVoucherEffectiveDate(voucherEffectiveDate);
        createVoucherRequest.setStatus(VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
        createVoucherRequest.setCirculationStatus(0);
        createVoucherRequest.setCreateTime(new Date());
        createVoucherRequest.setCreateUser(createUser);
        createVoucherRequest.setLogType(VoucherLogTypeEnum.DIGITAL_VOUCHER.code());
        createVoucherRequest.setCreateUser(request.getCreateUser());
        createVoucherRequest.setVoucherOwnerCode(customerOrder.getCustomerCode());
        createVoucherRequest.setVoucherOwnerType(CUSTOMER);
        createVoucherRequest.setVoucherBarcode(voucherNumberHelper.barCode27Bit(next));
        return createVoucherRequest;
    }

    private GetCpgTypeResponse getCpgType(String cpgCode) {
        GetCpgRequest cpgRequest = new GetCpgRequest();
        cpgRequest.setCpgCode(cpgCode);
        Result<GetCpgResponse> getCpg = cpgService.getCpg(cpgRequest);
        GetCpgResponse cpgData = getCpg.getData();

        GetCpgTypeRequest cpgTypeRequest = new GetCpgTypeRequest();
        cpgTypeRequest.setCpgTypeCode(cpgData.getCpgTypeCode());
        Result<Object> cpgType = cpgTypeService.getCpgType(cpgTypeRequest);
        return (GetCpgTypeResponse) cpgType.getData();
    }


    private void createTransactionData(ArrayList<CreateVoucherRequest> createVoucherRequests, TransactionTypeEnum typeEnum, CustomerOrder customerOrder, GenerateDigitalVouchersRequest generateRequest) {
        List<CreateTransactionDataRequest> requestList = new ArrayList<>();
        String merchantCode = outletService.getOutlet(GetOutletRequest.builder().outletCode(customerOrder.getOutletCode()).build()).getMerchantCode();
        createVoucherRequests.forEach(voucherRequest -> {
            CreateTransactionDataRequest request = new CreateTransactionDataRequest();
            request.setIssuerCode(customerOrder.getIssuerCode());
            request.setTransactionId(customerOrder.getCustomerOrderCode());
            request.setApproveCode(generateRequest.getApprovalCode());
            request.setInvoiceNumber(generateRequest.getInvoiceNumber());
            request.setTransactionType(typeEnum.getCode());
            request.setMerchantCode(merchantCode);
            request.setOutletCode(customerOrder.getOutletCode());
            request.setCpgCode(voucherRequest.getCpgCode());
            request.setTransactionDate(new Date());
            request.setVoucherCode(voucherRequest.getVoucherCode());
            request.setVoucherCodeNum(Long.valueOf(voucherRequest.getVoucherCode().replaceAll(A_Z_A_Z, "")));
            request.setInitiatedBy("");
            request.setPosCode("");
            request.setBatchCode(voucherRequest.getVoucherBatchCode());
            request.setLoginSource("");
            request.setDenomination(voucherRequest.getDenomination());
            request.setActualOutlet("");
            request.setVoucherEffectiveDate(voucherRequest.getVoucherEffectiveDate());
            request.setForwardingEntityId("");
            request.setResponseMessage(TRANSACTION_SUCCESSFUL);
            request.setTransactionMode("");
            request.setCustomerCode("");
            request.setCustomerSalutation("");
            request.setCustomerFirstName("");
            request.setCustomerLastName("");
            request.setMobile("");
            request.setInvoiceNumber(customerOrder.getInvoiceNo());
//            request.setOtherInputParameter("");
            request.setSuccessOrFailure("0");
            request.setMopCode(voucherRequest.getMopCode());
            request.setCreateUser(voucherRequest.getCreateUser());
            request.setCreateTime(new Date());

            requestList.add(request);
        });
        transactionDataService.createTransactionDataList(requestList);
    }


    private void judgingWhetherToEnd(GetCustomerOrderDetailsResponse detail, String voucherBatchCode, ArrayList<CreateVoucherRequest> createVoucherRequests, String invoiceNumber, CustomerOrder customerOrder, GenerateDigitalVouchersRequest request) {
        this.createVoucherList(createVoucherRequests);

        createTransactionData(createVoucherRequests, TransactionTypeEnum.GIFT_CARD_NEW_GENERATE, customerOrder,request );


        for (CreateVoucherRequest createVoucherRequest : createVoucherRequests) {

            if (createVoucherRequest.getStatus().equals(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode())) {
                createTransactionData(createVoucherRequests, TransactionTypeEnum.GIFT_CARD_ACTIVATE, customerOrder,request );
            }


        }


        //统计数量  batchCode + cpgCode
        redisTemplate.opsForValue().increment(REDIS_HEAD + voucherBatchCode + ":" + detail.getCpgCode(), createVoucherRequests.size());
        createVoucherRequests.clear();

        //判断是否完成，如果完成，改变状态为   已生成
        Integer voucherCount = (Integer) redisTemplate.opsForValue().get(REDIS_HEAD + voucherBatchCode + ":" + detail.getCpgCode());
        if (detail.getVoucherNum().equals(voucherCount)) {
            voucherBatchService.updateVoucherBatchStatus(UpdateVoucherBatchStatusRequest.builder().voucherBatchCode(voucherBatchCode).status(GENERATED).build());
            //删除用于判断是否完成的key
            redisTemplate.delete(REDIS_HEAD + voucherBatchCode + ":" + detail.getCpgCode());

            //删除用于重新生成的set
            redisTemplate.delete(REDIS_HEAD + voucherBatchCode + ":" + detail.getCpgCode() + ":" + DIGITAL1);

            log.info("结束");
        }
    }


    private int deleteByVoucherBatchAndCpg(String voucherBatchCode, String cpgCode) {

        Example example = new Example(Voucher.class);
        example.createCriteria().andEqualTo(Voucher.C_VOUCHER_BATCH_CODE, voucherBatchCode).andEqualTo(Voucher.C_CPG_CODE, cpgCode);

        return voucherMapper.deleteByCondition(example);

    }


    @Override
    public OnetimebarcodeResponse generateDigitalBarCode(OnetimebarcodeRequest request) {

        String success = "1";
        String message = "Transaction successful.";

        OnetimebarcodeResponse response = new OnetimebarcodeResponse();
        response.setResponseCode(0);
        response.setResponseMessage(TRANSACTION_SUCCESSFUL);
        response.setNotes(request.getNotes());
        response.setTransactionId(request.getTransactionId());
        response.setTransactionType("ONETIMEBARCODE");
        String barCode = null;

        String voucherCode = null;
        try {
            voucherCode = request.getCards().get(0).getCardNumber();
        } catch (Exception e) {
            response.setResponseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.code()));
            response.setResponseMessage(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.desc());
            return response;
        }
        if(StringUtil.isBlank(voucherCode)){
            //卡券不正确
            response.setResponseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.code()));
            response.setResponseMessage(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.desc());
            return response;
        }

        Voucher voucherByCode = voucherService.getVoucherByCode(voucherCode);
        if (null == voucherByCode) {
            //卡券不正确
            response.setResponseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.code()));
            response.setResponseMessage(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.desc());
            return response;
        }


        Date voucherEffectiveDate = voucherByCode.getVoucherEffectiveDate();
        String s = DateUtil.compareDateWithToday(voucherEffectiveDate);
        if (s.equals("-1")) {
            //卡券过期
            response.setResponseCode(GvPosCommonResponseCodesEnum.CARD_EXPIRED.getResponseCode());
            response.setResponseMessage(GvPosCommonResponseCodesEnum.CARD_EXPIRED.getResponseMessage());
            message = GvPosCommonResponseCodesEnum.CARD_EXPIRED.getResponseMessage();
            //return response;
        }

        Pos pos = masterDataCache.getPos(request.getTerminalId());
        if (null == pos){
            throw new GTechBaseException(ResultErrorCodeEnum.NO_POS_DATA_FOUND.code(), ResultErrorCodeEnum.NO_POS_DATA_FOUND.desc());
        }

        Outlet outlet = masterDataCache.getOutlet(pos.getOutletCode());
        if (null == outlet){
            throw new GTechBaseException(ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.code(), ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.desc());
        }



        if (0 == response.getResponseCode()){

            try {
                barCode = voucherNumberHelper.barCode27Bit(voucherCode);
            } catch (Exception e) {
                //卡券不正确
                response.setResponseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.code()));
                response.setResponseMessage(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.desc());
                return response;

            }
            redisTemplate.opsForValue().set(REDIS_HEAD + "VoucherBarCode:" + voucherCode, barCode, Integer.valueOf(getBarCodeExpiration()), TimeUnit.SECONDS);


        }
        Voucher voucher = voucherMapper.selectOne(Voucher.builder().voucherCode(voucherCode).build());
        VoucherBatchResponse voucherBatch;
        try {
            voucherBatch = voucherBatchService.getVoucherBatch(GetVoucherBatchRequest.builder().voucherBatchCode(voucher.getVoucherBatchCode()).build());
        } catch (Exception e) {
            //cpg error
            throw new GTechBaseException();
        }


        Cpg cpg = masterDataCache.getCpg(voucher.getCpgCode());
        GetCpgTypeRequest cpgTypeRequest = new GetCpgTypeRequest();
        cpgTypeRequest.setCpgTypeCode(cpg.getCpgTypeCode());




        String notes = StringUtil.EMPTY;
        if (StringUtil.isNotEmpty(request.getNotes())){
            notes = request.getNotes();
        }

        String approveCode = gvCodeHelper.generateApproveCode();

        if (StringUtil.isNotEmpty(barCode)){
            success = "0";
        }

        insertTransactionData(request.getTransactionId(),
                voucher, voucherBatch, cpg, pos,
                outlet, TransactionTypeEnum.GIFT_CARD_ONE_TIME_BARCODE,
                null, approveCode, "", notes,success, message,request.getBatchId());


        response.setCardNumber(voucherCode);
        response.setCardPin(voucher.getVoucherPin());
        response.setBarCode(barCode);
        response.setTransactionDateTime(DateUtil.format(new Date(), "yyyy-MM-dd'T'HH:mm:ss.FFFFFFFXXX"));
        response.setPinExpiry(getBarCodeExpiration());

        response.setApiWebProperties(null);

        return response;
    }


    private String getBarCodeExpiration() {
        MasterDataValueEntity entiy = new MasterDataValueEntity();
        entiy.setTenantCode(GvcoreConstants.SYSTEM_DEFAULT);
        entiy.setValueCode(GvcoreConstants.BARCODE_EXPIRATION);
        String expiration = "600";
        MasterDataValueEntity masterDataValueEntity = masterDataValueService.getByCode(entiy);
        if (masterDataValueEntity != null) {
            expiration = masterDataValueEntity.getValueValue();
        }
        return expiration;
    }



    @Override
    public void updateVoucherOwner(String voucherBatchCode, String issuerCode) {

        Voucher voucher = new Voucher();
        voucher.setVoucherOwnerCode(issuerWarehouseMap.get(issuerCode));
        voucher.setVoucherOwnerType(VoucherOwnerTypeEnum.WAREHOUSE.code());
        voucher.setCirculationStatus(VoucherCirculationStatusEnum.VOUCHER_TO_BE_RECEIVED.getCode());

        Example example = new Example(Voucher.class);
        example.createCriteria().andEqualTo(Voucher.C_VOUCHER_BATCH_CODE, voucherBatchCode);

        List<Voucher> voucherList = voucherMapper.selectByCondition(example);// 查询总记录数
        ListUtils.partition(voucherList, querySize).forEach(vouchers -> {
            // 查询要更新的数据
            Example update = new Example(Voucher.class);
            update.createCriteria().andIn(Voucher.C_VOUCHER_CODE, vouchers.stream().map(Voucher::getVoucherCode).collect(Collectors.toList()));
            voucherMapper.updateByConditionSelective(voucher, update); // 更新单条数据
        });


        /*List<Voucher> vouchers = voucherMapper.selectByCondition(example);
        List<VoucherLog> voucherLogs = BeanCopyUtils.jsonCopyList(vouchers, VoucherLog.class);
        voucherLogs.forEach(vo -> vo.setLogType(VoucherLogTypeEnum.VOUCHER_TO_BE_RECEIVED.code()));
        voucherLogService.insertList(voucherLogs);*/


    }

    @Override
    public void cancelSales(String voucherOwnerCode, String voucherOwnerType, List<String> voucherCodeList, Integer voucherStatus, Integer circulationStatus, Integer status) {
        voucherMapper.cancelSales(voucherOwnerCode, voucherOwnerType, voucherCodeList, voucherStatus, circulationStatus, status);
    }

    @Override
    public Integer cancelSalesByStartAndEnd(String voucherOwnerCode, String voucherOwnerType, String startVoucherCode,String endVoucherCode, Integer voucherStatus, Integer circulationStatus, Integer status) {
        return voucherMapper.cancelSalesByStartAndEnd(voucherOwnerCode, voucherOwnerType,startVoucherCode,endVoucherCode, voucherStatus, circulationStatus, status);
    }

    private List<Voucher> latestGvStatusReport(LatestGvStatusQueryData latestGvStatus) {
        return voucherMapper.latestGvStatusReport(latestGvStatus);
    }

    @Override
    public List<Voucher> queryByVoucherCodeNumRange(String voucherCode,
                                                    List<String> issuerCodeList) {

        return voucherMapper.queryByVoucherCodeNumRange(voucherCode, null, null, issuerCodeList);
    }

    @Override
    public Voucher getByVoucherCodeNumRangeReturnByPage(String voucherCode,
                                                        List<String> issuerCodeList) {

        List<Voucher> list = queryByVoucherCodeNumRange(voucherCode, issuerCodeList);

        if (CollectionUtils.isEmpty(list)) return null;

        return list.get(0);
    }

    @Override
    public Long countByVoucherCodeNumRangeReturn(String voucherCode, Long voucherCodeNumStart, Long voucherCodeNumEnd,
                                                 List<String> issuerCodeList) {

        PageMethod.startPage(1, -1);
        List<Voucher> list = voucherMapper.queryByVoucherCodeNumRange(voucherCode, voucherCodeNumStart, voucherCodeNumEnd, issuerCodeList);
        PageMethod.clearPage();
        return PageInfo.of(list).getTotal();
    }


    @Override
    public String getVoucherByBarCode(BarCodeToCodeRequest barCode) {


        try {


            if (StringUtil.isBlank(barCode.getBarCode())) {
                throw new GTechBaseException(ResultErrorCodeEnum.VOUCHER_BAR_CODE_ERROR.code(), ResultErrorCodeEnum.VOUCHER_BAR_CODE_ERROR.desc());
            } else {
                String code = voucherNumberHelper.barCodeToVoucher(barCode.getBarCode());
                //bookletBarCode算法一致
                if (StringUtil.isNotBlank(barCode.getType())) {

                    if ("0".equals(barCode.getType())) {
                        Voucher voucher = getVoucherByCode(code);
                        if (null == voucher) {
                            throw new GTechBaseException(ResultErrorCodeEnum.VOUCHER_BAR_CODE_ERROR.code(), ResultErrorCodeEnum.VOUCHER_BAR_CODE_ERROR.desc());
                        }
                        return voucher.getVoucherCode();
                    } else if ("1".equals(barCode.getType())) {
                        VoucherBooklet voucher = voucherBookletService.getBookletByCode(code);
                        if (null == voucher) {
                            throw new GTechBaseException(ResultErrorCodeEnum.BOOKLET_BAR_CODE_ERROR.code(), ResultErrorCodeEnum.BOOKLET_BAR_CODE_ERROR.desc());
                        }
                        return voucher.getBookletCode();
                    }


                }

                return code;


            }

        } catch (Exception e) {
            throw new GTechBaseException(ResultErrorCodeEnum.VOUCHER_BAR_CODE_ERROR.code(), ResultErrorCodeEnum.VOUCHER_BAR_CODE_ERROR.desc());

        }





    }

    /**
     * 根据Voucher Number区间和过期时间区间查询Voucher 的CPG集
     * 注意,如果参数全部为null,将拒绝查询直接返回空集合(否则将变为全表cpg查询)
     *
     * @param voucherNumberStart 编码起始
     * @param voucherNumberEnd   编码截止
     * @param expiryStatusStart  过期时间起始
     * @param expiryStatusEnd    过期时间截止
     * @return java.util.Set<java.lang.String> CPG集
     * <AUTHOR>
     * @date 2022/7/13 9:58
     * @since 1.0.0
     */
    @Override
    public List<Voucher> queryCpgCodeByNumberAndExpiryStatus(final String voucherNumberStart,
                                                             final String voucherNumberEnd,
                                                             final Date expiryStatusStart,
                                                             final Date expiryStatusEnd) {

        // 如果查询范围均为空,不进行查询
        if (StringUtils.isBlank(voucherNumberStart) && StringUtils.isBlank(voucherNumberEnd)
                && null == expiryStatusStart && null == expiryStatusEnd) {
            return Collections.emptyList();
        }

        final Example queryCondition = new Example(Voucher.class);
        queryCondition.createCriteria()
                .andGreaterThanOrEqualTo(Voucher.C_VOUCHER_CODE, GvConvertUtils.toString(voucherNumberStart))
                .andLessThanOrEqualTo(Voucher.C_VOUCHER_CODE, GvConvertUtils.toString(voucherNumberEnd))
                .andGreaterThanOrEqualTo(Voucher.C_VOUCHER_EFFECTIVE_DATE, expiryStatusStart)
                .andLessThanOrEqualTo(Voucher.C_VOUCHER_EFFECTIVE_DATE, expiryStatusEnd);

        queryCondition.selectProperties(Voucher.C_CPG_CODE);
        queryCondition.selectProperties(Voucher.C_VOUCHER_BATCH_CODE);

        return this.voucherMapper.selectByCondition(queryCondition);
    }

    @Override
    public Result<GetVoucherInformationResponse> getVoucherInformation(GetVoucherInformationRequest request) {

        if (StringUtil.isBlank(request.getVoucherNumber())){
            return Result.failed("VoucherNumber is null");
        }
        Voucher voucher = voucherMapper.selectOne(Voucher.builder().voucherCode(request.getVoucherNumber()).build());

        if (null == voucher) {
            return Result.failed("No data found");
        }
        GetVoucherInformationResponse getVoucherInformationResponse = BeanCopyUtils.jsonCopyBean(voucher, GetVoucherInformationResponse.class);
        getVoucherInformationResponse.setStatus(VoucherStatusEnum.getByCode(voucher.getStatus()));
        getVoucherInformationResponse.setVoucherNumber(voucher.getVoucherCode());
        return Result.ok(getVoucherInformationResponse);
    }

    @Override
    public List<OwnedCpgInfoResponse> queryOwnedCpgInfoList(final QueryOwnedCpgInfoListRequest request) {

        final String customerCode = request.getCustomerCode();

        List<CustomerOrder> customerOrderList = this.customerOrderMapper
                .select(CustomerOrder.builder().customerCode(customerCode).status(CustomerOrderStatusEnum.COMPLETED.getStatus()).build());

        if (CollectionUtils.isEmpty(customerOrderList)) return new ArrayList<>();

        Set<String> voucherBatchCodeSet = customerOrderList.stream().map(CustomerOrder::getVoucherBatchCode).filter(StringUtils::isNotBlank).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(voucherBatchCodeSet)) return new ArrayList<>();

        final List<String> cpgCodeSet = this.voucherMapper.queryOwnedCpgCodeList(customerCode, voucherBatchCodeSet);
        if (CollectionUtils.isEmpty(cpgCodeSet)) {
            return Collections.emptyList();
        }

        final Map<String, Cpg> cpgCodeCpgMap = this.cpgService.queryCpgMapByCpgCodeList(cpgCodeSet);

        return cpgCodeCpgMap.values().stream()
                .map(cpg -> {
                    final OwnedCpgInfoResponse ownedCpgInfo = new OwnedCpgInfoResponse();
                    ownedCpgInfo.setCpgCode(cpg.getCpgCode());
                    ownedCpgInfo.setCpgName(cpg.getCpgName());
                    return ownedCpgInfo;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<CustomerCpgEffectiveDateResult> queryCustomerCpgInventoryDetail(String customerCode, String cpgCode) {

        Result<GetCpgResponse> cpg = this.cpgService.getCpg(GetCpgRequest.builder().cpgCode(cpgCode).build());

        if (null == cpg || null == cpg.getData()) return Collections.emptyList();

        List<CustomerOrder> customerOrderList = this.customerOrderMapper
                .select(CustomerOrder.builder().customerCode(customerCode).status(CustomerOrderStatusEnum.COMPLETED.getStatus()).build());

        if (CollectionUtils.isEmpty(customerOrderList)) return new ArrayList<>();

        Set<String> voucherBatchCodeSet = customerOrderList.stream().map(CustomerOrder::getVoucherBatchCode).filter(StringUtils::isNotBlank).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(voucherBatchCodeSet)) return new ArrayList<>();

        List<CustomerCpgEffectiveDateResult> result = this.voucherMapper.queryCustomerCpg(customerCode, cpgCode, voucherBatchCodeSet);

        if (CollectionUtils.isEmpty(result)) return Collections.emptyList();

        result.forEach(e -> e.setDenomination(cpg.getData().getDenomination()));

        return result;
    }

    @Override
    public CpgInventoryDetailResponse queryCpgInventoryDetail(final QueryCpgInventoryDetailRequest request) {


        final String customerCode = request.getCustomerCode();


        List<CustomerOrder> customerOrderList = this.customerOrderMapper
                .select(CustomerOrder.builder().customerCode(customerCode).status(CustomerOrderStatusEnum.COMPLETED.getStatus()).build());
        Set<String> voucherBatchCodeSet = customerOrderList.stream().map(CustomerOrder::getVoucherBatchCode).filter(StringUtils::isNotBlank).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(voucherBatchCodeSet)) return new CpgInventoryDetailResponse();

        final List<CpgInventoryResponse> cpgInvList = this.voucherMapper.queryOwnedCpgInvInfoList(request.getCpgCode(), customerCode, voucherBatchCodeSet);

        // 获取CPG数据以得到CPG名称
        final List<String> cpgCodeList = cpgInvList.stream().map(CpgInventoryResponse::getCpgCode).collect(Collectors.toList());
        final Map<String, Cpg> cpgCodeCpgMap = this.cpgService.queryCpgMapByCpgCodeList(cpgCodeList);

        BigDecimal totalAmount = BigDecimal.ZERO;
        int totalQuantity = 0;
        int totalAvailable = 0;
        int totalDistributed = 0;

        for (CpgInventoryResponse cpgInv : cpgInvList) {

            final String cpgCode = cpgInv.getCpgCode();

            final int quantity = StringUtils.isBlank(cpgCode) ? 0 : this.customerOrderService.getCpgQuantityNum(new GetCpgQuantityNumParam()
                    .setCpgCode(cpgCode)
                    .setCustomerCode(customerCode));

            final int available = voucherService.countDistributableVoucher(cpgCode, customerCode);

            // 非Distributed均为Available
            final int distributed = quantity - available;
            // Amount  = Quantity (PCS) × Denomination(数量*面额)
            final BigDecimal amount = new BigDecimal(quantity).multiply(cpgInv.getDenomination());

            final Cpg cpg = cpgCodeCpgMap.get(cpgCode);

            if (null != cpg) {
                cpgInv.setCpgName(cpg.getCpgName());
            }
            cpgInv.setAmount(amount);
            cpgInv.setQuantity(quantity);
            cpgInv.setAvailable(available);
            cpgInv.setDistributed(distributed);

            totalAmount = totalAmount.add(amount);
            totalQuantity += quantity;
            totalAvailable += available;
            totalDistributed += distributed;
        }

        cpgInvList.sort((cpgInvA, cpgInvB) -> cpgInvB.getQuantity() - cpgInvA.getQuantity());

        final CpgInventoryDetailResponse cpgInvDetail = new CpgInventoryDetailResponse();
        cpgInvDetail.setCpgInventoryList(cpgInvList);
        cpgInvDetail.setAmount(totalAmount);
        cpgInvDetail.setQuantity(totalQuantity);
        cpgInvDetail.setAvailable(totalAvailable);
        cpgInvDetail.setDistributed(totalDistributed);

        return cpgInvDetail;
    }

    /**
     * 按 owner_code 和 cpg_code 分组,查询customer的cpg信息
     *
     * @param pageParam
     * @return java.util.List<com.gtech.gvcore.dto.CustomerCpgGroup>
     * <AUTHOR>
     * @date 2022/8/4 17:03
     * @since 1.0.0
     */
    @Override
    public List<CustomerCpgGroup> queryCustomerCpgGroupPage(final PageParam pageParam) {

        final RowBounds rowBounds = new RowBounds((pageParam.getPageNum() - 1) * pageParam.getPageSize(), pageParam.getPageSize());

        return this.voucherMapper.queryCustomerCpgGroupPage(rowBounds);
    }

    @Override
    @Transactional
    public void ready2Distribute(final Collection<String> voucherCodeSet, final int requiredQuantity, final String customerCode, final String updateUserCode) {

        if (null == voucherCodeSet || StringUtils.isBlank(customerCode)) {
            throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.PARAMTER_ERROR);
        }

        if (voucherCodeSet.size() != requiredQuantity) {
            throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.DISTRIBUTION_INSUFFICIENT_STOCK, "");
        }

        final Example updateExample = new Example(Voucher.class);
        updateExample.createCriteria()
                .andIn(Voucher.C_VOUCHER_CODE, voucherCodeSet)
                .andEqualTo(Voucher.C_VOUCHER_OWNER_CODE, GvConvertUtils.toString(customerCode, ""))
                .andEqualTo(Voucher.C_VOUCHER_OWNER_TYPE, CUSTOMER);

        final Voucher voucher = new Voucher();
        voucher.setCirculationStatus(VoucherCirculationStatusEnum.VOUCHER_DISTRIBUTING.getCode());
        voucher.setUpdateUser(updateUserCode);

        final int updateRow = this.voucherMapper.updateByConditionSelective(voucher, updateExample);
        if (updateRow != requiredQuantity) {
            throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.DISTRIBUTION_INSUFFICIENT_STOCK, "");
        }
    }

    @Override
    public Set<Voucher> queryDistributableVoucher(final String cpgCode, final Date expiryDate, final String customerCode, final int requiredQuantity) {

        if (StringUtils.isBlank(cpgCode) || null == expiryDate || StringUtils.isBlank(customerCode) || requiredQuantity <= 0) {
            throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.PARAMTER_ERROR);
        }

        final Example queryExample = queryDistributableVoucherExample(cpgCode, expiryDate, customerCode);

        final List<Voucher> voucherList = this.voucherMapper.selectByExampleAndRowBounds(queryExample, new RowBounds(0, requiredQuantity));

        return new HashSet<>(voucherList);
    }

    @Override
    public int countDistributableVoucher(final String cpgCode, final String customerCode) {

        final Example queryExample = queryDistributableVoucherExample(cpgCode, null, customerCode);

        if (null == queryExample) return 0;

        return this.voucherMapper.selectCountByCondition(queryExample);
    }

    @Override
    public void disableVoucherBatch(String voucherBatchCode) {

        voucherMapper.updateByConditionSelective(
                Voucher.builder()
                        .voucherStatus(VoucherStatusEnableDisableEnum.STATUS_DESTROY.getCode())
                        .build()
                , Example
                        .builder(Voucher.class)
                        .where(Sqls.custom()
                                .andEqualTo(Voucher.C_VOUCHER_BATCH_CODE, voucherBatchCode))
                        .build());

    }

    @Override
    public List<Voucher> queryVoucherListByVoucherBatchCode(String voucherBatchCode) {
		if (StringUtil.isEmpty(voucherBatchCode)) {
			return Collections.emptyList();
    	}
    	//queryVoucherListByVoucherBatchCode
        return voucherMapper.selectByCondition(Example.builder(Voucher.class).where(Sqls.custom().andEqualTo(Voucher.C_VOUCHER_BATCH_CODE, voucherBatchCode)).build());
    }

    @Override
    public APIBulkCancelRedeemResponse bulkCancelRedeem(BulkCancelRedeemRequest request, String terminalId, String batchId) {
        /*"TransactionId":1245,
                "CardNumber":"9990000200000048",
                "Notes":"cancel redeem ",
                "OriginalAmount":500,
                "OriginalInvoiceNumber":"12345",
                "OriginalTransactionId":1008,
                "OriginalBatchNumber":61725,
                "DateAtClient":"2018-10-18T17:08:14",
                "OriginalApprovalCode":"7860196515"*/





        String message = "Transaction successful.";
        String success = "1";

        if (null == request.getOriginalTransactionId()) {
            return APIBulkCancelRedeemResponse.builder().responseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.code())).responseMessage(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.desc()).build();
        }

        List<String> voucherCodes = request.getVoucherInfoList().stream().map(x -> x.getVoucherNumber()).collect(Collectors.toList());

        Example example1 = new Example(TransactionData.class);
        example1.createCriteria()
                .andIn("voucherCode",voucherCodes)
                .andEqualTo("transactionType",TransactionTypeEnum.GIFT_CARD_REDEEM.getCode());
        List<TransactionData> transactionData = transactionDataMapper.selectByCondition(example1);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(transactionData)) {
            //异常暂无
            return APIBulkCancelRedeemResponse.builder().responseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.code())).responseMessage(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.desc()).build();
        }


        Map<String, TransactionData> dataMap = transactionData.stream().collect(Collectors.toMap(TransactionData::getTransactionType, x -> x, (k1, k2) -> k2));

        Map<String, Map<String, TransactionData>> voucherTypeDataMap = transactionData.stream()
                .collect(Collectors.groupingBy(
                        TransactionData::getVoucherCode,
                        Collectors.toMap(
                                TransactionData::getTransactionType,
                                x -> x,
                                (existing, replacement) -> replacement
                        )
                ));

        if (voucherTypeDataMap.size() != voucherCodes.size()) {
            return APIBulkCancelRedeemResponse.builder().responseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_STATUS_ERROR.code())).responseMessage(ResultErrorCodeEnum.VOUCHER_STATUS_ERROR.desc()).build();
        }

        Weekend<Voucher> voucherWeekend = Weekend.of(Voucher.class);
        voucherWeekend.weekendCriteria()
                .andIn(Voucher::getVoucherCode,voucherCodes);

        List<Voucher> voucherList = voucherMapper.selectByCondition(voucherWeekend);


        if (CollectionUtils.isEmpty(voucherList)) {
            return APIBulkCancelRedeemResponse.builder().responseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.code())).responseMessage(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.desc()).build();
        }



        GetCpgRequest cpgRequest = new GetCpgRequest();
        cpgRequest.setCpgCode(request.getVpgCode());

        Cpg cpg = masterDataCache.getCpg(cpgRequest.getCpgCode());

        GetCpgTypeRequest cpgTypeRequest = new GetCpgTypeRequest();
        cpgTypeRequest.setCpgTypeCode(cpg.getCpgTypeCode());
        Result<Object> cpgType = cpgTypeService.getCpgType(cpgTypeRequest);
        GetCpgTypeResponse cpgTypeData = (GetCpgTypeResponse) cpgType.getData();
        //Date voucherEffectiveDate = toBeVerified.getVoucherEffectiveDate();
        String notes = StringUtil.EMPTY;
        if (StringUtil.isNotEmpty(request.getNotes())){
            notes = request.getNotes();
        }
        String approveCode = gvCodeHelper.generateApproveCode();



        APIBulkCancelRedeemResponse cancelRedeemResponse = new APIBulkCancelRedeemResponse();


        for (Voucher toBeVerified : voucherList) {
            //状态未使用 暂无错误码
            if (!toBeVerified.getStatus().equals(VoucherStatusEnum.VOUCHER_USED.getCode())) {
                cancelRedeemResponse.setResponseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.code()));
                cancelRedeemResponse.setResponseMessage(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.desc());
                message = ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.desc();

            }
        }





        //cancelRedeemResponse = checkCancelRedeemVoucher(request);

        if (null!= cancelRedeemResponse.getResponseCode() && !cancelRedeemResponse.getResponseCode().equals(0)) {
            return cancelRedeemResponse;
        }


        Pos pos = masterDataCache.getPos(terminalId);
        if (null == pos){
            throw new GTechBaseException(ResultErrorCodeEnum.NO_POS_DATA_FOUND.code(), ResultErrorCodeEnum.NO_POS_DATA_FOUND.desc());
        }


       Outlet outlet = masterDataCache.getOutlet(pos.getOutletCode());
        if (null == outlet){
            throw new GTechBaseException(ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.code(), ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.desc());
        }


        List<VoucherBatch> voucherBatch;
        try {
            voucherBatch = voucherBatchService.queryVoucherBatchByCodeList(voucherList.stream().map(x->x.getVoucherBatchCode()).collect(Collectors.toList()));
        } catch (Exception e) {
            //cpg error
            throw new GTechBaseException();
        }
        Map<String, VoucherBatch> voucherBatchMap = voucherBatch.stream().collect(Collectors.toMap(x -> x.getVoucherBatchCode(), x -> x));


        if (null == cancelRedeemResponse.getResponseCode() || cancelRedeemResponse.getResponseCode().equals(0)) {
            Weekend<Voucher> selectVoucherW = Weekend.of(Voucher.class);
            selectVoucherW.weekendCriteria().andIn(Voucher::getVoucherCode,voucherCodes);
            List<Voucher> vouchers = voucherMapper.selectByCondition(selectVoucherW);

            Voucher voucher = new Voucher();
            voucher.setStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
            voucher.setUsedOutlet(null);
            voucher.setUsedTime(null);
            Example voucherExample = new Example(Voucher.class);
            voucherExample.createCriteria().andIn(Voucher.C_VOUCHER_CODE, vouchers.stream().map(x->x.getVoucherCode()).collect(Collectors.toList()));
            voucherMapper.updateByConditionSelective(voucher, voucherExample);
            success = "0";
        }
        //插入交易数据
        insertTransactionData(request.getTransactionId(), voucherList, voucherBatchMap, cpg, pos,
                outlet, TransactionTypeEnum.GIFT_CARD_BULK_CANCEL_REDEEM,
                null, approveCode, "",notes, success, message, batchId);


        return this.makeResponse(request, Integer.valueOf(batchId.substring(batchId.length()-4)), voucherTypeDataMap, voucherList, cpg, cpgTypeData,  cancelRedeemResponse, approveCode);

    }



    private APIBulkCancelRedeemResponse makeResponse(BulkCancelRedeemRequest request, Integer batchId, Map<String, Map<String,TransactionData>> dataMap, List<Voucher> toBeVerifiedList, Cpg cpg, GetCpgTypeResponse cpgTypeData, APIBulkCancelRedeemResponse cancelRedeemResponse, String approveCode) {
        //cancelRedeemResponse.setCardExpiry(DateUtil.format(voucherEffectiveDate, YYYY_MM_DD_T_HH_MM_SS));
        cancelRedeemResponse.setInvoiceNumber(request.getOriginalInvoiceNumber());
        //cancelRedeemResponse.setSettlementDate(DateUtil.format(voucherEffectiveDate, DDMMYYYY));

        //DateUtil.format(voucherEffectiveDate, DDMMYYYY);

        //cancelRedeemResponse.setExpiry(DateUtil.format(voucherEffectiveDate, DDMMYYYY));

        ArrayList<APIBulkCancelRedeemResponse.VoucherInfo> voucherInfos = new ArrayList<>();
        toBeVerifiedList.forEach(x->{
            APIBulkCancelRedeemResponse.VoucherInfo voucherInfo = new APIBulkCancelRedeemResponse.VoucherInfo();

            voucherInfo.setAmount(x.getDenomination());
            voucherInfo.setCardStatusId(x.getVoucherStatus() == 0 ?
                    Integer.valueOf(VoucherEnableDisablePosEnum.getStatusIdByCode(x.getVoucherStatus())) :
                    Integer.valueOf(VoucherStatusPosEnum.getStatusIdByCode(x.getStatus())));
            voucherInfo.setCardStatus(x.getVoucherStatus() == 0 ?
                    VoucherEnableDisablePosEnum.getDescByCode(x.getVoucherStatus()) :
                    VoucherStatusPosEnum.getDescByCode(x.getStatus()));
            voucherInfo.setPreviousBalance(x.getDenomination());
            if (x.getMopCode().equals("VCE")) {
                voucherInfo.setCardCreationType(VIRTUAL);
            } else if (x.getMopCode().equals("VCR")) {
                voucherInfo.setCardCreationType("Physical");
            }

            TransactionData tData = null;
            if (null == dataMap.get(x.getVoucherCode()).get(TransactionTypeEnum.GIFT_CARD_ACTIVATE.getCode())) {
                tData = dataMap.get(x.getVoucherCode()).get(TransactionTypeEnum.GIFT_CARD_ACTIVATE_ONLY.getCode());

            } else {
                tData = dataMap.get(x.getVoucherCode()).get(TransactionTypeEnum.GIFT_CARD_ACTIVATE.getCode());

            }

            if(null != tData){
                cancelRedeemResponse.setActivationDate(DateUtil.format(tData.getTransactionDate(), YYYY_MM_DD_T_HH_MM_SS));
            }

            voucherInfos.add(voucherInfo);
        });
        cancelRedeemResponse.setVoucherInfos(voucherInfos);

        cancelRedeemResponse.setCardCurrencySymbol(CardCurrencySymbolEnum.getByCode(cpg.getCurrencyCode()));

        cancelRedeemResponse.setCardType("GB-GOLD");


        cancelRedeemResponse.setCardProgramGroupType(cpgTypeData.getCpgTypeName());
        cancelRedeemResponse.setCurrentBatchNumber(batchId);
        cancelRedeemResponse.setApprovalCode(approveCode);
        if (null == cancelRedeemResponse.getResponseCode()){
            cancelRedeemResponse.setResponseCode(0);
            cancelRedeemResponse.setResponseMessage(TRANSACTION_SUCCESSFUL1);
        }
        cancelRedeemResponse.setTransactionId(request.getTransactionId());
        cancelRedeemResponse.setTransactionType(String.valueOf(GvPosTransactionTypesEnum.GIFT_CARD_CANCEL_REDEEM.getCode()));
        return cancelRedeemResponse;
    }


    private Example queryDistributableVoucherExample(String cpgCode, Date expiryDate, String customerCode) {

        List<CustomerOrder> customerOrderList = this.customerOrderMapper
                .select(CustomerOrder.builder().customerCode(customerCode).status(CustomerOrderStatusEnum.COMPLETED.getStatus()).build());

        if (CollectionUtils.isEmpty(customerOrderList)) return null;

        Set<String> voucherBatchCodeSet = customerOrderList.stream().map(CustomerOrder::getVoucherBatchCode).filter(StringUtils::isNotBlank).collect(Collectors.toSet());

        final Example queryExample = new Example(Voucher.class);
        queryExample.createCriteria()
                .andIn(Voucher.C_VOUCHER_BATCH_CODE, voucherBatchCodeSet)
                .andNotEqualTo(Voucher.C_VOUCHER_STATUS, 0)
                .andNotIn(Voucher.C_CIRCULATION_STATUS, Arrays.asList(VoucherCirculationStatusEnum.VOUCHER_DISTRIBUTING.getCode()
                        , VoucherCirculationStatusEnum.VOUCHER_DISTRIBUTED.getCode()))
                .andEqualTo(Voucher.C_CPG_CODE, GvConvertUtils.toString(cpgCode, ""))
                .andEqualTo(Voucher.C_VOUCHER_EFFECTIVE_DATE, expiryDate)
                .andEqualTo(Voucher.C_VOUCHER_OWNER_TYPE, CUSTOMER)
                .andEqualTo(Voucher.C_VOUCHER_OWNER_CODE, GvConvertUtils.toString(customerCode, ""));

        return queryExample;
    }

    @Override
    public VoucherRangeCheckResponse checkVoucherRange(VoucherRangeCheckRequest request) {
        request.checkStartEnd();
        VoucherRangeCheckResponse response = new VoucherRangeCheckResponse();
        
        // 1. 获取订单下所有子订单的VPG类型和数量
        List<CustomerOrderDetails> orderDetails = customerOrderDetailsMapper.select(
            CustomerOrderDetails.builder()
                .customerOrderCode(request.getOrderCode())
                .build()
        );
        
        if (CollectionUtils.isEmpty(orderDetails)) {
            throw new GTechBaseException(ResultErrorCodeEnum.ORDER_NOT_FOUND.code(), 
                "Order details not found");
        }
        
        // 2. 检查请求的券是否属于订单中的VPG类型，且状态为待发行
        Example vpgExample = new Example(Voucher.class);
        Example.Criteria criteria = vpgExample.createCriteria();
        criteria.andGreaterThanOrEqualTo("voucherCodeNum", request.getStartNo())
               .andLessThanOrEqualTo("voucherCodeNum", request.getEndNo())
               .andIn("cpgCode", orderDetails.stream()
                       .map(CustomerOrderDetails::getCpgCode)
                       .collect(Collectors.toList()));
        
        List<Voucher> requestedVouchers = voucherMapper.selectByCondition(vpgExample);
        
        if (CollectionUtils.isEmpty(requestedVouchers)) {
            throw new GTechBaseException(ResultErrorCodeEnum.VOUCHER_NOT_FOUND.code(), 
                "Vouchers not found in order VPG types");
        }
        
        // 3. 检查每种VPG类型的数量是否超出订单限制
        Map<String, Long> requestedVpgCount = requestedVouchers.stream()
                .collect(Collectors.groupingBy(Voucher::getCpgCode, Collectors.counting()));
                
        Map<String, Integer> orderVpgLimit = orderDetails.stream()
                .collect(Collectors.toMap(
                    CustomerOrderDetails::getCpgCode,
                    CustomerOrderDetails::getVoucherNum,
                    (v1, v2) -> v1 // 如果有重复的CPG，保留第一个数量
                ));
                
        for (Map.Entry<String, Long> entry : requestedVpgCount.entrySet()) {
            Integer limit = orderVpgLimit.get(entry.getKey());
            if (limit == null || entry.getValue() > limit) {
                throw new GTechBaseException(ResultErrorCodeEnum.VOUCHER_QUANTITY_EXCEEDED.code(), 
                    "Number of voucher has reached Max limit");
            }
        }
        
        // 4. 检查券号是否连续
        List<String> voucherCodes = requestedVouchers.stream()
                .map(Voucher::getVoucherCode)
                .sorted()
                .collect(Collectors.toList());
                
        if (!isSequential(voucherCodes)) {
            throw new GTechBaseException(ResultErrorCodeEnum.VOUCHER_NOT_SEQUENTIAL.code(),
                "Voucher codes must be sequential");
        }
        
        // 5. 检查是否已发行
        List<String> issuedVouchers = voucherMapper.findIssuedVoucherInRange(
            request.getStartNo(), 
            request.getEndNo(),
            VoucherCirculationStatusEnum.VOUCHER_RECEIVED.getCode()
        );
        
        if (!issuedVouchers.isEmpty()) {
            throw new GTechBaseException(ResultErrorCodeEnum.VOUCHER_ALREADY_ISSUED.code(),
                "Some vouchers have already been issued");
        }
        
        response.setHasIssued(false);
        response.setIssuedVoucherNos(issuedVouchers);
        
        return response;
    }

    private boolean isSequential(List<String> voucherCodes) {
        if (CollectionUtils.isEmpty(voucherCodes) || voucherCodes.size() == 1) {
            return true;
        }
        
        for (int i = 1; i < voucherCodes.size(); i++) {
            String prev = voucherCodes.get(i - 1);
            String curr = voucherCodes.get(i);
            if (!isNextVoucherCode(prev, curr)) {
                return false;
            }
        }
        return true;
    }

    private boolean isNextVoucherCode(String prev, String curr) {
        try {
            long prevNum = Long.parseLong(prev.replaceAll("[a-zA-Z]", ""));
            long currNum = Long.parseLong(curr.replaceAll("[a-zA-Z]", ""));
            return currNum == prevNum + 1;
        } catch (NumberFormatException e) {
            // 如果券号不是纯数字，需要根据实际规则判断
            return false;
        }
    }

    @Transactional
    @Override
    public BulkCancelRedeemResponseV2 bulkCancelRedeemV2(BulkCancelRedeemRequestV2 request, String terminalId, String batchId) {
        BulkCancelRedeemResponseV2 response = new BulkCancelRedeemResponseV2();

        String approveCode = null;

        String invoiceNumber = gvCodeHelper.generateInvoiceNumber();

        try {
            // 1. 收集所有券号
            List<String> voucherCodes = request.getVoucherItems().stream()
                    .map(item -> item.getVoucherInfo().getVoucherNumber())
                    .collect(Collectors.toList());
                    
            // 2. 通过原始交易信息查询兑换记录
            List<TransactionData> transactions = voucherMapper.queryRedeemTransactions(
                voucherCodes,
                request.getOriginalTransactionId(),
                request.getOriginalInvoiceNumber(),
                request.getOriginalBatchNumber()
            );

            //取最新的一条
            Map<String, TransactionData> latestTransactionsMap = transactions.stream()
                    .collect(Collectors.toMap(
                            TransactionData::getVoucherCode,
                            transaction -> transaction,
                            (transaction1, transaction2) ->   transaction1
                    ));
            transactions = new ArrayList<>(latestTransactionsMap.values());

            // 3. 验证查询结果
            if (CollectionUtils.isEmpty(transactions) || transactions.size() != voucherCodes.size()) {
                throw new GTechBaseException(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.code(),
                        ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.desc());
            }
            
            // 4. 查询券信息
            VoucherDto voucherDto = new VoucherDto();
            voucherDto.setVoucherCodeList(voucherCodes);
            List<Voucher> vouchers = voucherMapper.queryByVoucherCodeList(voucherDto);
            
            if (CollectionUtils.isEmpty(vouchers)) {
                throw new GTechBaseException(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.code(),
                        ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.desc());
            }
            
            // 5. 校验原始交易信息
            validateOriginalTransaction(request, vouchers);
            
            // 6. 执行取消兑换
            // 设置券状态为未使用
            Voucher voucher = new Voucher();
            voucher.setUsedTime(null);
            voucher.setStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
            Example voucherExample = new Example(Voucher.class);
            voucherExample.createCriteria()
                    .andIn(Voucher.C_VOUCHER_CODE, vouchers.stream().map(Voucher::getVoucherCode).collect(Collectors.toList()));
            voucherMapper.setUsedOrSalesNull(voucher, voucherExample,0);
            
            // 7. 记录交易
            approveCode = recordTransaction(request, vouchers, terminalId, batchId,invoiceNumber);
            
            // 8. 构建成功响应
            buildSuccessResponse(response, request, vouchers, batchId, approveCode,invoiceNumber);
            
        } catch (GTechBaseException e) {

            /*Integer errCode = GvPosCommonResponseCodesEnum.VALIDATION_FAILED.getResponseCode();
            String errMsg = GvPosCommonResponseCodesEnum.VALIDATION_FAILED.getResponseMessage();
            */
            // 9. 构建失败响应
            buildFailureResponse(response, request, e.getCode(), e.getMessage());
        }
        
        return response;
    }

    private void validateOriginalTransaction(BulkCancelRedeemRequestV2 request, List<Voucher> vouchers) {
        List<String> voucherCodes = vouchers.stream()
                .map(Voucher::getVoucherCode)
                .collect(Collectors.toList());
/*
        // 查询原始兑换交易记录
        Example example = new Example(TransactionData.class);
        example.createCriteria()
                .andIn("voucherCode", voucherCodes)
                .andEqualTo("transactionType", TransactionTypeEnum.GIFT_CARD_REDEEM.getCode())
                .andEqualTo("transactionId", request.getOriginalTransactionId())
                .andEqualTo("invoiceNumber", request.getOriginalInvoiceNumber());
    
        if (request.getOriginalBatchNumber() != null) {
            example.and().andEqualTo("batchNumber", request.getOriginalBatchNumber());
        }

        List<TransactionData> transactionData = transactionDataMapper.selectByCondition(example);

        // 验证查询结果
        if (CollectionUtils.isEmpty(transactionData) || transactionData.size() != voucherCodes.size()) {
            throw new GTechBaseException(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.code(),
                    ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.desc());
        }*/

        // 验证券状态
        for (Voucher voucher : vouchers) {
            if (!VoucherStatusEnum.VOUCHER_USED.getCode().equals(voucher.getStatus())) {
                throw new GTechBaseException(ResultErrorCodeEnum.VOUCHER_STATUS_ERROR.code(),
                        ResultErrorCodeEnum.VOUCHER_STATUS_ERROR.desc());
            }
        }
    }

    private void buildSuccessResponse(BulkCancelRedeemResponseV2 response, BulkCancelRedeemRequestV2 request,
                                      List<Voucher> vouchers, String batchId, String approveCode, String invoiceNumber) {


        response.setResponseCode(0);
        response.setResponseMessage("Transaction successful.");
        response.setTransactionId(request.getTransactionId());

        response.setBatchId(Long.valueOf(batchId.substring(batchId.length() - 4)));
        response.setTotalSuccessVoucherCount(vouchers.size());
        response.setTotalVoucherCount(vouchers.size());
        response.setTransactionAmount(request.getTransactionAmount());
        response.setApprovalCode(approveCode);
        response.setReferenceNumber(request.getTransactionId().toString());
        response.setInvoiceNumber(invoiceNumber);

        // 构建券项响应
        for (int i = 0; i < request.getVoucherItems().size(); i++) {
            BulkCancelRedeemRequestV2.VoucherItem requestItem = request.getVoucherItems().get(i);
            Voucher voucher = vouchers.get(i);
            Cpg cpg = masterDataCache.getCpg(voucher.getCpgCode());
            ArticleMop articleMop = articleMopService.queryByArticleMopCode(cpg.getArticleMopCode());

            BulkCancelRedeemResponseV2.VoucherItemResponse itemResponse = new BulkCancelRedeemResponseV2.VoucherItemResponse();
            itemResponse.setItemNo(requestItem.getItemNo());
            itemResponse.setMopCode(voucher.getMopCode());
            itemResponse.setArticleCode(articleMop.getArticleCodeName());
            itemResponse.setVpgName(cpg.getCpgName());
            itemResponse.setTotalAmount(voucher.getDenomination());
            itemResponse.setTotalVoucherCount(request.getVoucherItems().size());
            
            BulkCancelRedeemResponseV2.VoucherDetail detail = new BulkCancelRedeemResponseV2.VoucherDetail();
            detail.setVoucherNumber(voucher.getVoucherCode());
            detail.setVoucherPIN(voucher.getVoucherPin());
            detail.setVoucherBalance(voucher.getDenomination());
            detail.setVoucherExpiryDate(voucher.getVoucherEffectiveDate());
            detail.setTransactionAmount(voucher.getDenomination());
            detail.setVoucherStatus("ACTIVATED");
            detail.setVpgName(cpg.getCpgName());
            detail.setArticleCode(articleMop.getArticleCodeName());
            detail.setMopCode(articleMop.getMopCode());
            detail.setBarcode(voucher.getVoucherBarcode());
            itemResponse.setVouchers(new ArrayList<>());
            itemResponse.getVouchers().add(detail);
            response.getVoucherItems().add(itemResponse);
        }
    }

    private String formatDate(Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        return sdf.format(date);
    }

    private String getVoucherCreationType(Voucher voucher) {
        // 根据券的特征判断类型
        return "VIRTUAL"; // 或其他类型
    }

    private String recordTransaction(BulkCancelRedeemRequestV2 request, List<Voucher> vouchers,
                                     String terminalId, String batchId, String invoiceNumber) {
        String approveCode = generateApproveCode();
        String success = "0";
        String message = "";
        String notes = "";


        Pos pos = masterDataCache.getPos(terminalId);
        if (null == pos){
            //pos error
            throw new GTechBaseException(ResultErrorCodeEnum.NO_POS_DATA_FOUND.code(), ResultErrorCodeEnum.NO_POS_DATA_FOUND.desc());
        }


        Outlet outlet = masterDataCache.getOutlet(pos.getOutletCode());
        if (null == outlet) {
            throw new GTechBaseException(ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.code(), ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.desc());
        }


        List<VoucherBatch> voucherBatch;
        try {
            voucherBatch = voucherBatchService.queryVoucherBatchByCodeList(vouchers.stream().map(x->x.getVoucherBatchCode()).collect(Collectors.toList()));
        } catch (Exception e) {
            //cpg error
            throw new GTechBaseException();
        }
        Map<String, VoucherBatch> voucherBatchMap = voucherBatch.stream().collect(Collectors.toMap(x -> x.getVoucherBatchCode(), x -> x));

        GetCpgRequest cpgRequest = new GetCpgRequest();
        cpgRequest.setCpgCode(vouchers.get(0).getCpgCode());
        Cpg cpg = masterDataCache.getCpg(cpgRequest.getCpgCode());


        // 插入交易数据
        insertTransactionData(
            Integer.valueOf(String.valueOf(request.getTransactionId())),
            vouchers,
            voucherBatchMap,
            cpg,
            pos,
            outlet,
            TransactionTypeEnum.GIFT_CARD_BULK_CANCEL_REDEEM,
            null,
            approveCode,
            invoiceNumber,
            notes,
            success,
            message,
            batchId
        );
        
        return approveCode;
    }

    private String generateApproveCode() {
        // 生成6位随机数字
        return String.format("%06d", RandomUtils.nextInt(0, 1000000));
    }

    private void buildFailureResponse(BulkCancelRedeemResponseV2 response, BulkCancelRedeemRequestV2 request,
            String errorCode, String errorMessage) {
        response.setResponseCode(Integer.valueOf(errorCode));
        response.setResponseMessage(errorMessage);
        response.setTransactionId(request.getTransactionId());
        response.setTotalSuccessVoucherCount(0);
        response.setTotalVoucherCount(request.getVoucherItems().size());
        response.setTransactionAmount(BigDecimal.ZERO);
        response.setReferenceNumber(request.getTransactionId().toString());
        
        // 构建失败项
        for (BulkCancelRedeemRequestV2.VoucherItem requestItem : request.getVoucherItems()) {
            BulkCancelRedeemResponseV2.RejectedVoucherItem rejectedItem = new BulkCancelRedeemResponseV2.RejectedVoucherItem();
            rejectedItem.setItemNo(requestItem.getItemNo() + "-1");
            rejectedItem.setArticleCode("");
            rejectedItem.setTotalAmount(BigDecimal.ZERO);

            BulkCancelRedeemResponseV2.VoucherDetail detail = new BulkCancelRedeemResponseV2.VoucherDetail();
            detail.setVoucherNumber(requestItem.getVoucherInfo().getVoucherNumber());
            detail.setResponseCode(Integer.valueOf(errorCode));
            detail.setResponseMessage("Transaction failed");
            detail.setVoucherStatus("");
            detail.setVoucherBalance(BigDecimal.ZERO);
            detail.setTransactionAmount(BigDecimal.ZERO);
            rejectedItem.setVouchers(new ArrayList<>());
            rejectedItem.getVouchers().add(detail);
            response.getRejectedVoucherList().add(rejectedItem);
        }
    }

    private boolean transactionExists(Long transactionId) {
        if (transactionId == null) {
            return false;
        }
        TransactionData transaction = transactionDataMapper.selectByPrimaryKey(transactionId);
        return transaction != null;
    }

    private boolean invoiceNumberValid(String invoiceNumber) {
        if (StringUtils.isBlank(invoiceNumber)) {
            return false;
        }
        // 根据业务规则验证发票号
        return true;
    }

}
