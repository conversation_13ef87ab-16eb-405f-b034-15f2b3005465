package com.gtech.gvcore.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.tomcat.util.threads.ThreadPoolExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.BookletStatusEnum;
import com.gtech.gvcore.common.enums.FlowEnum;
import com.gtech.gvcore.common.enums.FlowNodeEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.enums.VoucherAllocationBusinessTypeEnum;
import com.gtech.gvcore.common.enums.VoucherCirculationStatusEnum;
import com.gtech.gvcore.common.enums.VoucherOwnerTypeEnum;
import com.gtech.gvcore.common.enums.VoucherReceiveSourceTypeEnum;
import com.gtech.gvcore.common.enums.VoucherReceiveStatusEnum;
import com.gtech.gvcore.common.enums.VoucherStatusEnum;
import com.gtech.gvcore.common.request.allocation.GetVoucherAllocationRequest;
import com.gtech.gvcore.common.request.businesslog.CreateBusinessLogRequest;
import com.gtech.gvcore.common.request.businesslogdetail.CreateBusinessLogDetailRequest;
import com.gtech.gvcore.common.request.flow.SendNoticeRequest;
import com.gtech.gvcore.common.request.outlet.GetOutletRequest;
import com.gtech.gvcore.common.request.receive.CreateVoucherReceiveRequest;
import com.gtech.gvcore.common.request.receive.GetReceiveByCustomerOrderRequest;
import com.gtech.gvcore.common.request.receive.GetVoucherReceiveRequest;
import com.gtech.gvcore.common.request.receive.QueryVoucherReceiveBatchRequest;
import com.gtech.gvcore.common.request.receive.QueryVoucherReceiveRequest;
import com.gtech.gvcore.common.request.receive.ReceiveVoucherRequest;
import com.gtech.gvcore.common.request.receive.VoucherReceiveBatchRequest;
import com.gtech.gvcore.common.request.receive.VoucherReceiveRecordRequest;
import com.gtech.gvcore.common.request.voucher.UpdateVoucherStatusRequest;
import com.gtech.gvcore.common.request.voucherbatch.GetVoucherBatchRequest;
import com.gtech.gvcore.common.request.voucherbatch.UpdateVoucherBatchStatusRequest;
import com.gtech.gvcore.common.request.voucherbooklet.VoucherActivateUpdateBookletStatusDto;
import com.gtech.gvcore.common.response.allocation.GetVoucherAllocationResponse;
import com.gtech.gvcore.common.response.useraccount.PermissionCodeResponse;
import com.gtech.gvcore.common.response.voucherbatch.VoucherBatchResponse;
import com.gtech.gvcore.common.response.voucherreceive.ToBeReceiveVoucher;
import com.gtech.gvcore.common.response.voucherreceive.VoucherReceiveBatchResponse;
import com.gtech.gvcore.common.response.voucherreceive.VoucherReceiveResponse;
import com.gtech.gvcore.common.response.voucherreceive.VoucherStartAndEnd;
import com.gtech.gvcore.components.QueryVoucherComponent;
import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.dao.mapper.VoucherReceiveMapper;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.Issuer;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.dao.model.VoucherAllocation;
import com.gtech.gvcore.dao.model.VoucherBatch;
import com.gtech.gvcore.dao.model.VoucherBooklet;
import com.gtech.gvcore.dao.model.VoucherReceive;
import com.gtech.gvcore.dao.model.VoucherRequest;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.helper.PermissionHelper;
import com.gtech.gvcore.service.BusinessLogService;
import com.gtech.gvcore.service.CustomerOrderService;
import com.gtech.gvcore.service.FlowNoticeService;
import com.gtech.gvcore.service.GvUserAccountService;
import com.gtech.gvcore.service.IssuerService;
import com.gtech.gvcore.service.OutletService;
import com.gtech.gvcore.service.TransactionDataService;
import com.gtech.gvcore.service.VoucherAllocationService;
import com.gtech.gvcore.service.VoucherBatchService;
import com.gtech.gvcore.service.VoucherBookletService;
import com.gtech.gvcore.service.VoucherReceiveBatchService;
import com.gtech.gvcore.service.VoucherReceiveRecordService;
import com.gtech.gvcore.service.VoucherReceiveService;
import com.gtech.gvcore.service.VoucherRequestService;
import com.gtech.gvcore.service.VoucherService;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

@Service
@Slf4j
public class VoucherReceiveServiceImpl implements VoucherReceiveService {
	protected static final ThreadPoolExecutor executor = new ThreadPoolExecutor(50, 50, 100, TimeUnit.SECONDS,
			new LinkedBlockingDeque<>(1000), new ThreadPoolExecutor.CallerRunsPolicy());

	@Autowired
	private VoucherReceiveMapper voucherReceiveMapper;
	
	@Autowired
	private VoucherReceiveBatchService voucherReceiveBatchService;
	
	@Autowired
	private VoucherAllocationService voucherAllocationService;
	
	@Autowired
	private VoucherReceiveRecordService voucherReceiveRecordService;

	@Lazy
	@Autowired
	private VoucherService voucherService;


	@Autowired
	private VoucherMapper voucherMapper;

	@Lazy
	@Autowired
	private CustomerOrderService customerOrderService;

	@Autowired
	private VoucherBatchService voucherBatchService;

	@Autowired
	private BusinessLogService businessLogService;

	@Autowired
	private GvCodeHelper gvCodeHelper;


	
	@Autowired
	private FlowNoticeService flowNoticeService;
	
	@Autowired
	private VoucherRequestService voucherRequestService;
	
	@Autowired
	private GvUserAccountService userAccountService;
	
	@Autowired
	private OutletService outletService;

	@Autowired
	private IssuerService issuerService;

	@Autowired
	private PermissionHelper permissionHelper;

	@Autowired
	private TransactionDataService transactionDataService;

	@Autowired
	private VoucherBookletService voucherBookletService;

	@Autowired
	private QueryVoucherComponent queryVoucherComponent;

	@Value("${voucher.receive.batch.size:500}")
	private Integer pageSize;

	@Override
	//@Transactional(rollbackFor = Exception.class)
	public int createVoucherReceive(CreateVoucherReceiveRequest request) {

		VoucherReceive voucherReceive = BeanCopyUtils.jsonCopyBean(request, VoucherReceive.class);
		voucherReceive.setStatus(VoucherReceiveStatusEnum.PROCESSING.getCode());
		voucherReceive.setVoucherReceiveCode(gvCodeHelper.generateVoucherReceiveCode());
		int insert = voucherReceiveMapper.insert(voucherReceive);
		if (insert <= 0) {
			throw new GTechBaseException("","创建receive失败");
		}
		log.info("创建receive成功，receive编号：{}", voucherReceive.getVoucherReceiveCode());

		List<VoucherReceiveBatchRequest> batchList = request.getReceiveBatchList();
		if (CollectionUtils.isEmpty(batchList)) {
			return 0;
		}
		for (VoucherReceiveBatchRequest voucherReceiveBatchRequest : batchList) {
			voucherReceiveBatchRequest.setVoucherReceiveCode(voucherReceive.getVoucherReceiveCode());
			String bookletStartNo = voucherReceiveBatchRequest.getBookletStartNo();
			String bookletEndNo = voucherReceiveBatchRequest.getBookletEndNo();
			if (StringUtil.isNotEmpty(bookletStartNo) && StringUtil.isNotEmpty(bookletEndNo)) {
				Long num = Long.parseLong(bookletEndNo) - Long.parseLong(bookletStartNo) + 1;
				voucherReceiveBatchRequest.setBookletNum(Integer.valueOf(num.toString()));
			}
		}
		int i = voucherReceiveBatchService.saveReceiveBatch(batchList);
		if (i != batchList.size()) {
			throw new GTechBaseException("","创建receiveBatch失败");
		}
		log.info("创建receiveBatch成功，receive编号：{}", voucherReceive.getVoucherReceiveCode());

		return insert;
	}
	
	@Override
	public VoucherReceiveResponse getVoucherReceiveByCustomerOrder(GetReceiveByCustomerOrderRequest request){
		VoucherReceive voucherReceive = selectOneByCustomerOrder(request.getCustomerOrderCode(), null);
		return makeReceiveDetail(voucherReceive);
	}

	@Override
	public VoucherReceive getVoucherReceiveBySourceDataCode(String dataCode) {
		VoucherReceive request  = new VoucherReceive();
		request.setSourceDataCode(dataCode);
		return voucherReceiveMapper.selectOne(request);
	}

	@Override
	public VoucherReceiveResponse getVoucherReceive(GetVoucherReceiveRequest request) {
		String receiveCode = request.getVoucherReceiveCode();
		VoucherReceive voucherReceive = selectOneVoucherReceive(receiveCode);
		return makeReceiveDetail(voucherReceive);
	}

	private VoucherReceiveResponse makeReceiveDetail(VoucherReceive voucherReceive) {
		if (voucherReceive == null) {
			return null;
		}
		VoucherReceiveResponse voucherReceiveResponse = BeanCopyUtils.jsonCopyBean(voucherReceive, VoucherReceiveResponse.class);
		QueryVoucherReceiveBatchRequest queryVoucherReceiveBatchRequest = new QueryVoucherReceiveBatchRequest();
		queryVoucherReceiveBatchRequest.setVoucherReceiveCode(voucherReceive.getVoucherReceiveCode());
		voucherReceiveResponse.setReceiveBatchList(voucherReceiveBatchService.queryReceiveBatchList(queryVoucherReceiveBatchRequest));
		
		String sourceDataCode = voucherReceive.getSourceDataCode();
		String sourceType = voucherReceive.getSourceType();
		if (VoucherReceiveSourceTypeEnum.SALES.getCode().equals(sourceType) || VoucherReceiveSourceTypeEnum.RETURN.getCode().equals(sourceType)
				|| VoucherReceiveSourceTypeEnum.TRANSFER.getCode().equals(sourceType)) {
			GetVoucherAllocationRequest getVoucherAllocationRequest = new GetVoucherAllocationRequest();
			getVoucherAllocationRequest.setVoucherAllocationCode(sourceDataCode);
			//TODO 性能问题待优化,这里不能调用voucherAllocationService.getVoucherAllocation(getVoucherAllocationRequest)接口，因为这个接口会查询出voucherAllocation的voucherList，这个voucherList会导致性能问题
			Result<GetVoucherAllocationResponse> result = voucherAllocationService.getVoucherAllocationByReceive(getVoucherAllocationRequest);
			if (result.isSuccess()) {
				voucherReceiveResponse.setGetVoucherAllocationResponse(result.getData());
			}
			voucherReceiveResponse.setSourceDataCode(result.getData().getVoucherRequestCode());
		} else if (VoucherReceiveSourceTypeEnum.GENERATE.getCode().equals(voucherReceive.getSourceType())) {
			GetVoucherBatchRequest getVoucherBatchRequest = new GetVoucherBatchRequest();
			getVoucherBatchRequest.setVoucherBatchCode(sourceDataCode);
			VoucherBatchResponse voucherBatchResponse = voucherBatchService.getVoucherBatch(getVoucherBatchRequest);
			voucherReceiveResponse.setVoucherBatchResponse(voucherBatchResponse);
		}
		Issuer issuer = issuerService.queryByIssuerCode(voucherReceiveResponse.getIssuerCode());
		if (issuer != null) {
			voucherReceiveResponse.setIssuerName(issuer.getIssuerName());
		}
		List<VoucherReceiveBatchResponse> receiveBatchList = voucherReceiveResponse.getReceiveBatchList();
		List<Voucher> voucherList = new ArrayList<>();
		for (VoucherReceiveBatchResponse voucherReceiveBatchResponse : receiveBatchList) {
			String voucherStartNo = voucherReceiveBatchResponse.getVoucherStartNo();
			String voucherEndNo = voucherReceiveBatchResponse.getVoucherEndNo();
			//查询券号
			Example voucherExample = new Example(Voucher.class);
			voucherExample.createCriteria()
					.andEqualTo("circulationStatus",2)
					.andGreaterThanOrEqualTo("voucherCode", voucherStartNo).andLessThanOrEqualTo("voucherCode", voucherEndNo);
			List<Voucher> vouchers = voucherMapper.selectByCondition(voucherExample);
			voucherList.addAll(vouchers);
		}
		if (CollectionUtils.isNotEmpty(voucherList)) {
			List<ToBeReceiveVoucher> toBeReceiveVouchers = segmentStrings(voucherList);
			voucherReceiveResponse.setToBeReceiveVoucher(toBeReceiveVouchers);
		}


		return voucherReceiveResponse;
	}


	public static List<ToBeReceiveVoucher> segmentStrings(List<Voucher> list) {

		//list根据券号排序
		Collections.sort(list, new Comparator<Voucher>() {
			@Override
			public int compare(Voucher o1, Voucher o2) {
				return Long.compare(Long.parseLong(o1.getVoucherCode()), Long.parseLong(o2.getVoucherCode()));
			}
		});

		List<ToBeReceiveVoucher> segmentedStrings = new ArrayList<>();
		int start = 0;
		int end = 0;
		for (int i = 1; i < list.size(); i++) {
			if (Long.parseLong(list.get(i).getVoucherCode()) == Long.parseLong(list.get(i - 1).getVoucherCode()) + 1) {
				end = i;
			} else {
				segmentedStrings.add(new ToBeReceiveVoucher(new
						VoucherStartAndEnd(list.get(start).getVoucherCode(),list.get(start).getBookletCode())
						,new VoucherStartAndEnd(list.get(end).getVoucherCode(),list.get(end).getBookletCode())));
				start = i;
				end = i;
			}
		}
		segmentedStrings.add(new ToBeReceiveVoucher(new
				VoucherStartAndEnd(list.get(start).getVoucherCode(),list.get(start).getBookletCode())
				,new VoucherStartAndEnd(list.get(end).getVoucherCode(),list.get(end).getBookletCode())));
		return segmentedStrings;
	}


	@SuppressWarnings("unchecked")
	@Override
	public PageResult<VoucherReceiveResponse> queryVoucherReceivePage(QueryVoucherReceiveRequest request) {

		Map<String, Object> map = JSON.parseObject(JSON.toJSONString(request), Map.class);
		if (StringUtil.isNotEmpty(request.getBookletStartNo()) || StringUtil.isNotEmpty(request.getBookletEndNo())
				|| StringUtil.isNotEmpty(request.getVoucherStartNo()) || StringUtil.isNotEmpty(request.getVoucherEndNo())
				|| StringUtil.isNotEmpty(request.getCpgCode())) {
			QueryVoucherReceiveBatchRequest batchRequest = BeanCopyUtils.jsonCopyBean(request, QueryVoucherReceiveBatchRequest.class);
			List<VoucherReceiveBatchResponse> batchResponseList = voucherReceiveBatchService.queryReceiveBatchList(batchRequest);
			if (CollectionUtils.isEmpty(batchResponseList)) {
				return new PageResult<>(Collections.emptyList(), 0l);
			}
			List<String> receiveCodeList = batchResponseList.stream().map(VoucherReceiveBatchResponse::getVoucherReceiveCode).distinct()
					.collect(Collectors.toList());
			map.put("receiveCodeList", receiveCodeList);
		}

		final PermissionCodeResponse permission = this.permissionHelper.getPermission(request.getUserCode(), request.getIssuerCode());
		if (!PermissionCodeResponse.hasOutlet(permission)) {
			return new PageResult<>(Collections.emptyList(), 0L);
		}
		map.put("outletCodeRangeList", permission.getOutletCodeList());

		PageMethod.startPage(request.getPageNum(), request.getPageSize());
		List<VoucherReceive> receiveList = voucherReceiveMapper.query(map);
		PageInfo<VoucherReceive> pageInfo = new PageInfo<>(receiveList);
		
		List<VoucherReceiveResponse> list = makeReceiveListResult(receiveList);
		return new PageResult<>(list, pageInfo.getTotal());
	}

	@Override
	//@Transactional
	public void receive(ReceiveVoucherRequest receiveVoucherRequest) {
		String receiveCode = receiveVoucherRequest.getVoucherReceiveCode();
		VoucherReceive voucherReceive = selectOneVoucherReceive(receiveCode);
		if (voucherReceive == null) {
			throw new GTechBaseException(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
		}
		if (VoucherReceiveStatusEnum.PROCESSING.getCode() != voucherReceive.getStatus()) {
			throw new GTechBaseException(ResultErrorCodeEnum.VOUCHER_RECEIVE_STATUS_ERROR.code(), ResultErrorCodeEnum.VOUCHER_RECEIVE_STATUS_ERROR.desc());
		}
		
		List<VoucherReceiveRecordRequest> receiveRecordList = receiveVoucherRequest.getReceiveRecordList();
		voucherReceive.setUpdateUser(receiveVoucherRequest.getCreateUser());
		receiveVoucher(voucherReceive, receiveRecordList);
	}

	private Integer receiveVoucher(VoucherReceive voucherReceive, List<VoucherReceiveRecordRequest> receiveRecordList) {

		String receiveCode = voucherReceive.getVoucherReceiveCode();
		String updateUser = voucherReceive.getUpdateUser();
		QueryVoucherReceiveBatchRequest queryVoucherReceiveBatchRequest = new QueryVoucherReceiveBatchRequest();
		queryVoucherReceiveBatchRequest.setVoucherReceiveCode(receiveCode);
		List<VoucherReceiveBatchResponse> receiveBatchList = voucherReceiveBatchService.queryReceiveBatchList(queryVoucherReceiveBatchRequest);

		if (CollectionUtils.isEmpty(receiveBatchList)) {
			log.error("receiveVoucher error receiveBatchList null, receiveCode:{}", receiveCode);
			return 0;
		}
		String sourceType = voucherReceive.getSourceType();
		if (VoucherReceiveSourceTypeEnum.CUSTOMER_ORDER.getCode().equals(sourceType)) {
			receiveRecordList = new ArrayList<>();
			for (VoucherReceiveBatchResponse voucherReceiveBatchResponse : receiveBatchList) {
				if (voucherReceiveBatchResponse.getReceivedNum() != voucherReceiveBatchResponse.getVoucherNum()) {
					VoucherReceiveRecordRequest voucherReceiveRecordRequest = BeanCopyUtils.jsonCopyBean(voucherReceiveBatchResponse,
							VoucherReceiveRecordRequest.class);
					receiveRecordList.add(voucherReceiveRecordRequest);
				}
			}

		}
		if (CollectionUtils.isEmpty(receiveRecordList)) {
			log.error("receiveVoucher error receiveRecordList null, receiveCode:{}", receiveCode);
			return 0;
		}
		for (VoucherReceiveRecordRequest voucherReceiveRecordRequest : receiveRecordList) {
			String receiveStartNo = voucherReceiveRecordRequest.getVoucherStartNo();
			String receiveEndNo = voucherReceiveRecordRequest.getVoucherEndNo();
			getReceiveResponseIfMatch(receiveBatchList, receiveStartNo, receiveEndNo);
		}

		List<Voucher> errorList = new CopyOnWriteArrayList<>();
		JSONArray logContent = new JSONArray();


		AtomicInteger receiveNum = new AtomicInteger();

		String approveCode = gvCodeHelper.generateApproveCode();
		for (VoucherReceiveRecordRequest voucherReceiveRecordRequest : receiveRecordList) {

			try {
				int count = getReceiveNum(voucherReceive, receiveCode, updateUser, receiveBatchList, sourceType, errorList, logContent, voucherReceiveRecordRequest, approveCode);
				receiveNum.getAndAdd(count);
			} catch (Exception e) {
				log.error(e.getMessage(), e);
			}

		}

		List<VoucherReceiveRecordRequest> recordRequests = receiveRecordList.stream().filter(vo -> vo.getReceivedNum() != null).collect(Collectors.toList());

		try {
			// update receivedNum
			updateVoucherReceivedNum(receiveCode, receiveNum.get(), updateUser);
			voucherReceiveRecordService.saveVoucherReceiveRecord(recordRequests);
			// add business log
			CreateBusinessLogRequest createBusinessLogRequest = new CreateBusinessLogRequest();
			createBusinessLogRequest.setContentCode(receiveCode);
			createBusinessLogRequest.setCreateUser(updateUser);
			createBusinessLogRequest.setSuccess(receiveNum.get());
			createBusinessLogRequest.setFailed(errorList.size());
			createBusinessLogRequest.setContent(JSON.toJSONString(logContent));
			makeErrorList(errorList, createBusinessLogRequest, sourceType);
			businessLogService.createBusinessLog(createBusinessLogRequest);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}

		return receiveNum.get();

	}

	private int getReceiveNum(VoucherReceive voucherReceive, String receiveCode, String updateUser, List<VoucherReceiveBatchResponse> receiveBatchList, String sourceType, List<Voucher> errorList, JSONArray logContent,  VoucherReceiveRecordRequest voucherReceiveRecordRequest, String approvalCode) {
		int receiveNum = 0;
		String receiveStartNo = voucherReceiveRecordRequest.getVoucherStartNo();
		String receiveEndNo = voucherReceiveRecordRequest.getVoucherEndNo();

		VoucherReceiveBatchResponse voucherReceiveBatchResponse = getReceiveResponseIfMatch(receiveBatchList, receiveStartNo, receiveEndNo);
		voucherReceiveRecordRequest.setDenomination(voucherReceiveBatchResponse.getDenomination());

		List<Voucher> errorStatusList = voucherService.queryNotReceiveVoucher(receiveStartNo, receiveEndNo,voucherReceive.getSourceType());
		errorList.addAll(errorStatusList);
		if (CollectionUtils.isNotEmpty(errorStatusList)){
			log.error("接收校验失败{}",JSON.toJSONString(errorStatusList));
		}

		int count = updateVoucherStatus(receiveStartNo, receiveEndNo, sourceType);
		if (count > 0) {

			if (VoucherReceiveSourceTypeEnum.CUSTOMER_ORDER.getCode().equals(sourceType)) {
				List<Voucher> vouchers = voucherService.queryVoucherByInterval(receiveStartNo, receiveEndNo);

				if (CollectionUtils.isNotEmpty(errorStatusList)) vouchers.removeIf(x->errorStatusList.stream().map(Voucher::getVoucherCode).collect(Collectors.toList()).contains(x.getVoucherCode()));

				VoucherAllocation allocationByCode = voucherAllocationService.getAllocationByCode(voucherReceive.getSourceDataCode());
				CustomerOrder customerOrder = customerOrderService.queryByCustomerOrderCode(allocationByCode.getSourceDataCode());
				queryVoucherComponent.insertTransactionDataByVoucherList(vouchers, customerOrder, approvalCode, TransactionTypeEnum.GIFT_CARD_ACTIVATE);
				//addTransactionData(customerOrder,vouchers,approvalCode);
				log.info("-------------------插入交易记录成功---------------------");
				//激活册子
				List<String> bookletCode = vouchers.stream().map(Voucher::getBookletCode).distinct().collect(Collectors.toList());
				ListUtils.partition(bookletCode, pageSize).forEach(x -> {
					voucherBookletService.voucherActivateUpdateBookletStatus(
							VoucherActivateUpdateBookletStatusDto.builder().type("3")
									.bookletCodeList(x)
									.statusEnum(BookletStatusEnum.PARTIALLY_ACTIVATED)
									.build());
				});
			}



			// update receive batch receivedNum
				try {
					voucherReceiveBatchService.updateReceivedNum(voucherReceiveBatchResponse.getVoucherReceiveBatchCode(), count);
				} catch (Exception e) {
					log.error(e.getMessage(), e);
				}
			//voucherReceiveBatchService.updateReceivedNum(voucherReceiveBatchResponse.getVoucherReceiveBatchCode(), count);
			// add receive record
			voucherReceiveRecordRequest.setReceivedNum(count);
			voucherReceiveRecordRequest.setVoucherReceiveCode(receiveCode);
			voucherReceiveRecordRequest.setCpgCode(voucherReceiveBatchResponse.getCpgCode());
			voucherReceiveRecordRequest.setCreateUser(updateUser);
			receiveNum += count;
		}else {
			log.error("接收券，修改券数量错误receiveStartNo:{},receiveEndNo:{},sourceType:{},SourceDataCode:{},approvalCode:{}",receiveStartNo, receiveEndNo, sourceType, voucherReceive.getSourceDataCode(), approvalCode);
		}


		Map<String, String> contentMap = new HashMap<>();
		contentMap.put(VoucherBooklet.C_VOUCHER_NO_START, receiveStartNo);
		contentMap.put(VoucherBooklet.C_VOUCHER_NO_END, receiveEndNo);
		logContent.add(contentMap);
		return receiveNum;
	}

	private static VoucherOwnerTypeEnum getCodeType(String code) {
		String codeTitle = ConvertUtils.toString(StringUtils.substring(code, 0, 2), StringUtils.EMPTY).toUpperCase();
		VoucherOwnerTypeEnum voucherOwnerTypeEnum;
		if ("CU".equals(codeTitle)) voucherOwnerTypeEnum = VoucherOwnerTypeEnum.CUSTOMER;
		else if ("CP".equals(codeTitle)) voucherOwnerTypeEnum = VoucherOwnerTypeEnum.PRINTER;
		else voucherOwnerTypeEnum = VoucherOwnerTypeEnum.OUTLET;
		return voucherOwnerTypeEnum;
	}

	private void makeErrorList(List<Voucher> errorList, CreateBusinessLogRequest createBusinessLogRequest, String sourceType) {
		if (!CollectionUtils.isEmpty(errorList)) {
			List<CreateBusinessLogDetailRequest> businessLogDetailList = new ArrayList<>();
			createBusinessLogRequest.setBusinessLogDetailList(businessLogDetailList);
			for (Voucher vo : errorList) {
				CreateBusinessLogDetailRequest createBusinessLogDetailRequest = new CreateBusinessLogDetailRequest();
				createBusinessLogDetailRequest.setDetailContentCode(vo.getVoucherCode());
				String message = "";
				Integer circulationStatus = vo.getCirculationStatus();
				if (VoucherCirculationStatusEnum.VOUCHER_TO_BE_RECEIVED.getCode() != circulationStatus) {
					message =  "The gift voucher has been "+ VoucherCirculationStatusEnum.getDesc(circulationStatus) +" before.";
				} else if (vo.getVoucherStatus().intValue() == GvcoreConstants.STATUS_DISABLE) {
					message =  "The gift voucher has been DISABLE";
				} else if (VoucherReceiveSourceTypeEnum.GENERATE.getCode().equals(sourceType)
						&& vo.getStatus().intValue() == VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode()) {
					message =  "The gift voucher has been "+ VoucherStatusEnum.valueOf(String.valueOf(vo.getStatus())).getDesc();
				}
				Map<String, Object> reasonMap = new HashMap<>();
				reasonMap.put("desc", message);
				createBusinessLogDetailRequest.setReason(JSON.toJSONString(reasonMap));
				businessLogDetailList.add(createBusinessLogDetailRequest);
			}
		}
	}

	// 发送有邮箱，不再调用
	@SuppressWarnings("unused")
	private void sendEmail(VoucherReceive voucherReceive) {
		if (voucherReceive == null) {
			return;
		}
		String receiveCode = voucherReceive.getVoucherReceiveCode();
		String sourceType = voucherReceive.getSourceType();
		SendNoticeRequest sendNoticeRequest = new SendNoticeRequest();
		String type = "";
		String flowCode = "";
		List<String> emails = getSendEmails(voucherReceive);
		if (VoucherReceiveSourceTypeEnum.SALES.getCode().equals(sourceType)) {
			flowCode = FlowEnum.SALES_VOUCHER_FLOW.getCode();
		} else if (VoucherReceiveSourceTypeEnum.RETURN.getCode().equals(sourceType)) {
			flowCode = FlowEnum.RETURN_VOUCHER_FLOW.getCode();
			type = VoucherReceiveSourceTypeEnum.RETURN.getDesc();
		} else if (VoucherReceiveSourceTypeEnum.TRANSFER.getCode().equals(sourceType)) {
			flowCode = FlowEnum.TRANSFER_ORDER_FLOW.getCode();
			type = VoucherReceiveSourceTypeEnum.TRANSFER.getDesc();
		} else {
			return;
		}
		sendNoticeRequest.setFlowCode(flowCode);
		sendNoticeRequest.setFlowNodeCode(FlowNodeEnum.RECEIVE.getCode());
		sendNoticeRequest.setBusinessCode(receiveCode);
		sendNoticeRequest.setEmails(emails);
		Map<String, Object> map = new HashMap<>();
		map.put("type", type);
		sendNoticeRequest.setExtendParams(map);
		flowNoticeService.send(sendNoticeRequest);
	}

	private List<String> getSendEmails(VoucherReceive voucherReceive) {
		String receiveCode = voucherReceive.getVoucherReceiveCode();
		VoucherAllocation voucherAllocation = voucherAllocationService
				.getAllocationByCode(voucherReceive.getSourceDataCode());
		if (voucherAllocation == null) {
			log.info("Receive code :{}, voucherAllocation empty, source data code: {}", receiveCode,
					voucherReceive.getSourceDataCode());
			return Collections.emptyList();
		}
		VoucherRequest voucherRequest = voucherRequestService
				.queryByVoucherRequestCode(voucherAllocation.getSourceDataCode());
		if (voucherRequest == null) {
			log.info("Receive code :{}, voucherRequest empty,source data code: {}", receiveCode,
					voucherAllocation.getSourceDataCode());
			return Collections.emptyList();
		}
		String createUser = voucherRequest.getCreateUser();
		String createEmail = userAccountService.getUserEmail(createUser);
		List<String> emails = new ArrayList<>();
		emails.add(createEmail);
		if (StringUtil.isNotEmpty(createEmail)) {
			emails.addAll(Arrays.asList(voucherRequest.getEmail().split(",")));
		}

		String outletEmail = null;
		Outlet outlet = outletService.queryByOutletCode(voucherRequest.getVoucherOwnerCode());
		if (outlet != null) {
			outletEmail = outlet.getEmail();
			if (VoucherReceiveSourceTypeEnum.RETURN.getCode().equals(voucherReceive.getSourceType())
					|| VoucherReceiveSourceTypeEnum.TRANSFER.getCode().equals(voucherReceive.getSourceType())) {
				emails.addAll(Arrays.asList(outletEmail.split(",")));
			}
		}
		return emails;
	}
	
	private int updateVoucherStatus(String receiveStartNo, String receiveEndNo, String sourceType) {
		UpdateVoucherStatusRequest request = new UpdateVoucherStatusRequest();

		if (VoucherReceiveSourceTypeEnum.CUSTOMER_ORDER.getCode().equals(sourceType)) {
			request.setStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
			request.setOldStatus(VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
		}else if (VoucherReceiveSourceTypeEnum.GENERATE.getCode().equals(sourceType)) {
			request.setOldStatus(VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
		}
		request.setCirculationStatus(VoucherCirculationStatusEnum.VOUCHER_RECEIVED.getCode());
		request.setOldCirculationStatus(VoucherCirculationStatusEnum.VOUCHER_TO_BE_RECEIVED.getCode());
		request.setOldVoucherStatus(GvcoreConstants.STATUS_ENABLE);
		List<String> voucherCodeList = new ArrayList<>();
//		request.setVoucherStartNo(receiveStartNo);
//		request.setVoucherEndNo(receiveEndNo);
		AtomicInteger count = new AtomicInteger();
		try {
			long startNo = Long.parseLong(receiveStartNo);
			long endNo = Long.parseLong(receiveEndNo);
			int groupSize = 10000;

			for (long i = startNo; i <= endNo; i++) {
				voucherCodeList.add(Long.toString(i));
				if (voucherCodeList.size() == groupSize) {

					Map<Integer, List<String>> splitByTable = splitVouchersByCode(voucherCodeList);
					splitByTable.forEach((table,voucherCodes)->{
						request.setVoucherCodeList(voucherCodes);
						int returnCount = voucherService.updateVoucherStatusByTable(request,table);
						count.addAndGet(returnCount);
					});

					voucherCodeList.clear();
				}
			}
			if (!voucherCodeList.isEmpty()) {
				Map<Integer, List<String>> splitByTable = splitVouchersByCode(voucherCodeList);
				splitByTable.forEach((table,voucherCodes)->{
					request.setVoucherCodeList(voucherCodes);
					int returnCount = voucherService.updateVoucherStatusByTable(request,table);
					count.addAndGet(returnCount);
				});
			}
		} catch (Exception e) {
			log.error("update voucher status to received fail:{}", JSON.toJSONString(e.getMessage()));
		}
		return count.get();
	}



	public static Map<Integer, List<String>> splitVouchersByCode(List<String> codeCountList) {
		Map<Integer, List<String>> resultMap = new HashMap<>();

		for (String voucher : codeCountList) {

			int bucket = (int) (Long.valueOf(voucher.replaceAll("[a-zA-Z]", ""))%64);

			resultMap.computeIfAbsent(bucket, k -> new ArrayList<>()).add(voucher);
		}

		return resultMap;
	}



	private void addTransactionData(CustomerOrder customerOrder,List<Voucher> voucherList,String approvalCode){
		ArrayList<TransactionData> transactionDatas = new ArrayList<>();

		voucherList.forEach(x->{
			//插入transactionData
			TransactionData transactionData = new TransactionData();
			transactionData.setTransactionId(customerOrder.getCustomerOrderCode());
			transactionData.setTransactionType(TransactionTypeEnum.GIFT_CARD_ACTIVATE.getCode());
			transactionData.setMerchantCode(outletService.getOutlet(GetOutletRequest.builder().outletCode(customerOrder.getOutletCode()).build()).getMerchantCode());
			transactionData.setIssuerCode(customerOrder.getIssuerCode());
			transactionData.setBatchId("");
			transactionData.setBillNumber("");
			transactionData.setOutletCode(customerOrder.getOutletCode());
			transactionData.setCpgCode(x.getCpgCode());
			transactionData.setTransactionDate(new Date());
			transactionData.setVoucherCode(x.getVoucherCode());
			transactionData.setVoucherCodeNum(Long.valueOf(x.getVoucherCode().replaceAll("[a-zA-Z]","")));
			transactionData.setInitiatedBy("");
			transactionData.setPosCode("");
			transactionData.setBatchCode("");
			transactionData.setLoginSource("");
			transactionData.setDenomination(x.getDenomination());
			transactionData.setPaidAmount(new BigDecimal("0"));
			transactionData.setPaymentMethod("");
			transactionData.setDiscountAmount(new BigDecimal("0"));
			transactionData.setActualOutlet("");
			transactionData.setCardEntryMode("GV POS");
			transactionData.setMopCode(x.getMopCode());
			transactionData.setForwardingEntityId("");
			transactionData.setResponseMessage("Transaction successful.");
			transactionData.setTransactionMode("");
			transactionData.setCorporateName("");
			transactionData.setDepartmentDivisionBranch("");
			transactionData.setCustomerSalutation("");
			transactionData.setCustomerFirstName("");
			transactionData.setCustomerLastName("");
			transactionData.setMobile("");
			transactionData.setEmail("");
			transactionData.setInvoiceNumber(customerOrder.getInvoiceNo());
			transactionData.setOtherInputParameter("{}");
			transactionData.setCustomerType("");
			transactionData.setSuccessOrFailure("0");
			transactionData.setPurchaseOrderNo(customerOrder.getPurchaseOrderNo());
			transactionData.setVoucherEffectiveDate(x.getVoucherEffectiveDate());
			transactionData.setCreateUser("");
			transactionData.setCreateTime(new Date());
			transactionData.setUpdateUser("");
			transactionData.setUpdateTime(new Date());
			transactionData.setCustomerCode(customerOrder.getCustomerCode());
			transactionData.setCardEntryMode("Swiped");
			transactionData.setApproveCode(approvalCode);
			transactionData.setReferenceNumber(gvCodeHelper.generateReferenceNumber());
			transactionDatas.add(transactionData);
		});
		transactionDataService.insertList(transactionDatas);
	}

	private boolean updateVoucherReceivedNum(String receiveCode, Integer receivedNum, String createUser) {
		if (receivedNum == 0) {
			return false;
		}
		voucherReceiveMapper.updateVoucherReceivedNum(receiveCode, receivedNum);
		VoucherReceive voucherReceive = selectOneVoucherReceive(receiveCode);
		if (voucherReceive == null || voucherReceive.getReceivedNum().intValue() != voucherReceive.getVoucherNum().intValue()) {
			return false;
		}
		voucherReceive.setStatus(VoucherReceiveStatusEnum.COMPLETED.getCode());
		voucherReceiveMapper.updateByPrimaryKey(voucherReceive);

		if (VoucherReceiveSourceTypeEnum.GENERATE.getCode().equals(voucherReceive.getSourceType())) {
			UpdateVoucherBatchStatusRequest param = new UpdateVoucherBatchStatusRequest();
			param.setStatus(VoucherBatchServiceImpl.COMPLETED);
			param.setVoucherBatchCode(voucherReceive.getSourceDataCode());
			voucherBatchService.updateVoucherBatchStatus(param);
		} else {
			voucherAllocationService.completed(voucherReceive.getSourceDataCode(), createUser);
		}
		return true;
	}

	private VoucherReceiveBatchResponse getReceiveResponseIfMatch(List<VoucherReceiveBatchResponse> receiveBatchList, String receiveStartNo,
			String receiveEndNo) {
		for (VoucherReceiveBatchResponse voucherReceiveBatchResponse : receiveBatchList) {
			Long voucherStartNo = Long.parseLong(voucherReceiveBatchResponse.getVoucherStartNo());
			Long voucherEndNo = Long.parseLong(voucherReceiveBatchResponse.getVoucherEndNo());
			if (voucherStartNo <= Long.parseLong(receiveStartNo) && voucherEndNo >= Long.parseLong(receiveEndNo)) {
				return voucherReceiveBatchResponse;
			}
		}
		log.error("receiveStartNo:{},receiveEndNo:{} not match", receiveStartNo, receiveEndNo);
		throw new GTechBaseException(ResultErrorCodeEnum.RECEIVE_NOT_MATCH_ERROR.code(),
				ResultErrorCodeEnum.RECEIVE_NOT_MATCH_ERROR.desc() + " : " + receiveStartNo + "-" + receiveEndNo);
	}

	private List<VoucherReceiveResponse> makeReceiveListResult(List<VoucherReceive> receiveList) {

		List<VoucherReceiveResponse> list = Collections.emptyList();
		if (CollectionUtils.isEmpty(receiveList)) {
			return list;
		}
		list = BeanCopyUtils.jsonCopyList(receiveList, VoucherReceiveResponse.class);

		// 查询request源
		List<String> allocationCode = list.stream()
				.filter(x -> x.getSourceType().equals(VoucherReceiveSourceTypeEnum.RETURN.getCode())
						|| x.getSourceType().equals(VoucherReceiveSourceTypeEnum.TRANSFER.getCode())
						|| x.getSourceType().equals(VoucherReceiveSourceTypeEnum.SALES.getCode()))
				.map(VoucherReceiveResponse::getSourceDataCode).collect(Collectors.toList());
		List<VoucherAllocation> voucherAllocations = voucherAllocationService.queryByVoucherAllocationCodeList(allocationCode);
		Map<String, VoucherAllocation> allocationMap = voucherAllocations.stream()
				.collect(Collectors.toMap(VoucherAllocation::getVoucherAllocationCode,x->x));
		// 查询GENERATE源
		List<String> generateCode = list.stream().filter(x -> x.getSourceType().equals(VoucherReceiveSourceTypeEnum.GENERATE.getCode()))
				.map(VoucherReceiveResponse::getSourceDataCode).collect(Collectors.toList());
		List<VoucherBatch> batchList = voucherBatchService.queryVoucherBatchByCodeList(generateCode);
		Map<String, String> generateMap = batchList.stream().collect(Collectors.toMap(VoucherBatch::getVoucherBatchCode, VoucherBatch::getPurchaseOrderNo));
		for (VoucherReceiveResponse voucherReceiveResponse : list) {
			VoucherAllocation getRes = allocationMap.get(voucherReceiveResponse.getSourceDataCode());
			if (null != getRes) {
				voucherReceiveResponse.setSourceDataCode(getRes.getSourceDataCode());
			}
			voucherReceiveResponse.setPurchaseOrderNo(generateMap.get(voucherReceiveResponse.getSourceDataCode()));
		}

		// query receiveBatch
		List<String> receiveCodeList = list.stream().map(VoucherReceiveResponse::getVoucherReceiveCode).collect(Collectors.toList());
		QueryVoucherReceiveBatchRequest request = new QueryVoucherReceiveBatchRequest();
		request.setReceiveCodeList(receiveCodeList);
		List<VoucherReceiveBatchResponse> receiveBatchList = voucherReceiveBatchService.queryReceiveBatchList(request);
		if (CollectionUtils.isEmpty(receiveBatchList)) {
			return list;
		}
		Map<String, List<VoucherReceiveBatchResponse>> receivBatchMap = receiveBatchList.stream()
				.collect(Collectors.groupingBy(VoucherReceiveBatchResponse::getVoucherReceiveCode));

		for (VoucherReceiveResponse voucherReceiveResponse : list) {

			List<VoucherReceiveBatchResponse> batchResponses = receivBatchMap.get(voucherReceiveResponse.getVoucherReceiveCode());

			voucherReceiveResponse.setReceiveBatchList(batchResponses);

			BigDecimal totalAmount = BigDecimal.ZERO;

			if (CollectionUtils.isEmpty(batchResponses)) batchResponses = new ArrayList<>();

			//cpg信息
			for (VoucherReceiveBatchResponse batchRespons : batchResponses) {
				String cpgName = batchRespons.getCpgName() + "/" + batchRespons.getDenomination().setScale(0).divide(new BigDecimal(1000)) + "K";
				if (StringUtil.isNotEmpty(voucherReceiveResponse.getCpgName())) {
					voucherReceiveResponse.setCpgName(voucherReceiveResponse.getCpgName() + "," + cpgName);
				} else {
					voucherReceiveResponse.setCpgName(cpgName);
				}
				totalAmount = totalAmount.add(batchRespons.getDenomination().multiply(BigDecimal.valueOf(batchRespons.getVoucherNum())));
			}
			voucherReceiveResponse.setTotalAmount(totalAmount);

		}
		return list;
	}


	/*private void extracted(List<VoucherReceiveResponse> list, List<String> allocationCode) {
		List<VoucherAllocation> voucherAllocations = voucherAllocationService.queryByVoucherAllocationCodeList(allocationCode);
		Map<String, VoucherAllocation> allocationMap = voucherAllocations.stream()
				.collect(Collectors.toMap(VoucherAllocation::getVoucherAllocationCode,x->x));
		for (VoucherReceiveResponse voucherReceiveResponse : list) {
			VoucherAllocation getRes = allocationMap.get(voucherReceiveResponse.getSourceDataCode());
			if (null != getRes){
				voucherReceiveResponse.setSourceDataCode(getRes.getSourceDataCode());
			}
		}
	}

	private void updateSourceToRequestCode(List<VoucherReceiveResponse> list, List<VoucherAllocation> voucherAllocations) {
		Map<String, VoucherAllocation> allocationMap = voucherAllocations.stream()
				.collect(Collectors.toMap(VoucherAllocation::getVoucherAllocationCode,x->x));
		for (VoucherReceiveResponse voucherReceiveResponse : list) {
			VoucherAllocation getRes = allocationMap.get(voucherReceiveResponse.getSourceDataCode());
			if (null != getRes){
				voucherReceiveResponse.setSourceDataCode(getRes.getSourceDataCode());
			}
		}
	}*/

	private VoucherReceive selectOneVoucherReceive(String receiveCode) {

		try {
			VoucherReceive voucherReceive = new VoucherReceive();
			voucherReceive.setVoucherReceiveCode(receiveCode);
			return voucherReceiveMapper.selectOne(voucherReceive);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return null;
		}
	}

	@Override
	//@Transactional
	public Integer customerOrderReceive(String customerOrderCode, String updateUser) {
		VoucherReceive voucherReceive = selectOneByCustomerOrder(customerOrderCode, VoucherReceiveStatusEnum.PROCESSING.getCode());
        if (voucherReceive == null) {
			log.error("CustomerOrderCode= {} voucherReceive data not found", customerOrderCode);
        	throw new GTechBaseException(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(),
                    "CustomerOrderCode= {0} voucherReceive data not found", customerOrderCode);
        }
        voucherReceive.setUpdateUser(updateUser);
       return receiveVoucher(voucherReceive, null);
	}

	private VoucherReceive selectOneByCustomerOrder(String customerOrderCode, Integer status) {
		
		VoucherAllocation voucherAllocation = voucherAllocationService.getAllocationBySourceDataCode(customerOrderCode, VoucherAllocationBusinessTypeEnum.CUSTOMERORDER.code());
		if (voucherAllocation == null) {
			log.error("CustomerOrderCode= {} voucherAllocation data not found", customerOrderCode);
			return null;
		}
		VoucherReceive voucherReceive  = new VoucherReceive();
		voucherReceive.setSourceDataCode(voucherAllocation.getVoucherAllocationCode());
		voucherReceive.setSourceType(VoucherReceiveSourceTypeEnum.CUSTOMER_ORDER.getCode());
		voucherReceive.setStatus(status);
		return voucherReceiveMapper.selectOne(voucherReceive);
	}


}
