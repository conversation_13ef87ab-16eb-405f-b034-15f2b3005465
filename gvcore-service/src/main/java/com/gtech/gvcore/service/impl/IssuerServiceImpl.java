package com.gtech.gvcore.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.issuer.CreateIssuerRequest;
import com.gtech.gvcore.common.request.issuer.DeleteIssuerRequest;
import com.gtech.gvcore.common.request.issuer.GetIssuerRequest;
import com.gtech.gvcore.common.request.issuer.QueryIssuerRequest;
import com.gtech.gvcore.common.request.issuer.UpdateIssuerRequest;
import com.gtech.gvcore.common.request.issuer.UpdateIssuerStatusRequest;
import com.gtech.gvcore.common.response.issuer.IssuerResponse;
import com.gtech.gvcore.dao.mapper.IssuerMapper;
import com.gtech.gvcore.dao.model.Company;
import com.gtech.gvcore.dao.model.Issuer;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.CompanyService;
import com.gtech.gvcore.service.IssuerService;
import com.gtech.gvcore.service.MerchantService;
import com.gtech.gvcore.service.OutletService;

import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @Date 2022/2/16 16:24
 */
@Service
public class IssuerServiceImpl implements IssuerService {

    @Autowired
    private IssuerMapper issuerMapper;

    @Autowired
    private GvCodeHelper codeHelper;

	@Autowired
	private CompanyService companyService;

	@Autowired
	private MerchantService merchantService;

	@Autowired
	private OutletService outletService;

    @Override
    public Result<String> createIssuer(CreateIssuerRequest param) {
        Issuer entity = BeanCopyUtils.jsonCopyBean(param, Issuer.class);
        entity.setIssuerCode(codeHelper.generateIssuerCode());
        entity.setCreateTime(new Date());
        //默认开启
        entity.setStatus(GvcoreConstants.STATUS_ENABLE);
        try {
            issuerMapper.insertSelective(entity);
        } catch (DuplicateKeyException e) {
            return Result.failed(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                    ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
        }
        return Result.ok(entity.getIssuerCode());
    }

    @Override
    public Result<Void> updateIssuer(UpdateIssuerRequest param) {
        Issuer entity = BeanCopyUtils.jsonCopyBean(param, Issuer.class);
        entity.setUpdateTime(new Date());

        Example example = new Example(Issuer.class);
        example.createCriteria()
                .andEqualTo(Issuer.C_ISSUER_CODE, param.getIssuerCode());

        try {
            issuerMapper.updateByConditionSelective(entity, example);
        } catch (DuplicateKeyException e) {
            return Result.failed(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                    ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
        }
        return Result.ok();
    }

    @Override
    public Result<Void> deleteIssuer(DeleteIssuerRequest param) {
        Example example = new Example(Issuer.class);
        example.createCriteria()
                .andEqualTo(Issuer.C_ISSUER_CODE, param.getIssuerCode());
        issuerMapper.deleteByCondition(example);

        return Result.ok();
    }

    @Override
    public PageResult<IssuerResponse> queryIssuerList(QueryIssuerRequest param) {

        PageHelper.startPage(param.getPageNum(), param.getPageSize());

        Example example = new Example(Issuer.class);
        Example.Criteria criteria = example.createCriteria();

        if (StringUtil.isNotEmpty(param.getStatus())) {
            criteria.andEqualTo(Issuer.C_STATUS, param.getStatus());
        }

        if (StringUtil.isNotEmpty(param.getIssuerCode())) {
            criteria.andEqualTo(Issuer.C_ISSUER_CODE, param.getIssuerCode());
        }

        if (CollectionUtils.isNotEmpty(param.getIssuerCodes())) {
            criteria.andIn(Issuer.C_ISSUER_CODE, param.getIssuerCodes());
        }

        if (StringUtil.isNotEmpty(param.getIssuerName())) {
            criteria.andLike(Issuer.C_ISSUER_NAME, "%" + param.getIssuerName() + "%");
        }
        //更新时间倒序
        example.orderBy(Company.C_CREATE_TIME).desc();

        List<Issuer> gvIssuerEntities = issuerMapper.selectByCondition(example);
        PageInfo<Issuer> info = PageInfo.of(gvIssuerEntities);

        return new PageResult<>(BeanCopyUtils.jsonCopyList(info.getList(), IssuerResponse.class), info.getTotal());
    }

    @Override
    public IssuerResponse getIssuer(GetIssuerRequest param) {
        Issuer entity = BeanCopyUtils.jsonCopyBean(param, Issuer.class);
        Issuer issuer = issuerMapper.selectOne(entity);

        return BeanCopyUtils.jsonCopyBean(issuer, IssuerResponse.class);
    }

    @Override
    public Result<Void> updateIssuerStatus(UpdateIssuerStatusRequest param) {
        Issuer entity = BeanCopyUtils.jsonCopyBean(param, Issuer.class);
        entity.setUpdateTime(new Date());

        Example example = new Example(Issuer.class);
        example.createCriteria()
                .andEqualTo(Issuer.C_ISSUER_CODE, param.getIssuerCode());


        issuerMapper.updateByConditionSelective(entity, example);

        return Result.ok();
    }

    @Override
    public Issuer queryByIssuerCode(String issuerCode) {

        if (StringUtils.isBlank(issuerCode)) {
            return null;
        }

        Issuer issuer = new Issuer();
        issuer.setIssuerCode(issuerCode);
        return issuerMapper.selectOne(issuer);
    }

	@Override
	public List<Issuer> queryIssuerByCodeList(List<String> issuerCodeList) {
		if (CollectionUtils.isEmpty(issuerCodeList)) {
			return Collections.emptyList();
		}
		Example example = new Example(Issuer.class);
		example.createCriteria().andIn(Issuer.C_ISSUER_CODE, issuerCodeList);
		return issuerMapper.selectByCondition(example);
	}

}
