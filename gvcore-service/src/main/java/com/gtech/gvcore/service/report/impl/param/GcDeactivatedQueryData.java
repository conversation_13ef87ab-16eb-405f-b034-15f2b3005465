package com.gtech.gvcore.service.report.impl.param;

import com.gtech.gvcore.service.report.ReportQueryParam;
import com.gtech.gvcore.service.report.extend.row.TransactionDataPageParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * @ClassName DeactivatedQueryData
 * @Description
 * <AUTHOR>
 * @Date 2023/5/10 10:36
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GcDeactivatedQueryData extends TransactionDataPageParam implements ReportQueryParam {

    //transaction date start 交易时间 开始
    private Date transactionDateStart;

    //transaction date end 交易时间 结束
    private Date transactionDateEnd;

    //merchant code list
    private List<String> merchantCodeList;

    //cpg code list
    private List<String> cpgCodeList;
    private List<String> issuerCodeList;
    private List<String> voucherCode;
    private List<String> outletCodeList;
    private List<String> customerCodes;
}
