package com.gtech.gvcore.service.report.extend.joindate.impl;

import com.gtech.gvcore.dao.mapper.PrinterMapper;
import com.gtech.gvcore.dao.model.Printer;
import com.gtech.gvcore.service.report.extend.joindate.QuerySupport;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

/**
 * @ClassName PrinterQueryImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/2/13 19:23
 * @Version V1.0
 **/
@Component
public class PrinterQueryImpl implements QuerySupport<Printer> {

    public static final Printer EMPTY = new Printer();

    @Autowired private PrinterMapper printerMapper;

    @Override
    public List<Printer> queryByCode(List<String> codes, String... selectFields) {

        if (CollectionUtils.isEmpty(codes)) return Collections.emptyList();

        Example example = new Example(Printer.class);
        example.createCriteria().andIn(Printer.C_PRINTER_CODE, codes);

        if (ArrayUtils.isNotEmpty(selectFields)) example.selectProperties(selectFields);

        List<Printer> printerList = printerMapper.selectByCondition(example);
        if (CollectionUtils.isEmpty(printerList)) return Collections.emptyList();

        return printerList;
    }

    @Override
    public Function<Printer, String> codeMapper() {
        return Printer::getPrinterCode;
    }

    @Override
    public Printer emptyObject() {
        return EMPTY;
    }

    @Override
    public Class<Printer> supportType() {
        return Printer.class;
    }


}
