package com.gtech.gvcore.service.report.impl;

import com.gtech.basic.masterdata.web.entity.MasterDataDistrictEntity;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.CustomerOrderStatusEnum;
import com.gtech.gvcore.common.enums.ProductCategoryDiscountTypeEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.common.utils.GvConvertUtils;
import com.gtech.gvcore.dao.mapper.CustomerOrderDetailsMapper;
import com.gtech.gvcore.dao.mapper.CustomerOrderMapper;
import com.gtech.gvcore.dao.mapper.TransactionDataMapper;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.CustomerOrderDetails;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.helper.GvPageHelper;
import com.gtech.gvcore.service.VoucherAllocationService;
import com.gtech.gvcore.service.VoucherService;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.PollReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.ReportParamConvertHelper;
import com.gtech.gvcore.service.report.extend.ReportProportionDataFunction;
import com.gtech.gvcore.service.report.extend.context.ReportContextHelper;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.BulkOrderDetailedBean;
import com.gtech.gvcore.service.report.impl.bo.BulkOrderReportBasicDataContext;
import com.gtech.gvcore.service.report.impl.param.BulkOrderDetailsQueryData;
import com.gtech.gvcore.service.report.impl.support.bulk.BulkOrderReportBasicDataContextFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:54
 * @Description:
 */
@Service
public class BulkOrderDetailedImpl extends ReportSupport
        implements BusinessReport<BulkOrderDetailsQueryData, BulkOrderDetailedBean>, PollReport, ReportProportionDataFunction {

    @Autowired private TransactionDataMapper transactionDataMapper;
    @Autowired private VoucherAllocationService voucherAllocationService;
    @Autowired private BulkOrderReportBasicDataContextFactory bulkOrderReportBasicDataContextFactory;
    @Autowired private CustomerOrderMapper customerOrderMapper;
    @Autowired private CustomerOrderDetailsMapper customerOrderDetailsMapper;
    @Autowired private VoucherService voucherService;

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.BULK_ORDER_DETAILED_REPORT;
    }

    @Override
    public BulkOrderDetailsQueryData builderQueryParam(final CreateReportRequest reportParam) {

        ReportParamConvertHelper.convertQueryDateMerchantCodeToOutletCode(reportParam);

        final BulkOrderDetailsQueryData queryData = new BulkOrderDetailsQueryData();
        queryData.setTransactionDateStart(reportParam.getTransactionDateStart());
        queryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());
        queryData.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        queryData.setOutletCodeList(reportParam.getOutletCodes());
        queryData.setCpgCodeList(reportParam.getCpgCodes());
        if (null != reportParam.getVoucherCodeNumStart()) {
            queryData.setVoucherNumberStart(String.valueOf(reportParam.getVoucherCodeNumStart()));
        }
        if (null != reportParam.getVoucherCodeNumEnd()) {
            queryData.setVoucherNumberEnd(String.valueOf(reportParam.getVoucherCodeNumEnd()));
        }
        queryData.setInvoiceNo(reportParam.getInvoiceNo());
        queryData.setCustomerType(reportParam.getCustomerType());
        queryData.setCustomerCodeList(reportParam.getCustomerCodes());
        queryData.setPurchaseOrderNumber(reportParam.getPurchaseOrderNo());
        queryData.setOrderStatusList(reportParam.getOrderStatuses());

        return queryData;
    }

    @Override
    public List<BulkOrderDetailedBean> getExportData(final BulkOrderDetailsQueryData queryData) {

        final List<CustomerOrderDetails> customerOrderDetailsList = this.queryCustomerOrderDetailReport(queryData);

        if (CollectionUtils.isEmpty(customerOrderDetailsList)) return Collections.emptyList();

        final Map<String, CustomerOrder> orderCodeOrderMap = super.getMapByCode(customerOrderDetailsList, CustomerOrderDetails::getCustomerOrderCode, CustomerOrder.class);
        final BulkOrderReportBasicDataContext basicDataContext = this.bulkOrderReportBasicDataContextFactory.buildBasicDataContext(orderCodeOrderMap.values());
        final Map<String, Cpg> cpgCodeCpgMap = super.getMapByCode(customerOrderDetailsList, CustomerOrderDetails::getCpgCode, Cpg.class);
        final JoinDataMap<MasterDataDistrictEntity> dataDistrictMap  = super.getMapByCode(basicDataContext.getOutletMap().values(), Outlet::getStateCode, MasterDataDistrictEntity.class);

        return customerOrderDetailsList.stream().map(detail -> this.detail2BulkOrderDetailed(detail, orderCodeOrderMap, cpgCodeCpgMap, dataDistrictMap, basicDataContext)).collect(Collectors.toList());
    }

    private BulkOrderDetailedBean detail2BulkOrderDetailed(final CustomerOrderDetails detail,
                                                           final Map<String, CustomerOrder> orderCodeOrderMap,
                                                           final Map<String, Cpg> cpgCodeCpgMap,
                                                           final JoinDataMap<MasterDataDistrictEntity> dataDistrictMap,
                                                           final BulkOrderReportBasicDataContext basicDataContext) {

        final BulkOrderDetailedBean detailedBean = new BulkOrderDetailedBean();

        final CustomerOrder order = orderCodeOrderMap.get(detail.getCustomerOrderCode());

        Merchant merchant = null;
        final Outlet outlet = basicDataContext.getOutlet(order.getOutletCode());

        if (null != outlet) {
            detailedBean.setMerchantOutlet(outlet.getOutletName());
            detailedBean.setMerchantOutletCode(outlet.getBusinessOutletCode());

            detailedBean.setRegion(dataDistrictMap.findValue(outlet.getStateCode()).getDistrictName());
            merchant = basicDataContext.getMerchant(outlet.getMerchantCode());
        }

        if (null != merchant) {
            detailedBean.setMerchant(merchant.getMerchantName());
        }

        detailedBean.setCustomerName(basicDataContext.getCustomerName(order.getCustomerCode()));
        detailedBean.setCompanyName(basicDataContext.getCompanyName(order.getCustomerCode()));
        detailedBean.setVoucherType(basicDataContext.getVoucherTypeText(order.getMopCode()));
        detailedBean.setPoNumber(order.getPurchaseOrderNo());
        detailedBean.setPoDate(DateUtil.format(detail.getCreateTime(), DateUtil.FORMAT_YYYYMMDDHHMISS));
        detailedBean.setStatus(basicDataContext.getOrderStatusText(order.getStatus()));
        detailedBean.setPoValue(super.toAmount(detail.getVoucherAmount()));
        detailedBean.setDiscountType(basicDataContext.getDiscountTypeText(order.getDiscountType()));
        detailedBean.setDiscountAmount(super.toAmount(detail.getDiscount()));
        detailedBean.setInvoiceNumber(order.getInvoiceNo());
        detailedBean.setTotalNetAmount(super.toAmount(detail.getVoucherAmount().subtract(detail.getDiscount()).setScale(0, RoundingMode.UP)));
        if (ProductCategoryDiscountTypeEnum.PERCENTAGE.equalsCode(order.getDiscountType())) {
            detailedBean.setDiscount(order.getDiscount().setScale(1)+ PERCENT);
        } else if (ProductCategoryDiscountTypeEnum.AMOUNT.equalsCode(order.getDiscountType())) {
            // AMOUNT 类型折扣值固定为 0
            detailedBean.setDiscount("0" + PERCENT);
        }else{
            detailedBean.setDiscount(EMPTY_PROPORTION);
        }
        final Cpg cpg = cpgCodeCpgMap.get(detail.getCpgCode());
        detailedBean.setOrderItem(null == cpg ? null : cpg.getCpgName());
        detailedBean.setEmailRecipient(order.getContactEmail());
        detailedBean.setPaymentMode(basicDataContext.getPaymentMode(order.getMeansOfPaymentCode()));
        detailedBean.setSubmitAccount(basicDataContext.getSubmitAccount(order.getCreateUser()));
        detailedBean.setTotalQuantity(Integer.toString(detail.getVoucherNum()));
        detailedBean.setSuccessCount(detailedBean.getTotalQuantity());
        detailedBean.setFailedCount("0");


        // 统计激活 卡券最新的一条跟 取消激活 or 激活 相关的交易记录
        List<TransactionData> activateAndCancelTransaction = (List<TransactionData>) ReportContextHelper.findContext()
                .getCacheIfAbsent("transactionDataListByCustomerOrder" + order.getCustomerOrderCode(), List.class,
                        () -> selectTransactionByCustomerOrderCode(order.getCustomerOrderCode()).stream()
                                .filter(transactionData -> TransactionTypeEnum.GIFT_CARD_ACTIVATE.equalsCode(transactionData.getTransactionType()) || TransactionTypeEnum.GIFT_CARD_CANCEL_ACTIVATE.equalsCode(transactionData.getTransactionType()))
                                .collect(Collectors.toMap(TransactionData::getVoucherCode, Function.identity(), (v1, v2) -> v1.getCreateTime().compareTo(v2.getCreateTime()) > 0 ? v1 : v2))
                                .values().stream().collect(Collectors.toList()));

        if (CustomerOrderStatusEnum.RELEASE.beforeOrEquals(CustomerOrderStatusEnum.valueOfCode(order.getStatus()))) {
            /**
             * 统计激活
             * 先查询订单所有的券,再查询券取消的数据
             */
            List<TransactionData> collect = activateAndCancelTransaction.stream()
                    .filter(transactionData -> TransactionTypeEnum.GIFT_CARD_ACTIVATE.equalsCode(transactionData.getTransactionType()))
                    .collect(Collectors.toList());
            List<TransactionData> vouchers = collect.stream().filter(voucher -> voucher.getCpgCode().equals(detail.getCpgCode())).collect(Collectors.toList());

            BigDecimal activationAmount = vouchers.stream().map(TransactionData::getDenomination).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(1);
            detailedBean.setActivationAmount(super.toAmount(activationAmount));

            int size = vouchers.size();
            detailedBean.setActivationCount(size == 0?"0":String.valueOf(size));

        } else {
            detailedBean.setActivationAmount("0");
            detailedBean.setActivationCount("0");
        }
        /**
         * 统计取消激活
         * 先查询订单所有的券,再查询券取消的数据
         */
        List<TransactionData> collect = activateAndCancelTransaction.stream()
                .filter(transactionData -> TransactionTypeEnum.GIFT_CARD_CANCEL_ACTIVATE.equalsCode(transactionData.getTransactionType()))
                .collect(Collectors.toList());
        List<TransactionData> vouchers = collect.stream().filter(voucher -> voucher.getCpgCode().equals(detail.getCpgCode())).collect(Collectors.toList());


        BigDecimal cancelActivationAmount = vouchers.stream().map(TransactionData::getDenomination).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(1);
        detailedBean.setCancelActivation(super.toAmount(cancelActivationAmount));

        int size = vouchers.size();
        detailedBean.setCancelActivationCount(size == 0?"0":String.valueOf(size));

        detailedBean.setSkippedCount("0");

        return detailedBean;
    }

    public List<CustomerOrderDetails> queryCustomerOrderDetailReport(final BulkOrderDetailsQueryData queryData) {

        final Example queryCondition = this.judgmentAndBuildCustomerOrderDetailCondition(queryData);
        if (queryCondition == null) {
            return Collections.emptyList();
        }
        return this.customerOrderDetailsMapper.selectByExampleAndRowBounds(queryCondition, GvPageHelper.getRowBounds(queryData));
    }

    /**
     * 判断是否可能存在有效值,如果可能则构建 BulkOrderDetailedReport 报表的查询条件
     *
     * @param queryData
     * @return tk.mybatis.mapper.entity.Example 查询条件,如果条件筛选后发现数据库一定不存在匹配数据,该方法将返回null
     * <AUTHOR>
     * @date 2022/7/13 10:12
     * @since 1.0.0
     */
    private Example judgmentAndBuildCustomerOrderDetailCondition(final BulkOrderDetailsQueryData queryData) {

        // 筛选 cpg_code
        Set<String> cpgCodeList = new HashSet<>();
        Set<String> batchCodeSet = new HashSet<>();
        Set<String> orderCodeSet = new HashSet<>();
        if (StringUtils.isNotBlank(queryData.getVoucherNumberStart())
                || StringUtils.isNotBlank(queryData.getVoucherNumberEnd())
                || null != queryData.getExpiryStatusStart()
                || null != queryData.getExpiryStatusEnd()) {
            // 电子券部分
            final List<Voucher> voucherList = this.voucherService.queryCpgCodeByNumberAndExpiryStatus(queryData.getVoucherNumberStart(), queryData.getVoucherNumberEnd(), queryData.getExpiryStatusStart(), queryData.getExpiryStatusEnd());
            cpgCodeList = voucherList.stream().map(Voucher::getCpgCode).collect(Collectors.toSet());
            batchCodeSet = voucherList.stream().map(Voucher::getVoucherBatchCode).collect(Collectors.toSet());

            // 实体券部分
            orderCodeSet = this.voucherAllocationService.querySourceDataCodeVoucherAllocation(queryData.getVoucherNumberStart(), queryData.getVoucherNumberEnd());

            // 限定了Voucher Number和Expiry Status 作为查询条件时,如果没有搜索到CPG,可以认为整个搜索条件组合无匹配数据
            if ((CollectionUtils.isEmpty(cpgCodeList) || CollectionUtils.isEmpty(batchCodeSet)) && CollectionUtils.isEmpty(orderCodeSet)) return null;
        }
        if (!CollectionUtils.isEmpty(queryData.getCpgCodeList())) {
            cpgCodeList.addAll(queryData.getCpgCodeList());
        }

        // 筛选 order_code ,该集合必定存在值
        final Set<String> customerOrderCodeList = this.queryCustomerOrderCode(queryData, batchCodeSet, orderCodeSet);
        if (CollectionUtils.isEmpty(customerOrderCodeList)) {
            return null;
        }

        // 由于 gv_customer_order_details 中没有足够的信息供查询使用,所以将查询条件转换为 customerOrderCodeList 和 cpgCodeList
        final Example condition = new Example(CustomerOrderDetails.class);
        condition.createCriteria()
                .andIn(CustomerOrderDetails.C_DELETE_STATUS, Arrays.asList(GvcoreConstants.DELETE_STATUS_DISABLE,2))
                .andIn(CustomerOrderDetails.C_CUSTOMER_ORDER_CODE, customerOrderCodeList)
                .andIn(CustomerOrderDetails.C_CPG_CODE, GvConvertUtils.toCollection(cpgCodeList, null));

        return condition;
    }

    private Set<String> queryCustomerOrderCode(final BulkOrderDetailsQueryData queryData, final Set<String> batchCodeSet, final Set<String> orderCodeSet) {

        final Example queryCondition = new Example(CustomerOrder.class);
        final Example.Criteria criteria = queryCondition.createCriteria();
        criteria
                .andGreaterThanOrEqualTo(CustomerOrder.C_CREATE_TIME, queryData.getTransactionDateStart())
                .andLessThanOrEqualTo(CustomerOrder.C_CREATE_TIME, queryData.getTransactionDateEnd())
                .andEqualTo(CustomerOrder.C_INVOICE_NO, GvConvertUtils.toString(queryData.getInvoiceNo(), null))
                .andEqualTo(CustomerOrder.C_CUSTOMER_TYPE, GvConvertUtils.toString(queryData.getCustomerType(), null))
                .andEqualTo(CustomerOrder.C_PURCHASE_ORDER_NO, GvConvertUtils.toString(queryData.getPurchaseOrderNumber(), null))
                .andIn(CustomerOrder.C_ISSUER_CODE, GvConvertUtils.toCollection(queryData.getIssuerCodeList(), null))
                .andIn(CustomerOrder.C_OUTLET_CODE, GvConvertUtils.toCollection(queryData.getOutletCodeList(), null))
                .andIn(CustomerOrder.C_CUSTOMER_CODE, GvConvertUtils.toCollection(queryData.getCustomerCodeList(), null))
                .andIn(CustomerOrder.C_STATUS, GvConvertUtils.toCollection(queryData.getOrderStatusList(), null))
                .andIn(CustomerOrder.C_VOUCHER_BATCH_CODE, GvConvertUtils.toCollection(batchCodeSet, null))
                .andIn(CustomerOrder.C_CUSTOMER_ORDER_CODE, GvConvertUtils.toCollection(orderCodeSet, null));

        // 如果orderCode不为空,允许C_VOUCHER_BATCH_CODE为null
        if (!CollectionUtils.isEmpty(orderCodeSet)) {
            queryCondition.or(queryCondition.createCriteria()
                    .andGreaterThanOrEqualTo(CustomerOrder.C_CREATE_TIME, queryData.getTransactionDateStart())
                    .andLessThanOrEqualTo(CustomerOrder.C_CREATE_TIME, queryData.getTransactionDateEnd())
                    .andEqualTo(CustomerOrder.C_INVOICE_NO, GvConvertUtils.toString(queryData.getInvoiceNo(), null))
                    .andEqualTo(CustomerOrder.C_CUSTOMER_TYPE, GvConvertUtils.toString(queryData.getCustomerType(), null))
                    .andEqualTo(CustomerOrder.C_PURCHASE_ORDER_NO, GvConvertUtils.toString(queryData.getPurchaseOrderNumber(), null))
                    .andIn(CustomerOrder.C_ISSUER_CODE, GvConvertUtils.toCollection(queryData.getIssuerCodeList(), null))
                    .andIn(CustomerOrder.C_OUTLET_CODE, GvConvertUtils.toCollection(queryData.getOutletCodeList(), null))
                    .andIn(CustomerOrder.C_CUSTOMER_CODE, GvConvertUtils.toCollection(queryData.getCustomerCodeList(), null))
                    .andIn(CustomerOrder.C_STATUS, GvConvertUtils.toCollection(queryData.getOrderStatusList(), null))
                    .andIsNull(CustomerOrder.C_VOUCHER_BATCH_CODE)
                    .andIn(CustomerOrder.C_CUSTOMER_ORDER_CODE, GvConvertUtils.toCollection(orderCodeSet, null)));
        }

        return this.customerOrderMapper.selectByCondition(queryCondition)
                .stream()
                .map(CustomerOrder::getCustomerOrderCode)
                .collect(Collectors.toSet());
    }

    public List<TransactionData> selectTransactionByCustomerOrderCode (final String customerOrderCode) {

        Example example = new Example(TransactionData.class);

        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(TransactionData.C_TRANSACTION_ID, customerOrderCode);

        return this.transactionDataMapper.selectByCondition(example);
    }

}
