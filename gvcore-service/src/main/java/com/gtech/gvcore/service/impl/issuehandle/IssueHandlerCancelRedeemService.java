package com.gtech.gvcore.service.impl.issuehandle;

import com.gtech.gvcore.common.enums.IssueHandlingProcessStatusEnum;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.enums.VoucherStatusEnum;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.IssueHandlerBaseService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class IssueHandlerCancelRedeemService extends IssueHandlerValidateService implements IssueHandlerBaseService {

    @Override
    public IssueHandlingTypeEnum getIssueHandlingType() {
        return IssueHandlingTypeEnum.CANCEL_REDEEM;
    }

    @Override
	public List<IssueHandlingDetails> validate(List<IssueHandlingDetails> details, String issuerCode) {

		return check(details, issuerCode);
    }

    @Override
	public List<IssueHandlingDetails> execute(List<IssueHandlingDetails> details, String issuerCode) {

		List<IssueHandlingDetails> check = check(details, issuerCode);

        List<String> successVoucherCodes = check.stream()
                .filter(detail -> IssueHandlingProcessStatusEnum.SUCCESS.code().equals(detail.getProcessStatus()))
                .map(IssueHandlingDetails::getVoucherCode)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(successVoucherCodes)){
            performAction(successVoucherCodes);
        }



        return check;
    }


	private List<IssueHandlingDetails> check(List<IssueHandlingDetails> details, String issuerCode) {

        if (CollectionUtils.isEmpty(details)) {
            return Collections.emptyList();
        }

		checkIfExist(details, getIssueHandlingType(), issuerCode);
        checkInvoiceNumber(details,TransactionTypeEnum.GIFT_CARD_REDEEM);
        checkApprovalCode(details, TransactionTypeEnum.GIFT_CARD_REDEEM);
		checkOutletName(details, issuerCode);
        return details;
    }






    private int performAction(List<String> voucherCodes) {


        Example example = new Example(Voucher.class);
        example.createCriteria().andIn(Voucher.C_VOUCHER_CODE, voucherCodes);
        Voucher voucher = new Voucher();
        voucher.setStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
        voucher.setUsedOutlet(null);
        voucher.setUsedTime(null);

        return voucherMapper.setUsedOrSalesNull(voucher, example,0);
    }



}


