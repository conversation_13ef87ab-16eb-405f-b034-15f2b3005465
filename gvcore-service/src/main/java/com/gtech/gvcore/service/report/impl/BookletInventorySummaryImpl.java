package com.gtech.gvcore.service.report.impl;

import com.gtech.gvcore.common.enums.BookletStatusEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.common.utils.GvDateUtil;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.ReportParamConvertHelper;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import com.gtech.gvcore.service.report.extend.context.ReportContextHelper;
import com.gtech.gvcore.service.report.impl.bean.BookletInventorySummaryBean;
import com.gtech.gvcore.service.report.impl.bo.BookletInventorySummaryBo;
import com.gtech.gvcore.service.report.impl.param.BookletInventoryQueryData;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/2 22:03
 */

@Service
@Slf4j
public class BookletInventorySummaryImpl extends ReportSupport
        implements BusinessReport<BookletInventoryQueryData, BookletInventorySummaryBean>, SingleReport {

    @Override
    public Object getHeadObject(ReportContext context) {

        return new Head().setGenerateDate(GvDateUtil.formatUs(ReportContextHelper.reportBuilderTime(), GvDateUtil.FORMAT_US_DATETIME_DD_MM_YY_HH_MM_A));
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class Head {
        private String generateDate;
    }


    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.BOOKLET_INVENTORY_SUMMARY_REPORT;
    }

    @Override
    public BookletInventoryQueryData builderQueryParam(CreateReportRequest reportParam) {

        ReportParamConvertHelper.convertQueryDateMerchantCodeToOutletCode(reportParam);

        BookletInventoryQueryData bookletInventoryReport = new BookletInventoryQueryData();

        bookletInventoryReport.setIssuerCodes(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        bookletInventoryReport.setOutletCodes(reportParam.getOutletCodes());
        bookletInventoryReport.setCpgCodes(reportParam.getCpgCodes());
        bookletInventoryReport.setOrderStatus(reportParam.getOrderStatuses());
        bookletInventoryReport.setBookletStatus(reportParam.getBookletStatus());
        bookletInventoryReport.setStartVoucherNumber(reportParam.getVoucherCodeNumStart() + "");
        bookletInventoryReport.setEndVoucherNumber(reportParam.getVoucherCodeNumEnd() + "");
        bookletInventoryReport.setStartBookletNo(reportParam.getBookletStart());
        bookletInventoryReport.setEndBookletNo(reportParam.getBookletEnd());
        bookletInventoryReport.setVoucherEffectiveDateStart(reportParam.getVoucherEffectiveDateStart());
        bookletInventoryReport.setVoucherEffectiveDateEnd(reportParam.getVoucherEffectiveDateEnd());

        return bookletInventoryReport;
    }

    @Override
    public List<BookletInventorySummaryBean> getExportData(BookletInventoryQueryData queryData) {

        List<BookletInventorySummaryBo> inventorySummaryList = this.queryData(queryData);
        if (CollectionUtils.isEmpty(inventorySummaryList)) return Collections.emptyList();

        List<BookletInventorySummaryBean> inventorySummaryBeans = inventorySummaryList.stream()
                //.filter(e -> !e.getCardStatus().equals(VoucherStatusEnum.VOUCHER_ACTIVATED.getDesc()))
                .map(inventorySummary -> {

                    final BookletStatusEnum status = BookletStatusEnum.getStatus(inventorySummary.getBookletStatus());

                    final BookletInventorySummaryBean bookletInventorySummaryBean = new BookletInventorySummaryBean();

                    bookletInventorySummaryBean.setIssuer(inventorySummary.getIssuer());
                    bookletInventorySummaryBean.setMerchant(inventorySummary.getMerchant());
                    //bookletInventorySummaryBean.setOutletCode(inventorySummary.getOutletCode());
                    bookletInventorySummaryBean.setMerchantOutletName(inventorySummary.getOutletName());
                    bookletInventorySummaryBean.setVpg(inventorySummary.getVoucherProgramGroup());
                    bookletInventorySummaryBean.setVpgType(inventorySummary.getCardProgramGroupType());
//                    bookletInventorySummaryBean.setCardStatus(StringUtils.toRootUpperCase(inventorySummary.getCardStatus()));
//                    bookletInventorySummaryBean.setCardsCount(inventorySummary.getCardsCount());
                    bookletInventorySummaryBean.setExpiryDate(inventorySummary.getExpiryDate());
                    bookletInventorySummaryBean.setBookletCount(String.valueOf(inventorySummary.getBookletCount()));
                    if (null != status) bookletInventorySummaryBean.setBookletStatus(status.getDesc());

                    return bookletInventorySummaryBean;
                }).collect(Collectors.toList());

        sliceStatistics(inventorySummaryBeans, ReportContextHelper.findContext());

        return inventorySummaryBeans;
    }

    public List<BookletInventorySummaryBo> queryData(BookletInventoryQueryData queryData) {

        List<BookletInventorySummaryBo> summaryResponses = reportBusinessMapper.bookletInventorySummary(queryData);

        List<BookletInventorySummaryBo> resultList = new ArrayList<>();
        summaryResponses.stream()
                .collect(Collectors.groupingBy(x -> ( x.getVoucherProgramGroup() + x.getOutletCode()  + x.getExpiryDate() + x.getBookletStatus() ), Collectors.toList()))
                .forEach((a, b) -> {
                    b.stream().reduce((x, y) ->
                            new BookletInventorySummaryBo(x.getIssuer(),
                                    x.getMerchant(),
                                    x.getOutletCode(),
                                    x.getOutletName(),
                                    x.getVoucherProgramGroup(),
                                    x.getCardProgramGroupType(),
                                    x.getExpiryDate(),
                                    x.getBookletCount() + y.getBookletCount(),
                                    x.getBookletStatus()
                            )
                    ).ifPresent(resultList::add);
                });

        return resultList;
    }

    @Override
    public void dataFinish(ReportContext reportContext) {

        this.finishStatistics(reportContext);
    }

    private void finishStatistics(ReportContext context) {

        Integer total = context.getCache(TOTAL_KEY, Integer.class);
        if (Objects.isNull(total)) return;

        try {
            BookletInventorySummaryBean totalBean = new BookletInventorySummaryBean();
            totalBean.setIssuer(TOTAL_KEY);
            totalBean.setBookletCount(String.valueOf(total));
            context.appendDate(Collections.singletonList(totalBean));
        } catch (Exception e) {
            log.error("统计错误", e);
        }
    }

    private static final String TOTAL_KEY = "total";

    private void sliceStatistics(List<BookletInventorySummaryBean> dataList, ReportContext context) {

        Integer cardsCount = dataList.stream().map(BookletInventorySummaryBean::getBookletCount)
                .mapToInt(Integer::parseInt).sum();

        Integer total = context.getCache(TOTAL_KEY, Integer.class);
        if (Objects.isNull(total)) context.putCache(TOTAL_KEY, cardsCount);
        else {
            try {
                context.putCache(TOTAL_KEY, total + cardsCount);
            } catch (Exception e) {
               log.error("统计错误", e);
                context.putCache(TOTAL_KEY, 0);
            }
        }

    }

}
