package com.gtech.gvcore.service.report.impl.support.life.builder;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.utils.GvDateUtil;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.helper.TableHelper;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.CardLifeCycleTransactionBean;
import com.gtech.gvcore.service.report.impl.param.VoucherLifeCycleQueryData;
import com.gtech.gvcore.service.report.impl.support.life.VoucherLifeCycleAbstract;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @ClassName VoucherLifeCycleTransactionBuilder
 * @Description
 * <AUTHOR>
 * @Date 2023/1/5 20:19
 * @Version V1.0
 **/
@Service
public class VoucherLifeCycleTransactionBuilder extends VoucherLifeCycleAbstract<CardLifeCycleTransactionBean> {

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.CARD_LIFE_CYCLE_TRANSACTION_REPORT;
    }

    @Override
    public Class<?> getExportDataClass() {
        return CardLifeCycleTransactionBean.class;
    }

    @Override
    public String getFillKey() {
        return "transaction";
    }

    @Override
    public List<CardLifeCycleTransactionBean> builder(VoucherLifeCycleQueryData queryData) {

        final List<TransactionData> transactionDataList = super.reportBusinessMapper.selectVoucherLifeCycleTransactionDataByVoucherCodeNum(queryData.getVoucherCodeNumStart(), TableHelper.getTableIndex(queryData.getVoucherCodeNumStart()));

        if (CollectionUtils.isEmpty(transactionDataList)) return Collections.emptyList();

        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(transactionDataList, TransactionData::getCpgCode, Cpg.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(transactionDataList, TransactionData::getOutletCode, Outlet.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(transactionDataList, TransactionData::getMerchantCode, Merchant.class);

        return transactionDataList.stream()
                .filter(transactionData ->
                        Optional.of(transactionData)
                                .map(TransactionData::getTransactionType)
                                .map(TransactionTypeEnum::valueOfCode)
                                .map(e -> e != TransactionTypeEnum.GIFT_CARD_ACTIVATE_VALIDATE && e != TransactionTypeEnum.GIFT_CARD_REDEEM_VALIDATE)
                                .orElse(true)
                ).map(transactionData -> {

                    Cpg cpg = cpgMap.findValue(transactionData.getCpgCode());
                    Outlet outlet = outletMap.findValue(transactionData.getOutletCode());
                    Merchant merchant = merchantMap.findValue(transactionData.getMerchantCode());

                    return new CardLifeCycleTransactionBean()

                            //merchant
                            .setMerchantName(merchant.getMerchantName())

                            //outlet
                            .setOutletCode(outlet.getBusinessOutletCode())
                            .setOutletName(outlet.getOutletName())

                            .setVoucherNumber(transactionData.getVoucherCode())

                            //cpg
                            .setBaseCurrencyOfTheVoucher(cpg.getCurrencyCode())

                            .setExpiryDate(DateUtil.format(transactionData.getVoucherEffectiveDate(), DateUtil.FORMAT_DEFAULT))
                            .setInvoiceNumber(transactionData.getInvoiceNumber())
                            .setTransactionDate(DateUtil.format(transactionData.getTransactionDate(), DateUtil.FORMAT_YYYYMMDDHHMISS))
                            .setTransactionTimeZone(GvDateUtil.TIME_ZONE_DISPLAY_NAME)
                            .setTransactionType(TransactionTypeEnum.getTypeDesc(transactionData.getTransactionType()))
                            .setResponseMessage(transactionData.getResponseMessage())
                            .setExpiryTimeZone(GvDateUtil.TIME_ZONE_DISPLAY_NAME)
                            .setTransactionAmount(super.toAmount(ConvertUtils.toBigDecimal(transactionData.getDenomination(), BigDecimal.ZERO)));
                }).collect(Collectors.toList());
    }


}
