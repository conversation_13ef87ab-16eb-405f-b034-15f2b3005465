package com.gtech.gvcore.service.report.impl.bean.aging;

import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportLabel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName AgeBySbuDetailsOneBean
 * @Description Age By Sbu Details One Bean
 * <AUTHOR>
 * @Date 2022/11/4 15:43
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class AgeBySbuDetailsOneBean {

    @ReportLabel(value = "Details")
    private String customerName;

    @ReportLabel(value = "SBU")
    private String sbuName;

    @ReportAmountValue
    @ReportLabel(value = "1 month")
    private String oneValue;

    @ReportAmountValue
    @ReportLabel(value = "2 month")
    private String twoValue;

    @ReportAmountValue
    @ReportLabel(value = "3 month")
    private String threeValue;

    @ReportAmountValue
    @ReportLabel(value = "4 to 6 month")
    private String fourValue;

    @ReportAmountValue
    @ReportLabel(value = "6 to 12 month")
    private String sixValue;

    @ReportAmountValue
    @ReportLabel(value = "Grand Total")
    private String total;

}
