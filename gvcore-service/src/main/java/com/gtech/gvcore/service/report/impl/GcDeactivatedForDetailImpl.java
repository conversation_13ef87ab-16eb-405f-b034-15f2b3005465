package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.CustomerTypeEnum;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.service.IssueHandlingDetailsService;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.GcDeactivatedForDetailBean;
import com.gtech.gvcore.service.report.impl.bo.DeactivatedBo;
import com.gtech.gvcore.service.report.impl.param.GcDeactivatedQueryData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:57
 * @Description: Gift Card Deactivated (Block) Report Detail Implementation
 */
@Service
public class GcDeactivatedForDetailImpl extends ReportSupport
        implements BusinessReport<GcDeactivatedQueryData, GcDeactivatedForDetailBean>, SingleReport {

    @Autowired
    protected IssueHandlingDetailsService issueHandlingDetailsService;

    @Override
    public GcDeactivatedQueryData builderQueryParam(CreateReportRequest reportParam) {

        GcDeactivatedQueryData queryData = new GcDeactivatedQueryData();

        queryData.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        queryData.setMerchantCodeList(reportParam.getMerchantCodes());
        queryData.setOutletCodeList(reportParam.getOutletCodes());

        queryData.setTransactionDateStart(reportParam.getTransactionDateStart());
        queryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());

        queryData.setCpgCodeList(reportParam.getCpgCodes());
        queryData.setCustomerCodes(reportParam.getCustomerCodes());
        if (StringUtils.isNotBlank(reportParam.getVoucherCode())) {
            queryData.setVoucherCode(Arrays.asList(reportParam.getVoucherCode().split(",")));
        }
        return queryData;
    }

    @Override
    public List<GcDeactivatedForDetailBean> getExportData(GcDeactivatedQueryData queryData) {
        List<DeactivatedBo> list = new ArrayList<>();
        PollPageHelper.pollSelect(gcReportBusinessMapper::selectGcDeactivated, queryData, list::addAll);
        //empty
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        //init map
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(list, DeactivatedBo::getOutletCode, Outlet.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(list, DeactivatedBo::getMerchantCode, Merchant.class);
        final JoinDataMap<GcCpg> cpgMap = super.getMapByCode(list, DeactivatedBo::getCpgCode, GcCpg.class);
        final JoinDataMap<Customer> customerMap = super.getMapByCode(list, DeactivatedBo::getCustomerCode, Customer.class);
        final Map<String, String> voucherMap = issueHandlingDetailsService.queryRemarkByVoucherCodeAndIssueType(list.stream().map(DeactivatedBo::getVoucherCode)
                .distinct().collect(Collectors.toList()), IssueHandlingTypeEnum.GC_BULK_DEACTIVATE);

        //convert result
        return list.stream()
                .map(e -> {
                    String customerName;
                    Customer value = customerMap.findValue(e.getCustomerCode());
                    if (Objects.equals(value.getCustomerType(), CustomerTypeEnum.CORPORATE.code())) {
                        customerName = value.getCompanyName();
                    } else {
                        customerName = value.getCustomerName();
                    }
                    return new GcDeactivatedForDetailBean()
                            .setVoucherAmount(super.toAmount(ConvertUtils.toBigDecimal(e.getDenomination(), BigDecimal.ZERO)))
                            .setVoucherNumber(e.getVoucherCode())
                            .setTransactionDate(DateUtil.format(e.getTransactionDate(), "yyyy-MM-dd HH:mm:ss"))
                            .setInvoiceNumber(e.getInvoiceNumber())
                            .setDeactivatedReason(StringUtils.isNotBlank(voucherMap.get(e.getVoucherCode())) ? voucherMap.get(e.getVoucherCode()) : e.getBlockReason())
                            .setVpg(cpgMap.findValue(e.getCpgCode()).getCpgName())
                            .setMerchantOut(outletMap.findValue(e.getOutletCode()).getOutletName())
                            .setMerchant(merchantMap.findValue(e.getMerchantCode()).getMerchantName())
                            .setCustomerName(customerName);
                })
                .collect(Collectors.toList());
    }

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_BLOCKED_AND_DEACTIVATED_DETAILED;
    }
}
