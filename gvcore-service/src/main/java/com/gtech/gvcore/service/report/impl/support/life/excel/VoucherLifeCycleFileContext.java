package com.gtech.gvcore.service.report.impl.support.life.excel;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.service.report.export.file.FileContext;
import com.gtech.gvcore.service.report.extend.ReportUploadHelper;
import com.gtech.gvcore.service.report.extend.context.ReportContextHelper;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;

public class VoucherLifeCycleFileContext implements FileContext {

    private static final String TEMPLATE_NAME = ReportExportTypeEnum.CARD_LIFE_CYCLE_REPORT.getTemplateName();

    private final ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    private final FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
    private ExcelWriter excelWriter;

    @Override
    public void init() {

        excelWriter = EasyExcelFactory.write(outputStream)
                .withTemplate(Thread.currentThread().getContextClassLoader().getResourceAsStream(TEMPLATE_NAME))
                .build();
    }

    public void doFill(VoucherLifeCycleSheet sheet) {

        WriteSheet writeSheet = EasyExcelFactory.writerSheet(sheet.getSheetName()).build();

        excelWriter.fill(new FillWrapper(sheet.getFillKey(), sheet.getData()), fillConfig, writeSheet);

    }

    @Override
    public String finish() {

        excelWriter.finish();

        return ReportUploadHelper.fileUpload(ReportContextHelper.findContext(), new ByteArrayInputStream(outputStream.toByteArray()));
    }


}