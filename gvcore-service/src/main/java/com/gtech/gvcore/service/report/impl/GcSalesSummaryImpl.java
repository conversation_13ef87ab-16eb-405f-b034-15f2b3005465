package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.CustomerTypeEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.mapper.CustomerOrderMapper;
import com.gtech.gvcore.dao.model.*;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.service.IssueHandlingService;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.GcSalesSummaryBean;
import com.gtech.gvcore.service.report.impl.bo.GcSalesBo;
import com.gtech.gvcore.service.report.impl.param.GcSalesQueryData;
import com.gtech.gvcore.service.report.impl.support.GcSalesBaseImpl;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 14:01
 * @Description: 0.24 - 1.02 59.58s,
 */
@Service
public class GcSalesSummaryImpl extends GcSalesBaseImpl<GcSalesSummaryBean>
        implements BusinessReport<GcSalesQueryData, GcSalesSummaryBean>, SingleReport {

    @Autowired
    protected IssueHandlingService issueHandlingService;
    @Autowired
    protected CustomerOrderMapper customerOrderMapper;


    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_SALES_SUMMARY_REPORT;
    }

    @Override
    protected void addParam(GcSalesQueryData param, CreateReportRequest reportParam) {

    }

    @Override
    public List<GcSalesSummaryBean> getExportData(List<GcSalesBo> boList) {

        final Map<String, GcSalesSummaryBo> resultMap = new HashMap<>();

        boList.forEach(e -> {

            final String key = StringUtils.join(e.getSalesCode(), e.getMerchantCode(), e.getOutletCode(), e.getInvoiceNumber(), e.getCpgCode(), e.getCustomerCode());

            final GcSalesSummaryBo salesSummaryBo = resultMap.computeIfAbsent(key, k -> GcSalesSummaryBo.newInstance(e));

            salesSummaryBo.addSalesCount().addSalesAmount(e.getDenomination());
        });

        final List<GcSalesSummaryBo> resultList = new ArrayList<>(resultMap.values());

        final JoinDataMap<Outlet> outletMap = super.getMapByCode(resultList, GcSalesSummaryBo::getOutletCode, Outlet.class);
        final JoinDataMap<GcCpg> cpgMap = super.getMapByCode(resultList, GcSalesSummaryBo::getCpgCode, GcCpg.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(resultList, GcSalesSummaryBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Customer> customerMap = super.getMapByCode(resultList, GcSalesSummaryBo::getCustomerCode, Customer.class);

        List<GcSalesSummaryBean> collect = resultList.stream()
                .map(e -> {

                    final Merchant merchant = merchantMap.findValue(e.getMerchantCode());
                    final Outlet outlet = outletMap.findValue(e.getOutletCode());
                    final GcCpg cpg = cpgMap.findValue(e.getCpgCode());
                    Customer value = customerMap.findValue(e.getCustomerCode());
                    String customerName;
                    if (Objects.equals(value.getCustomerType(), CustomerTypeEnum.CORPORATE.code())) {
                        customerName = value.getCompanyName();
                    } else {
                        customerName = value.getCustomerName();
                    }
                    return new GcSalesSummaryBean()
                            .setDate(e.getDate())
                            .setInvoiceNumber(e.getInvoiceNumber())
                            .setSubCompanyName(customerName)
                            .setMerchantName(merchant.getMerchantName())
                            .setMerchantOutletName(outlet.getOutletName())
                            .setSalesCount(e.getSalesCount().toString())
                            .setSalesAmount(super.toAmount(e.getSalesAmount()))
                            .setCpgCode(cpg.getCpgName())
                            .setNotes(e.getNotes());
                }).collect(Collectors.toList());
        GcSalesSummaryBean blank = new GcSalesSummaryBean();
        GcSalesSummaryBean gcSalesSummaryBean = new GcSalesSummaryBean();
        gcSalesSummaryBean.setDate("Total");
        gcSalesSummaryBean.setSalesCount(resultList.stream().map(GcSalesSummaryBo::getSalesCount).reduce(0, Integer::sum).toString());
        gcSalesSummaryBean.setSalesAmount(resultList.stream().map(GcSalesSummaryBo::getSalesAmount).reduce(BigDecimal.ZERO, BigDecimal::add).toString());
        collect.add(blank);
        collect.add(gcSalesSummaryBean);
        return collect;
    }


    @Getter
    @Setter
    @Accessors(chain = true)
    public static class GcSalesSummaryBo {

        private String date;

        private String saleCode;

        private String merchantCode;

        private String outletCode;

        private String invoiceNumber;

        private String cpgCode;

        private Integer salesCount = 0;

        private BigDecimal salesAmount = BigDecimal.ZERO;

        private String notes;

        private String customerCode;

        public static GcSalesSummaryBo newInstance(GcSalesBo bo) {

            return newInstance(DateUtil.format(bo.getTransactionDate(), DateUtil.FORMAT_YYYYMMDDHHMISS), bo.getSalesCode(), bo.getMerchantCode(), bo.getOutletCode(), bo.getInvoiceNumber(), bo.getCpgCode(), bo.getNotes(), bo.getCustomerCode());
        }

        public static GcSalesSummaryBo newInstance(String date, String saleCode, String merchantCode, String outletCode, String invoiceNumber, String cpgCode, String notes, String customerCode) {

            return new GcSalesSummaryBo()
                    .setDate(date)
                    .setSaleCode(saleCode)
                    .setMerchantCode(merchantCode)
                    .setOutletCode(outletCode)
                    .setInvoiceNumber(invoiceNumber)
                    .setCpgCode(cpgCode)
                    .setNotes(notes)
                    .setCustomerCode(customerCode);
        }

        public GcSalesSummaryBo addSalesCount() {
            this.salesCount += 1;
            return this;
        }

        public void addSalesAmount(BigDecimal amount) {
            this.salesAmount = this.salesAmount.add(amount);
        }

    }

}
