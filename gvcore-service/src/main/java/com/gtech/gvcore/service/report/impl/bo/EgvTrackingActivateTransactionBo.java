package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.DateUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @ClassName EgvTrackingTransactionBo
 * @Description
 * <AUTHOR>
 * @Date 2023/1/11 20:42
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class EgvTrackingActivateTransactionBo {

    private String voucherCode;

    private String transactionDate;

    public Date getTransactionDate() {

        return DateUtil.parseDate(transactionDate, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

}
