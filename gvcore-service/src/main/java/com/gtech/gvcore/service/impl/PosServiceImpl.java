package com.gtech.gvcore.service.impl;

import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.gtech.gvcore.cache.MasterDataCache;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.gtech.basic.masterdata.web.dao.MasterDataDistrictMapper;
import com.gtech.basic.masterdata.web.entity.MasterDataDistrictEntity;
import com.gtech.commons.redis.GTechRedisTemplate;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.request.outlet.GetOutletRequest;
import com.gtech.gvcore.common.request.pos.CreatePosRequest;
import com.gtech.gvcore.common.request.pos.GetPosRequest;
import com.gtech.gvcore.common.request.pos.QueryOutletByPosIdRequest;
import com.gtech.gvcore.common.request.pos.QueryPosListRequest;
import com.gtech.gvcore.common.request.pos.UpdatePosRequest;
import com.gtech.gvcore.common.request.pos.UpdatePosStatusRequest;
import com.gtech.gvcore.common.request.posaccount.QueryPosAccountListRequest;
import com.gtech.gvcore.common.request.poscpg.CreatePosCpgRequest;
import com.gtech.gvcore.common.request.poscpg.DeletePosCpgByPosCodeRequest;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.common.response.pos.PosCpgResponse;
import com.gtech.gvcore.common.response.pos.PosResponse;
import com.gtech.gvcore.common.response.posaccount.PosAccountResponse;
import com.gtech.gvcore.dao.mapper.PosMapper;
import com.gtech.gvcore.dao.model.Pos;
import com.gtech.gvcore.dao.model.Printer;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.OutletService;
import com.gtech.gvcore.service.PosAccountService;
import com.gtech.gvcore.service.PosCpgService;
import com.gtech.gvcore.service.PosService;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

@Slf4j
@Service
public class PosServiceImpl implements PosService {

    @Autowired
    private PosMapper posMapper;

    @Autowired
    private PosCpgService posCpgService;

    @Autowired
    private OutletService outletService;

    @Autowired
    private MasterDataDistrictMapper masterDataDistrictMapper;
    @Autowired
    private GvCodeHelper gvCodeHelper;

    @Autowired
    private PosAccountService posAccountService;
    @Autowired
    private GTechRedisTemplate redisTemplate;

    @Autowired
    private MasterDataCache masterDataCache;

    @Override
    public Result<String> createPos(CreatePosRequest param) {

        Pos pos = BeanCopyUtils.jsonCopyBean(param, Pos.class);
        pos.setPosCode(gvCodeHelper.generatePosCode());
        pos.setStatus(GvcoreConstants.DELETE_STATUS_ENABLE);

        try {
            posMapper.insertSelective(pos);
        } catch (DuplicateKeyException e) {
            return Result.failed("MachineId repeat");
        }
        if (CollectionUtils.isEmpty(param.getCpgCodes())){
            return Result.ok(pos.getPosCode());
        }

        //添加pos cpg
        for (String cpgCode : param.getCpgCodes()) {
            CreatePosCpgRequest request = BeanCopyUtils.jsonCopyBean(param,CreatePosCpgRequest.class);
            request.setPosCode(pos.getPosCode());
            request.setCpgCode(cpgCode);
            request.setCreateUser(param.getCreateUser());
            posCpgService.createPosCpg(request);
        }
        masterDataCache.updatePosCache(pos);
        return Result.ok(pos.getPosCode());

    }

    @Override
    public Result<String> updatePos(UpdatePosRequest param) {

        Pos pos = BeanCopyUtils.jsonCopyBean(param, Pos.class);
        Example example = new Example(Pos.class);
        example.createCriteria()
                .andEqualTo(Pos.CONST_POS_CODE,param.getPosCode());
        posMapper.updateByConditionSelective(pos,example);

        //先删除后修改
        if (CollectionUtils.isNotEmpty(param.getCpgCodes())) {
            posCpgService.deletePosCpgByPosCode(DeletePosCpgByPosCodeRequest.builder().posCode(param.getPosCode()).build());
            for (String cpgCode : param.getCpgCodes()) {
                CreatePosCpgRequest request = BeanCopyUtils.jsonCopyBean(param,CreatePosCpgRequest.class);
                request.setPosCode(pos.getPosCode());
                request.setCpgCode(cpgCode);
                request.setCreateUser(param.getUpdateUser());
                posCpgService.createPosCpg(request);
            }
        }else {
            posCpgService.deletePosCpgByPosCode(DeletePosCpgByPosCodeRequest.builder().posCode(param.getPosCode()).build());
        }
        masterDataCache.deletePosCpgCache(pos.getPosCode());
        masterDataCache.updatePosCache(pos);
        return Result.ok();
    }

    @Override
    public PageResult<PosResponse> queryPosList(QueryPosListRequest request) {
        PageHelper.startPage(request.getPageNum(),request.getPageSize());

        List<PosResponse> pos = posMapper.queryPosList(request);

        PageInfo<PosResponse> info = PageInfo.of(pos);
        List<PosResponse> posResponses = BeanCopyUtils.jsonCopyList(info.getList(), PosResponse.class);
        for (PosResponse posResponse : posResponses) {
            List<PosCpgResponse> response = posCpgService.queryPosCpgListByPos(posResponse.getPosCode());

            if (StringUtil.isNotEmpty(posResponse.getOutletCode())){
                OutletResponse outlet = outletService.getOutlet(GetOutletRequest.builder().outletCode(posResponse.getOutletCode()).build());
                if (null != outlet){
                    posResponse.setOutletName(outlet.getOutletName());

                }
            }

            if (CollectionUtils.isEmpty(response)) {
                continue;
            }
            List<PosCpgResponse> cpg = BeanCopyUtils.jsonCopyList(response, PosCpgResponse.class);
            posResponse.setCpg(cpg);
        }
        List<PosResponse> collect = posResponses.stream().sorted(Comparator.comparing(PosResponse::getMerchantName)).collect(Collectors.toList());
        return new PageResult<>(collect,info.getTotal());
    }

    @Override
    public List<Pos> queryPosALL() {
        return posMapper.selectAll();
    }

    @Override
    public Result<String> updatePosStatus(UpdatePosStatusRequest param) {
        Pos entity = BeanCopyUtils.jsonCopyBean(param, Pos.class);
        entity.setUpdateTime(new Date());

        Example example = new Example(Pos.class);
        example.createCriteria()
                .andEqualTo(Pos.CONST_POS_CODE,param.getPosCode());

        posMapper.updateByConditionSelective(entity,example);
        Pos selectPos = new Pos();
        selectPos.setPosCode(param.getPosCode());
        Pos pos = posMapper.selectOne(selectPos);
        masterDataCache.updatePosCache(pos);
        return Result.ok();
    }

    @Override
    public Result<PosResponse> getPos(GetPosRequest request) {

        Pos pos = BeanCopyUtils.jsonCopyBean(request,Pos.class);

        Pos posEntity = posMapper.selectOne(pos);

        PosResponse posResponse = BeanCopyUtils.jsonCopyBean(posEntity, PosResponse.class);

        List<PosCpgResponse> response = posCpgService.queryPosCpgListByPos(posResponse.getPosCode());
        if (CollectionUtils.isNotEmpty(response)){
            List<PosCpgResponse> cpgs = BeanCopyUtils.jsonCopyList(response, PosCpgResponse.class);
            posResponse.setCpg(cpgs);
        }

        if (StringUtil.isNotEmpty(posResponse.getOutletCode())){
            OutletResponse outlet = outletService.getOutlet(GetOutletRequest.builder().outletCode(posResponse.getOutletCode()).build());
            if (null != outlet){
                posResponse.setOutletName(outlet.getOutletName());
            }
        }

        return Result.ok(posResponse);
    }

    @Override
    public Result<OutletResponse> queryOutletByPosId(QueryOutletByPosIdRequest request) {

		if (StringUtil.isEmpty(request.getMachineId())) {
			return Result.failed("MachineId can't be empty");
		}
        Pos pos = posMapper.selectOne(Pos.builder().machineId(request.getMachineId()).status(1).build());

        if (null == pos){
            return Result.failed("Terminal does not exist");
        }

        OutletResponse outlet = outletService.getOutlet(GetOutletRequest.builder().outletCode(pos.getOutletCode()).build());

        if (null == outlet){
            return Result.failed("Terminal does not exist");
        }


        Map<String, String> districtMap = getDistrictMap(Lists.newArrayList(outlet));
        outlet.setStateName(districtMap.get(outlet.getStateCode()));
        outlet.setCityName(districtMap.get(outlet.getCityCode()));
        outlet.setDistrictName(districtMap.get(outlet.getDistrictCode()));




        if (StringUtil.isNotEmpty(pos.getAccount()) && StringUtil.isNotEmpty(pos.getPassword())){
            if (pos.getAccount().equals(request.getPosAccount()) && pos.getPassword().equals(request.getPosPassword())){
                return Result.ok(BeanCopyUtils.jsonCopyBean(outlet,OutletResponse.class));
            }else {
                return Result.failed("wrong password.");
            }
        }else {
            Result<PosAccountResponse> posAccount = posAccountService.getPosAccount(QueryPosAccountListRequest.builder().posAccount(request.getPosAccount()).posPassword(request.getPosPassword()).outletType(outlet.getOutletType()).build());
            if (!posAccount.isSuccess()){
                return Result.failed("wrong password.");
            }
        }


        return Result.ok(BeanCopyUtils.jsonCopyBean(outlet,OutletResponse.class));
    }



    private Map<String, String> getDistrictMap(List<OutletResponse> item) {

        if (org.springframework.util.CollectionUtils.isEmpty(item)) {
            return new HashMap<>(1);
        }


        Set<String> stateCodeSet = item.stream().map(OutletResponse::getStateCode).collect(Collectors.toSet());
        Set<String> cityCodeSet = item.stream().map(OutletResponse::getCityCode).collect(Collectors.toSet());
        Set<String> districtCodeSet = item.stream().map(OutletResponse::getDistrictCode).collect(Collectors.toSet());


        Set<String> codesSet = Stream.of(stateCodeSet, cityCodeSet, districtCodeSet).flatMap(Collection::stream).collect(Collectors.toSet());

        Example example = new Example(MasterDataDistrictEntity.class, true, false);
        example.createCriteria().andIn(Printer.C_MASTER_DATA_DISTRICT_DISTRICT_CODE, codesSet);

        List<MasterDataDistrictEntity> districtEntities = masterDataDistrictMapper.selectByExample(example);


        return org.springframework.util.CollectionUtils.isEmpty(districtEntities) ? new HashMap<>(1) : districtEntities.stream().collect(Collectors.toMap(MasterDataDistrictEntity::getDistrictCode, MasterDataDistrictEntity::getDistrictName));
    }

    @Override
    public Map<String, String> queryNameByPosCodeList(List<String> posCodeList) {

        if (CollectionUtils.isEmpty(posCodeList)) {
            return Collections.emptyMap();
        }

        List<Pos> list = posMapper.queryByPosCodeList(posCodeList);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(Pos::getPosCode, Pos::getPosName));
    }

}
