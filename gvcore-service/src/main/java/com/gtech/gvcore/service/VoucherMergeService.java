package com.gtech.gvcore.service;

import com.gtech.gvcore.common.request.vouchermerge.CancelMergeVoucherRequest;
import com.gtech.gvcore.common.request.vouchermerge.MergeVoucherRequest;
import com.gtech.gvcore.common.response.vouchermerge.MergeVoucherResponse;

public interface VoucherMergeService {

	MergeVoucherResponse merge(MergeVoucherRequest request);

	void cancelMerge(CancelMergeVoucherRequest request);

	void useMergeVoucher(String voucherCode);
}
