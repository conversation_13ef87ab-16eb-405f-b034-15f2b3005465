package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.PollReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.CancelSalesDetailBean;
import com.gtech.gvcore.service.report.impl.bo.CancelSalesBo;
import com.gtech.gvcore.service.report.impl.param.CancelSalesQueryData;
import com.gtech.gvcore.service.report.impl.support.CancelSalesBaseImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:58
 * @Description:
 */
@Service
public class CancelSalesDetailImpl extends CancelSalesBaseImpl<CancelSalesDetailBean>
        implements BusinessReport<CancelSalesQueryData, CancelSalesDetailBean>, SingleReport {

    @Override
    public List<CancelSalesDetailBean> getExportData(CancelSalesQueryData queryData) {

        //find
        List<CancelSalesBo> list = super.getBoList(queryData);

        //empty
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        //init map
        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(list, CancelSalesBo::getCpgCode,  Cpg.class);
        final JoinDataMap<Customer> customerMap = super.getMapByCode(list, CancelSalesBo::getCustomerCode,  Customer.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(list, CancelSalesBo::getMerchantCode,  Merchant.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(list, CancelSalesBo::getOutletCode,  Outlet.class);
        final JoinDataMap<Voucher> voucherMap = super.getMapByCode(list, CancelSalesBo::getVoucherCode,  Voucher.class);

        //convert result
        return list.stream().map(e -> {

            //voucher
            Voucher voucher = voucherMap.findValue(e.getVoucherCode());

            //cancelSalesDetailBean
            return new CancelSalesDetailBean()
                    .setVoucherNumber(e.getVoucherCode())
                    .setTransactionDate(DateUtil.format(e.getTransactionDate(), DateUtil.FORMAT_YYYYMMDDHHMISS))
                    .setInvoiceNumber(e.getInvoiceNumber())
                    .setOutlet(outletMap.findValue(e.getOutletCode()).getOutletName())
                    .setVoucherProgramGroup(cpgMap.findValue(e.getCpgCode()).getCpgName())
                    .setCorporateName(customerMap.findValue(e.getCustomerCode()).getCustomerName())
                    .setMerchant(merchantMap.findValue(e.getMerchantCode()).getMerchantName())
                    .setTransactionAmount(super.toAmount(ConvertUtils.toBigDecimal(voucher.getDenomination(), BigDecimal.ZERO)))
                    .setExpiryDate(DateUtil.format(voucher.getVoucherEffectiveDate(), DateUtil.FORMAT_YYYYMMDDHHMISS));

        }).collect(Collectors.toList());
    }

    @Override
    public ReportExportTypeEnum exportTypeEnum() {

        //ENUM
        return ReportExportTypeEnum.CANCEL_SALES_DETAILED_REPORT;
    }



}
