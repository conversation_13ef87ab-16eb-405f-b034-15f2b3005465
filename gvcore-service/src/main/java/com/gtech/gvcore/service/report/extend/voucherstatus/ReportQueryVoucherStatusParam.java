package com.gtech.gvcore.service.report.extend.voucherstatus;

import com.gtech.gvcore.common.enums.ReportVoucherStatusEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @ClassName ReportQueryVoucherStatusParam
 * @Description
 *  使用该接口统一报表卡券状态全局条件
 *  注意事项
 *  1.使用该接口时 voucher table 需要使用别名 v
 *  2.使用方法为在需要追加条件的位置 ${}appendVoucherStatusSql
 *
 * <AUTHOR>
 * @Date 2023/1/17 16:50
 * @Version V1.0
 **/
public interface ReportQueryVoucherStatusParam {

    /**
     * 获取卡券状态编码集合
     * @return
     */
    List<String> getVoucherStatusList();

    /**
     * 获取卡券状态条件sql
     * @return
     */
    default String getAppendVoucherStatusSql() {

        StringBuilder sql = new StringBuilder();

        if (valid(ReportVoucherStatusEnum.VOUCHER_NEWLY_GENERATED)) sql.append(ReportVoucherStatusConvertUtils.SELECT_NEWLY_GENERATED);
        if (valid(ReportVoucherStatusEnum.VOUCHER_ACTIVATED))       sql.append(ReportVoucherStatusConvertUtils.SELECT_ACTIVATED);
        if (valid(ReportVoucherStatusEnum.VOUCHER_REDEEMED))        sql.append(ReportVoucherStatusConvertUtils.SELECT_REDEEMED);
        if (valid(ReportVoucherStatusEnum.VOUCHER_CANCELLED))       sql.append(ReportVoucherStatusConvertUtils.SELECT_CANCELLED);
        if (valid(ReportVoucherStatusEnum.VOUCHER_EXPIRED))         sql.append(ReportVoucherStatusConvertUtils.SELECT_EXPIRED);
        if (valid(ReportVoucherStatusEnum.VOUCHER_PURCHASED))       sql.append(ReportVoucherStatusConvertUtils.SELECT_PURCHASED);
        if (valid(ReportVoucherStatusEnum.VOUCHER_DEACTIVATED))     sql.append(ReportVoucherStatusConvertUtils.SELECT_DEACTIVATED);

        if (sql.length() < 1) return StringUtils.EMPTY;

        return ReportVoucherStatusConvertUtils.BEGIN + sql + ReportVoucherStatusConvertUtils.END;

    }

    default boolean valid(ReportVoucherStatusEnum voucherStatusEnum) {
        if (CollectionUtils.isEmpty(getVoucherStatusList())) return false;

        return CollectionUtils.containsAny(getVoucherStatusList(), voucherStatusEnum.getCodeString());
    }


}
