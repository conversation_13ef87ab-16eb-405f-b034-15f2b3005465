package com.gtech.gvcore.service.report.impl.param;

import com.gtech.gvcore.service.report.ReportQueryParam;
import com.gtech.gvcore.service.report.extend.row.TransactionDataPageParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年6月22日
 */
@Getter
@Setter
@Accessors(chain = true)
public class SalesDetailedQueryData extends TransactionDataPageParam implements ReportQueryParam {

    private String issuerCode;

    private List<String> merchantCodeList;

    private List<String> outletCodeList;

    private Date transactionDateStart;

    private Date transactionDateEnd;

    private List<String> cpgCodeList;

    private String invoiceNumber;

    private Long voucherCodeNumStart;

    private Long voucherCodeNumEnd;

}
