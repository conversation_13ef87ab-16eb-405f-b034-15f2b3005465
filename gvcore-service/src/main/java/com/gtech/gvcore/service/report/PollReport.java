package com.gtech.gvcore.service.report;


import com.gtech.commons.page.PageParam;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * @ClassName PollReport
 * @Description Poll Report
 * <AUTHOR>
 * @Date 2023/1/5 13:51
 * @Version V1.0
 **/
public interface PollReport {

    /**
     * 报表分批查询每页大小
     * @return
     */
    default int pageSize() { return 1000; }

    /**
     * 程序执行极限次数 safetyNumber() * pageSize() = 1000
     * @return
     */
    default int safetyNumber() { return 5_0000; }

    /**
     * 是否终止查询下一页
     * @param pageResult
     * @return
     */
    default boolean termination(List<?> pageResult) {

        return !CollectionUtils.isNotEmpty(pageResult);
    }

    /**
     * 构建报表
     * @param context
     */
    default void builder(ReportContext context) {

        // init
        final BusinessReport<ReportQueryParam, ?> reportImpl = context.getBusinessReport();
        final ReportQueryParam reportParam = context.getQueryParam();
        final PageParam pageParam = this.getPageParam(reportParam);
        boolean first = true;

        pageParam.setPageNum(0);

        // for
        for (int currentNumber = 1; pageParam.getPageNum() < safetyNumber(); first = false, currentNumber++) {

            // log
            LoggerFactory.getLogger(PollReport.class).info("builderReportByContextFindNextReport executing. orderReportCode:{} , current page:{}, Aspect page number:{}", context.getReportCode(), currentNumber, pageParam.getPageNum());
            pageParam.setPageNum(pageParam.getPageNum() + 1);

            //find
            final List<?> dataList = reportImpl.getExportData(reportParam);

            //方法若返回不查询下一页则跳出
            if (this.termination(dataList)) {

                //如果为首次查询 则 报表 no data
                if (first) context.noData();

                //跳出
                break;
            }

            // add data
            context.appendDate(dataList);
        }

        // data finish
        this.dataFinish(context);
    }

    /**
     * 数据构建完成
     * @param context
     */
    default void dataFinish (ReportContext context) {}

    /**
     * get page param
     *
     * @param reportParam report param
     * @return page param
     */
    default PageParam getPageParam(ReportQueryParam reportParam) {

        //check
        if (!(reportParam instanceof PageParam)) throw new UnsupportedOperationException("报表被识别为 poll report 但参数实现类并非PageParam 子类.");

        //get and setting page param
        final PageParam pageParam = (PageParam) reportParam;
        pageParam.setPageSize(this.pageSize());

        //return
        return pageParam;
    }

}
