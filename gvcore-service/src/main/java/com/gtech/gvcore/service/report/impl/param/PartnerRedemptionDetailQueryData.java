package com.gtech.gvcore.service.report.impl.param;

import com.gtech.gvcore.service.report.ReportQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-12-30 10:18
 */
@Getter
@Setter
@Accessors(chain = true)
public class PartnerRedemptionDetailQueryData implements ReportQueryParam {

    //transaction date start 交易时间 开始
    private Date transactionDateStart;

    //transaction date end 交易时间 结束
    private Date transactionDateEnd;

    //cpg code list
    private List<String> cpgCodeList;

    //invoice number 发票编号
    private String invoiceNumber;

    private List<String> customerCodeList;

    private String outletCode;

    private String transactionType;

    private Long voucherCodeNumStart;

    private Long voucherCodeNumEnd;
}
