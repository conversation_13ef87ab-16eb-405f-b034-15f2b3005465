package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/20 13:43
 * @Description: Gift Card Extend Expiry Report Summary Bean
 */
@Getter
@Setter
@Accessors(chain = true)
public class GcExtendExpiryForSummaryBean {

    /**
     * Gift Card Program Group
     */
    @ExcelProperty(value = "Gift Card Program Group")
    private String vpg;

    /**
     * Transaction Date
     */
    @ExcelProperty(value = "Transaction Date")
    private String transactionDate;

    /**
     * Merchant
     */
    @ExcelProperty(value = "Merchant")
    private String merchant;

    /**
     * Merchant Outlet
     */
    @ExcelProperty(value = "Merchant Outlet")
    private String merchantOutlet;

    /**
     * Cards Count
     */
    @ExcelProperty(value = "Cards Count", converter = ExportExcelNumberConverter.class)
    private String cardsCount;

    /**
     * Total Amount
     */
    @ReportAmountValue
    @ExcelProperty(value = "Total Amount", converter = ExportExcelNumberConverter.class)
    private String totalAmount;

    /**
     * Source (从extend的来源)
     */
    @ExcelProperty(value = "Source")
    private String source;
}
