package com.gtech.gvcore.service.report.impl.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @ClassName GcReactivatedBo
 * @Description Gift Card Reactivated (Unblock) Business Object
 * <AUTHOR>
 * @Date 2023/5/10 10:40
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GcRegenerateActivationCodeDetailBo {
    private String cardNumber;
    private String cpgCode;
    private Date transactionDate;
    private String totalAmount;
    private String customerName;
    private String expiryDate;
    private Date issuanceDate;
    private Date activationEnded;
    private Date gracePeriodEnded;
    private String issueHandlingCode;
    private String customerEmail;
    private String activationDeadline;
    private String activationGracePeriod;
    private Integer activationExtensionCount;
}
