package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName LatestGvStatusEnhancedBean
 * @Description Latest Gift Card Status Enhanced Report Bean
 * <AUTHOR>
 * @Date 2025-06-17
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GcLatestGvStatusBean {

    @ExcelProperty(value = "Gift Card Number")
    private String voucherNumber;

    @ExcelProperty(value = "Gift Card Program Group")
    private String voucherProgramGroup;

    @ExcelProperty(value = "Issuance Date")
    private String issuanceDate;

    @ExcelProperty(value = "Gift Card Status")
    private String voucherStatus;

    @ExcelProperty(value = "Transaction Type")
    private String transactionType;

    @ExcelProperty(value = "Last Action Date")
    private String lastActionDate;

    @ExcelProperty(value = "Merchant")
    private String merchant;

    @ExcelProperty(value = "Outlet Name")
    private String outletName;

    @ExcelProperty(value = "Expiry Date")
    private String expiryDate;

    @ExcelProperty(value = "SBU Company Name")
    private String sbuCompanyName;

    @ReportAmountValue
    @ExcelProperty(value = "Gift Card Current Balance", converter = ExportExcelNumberConverter.class)
    private String currentBalance;

    @ReportAmountValue
    @ExcelProperty(value = "Redeem Amount", converter = ExportExcelNumberConverter.class)
    private String redeemAmount;
}
