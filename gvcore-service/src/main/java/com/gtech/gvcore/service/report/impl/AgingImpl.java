package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.ReportVoucherStatusEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.common.utils.GvConvertUtils;
import com.gtech.gvcore.dao.model.Company;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.export.snapshoot.ReportDateContext;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import com.gtech.gvcore.service.report.extend.context.ReportContextBuilder;
import com.gtech.gvcore.service.report.extend.context.ReportContextHelper;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bo.AgingBo;
import com.gtech.gvcore.service.report.impl.bo.AgingVoucherTransactionBoVoucher;
import com.gtech.gvcore.service.report.impl.param.AgingReportQueryParamData;
import com.gtech.gvcore.service.report.impl.support.aging.AgingFileContext;
import com.gtech.gvcore.service.report.impl.support.aging.AgingSheet;
import com.gtech.gvcore.service.report.impl.support.aging.AgingSheetBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @ClassName AgingReport
 * @Description GV Aging Report
 * <AUTHOR>
 * @Date 2022/10/26 16:54
 * @Version V1.0
 **/
@Service
public class AgingImpl extends ReportSupport
        implements BusinessReport<AgingReportQueryParamData, Object>, SingleReport {

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.AGING_REPORT_SUMMARY_REPORT;
    }

    @Override
    public AgingReportQueryParamData builderQueryParam(CreateReportRequest reportParam) {

        final Date defaultStartTime = DateUtil.parseDate(DateUtil.format(DateUtil.now(), DateUtil.FORMAT_YYYY) + "0101000000", DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        final Date defaultEndTime = DateUtils.addYears(defaultStartTime, 1);

        final AgingReportQueryParamData queryParam = new AgingReportQueryParamData();

        queryParam.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        queryParam.setOutletCodeList(reportParam.getOutletCodes());

        queryParam.setTransactionDateStart(GvConvertUtils.toObject(reportParam.getTransactionDateStart(), defaultStartTime));
        queryParam.setTransactionDateEnd(GvConvertUtils.toObject(reportParam.getTransactionDateEnd(), defaultEndTime));
        queryParam.setCpgCodeList(reportParam.getCpgCodes());
        queryParam.setCustomerCodeList(reportParam.getCustomerCodes());

        return queryParam;
    }

    @Override
    public List<Object> getExportData(AgingReportQueryParamData param) {

        throw new UnsupportedOperationException();
    }

    @Autowired
    private List<AgingSheetBuilder> agingSheetBuilderList;

    @Override
    public void customContext(ReportContextBuilder builder) {

        builder.bindFileContext(new AgingFileContext());
    }

    @Override
    public void builder(ReportContext context) {

        //init data
        if (!initData(context)) return;

        // head info
        final Map<String, Object> head = new HashMap<>();
        head.put("updateTime", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm"));

        // excel context init
        final AgingFileContext excelContext = (AgingFileContext) context.getFileContext();

        //data context init
        final ReportDateContext reportDateContext = context.getReportDateContext();
        final Map<ReportExportTypeEnum, List<?>> reportData = new EnumMap<>(ReportExportTypeEnum.class);

        agingSheetBuilderList.forEach(e -> {

            //builder sheet
            final AgingSheet builder = e.builder(context);

            //excel context operations
            builder.setHead(head);
            excelContext.doFill(builder);

            //data context operations
            builder.getSheetData().forEach(i -> reportData.put(i.getReportExportTypeEnum(), i.getList()));
        });

        reportDateContext.fastSaveAll(reportData);
    }

    public boolean initData(final ReportContext context) {

        //param
        final AgingReportQueryParamData param = (AgingReportQueryParamData) context.getQueryParam();

        //sales data
        final List<AgingBo> salesData = selectAgingSalesDataAndValidation(param);

        //validation
        if (CollectionUtils.isEmpty(salesData)) return false;

        //sales data join
        final JoinDataMap<Customer> customerJoinDataMap = super.getMapByCode(salesData, AgingBo::getCustomerCode, Customer.class);
        final JoinDataMap<Merchant> merchantJoinDataMap = super.getMapByCode(salesData, AgingBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Voucher> voucherJoinDataMap = super.getMapByCode(salesData, AgingBo::getVoucherCode, Voucher.class,
                //default voucher
                Voucher.builder().createTime(new Date()).build());

        //redeem data
        final List<AgingVoucherTransactionBoVoucher> redeemList = selectVoucherRedeem(voucherJoinDataMap);

        //redeem data join
        final JoinDataMap<Merchant> redeemMerchantJoinDataMap = super.getMapByCode(redeemList, AgingVoucherTransactionBoVoucher::getMerchantCode, Merchant.class);
        final JoinDataMap<Company> redeemCompanyJoinDataMap = super.getMapByCode(redeemMerchantJoinDataMap.values(), Merchant::getCompanyCode, Company.class);

        //save cache
        context.putCache(AgingSheetBuilder.CUSTOMER_MAP_KEY, customerJoinDataMap);
        context.putCache(AgingSheetBuilder.MERCHANT_MAP_KEY, merchantJoinDataMap);
        context.putCache(AgingSheetBuilder.VOUCHER_MAP_KEY, voucherJoinDataMap);
        context.putCache(AgingSheetBuilder.SALES_DATA_KEY, salesData);
        context.putCache(AgingSheetBuilder.REDEEM_DATA_KEY, redeemList);
        context.putCache(AgingSheetBuilder.REDEEM_MERCHANT_MAP_KEY, redeemMerchantJoinDataMap);
        context.putCache(AgingSheetBuilder.REDEEM_COMPANY_MAP_KEY, redeemCompanyJoinDataMap);

        return true;
    }

    /**
     * select redeem data
     *
     * @param voucherJoinDataMap voucher map
     * @return redeem data
     */
    private List<AgingVoucherTransactionBoVoucher> selectVoucherRedeem(JoinDataMap<Voucher> voucherJoinDataMap) {

        //voucher code
        final List<String> redeemVoucherCode =
                voucherJoinDataMap.values()
                        .stream()
                        //filter voucher status
                        .filter(e -> ReportVoucherStatusEnum.VOUCHER_REDEEMED.equals(super.getVoucherStatus(e)))
                        //filter voucher code
                        .map(Voucher::getVoucherCode)
                        //distinct
                        .distinct()
                        // to code list
                        .collect(Collectors.toList());

        //redeem data
        return ListUtils.partition(redeemVoucherCode, 1000)
                // parallel stream
                .parallelStream()
                // select param => code to object
                .map(AgingReportQueryParamData.AgingReportRedeemQueryParam::new)
                // poll voucher redeem data
                .map(e -> PollPageHelper.pollGroupNewTransactionByCodeSelect(reportBusinessMapper::selectAgingVoucherTransactionData, e))
                // flat map
                .flatMap(Collection::stream)
                // redeem
                .filter(s -> TransactionTypeEnum.GIFT_CARD_REDEEM.equalsCode(s.getTransactionType()))
                // to list
                .collect(Collectors.toList());
    }

    /**
     * select sales data and validation
     * @param param
     * @return sales data
     */
    private List<AgingBo> selectAgingSalesDataAndValidation(AgingReportQueryParamData param) {

        //sales data
        final List<AgingBo> salesData =
                // select group by voucher
                Optional.of(PollPageHelper.pollGroupNewTransactionByCodeSelect(reportBusinessMapper::selectAgingVoucher, param))
                        // filter gift card sell
                        .map(e -> e.stream().filter(s -> TransactionTypeEnum.GIFT_CARD_SELL.equalsCode(s.getTransactionType())).collect(Collectors.toList()))
                        .orElse(new ArrayList<>());

        //validation
        if (CollectionUtils.isEmpty(salesData)) ReportContextHelper.noData();

        //result
        return salesData;
    }


    @Override
    public Map<ReportExportTypeEnum, Class<?>> getResultDateType() {
        return new EnumMap<>(ReportExportTypeEnum.class);
    }
}


