package com.gtech.gvcore.service.report.impl.support.life;

import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.service.report.export.snapshoot.label.ReportLabelSupport;
import com.gtech.gvcore.service.report.impl.param.VoucherLifeCycleQueryData;
import com.gtech.gvcore.service.report.impl.support.life.excel.VoucherLifeCycleFileContext;
import com.gtech.gvcore.service.report.impl.support.life.excel.VoucherLifeCycleSheet;

import java.util.List;
import java.util.Map;

/**
 * @ClassName VoucherLifeCycle
 * @Description
 * <AUTHOR>
 * @Date 2023/1/5 19:46
 * @Version V1.0
 **/
public interface VoucherLifeCycle extends ReportLabelSupport {

    default void builder(VoucherLifeCycleFileContext excelContext, Map<ReportExportTypeEnum, List<?>> reportData, VoucherLifeCycleQueryData param) {

        VoucherLifeCycleSheet sheet = builderSheet(param);

        excelContext.doFill(sheet);
        reportData.put(exportTypeEnum(), sheet.getData());
    }

    VoucherLifeCycleSheet builderSheet(VoucherLifeCycleQueryData param);
}
