package com.gtech.gvcore.service.report.impl.param;

import com.gtech.gvcore.service.report.ReportQueryParam;
import com.gtech.gvcore.service.report.extend.row.TransactionDataPageParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class GcSalesQueryData extends TransactionDataPageParam implements ReportQueryParam {

    private List<String> transactionIdList;

    private String issuerCode;

    private List<String> merchantCodeList;

    private List<String> outletCodeList;

    private List<String> cpgCodeList;

    private Date transactionDateStart;

    private Date transactionDateEnd;

    private String invoiceNumber;

    private List<String> voucherCode;


}
