package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportLabel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/21 16:54
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class ExpiryGvSummaryBean {

    @ExcelProperty(index = 0,value = "NO")
    private String no;

    @ExcelProperty(index = 1,value = "Voucher PROGRAM GROUP")
    private String voucherProgramGroup;

    @ExcelProperty(index = 2,value = "EXPIRY DATE")
    private String expiryDate;

    @ReportAmountValue
    @ReportLabel(dynamic = true,value = "{this#getFieldLabelName}", ignoreBlankLabelColumn = true)
    @ExcelProperty(index = 3, converter = ExportExcelNumberConverter.class)
    private String sumOfAmount1;

    @ReportAmountValue
    @ReportLabel(dynamic = true,value = "{this#getFieldLabelName}", ignoreBlankLabelColumn = true)
    @ExcelProperty(index = 4, converter = ExportExcelNumberConverter.class)
    private String sumOfAmount2;

    @ReportAmountValue
    @ReportLabel(dynamic = true,value = "{this#getFieldLabelName}", ignoreBlankLabelColumn = true)
    @ExcelProperty(index = 5, converter = ExportExcelNumberConverter.class)
    private String sumOfAmount3;

    @ReportAmountValue
    @ReportLabel(dynamic = true,value = "{this#getFieldLabelName}", ignoreBlankLabelColumn = true)
    @ExcelProperty(index = 6, converter = ExportExcelNumberConverter.class)
    private String sumOfAmount4;

    @ReportAmountValue
    @ReportLabel(dynamic = true,value = "{this#getFieldLabelName}", ignoreBlankLabelColumn = true)
    @ExcelProperty(index = 7, converter = ExportExcelNumberConverter.class)
    private String sumOfAmount5;

    @ReportAmountValue
    @ReportLabel(dynamic = true,value = "{this#getFieldLabelName}", ignoreBlankLabelColumn = true)
    @ExcelProperty(index = 8, converter = ExportExcelNumberConverter.class)
    private String sumOfAmount6;

    @ReportAmountValue
    @ReportLabel(dynamic = true,value = "{this#getFieldLabelName}", ignoreBlankLabelColumn = true)
    @ExcelProperty(index = 11, converter = ExportExcelNumberConverter.class)
    private String sumOfAmount7;

    @ReportAmountValue
    @ReportLabel(dynamic = true,value = "{this#getFieldLabelName}", ignoreBlankLabelColumn = true)
    @ExcelProperty(index = 12, converter = ExportExcelNumberConverter.class)
    private String sumOfAmount8;

    @ReportAmountValue
    @ReportLabel(dynamic = true,value = "{this#getFieldLabelName}", ignoreBlankLabelColumn = true)
    @ExcelProperty(index = 13, converter = ExportExcelNumberConverter.class)
    private String sumOfAmount9;

    @ReportAmountValue
    @ReportLabel(dynamic = true,value = "{this#getFieldLabelName}", ignoreBlankLabelColumn = true)
    @ExcelProperty(index = 14, converter = ExportExcelNumberConverter.class)
    private String sumOfAmount10;

    @ReportAmountValue
    @ReportLabel(dynamic = true,value = "{this#getFieldLabelName}", ignoreBlankLabelColumn = true)
    @ExcelProperty(index = 15, converter = ExportExcelNumberConverter.class)
    private String sumOfAmount11;

    @ReportAmountValue
    @ReportLabel(dynamic = true,value = "{this#getFieldLabelName}", ignoreBlankLabelColumn = true)
    @ExcelProperty(index = 16, converter = ExportExcelNumberConverter.class)
    private String sumOfAmount12;

    @ReportAmountValue
    @ReportLabel(dynamic = true,value = "{this#getFieldLabelName}", ignoreBlankLabelColumn = true)
    @ExcelProperty(index = 17, converter = ExportExcelNumberConverter.class)
    private String sumOfAmount13;

    @ReportAmountValue
    @ReportLabel(dynamic = true,value = "{this#getFieldLabelName}", ignoreBlankLabelColumn = true)
    @ExcelProperty(index = 18, converter = ExportExcelNumberConverter.class)
    private String sumOfAmount14;

    @ReportAmountValue
    @ReportLabel(dynamic = true,value = "{this#getFieldLabelName}", ignoreBlankLabelColumn = true)
    @ExcelProperty(index = 19, converter = ExportExcelNumberConverter.class)
    private String sumOfAmount15;

    @ReportAmountValue
    @ReportLabel(dynamic = true,value = "{this#getFieldLabelName}", ignoreBlankLabelColumn = true)
    @ExcelProperty(index = 20, converter = ExportExcelNumberConverter.class)
    private String sumOfAmount16;

    @ReportAmountValue
    @ReportLabel(dynamic = true,value = "{this#getFieldLabelName}", ignoreBlankLabelColumn = true)
    @ExcelProperty(index = 21, converter = ExportExcelNumberConverter.class)
    private String sumOfAmount17;

    @ReportAmountValue
    @ReportLabel(dynamic = true,value = "{this#getFieldLabelName}", ignoreBlankLabelColumn = true)
    @ExcelProperty(index = 22, converter = ExportExcelNumberConverter.class)
    private String sumOfAmount18;

    @ReportAmountValue
    @ReportLabel(dynamic = true,value = "{this#getFieldLabelName}", ignoreBlankLabelColumn = true)
    @ExcelProperty(index = 23, converter = ExportExcelNumberConverter.class)
    private String sumOfAmount19;

    @ReportAmountValue
    @ReportLabel(dynamic = true,value = "{this#getFieldLabelName}", ignoreBlankLabelColumn = true)
    @ExcelProperty(index = 24, converter = ExportExcelNumberConverter.class)
    private String sumOfAmount20;

    @ReportAmountValue
    @ReportLabel(dynamic = true,value = "{this#getFieldLabelName}", ignoreBlankLabelColumn = true)
    @ExcelProperty(index = 25, converter = ExportExcelNumberConverter.class)
    private String sumOfAmount21;

    @ReportAmountValue
    @ReportLabel(dynamic = true,value = "{this#getFieldLabelName}", ignoreBlankLabelColumn = true)
    @ExcelProperty(index = 26, converter = ExportExcelNumberConverter.class)
    private String sumOfAmount22;

    @ReportAmountValue
    @ReportLabel(dynamic = true,value = "{this#getFieldLabelName}", ignoreBlankLabelColumn = true)
    @ExcelProperty(index = 27, converter = ExportExcelNumberConverter.class)
    private String sumOfAmount23;

    @ReportAmountValue
    @ReportLabel(dynamic = true,value = "{this#getFieldLabelName}", ignoreBlankLabelColumn = true)
    @ExcelProperty(index = 28, converter = ExportExcelNumberConverter.class)
    private String sumOfAmount24;

    @ReportAmountValue
    @ReportLabel(dynamic = true,value = "{this#getFieldLabelName}", ignoreBlankLabelColumn = true)
    @ExcelProperty(index = 29, converter = ExportExcelNumberConverter.class)
    private String sumOfAmount25;

    @ReportAmountValue
    @ReportLabel(dynamic = true,value = "{this#getFieldLabelName}", ignoreBlankLabelColumn = true)
    @ExcelProperty(index = 30, converter = ExportExcelNumberConverter.class)
    private String sumOfAmount26;

}
