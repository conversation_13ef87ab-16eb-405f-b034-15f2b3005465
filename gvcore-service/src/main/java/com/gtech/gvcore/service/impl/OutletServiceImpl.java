package com.gtech.gvcore.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.gtech.basic.masterdata.web.dao.MasterDataDistrictMapper;
import com.gtech.basic.masterdata.web.entity.MasterDataDistrictEntity;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.redis.GTechRedisTemplate;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.cache.MasterDataCache;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.outlet.CreateOutletRequest;
import com.gtech.gvcore.common.request.outlet.DeleteOutletRequest;
import com.gtech.gvcore.common.request.outlet.GetOutletRequest;
import com.gtech.gvcore.common.request.outlet.QueryOutletByBusinessRequest;
import com.gtech.gvcore.common.request.outlet.QueryOutletByMerchantCodesRequest;
import com.gtech.gvcore.common.request.outlet.QueryOutletRequest;
import com.gtech.gvcore.common.request.outlet.ReplaceOutletCodeRequest;
import com.gtech.gvcore.common.request.outlet.UpdateOutletRequest;
import com.gtech.gvcore.common.request.outlet.UpdateOutletStatusRequest;
import com.gtech.gvcore.common.request.outletcpg.CreateOutletCpgRequest;
import com.gtech.gvcore.common.request.outletcpg.DeleteOutletCpgByOutletCodeRequest;
import com.gtech.gvcore.common.response.outlet.OutletCpgVo;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.common.response.outletcpg.OutletCpgResponse;
import com.gtech.gvcore.common.utils.BeanUtil;
import com.gtech.gvcore.dao.dto.OutletDto;
import com.gtech.gvcore.dao.dto.OutletIssuerNameInfo;
import com.gtech.gvcore.dao.mapper.OutletMapper;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dto.OutletMerchantNameInfo;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.CompanyService;
import com.gtech.gvcore.service.MerchantService;
import com.gtech.gvcore.service.OutletCpgService;
import com.gtech.gvcore.service.OutletService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
/**
 * <AUTHOR>
 * @Date 2022/2/17 15:48
 */
@Service
public class OutletServiceImpl implements OutletService {

    @Autowired
    private OutletMapper outletMapper;

    @Autowired
    private OutletCpgService outletCpgService;

    @Autowired
    private MasterDataDistrictMapper masterDataDistrictMapper;

    @Value("#{${gv.issuer.warehouse}}")
    private Map<String, String> issuerWarehouseMap;

    @Value("#{${gv.issuer.businesswarehouse}}")
    private Map<String, String> issuerBusinessWarehouseMap;


    @Autowired
    private GvCodeHelper codeHelper;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private CompanyService companyService;
    @Autowired
    private GTechRedisTemplate redisTemplate;

    @Autowired
    private MasterDataCache masterDataCache;

    @Transactional
    @Override
    public Result<String> createOutlet(CreateOutletRequest param) {
        Outlet entity = BeanCopyUtils.jsonCopyBean(param, Outlet.class);
		if (StringUtil.isEmpty(entity.getParentOutlet())) {
			entity.setParentOutlet(null);
		}
        entity.setCreateTime(new Date());
        entity.setOutletCode(codeHelper.generateOutletCode());
        //默认开启
        entity.setStatus(GvcoreConstants.STATUS_ENABLE);
        try {
            outletMapper.insertSelective(entity);
        } catch (DuplicateKeyException e) {
                return Result.failed(ResultErrorCodeEnum.OUTLET_CODE_ISSUER.code(),ResultErrorCodeEnum.OUTLET_CODE_ISSUER.desc());
        }

        //添加outletCpg
        for (String cpgCode : param.getCpgCode()) {
            CreateOutletCpgRequest request = BeanCopyUtils.jsonCopyBean(param, CreateOutletCpgRequest.class);
            request.setOutletCode(entity.getOutletCode());
            request.setCpgCode(cpgCode);
            request.setCreateUser(param.getCreateUser());
            outletCpgService.createOutletCpg(request);
        }
        masterDataCache.updateOutletCache(entity);
        return Result.ok(entity.getOutletCode());
    }

    @Transactional
    @Override
    public Result<Void> updateOutlet(UpdateOutletRequest param) {
    	
		Outlet entity = getOutletByCode(param.getOutletCode());
		if (entity == null) {
			return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
		}
		BeanUtil.copyPropertiesIgnoreNull(param, entity);
        entity.setUpdateTime(new Date());

        Example example = new Example(Outlet.class);
        example.createCriteria()
                .andEqualTo(Outlet.C_OUTLET_CODE, param.getOutletCode());
		if (StringUtil.isEmpty(entity.getParentOutlet())) {
			entity.setParentOutlet(null);
		}
        try {
			outletMapper.updateByCondition(entity, example);
        } catch (DuplicateKeyException e) {
                return Result.failed(ResultErrorCodeEnum.OUTLET_CODE_ISSUER.code(),ResultErrorCodeEnum.OUTLET_CODE_ISSUER.desc());
        }

        //先删除后修改
        if (CollectionUtils.isNotEmpty(param.getCpgCode())) {
            outletCpgService.deleteOutletCpgByOutletCode(DeleteOutletCpgByOutletCodeRequest.builder().outletCode(param.getOutletCode()).build());
            for (String cpgCode : param.getCpgCode()) {
                CreateOutletCpgRequest request = BeanCopyUtils.jsonCopyBean(param, CreateOutletCpgRequest.class);
                request.setOutletCode(entity.getOutletCode());
                request.setCpgCode(cpgCode);
                request.setCreateUser(param.getUpdateUser());
                outletCpgService.createOutletCpg(request);
            }
            masterDataCache.deleteOutletCpgCache(param.getOutletCode());
        }
        masterDataCache.updateOutletCache(entity);
        return Result.ok();
    }

    @Override
    public Result<Void> deleteOutlet(DeleteOutletRequest param) {
        Example example = new Example(Outlet.class);
        example.createCriteria()
                .andEqualTo(Outlet.C_OUTLET_CODE, param.getOutletCode());
        outletMapper.deleteByCondition(example);
        outletCpgService.deleteOutletCpgByOutletCode(DeleteOutletCpgByOutletCodeRequest.builder().outletCode(param.getOutletCode()).build());
        return Result.ok();
    }

    @Override
    public PageResult<OutletResponse> queryOutletList(QueryOutletRequest param) {
        String outletTypes = param.getOutletType();
        StringBuilder outletType = new StringBuilder();
        if (StringUtils.isNotEmpty(outletTypes)) {
            String[] split = outletTypes.split(",");
            for (String s : split) {
                outletType.append("'").append(s).append("',");
            }
            outletType.deleteCharAt(outletType.lastIndexOf(","));
        }
        param.setOutletType(outletType.toString());
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        List<OutletResponse> gvOutletEntities = outletMapper.queryOutletList(param);


        //查询省市区
        Map<String, String> districtMap = getDistrictMap(gvOutletEntities);

        PageInfo<OutletResponse> info = PageInfo.of(gvOutletEntities);

        List<String> outletCodeList = info.getList().stream().map(OutletResponse::getOutletCode).collect(Collectors.toList());

        //设置cpg
        List<OutletCpgResponse> response = outletCpgService.queryOutletCpgListByOutletList(outletCodeList);
        Map<String, List<OutletCpgResponse>> outletCpgMap = response.stream().collect(Collectors.groupingBy(OutletCpgResponse::getOutletCode));


        for (OutletResponse outletResponse : info.getList()) {
            //设置省市区
            outletResponse.setStateName(districtMap.get(outletResponse.getStateCode()));
            outletResponse.setCityName(districtMap.get(outletResponse.getCityCode()));
            outletResponse.setDistrictName(districtMap.get(outletResponse.getDistrictCode()));
            List<OutletCpgResponse> outletCpgResponses = outletCpgMap.get(outletResponse.getOutletCode());
            if (CollectionUtils.isEmpty(outletCpgResponses)) {
                continue;
            }
            List<OutletCpgVo> cpgs = BeanCopyUtils.jsonCopyList(outletCpgResponses, OutletCpgVo.class);
            outletResponse.setCpg(cpgs);
        }

        return new PageResult<>(BeanCopyUtils.jsonCopyList(info.getList(), OutletResponse.class), info.getTotal());
    }





    @Override
    public OutletResponse getOutlet(GetOutletRequest param) {
        OutletResponse outlet = outletMapper.getOutlet(param);
        if (null == outlet) {
            throw new GTechBaseException(ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.code(),ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.desc());
        }

        List<OutletCpgResponse> response = outletCpgService.queryOutletCpgListByOutlet(param.getOutletCode());
        if (CollectionUtils.isNotEmpty(response)) {
            List<OutletCpgVo> cpgs = BeanCopyUtils.jsonCopyList(response, OutletCpgVo.class);
            outlet.setCpg(cpgs);
        }


        ArrayList<OutletResponse> outletResponses = Lists.newArrayList(outlet);
        try {
            getAddress(outlet, outletResponses);
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        return BeanCopyUtils.jsonCopyBean(outlet, OutletResponse.class);
    }

    @Override
    public OutletResponse getOutletByOutletName(String outletName) {

        //queryOutletByOutletName

        //Outlet outlet = outletMapper.selectOne(Outlet.builder().outletName(outletName).issuerCode(issuerCode).build());
        Outlet outlet = outletMapper.selectOne(Outlet.builder().outletName(outletName).build());

        return BeanCopyUtils.jsonCopyBean(outlet, OutletResponse.class);
    }

    private void getAddress(OutletResponse outlet, ArrayList<OutletResponse> outletResponses) {
        //查询省市区
        Map<String, String> districtMap = getDistrictMap(outletResponses);
        outlet.setStateName(districtMap.get(outlet.getStateCode()));
        outlet.setCityName(districtMap.get(outlet.getCityCode()));
        outlet.setDistrictName(districtMap.get(outlet.getDistrictCode()));
    }


    public Map<String, String> getDistrictMap(List<OutletResponse> item) {

        if (org.springframework.util.CollectionUtils.isEmpty(item)) {
            return new HashMap<>(1);
        }


        Set<String> stateCodeSet = item.stream().map(OutletResponse::getStateCode).collect(Collectors.toSet());
        Set<String> cityCodeSet = item.stream().map(OutletResponse::getCityCode).collect(Collectors.toSet());
        Set<String> districtCodeSet = item.stream().map(OutletResponse::getDistrictCode).collect(Collectors.toSet());


        Set<String> codesSet = Stream.of(stateCodeSet, cityCodeSet, districtCodeSet).flatMap(Collection::stream).collect(Collectors.toSet());

        Example example = new Example(MasterDataDistrictEntity.class, true, false);
        example.createCriteria().andIn(Outlet.C_DISTRICT_CODE, codesSet);

        List<MasterDataDistrictEntity> districtEntities = masterDataDistrictMapper.selectByExample(example);


        return org.springframework.util.CollectionUtils.isEmpty(districtEntities) ? new HashMap<>(1) : districtEntities.stream().collect(Collectors.toMap(MasterDataDistrictEntity::getDistrictCode, MasterDataDistrictEntity::getDistrictName));
    }


    @Override
    public Result<Void> updateOutletStatus(UpdateOutletStatusRequest param) {
        Outlet entity = BeanCopyUtils.jsonCopyBean(param, Outlet.class);
        entity.setUpdateTime(new Date());

        Example example = new Example(Outlet.class);
        example.createCriteria()
                .andEqualTo(Outlet.C_OUTLET_CODE, param.getOutletCode());

        outletMapper.updateByConditionSelective(entity, example);
        Outlet outlet = outletMapper.selectOne(new Outlet().setOutletCode(param.getOutletCode()));
        masterDataCache.updateOutletCache(outlet);
        return Result.ok();
    }


	/**
	 * 包含chlidList
	 */
    @Override
    public PageResult<OutletResponse> queryOutletByMerchantCodes(QueryOutletByMerchantCodesRequest request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        Example example = new Example(Outlet.class);
		example.createCriteria().andIn(Outlet.C_MERCHANT_CODE, request.getMerchantCodes()).andIsNull(Outlet.C_PARENT_OUTLET);
        List<Outlet> outlet = outletMapper.selectByCondition(example);
        PageInfo<Outlet> info = PageInfo.of(outlet);
		List<OutletResponse> outletList = BeanCopyUtils.jsonCopyList(info.getList(), OutletResponse.class);
		if (!CollectionUtils.isEmpty(outletList)) {
			Example exampleAll = new Example(Outlet.class);
			exampleAll.createCriteria().andIn(Outlet.C_MERCHANT_CODE, request.getMerchantCodes()).andIsNotNull(Outlet.C_PARENT_OUTLET);
			List<Outlet> outletAllList = outletMapper.selectByCondition(exampleAll);
			Map<String, List<Outlet>> outletMap = outletAllList.stream().collect(Collectors.groupingBy(Outlet::getParentOutlet));
			makeChildList(outletList, outletMap);
		}
		return new PageResult<>(outletList, info.getTotal());
    }

	private void makeChildList(List<OutletResponse> outletList, Map<String, List<Outlet>> outletMap) {
		outletList.forEach(vo -> {
			String outletCode = vo.getOutletCode();
			List<Outlet> childList = outletMap.get(outletCode);
			if (!CollectionUtils.isEmpty(childList)) {
				vo.setChildList(BeanCopyUtils.jsonCopyList(childList, OutletResponse.class));
				makeChildList(vo.getChildList(), outletMap);
			}
		});
	}

	@Override
	public List<Outlet> queryOutletByMerchantCodes(List<String> merchantCodeList) {
		if (CollectionUtils.isEmpty(merchantCodeList)) {
			return Collections.emptyList();
		}
		Example example = new Example(Outlet.class);
		example.createCriteria().andIn(Outlet.C_MERCHANT_CODE, merchantCodeList);
		return outletMapper.selectByCondition(example);
	}

    @Override
    public List<OutletResponse> queryOutletByCodes(List<String> codeList) {
        Example example = new Example(Outlet.class);
        example.createCriteria().andIn(Outlet.C_OUTLET_CODE, codeList);
        List<Outlet> list = outletMapper.selectByCondition(example);
        return BeanCopyUtils.jsonCopyList(list, OutletResponse.class);
    }

	@Override
	public List<Outlet> queryOutletByNames(List<String> nameList) {

        //MER-1884
        Example example = new Example(Outlet.class);
		example.createCriteria().andIn(Outlet.C_OUTLET_NAME, nameList);
		return outletMapper.selectByCondition(example);
	}

    @Override
    public Outlet queryByOutletCode(String outletCode) {

        if (StringUtils.isBlank(outletCode)) {
            return null;
        }

        Outlet outlet = new Outlet();
        outlet.setOutletCode(outletCode);
        return outletMapper.selectOne(outlet);
    }

    @Override
    public List<Outlet> queryByOutletCodeList(List<String> outletCodeList) {
        
        if (CollectionUtils.isEmpty(outletCodeList)) return Collections.emptyList();

        Example example = new Example(Outlet.class);
        example.createCriteria().andIn(Outlet.C_OUTLET_CODE, outletCodeList);

        List<Outlet> list = outletMapper.selectByCondition(example);
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        return list;
    }

    @Override
    public Map<String, Outlet> queryOutletMapByOutletCodeList(List<String> outletCodeList) {

        List<Outlet> list = queryByOutletCodeList(outletCodeList);
        if (list.isEmpty()) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(Outlet::getOutletCode, v -> v));
    }

    @Override
    public List<OutletIssuerNameInfo> queryOutletIssuerNameInfo(OutletDto dto) {

        List<OutletIssuerNameInfo> list = outletMapper.queryOutletIssuerNameInfo(dto);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }

    @Override
    public Result<List<OutletResponse>> queryOutletByBusinessType(QueryOutletByBusinessRequest queryOutletByBusinessRequest) {
        QueryOutletRequest queryOutletRequest = new QueryOutletRequest();
        queryOutletRequest.setIssuerCode(queryOutletByBusinessRequest.getIssuerCode());
        queryOutletRequest.setOutletName(queryOutletByBusinessRequest.getOutletName());
        queryOutletRequest.setPageSize(100);
        PageResult<OutletResponse> outletResponsePageResult = queryOutletList(queryOutletRequest);
        List<OutletResponse> list = outletResponsePageResult.getData().getList();
        List<OutletResponse> collect = list.stream().filter(outletResponse -> !outletResponse.getOutletCode().equals(issuerWarehouseMap.get(queryOutletByBusinessRequest.getIssuerCode()))).collect(Collectors.toList());
        if (queryOutletByBusinessRequest.getBusinessType().equals("request") || queryOutletByBusinessRequest.getFromOrTo().equals("to")) {
            return Result.ok(collect);
        } else {
            List<OutletResponse> collect1 = collect.stream().filter(outletResponse -> !outletResponse.getOutletCode().equals(issuerBusinessWarehouseMap.get(queryOutletByBusinessRequest.getIssuerCode()))).collect(Collectors.toList());
            return Result.ok(collect1);
        }

    }

    @Override
    public Map<String, OutletMerchantNameInfo> queryOutletMerchantName(List<String> outletCodeList) {

        if (CollectionUtils.isEmpty(outletCodeList)) {
            return Collections.emptyMap();
        }

        List<OutletMerchantNameInfo> list = outletMapper.queryOutletMerchantName(outletCodeList);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(OutletMerchantNameInfo::getOutletCode, v -> v));
    }

    @Override
    public List<String> queryAllOutletCodeByCompanyOrMerchant(List<String> companyCodeList,
            List<String> merchantCodeList) {

        if (CollectionUtils.isEmpty(companyCodeList) && CollectionUtils.isEmpty(merchantCodeList)) {
            return Collections.emptyList();
        }

        OutletDto dto = new OutletDto();
        dto.setCompanyCodeList(companyCodeList);
        dto.setMerchantCodeList(companyCodeList);
        List<String> list = outletMapper.queryAllOutletCodeByCompanyOrMerchant(dto);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }

    @Override
    public Outlet getOutletByCode(String outletCode) {

        if (StringUtils.isBlank(outletCode)) return null;

        return outletMapper.selectOne(Outlet.builder().outletCode(outletCode).build());
    }

	public List<Outlet> queryAllOutLet() {
		return outletMapper.selectAll();
	}


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateOutletCodeScript(ReplaceOutletCodeRequest outlet) {

//        Outlet oldOutlet = outletMapper.getOutletInfo(outlet.getOldOutletCode());
//        Outlet newOutlet = outletMapper.getOutletInfo(outlet.getNewOutletCode());
//
//
//        if (ObjectUtils.isEmpty(oldOutlet) || ObjectUtils.isEmpty(newOutlet))
//            return Result.failed("未查询到传入的outlet信息");
//        if (!StringUtils.equals(oldOutlet.getOutletType(), newOutlet.getOutletType()))
//            return Result.failed("outletType不一致");
//        if (StringUtils.equals(oldOutlet.getOutletCode(), newOutlet.getOutletCode()))
//            return Result.failed("禁止传入两个相同的outletCode");
//
//
//        List<String> allTableNames = outletMapper.selectAllTables()
//                .stream().filter(table -> table.startsWith("gv_")
//                        && !StringUtils.equals("gv_merchant", table)
//                        && !StringUtils.equals("gv_issuer", table)
//                        && !StringUtils.equals("gv_transaction_data", table)
//                        && !StringUtils.equals("gv_voucher", table)
//                        && !StringUtils.equals("gv_voucher_log", table)
//                ).collect(Collectors.toList());
//
//
//        String[] statements = {"outlet_code", "voucher_owner_code", "outbound_code", "inbound_code", "business_code",
//                "actual_outlet", "receiver_code"};
//        log.info("outlet ===> 开始交换outletCode数据");
//
//        final String unknownColumn = "Unknown column";
//        final String rollBack = "outlet ===> 数据更新出错，即将回滚";
//
//        for (String tableName : allTableNames) {
//
//
//            if (StringUtils.equals(tableName, "gv_outlet")) {
//
//                int update = 0;
//                update += outletMapper.updateParentCode(oldOutlet, newOutlet);
//                update += outletMapper.updateOutletParentCode(oldOutlet, newOutlet);
//
//                if (update > 0)
//                    log.info("表 {} 更新字段 parent_outlet 成功，值 {} 与 值 {} 交换数据 {} 条", tableName, oldOutlet.getOutletCode(), newOutlet.getOutletCode(), update);
//
//
//            } else {
//
//                int merchantIsReplace = 0;
//                int issuerIsReplace = 0;
//
//                for (String statement : statements) {
//
//                    int update = 0;
//
//
//                    try {
//
//                        update += outletMapper.updateOutletCode(tableName, statement, oldOutlet, newOutlet);
//
//
//                        if (update > 0) {
//
//                            if (!StringUtils.equals(oldOutlet.getIssuerCode(), newOutlet.getIssuerCode()) && issuerIsReplace == 0) {
//                                try {
//                                    int issuerReplaceNum = outletMapper.updateIssuerCode(tableName, statement, oldOutlet, newOutlet);
//                                    if (issuerReplaceNum > 0) {
//                                        issuerIsReplace = 1;
//                                        log.info("表 {} 更新字段 issuer_code，值 {} 与 值 {} 交换数据 {} 条", tableName, oldOutlet.getIssuerCode(), newOutlet.getIssuerCode(), issuerReplaceNum);
//                                    }
//                                } catch (Exception e) {
//                                    if (!e.getMessage().contains(unknownColumn)) {
//                                        e.printStackTrace();
//                                        log.error(rollBack);
//                                        throw new GvcoreUnknownException();
//                                    }
//                                }
//                            }
//
//
//                            if (!StringUtils.equals(oldOutlet.getMerchantCode(), newOutlet.getMerchantCode()) && merchantIsReplace == 0) {
//                                try {
//                                    int merchantReplaceNum = outletMapper.updateMerchantCode(tableName, statement, oldOutlet, newOutlet);
//                                    if (merchantReplaceNum > 0) {
//                                        merchantIsReplace = 1;
//                                        log.info("表 {} 更新字段 merchant_code，值 {} 与 值 {} 交换数据 {} 条", tableName, oldOutlet.getMerchantCode(), newOutlet.getMerchantCode(), merchantReplaceNum);
//                                    }
//                                } catch (Exception e) {
//                                    if (!e.getMessage().contains(unknownColumn)) {
//                                        e.printStackTrace();
//                                        log.error(rollBack);
//                                        throw new GvcoreUnknownException();
//                                    }
//                                }
//                            }
//
//                            String oldColum = oldOutlet.getOutletCode();
//                            String newColum = newOutlet.getOutletCode();
//                            log.info("表 {} 更新字段 {}，值 {} 与 值 {} 交换数据 {} 条", tableName, statement, oldColum, newColum, update);
//
//                        }
//
//                    } catch (Exception e) {
//
//                        if (!e.getMessage().contains(unknownColumn)) {
//                            e.printStackTrace();
//                            log.error(rollBack);
//                            throw new GvcoreUnknownException();
//                        }
//
//                    }
//                }
//            }
//        }
//        log.info("outlet ===> OutletCode所有数据更新完成");
//        log.info("outlet ===> 开始替换OutletName数据");
//        log.info("表 gv_voucher_allocation 更新字段 voucher_owner_name，值 {} 与 值 {} 交换数据 {} 条",
//                oldOutlet.getOutletName(), newOutlet.getOutletName(), outletMapper.updateVoucherAllocationVoucherOwnerName(newOutlet, oldOutlet));
//
//        log.info("表 gv_voucher_allocation 更新字段 receiver_name，值 {} 与 值 {} 交换数据 {} 条",
//                oldOutlet.getOutletName(), newOutlet.getOutletName(), outletMapper.updateVoucherAllocationReceiverName(newOutlet, oldOutlet));
//
//        log.info("表 gv_voucher_receive 更新字段 out_bound，值 {} 与 值 {} 交换数据 {} 条",
//                oldOutlet.getOutletName(), newOutlet.getOutletName(), outletMapper.updateVoucherReceiveOutBound(newOutlet, oldOutlet));
//
//        log.info("表 gv_voucher_receive 更新字段 in_bound，值 {} 与 值 {} 交换数据 {} 条",
//                oldOutlet.getOutletName(), newOutlet.getOutletName(), outletMapper.updateVoucherReceiveInBound(newOutlet, oldOutlet));
//
//        log.info("表 gv_voucher_request 更新字段 voucher_owner_name，值 {} 与 值 {} 交换数据 {} 条",
//                oldOutlet.getOutletName(), newOutlet.getOutletName(), outletMapper.updateVoucherRequestVoucherOwnerName(newOutlet, oldOutlet));
//
//        log.info("表 gv_voucher_request 更新字段 receiver_name，值 {} 与 值 {} 交换数据 {} 条",
//                oldOutlet.getOutletName(), newOutlet.getOutletName(), outletMapper.updateVoucherRequestReceiverName(newOutlet, oldOutlet));
//
//        log.info("outlet ===> OutletName所有数据更新完成");
//
        return Result.ok();
    }

    @Override
    public Result<Void> checkColumWithOutlet() {

        List<String> allTableNames = outletMapper.selectAllTables()
                .stream().filter(table -> table.startsWith("gv_")
                        && !StringUtils.equals("gv_merchant", table)
                        && !StringUtils.equals("gv_issuer", table)
                        && !StringUtils.equals("gv_transaction_data", table)
                        && !StringUtils.equals("gv_voucher_01", table)
                        && !StringUtils.equals("gv_voucher", table)
                        && !StringUtils.equals("gv_voucher_log", table)
                        && !StringUtils.equals("gv_sys_logger", table)
                ).collect(Collectors.toList());

        for (String allTableName : allTableNames) {
            List<String> columns = outletMapper.selectAllColumns(allTableName);
            for (String column : columns) {
                try {
                    if (!column.contains("time")) {
                        int count = outletMapper.selectCountWhereColumnWithOutlet(allTableName, column);
                        if (count > 0) log.info("Outlet查询结果: " + allTableName + "." + column + " 是OutletName");
                    }
                } catch (IndexOutOfBoundsException e) {
                    e.printStackTrace();
                }
            }
        }

        return Result.ok();
    }

}
