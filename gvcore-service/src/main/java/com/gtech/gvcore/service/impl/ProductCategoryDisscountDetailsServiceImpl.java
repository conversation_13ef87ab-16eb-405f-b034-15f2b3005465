package com.gtech.gvcore.service.impl;

import java.util.Collections;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.utils.UUIDUtils;
import com.gtech.gvcore.dao.mapper.ProductCategoryDisscountDetailsMapper;
import com.gtech.gvcore.dao.model.ProductCategoryDisscountDetails;
import com.gtech.gvcore.service.ProductCategoryDisscountDetailsService;

/**
 * <AUTHOR>
 * @date 2022年2月16日
 */
@Service
public class ProductCategoryDisscountDetailsServiceImpl implements ProductCategoryDisscountDetailsService {

    @Autowired
    private ProductCategoryDisscountDetailsMapper productCategoryDisscountDetailsMapper;

    @Override
    public List<ProductCategoryDisscountDetails> queryByProductCategoryDisscountCode(
            String productCategoryDisscountCode, Integer deleteStatus) {

        if (StringUtils.isBlank(productCategoryDisscountCode)) {
            return Collections.emptyList();
        }

        ProductCategoryDisscountDetails productCategoryDisscountDetail = new ProductCategoryDisscountDetails();
        productCategoryDisscountDetail.setProductCategoryDisscountCode(productCategoryDisscountCode);
        productCategoryDisscountDetail.setDeleteStatus(deleteStatus);
        List<ProductCategoryDisscountDetails> details = productCategoryDisscountDetailsMapper
                .select(productCategoryDisscountDetail);
        if (CollectionUtils.isEmpty(details)) {
            return Collections.emptyList();
        }
        return details;
    }

    @Transactional
    @Override
    public int insertList(List<ProductCategoryDisscountDetails> list) {

        for (ProductCategoryDisscountDetails detail : list) {
            detail.setProductCategoryDisscountDetailsCode(UUIDUtils.generateCode());
            detail.setDeleteStatus(GvcoreConstants.DELETE_STATUS_DISABLE);
        }

        return productCategoryDisscountDetailsMapper.insertList(list);
    }

    @Transactional
    @Override
    public int updateById(ProductCategoryDisscountDetails detail) {
        detail.setDeleteStatus(GvcoreConstants.DELETE_STATUS_DISABLE);
        return productCategoryDisscountDetailsMapper.updateById(detail);
    }

    @Transactional
    @Override
    public int deleteStatusByPrimaryKey(ProductCategoryDisscountDetails detail) {
        detail.setStatus(GvcoreConstants.STATUS_DISABLE);
        detail.setDeleteStatus(GvcoreConstants.DELETE_STATUS_ENABLE);
        return productCategoryDisscountDetailsMapper.updateByPrimaryKeySelective(detail);
    }

}
