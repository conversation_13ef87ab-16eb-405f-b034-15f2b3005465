package com.gtech.gvcore.service.report.impl;

import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Company;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.ReportParamConvertHelper;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.GcRedemptionSummaryBean;
import com.gtech.gvcore.service.report.impl.bo.GcRedemptionBo;
import com.gtech.gvcore.service.report.impl.bo.GcRedemptionSummaryStatisticalBo;
import com.gtech.gvcore.service.report.impl.param.RedemptionQueryData;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 14:00
 * @Description:
 * @Version 1.1 3/
 */
@Service
public class GcRedemptionSummaryImpl extends ReportSupport
        implements BusinessReport<RedemptionQueryData, GcRedemptionSummaryBean>, SingleReport {

    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_REDEMPTION_SUMMARY_REPORT;
    }

    @Override
    public RedemptionQueryData builderQueryParam(CreateReportRequest reportRequest) {

        ReportParamConvertHelper.convertQueryDateCompanyCodeToMerchantCode(reportRequest);

        RedemptionQueryData redemptionQueryData = new RedemptionQueryData();

        //transaction
        redemptionQueryData.setTransactionDateEnd(reportRequest.getTransactionDateEnd());
        redemptionQueryData.setTransactionDateStart(reportRequest.getTransactionDateStart());

        // issuer merchant outlet
        redemptionQueryData.setMerchantCodeList(reportRequest.getMerchantCodes());
        redemptionQueryData.setOutletCodeList(reportRequest.getOutletCodes());

        //cpg
        redemptionQueryData.setCpgCodeList(reportRequest.getCpgCodes());

        //voucher code
        redemptionQueryData.setVoucherCodeNumStart(reportRequest.getVoucherCodeNumStart());
        redemptionQueryData.setVoucherCodeNumEnd(reportRequest.getVoucherCodeNumEnd());

        //invoice number
        redemptionQueryData.setInvoiceNumber(reportRequest.getInvoiceNo());

        return redemptionQueryData;
    }

    @Override
    public List<GcRedemptionSummaryBean> getExportData(RedemptionQueryData queryData) {

        Collection<GcRedemptionSummaryStatisticalBo> boList = this.queryBoList(queryData);

        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(boList, GcRedemptionSummaryStatisticalBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(boList, GcRedemptionSummaryStatisticalBo::getOutletCode, Outlet.class);
        final JoinDataMap<GcCpg> cpgMap = super.getMapByCode(boList, GcRedemptionSummaryStatisticalBo::getCpgCode, GcCpg.class);
        final JoinDataMap<Company> companyMap = super.getMapByCode(merchantMap.values(), Merchant::getCompanyCode, Company.class);

        List<GcRedemptionSummaryBean> collect = boList.stream()
                .map(e -> new GcRedemptionSummaryBean()
                        .setDate(e.getTransactionDateDay())
                        .setMerchantName(merchantMap.findValue(e.getMerchantCode()).getMerchantName())
                        .setMerchantOutletName(outletMap.findValue(e.getOutletCode()).getOutletName())
                        .setInvoiceNumber(e.getInvoiceNumber())
                        .setVoucherProgramGroup(cpgMap.findValue(e.getCpgCode()).getCpgName())
                        .setRedemptionCount(String.valueOf(e.getRedemptionCount()))
                        .setRedemptionAmount(super.toAmount(e.getAmount()))
                        .setSbuCompanyName(companyMap.findValue(merchantMap.findValue(e.getMerchantCode()).getCompanyCode()).getCompanyName())
                        .setNotes(e.getNotes())

                ).sorted(Comparator.comparing(GcRedemptionSummaryBean::getDate).reversed()).collect(Collectors.toList());
        GcRedemptionSummaryBean gcRedemptionSummaryBean = new GcRedemptionSummaryBean();
        gcRedemptionSummaryBean.setDate("Total");
        gcRedemptionSummaryBean.setRedemptionCount(boList.stream().map(GcRedemptionSummaryStatisticalBo::getRedemptionCount).reduce(0, Integer::sum).toString());
        gcRedemptionSummaryBean.setRedemptionAmount(boList.stream().map(GcRedemptionSummaryStatisticalBo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add).toString());
        collect.add(gcRedemptionSummaryBean);
        return collect;
    }

    private Collection<GcRedemptionSummaryStatisticalBo> queryBoList(RedemptionQueryData queryData) {

        // 查询最新并且是兑换的交易
        final List<GcRedemptionBo> redemptionBoList = new ArrayList<>();
        PollPageHelper.pollSelect(gcReportBusinessMapper::selectGcRedemption, queryData, redemptionBoList::addAll);

        //根据条件分组
        return redemptionBoList.stream()
                .map(GcRedemptionSummaryStatisticalBo::convert)
                .collect(Collectors.toMap(
                                // key
                                GcRedemptionSummaryStatisticalBo::getGroupKey,
                                // value
                                GcRedemptionSummaryStatisticalBo::newInstance,
                                // merge
                                GcRedemptionSummaryStatisticalBo::merge)
                        //to map
                ).values();
    }
}
