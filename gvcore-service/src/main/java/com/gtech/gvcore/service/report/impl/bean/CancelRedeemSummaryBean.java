package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/21 18:32
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class CancelRedeemSummaryBean {

    /**
     * gv_transaction_data 表中 merchant_code 对应的 Merchant Name
     */
    @ExcelProperty(value = "Merchant")
    private String merchant;

    /**
     * gv_transaction_data 表中 outlet_code 对应的 Outlet Name
     */
    @ExcelProperty(value = "Outlet")
    private String outlet;

    /**
     * gv_transaction_data 表中 cpg_code 对应的 Cpg(Vpg) Name
     */
    @ExcelProperty(value = "Voucher Program Group")
    private String voucherProgramGroup;

    /**
     * 该CPG被cancel redeem的数量  根据条件从 gv_transaction_data group by cpg_code 后count
     */
    @ExcelProperty(value = "Number of Vouchers", converter = ExportExcelNumberConverter.class)
    private String numberOfVouchers;

    /**
     * 该CPG被cancel redeem的总面额 根据条件从 gv_transaction_data group by cpg_code 后 sum 面额
     */
    @ReportAmountValue
    @ExcelProperty(value = "Total Amount", converter = ExportExcelNumberConverter.class)
    private String totalAmount;
}
