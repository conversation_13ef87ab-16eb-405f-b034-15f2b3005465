package com.gtech.gvcore.service.report.export.snapshoot.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * ReportLabel
 * 报表label注解
 * 标明报表具体的表头
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ReportLabel {

    /**
     *
     * 表头值
     * 支持多表头 多表头则根据表头层级排列 越前的表头值越优先渲染
     *   [动态表头]
     *   使用需要开启 dynamic = true
     *      [从入参中取值]
     *          该方式会直接从requestReportParam 或者 结果(ResultBean)的第一个对象中获取 字段冲突优先取入参
     *          {paramA}
     *      [执行方法取值] !!!!!!!!!!!! 该方法不支持嵌套调用 dynamicFormat 请一次性处理结束 !!!!!!!!!!!!!!!
     *          {this#methodName}
     *          {springBean#methodName}
     *          使用该参数对方法签名有所要求 支持
     *          空参 与 OrderReport ResultBean ReportLabelContextBean String 的任意组装
     * @see
     *              public String methodName();
     *              public String methodName(OrderReport);
     *              public String methodName(ResultBean); # ResultBean结果中的第一个对象
     *              public String methodName(OrderReport, ResultBean); # ResultBean结果中的第一个对象
     *              public String methodName(ReportLabelContextBean);
     *
     */
    String[] value() default {};

    /**
     * 动态表头 启用/禁用
     */
    boolean dynamic() default false;


    /**
     * 动态表头格式化
     *  this#methodName 执行当前业务报表对象的方法
     *  springBean#methodName 执行spring容器内对象的方法
     *  methodName 执行当前业务报表对象的方法
     * 使用该参数对方法签名有所要求
     *  public String methodName(? param);
     *
     */
    String dynamicFormat() default "";


    /**
     * 操作符号
     */
    ActionEnum action() default ActionEnum.NOT_ACTION;

    /**
     * 是否忽略空白 label 列
     * @return
     */
    boolean ignoreBlankLabelColumn() default false;

    /**
     * 与前端约定的事件操作符
     */
    enum ActionEnum {
        NOT_ACTION, // 无操作
        FIND_SYSTEM_LOGGER, //查询系统日志
        ; // 查询系统日志
        public String toResult() {
            if (this == NOT_ACTION) return null;
            return super.toString();
        }
    }

}
