package com.gtech.gvcore.service.report.impl.param;

import com.gtech.commons.page.PageParam;
import com.gtech.gvcore.service.report.ReportQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
public class BulkOrderDetailsQueryData extends PageParam implements ReportQueryParam {

	private List<String> issuerCodeList;

	private List<String> outletCodeList;

	/**
	 * 操作起始时间
	 */
	private Date transactionDateStart;

	/**
	 * 操作截止时间
	 */
	private Date transactionDateEnd;

	// merchantCodeList - 已被outletCodeList覆盖
	// super.issuerCodeList - issuerCode集
	// super.outletCodeList - outlet集

	/**
	 * VPG 集
	 */
	private List<String> cpgCodeList;

	// -----------------筛选 cpg_code------------------

	/**
	 * 券编码起始
	 */
	private String voucherNumberStart;

	/**
	 * 券编码截止
	 */
	private String voucherNumberEnd;

	// -----------------筛选 cpg_code------------------


	/**
	 * 发票编码
	 */
	private String invoiceNo;

	/**
	 * 客户类型
	 */
	private String customerType;

	/**
	 * 客户编码集
	 */
	private List<String> customerCodeList;

	/**
	 * 订单号
	 */
	private String purchaseOrderNumber;

	/**
	 * 订单状态集
	 */
	private List<String> orderStatusList;

	// -----------------筛选 cpg_code------------------

	/**
	 * 券有效期起始值
	 */
	private Date expiryStatusStart;

	/**
	 * 券有效期截止值
	 */
	private Date expiryStatusEnd;

	// -----------------筛选 cpg_code------------------

}

