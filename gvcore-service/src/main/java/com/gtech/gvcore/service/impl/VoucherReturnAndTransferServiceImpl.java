package com.gtech.gvcore.service.impl;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ApproveNodeRecordTypeEnum;
import com.gtech.gvcore.common.enums.ApproveTypeEnum;
import com.gtech.gvcore.common.enums.FlowNodeEnum;
import com.gtech.gvcore.common.enums.MessageEnventEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.enums.VoucherAllocationBusinessTypeEnum;
import com.gtech.gvcore.common.enums.VoucherRequestStatusEnum;
import com.gtech.gvcore.common.request.allocation.AllocateRequest;
import com.gtech.gvcore.common.request.flow.SendEmailRequest;
import com.gtech.gvcore.common.request.outlet.GetOutletRequest;
import com.gtech.gvcore.common.request.releaseapprove.ApproveNodeRecordRequest;
import com.gtech.gvcore.common.request.releaseapprove.ReleaseApproveAbleRequest;
import com.gtech.gvcore.common.request.voucherrequest.CreateVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.UpdateVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherreturnantransfer.QueryVoucherReturnAndTransferRequest;
import com.gtech.gvcore.common.request.voucherreturnantransfer.ReturnAndTransferRequest;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.common.response.voucherrequest.QueryVoucherRequestResponse;
import com.gtech.gvcore.dao.mapper.VoucherRequestMapper;
import com.gtech.gvcore.dao.model.ApproveNodeRecord;
import com.gtech.gvcore.dao.model.VoucherAllocation;
import com.gtech.gvcore.dao.model.VoucherRequest;
import com.gtech.gvcore.dto.QueryVoucherRequestPermissionRequest;
import com.gtech.gvcore.service.CpgService;
import com.gtech.gvcore.service.GvUserAccountService;
import com.gtech.gvcore.service.OutletService;
import com.gtech.gvcore.service.ReleaseApproveService;
import com.gtech.gvcore.service.VoucherAllocationService;
import com.gtech.gvcore.service.VoucherRequestService;
import com.gtech.gvcore.service.VoucherReturnAndTransferService;
import com.gtech.gvcore.threadpool.ThreadPoolCenter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.util.StringUtil;

import java.util.Arrays;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/26 14:45
 */

@Service
public class VoucherReturnAndTransferServiceImpl implements VoucherReturnAndTransferService {

    @Autowired
    private VoucherRequestService voucherRequestService;
    @Autowired
    private VoucherRequestMapper voucherRequestMapper;
    @Autowired
    private ReleaseApproveService releaseApproveService;
    @Autowired
    private VoucherAllocationService voucherAllocationService;

	@Autowired
	private GvUserAccountService userAccountService;

	@Autowired
	private MessageComponent messageComponent;

	@Autowired
	private OutletService outletService;

	@Autowired
    private CpgService cpgService;

    @Lazy
    @Autowired
    private VoucherReturnAndTransferService voucherReturnAndTransferService;


    @Override
    public Result<String> addVoucherRequest(CreateVoucherRequestRequest createVoucherRequestRequest) {

        /*for (CreateVoucherRequestDetailsRequest detailsRequest : createVoucherRequestRequest.getDetailsRequests()) {
            GetCpgRequest cpgRequest = new GetCpgRequest();
            cpgRequest.setCpgCode(detailsRequest.getVpgCode());
            Result<GetCpgResponse> cpg = cpgService.getCpg(cpgRequest);
            if (cpg.getData().getDisableGeneration().equals(VpgDisableGenerationEnum.DISABLED.code())){
                return Result.failed("This vpg cannot be used to generate coupons and sales");
            }
        }*/



        Result<String> result = voucherRequestService.addVoucherRequest(createVoucherRequestRequest, "return");
        if(StringUtils.isNotBlank(result.getData())) {
			autoApprove(result.getData());
        }
        return result;
    }

	@Override
	public Result<Void> updateVoucherRequest(UpdateVoucherRequestRequest updateVoucherRequestRequest) {

		Result<Void> result = voucherRequestService.updateVoucherRequest(updateVoucherRequestRequest);
		String requestCode = updateVoucherRequestRequest.getVoucherRequestCode();
		autoApprove(requestCode);

		return result;
	}

	private void autoApprove(String requestCode) {
		VoucherRequest request = voucherRequestService.queryByVoucherRequestCode(requestCode);
		if (request != null) {
			ApproveNodeRecordRequest approveNodeRecordRequest = new ApproveNodeRecordRequest();
			approveNodeRecordRequest.setBusinessCode(request.getVoucherRequestCode());
			approveNodeRecordRequest.setVoucherAmount(request.getVoucherAmount());
			approveNodeRecordRequest.setReleaseApproveAmountType(ApproveTypeEnum.APPROVE_TYPE_02.getType());
			approveNodeRecordRequest.setReleaseType(ApproveNodeRecordTypeEnum.APPROVE.getType());
			approveNodeRecordRequest.setIssuerCode(request.getIssuerCode());
			Map<String, Object> params = voucherRequestService.getExtendparams(request);
			approveNodeRecordRequest.setExtendParams(params);
			ThreadPoolCenter.commonThreadPoolExecute(() -> releaseApproveService.automaticApproveAndNoticeNextNode(approveNodeRecordRequest,
					v -> voucherReturnAndTransferService.approveVoucherReturnAndTransfer(v)));
		}
	}

    @Override
    public PageResult<QueryVoucherRequestResponse> queryVoucherRequest(
            QueryVoucherReturnAndTransferRequest queryRequest) {

        QueryVoucherRequestPermissionRequest queryVoucherRequestRequest = new QueryVoucherRequestPermissionRequest();
        queryVoucherRequestRequest.setHasApprovePermission(queryRequest.getHasApprovePermission());
        queryVoucherRequestRequest.setUserCode(queryRequest.getUserCode());
        queryVoucherRequestRequest.setPageNum(queryRequest.getPageNum());
        queryVoucherRequestRequest.setPageSize(queryRequest.getPageSize());
        queryVoucherRequestRequest.setOutletCode(queryRequest.getOutletCode());
        queryVoucherRequestRequest.setDenomination(queryRequest.getDenomination());
        queryVoucherRequestRequest.setIssuerCode(queryRequest.getIssuerCode());
        if(StringUtils.isNotBlank(queryRequest.getShowStatus())) {
            if (queryRequest.getShowStatus()
                    .equalsIgnoreCase(GvcoreConstants.RETURN + VoucherRequestStatusEnum.PENDING_ALLOCATION.getCode())) {
                queryVoucherRequestRequest.setBusinessType(GvcoreConstants.RETURN);
                queryVoucherRequestRequest.setStatus(VoucherRequestStatusEnum.PENDING_ALLOCATION.getCode());
            } else if (queryRequest.getShowStatus().equalsIgnoreCase(
                    GvcoreConstants.TRANSFER + VoucherRequestStatusEnum.PENDING_ALLOCATION.getCode())) {
                queryVoucherRequestRequest.setBusinessType(GvcoreConstants.TRANSFER);
                queryVoucherRequestRequest.setStatus(VoucherRequestStatusEnum.PENDING_ALLOCATION.getCode());
            }else {
                queryVoucherRequestRequest.setStatus(Integer.parseInt(queryRequest.getShowStatus()));
            }
        }
        
        PageResult<QueryVoucherRequestResponse> pageResult = voucherRequestService
                .queryVoucherRequest(queryVoucherRequestRequest);
        for (QueryVoucherRequestResponse response : pageResult.getData().getList()) {
            response.setApproveAble(false);
            if (VoucherRequestStatusEnum.PENDING_APPROVAL.getCode() == response.getStatus()) {
                ReleaseApproveAbleRequest build = ReleaseApproveAbleRequest.builder()
						.issuerCode(queryRequest.getIssuerCode())
                        .releaseType(ApproveNodeRecordTypeEnum.APPROVE.getType())
                        .approveRoleCode(queryRequest.getRoleList()).approveUser(queryRequest.getUserCode())
                        .voucherAmount(response.getVoucherAmount())
						.releaseApproveAmountType(ApproveTypeEnum.APPROVE_TYPE_02.getType())
                        .businessCode(response.getVoucherRequestCode()).build();

                Result<Integer> booleanResult = releaseApproveService.approveAble(build);
                response.setApproveAble(booleanResult.isSuccess());
                if (booleanResult.getData() > 0) {
                    ApproveNodeRecordRequest approveNodeRecordRequest = new ApproveNodeRecordRequest();
                    approveNodeRecordRequest.setBusinessCode(response.getVoucherRequestCode());
                    approveNodeRecordRequest.setVoucherAmount(response.getVoucherAmount());
                    approveNodeRecordRequest.setReleaseApproveAmountType(ApproveTypeEnum.APPROVE_TYPE_02.getType());
                    approveNodeRecordRequest.setReleaseType(ApproveNodeRecordTypeEnum.APPROVE.getType());
					approveNodeRecordRequest.setIssuerCode(queryRequest.getIssuerCode());
                    ThreadPoolCenter.commonThreadPoolExecute(
                            () -> releaseApproveService.automaticApproveAndNoticeNextNode(approveNodeRecordRequest,
                                    v -> voucherReturnAndTransferService.approveVoucherReturnAndTransfer(v)));
                }
            }
        }

        return pageResult;
    }

    @Override
    public Result<Boolean> approveVoucherReturnAndTransferAble(ReleaseApproveAbleRequest releaseApproveAbleRequest) {
        VoucherRequest voucherRequest = voucherRequestMapper.selectOne(new VoucherRequest(releaseApproveAbleRequest.getBusinessCode()));
        if (voucherRequest == null) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }
		releaseApproveAbleRequest.setReleaseApproveAmountType(ApproveTypeEnum.APPROVE_TYPE_02.getType());
        releaseApproveAbleRequest.setVoucherAmount(voucherRequest.getVoucherAmount());
        releaseApproveAbleRequest.setReleaseType(ApproveNodeRecordTypeEnum.APPROVE.getType());
		releaseApproveAbleRequest.setIssuerCode(voucherRequest.getIssuerCode());
        Result<Integer> result = releaseApproveService.approveAble(releaseApproveAbleRequest);
        return Result.ok(result.isSuccess());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<String> approveVoucherReturnAndTransfer(ApproveNodeRecordRequest approveNodeRecordRequest) {
        VoucherRequest voucherRequest = voucherRequestMapper.selectOne(new VoucherRequest(approveNodeRecordRequest.getBusinessCode()));
		Map<String, Object> params = voucherRequestService.getExtendparams(voucherRequest);
		approveNodeRecordRequest.setExtendParams(params);
		approveNodeRecordRequest.setReleaseApproveAmountType(ApproveTypeEnum.APPROVE_TYPE_02.getType());
        approveNodeRecordRequest.setVoucherAmount(voucherRequest.getVoucherAmount());
        approveNodeRecordRequest.setReleaseType(ApproveNodeRecordTypeEnum.APPROVE.getType());
		approveNodeRecordRequest.setIssuerCode(voucherRequest.getIssuerCode());
        Result<ApproveNodeRecord> approve = releaseApproveService.approve(approveNodeRecordRequest);
        ApproveNodeRecord approveNodeRecord = approve.getData();
        if (approveNodeRecord == null) {
            return Result.failed(approve.getCode(), approve.getMessage());
        }
        if (approveNodeRecord.getNextRoleCode() == null) {
            if (Boolean.TRUE.equals(approveNodeRecordRequest.getStatus())) {
                voucherRequest.setStatus(VoucherRequestStatusEnum.PENDING_ALLOCATION.getCode());
            } else {
                voucherRequest.setStatus(VoucherRequestStatusEnum.REJECTED.getCode());
            }
            voucherRequest.setUpdateTime(new Date());
            voucherRequest.setUpdateUser(approveNodeRecordRequest.getApproveUser());
            voucherRequestMapper.updateByPrimaryKey(voucherRequest);
			sendEmail(approveNodeRecordRequest, voucherRequest);

        }
        if (VoucherRequestStatusEnum.PENDING_ALLOCATION.getCode() == voucherRequest.getStatus()) {
            voucherAllocationService.createByVoucherRequest(voucherRequest);
        }
        return Result.ok(approveNodeRecord.getApproveNodeRecordCode());
    }

	void sendEmail(ApproveNodeRecordRequest approveNodeRecordRequest, VoucherRequest voucherRequest) {
		Boolean status = approveNodeRecordRequest.getStatus();
		String businessType = voucherRequest.getBusinessType();
		String createUser = voucherRequest.getCreateUser();
		String flowNode = FlowNodeEnum.APPROVE.getDesc();
		if (Boolean.FALSE.equals(status)) {
			flowNode = FlowNodeEnum.REJECTED.getDesc();
		}
		Map<String, Object> extendParam = approveNodeRecordRequest.getExtendParams();
		SendEmailRequest sendEmailRequest = new SendEmailRequest();
		sendEmailRequest.setExtendParams(extendParam);
		extendParam.put("flowNode", flowNode);
		if (businessType.equals(GvcoreConstants.RETURN) || (businessType.equals(GvcoreConstants.TRANSFER) && Boolean.FALSE.equals(status))) {
			String email = userAccountService.getUserEmail(createUser);
			sendEmailRequest.setEmails(Arrays.asList(email));
			messageComponent.sendEmail(sendEmailRequest, MessageEnventEnum.REJECT_VOUCHER_RETURN_TRANSFER.getCode());
		} else {
			String ownerCode = voucherRequest.getVoucherOwnerCode();
			OutletResponse outletResponse = outletService.getOutlet(GetOutletRequest.builder().outletCode(ownerCode).build());
			if (outletResponse == null || StringUtil.isEmpty(outletResponse.getEmail())) {
				return;
			}
			sendEmailRequest.setEmails(Arrays.asList(outletResponse.getEmail().split(",")));
			messageComponent.sendEmail(sendEmailRequest, MessageEnventEnum.APPROVE_VOUCHER_RETURN_TRANSFER.getCode());
		}
    }

    @Override
    public Result<Void> returnAndTransfer(ReturnAndTransferRequest returnAndTransferRequest) {
        VoucherRequest voucherRequest = voucherRequestMapper.selectOne(VoucherRequest.builder().voucherRequestCode(returnAndTransferRequest.getVoucherRequestCode()).build());
        VoucherAllocation voucherAllocationByCode;
        if (voucherRequest.getBusinessType().equals(GvcoreConstants.RETURN)) {
            voucherAllocationByCode = voucherAllocationService.getAllocationBySourceDataCode(returnAndTransferRequest.getVoucherRequestCode(), VoucherAllocationBusinessTypeEnum.RETURN.code());
        } else {
            voucherAllocationByCode = voucherAllocationService.getAllocationBySourceDataCode(returnAndTransferRequest.getVoucherRequestCode(), VoucherAllocationBusinessTypeEnum.TRANSFER.code());
        }

        if (voucherAllocationByCode == null || voucherAllocationByCode.getVoucherAllocationCode() == null) {
            return Result.failed(ResultErrorCodeEnum.REQUEST_NO_APPROVE.code(), ResultErrorCodeEnum.REQUEST_NO_APPROVE.desc());
        }
        AllocateRequest allocateRequest = new AllocateRequest();
        allocateRequest.setVoucherAllocationCode(voucherAllocationByCode.getVoucherAllocationCode());
        allocateRequest.setUpdateUser(returnAndTransferRequest.getUser());
        allocateRequest.setVoucherBatchList(returnAndTransferRequest.getVoucherBatchList());
        return voucherAllocationService.allocate(allocateRequest);
    }
}
