package com.gtech.gvcore.service.report.extend.poll;

import com.gtech.commons.page.PageParam;
import com.gtech.gvcore.helper.GvPageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.RowBounds;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BinaryOperator;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * @ClassName FindNextPageHelper
 * @Description
 * <AUTHOR>
 * @Date 2023/3/23 15:37
 * @Version V1.0
 **/
@Slf4j
public class PollPageHelper {

    public static final int PAGE_SIZE = 1000;

    /**
     * 轮询查询
     *
     * @param selectFunction 查询方法
     * @param param          查询参数
     * @param map            每一页查询结果处理 当存在该function时不产出结果集
     * @param <R>            结果集类型
     * @param <P>            查询参数类型
     */
    public static <R, P extends PageParam> void pollSelect(MapperFunction<R, P> selectFunction, P param, final Consumer<List<R>> map) {

        pollSelect(selectFunction, param, map, PAGE_SIZE);
    }

    /**
     * 轮询查询
     *
     * @param selectFunction 查询方法
     * @param param          查询参数
     * @param map            每一页查询结果处理 当存在该function时不产出结果集
     * @param pageSize       每页大小
     * @param <R>            结果集类型
     * @param <P>            查询参数类型
     */
    public static <R, P extends PageParam> void pollSelect(final MapperFunction<R, P> selectFunction, final P param, final Consumer<List<R>> map, final int pageSize) {

        // 设置分页参数
        param.setPageSize(pageSize);
        param.setPageNum(0);

        // 轮询查询
        for (List<R> boList = poll(selectFunction, param); CollectionUtils.isNotEmpty(boList); boList = poll(selectFunction, param)) {

            // 当页结果处理
            map.accept(boList);
        }
    }

    /**
     * 轮询分组查询
     *
     * @param selectFunction 查询方法
     * @param param          查询参数
     * @param merge          数据合并方法
     * @param groupKey       分组key
     * @param <R>            结果集类型
     * @param <P>            查询参数类型
     * @return 结果集
     */
    public static <R, P extends PageParam> Collection<R> pollGroupSelect(final MapperFunction<R, P> selectFunction, P param, final BinaryOperator<R> merge, final Function<R, String> groupKey) {

        return pollGroupSelect(selectFunction, param, merge, groupKey, PAGE_SIZE);
    }

    /**
     * 轮询分组查询
     *
     * @param selectFunction 查询方法
     * @param param          查询参数
     * @param merge          数据合并方法
     * @param groupKey       分组key
     * @param pageSize       每页大小
     * @param <R>            结果集类型
     * @param <P>            查询参数类型
     * @return 结果集
     */
    public static <R, P extends PageParam> Collection<R> pollGroupSelect(final MapperFunction<R, P> selectFunction, final P param, final BinaryOperator<R> merge, final Function<R, String> groupKey, final int pageSize) {

        // 设置分页参数
        param.setPageSize(pageSize);
        param.setPageNum(0);

        // 结果
        final Map<String, R> result = new HashMap<>();

        // 轮询查询
        for (List<R> boList = poll(selectFunction, param); CollectionUtils.isNotEmpty(boList); boList = poll(selectFunction, param)) {

            group(merge, groupKey, result, boList);

        }

        // 返回结果
        return result.values();
    }

    /**
     * 分组合并
     * @param merge 数据合并方法
     * @param groupKey 分组key
     * @param boList 数据集
     * @return 分组合并后的数据集
     * @param <R> 结果集类型
     */
    public static <R> Collection<R> group(final BinaryOperator<R> merge, final Function<R, String> groupKey, final List<R> boList) {

        final Map<String, R> result = new HashMap<>();

        group(merge, groupKey, result, boList);

        return result.values();
    }

    /**
     * 分组合并
     * @param merge 数据合并方法
     * @param groupKey 分组key
     * @param result 结果集
     * @param boList 数据集
     * @param <R> 结果集类型
     */
    private static <R> void group(final BinaryOperator<R> merge, final Function<R, String> groupKey, final Map<String, R> result, final List<R> boList) {

        // 分组合并
        boList.forEach(e -> {

            // 分组key
            String key = groupKey.apply(e);

            //如果存在则合并
            if (result.containsKey(key)) result.put(key, merge.apply(e, result.get(key)));
                //不存在则新增
            else result.put(key, e);

        });

    }

    /**
     * 轮询查询结果
     * 根据结果的卡券编号获取其最新的一行交易记录 卡券唯一
     *
     * @param selectFunction 查询方法
     * @param param          查询参数
     * @param <R>            结果集类型
     * @param <P>            查询参数类型
     * @return 结果中卡券最新的一行交易记录
     */
    public static <R extends GroupNewTransactionByVoucherCodeSupport, P extends PageParam> Collection<R> pollGroupNewTransactionByCodeSelect(
            final MapperFunction<R, P> selectFunction, final P param) {

        return PollPageHelper.pollGroupSelect(selectFunction, param
                , (v1, v2) -> {

                    // 如果v1为空则返回v2
                    if (null == v1) return v2;

                    // 如果v2为空则返回v1
                    if (null == v2) return v1;

                    // 如果v1的交易号大于v2的交易号则返回v1
                    if (v1.getTransactionNumber().compareTo(v2.getTransactionNumber()) > 0) return v1;

                    // 否则返回v2
                    return v2;
                }
                , GroupNewTransactionByVoucherCodeSupport::getVoucherCode);
    }

    /**
     * @param selectFunction 查询方法
     * @param param          查询参数
     * @param <B>            结果集类型
     * @param <P>            查询参数类型
     * @return 结果集
     */
    private static <B, P extends PageParam> List<B> poll(final MapperFunction<B, P> selectFunction,final P param) {

        log.info("poll page helper [poll] paramSize:{}, pageNumber:{}", param.getPageSize(), param.getPageNum());

        // 设置分页参数
        param.setPageNum(param.getPageNum() + 1);

        // 查询
        return selectFunction.select(param, GvPageHelper.getRowBounds(param));
    }

    /**
     * 查询方法
     *
     * @param <T> 结果集类型
     * @param <P> 查询参数类型
     */
    public interface MapperFunction<T, P extends PageParam> {

        List<T> select(final P param, final RowBounds rowBounds);
    }


}
