package com.gtech.gvcore.service.report.impl.param;

import com.gtech.gvcore.service.report.ReportQueryParam;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @ClassName GcRegenerateActivationCodeQueryData
 * @Description Gift Card Regenerate Activation Code Query Data
 * <AUTHOR> based on RegenerateActivationCodeQueryDate
 * @Date 2025/6/19
 * @Version V1.0
 **/
@Data
public class GcRegenerateActivationCodeQueryData implements ReportQueryParam {

    /**
     * Request Number
     */
    private String issueHandlingCode;

    /**
     * Request Date Range
     */
    private Date startDate;
    private Date endDate;

    /**
     * Gift Card Number Range
     */
    private Long startCardNumber;
    private Long endCardNumber;

    /**
     * Customer Email
     */
    private String customerEmail;

    /**
     * Gift Card Codes from CSV Upload
     */
    private List<String> cardNumbers;
    private String fileName;
    private List<String> status;

    private List<String> cpgCodes;
}
