package com.gtech.gvcore.service.report.impl.bo;

import cn.hutool.core.util.RandomUtil;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.service.report.extend.poll.GroupNewTransactionByVoucherCodeSupport;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/6/22 10:28
 */
@Getter
@Setter
@Accessors(chain = true)
public class GcRedemptionBo implements GroupNewTransactionByVoucherCodeSupport {

    private String transactionDate;
    private String cardNumber;
    private String merchantCode;
    private String outletCode;
    private String cpgCode;
    private String transactionType;
    private BigDecimal denomination;
    private String invoiceNumber;
    private BigDecimal amount;
    private BigDecimal balanceBefore;
    private BigDecimal balanceAfter;
    private String approvalCode;
    private String subCompanyName;

    public Date getTransactionDate() {

        return DateUtil.parseDate(transactionDate, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

    public BigDecimal getTransactionNumber() {
        return RandomUtil.randomBigDecimal();
    }

    @Override
    public String getVoucherCode() {
        return cardNumber;
    }
}
