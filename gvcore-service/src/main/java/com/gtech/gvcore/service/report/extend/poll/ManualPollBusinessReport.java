package com.gtech.gvcore.service.report.extend.poll;

import com.gtech.commons.page.PageParam;
import com.gtech.gvcore.helper.GvPageHelper;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.PollReport;
import com.gtech.gvcore.service.report.ReportQueryParam;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.RowBounds;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * @ClassName ManualPollBusinessReport
 * @Description manual poll report
 * <AUTHOR>
 * @Date 2023/2/3 18:36
 * @Version V1.0
 * @param <P> query param type
 * @param <R> export data type
 * @param <B> business data type
 */
public abstract class ManualPollBusinessReport<P extends ReportQueryParam, R, B> extends ReportSupport implements PollReport, BusinessReport<P, R> {

    /**
     * 查询数据
     * @param param
     * @param rowBounds
     * @return
     */
    protected abstract List<B> findDate(P param, RowBounds rowBounds);

    /**
     * 过滤数据
     * @param boList
     * @return
     */
    public abstract List<B> filter(P param, List<B> boList);

    /**
     * 组装数据
     * @param param
     * @param boList
     * @return
     */
    protected abstract List<R> getExportData(P param, List<B> boList);

    /**
     *
     * @param context
     */
    @Override
    public final void builder(ReportContext context) {

        //get param
        @SuppressWarnings("unchecked") final P reportParam = (P) context.getQueryParam();
        //get page param
        final PageParam pageParam = this.getPageParam(reportParam);

        //exist date
        boolean existDate = false;

        //for
        for (int pageNum = 1; pageNum < this.safetyNumber(); pageNum++) { //NOSONAR 警告内容为循环中不应该多次使用控制语句但此处为了代码简洁 忽略该错误

            //log
            LoggerFactory.getLogger(PollReport.class).info("builderReportByContextFindNextReport executing. orderReportCode:{} , current page:{}", context.getReportCode(), pageNum);
            pageParam.setPageNum(pageNum);

            //find
            final List<B> boList = this.findDate(reportParam, GvPageHelper.getRowBounds(pageParam));

            // termination
            if (this.termination(boList)) break;

            // filter
            final List<B> filterBoList = this.filter(reportParam, boList);
            if (CollectionUtils.isEmpty(filterBoList)) continue;

            // add data
            final List<?> dataList = this.getExportData(reportParam, boList);
            if (CollectionUtils.isNotEmpty(dataList)) {
                context.appendDate(dataList);
                existDate = true;
            }
        }

        //no data
        if (!existDate) context.noData();

        //data finish
        this.dataFinish(context);
    }

    @Override
    public final List<R> getExportData(P param) {

        throw new UnsupportedOperationException();
    }

}
