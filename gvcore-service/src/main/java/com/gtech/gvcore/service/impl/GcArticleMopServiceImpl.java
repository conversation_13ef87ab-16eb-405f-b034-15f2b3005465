package com.gtech.gvcore.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.cache.MasterDataCache;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.articlemop.CreateArticleMopRequest;
import com.gtech.gvcore.common.request.articlemop.QueryArticleMopsByPageRequest;
import com.gtech.gvcore.common.request.articlemop.UpdateArticleMopRequest;
import com.gtech.gvcore.common.request.articlemop.UpdateArticleMopStatusRequest;
import com.gtech.gvcore.common.response.articlemop.CreateArticleMopResponse;
import com.gtech.gvcore.common.response.articlemop.QueryArticleMopsByPageResponse;
import com.gtech.gvcore.common.utils.UUIDUtils;
import com.gtech.gvcore.dao.mapper.GcArticleMopMapper;
import com.gtech.gvcore.dao.model.GcArticleMop;
import com.gtech.gvcore.service.GcArticleMopService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * gc商品支付方式关联Service实现
 */
@Service
public class GcArticleMopServiceImpl implements GcArticleMopService {

    @Autowired
    private GcArticleMopMapper gcArticleMopMapper;

    @Autowired
    private MasterDataCache masterDataCache;

    @Override
    public Result<String> createArticleMop(CreateArticleMopRequest request) {
        GcArticleMop gcArticleMop = new GcArticleMop();
        gcArticleMop.setArticleCode(request.getArticleCode());
        gcArticleMop.setMopCode(request.getMopCode());

        BeanUtils.copyProperties(request, gcArticleMop);
        gcArticleMop.setArticleMopCode(UUIDUtils.generateCode());
        gcArticleMop.setStatus(GvcoreConstants.STATUS_ENABLE);
        gcArticleMop.setCreateTime(new Date());
        try {
            gcArticleMopMapper.insertSelective(gcArticleMop);
        } catch (DuplicateKeyException e) {
            return Result.failed(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                    ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
        }

        CreateArticleMopResponse response = new CreateArticleMopResponse();
        response.setArticleMopCode(gcArticleMop.getArticleMopCode());
        return Result.ok(gcArticleMop.getArticleMopCode());
    }

    @Override
    public Result<Void> updateArticleMop(UpdateArticleMopRequest request) {
        GcArticleMop gcArticleMop = gcArticleMopMapper.selectByPrimaryKey(request.getId());
        if (gcArticleMop == null) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }

        BeanUtils.copyProperties(request, gcArticleMop);
        gcArticleMop.setUpdateTime(new Date());
        int i = gcArticleMopMapper.updateByPrimaryKey(gcArticleMop);
        if (i == 0) {
            return Result.failed(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(),
                    ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.desc());
        }

        return Result.ok();
    }

    @Override
    public Result<Void> updateArticleMopStatus(UpdateArticleMopStatusRequest request) {
        GcArticleMop gcArticleMop = new GcArticleMop();
        gcArticleMop.setId(request.getId());
        int i = gcArticleMopMapper.selectCount(gcArticleMop);
        if (i == 0) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }

        gcArticleMop.setStatus(request.getStatus());
        gcArticleMop.setUpdateUser(request.getUpdateUser());
        gcArticleMop.setUpdateTime(new Date());
        i = gcArticleMopMapper.updateByPrimaryKeySelective(gcArticleMop);
        if (i == 0) {
            return Result.failed(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(),
                    ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.desc());
        }
        masterDataCache.updateGcArticleMopCache(gcArticleMop);

        return Result.ok();
    }

    @Override
    public PageResult<QueryArticleMopsByPageResponse> queryArticleMopsByPage(QueryArticleMopsByPageRequest request) {
        GcArticleMop gcArticleMop = new GcArticleMop();
        gcArticleMop.setArticleCodeName(request.getArticleCodeName());

        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<GcArticleMop> list = gcArticleMopMapper.selectSelective(gcArticleMop);
        PageInfo<GcArticleMop> pageInfo = new PageInfo<>(list);

        List<QueryArticleMopsByPageResponse> responses = new ArrayList<>(request.getPageSize());
        list.forEach(item -> {
            QueryArticleMopsByPageResponse response = new QueryArticleMopsByPageResponse();
            BeanUtils.copyProperties(item, response);
            responses.add(response);
        });

        return PageResult.ok(responses, pageInfo.getTotal());
    }

    @Override
    public QueryArticleMopsByPageResponse getArticleMop(String articleMopCode) {
        if (StringUtils.isBlank(articleMopCode)) {
            return null;
        }
        
        GcArticleMop gcArticleMop = new GcArticleMop();
        gcArticleMop.setArticleMopCode(articleMopCode);
        GcArticleMop result = gcArticleMopMapper.selectOne(gcArticleMop);
        if (result == null) {
            return null;
        }
        QueryArticleMopsByPageResponse response = new QueryArticleMopsByPageResponse();
        BeanUtils.copyProperties(result, response);
        return response;
    }

    @Override
    public GcArticleMop queryByArticleMopCode(String articleMopCode) {
        if (StringUtils.isBlank(articleMopCode)) {
            return null;
        }
        
        GcArticleMop gcArticleMop = new GcArticleMop();
        gcArticleMop.setArticleMopCode(articleMopCode);
        return gcArticleMopMapper.selectOne(gcArticleMop);
    }

    @Override
    public Map<String, GcArticleMop> queryByArticleMopCodeList(List<String> articleMopCodeList) {
        if(CollectionUtils.isEmpty(articleMopCodeList)) {
            return Collections.emptyMap();
        }
        
        List<GcArticleMop> list = gcArticleMopMapper.queryByArticleMopCodeList(articleMopCodeList);
        if(CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(GcArticleMop :: getArticleMopCode, Function.identity(), (o1, o2) -> o1));
    }
} 