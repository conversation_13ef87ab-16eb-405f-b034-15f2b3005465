package com.gtech.gvcore.service.report.impl.param;

import com.gtech.commons.page.PageParam;
import com.gtech.gvcore.service.report.ReportQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
public class BulkOrderQueryData extends PageParam implements ReportQueryParam {

	private List<String> issuerCodeList;

	private List<String> outletCodeList;

	/**
	 * 操作起始时间
	 */
	private Date transactionDateStart;

	/**
	 * 操作截止时间
	 */
	private Date transactionDateEnd;

	// super.issuerCodeList - issuerCode集
	// super.outletCodeList - outlet集

	/**
	 * VPG 集
	 */
	private List<String> cpgCodeList;

	/**
	 * 发票编码
	 */
	private String invoiceNo;

	/**
	 * 客户类型
	 */
	private String customerType;

	/**
	 * 客户编码集
	 */
	private List<String> customerCodeList;

	/**
	 * 订单号
	 */
	private String purchaseOrderNumber;

	/**
	 * 订单状态集
	 */
	private List<String> orderStatusList;

	/**
	 * 非页面参数,通过 cpgCodeList 筛选出来的 customer_order_code 参数集
	 */
	private List<String> customerOrderCodeList;

}

