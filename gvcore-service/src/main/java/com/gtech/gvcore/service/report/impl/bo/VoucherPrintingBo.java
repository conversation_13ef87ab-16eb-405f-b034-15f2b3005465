package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.utils.AmountUtils;
import com.gtech.gvcore.dao.model.UserAccount;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * @ClassName VoucherPrintingBo
 * @Description
 * <AUTHOR>
 * @Date 2023/2/13 18:28
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class VoucherPrintingBo {

    // issuer code
    private String issuerCode;

    // cpg code
    private String cpgCode;

    // purchase order no
    private String purchaseOrderNo;

    // voucher batch code
    private String voucherBatchCode;

    // create time
    private String createTime;

    // create user
    private String createUser;

    // printer code
    private String printerCode;

    // effective date
    private String voucherEffectiveDate;

    // booklet start no
    private String bookletStartNo;

    // booklet end no
    private String bookletEndNo;

    // booklet num
    private String bookletNum;

    // voucher start no
    private String voucherStartNo;

    // voucher end no
    private String voucherEndNo;

    // voucher num
    private String voucherNum;

    // status
    private String status;

    public Date getCreateTime() {

        // return create time (Date)
        return DateUtil.parseDate(createTime, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

    /**
     * get create by
     * @param userMap user map
     * @return create by (first name + last name)
     */
    public String getCreateBy(JoinDataMap<UserAccount> userMap) {

        // find non value
        UserAccount user = userMap.findValue(createUser);

        // create by (first name + last name)
        return StringUtils.join(new String[] {user.getFirstName(), user.getLastName()}, " ");
    }

    /**
     * get booklet number
     * @return booklet number start ~ end (number)
     */
    public String getBookletNumber() {

        // empty
        if (bookletStartNo == null || bookletEndNo == null) {
            return StringUtils.EMPTY;
        }

        // booklet number start ~ end (number)
        return bookletStartNo + " ~ " + bookletEndNo + " (" + bookletNum + ")";
    }

    /**
     * get voucher number
     * @return voucher number start ~ end (number)
     */
    public String getVoucherNumber() {

        // empty
        if (voucherStartNo == null || voucherEndNo == null) {
            return StringUtils.EMPTY;
        }

        // voucher number start ~ end (number)
        return voucherStartNo + " ~ " + voucherEndNo + " (" + AmountUtils.idrCommaFormat(voucherNum) + ")";
    }

}
