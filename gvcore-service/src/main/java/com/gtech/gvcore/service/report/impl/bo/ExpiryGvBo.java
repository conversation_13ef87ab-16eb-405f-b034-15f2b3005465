package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.gvcore.service.report.extend.poll.GroupNewTransactionByVoucherCodeSupport;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @ClassName ExpiryGvBo
 * @Description expiry gv bo
 * <AUTHOR>
 * @Date 2023/4/11 16:26
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class ExpiryGvBo implements GroupNewTransactionByVoucherCodeSupport {

    private String voucherCode;

    private String transactionType;

    private String transactionCode;

    private BigDecimal transactionNumber;

    public void setTransactionCode(String transactionCode) {
        this.transactionNumber = new BigDecimal(transactionCode.substring(3));
        this.transactionCode = transactionCode;
    }
}
