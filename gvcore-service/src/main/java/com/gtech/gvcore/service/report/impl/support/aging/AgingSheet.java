package com.gtech.gvcore.service.report.impl.support.aging;

import com.alibaba.excel.write.handler.CellWriteHandler;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
public class AgingSheet {

    private List<Data> sheetData = new ArrayList<>();

    private Object head;

    private String sheetName;

    private CellWriteHandler cellWriteHandler;

    public AgingSheet addSheetData(String key, ReportExportTypeEnum reportExportTypeEnum, List<?> data) {

        this.sheetData.add(new Data(key, reportExportTypeEnum, data));
        return this;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Data {

        private String fillKey;

        private ReportExportTypeEnum reportExportTypeEnum;

        private List<?> list;

    }

}
