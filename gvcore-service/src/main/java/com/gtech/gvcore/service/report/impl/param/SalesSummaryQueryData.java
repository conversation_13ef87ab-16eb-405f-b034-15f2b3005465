package com.gtech.gvcore.service.report.impl.param;

import com.gtech.commons.page.PageParam;
import com.gtech.gvcore.service.report.ReportQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年6月22日
 */
@Getter
@Setter
@Accessors(chain = true)
public class SalesSummaryQueryData extends PageParam implements ReportQueryParam {
    
    private Date transactionDateStart;

    private Date transactionDateEnd;

    private String issuerCode;

    private List<String> merchantCodeList;

    private List<String> outletCodeList;

    private List<String> cpgCodeList;

    private String invoiceNumber;

    private String purchaseOrderNo;

    private List<String> orderStatusList;

    private List<String> transactionTypes;

}
