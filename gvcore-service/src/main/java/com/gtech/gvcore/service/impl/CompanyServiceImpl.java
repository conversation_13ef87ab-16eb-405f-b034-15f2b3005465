package com.gtech.gvcore.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.company.CreateCompanyRequest;
import com.gtech.gvcore.common.request.company.DeleteCompanyRequest;
import com.gtech.gvcore.common.request.company.GetCompanyRequest;
import com.gtech.gvcore.common.request.company.QueryCompanyByIssuerCodesRequest;
import com.gtech.gvcore.common.request.company.QueryCompanyRequest;
import com.gtech.gvcore.common.request.company.UpdateCompanyRequest;
import com.gtech.gvcore.common.request.company.UpdateCompanyStatusRequest;
import com.gtech.gvcore.common.request.issuer.GetIssuerRequest;
import com.gtech.gvcore.common.response.company.CompanyResponse;
import com.gtech.gvcore.common.response.issuer.IssuerResponse;
import com.gtech.gvcore.common.response.issuer.PermissionTreeResponse;
import com.gtech.gvcore.dao.mapper.CompanyMapper;
import com.gtech.gvcore.dao.model.Company;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.CompanyService;
import com.gtech.gvcore.service.IssuerService;
import com.gtech.gvcore.service.MerchantService;
import com.gtech.gvcore.service.OutletService;

import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @Date 2022/2/17 13:34
 */
@Service
public class CompanyServiceImpl implements CompanyService {


    @Autowired
    private CompanyMapper companyMapper;

    @Autowired
    private GvCodeHelper codeHelper;

    @Autowired
    private IssuerService issuerService;

	@Autowired
	private MerchantService merchantService;

	@Autowired
	private OutletService outletService;

    @Override
    public Result<String> createCompany(CreateCompanyRequest param) {
        Company entity = BeanCopyUtils.jsonCopyBean(param, Company.class);
        entity.setCompanyCode(codeHelper.generateCompanyCode());
        entity.setCreateTime(new Date());
        //默认开启
        entity.setStatus(GvcoreConstants.STATUS_ENABLE);

        try {
            //companyName 唯一
            companyMapper.insertSelective(entity);
        } catch (DuplicateKeyException e) {
            return Result.failed(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                    ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
        }
        return Result.ok(entity.getCompanyCode());

    }

    @Override
    public Result<Void> updateCompany(UpdateCompanyRequest param) {
        Company entity = BeanCopyUtils.jsonCopyBean(param, Company.class);
        entity.setUpdateTime(new Date());

        Example example = new Example(Company.class);
        example.createCriteria()
                .andEqualTo(Company.C_COMPANY_CODE, param.getCompanyCode());

        try {
            //companyName 唯一
            companyMapper.updateByConditionSelective(entity, example);
        } catch (DuplicateKeyException e) {
            return Result.failed(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                    ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
        }
        return Result.ok();
    }

    @Override
    public Result<Void> updateCompanyStatus(UpdateCompanyStatusRequest param) {
        Company entity = BeanCopyUtils.jsonCopyBean(param, Company.class);
        entity.setUpdateTime(new Date());

        Example example = new Example(Company.class);
        example.createCriteria()
                .andEqualTo(Company.C_COMPANY_CODE, param.getCompanyCode());
        companyMapper.updateByConditionSelective(entity, example);

        return Result.ok();
    }

    @Override
    public Result<Void> deleteCompany(DeleteCompanyRequest param) {
        Example example = new Example(Company.class);
        example.createCriteria()
                .andEqualTo(Company.C_COMPANY_CODE, param.getCompanyCode());
        companyMapper.deleteByCondition(example);

        return Result.ok();
    }

    @Override
    public PageResult<CompanyResponse> queryCompanyList(QueryCompanyRequest param) {

        PageHelper.startPage(param.getPageNum(), param.getPageSize());

        Example example = new Example(Company.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtil.isNotEmpty(param.getCompanyName())) {
            criteria.andLike(Company.C_COMPANY_NAME, "%" + param.getCompanyName() + "%");
        }
        if (StringUtil.isNotEmpty(param.getCompanyCode())) {
            criteria.andEqualTo(Company.C_COMPANY_CODE, param.getCompanyCode());
        }
        /*MER-1884
        if (StringUtil.isNotEmpty(param.getIssuerCode())) {
            criteria.andEqualTo(Company.C_ISSUER_CODE, param.getIssuerCode());
        }*/
        if (StringUtil.isNotEmpty(param.getSbu())) {
            criteria.andEqualTo(Company.C_SBU, param.getSbu());
        }
        if (StringUtil.isNotEmpty(param.getStatus())) {
            criteria.andEqualTo(Company.C_STATUS, param.getStatus());
        }
        if (CollectionUtils.isNotEmpty(param.getCompanyCodeList())) {
        	 criteria.andIn(Company.C_COMPANY_CODE, param.getCompanyCodeList());
        }
        //创建时间倒序
        example.orderBy(Company.C_CREATE_TIME).desc();

        List<Company> gvCompanyEntities = companyMapper.selectByCondition(example);
        PageInfo<Company> info = PageInfo.of(gvCompanyEntities);

        List<CompanyResponse> companyResponses = BeanCopyUtils.jsonCopyList(info.getList(), CompanyResponse.class);

        for (CompanyResponse response : companyResponses) {

            if (StringUtil.isEmpty(response.getIssuerCode())){
                continue;
            }
            GetIssuerRequest request = new GetIssuerRequest();
            request.setIssuerCode(response.getIssuerCode());
            IssuerResponse issuer = issuerService.getIssuer(request);
            if (null != issuer ){
                response.setIssuerName(issuer.getIssuerName());
            }

        }

        return new PageResult<>(companyResponses, info.getTotal());
    }

    @Override
    public CompanyResponse getCompany(GetCompanyRequest param) {
        Company entity = BeanCopyUtils.jsonCopyBean(param, Company.class);
        Company company = companyMapper.selectOne(entity);
        /*
        MER-1884
        if (null == company || StringUtil.isEmpty(company.getIssuerCode())){
            return BeanCopyUtils.jsonCopyBean(company, CompanyResponse.class);
        }*/

        if (null == company ){
            return BeanCopyUtils.jsonCopyBean(company, CompanyResponse.class);
        }
        CompanyResponse response = BeanCopyUtils.jsonCopyBean(company, CompanyResponse.class);

        /*
        MER-1884
        GetIssuerRequest request = new GetIssuerRequest();
        request.setIssuerCode(response.getIssuerCode());
        IssuerResponse issuer = issuerService.getIssuer(request);
        if (null != issuer ){
            response.setIssuerName(issuer.getIssuerName());
        }*/
        return response;
    }

    @Override
    public PageResult<CompanyResponse> queryCompanyByIssuerCodes(QueryCompanyByIssuerCodesRequest request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        Example example = new Example(Company.class);
        // MER-1884 example.createCriteria().andIn(Company.C_ISSUER_CODE,request.getIssuerCodes());
        List<Company> gvCompanyEntities = companyMapper.selectByCondition(example);
        PageInfo<Company> info = PageInfo.of(gvCompanyEntities);
        return new PageResult<>(BeanCopyUtils.jsonCopyList(info.getList(), CompanyResponse.class), info.getTotal());
    }

	@Override
	public List<CompanyResponse> queryCompanyByCodes(List<String> companyCodeList) {

        if (CollectionUtils.isEmpty(companyCodeList)) return Collections.emptyList();

        Example example = new Example(Company.class);
		example.createCriteria().andIn(Company.C_COMPANY_CODE, companyCodeList);

        List<Company> list = companyMapper.selectByCondition(example);
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        return BeanCopyUtils.jsonCopyList(list, CompanyResponse.class);
	}

	@Override
	public List<Company> queryAllCompany() {
		return companyMapper.selectAll();
	}

	@Override
	public List<PermissionTreeResponse> queryPermissionTree() {

		List<Company> companyList = queryAllCompany();

		Map<String, List<Merchant>> merchantMap = new HashMap<>();
		List<Merchant> merchantList = merchantService.queryAllMerchant();
		if (CollectionUtils.isNotEmpty(merchantList)) {
			merchantMap = merchantList.stream().collect(Collectors.groupingBy(Merchant::getCompanyCode));
		}

		Map<String, List<Outlet>> outletMap = new HashMap<>();
		List<Outlet> outletList = outletService.queryAllOutLet();
		if (CollectionUtils.isNotEmpty(outletList)) {
			outletMap = outletList.stream().collect(Collectors.groupingBy(Outlet::getMerchantCode));
		}
		List<PermissionTreeResponse> list = new ArrayList<>();
		if (CollectionUtils.isEmpty(companyList)) {
			return list;
		}
		for (Company company : companyList) {
			PermissionTreeResponse companyTree = new PermissionTreeResponse();
			list.add(companyTree);
			companyTree.setCode(company.getCompanyCode());
			companyTree.setName(company.getCompanyName());
			companyTree.setType(2);
			List<Merchant> merchantChild = merchantMap.get(company.getCompanyCode());
			if (CollectionUtils.isEmpty(merchantChild)) {
				continue;
			}
			List<PermissionTreeResponse> merchantTreeList = new ArrayList<>();
			companyTree.setChildList(merchantTreeList);
			for (Merchant merchant : merchantChild) {
				PermissionTreeResponse merchantTree = new PermissionTreeResponse();
				merchantTreeList.add(merchantTree);
				merchantTree.setCode(merchant.getMerchantCode());
				merchantTree.setName(merchant.getMerchantName());
				merchantTree.setType(3);
				List<Outlet> outletChild = outletMap.get(merchant.getMerchantCode());
				if (CollectionUtils.isEmpty(outletChild)) {
					continue;
				}

				List<PermissionTreeResponse> outletTreeList = new ArrayList<>();
				merchantTree.setChildList(outletTreeList);
				for (Outlet outlet : outletChild) {
					PermissionTreeResponse outletTree = new PermissionTreeResponse();
					outletTreeList.add(outletTree);
					outletTree.setCode(outlet.getOutletCode());
					outletTree.setName(outlet.getOutletName());
					outletTree.setType(4);
				}
			}
		}
		return list;
	}

}
