package com.gtech.gvcore.service.report.impl;

import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.utils.GvDateUtil;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.GoodsInTransitSummaryBean;
import com.gtech.gvcore.service.report.impl.bo.GoodsInTransitSummaryBo;
import com.gtech.gvcore.service.report.impl.param.GoodsInTransitQueryData;
import com.gtech.gvcore.service.report.impl.support.GoodsInTransitBaseImpl;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName GoodsInTransitSummaryImpl
 * @Description goods in transit summary impl
 * <AUTHOR>
 * @Date 2022/12/7 16:54
 * @Version V1.0
 **/
@Service
public class GoodsInTransitSummaryImpl extends GoodsInTransitBaseImpl<GoodsInTransitSummaryBean>
        implements BusinessReport<GoodsInTransitQueryData, GoodsInTransitSummaryBean>, SingleReport {

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GOODS_IN_TRANSIT_SUMMARY_REPORT;
    }

    @Override
    public List<GoodsInTransitSummaryBean> getExportData(GoodsInTransitQueryData queryData) {

        //find
        List<GoodsInTransitSummaryBo> dataList = reportBusinessMapper.selectGoodsInTransitSummary(queryData);

        // join date
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(dataList, Arrays.asList(GoodsInTransitSummaryBo::getInbound, GoodsInTransitSummaryBo::getOutbound), Outlet.class);
        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(dataList, GoodsInTransitSummaryBo::getCpgCode, Cpg.class);

        //function
        final Function<Date, String> dateFormat = time -> null == time ? StringUtils.EMPTY : GvDateUtil.formatUs(time, GvDateUtil.FORMAT_US_DATETIME_DD_MM_YYYY_HH_MM_A);
        final BiFunction<Date, Date, String> transitDeliveryIntervalFormat = (aTime, rTime) -> {

            //convert
            if (null == aTime) return StringUtils.EMPTY;
            rTime = rTime == null ? new Date() : rTime;

            //day number
            long times = rTime.getTime() - aTime.getTime();
            long day = times / (1000 * 60 * 60 * 24);

            //convert
            return day + " day";
        };

        //convert
        return dataList.stream()
                .map(e -> new GoodsInTransitSummaryBean()
                        .setRequestId(e.getRequestId())
                        .setOutboundOutlet(outletMap.findValue(e.getOutbound()).getOutletName())
                        .setInboundOutlet(outletMap.findValue(e.getInbound()).getOutletName())
                        .setAllocationDate(dateFormat.apply(e.getAllLocationTime()))
                        .setReceiveDate(dateFormat.apply(e.getInboundTime()))
                        .setVpg(cpgMap.findValue(e.getCpgCode()).getCpgName())
                        .setNumberOfVouchers(String.valueOf(e.getVoucherCount()))
                        .setVoucherAmount(super.toAmount(e.getAmount()))
                        .setTransitDeliveryInterval(transitDeliveryIntervalFormat.apply(e.getAllLocationTime(), e.getInboundTime()))
                ).collect(Collectors.toList());
    }

    @Override
    public Object getHeadObject(ReportContext context) {

        final GoodsInTransitQueryData queryData = (GoodsInTransitQueryData) context.getQueryParam();

        return new Head()
                .setTransactionDate(GvDateUtil.formatUs(queryData.getTransactionDateStart(), GvDateUtil.FORMAT_US_DATETIME_DD_MMM_YY)
                        + " - " + GvDateUtil.formatUs(queryData.getTransactionDateEnd(), GvDateUtil.FORMAT_US_DATETIME_DD_MMM_YY))
                .setGeneratedDate(GvDateUtil.formatUs(new Date(), GvDateUtil.FORMAT_US_DATETIME_DD_MMM_YY_HH_MM_A));
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class Head {

        private String transactionDate;
        private String generatedDate;

    }
}


