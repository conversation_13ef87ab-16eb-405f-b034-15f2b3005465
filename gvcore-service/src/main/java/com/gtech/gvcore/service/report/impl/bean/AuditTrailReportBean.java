package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportLabel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/20 11:19
 * @Description:AuditTrailReport.xlsx Audit Trail Report
 */
@Getter
@Setter
@Accessors(chain = true)
public class AuditTrailReportBean {

    /**
     * 操作时间
     */
    @ExcelProperty(index = 0,value = "Operator Time")
    private String operatorTime;

    /**
     * 操作人
     */
    @ExcelProperty(index = 1,value = "Operator")
    private String operator;

    /**
     * 请求地址
     */
    @ExcelProperty(index = 2,value = "Request Path")
    private String requestPath;

    @ReportLabel(value = "Request id", action = ReportLabel.ActionEnum.FIND_SYSTEM_LOGGER)
    @ExcelProperty(index = 3,value = "Request id")
    private String requestId;

}
