package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.issuehandling.*;
import com.gtech.gvcore.common.response.issuehandling.GetIssueHandlingResponse;
import com.gtech.gvcore.common.response.issuehandling.QueryIssueHandlingByPageResponse;
import com.gtech.gvcore.common.response.issuehandling.RegenerateActivationCodeVoucherInfoResponse;
import com.gtech.gvcore.common.response.issuehandling.ValidateUploadedFileResponse;
import com.gtech.gvcore.dao.model.IssueHandling;

import java.util.List;
import java.util.Map;

public interface IssueHandlingService {

	/**
	 * 
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月5日
	 */
	Result<String> createIssueHandling(CreateIssueHandlingRequest request);

	/**
	 * 
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月5日
	 */
	Result<ValidateUploadedFileResponse> validateUploadedFile(ValidateUploadedFileRequest request);

    /**
     * 
     * @param request
     * @return
     * <AUTHOR>
     * @date 2022年4月15日
     */
    Result<String> editIssueHandling(EditIssueHandlingRequest request);
    
    /**
     * 
     * @param request
     * @return
     * <AUTHOR>
     * @date 2022年5月30日
     */
    Result<String> submit(IssueHandlingSubmitRequest request);

	/**
	 * 
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月5日
	 */
	Result<String> approve(IssueHandlingApproveRequest request);

	/**
	 * 
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月19日
	 */
	Result<String> cancel(IssueHandlingCancelRequest request);

	/**
	 * 
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月6日
	 */
	PageResult<QueryIssueHandlingByPageResponse> queryIssueHandlingByPage(QueryIssueHandlingByPageRequest request);

	/**
	 * 
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月6日
	 */
	Result<GetIssueHandlingResponse> getIssueHandling(GetIssueHandlingRequest request);

	IssueHandling queryByIssueHandlingCode(String issueHandlingCode);

    Map<String,IssueHandling> queryMapIssueHandingByCodes(List<String> codes);

    String getNotesByMaxIdInVoucherCodes(String transactionId);
	void resendRegenerateActivationCodeEmail(ResendRegenerateActivationCodeEmailRequest request);

	PageResult<RegenerateActivationCodeVoucherInfoResponse> regenerateActivationCodeVoucherInfo(RegenerateActivationCodeVoucherInfoRequest request);
}
