package com.gtech.gvcore.service.report.impl.support.liability.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @ClassName VoucherBo
 * @Description VoucherBo
 * <AUTHOR>
 * @Date 2023/4/20 16:01
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GcLiabilityVoucherBo {

    private final String tableCode;

    private final String cardNumber;

    private final Date lastMoonTime;

    private final GcLiabilityVoucherMode voucher;

    private String status;

    private String merchantCode;

    public GcLiabilityVoucherBo(final String tableCode, final Date lastMoonTime, final GcLiabilityVoucherMode voucher, String merchantCode, String status) {
        this.tableCode = tableCode;
        this.cardNumber = voucher.getCardNumber();
        this.lastMoonTime = lastMoonTime;
        this.voucher = voucher;
        this.merchantCode = merchantCode;
        this.status = status;
    }

}
