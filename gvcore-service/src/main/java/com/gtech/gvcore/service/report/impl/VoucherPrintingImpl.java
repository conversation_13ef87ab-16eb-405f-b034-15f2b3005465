package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.VoucherBatchStatusEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Issuer;
import com.gtech.gvcore.dao.model.Printer;
import com.gtech.gvcore.dao.model.UserAccount;
import com.gtech.gvcore.helper.GvPageHelper;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.PollReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.VoucherPrintingBean;
import com.gtech.gvcore.service.report.impl.bo.VoucherPrintingBo;
import com.gtech.gvcore.service.report.impl.param.VoucherPrintingQueryDate;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName VoucherPrintingImpl
 * @Description Voucher printing report
 *      Poll Report
 *      Simple Business Report
 * <AUTHOR>
 * @Date 2023/2/13 16:11
 * @Version V1.0
 **/
@Service
public class VoucherPrintingImpl
    extends ReportSupport
    implements BusinessReport<VoucherPrintingQueryDate, VoucherPrintingBean>, PollReport {

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        // return report type
        return ReportExportTypeEnum.VOUCHER_PRINTING_REPORT;
    }

    @Override
    public VoucherPrintingQueryDate builderQueryParam(CreateReportRequest reportParam) {

        // set query param
        VoucherPrintingQueryDate param = new VoucherPrintingQueryDate();

        // set create time
        param.setCreateTimeBegin(reportParam.getTransactionDateStart());
        param.setCreateTimeEnd(reportParam.getTransactionDateEnd());

        // issuer code
        param.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        // cpg code
        param.setCpgCodeList(reportParam.getCpgCodes());

        // voucher printing status
        param.setVoucherPrintingStatusList(reportParam.getVoucherPrintingStatusList());
        // voucher printing vendor
        param.setPrintingVendorList(reportParam.getPrintingVendorList());

        // purchase order no
        param.setPurchaseOrderNo(reportParam.getPurchaseOrderNo());

        // voucher effective date
        param.setEffectiveDateBegin(reportParam.getVoucherEffectiveDateStart());
        param.setEffectiveDateEnd(reportParam.getVoucherEffectiveDateEnd());

        // return param
        return param;
    }

    @Override
    public List<VoucherPrintingBean> getExportData(VoucherPrintingQueryDate param) {

        // select data
        List<VoucherPrintingBo> boList = super.reportBusinessMapper.selectVoucherPrinting(param, GvPageHelper.getRowBounds(param));

        // EMPTY
        if (CollectionUtils.isEmpty(boList)) return Collections.emptyList();

        // join data
        JoinDataMap<Issuer> issuerMap = super.getMapByCode(boList, VoucherPrintingBo::getIssuerCode, Issuer.class);
        JoinDataMap<Cpg> cpgMap = super.getMapByCode(boList, VoucherPrintingBo::getCpgCode, Cpg.class);
        JoinDataMap<Printer> printMap = super.getMapByCode(boList, VoucherPrintingBo::getPrinterCode, Printer.class);
        JoinDataMap<UserAccount> userMap = super.getMapByCode(boList, VoucherPrintingBo::getCreateUser, UserAccount.class);

        // convert return data
        return boList.stream()
                //convert
                .map(e -> new VoucherPrintingBean()
                        // issuer
                        .setIssuerName(issuerMap.findValue(e.getIssuerCode()).getIssuerName())
                        // cpg
                        .setCpgName(cpgMap.findValue(e.getCpgCode()).getCpgName())
                        // purchase order no
                        .setPurchaseOrderNo(e.getPurchaseOrderNo())
                        // voucher batch code
                        .setVoucherBatchCode(e.getVoucherBatchCode())
                        // create time
                        .setCreateTime(DateUtil.format(e.getCreateTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_2))
                        // create user
                        .setCreateUser(e.getCreateBy(userMap))
                        // printer
                        .setPrinterName(printMap.findValue(e.getPrinterCode()).getPrinterName())
                        // voucher effective date
                        .setVoucherEffectiveDate(e.getVoucherEffectiveDate())
                        // voucher expiry date
                        .setBookletNumber(e.getBookletNumber())
                        // voucher number
                        .setVoucherNumber(e.getVoucherNumber())
                        // voucher printing status
                        .setStatus(VoucherBatchStatusEnum.valueOfCode(e.getStatus()).desc())
                ).collect(Collectors.toList());

    }


}


