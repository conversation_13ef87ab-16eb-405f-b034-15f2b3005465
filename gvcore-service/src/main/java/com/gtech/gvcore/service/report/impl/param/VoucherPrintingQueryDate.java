package com.gtech.gvcore.service.report.impl.param;

import com.gtech.commons.page.PageParam;
import com.gtech.gvcore.service.report.ReportQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * @ClassName VoucherPrintingParam
 * @Description
 * <AUTHOR>
 * @Date 2023/2/13 17:58
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class VoucherPrintingQueryDate extends PageParam implements ReportQueryParam {

    private Date createTimeBegin;

    private Date createTimeEnd;

    private List<String> issuerCodeList;

    private List<String> cpgCodeList;

    private List<String> voucherPrintingStatusList;

    private List<String> printingVendorList;

    private Date effectiveDateBegin;

    private Date effectiveDateEnd;

    private String purchaseOrderNo;

}
