package com.gtech.gvcore.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.utils.UUIDUtils;
import com.gtech.gvcore.dao.mapper.MeansOfPaymentOutletMapper;
import com.gtech.gvcore.dao.model.MeansOfPaymentOutlet;
import com.gtech.gvcore.service.MeansOfPaymentOutletService;

/**
 * 
 * <AUTHOR>
 * @date 2022年4月22日
 */
@Service
public class MeansOfPaymentOutletServiceImpl implements MeansOfPaymentOutletService {

    @Autowired
    private MeansOfPaymentOutletMapper meansOfPaymentOutletMapper;

    @Override
    public int insesrt(String meansOfPaymentCode, List<String> outletCodeList, String createUser) {

        Date createTime = new Date();
        List<MeansOfPaymentOutlet> list = new ArrayList<>(outletCodeList.size());
        for (String outletCode : outletCodeList) {
            MeansOfPaymentOutlet outlet = new MeansOfPaymentOutlet();
            outlet.setMeansOfPaymentOutletCode(UUIDUtils.generateCode());
            outlet.setMeansOfPaymentCode(meansOfPaymentCode);
            outlet.setOutletCode(outletCode);
            outlet.setDeleteStatus(GvcoreConstants.DELETE_STATUS_DISABLE);
            outlet.setCreateUser(createUser);
            outlet.setCreateTime(createTime);
            list.add(outlet);
        }
        return meansOfPaymentOutletMapper.insertList(list);
    }

    @Override
    public List<MeansOfPaymentOutlet> queryByMeansOfPaymentCode(String meansOfPaymentCode) {

        if (StringUtils.isBlank(meansOfPaymentCode)) {
            return Collections.emptyList();
        }

        MeansOfPaymentOutlet outlet = new MeansOfPaymentOutlet();
        outlet.setMeansOfPaymentCode(meansOfPaymentCode);
        List<MeansOfPaymentOutlet> list = meansOfPaymentOutletMapper.select(outlet);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateByPrimaryKeySelective(MeansOfPaymentOutlet meansOfPaymentOutlet) {
        return meansOfPaymentOutletMapper.updateByPrimaryKeySelective(meansOfPaymentOutlet);
    }

    @Override
    public List<MeansOfPaymentOutlet> queryByMeansOfPaymentCodeList(List<String> meansOfPaymentCodeList,
            Integer deleteStatus) {

        if (CollectionUtils.isEmpty(meansOfPaymentCodeList)) {
            return Collections.emptyList();
        }

        List<MeansOfPaymentOutlet> list = meansOfPaymentOutletMapper
                .queryByMeansOfPaymentCodeList(meansOfPaymentCodeList, deleteStatus);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }

}


