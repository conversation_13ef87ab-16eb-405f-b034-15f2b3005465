package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/20 16:52
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class GcCancelSalesDetailBean {


    @ExcelProperty(value = "Transaction Date")
    private String transactionDate;

    @ExcelProperty(value = "Gift Card Number")
    private String voucherNumber;

    @ExcelProperty(value = "Merchant Name")
    private String merchant;

    @ExcelProperty(value = "Gift Card Program Group")
    private String voucherProgramGroup;

    @ReportAmountValue
    @ExcelProperty(value = "Total Amount", converter = ExportExcelNumberConverter.class)
    private String transactionAmount;

    @ExcelProperty(value = "Customer Name")
    private String customerName;

    @ExcelProperty(value = "Invoice Number")
    private String invoiceNumber;
}
