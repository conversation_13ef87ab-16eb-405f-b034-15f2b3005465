package com.gtech.gvcore.service.report.impl.param;

import com.gtech.gvcore.service.report.ReportQueryParam;
import com.gtech.gvcore.service.report.extend.row.TransactionDataPageParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * @ClassName ExpiryGvDetailQueryData
 * @Description 超市时间明细报表
 * <AUTHOR>
 * @Date 2022/9/26 11:17
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class ExpiryGvQueryData extends TransactionDataPageParam implements ReportQueryParam {

    private List<String> issuerCodeList;

    private List<String> outletCodeList;

    private List<String> cpgCodeList;

    private Date voucherEffectiveDateStart;

    private Date voucherEffectiveDateEnd;

    private String customerCode;

    private List<String> merchantCodeList;

    private boolean selectMigrationDataFlag;

}
