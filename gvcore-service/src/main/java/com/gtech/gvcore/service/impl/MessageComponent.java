package com.gtech.gvcore.service.impl;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

import com.gtech.gvcore.dto.ResponseMessageBean;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ApproveNodeRecordTypeEnum;
import com.gtech.gvcore.common.enums.ApproveTypeEnum;
import com.gtech.gvcore.common.enums.MessageEnventEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.flow.SendEmailRequest;
import com.gtech.gvcore.common.request.flow.SendEmailRequest.FileVo;
import com.gtech.gvcore.common.utils.HttpUtils;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.util.StringUtil;


@Component
@Slf4j
public class MessageComponent {
	
	@Value("${titan.gv.url:}")
	private String gatewayUrl;
	
	public void send(JSONObject messageRequest){
		if (messageRequest == null) {
			return;
		}
		messageRequest.put("domainCode", GvcoreConstants.SYSTEM_DEFAULT);
		messageRequest.put("tenantCode", GvcoreConstants.SYSTEM_DEFAULT);
		messageRequest.put("orgCode", GvcoreConstants.SYSTEM_DEFAULT);
		try {
			String str = HttpUtils.doPost(gatewayUrl + GvcoreConstants.MESSAGE_URL, messageRequest.toJSONString(), "json");
			log.info("send email response:{}", str);
		} catch (IOException e) {
			log.error(e.getMessage(), e);
			throw new GTechBaseException(ResultErrorCodeEnum.SEND_EMAIL_FAIL.code(), ResultErrorCodeEnum.SEND_EMAIL_FAIL.desc());
		}
	}

	public ResponseMessageBean sendEmail(JSONObject messageRequest){
		if (messageRequest == null) {
			return ResponseMessageBean.fail();
		}
		messageRequest.put("domainCode", GvcoreConstants.SYSTEM_DEFAULT);
		messageRequest.put("tenantCode", GvcoreConstants.SYSTEM_DEFAULT);
		messageRequest.put("orgCode", GvcoreConstants.SYSTEM_DEFAULT);
		try {
			final String responseStr = HttpUtils.doPost(gatewayUrl + GvcoreConstants.MESSAGE_URL, messageRequest.toJSONString(), "json");
			log.info("send email response:{}", responseStr);
			return JSON.parseObject(responseStr, ResponseMessageBean.class);
		} catch (IOException e) {
			log.error(e.getMessage(), e);
			throw new GTechBaseException(ResultErrorCodeEnum.SEND_EMAIL_FAIL.code(), ResultErrorCodeEnum.SEND_EMAIL_FAIL.desc());
		}
	}

	public void sendApproveEmail(SendEmailRequest request) {
		log.info("send approve email:{}", JSON.toJSONString(request));
		if (request == null) {
			return;
		}
		String flow = request.getFlow();
		String node = request.getNode();
		if (StringUtil.isEmpty(flow) || StringUtil.isEmpty(node)) {
			log.info("flow or node is empty : {}", JSON.toJSONString(request));
			return;
		}
		String enventCode = "";
		if (flow.equals(ApproveTypeEnum.APPROVE_TYPE_01.getType()) && node.equals(ApproveNodeRecordTypeEnum.RELEASE.getType())) {
			enventCode = MessageEnventEnum.ISSUANCE_CUSTOMER_ORDER.getCode();
		} else if (flow.equals(ApproveTypeEnum.APPROVE_TYPE_02.getType())) {
			enventCode = MessageEnventEnum.CREATE_VOUCHER_RETURN_TRANSFER.getCode();
		}
		if (StringUtil.isEmpty(enventCode)) {
			log.info("enventCode is empty: {}", JSON.toJSONString(request));
			return;
		}
		sendEmail(request, enventCode);
	}

	public void sendEmail(SendEmailRequest request, String enventCode) {
		List<String> emails = request.getEmails();
		if (CollectionUtils.isEmpty(emails)) {
			log.info("sendApproveEmail emails empty : {}", JSON.toJSONString(request));
			return;
		}
		emails = emails.stream().filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
		JSONObject messageRequest = new JSONObject();
		JSONObject param = new JSONObject();
		messageRequest.put("eventCode", enventCode);
		param.put("emails", emails);
		param.put("businessCode", request.getBusinessCode());
		if (request.getExtendParams() != null) {
			param.putAll(request.getExtendParams());
		}
		messageRequest.put("param", param);
		
		List<FileVo> fileList = request.getFileList();
		if (CollectionUtils.isNotEmpty(fileList)) {
			JSONArray attachments = new JSONArray();
			for(FileVo fileVo : fileList) {
				JSONObject files = new JSONObject();
				files.put("filename", fileVo.getFilename());
				files.put("url", fileVo.getUrl());
				attachments.add(files);
			}
			param.put("attachments", attachments);
		}
		
        
		send(messageRequest);
	}
}
