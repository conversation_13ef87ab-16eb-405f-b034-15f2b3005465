package com.gtech.gvcore.service.report.impl.support.liability.model;

import com.gtech.gvcore.dao.model.Voucher;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName LiabilityVoucherMode
 * @Description LiabilityVoucherMode
 * <AUTHOR>
 * @Date 2023/4/20 15:55
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class LiabilityVoucherMode {

    @Column(name = "id")
    private Long id;

    @Column(name = "voucher_code")
    private String voucherCode;

    @Column(name = "cpg_code")
    private String cpgCode;

    @Column(name = "mop_code")
    private String mopCode;

    @Column(name = "denomination")
    private BigDecimal denomination;

    @Column(name = "voucher_effective_date")
    private Date voucherEffectiveDate;

    @Column(name = "status")
    private Integer status;

    @Column(name = "voucher_status")
    private Integer voucherStatus;

    public Voucher convertVoucher() {

        return Voucher.builder()
                .voucherCode(this.voucherCode)
                .cpgCode(this.cpgCode)
                .mopCode(this.mopCode)
                .denomination(this.denomination)
                .voucherEffectiveDate(this.voucherEffectiveDate)
                .status(this.status)
                .voucherStatus(this.voucherStatus)
                .build();

    }


}
