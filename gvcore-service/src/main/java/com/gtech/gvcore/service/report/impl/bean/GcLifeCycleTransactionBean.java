package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import com.gtech.gvcore.service.report.extend.ReportBeanCustomerAutoFull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: Generated based on CardLifeCycleMovementBean
 * @Date: 2025/6/19
 * @Description: Gift Card Life Cycle Transaction Bean
 */
@Getter
@Setter
@Accessors(chain = true)
public class GcLifeCycleTransactionBean implements ReportBeanCustomerAutoFull {

    @ExcelProperty(index = 0, value = "Transaction Date")
    private String transactionDate;

    @ExcelProperty(index = 1, value = "Gift Card Number")
    private String giftCardNumber;

    @ExcelProperty(index = 2, value = "Merchant Name")
    private String merchantName;

    @ExcelProperty(index = 3, value = "Outlet Name")
    private String outletName;

    @ExcelProperty(index = 4, value = "Invoice Number")
    private String invoiceNumber;

    @ReportAmountValue
    @ExcelProperty(index = 5, value = "Transaction Amount", converter = ExportExcelNumberConverter.class)
    private String transactionAmount;

    @ExcelProperty(index = 6, value = "Transaction Type")
    private String transactionType;

    @ExcelProperty(index = 7, value = "Response Message")
    private String responseMessage;

    @ExcelProperty(index = 9, value = "SBU Company Name")
    private String sbuCompanyName;
}
