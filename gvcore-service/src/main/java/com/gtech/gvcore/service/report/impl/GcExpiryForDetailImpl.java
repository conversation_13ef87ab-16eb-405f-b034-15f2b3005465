package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.CustomerTypeEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.giftcard.domain.model.GcSalesRecord;
import com.gtech.gvcore.giftcard.domain.repository.GcSalesRepository;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.giftcard.util.GiftCardStatusCalculator;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.GcExpiryForDetailBean;
import com.gtech.gvcore.service.report.impl.bo.GcExpiryBo;
import com.gtech.gvcore.service.report.impl.param.GcExpiryQueryData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:57
 * @Description: Gift Card Expiry Report Detail Implementation
 */
@Service
public class GcExpiryForDetailImpl extends ReportSupport
        implements BusinessReport<GcExpiryQueryData, GcExpiryForDetailBean>, SingleReport {

    @Autowired
    private GcSalesRepository gcSalesRepository;

    @Override
    public GcExpiryQueryData builderQueryParam(CreateReportRequest reportParam) {

        GcExpiryQueryData queryData = new GcExpiryQueryData();

        queryData.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);

        // 过期时间范围
        queryData.setExpiryDateStart(reportParam.getVoucherEffectiveDateStart());
        queryData.setExpiryDateEnd(reportParam.getVoucherEffectiveDateEnd());

        queryData.setCpgCodeList(reportParam.getCpgCodes());
        queryData.setCustomerCodeList(reportParam.getCustomerCodes());
        if (StringUtils.isNotBlank(reportParam.getVoucherCode())) {
            queryData.setVoucherCode(Arrays.asList(reportParam.getVoucherCode().split(",")));
        }
        queryData.setPurchaseDateStart(reportParam.getPurchaseDateStart());
        queryData.setPurchaseDateEnd(reportParam.getPurchaseDateEnd());

        // 激活时间范围
        queryData.setActivationTimeStart(reportParam.getActivationDateStart());
        queryData.setActivationTimeEnd(reportParam.getActivationDateEnd());
        queryData.setInvoiceNumber(reportParam.getInvoiceNo());

        return queryData;
    }

    @Override
    public List<GcExpiryForDetailBean> getExportData(GcExpiryQueryData queryData) {
        List<GcExpiryBo> boList = new ArrayList<>();
        PollPageHelper.pollSelect(gcReportBusinessMapper::selectGcExpiry, queryData, boList::addAll);
        //find
        final Collection<GcExpiryBo> list =
                Optional.of(boList)
                        .orElse(Collections.emptyList());

        //empty
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        //init map
        final JoinDataMap<GcCpg> cpgMap = super.getMapByCode(list, GcExpiryBo::getCpgCode, GcCpg.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(list, GcExpiryBo::getOutletCode, Outlet.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(outletMap.values(), Outlet::getMerchantCode, Merchant.class);
        final JoinDataMap<Customer> customerMap = super.getMapByCode(list, GcExpiryBo::getOwnerCustomer, Customer.class);

        // 批量查询销售记录获取invoice_number
        final List<String> cardNumbers = list.stream().map(GcExpiryBo::getVoucherCode).distinct().collect(Collectors.toList());
        final List<GcSalesRecord> salesRecords = gcSalesRepository.findByCardNumber(cardNumbers);
        final Map<String, String> invoiceMap = salesRecords.stream()
                .collect(Collectors.toMap(GcSalesRecord::getCardNumber, GcSalesRecord::getInvoiceNumber, (existing, replacement) -> existing));

        //convert result
        List<GcExpiryForDetailBean> collect = list.stream()
                .map(e -> {
                    Customer customer = customerMap.findValue(e.getCustomerCode());
                    String customerName = getCustomerDisplayName(customer);
                    Outlet outlet = outletMap.findValue(e.getOutletCode());

                    String status = GiftCardStatusCalculator.builder().cardStatus(e.getCardStatus())
                            .activationDeadline(e.getActivationDeadline())
                            .expiryTime(e.getExpiryTime())
                            .managementStatus(e.getManagementStatus())
                            .activationExtensionCount(e.getActivationExtensionCount()).build().determineCardStatus();
                    return new GcExpiryForDetailBean()
                            .setVoucherAmount(super.toAmount(getCardAmount(e)))
                            .setVoucherNumber(e.getVoucherCode())
                            .setCardStatus(status)
                            .setPurchaseDate(formatDate(e.getSalesTime()))
                            .setActivationDate(formatDate(e.getActivationTime()))
                            .setExpiryDate(formatDate(e.getExpiryTime()))
                            .setInvoiceNumber(invoiceMap.get(e.getVoucherCode()))
                            .setVpg(cpgMap.findValue(e.getCpgCode()).getCpgName())
                            .setMerchant(merchantMap.findValue(outlet.getMerchantCode()).getMerchantName())
                            .setCustomerName(customerName);
                })
                .collect(Collectors.toList());
        return collect;
    }

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_EXPIRY_DETAILED;
    }

    /**
     * 获取客户显示名称
     *
     * @param customer 客户信息
     * @return 客户显示名称
     */
    private String getCustomerDisplayName(Customer customer) {
        if (customer == null) {
            return "";
        }

        // 企业客户显示公司名称，个人客户显示客户姓名
        if (CustomerTypeEnum.CORPORATE.code().equals(customer.getCustomerType())) {
            return StringUtils.defaultString(customer.getCompanyName(), "");
        } else {
            return StringUtils.defaultString(customer.getCustomerName(), "");
        }
    }

    /**
     * 获取礼品卡金额
     * 优先使用余额，如果余额为空则使用面额
     *
     * @param cardInfo 礼品卡信息
     * @return 金额
     */
    private BigDecimal getCardAmount(GcExpiryBo cardInfo) {
        BigDecimal balance = cardInfo.getBalance();
        BigDecimal denomination = cardInfo.getDenomination();

        // 优先使用余额，如果余额为null则使用面额，都为null则返回0
        if (balance != null) {
            return balance;
        } else if (denomination != null) {
            return denomination;
        } else {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 格式化日期
     *
     * @param date 日期
     * @return 格式化后的日期字符串，如果日期为null则返回空字符串
     */
    private String formatDate(Date date) {
        return date != null ? DateUtil.format(date, "yyyy-MM-dd HH:mm:ss") : "";
    }
}
