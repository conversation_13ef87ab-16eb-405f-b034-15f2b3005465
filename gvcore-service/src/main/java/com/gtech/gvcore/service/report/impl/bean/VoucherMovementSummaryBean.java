package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportLabel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/21 13:18
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class VoucherMovementSummaryBean {

    @ExcelProperty(index = 0, value = "Product")
    private String product;

    @ExcelProperty(index = 1, converter = ExportExcelNumberConverter.class)
    @ReportLabel({"Sales", "Quantity"})
    private String salesQuantity = "0";

    @ReportAmountValue
    @ExcelProperty(index = 2, converter = ExportExcelNumberConverter.class)
    @ReportLabel({"Sales", "Amount"})
    private String salesAmount = "0";

    @ExcelProperty(index = 3, converter = ExportExcelNumberConverter.class)
    @ReportLabel({"Activation", "Quantity"})
    private String activationQuantity = "0";

    @ReportAmountValue
    @ExcelProperty(index = 4, converter = ExportExcelNumberConverter.class)
    @ReportLabel({"Activation", "Amount"})
    private String activationAmount = "0";

    @ExcelProperty(index = 5, converter = ExportExcelNumberConverter.class)
    @ReportLabel({"Redemption", "Quantity"})
    private String redemptionQuantity = "0";

    @ReportAmountValue
    @ExcelProperty(index = 6, converter = ExportExcelNumberConverter.class)
    @ReportLabel({"Redemption", "Amount"})
    private String redemptionAmount = "0";

    @ExcelProperty(index = 7, converter = ExportExcelNumberConverter.class)
    @ReportLabel({"Balance", "Quantity"})
    private String balanceQuantity = "0";

    @ReportAmountValue
    @ExcelProperty(index = 8, converter = ExportExcelNumberConverter.class)
    @ReportLabel({"Balance", "Amount"})
    private String balanceAmount = "0";

    @ExcelProperty(index = 9, converter = ExportExcelNumberConverter.class)
    @ReportLabel({"EXPIRED", "Quantity"})
    private String expiredQuantity = "0";

    @ReportAmountValue
    @ExcelProperty(index = 10, converter = ExportExcelNumberConverter.class)
    @ReportLabel({"EXPIRED", "Amount"})
    private String expiredAmount = "0";

    public VoucherMovementSummaryBean addSalesQuantity() {
        this.salesQuantity = String.valueOf(ConvertUtils.toInteger(this.salesQuantity, 0) + 1);
        return this;
    }

    public VoucherMovementSummaryBean addSalesQuantity(String salesQuantity) {
        this.salesQuantity = String.valueOf(ConvertUtils.toInteger(this.salesQuantity, 0) + ConvertUtils.toInteger(salesQuantity, 0));
        return this;
    }

    public VoucherMovementSummaryBean addSalesAmount(BigDecimal salesAmount) {
        this.salesAmount = ConvertUtils.toBigDecimal(this.salesAmount, BigDecimal.ZERO).add(ConvertUtils.toBigDecimal(salesAmount, BigDecimal.ZERO)).toPlainString();
        return this;
    }

    public VoucherMovementSummaryBean addActivationQuantity() {
        this.activationQuantity = String.valueOf(ConvertUtils.toInteger(this.activationQuantity, 0) + 1);
        return this;
    }

    public VoucherMovementSummaryBean addActivationQuantity(String activationQuantity) {
        this.activationQuantity = String.valueOf(ConvertUtils.toInteger(this.activationQuantity, 0) + ConvertUtils.toInteger(activationQuantity, 0));
        return this;
    }

    public VoucherMovementSummaryBean addActivationAmount(BigDecimal activationAmount) {
        this.activationAmount = ConvertUtils.toBigDecimal(this.activationAmount, BigDecimal.ZERO).add(ConvertUtils.toBigDecimal(activationAmount, BigDecimal.ZERO)).toPlainString();
        return this;
    }

    public VoucherMovementSummaryBean addRedemptionQuantity() {
        this.redemptionQuantity = String.valueOf(ConvertUtils.toInteger(this.redemptionQuantity, 0) + 1);
        return this;
    }

    public VoucherMovementSummaryBean addRedemptionQuantity(String redemptionQuantity) {
        this.redemptionQuantity = String.valueOf(ConvertUtils.toInteger(this.redemptionQuantity, 0) + ConvertUtils.toInteger(redemptionQuantity, 0));
        return this;
    }

    public VoucherMovementSummaryBean addRedemptionAmount(BigDecimal redemptionAmount) {
        this.redemptionAmount = ConvertUtils.toBigDecimal(this.redemptionAmount, BigDecimal.ZERO).add(ConvertUtils.toBigDecimal(redemptionAmount, BigDecimal.ZERO)).toPlainString();
        return this;
    }

    public VoucherMovementSummaryBean addExpiredQuantity() {
        this.expiredQuantity = String.valueOf(ConvertUtils.toInteger(this.activationQuantity, 0) + 1);
        return this;
    }

    public VoucherMovementSummaryBean addExpiredQuantity(String expiredQuantity) {
        this.expiredQuantity = String.valueOf(ConvertUtils.toInteger(this.expiredQuantity, 0) + ConvertUtils.toInteger(expiredQuantity, 0));
        return this;
    }

    public VoucherMovementSummaryBean addExpiredAmount(BigDecimal expiredAmount) {
        this.expiredAmount = ConvertUtils.toBigDecimal(this.expiredAmount, BigDecimal.ZERO).add(ConvertUtils.toBigDecimal(expiredAmount, BigDecimal.ZERO)).toPlainString();
        return this;
    }

}
