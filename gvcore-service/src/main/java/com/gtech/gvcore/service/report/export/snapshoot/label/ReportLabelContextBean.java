package com.gtech.gvcore.service.report.export.snapshoot.label;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.ReportRequest;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName ReportLabelContextBean
 * @Description report label context
 * <AUTHOR>
 * @Date 2022/12/26 14:06
 * @Version V1.0
 **/
@Getter
@Setter(AccessLevel.PACKAGE)
@Accessors(chain = true)
public class ReportLabelContextBean {

    public static final String RESULT_BEAN_KEY = "reportLabelContextBeanResultBean";
    public static final String REPORT_BEAN_KEY = "reportLabelContextBeanReportBean";

    private JSONObject jsonObject;
    private Object resultBean;
    private ReportRequest reportRequest;
    private CreateReportRequest reportParam;

    private ReportExportTypeEnum exportTypeEnumByType;
    private String fieldName;

    public ReportLabelContextBean setReportRequest(ReportRequest reportRequest) {

        this.reportRequest = reportRequest;
        this.reportParam = JSON.parseObject(reportRequest.getRequestJson(), CreateReportRequest.class);

        return this;
    }
}
