package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/21 11:36
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class GcLiabilityDetailedBean {

    @ExcelProperty(value = "Gift Card Number")
    private String voucherNumber;

    @ExcelProperty(value = "Gift Card Program Group")
    private String voucherProgramGroup;

    @ExcelProperty(value = " Gift Card Status")
    private String voucherStatus;

    @ReportAmountValue
    @ExcelProperty(value = "Denomination", converter = ExportExcelNumberConverter.class)
    private String denomination;

    @ReportAmountValue
    @ExcelProperty(value = "Current Balance", converter = ExportExcelNumberConverter.class)
    private String balance;

    @ExcelProperty(value = "Expiry Date")
    private String expiryDate;
}
