package com.gtech.gvcore.service.report.impl.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @ClassName PerformanceStatisticBo
 * @Description performance statistic bo
 * <AUTHOR>
 * @Date 2023/3/14 10:36
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class PerformanceStatisticBo {


    private String issuerCode;

    private String merchantCode;

    private String outletCode;

    private String outletType;

    private String cpgCode;

    private Integer netSalesCount = 0;

    private BigDecimal netSalesAmount = BigDecimal.ZERO;

    private Integer netRedemptionCount = 0;

    private BigDecimal netRedemptionAmount = BigDecimal.ZERO;

    public PerformanceStatisticBo addNetSalesCount(int count) {
        netSalesCount += count;
        return this;
    }

    public PerformanceStatisticBo addNetSalesAmount(BigDecimal amount) {
        netSalesAmount = netSalesAmount.add(amount);
        return this;
    }

    public PerformanceStatisticBo addNetRedemptionCount(int count) {
        netRedemptionCount += count;
        return this;
    }

    public void addNetRedemptionAmount(BigDecimal amount) {
        netRedemptionAmount = netRedemptionAmount.add(amount);
    }

}