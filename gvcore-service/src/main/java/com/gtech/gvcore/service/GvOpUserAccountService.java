package com.gtech.gvcore.service;

import com.gtech.basic.idm.service.dto.OpUserAccountDto;
import com.gtech.basic.idm.web.vo.param.GetOpUserAccountParam;
import com.gtech.basic.idm.web.vo.param.UpdateOpUserAccountParam;
import com.gtech.gvcore.common.request.opuseraccount.CreateOpUserAccountRequest;

/**
 * <AUTHOR>
 * @Date 2022/6/13 13:45
 */
public interface GvOpUserAccountService {




    void createOpUserAccount(CreateOpUserAccountRequest param);


    int updateOpUserAccount(UpdateOpUserAccountParam param);


    OpUserAccountDto getOpUserAccount(GetOpUserAccountParam paramDto);


    void deleteByCustomer(String customerCode);
}
