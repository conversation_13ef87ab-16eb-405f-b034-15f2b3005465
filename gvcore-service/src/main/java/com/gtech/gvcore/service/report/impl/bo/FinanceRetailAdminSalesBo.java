package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.service.report.extend.poll.GroupNewTransactionByVoucherCodeSupport;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName FinanceRetailAdminSalesDetailBo
 * @Description
 * <AUTHOR>
 * @Date 2023/1/13 14:04
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class FinanceRetailAdminSalesBo implements GroupNewTransactionByVoucherCodeSupport {

    private String transactionCode;

    private BigDecimal transactionNumber;

    private String merchantCode;

    private String customerCode;

    private String outletCode;

    private String cpgCode;

    private String posCode;

    private String voucherCode;

    private String corporateName;

    private String customerFirstName;

    private String customerLastName;

    private String email;

    private String transactionDate;

    private String transactionType;

    private BigDecimal denomination;

    private String invoiceNumber;

    private String responseMessage;

    private String transactionMode;

    private String referenceNumber;

    private String batchCode;

    private String approveCode;

    private String voucherEffectiveDate;

    private String notes;

    public void setTransactionCode(String transactionCode) {
        this.transactionNumber = new BigDecimal(transactionCode.substring(3));
        this.transactionCode = transactionCode;
    }

    public Date getTransactionDate() {

        return DateUtil.parseDate(transactionDate, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

    public Date getVoucherEffectiveDate() {

        return DateUtil.parseDate(voucherEffectiveDate, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

}
