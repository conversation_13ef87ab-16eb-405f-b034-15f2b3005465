package com.gtech.gvcore.service.distribution;

import com.gtech.gvcore.common.request.distribution.ChangeEmailTemplateStatusRequest;
import com.gtech.gvcore.common.request.distribution.CreateDistributionEmailTemplateRequest;
import com.gtech.gvcore.common.request.distribution.PreviewSendEmailTemplateRequest;
import com.gtech.gvcore.common.request.distribution.QueryEmailTemplateRequest;
import com.gtech.gvcore.common.request.distribution.UpdateDistributionEmailTemplateRequest;
import com.gtech.gvcore.common.response.distribution.DistributionEmailTemplateResponse;

import java.util.List;

/**
 * @ClassName DistributionEmailTemplateService
 * @Description 分发系统邮件模板Service
 * <AUTHOR>
 * @Date 2022/7/5 15:15
 * @Version V1.0
 **/
public interface DistributionEmailTemplateService {
    String createEmailTemplate(CreateDistributionEmailTemplateRequest request);

    String updateEmailTemplate(UpdateDistributionEmailTemplateRequest request);

    String changeStatus(ChangeEmailTemplateStatusRequest request);

    List<DistributionEmailTemplateResponse> queryEmailTemplates(QueryEmailTemplateRequest request);

    void previewSend(PreviewSendEmailTemplateRequest request);

    void validateEmailContent(String subject, String richText);
}
