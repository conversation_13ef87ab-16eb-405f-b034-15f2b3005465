package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Company;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.Pos;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.ReportParamConvertHelper;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.FinanceRetailAdminRedemptionDetailedBean;
import com.gtech.gvcore.service.report.impl.bo.FinanceRetailAdminRedemptionBo;
import com.gtech.gvcore.service.report.impl.param.FinanceRetailAdminRedemptionQueryData;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @ClassName FinanceRetailAdminRedemptionDetailedImpl
 * @Description FinanceRetailAdminRedemptionDetailedImpl
 * <AUTHOR>
 * @Date 2022/7/8 17:07
 * @Version V1.0
 **/
@Service
public class FinanceRetailAdminRedemptionDetailedImpl extends ReportSupport
        implements BusinessReport<FinanceRetailAdminRedemptionQueryData, FinanceRetailAdminRedemptionDetailedBean>, SingleReport {

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.FINANCE_RETAIL_ADMIN_REDEMPTION_DETAILED_REPORT;
    }

    @Override
    public FinanceRetailAdminRedemptionQueryData builderQueryParam(CreateReportRequest reportParam) {

        //convert
        ReportParamConvertHelper.convertQueryDateCompanyCodeToMerchantCode(reportParam);

        FinanceRetailAdminRedemptionQueryData param = new FinanceRetailAdminRedemptionQueryData();

        //transaction
        param.setTransactionDateEnd(reportParam.getTransactionDateEnd());
        param.setTransactionDateStart(reportParam.getTransactionDateStart());

        //invoice number
        param.setInvoiceNumber(reportParam.getInvoiceNo());

        // issuer merchant outlet
        param.setMerchantCodeList(reportParam.getMerchantCodes());
        param.setOutletCodeList(reportParam.getOutletCodes());

        //cpg
        param.setCpgCodeList(reportParam.getCpgCodes());

        //voucher code
        param.setVoucherCodeNumStart(reportParam.getVoucherCodeNumStart());
        param.setVoucherCodeNumEnd(reportParam.getVoucherCodeNumEnd());

        return param;
    }

    @Override
    public List<FinanceRetailAdminRedemptionDetailedBean> getExportData(FinanceRetailAdminRedemptionQueryData param) {

        List<FinanceRetailAdminRedemptionBo> list = this.getBoList(param);

        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        final JoinDataMap<Voucher> voucherMap = super.getMapByCode(list, FinanceRetailAdminRedemptionBo::getVoucherCode, Voucher.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(list, FinanceRetailAdminRedemptionBo::getOutletCode, Outlet.class);
        final JoinDataMap<Pos> posMap = super.getMapByCode(list, FinanceRetailAdminRedemptionBo::getPosCode, Pos.class);
        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(list, FinanceRetailAdminRedemptionBo::getCpgCode, Cpg.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(list, FinanceRetailAdminRedemptionBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Customer> customerMap = super.getMapByCode(voucherMap.values(), Voucher::getVoucherOwnerCode, Customer.class);
        final JoinDataMap<Company> companyMap = super.getMapByCode(merchantMap.values(), Merchant::getCompanyCode, Company.class);

        return list.stream()
                .map(e -> {

                    final Merchant merchant = merchantMap.findValue(e.getMerchantCode());
                    final Pos pos = posMap.findValue(e.getPosCode());
                    final Outlet outlet = outletMap.findValue(e.getOutletCode());
                    final Voucher voucher = voucherMap.findValue(e.getVoucherCode());
                    final Customer customer = customerMap.findValue(voucher.getVoucherOwnerCode());
                    final Cpg cpg = cpgMap.findValue(e.getCpgCode());
                    final Company company = companyMap.findValue(merchant.getCompanyCode());

                    final String posName = null != pos && StringUtil.isNotEmpty(pos.getPosName()) ? pos.getPosName() : "GV POS";
                    final String denomination = super.toAmount(e.getDenomination());
                    final String transactionDate = DateUtil.format(e.getTransactionDate(), DateUtil.FORMAT_YYYYMMDDHHMISS);

                    return new FinanceRetailAdminRedemptionDetailedBean()
                            .setCardNumber(voucher.getVoucherCode())
                            .setBookletNumber(ConvertUtils.toString(voucher.getBookletCode(), "NA"))
                            .setBatchNumber(voucher.getVoucherBatchCode())
                            .setMerchant(merchant.getMerchantName())
                            .setOutlet(outlet.getOutletName())
                            .setDescriptiveOutletName(company.getCompanyName())
                            .setOutletCode(outlet.getBusinessOutletCode())
                            .setTransactionDate(transactionDate)
                            .setPosName(posName)
                            .setVoucherProgramGroup(cpg.getCpgName())
                            .setTransactionType(TransactionTypeEnum.getTypeDesc(e.getTransactionType()))
                            .setAmount(denomination)
                            .setInvoiceNumber(e.getInvoiceNumber())
                            .setResponseMessage(e.getResponseMessage())
                            .setDateAtClient(transactionDate)
                            .setPreTransactionVoucherDenomination(denomination)
                            .setTransactionPostDate(transactionDate)
                            .setNotes(e.getNotes())
                            .setExpiryDate(DateUtil.format(e.getVoucherEffectiveDate(), DateUtil.FORMAT_YYYYMMDDHHMISS))
                            .setIssuanceYear(DateUtil.format(voucher.getCreateTime(), DateUtil.FORMAT_YYYY))
                            .setReferenceNumber(e.getReferenceNumber())
                            .setBatchNumber(e.getBatchCode())
                            .setRequestAmount(denomination)
                            .setApprovalCode(e.getApproveCode())
                            //customer auto fill
                            .autoFull(customer, FinanceRetailAdminRedemptionDetailedBean.class);

                }).collect(Collectors.toList());

    }

    private List<FinanceRetailAdminRedemptionBo> getBoList(FinanceRetailAdminRedemptionQueryData param) {

        return Optional.of(PollPageHelper.pollGroupNewTransactionByCodeSelect(reportBusinessMapper::selectFinanceRetailRedeemAdmin, param))
                .map(e -> e.stream().filter(d -> TransactionTypeEnum.GIFT_CARD_REDEEM.equalsCode(d.getTransactionType())).collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

}
