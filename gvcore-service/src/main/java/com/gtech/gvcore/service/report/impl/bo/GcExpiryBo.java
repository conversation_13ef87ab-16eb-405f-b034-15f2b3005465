package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.service.report.extend.poll.GroupNewTransactionByVoucherCodeSupport;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName GcExpiryBo
 * @Description Gift Card Expiry Business Object
 * <AUTHOR>
 * @Date 2023/5/10 10:40
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GcExpiryBo implements GroupNewTransactionByVoucherCodeSupport {

    private String transactionCode;

    private BigDecimal transactionNumber;

    public void setTransactionCode(String transactionCode) {
        if (transactionCode != null && transactionCode.length() > 3) {
            this.transactionNumber = new BigDecimal(transactionCode.substring(3));
        }
        this.transactionCode = transactionCode;
    }

    /**
     * Outlet code.
     */
    private String outletCode;

    /**
     * Cpg code.
     */
    private String cpgCode;

    /**
     * Voucher Code.
     */
    private String voucherCode;

    /**
     * Invoice Number.
     */
    private String invoiceNumber;

    /**
     * Denomination.
     */
    private BigDecimal denomination;

    /**
     * Balance.
     */
    private BigDecimal balance;

    /**
     * Expiry Time.
     */
    private String expiryTime;

    /**
     * Customer Code.
     */
    private String customerCode;

    /**
     * Owner Customer.
     */
    private String ownerCustomer;

    /**
     * Card Status.
     */
    private String cardStatus;

    /**
     * Sales Time (Purchase Date).
     */
    private String salesTime;

    /**
     * Activation Time.
     */
    private String activationTime;

    private String managementStatus;
    private Date activationDeadline;
    private Integer activationExtensionCount;

    public Date getExpiryTime() {
        return DateUtil.parseDate(expiryTime, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

    public Date getSalesTime() {
        return DateUtil.parseDate(salesTime, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

    public Date getActivationTime() {
        return DateUtil.parseDate(activationTime, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }
}
