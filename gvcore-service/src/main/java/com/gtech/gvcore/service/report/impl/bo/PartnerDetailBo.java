package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.report.extend.poll.GroupNewTransactionByVoucherCodeSupport;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName PartnerSalesDetailBo
 * @Description
 * <AUTHOR>
 * @Date 2023/1/3 17:14
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class PartnerDetailBo implements GroupNewTransactionByVoucherCodeSupport {

    private String issuerCode;

    private String transactionId;

    private String voucherCode;

    private String merchantCode;

    private String outletCode;

    private String customerCode;

    private String transactionDate;

    private String posCode;

    private String cpgCode;

    private String invoiceNumber;

    private String referenceNumber;

    private String approveCode;

    private String corporateName;

    private String customerFirstName;

    private String customerLastName;

    private String email;

    private String transactionCode;

    private String transactionType;

    private BigDecimal transactionNumber;

    public void setTransactionCode(String transactionCode) {
        this.transactionNumber = new BigDecimal(transactionCode.substring(3));
        this.transactionCode = transactionCode;
    }


    private Voucher voucher;


    public Date getTransactionDate() {

        return DateUtil.parseDate(transactionDate, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

}
