package com.gtech.gvcore.service.report.export.snapshoot;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportLabel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @ClassName QueryOrderReportDataResponse
 * @Description 查询报表数据结果
 * <AUTHOR>
 * @Date 2022/8/23 14:03
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "QueryOrderReportDataResponse")
public class QueryOrderReportDataResponse {

    @ApiModelProperty(value = "list")
    private List<?> list;

    @ApiModelProperty(value = "label array")
    private List<LabelBean> labelArray;

    @ApiModelProperty(value = "total")
    private Long total;

    @ApiModelProperty(value = "Head Html")
    private String headHtml;

    @Getter
    @Setter
    @Accessors(chain = true)
    @ApiModel(value = "ReportDataLabelBean")
    public static class LabelBean {

        @ApiModelProperty(value = "label")
        private String label;

        @ApiModelProperty(value = "value")
        private String value;

        @ApiModelProperty(value = "children")
        private List<LabelBean> children;

        @ApiModelProperty(value = "action")
        private String action;

        @JsonIgnore
        @ApiModelProperty(hidden = true)
        private ReportLabel reportLabel;

        @SuppressWarnings("unused")
        @ApiModelProperty(value = "show")
        public boolean isShow () {

            if (ignoreBlankLabelColumn) return StringUtils.isNotBlank(label) && !"null".equalsIgnoreCase(label);

            return true;
        }

        @JsonIgnore
        private boolean ignoreBlankLabelColumn;

        public void addChildrenNode(LabelBean label) {

            if (null == children) children = new ArrayList<>();
            children.add(label);

        }

        public LabelBean copy() {
            LabelBean labelBean = new LabelBean();
            labelBean.setLabel(label);
            labelBean.setReportLabel(reportLabel);
            labelBean.setValue(value);
            labelBean.setIgnoreBlankLabelColumn(ignoreBlankLabelColumn);
            labelBean.setChildren(null == children ? null : children.stream().map(LabelBean::copy).collect(Collectors.toList()));
            return labelBean;
        }
    }

}
