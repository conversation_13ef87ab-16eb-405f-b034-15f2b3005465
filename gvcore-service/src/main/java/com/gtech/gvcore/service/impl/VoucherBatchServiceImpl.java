package com.gtech.gvcore.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.gtech.basic.filecloud.api.model.Resp;
import com.gtech.basic.filecloud.commons.PagedData;
import com.gtech.basic.filecloud.commons.PagedDatas;
import com.gtech.basic.filecloud.exports.excel.spec.ExcelExportSpec;
import com.gtech.basic.filecloud.exports.management.FileExport;
import com.gtech.basic.filecloud.exports.management.FileExportManager;
import com.gtech.basic.filecloud.exports.management.FileExportResult;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.page.PageData;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.CryptoUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.MessageEnventEnum;
import com.gtech.gvcore.common.enums.ReceivingMethodEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.enums.VoucherReceiveSourceTypeEnum;
import com.gtech.gvcore.common.enums.VoucherReceiveStatusEnum;
import com.gtech.gvcore.common.enums.VpgDisableGenerationEnum;
import com.gtech.gvcore.common.request.base.PageBean;
import com.gtech.gvcore.common.request.cpg.GetCpgRequest;
import com.gtech.gvcore.common.request.cpg.GetCpgTypeRequest;
import com.gtech.gvcore.common.request.outlet.GetOutletRequest;
import com.gtech.gvcore.common.request.receive.CreateVoucherReceiveRequest;
import com.gtech.gvcore.common.request.receive.VoucherReceiveBatchRequest;
import com.gtech.gvcore.common.request.senddigitalvoucherexceltoemail.SendDigitalVoucherExcelToEmailRequest;
import com.gtech.gvcore.common.request.voucher.SendVoucherRequest;
import com.gtech.gvcore.common.request.voucherbatch.*;
import com.gtech.gvcore.common.request.voucherdistribute.CreateVoucherDistributeRequest;
import com.gtech.gvcore.common.response.cpg.GetCpgResponse;
import com.gtech.gvcore.common.response.cpg.GetCpgTypeResponse;
import com.gtech.gvcore.common.response.customerorder.GetCustomerOrderDetailsResponse;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.common.response.voucherbatch.ExportDigitalVoucherResponse;
import com.gtech.gvcore.common.response.voucherbatch.ExportVoucherResponse;
import com.gtech.gvcore.common.response.voucherbatch.QueryStartCodeResponse;
import com.gtech.gvcore.common.response.voucherbatch.VoucherBatchResponse;
import com.gtech.gvcore.components.TaskProgressManager;
import com.gtech.gvcore.dao.mapper.CustomerOrderEmailMapper;
import com.gtech.gvcore.dao.mapper.PrinterMapper;
import com.gtech.gvcore.dao.mapper.TransactionDataMapper;
import com.gtech.gvcore.dao.mapper.VoucherBatchMapper;
import com.gtech.gvcore.dao.model.ArticleMop;
import com.gtech.gvcore.dao.model.CustomerOrderEmail;
import com.gtech.gvcore.dao.model.Printer;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.dao.model.VoucherBatch;
import com.gtech.gvcore.dao.model.VoucherReceive;
import com.gtech.gvcore.dto.TaskProgress;
import com.gtech.gvcore.helper.FileCompressionHelper;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.helper.OssHelper;
import com.gtech.gvcore.helper.VoucherNumberHelper;
import com.gtech.gvcore.service.ArticleMopService;
import com.gtech.gvcore.service.CpgService;
import com.gtech.gvcore.service.CpgTypeService;
import com.gtech.gvcore.service.GvUserAccountService;
import com.gtech.gvcore.service.OutletService;
import com.gtech.gvcore.service.VoucherBatchService;
import com.gtech.gvcore.service.VoucherBookletService;
import com.gtech.gvcore.service.VoucherDistributeService;
import com.gtech.gvcore.service.VoucherReceiveService;
import com.gtech.gvcore.service.VoucherService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/3/2 11:17
 */
@Slf4j
@Service
public class VoucherBatchServiceImpl implements VoucherBatchService {


    @Autowired
    private GvCodeHelper gvCodeHelper;

    @Autowired
    private VoucherBatchMapper voucherBatchMapper;

    @Lazy
    @Autowired
    private VoucherService voucherService;

    @Lazy
    @Autowired
    private VoucherBookletService voucherBookletService;

    @Autowired
    private VoucherDistributeService voucherDistributeService;

    @Autowired
    private FileExportManager fileExportManager;

    @Autowired
    private VoucherNumberHelper voucherNumberHelper;

    @Lazy
    @Autowired
    private VoucherReceiveService voucherReceiveService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private MessageComponent messageComponent;
    @Autowired
    private PrinterMapper printerMapper;


    @Autowired
    private ArticleMopService articleMopService;

    @Autowired
    private GvUserAccountService userAccountService;


    @Value("#{${gv.issuer.warehouse:}}")
    private Map<String, String> issuerWarehouseMap;

    @Autowired
    private OutletService outletService;

    @Autowired
    private CpgService cpgService;

    @Autowired
    private CpgTypeService cpgTypeService;

    @Autowired
    private CustomerOrderEmailMapper customerOrderEmailMapper;

    @Autowired
    private TaskProgressManager taskProgressManager;

    @Lazy
    @Autowired
    private TransactionDataMapper transactionDataMapper;

    public static final String YEAR = "2022";
    public static final String HEAD = "9001";

    public static final String DIGITAL = "101";

    public static final String VOUCHER_AUTO_INCR = "0000001";
    public static final String BOOKLET_AUTO_INCR = "**********";
    public static final String BATCH_AUTO_INCR = "001";
    public static final String FILE_NAME_AUTO_INCR = "01";

    public static final String REDIS_HEAD = "GV:VOUCHER:";

    public static final String REDIS_BOOKLET = "BOOKLET:";

    public static final String VOUCHER_YEAR = "YEAR:";

    public static final Integer GENERATING = 0;
    public static final Integer GENERATED = 1;
    public static final Integer PUBLISHED = 2;
    public static final Integer COMPLETED = 3;
    public static final Integer FAILURE = 4;
    public static final Integer DELETE = 5;
    public static final Integer CANCEL = 6;
    public static final String VOUCHER_CODE = "VoucherCode:";

    public static final String EXCEL = ".xlsx";


    public static final String RE = "[a-zA-Z]";

    @Autowired
    private OssHelper ossHelper;

    private static final ThreadPoolExecutor EXECUTOR = new ThreadPoolExecutor(200, 300, 60, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(500000),
            new ThreadFactoryBuilder().setNameFormat("ThreadPoolCenter-VoucherBatch-%d").build(),
            new ThreadPoolExecutor.CallerRunsPolicy());

    @Override
    public Result<String> createVoucherBatch(CreateVoucherBatchRequest request) {
        Date date;
        try {
            GetCpgRequest cpgRequest = new GetCpgRequest();
            cpgRequest.setCpgCode(request.getCpgCode());
            Result<GetCpgResponse> cpg = cpgService.getCpg(cpgRequest);
            if (cpg.getData().getDisableGeneration().equals(VpgDisableGenerationEnum.DISABLED.code())) {
                return Result.failed("This vpg cannot be used to generate coupons and sales");
            }

            date = voucherNumberHelper.cpgEffectiveDateToDate(cpg.getData());
        } catch (Exception e) {
            throw new GTechBaseException(ResultErrorCodeEnum.GET_CPG_ERROR.code(), ResultErrorCodeEnum.GET_CPG_ERROR.desc());
        }

        if (null != date) {
            request.setVoucherEffectiveDate(date);
        }


        //mop
        ArticleMop articleMop = articleMopService.queryByArticleMopCode(request.getArticleCode());
        request.setMopCode(articleMop.getMopCode());

        VoucherBatch voucherBatch = BeanCopyUtils.jsonCopyBean(request, VoucherBatch.class);

        //生成中
        voucherBatch.setStatus(GENERATING);
        voucherBatch.setCreateTime(new Date());
        Long voucherStartNo = Long.valueOf(request.getVoucherStartNo());
        Long bookletStartNo = Long.valueOf(request.getBookletStartNo());

        String voucherEndNo = request.getVoucherEndNo();

        String voucherCode = (String) redisTemplate.opsForValue().get(REDIS_HEAD + VOUCHER_CODE + request.getVoucherStartNo().substring(0, 8));
        if (null == voucherCode) {
            redisTemplate.opsForValue().set(REDIS_HEAD + VOUCHER_CODE + voucherEndNo.substring(0, 8), voucherEndNo);
        } else if (voucherStartNo <= Long.valueOf(voucherCode)) {
            return Result.failed("Already generated vouchers please try again");
        } else {
            redisTemplate.opsForValue().set(REDIS_HEAD + VOUCHER_CODE + voucherEndNo.substring(0, 8), voucherEndNo);
        }

        //set year
        String setYear = String.valueOf(voucherStartNo).substring(4, 6);
        setVoucherYear(setYear);


        //添加voucherBatch表
        voucherBatchMapper.insert(voucherBatch);

        //添加approvalCode和invoiceNo
        request.setApprovalCode(gvCodeHelper.generateApproveCode());


        //添加voucherBooklet表
        ExecutorService threadPool = Executors.newCachedThreadPool();
        threadPool.submit(() -> {
            addVoucherBooklet(request, voucherStartNo, bookletStartNo);
        });

        return Result.ok(voucherBatch.getVoucherBatchCode());
    }

    private void addVoucherBooklet(CreateVoucherBatchRequest request, Long voucherStartNo, Long bookletStartNo) {
        Integer bookletNum = request.getBookletNum();
        ExecutorService threadPool = Executors.newCachedThreadPool();

        for (int i = 0; i < bookletNum; i++) {
            redisTemplate.opsForValue().set(REDIS_HEAD + REDIS_BOOKLET + String.valueOf(bookletStartNo), voucherStartNo - 1, 24L, TimeUnit.HOURS);

            //每包的开始
            request.setVoucherStartNo(String.valueOf(voucherStartNo));

            //每包的数量
            voucherStartNo += Long.valueOf(request.getBookletPerNum());

            //每包的结束
            request.setVoucherEndNo(String.valueOf(voucherStartNo - 1));

            CreateVoucherBatchRequest createVoucherBatchRequest = BeanCopyUtils.jsonCopyBean(request, CreateVoucherBatchRequest.class);

            final String booklet = String.valueOf(bookletStartNo);
            threadPool.submit(() -> {
                voucherBookletService.createVoucherBooklet(createVoucherBatchRequest, booklet, request.getCreateUser());

            });
            //voucherBookletService.createVoucherBooklet(createVoucherBatchRequest, (String.valueOf(bookletStartNo)),request.getCreateUser());

            bookletStartNo += 1;

        }
    }


    @Override
    public Result<Void> regenerateVoucherBatch(RegenerateVoucherBatchRequest param) {

        voucherBookletService.deleteByCondition(param.getVoucherBatchCode());
        voucherService.deleteByVoucherBatch(param.getVoucherBatchCode());
        GetVoucherBatchRequest getOne = new GetVoucherBatchRequest();
        getOne.setVoucherBatchCode(param.getVoucherBatchCode());
        VoucherBatchResponse voucherBatch = voucherBatchMapper.getVoucherBatch(getOne);

        redisTemplate.delete(REDIS_HEAD + String.valueOf(param.getVoucherBatchCode()));


        Long voucherStartNo = Long.valueOf(voucherBatch.getVoucherStartNo());
        Long bookletStartNo = Long.valueOf(voucherBatch.getBookletStartNo());

        CreateVoucherBatchRequest request = BeanCopyUtils.jsonCopyBean(voucherBatch, CreateVoucherBatchRequest.class);

        //添加approvalCode和invoiceNo
        request.setApprovalCode(gvCodeHelper.generateApproveCode());
        request.setInvoiceNo(gvCodeHelper.generateInvoiceNumber());

        for (int i = 0; i < voucherBatch.getBookletNum(); i++) {
            redisTemplate.delete(REDIS_HEAD + REDIS_BOOKLET + String.valueOf(bookletStartNo));
            redisTemplate.opsForValue().set(REDIS_HEAD + REDIS_BOOKLET + String.valueOf(bookletStartNo), voucherStartNo - 1, 24L, TimeUnit.HOURS);

            //每包的开始
            request.setVoucherStartNo(String.valueOf(voucherStartNo));

            //每包的数量
            voucherStartNo += Long.valueOf(request.getBookletPerNum());

            //每包的结束
            request.setVoucherEndNo(String.valueOf(voucherStartNo - 1));

            CreateVoucherBatchRequest createVoucherBatchRequest = BeanCopyUtils.jsonCopyBean(request, CreateVoucherBatchRequest.class);

            voucherBookletService.createVoucherBooklet(createVoucherBatchRequest, (String.valueOf(bookletStartNo)), param.getCreateUser());


            bookletStartNo += 1;

        }
        return Result.ok();

    }

    @Async
    @Override
    public void cancelVoucherBatch(CancelVoucherBatchRequest param) {
        GetVoucherBatchRequest getOne = new GetVoucherBatchRequest();
        getOne.setVoucherBatchCode(param.getVoucherBatchCode());
        VoucherBatchResponse voucherBatch = voucherBatchMapper.getVoucherBatch(getOne);

        /*if (!voucherBatch.getStatus().equals(GENERATED)){
            return Result.failed("The voucher has not been generated or has been sent");
        }*/

        voucherBookletService.deleteByCondition(param.getVoucherBatchCode());
        voucherService.deleteByVoucherBatch(param.getVoucherBatchCode());
        redisTemplate.delete(REDIS_HEAD + VOUCHER_CODE + voucherBatch.getVoucherStartNo().substring(0, 8));
        redisTemplate.delete(REDIS_HEAD + String.valueOf(param.getVoucherBatchCode()));
        Long voucherStartNo = Long.valueOf(voucherBatch.getVoucherStartNo());
        Long bookletStartNo = Long.valueOf(voucherBatch.getBookletStartNo());
        CreateVoucherBatchRequest request = BeanCopyUtils.jsonCopyBean(voucherBatch, CreateVoucherBatchRequest.class);

        VoucherBatch batch = new VoucherBatch();
        batch.setStatus(CANCEL);
        Example example = new Example(VoucherBatch.class);
        example.createCriteria().andEqualTo(VoucherBatch.C_VOUCHER_BATCH_CODE, param.getVoucherBatchCode());
        voucherBatchMapper.updateByConditionSelective(batch, example);

        //删除transactionData
        Example transactionExample = new Example(TransactionData.class);
        transactionExample.createCriteria().andEqualTo(TransactionData.C_BATCH_CODE, param.getVoucherBatchCode());
        transactionDataMapper.deleteByCondition(transactionExample);


        for (int i = 0; i < voucherBatch.getBookletNum(); i++) {
            Long finalBookletStartNo = bookletStartNo;
            EXECUTOR.execute(() -> {
                redisTemplate.delete(REDIS_HEAD + REDIS_BOOKLET + String.valueOf(finalBookletStartNo));
            });
            bookletStartNo += 1;
        }


    }

    @Override
    public PageResult<VoucherBatchResponse> queryVoucherBatch(QueryVoucherBatchRequest request) {

        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<VoucherBatchResponse> list = voucherBatchMapper.queryVoucherBatchList(request);
        PageInfo<VoucherBatchResponse> info = PageInfo.of(list);

        List<String> collect = info.getList().stream().map(VoucherBatchResponse::getCreateUser).collect(Collectors.toList());
        Map<String, String> stringStringMap = userAccountService.queryFullNameByCodeList(collect);

        for (VoucherBatchResponse response : info.getList()) {
            if (response.getStatus().equals(GENERATING)) {
                Integer result = (Integer) redisTemplate.opsForValue().get(REDIS_HEAD + response.getVoucherBatchCode());
                response.setRealTimeProgress(result);
            }
            response.setCreateUser(StringUtil.isEmpty(stringStringMap.get(response.getCreateUser())) ? null : stringStringMap.get(response.getCreateUser()));
        }


        return new PageResult<>(info.getList(), info.getTotal());
    }

    @Override
    public VoucherBatchResponse getVoucherBatch(GetVoucherBatchRequest request) {

        return voucherBatchMapper.getVoucherBatch(request);
    }

    @Override
    public Result<Void> updateVoucherBatchStatus(UpdateVoucherBatchStatusRequest param) {

        VoucherBatch voucherBatch = new VoucherBatch();
        voucherBatch.setStatus(param.getStatus());

        Example example = new Example(VoucherBatch.class);
        example.createCriteria().andEqualTo(VoucherBatch.C_VOUCHER_BATCH_CODE, param.getVoucherBatchCode());
        voucherBatchMapper.updateByConditionSelective(voucherBatch, example);

        return Result.ok();
    }

    @Override
    public Result<Void> updateVoucherBatch(UpdateVoucherBatchRequest param) {
        VoucherBatch voucherBatch = new VoucherBatch();
        voucherBatch.setPurchaseOrderNo(param.getPurchaseOrderNo());
        voucherBatch.setUpdateUser(param.getUpdateUser());

        Example example = new Example(VoucherBatch.class);
        example.createCriteria().andEqualTo(VoucherBatch.C_VOUCHER_BATCH_CODE, param.getVoucherBatchCode());
        voucherBatchMapper.updateByConditionSelective(voucherBatch, example);

        return Result.ok();
    }

    @Override
    public Result<Integer> realTimeProgressBar(QueryRealTimeProgressBarRequest param) {

        //REDIS_HEAD + "DIGITAL" + ":" + voucherBatchCode + ":" + detail.getCpgCode(), digitalVoucherCodes

        if (param.getVoucherBatchCode().startsWith("EGV")) {
            HashSet set = new HashSet();
            Integer result = 0;
            if (StringUtil.isEmpty(param.getCpgCode())) {
                set = (HashSet) redisTemplate.opsForValue().get(REDIS_HEAD + "DIGITAL" + ":" + param.getVoucherBatchCode());
                if (null == set) {
                    return new Result<>(result);
                }
                result = set.size();
            } else {
                set = (HashSet) redisTemplate.opsForValue().get(REDIS_HEAD + "DIGITAL" + ":" + param.getVoucherBatchCode() + ":" + param.getCpgCode());
                if (null == set) {
                    return new Result<>(result);
                }
                result = set.size();
            }
            return new Result<>(result);
        }

        Integer result = 0;
        if (StringUtil.isEmpty(param.getCpgCode())) {
            result = (Integer) redisTemplate.opsForValue().get(REDIS_HEAD + param.getVoucherBatchCode());
        } else {
            result = (Integer) redisTemplate.opsForValue().get(REDIS_HEAD + param.getVoucherBatchCode() + ":" + param.getCpgCode());
        }
        return new Result<>(result);
    }


    @Override
    public QueryStartCodeResponse queryStartCode(QueryStartCodeRequest request) {


        String prefix = "";
        if (StringUtil.isNotEmpty(request.getCpgCode())) {
            try {
                GetCpgRequest cpgRequest = new GetCpgRequest();
                cpgRequest.setCpgCode(request.getCpgCode());
                Result<GetCpgResponse> cpg = cpgService.getCpg(cpgRequest);
                Result<Object> cpgType = cpgTypeService.getCpgType(GetCpgTypeRequest.builder().cpgTypeCode(cpg.getData().getCpgTypeCode()).build());
                if (cpg.getData().getDisableGeneration().equals(VpgDisableGenerationEnum.DISABLED.code())) {
                    throw new GTechBaseException(ResultErrorCodeEnum.GET_CPG_ERROR.code(), ResultErrorCodeEnum.GET_CPG_ERROR.desc());
                }
                GetCpgTypeResponse data = (GetCpgTypeResponse) cpgType.getData();
                prefix = data.getPrefix();
            } catch (Exception e) {
                throw new GTechBaseException(ResultErrorCodeEnum.GET_CPG_ERROR.code(), ResultErrorCodeEnum.GET_CPG_ERROR.desc());
            }
        }

        //todo 需要Redis中查询出来年份，查不出来就用当前年的
        String voucherYear = getVoucherYear();
        int year = 0;
        if (voucherYear == null)
            year = Calendar.getInstance().get(Calendar.YEAR);
        else
            year = Integer.valueOf(voucherYear);

        Long incr = year - Long.valueOf(YEAR);
        /*Long bookletHead = Long.valueOf(HEAD);
        bookletHead += incr;*/
        QueryStartCodeResponse queryStartCodeResponse = new QueryStartCodeResponse();
        String voucherHead = voucherNumberHelper.voucherType(prefix, request.getDenomination(), String.valueOf(year));
        /*String maxVoucher = (String) redisTemplate.opsForValue().get("VOUCHERMAX:" + voucherHead);
        String maxVoucherBatchCode = (String) redisTemplate.opsForValue().get("BATCHMAX:" );
        String maxBookletCode = (String) redisTemplate.opsForValue().get("BOOKLETMAX:" + bookletHead);*/
        String maxVoucher = "";
        String maxVoucherBatchCode = "";
        String maxBookletCode = "";

        if (StringUtil.isEmpty(maxVoucher)) {
            maxVoucher = voucherBatchMapper.queryMaxVoucher(voucherHead);
            if (StringUtil.isEmpty(maxVoucher)) {
                maxVoucher = voucherHead + VOUCHER_AUTO_INCR;
            } else {
                Long aLong = Long.valueOf(maxVoucher);
                aLong += 1;
                maxVoucher = String.valueOf(aLong);
            }
        }

        queryStartCodeResponse.setVoucherStartNo(maxVoucher);

        String bookletHead = voucherNumberHelper.bookletType(request.getDenomination(), String.valueOf(year));
        if (StringUtil.isEmpty(maxBookletCode)) {
            maxBookletCode = voucherBookletService.queryMaxBooklet(bookletHead);
            if (StringUtil.isEmpty(maxBookletCode)) {
                maxBookletCode = bookletHead + BOOKLET_AUTO_INCR;
            } else {
                Long bookletCode = Long.valueOf(maxBookletCode);
                bookletCode += 1;
                maxBookletCode = String.valueOf(bookletCode);
            }
        }
        queryStartCodeResponse.setBookletStartNo(maxBookletCode);


        if (StringUtil.isEmpty(maxVoucherBatchCode)) {
            maxVoucherBatchCode = voucherBatchMapper.queryMaxVoucherBatchCode();
            if (StringUtil.isEmpty(maxVoucherBatchCode)) {
                maxVoucherBatchCode = BATCH_AUTO_INCR;
            } else {
                String[] split = maxVoucherBatchCode.split("-");
                maxVoucherBatchCode = split[split.length - 1];
                long batch = Long.parseLong(maxVoucherBatchCode);
                batch+= 1;
                if (batch<100){
                    maxVoucherBatchCode = String.format("%03d", batch);
                }else {
                    maxVoucherBatchCode = String.valueOf(batch);
                }
            }
        }

        queryStartCodeResponse.setVoucherBatchCode(maxVoucherBatchCode);


        String maxFileName = voucherBatchMapper.queryMaxFileName();
        if (StringUtil.isEmpty(maxFileName)) {
            maxFileName = FILE_NAME_AUTO_INCR;
        } else {
            String[] split = maxFileName.split("-");
            maxFileName = split[split.length - 1];

            Long batchCode = Long.valueOf(maxFileName);
            batchCode += 1;
            maxFileName = String.format("%02d", batchCode);


        }
        queryStartCodeResponse.setFileName(maxFileName);


        return queryStartCodeResponse;
    }

    public String getVoucherYear() {
        String year = (String) redisTemplate.opsForValue().get(REDIS_HEAD + VOUCHER_YEAR);
        return year;
    }



    public void setVoucherYear(String year) {
        int currentYear = Calendar.getInstance().get(Calendar.YEAR);
        String setYear = String.valueOf(currentYear).substring(0, 2) + year;
        int afterYear = currentYear + 1;
        if (afterYear != Integer.valueOf(setYear) && currentYear != Integer.valueOf(setYear)) {
            throw new GTechBaseException("3001", "The year can only be this year or next year");
        }
        try {
            if (!setYear.equals(getVoucherYear())) {
                redisTemplate.opsForValue().set(REDIS_HEAD + VOUCHER_YEAR, setYear);
                log.info("Successfully modified the year of voucher:{}", setYear);
            }
        } catch (Exception e) {
            log.info("Failed to modify the year of voucher:{}", setYear);
            e.printStackTrace();
        }
    }

    private PageData<ExportVoucherResponse> queryBatchVoucher(SendVoucherRequest request, PageBean pageBean) {
        PageHelper.startPage(pageBean.getPageNum(), pageBean.getPageSize());
        List<ExportVoucherResponse> voucherResponseList = voucherBatchMapper.export(request);
        voucherResponseList.forEach(vo -> vo.setDenomination(new BigDecimal(vo.getDenomination()).stripTrailingZeros().toPlainString()));
        PageInfo<ExportVoucherResponse> info = PageInfo.of(voucherResponseList);
        return new PageData<>(info.getList(), info.getTotal());
    }

    @Async
    @Override
    public String export(SendVoucherRequest request) throws IOException {

        PageBean pageBean = new PageBean();
        pageBean.setPageNum(1);
        pageBean.setPageSize(500);

        GetVoucherBatchRequest getBatchRequest = new GetVoucherBatchRequest();
        getBatchRequest.setVoucherBatchCode(request.getVoucherBatchCode());
        VoucherBatchResponse voucherBatch = voucherBatchMapper.getVoucherBatch(getBatchRequest);

        if (!request.getSendMailOnly()) {
            //添加receive数据
            //receive request
            createReceiveVoucherRequest(request.getCreateUser(), voucherBatch);
            updateVoucherBatchStatus(UpdateVoucherBatchStatusRequest.builder().voucherBatchCode(request.getVoucherBatchCode()).status(PUBLISHED).build());
            //owner CirculationStatus
            voucherService.updateVoucherOwner(request.getVoucherBatchCode(), voucherBatch.getIssuerCode());
        }


        PagedData<ExportVoucherResponse> voucherData = PagedDatas
                .<ExportVoucherResponse, PageData<ExportVoucherResponse>>builder()
                .query(() -> queryBatchVoucher(request, pageBean))
                .queryResultConverter(PageData::getList)
                .hasNextPage(pageResult -> pageBean.getPageNum() <= totalPageSize(pageResult.getTotal(), pageBean.getPageSize()))
                .afterQuery(() ->
                        pageBean.setPageNum(pageBean.getPageNum() + 1)
                ).build();

        String secretCode = voucherNumberHelper.randomPassword(10);
        ExcelExportSpec.SheetBuilder<ExportVoucherResponse> sheet = ExcelExportSpec.builder()
                .sheet(ExportVoucherResponse.class, "SequentialRandomPasswordFile")
                .dataSource(voucherData);

        FileExport fileExport = FileExport.builder()
                .domainCode(GvcoreConstants.SYSTEM_DEFAULT)
                .tenantCode(GvcoreConstants.SYSTEM_DEFAULT)
                .userId(request.getCreateUser())
                .name(voucherBatch.getFileName())
                .category("voucher.batch")
                .spec(sheet.build())
                .build();

        String accessUrl = "";

        Resp<FileExportResult> result = new Resp<>();
        try {
            result = FileExportResult.from(fileExportManager.export(fileExport));
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        if (result.getData() != null) {
            accessUrl = result.getData().getAccessUrl();
            final FileCompressionHelper.EncryptCompressionParameter parameter = FileCompressionHelper.EncryptCompressionParameter.builder()
                    .fileUrl(accessUrl)
                    .fileName(voucherBatch.getFileName() + EXCEL)
                    .password(secretCode)
                    .build();
            accessUrl = ossHelper.compressionUploadToOss(parameter, voucherBatch.getFileName());
        } else {
            //暂不处理

        }

        redisTemplate.opsForValue().set(REDIS_HEAD + voucherBatch.getFileName(), secretCode);


        String email = "";
        String receivingMethod = "";
        Printer printer;
        try {
            String printerCode = voucherBatch.getPrinterCode();
            Printer printerEntity = new Printer();
            printerEntity.setPrinterCode(printerCode);
            printer = printerMapper.selectOne(printerEntity);
            email = printer.getEmail();
            receivingMethod = printer.getReceivingMethod();
        } catch (Exception e) {
            throw new GTechBaseException(ResultErrorCodeEnum.OBTAIN_PRINTER_PIC_EMAIL_ERROR.code(), ResultErrorCodeEnum.OBTAIN_PRINTER_PIC_EMAIL_ERROR.desc());
        }


        String[] split = email.split(",");

        for (String splitEmail : split) {
            sendEmail(voucherBatch, secretCode, ossHelper.grantAccessUrl(accessUrl), splitEmail, receivingMethod);
        }


        return accessUrl;
    }

    private void sendEmail(VoucherBatchResponse voucherBatch, String secretCode, String accessUrl, String email, String receivingMethod) {
        // 配置供应商接收打印券文件的方式；
        if (ReceivingMethodEnum.EMAIL.getCode().equals(receivingMethod)) {
            //发送EXCEL邮件 TEST
            sendExcelToEmail(voucherBatch, accessUrl, email, secretCode, MessageEnventEnum.SEND_PHYSICAL_VOUCHER);
            //发送邮件  Excel密码
            sendExcelToEmail(voucherBatch, null, email, secretCode, MessageEnventEnum.SEND_PHYSICAL_PASSWORD);
        } else if (receivingMethod.equals(ReceivingMethodEnum.FTP.getCode())) {
            uploadFiles(voucherBatch, accessUrl);
            //发送邮件  Excel密码
            sendExcelToEmail(voucherBatch, null, email, secretCode, MessageEnventEnum.SEND_PHYSICAL_PASSWORD);
        } else if (receivingMethod.equals(ReceivingMethodEnum.EMAIL_AND_FTP.getCode()) || receivingMethod.equals(ReceivingMethodEnum.EMAIL_AND_FTP_TWO.getCode())) {
            //发送EXCEL邮件 TEST
            sendExcelToEmail(voucherBatch, accessUrl, email, secretCode, MessageEnventEnum.SEND_PHYSICAL_VOUCHER);
            //发送邮件  Excel密码
            sendExcelToEmail(voucherBatch, null, email, secretCode, MessageEnventEnum.SEND_PHYSICAL_PASSWORD);

            uploadFiles(voucherBatch, accessUrl);
        }
    }

    private void uploadFiles(VoucherBatchResponse voucherBatch, String accessUrl) {
        //上传文件
        CreateVoucherDistributeRequest distRequest = new CreateVoucherDistributeRequest();
        distRequest.setExcelFileUrl(accessUrl);
        distRequest.setTargetFileName(voucherBatch.getFileName() + EXCEL);
        distRequest.setPrinterCode(voucherBatch.getPrinterCode());
        voucherDistributeService.voucherDistributeByPrint(distRequest);
    }


    private void createReceiveVoucherRequest(String createUser, VoucherBatchResponse voucherBatch) {

        VoucherReceive voucherReceiveBySourceDataCode = voucherReceiveService.getVoucherReceiveBySourceDataCode(voucherBatch.getVoucherBatchCode());
        if (null != voucherReceiveBySourceDataCode) {
            return;
        }

        VoucherReceiveBatchRequest batch = BeanCopyUtils.jsonCopyBean(voucherBatch, VoucherReceiveBatchRequest.class);
        batch.setVoucherReceiveBatchCode(voucherBatch.getVoucherBatchCode());
        batch.setReceivedNum(voucherBatch.getVoucherNum());
        CreateVoucherReceiveRequest receiveRequest = new CreateVoucherReceiveRequest();
        receiveRequest.setSourceType(VoucherReceiveSourceTypeEnum.GENERATE.getCode());
        receiveRequest.setSourceDataCode(voucherBatch.getVoucherBatchCode());
        receiveRequest.setIssuerCode(voucherBatch.getIssuerCode());

        try {
            Printer printerEntity = new Printer();
            printerEntity.setPrinterCode(voucherBatch.getPrinterCode());
            Printer printer = printerMapper.selectOne(printerEntity);
            receiveRequest.setOutbound(printer.getPrinterName());
            receiveRequest.setOutboundCode(printer.getPrinterCode());
        } catch (Exception e) {
            throw new GTechBaseException(ResultErrorCodeEnum.PRINTER_ERROR.code(),
                    ResultErrorCodeEnum.PRINTER_ERROR.desc(), voucherBatch.getPrinterCode());
        }
        OutletResponse outlet = outletService.getOutlet(GetOutletRequest.builder().outletCode(issuerWarehouseMap.get(voucherBatch.getIssuerCode())).build());
        receiveRequest.setReceiverCode(outlet.getOutletCode());
        receiveRequest.setInbound(outlet.getOutletName());
        receiveRequest.setVoucherNum(voucherBatch.getVoucherNum());
        receiveRequest.setReceivedNum(0);
        receiveRequest.setStatus(VoucherReceiveStatusEnum.PROCESSING.getCode());
        receiveRequest.setReceiveBatchList(Lists.newArrayList(batch));
        receiveRequest.setCreateUser(createUser);
        voucherReceiveService.createVoucherReceive(receiveRequest);
    }


    //电子券生成
    @Override
    public synchronized Result<String> generateDigitalVoucher(GenerateDigitalVouchersRequest request) throws IOException {


        log.info("开始生成电子券");
        String voucherBatchCode = gvCodeHelper.generateVoucherBatchCode();

        VoucherBatch batch = new VoucherBatch();
        batch.setPurchaseOrderNo(request.getPurchaseOrderNo());
        batch.setIssuerCode(request.getIssuerCode());
        batch.setVoucherBatchCode(voucherBatchCode);
        batch.setMopCode(request.getMopCode());
        batch.setVoucherNum(request.getCustomerOrderDetails().stream().mapToInt(t -> t.getVoucherNum() == null ? 0 : t.getVoucherNum()).sum());
        batch.setDenomination(null != request.getDenomination() ? request.getDenomination() : BigDecimal.ZERO);
        batch.setVoucherEffectiveDate(request.getVoucherEffectiveDate());
        batch.setFileName(gvCodeHelper.generateFileNameCode());
        batch.setFileFormat("Excel");
        batch.setStatus(GENERATING);
        batch.setCreateTime(new Date());
        batch.setCreateUser(request.getCreateUser());
        voucherBatchMapper.insert(batch);

        //添加approvalCode和invoiceNo
        request.setApprovalCode(gvCodeHelper.generateApproveCode());
        request.setInvoiceNo(gvCodeHelper.generateInvoiceNumber());

        //初始化进度条
        Map<String, TaskProgress> progressMap = new HashMap<>();

        // 根据不同面额遍历生成（异步）
        for (GetCustomerOrderDetailsResponse detail : request.getCustomerOrderDetails()) {

            TaskProgress taskProgress = new TaskProgress();
            taskProgress.setTotalUnits(Long.valueOf(detail.getVoucherNum()));
            progressMap.put(detail.getCustomerOrderDetailsCode(), taskProgress);

            //获取前缀
            String prefix = getStringResult(detail.getCpgCode());
            Set<String> digitalVoucherCodes = voucherNumberHelper.voucherCodeElectronicList(prefix, detail.getDenomination(), String.valueOf(Calendar.getInstance().get(Calendar.YEAR)), detail.getVoucherNum(), request.getIssuerCode());
            voucherService.generateECouponsAsynchronously(request, detail, voucherBatchCode, digitalVoucherCodes);
        }

        taskProgressManager.initializeTask(request.getCustomerOrderCode(),progressMap);

        return Result.ok(voucherBatchCode);
    }

    private String getStringResult(String cpgCode) {
        String prefix = "";
        if (StringUtil.isNotEmpty(cpgCode)) {
            try {
                GetCpgRequest cpgRequest = new GetCpgRequest();
                cpgRequest.setCpgCode(cpgCode);
                Result<GetCpgResponse> cpg = cpgService.getCpg(cpgRequest);
                Result<Object> cpgType = cpgTypeService.getCpgType(GetCpgTypeRequest.builder().cpgTypeCode(cpg.getData().getCpgTypeCode()).build());
                if (cpg.getData().getDisableGeneration().equals(VpgDisableGenerationEnum.DISABLED.code())) {
                    throw new GTechBaseException(ResultErrorCodeEnum.GET_CPG_ERROR.code(), ResultErrorCodeEnum.GET_CPG_ERROR.desc());
                }
                GetCpgTypeResponse data = (GetCpgTypeResponse) cpgType.getData();
                prefix = data.getPrefix();
                if (StringUtil.isEmpty(prefix)) {
                    throw new GTechBaseException(ResultErrorCodeEnum.GET_CPG_ERROR.code(), ResultErrorCodeEnum.GET_CPG_ERROR.desc());
                }

            } catch (Exception e) {
                throw new GTechBaseException(ResultErrorCodeEnum.GET_CPG_ERROR.code(), ResultErrorCodeEnum.GET_CPG_ERROR.desc());
            }
        }
        return prefix;
    }

    @Override
    public void sendDigitalVoucherExcelToEmail(SendDigitalVoucherExcelToEmailRequest request) throws IOException {

        Boolean isActivation = new Boolean(false);

        PageBean pageBean = new PageBean();
        pageBean.setPageSize(500);
        pageBean.setPageNum(1);
        String fileName = gvCodeHelper.generateFileNameCode();
        SendVoucherRequest sendRequest = new SendVoucherRequest();
        sendRequest.setVoucherBatchCode(request.getVoucherBatchCode());
        sendRequest.setCreateUser(request.getCreateUser());

        List<ExportDigitalVoucherResponse> voucherResponses = voucherBatchMapper.exportDigital(sendRequest);
        for (ExportDigitalVoucherResponse vs : voucherResponses) {
            String date = vs.getVoucherEffectiveDate();
            String format = DateUtil.format(DateUtil.parseDate(date, DateUtil.FORMAT_YYYYMMDDHHMISS), "dd/MM/yyyy");
            vs.setVoucherEffectiveDate(format);
        }

        List<String> activationCodes = voucherResponses.stream().filter(vo -> StringUtil.isNotEmpty(vo.getActivationCode())).map(ExportDigitalVoucherResponse::getActivationCode).collect(Collectors.toList());
        List<String> activationUrls = voucherResponses.stream().filter(vo -> StringUtil.isNotEmpty(vo.getActivationUrl())).map(ExportDigitalVoucherResponse::getActivationUrl).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(activationCodes) && CollectionUtils.isNotEmpty(activationUrls)) {
            isActivation = true;
        }


        PagedData<ExportDigitalVoucherResponse> voucherData = PagedDatas
                .<ExportDigitalVoucherResponse, PageData<ExportDigitalVoucherResponse>>builder()
                .query(() -> queryDigitalVoucher(sendRequest, pageBean, request.getInvoiceNumber()))
                .queryResultConverter(PageData::getList)
                .hasNextPage(pageResult -> pageBean.getPageNum() <= totalPageSize(pageResult.getTotal(), pageBean.getPageSize()))
                .afterQuery(() ->
                        pageBean.setPageNum(pageBean.getPageNum() + 1)
                ).build();

        //电子券excel暂无密码
        String secretCode = voucherNumberHelper.randomPassword(10);
        ExcelExportSpec.SheetBuilder<ExportDigitalVoucherResponse> sheet = ExcelExportSpec.builder()
                .sheet(ExportDigitalVoucherResponse.class, "Digital Voucher")
                .dataSource(voucherData);

        if (isActivation) {
            sheet.addColumn("ActivationCode", "ActivationCode", ExportDigitalVoucherResponse::getActivationCode);
            sheet.addColumn("ActivationUrl", "ActivationUrl", ExportDigitalVoucherResponse::getActivationUrl);
        }


        FileExport fileExport = FileExport.builder()
                .domainCode(GvcoreConstants.SYSTEM_DEFAULT)
                .tenantCode(GvcoreConstants.SYSTEM_DEFAULT)
                .userId(request.getCreateUser())
                .name(fileName)
                .category("voucher.digital")
                .spec(sheet.build())
                .build();

        String accessUrl = "";
        Resp<FileExportResult> result = FileExportResult.from(fileExportManager.export(fileExport));
        accessUrl = result.getData().getAccessUrl();

        final FileCompressionHelper.EncryptCompressionParameter parameter = FileCompressionHelper.EncryptCompressionParameter.builder()
                .fileUrl(accessUrl)
                .fileName(fileName + EXCEL)
                .password(secretCode)
                .build();
        accessUrl = ossHelper.compressionUploadToOss(parameter, fileName);
        String[] split = request.getEmail().split(",");
        for (String email : split) {

            List<CustomerOrderEmail> emailExistArray = customerOrderEmailMapper.select(new CustomerOrderEmail().setEmailAddress(email).setCustomerOrderCode(request.getCustomerOrderCode()));

            //构建邮件记录对象
            int status;
            //发送EXCEL邮件 TEST
            try {

                //SEND
                VoucherBatchResponse voucherBatch = new VoucherBatchResponse();
                voucherBatch.setFileName(fileName);
                sendExcelToEmail(voucherBatch, ossHelper.grantAccessUrl(accessUrl), email, secretCode, MessageEnventEnum.EGV_CUSTOMER_ORDER_COMPLETED);
                sendExcelToEmail(voucherBatch, null, email, secretCode, MessageEnventEnum.SEND_E_VOUCHER_FILE_PWD);

                status = 1;
                log.info("邮件发送完毕{}",JSON.toJSONString(request));
            } catch (Exception e) {

                //ERROR
                log.error("发送邮件失败", e);
                status = 0;
            }

            //insert
            if (CollectionUtils.isEmpty(emailExistArray)) {
                customerOrderEmailMapper.insertSelective(new CustomerOrderEmail()
                        .setEmailCode(gvCodeHelper.generateCustomerOrderEmailCode())
                        .setEmailAddress(email)
                        .setExcelFileUrl(accessUrl)
                        .setSecretCode(CryptoUtils.aesEncrypt(secretCode))
                        .setCreateUser(request.getCreateUser())
                        .setCustomerOrderCode(request.getCustomerOrderCode())
                        .setFileName(fileName)
                        .setSendStatus(status));
            } else {
                customerOrderEmailMapper.updateByPrimaryKeySelective(new CustomerOrderEmail()
                        .setId(emailExistArray.get(0).getId())
                        .setSendTime(new Date())
                        .setFileName(fileName)
                        .setSecretCode(CryptoUtils.aesEncrypt(secretCode))
                        .setExcelFileUrl(accessUrl)
                        .setSendStatus(status));

            }
        }

    }

    @Override
    @Transactional
    public void resendVoucherExcelEmail(String emailCode) {

        //empty
        if (StringUtils.isBlank(emailCode)) return;

        //find
        CustomerOrderEmail customerOrderEmail = this.customerOrderEmailMapper.selectOne(new CustomerOrderEmail().setEmailCode(emailCode));

        // eq null
        if (null == customerOrderEmail) return;

        //init
        String emailAddress = customerOrderEmail.getEmailAddress();
        String excelFileUrl = customerOrderEmail.getExcelFileUrl();
        String fileName = customerOrderEmail.getFileName();
        String secretCode = CryptoUtils.aesDecrypt(customerOrderEmail.getSecretCode());
        VoucherBatchResponse voucherBatch = new VoucherBatchResponse();
        voucherBatch.setFileName(fileName);

        CustomerOrderEmail saveObj = new CustomerOrderEmail()
                .setId(customerOrderEmail.getId())
                .setSendTime(new Date());

        //send
        try {
            sendExcelToEmail(voucherBatch, excelFileUrl, emailAddress, secretCode, MessageEnventEnum.EGV_CUSTOMER_ORDER_COMPLETED);
            sendExcelToEmail(voucherBatch, null, emailAddress, secretCode, MessageEnventEnum.SEND_E_VOUCHER_FILE_PWD);
            saveObj.setSendStatus(1);

        } catch (Exception e) {
            log.error("重发邮件失败");
            saveObj.setSendStatus(0);
        }

        //save
        this.customerOrderEmailMapper.updateByPrimaryKeySelective(saveObj);
    }

    @Override
    public String barCodeToCode(BarCodeToCodeRequest barCode) {


        return voucherService.getVoucherByBarCode(barCode);
    }

    @SuppressWarnings("unchecked")
    private void sendExcelToEmail(VoucherBatchResponse voucherBatch, String fileUrl, String email, String pwd, MessageEnventEnum type) {

        JSONObject messageRequest = new JSONObject();
        messageRequest.put("eventCode", type.getCode());
        JSONObject param = new JSONObject();
        String json = JSON.toJSONString(voucherBatch);
        param.putAll(JSONObject.parseObject(json, Map.class));
        param.put(VoucherBatch.C_CREATE_TIME, DateUtil.format(voucherBatch.getCreateTime(), GvcoreConstants.EMAIL_DATE_FORMAT));
        param.put("email", email);
        param.put("password", pwd);
        String fileName = voucherBatch.getFileName() + ".zip";
        param.put("filename", fileName);
        if (StringUtil.isNotEmpty(fileUrl)) {
            JSONArray attachments = new JSONArray();
            JSONObject files = new JSONObject();
            files.put("filename", fileName);
            files.put("url", fileUrl);
            attachments.add(files);
            param.put("attachments", attachments);
        }
        messageRequest.put("param", param);
        messageComponent.send(messageRequest);
    }

    private PageData<ExportDigitalVoucherResponse> queryDigitalVoucher(SendVoucherRequest request, PageBean pageBean, String invoiceNubmer) {
        PageHelper.startPage(pageBean.getPageNum(), pageBean.getPageSize());
        List<ExportDigitalVoucherResponse> voucherResponseList = voucherBatchMapper.exportDigital(request);
        PageInfo<ExportDigitalVoucherResponse> info = PageInfo.of(voucherResponseList);
        info.getList().forEach(t -> {
            t.setInvoiceNumber(invoiceNubmer);
            t.setDenomination(new BigDecimal(t.getDenomination()).stripTrailingZeros().toPlainString());
        });

        return new PageData<>(info.getList(), info.getTotal());
    }


    private int totalPageSize(Long total, int pageSize) {
        return (int) (total % pageSize == 0 ? total / pageSize : total / pageSize + 1);
    }


    @Override
    public List<VoucherBatch> queryVoucherBatchByCodeList(List<String> codeList) {
        if (CollectionUtils.isEmpty(codeList)) {
            return Collections.emptyList();
        }
        Example condition = new Example(VoucherBatch.class);
        condition.createCriteria().andIn(VoucherBatch.C_VOUCHER_BATCH_CODE, codeList);
        return voucherBatchMapper.selectByCondition(condition);
    }

}
