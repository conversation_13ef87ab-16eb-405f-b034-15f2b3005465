package com.gtech.gvcore.service.report.impl.bo;


import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.dao.model.UserAccount;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
@Accessors(chain = true)
public class TransactionDetailedBo {

    private String transactionType;

    private String outletCode;

    private String voucherCode;

    private String createUser;

    private String posCode;

    private String merchantCode;

    private String batchCode;

    private String loginSource;

    private BigDecimal denomination;

    private String actualOutlet;

    private String forwardingEntityId;

    private String responseMessage;

    private String transactionMode;

    private String customerSalutation;

    private String customerFirstName;

    private String customerLastName;

    private String mobile;

    private String invoiceNumber;

    private String otherInputParameter;

    private String transactionDate;

    public Date getTransactionDate() {

        return DateUtil.parseDate(transactionDate, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

    public String getCreateUser(JoinDataMap<UserAccount> userAccountMap) {

        UserAccount userAccount = userAccountMap.findValue(createUser);

        return ConvertUtils.toString(userAccount.getFirstName(), StringUtils.EMPTY) + " " + ConvertUtils.toString(userAccount.getLastName(), StringUtils.EMPTY);
    }
}
