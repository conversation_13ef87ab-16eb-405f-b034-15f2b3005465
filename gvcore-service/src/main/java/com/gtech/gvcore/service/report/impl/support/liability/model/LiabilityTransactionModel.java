package com.gtech.gvcore.service.report.impl.support.liability.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName LiabilityTransactionModel
 * @Description LiabilityTransactionModel
 * <AUTHOR>
 * @Date 2023/4/20 16:08
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class LiabilityTransactionModel {

    private String issuerCode;

    private String merchantCode;

    private String outletCode;

    private String voucherCode;

}
