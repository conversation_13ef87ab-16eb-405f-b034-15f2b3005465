package com.gtech.gvcore.service.report.impl;

import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.DeactivatedForSummaryBean;
import com.gtech.gvcore.service.report.impl.bo.DeactivatedBo;
import com.gtech.gvcore.service.report.impl.param.DeactivatedQueryData;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:57
 * @Description:
 */
@Service
public class DeactivatedForSummaryImpl extends ReportSupport
        implements BusinessReport<DeactivatedQueryData, DeactivatedForSummaryBean>, SingleReport {

    @Override
    public DeactivatedQueryData builderQueryParam(CreateReportRequest reportParam) {

        DeactivatedQueryData queryData = new DeactivatedQueryData();

        queryData.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        queryData.setMerchantCodeList(reportParam.getMerchantCodes());
        queryData.setOutletCodeList(reportParam.getOutletCodes());

        queryData.setTransactionDateStart(reportParam.getTransactionDateStart());
        queryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());

        queryData.setCpgCodeList(reportParam.getCpgCodes());
        queryData.setInvoiceNumber(reportParam.getInvoiceNo());

        queryData.setVoucherEffectiveDateStart(reportParam.getVoucherEffectiveDateStart());
        queryData.setVoucherEffectiveDateEnd(reportParam.getVoucherEffectiveDateEnd());

        queryData.setTransactionType(TransactionTypeEnum.GIFT_CARD_DEACTIVATE.getCode());

        return queryData;
    }

    @Override
    public List<DeactivatedForSummaryBean> getExportData(DeactivatedQueryData queryData) {

        //find
        Collection<DeactivatedSummaryStatisticalBo> list =
                Optional.of(PollPageHelper.pollGroupNewTransactionByCodeSelect(reportBusinessMapper::selectDeactivated, queryData))
                        .map(e -> e.stream()
                                .collect(Collectors.toMap(
                                        DeactivatedSummaryStatisticalBo::groupKey,
                                        DeactivatedSummaryStatisticalBo::convert,
                                        DeactivatedSummaryStatisticalBo::merge))
                        ).map(Map::values)
                        .orElseGet(Collections::emptyList);

        //empty
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(list, DeactivatedSummaryStatisticalBo::getCpgCode, Cpg.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(list, DeactivatedSummaryStatisticalBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(list, DeactivatedSummaryStatisticalBo::getOutletCode, Outlet.class);

        //convert result
        return list.stream()
                .map(e -> new DeactivatedForSummaryBean()
                        .setVoucherAmount(super.toAmount(e.getTotalAmount()))
                        .setNumberOfVouchers(String.valueOf(e.getNumberOfVouchers()))
                        .setVpg(cpgMap.findValue(e.getCpgCode()).getCpgName())
                        .setMerchantOut(outletMap.findValue(e.getOutletCode()).getOutletName())
                        .setMerchant(merchantMap.findValue(e.getMerchantCode()).getMerchantName()))
                .collect(Collectors.toList());
    }

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.BLOCKED_AND_DEACTIVATED_SUMMARY;
    }


    @Getter
    @Setter
    @Accessors(chain = true)
    public static class DeactivatedSummaryStatisticalBo {

        /**
         * Merchant code.
         */
        private String merchantCode;

        /**
         * Outlet code.
         */
        private String outletCode;

        /**
         * Cpg code.
         */
        private String cpgCode;

        /**
         * Number of vouchers.
         */
        private int numberOfVouchers = 1;

        /**
         * Total amount.
         */
        private BigDecimal totalAmount;

        public static DeactivatedSummaryStatisticalBo convert (DeactivatedBo reactivatedBo) {

            return new DeactivatedSummaryStatisticalBo()
                    .setMerchantCode(reactivatedBo.getMerchantCode())
                    .setOutletCode(reactivatedBo.getOutletCode())
                    .setCpgCode(reactivatedBo.getCpgCode())
                    .setTotalAmount(reactivatedBo.getDenomination());
        }

        public DeactivatedSummaryStatisticalBo merge (DeactivatedSummaryStatisticalBo bo) {

            this.numberOfVouchers += bo.getNumberOfVouchers();
            this.totalAmount = this.totalAmount.add(bo.getTotalAmount());

            return this;
        }

        public static String groupKey (DeactivatedBo reactivatedBo) {

            return StringUtils.join(",", reactivatedBo.getMerchantCode(), reactivatedBo.getOutletCode(), reactivatedBo.getCpgCode());
        }

    }
}
