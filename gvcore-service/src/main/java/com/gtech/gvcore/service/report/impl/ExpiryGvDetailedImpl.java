package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.enums.VoucherOwnerTypeEnum;
import com.gtech.gvcore.common.utils.GvConvertUtils;
import com.gtech.gvcore.dao.mapper.CustomerOrderMapper;
import com.gtech.gvcore.dao.mapper.TransactionDataMapper;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.ExpiryGvDetailedBean;
import com.gtech.gvcore.service.report.impl.param.ExpiryGvQueryData;
import com.gtech.gvcore.service.report.impl.support.expiry.ExpiryGvBaseImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.Sqls;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:59
 * @Description:
 */
@Service
public class ExpiryGvDetailedImpl extends ExpiryGvBaseImpl<ExpiryGvDetailedBean>
        implements BusinessReport<ExpiryGvQueryData, ExpiryGvDetailedBean>, SingleReport {

    private static final String REGEX = "[^0-9]";

    @Autowired private TransactionDataMapper transactionDataMapper;
    @Autowired private CustomerOrderMapper customerOrderMapper;

    @Override
    public List<ExpiryGvDetailedBean> getExportData(ExpiryGvQueryData queryData) {

        List<String> voucherCode = super.getVoucherCodeList(queryData);

        if (CollectionUtils.isEmpty(voucherCode)) return Collections.emptyList();

        final JoinDataMap<Voucher> voucherMap = super.getMapByCode(voucherCode, Voucher.class);
        final List<Voucher> vouchers = new ArrayList<>(voucherMap.values());

        if (CollectionUtils.isEmpty(vouchers)) return Collections.emptyList();

        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(vouchers, Voucher::getCpgCode, Cpg.class);
        final JoinDataMap<Customer> customerMap = super.getMapByCode(vouchers.stream().filter(v -> VoucherOwnerTypeEnum.CUSTOMER.equalsCode(v.getVoucherOwnerType())).map(Voucher::getVoucherOwnerCode).distinct().collect(Collectors.toList()), Customer.class);
        final JoinDataMap<CustomerOrder> customerOrderMap = this.joinCustomerOrder(vouchers);

        return voucherCode.stream()
                .filter(voucherMap::containsKey)
                .sorted((v1, v2) -> {
                    long v1Long = Long.parseLong(v1.replaceAll(REGEX, ""));
                    long v2Long = Long.parseLong(v2.replaceAll(REGEX, ""));
                    return Long.compare(v1Long, v2Long);
                }).map(e -> {

                    final Voucher voucher = voucherMap.findValue(e);
                    final CustomerOrder customerOrder = customerOrderMap.findValue(voucher.getVoucherCode());

                    final ExpiryGvDetailedBean expiryGvDetailedBean = new ExpiryGvDetailedBean();

                    expiryGvDetailedBean.setVoucherNumber(voucher.getVoucherCode());
                    expiryGvDetailedBean.setBookletNumber(voucher.getBookletCode());
                    expiryGvDetailedBean.setCardStatus(this.getVoucherStatusDesc(voucher));

                    if (VoucherOwnerTypeEnum.CUSTOMER.equalsCode(voucher.getVoucherOwnerType())) {
                        expiryGvDetailedBean.autoFull(customerMap.findValue(voucher.getVoucherOwnerCode()));
                    }

                    if (null != customerOrder) {
                        expiryGvDetailedBean.setPurchaseDate(DateUtil.format(customerOrder.getCreateTime(), DateUtil.FORMAT_YYYYMMDDHHMISS));
                        expiryGvDetailedBean.setInvoiceNumber(customerOrder.getInvoiceNo());
                    }
                    expiryGvDetailedBean.setVoucherProgramGroup(cpgMap.findValue(voucher.getCpgCode()).getCpgName());
                    expiryGvDetailedBean.setDenomination(super.toAmount(ConvertUtils.toBigDecimal(voucher.getDenomination(), BigDecimal.ZERO)));
                    expiryGvDetailedBean.setExpiryDate(DateUtil.format(voucher.getVoucherEffectiveDate(), "yyyy/MM/dd"));

                    expiryGvDetailedBean.setIssuanceYear(DateUtil.format(voucher.getCreateTime(), DateUtil.FORMAT_YYYY));

                    return expiryGvDetailedBean;

                }).collect(Collectors.toList());
    }

    public JoinDataMap<CustomerOrder> joinCustomerOrder(List<Voucher> voucherlist) {

        final ConcurrentHashMap<String, CustomerOrder> customerOrderMap = new ConcurrentHashMap<>();

        //分批处理
        ListUtils.partition(voucherlist, 1000)
                //并行处理
                .parallelStream()
                .forEach(vouchers -> {

                    //init
                    final List<Voucher> vceVoucher = vouchers.stream().filter(v -> GvcoreConstants.MOP_CODE_VCE.equals(v.getMopCode())).collect(Collectors.toList());
                    final List<Voucher> vcrVoucher = vouchers.stream().filter(v -> GvcoreConstants.MOP_CODE_VCR.equals(v.getMopCode())).collect(Collectors.toList());

                    //vce
                    //将卡券转换成批次号集合
                    Optional.of(vceVoucher.stream().map(Voucher::getVoucherBatchCode).distinct().collect(Collectors.toList()))
                            //过滤空
                            .filter(CollectionUtils::isNotEmpty)
                            //转换成订单集合
                            .map(batchCodeList -> customerOrderMapper.selectByCondition(Example.builder(CustomerOrder.class)
                                    .select(CustomerOrder.C_CREATE_TIME, CustomerOrder.C_INVOICE_NO, CustomerOrder.C_VOUCHER_BATCH_CODE)
                                    .where(Sqls.custom()
                                            .andIn(CustomerOrder.C_VOUCHER_BATCH_CODE, batchCodeList))
                                    .build())
                            //过滤空
                            ).filter(CollectionUtils::isNotEmpty)
                            //转换成批次号 -> 订单的map
                            .map(customerOrders -> customerOrders.stream().collect(Collectors.toMap(CustomerOrder::getVoucherBatchCode, e -> e)))
                            //遍历vce的voucher，将订单放入map
                            .ifPresent(e -> vceVoucher.forEach(v -> customerOrderMap.put(v.getVoucherCode(), GvConvertUtils.toObject(e.get(v.getVoucherBatchCode()), new CustomerOrder()))));

                    //vcr
                    //将卡券转换成卡券编号集合
                    Optional.of(vcrVoucher.stream().map(Voucher::getVoucherCode).distinct().collect(Collectors.toList()))
                            //过滤空
                            .filter(CollectionUtils::isNotEmpty)
                            //通过卡券编号查询 sell or cancel sell
                            .map(voucherCodeList ->
                                            //查询
                                            transactionDataMapper.selectByCondition(Example.builder(TransactionData.class)
                                                    //查询字段设置 voucherCode, transactionId, transactionType
                                                    .select(TransactionData.C_VOUCHER_CODE, TransactionData.C_TRANSACTION_ID, TransactionData.C_TRANSACTION_TYPE)
                                                    //条件构建
                                                    .where(Sqls.custom()
                                                            //交易类型为 sell or cancel sell
                                                            .andIn(TransactionData.C_TRANSACTION_TYPE, Stream.of(TransactionTypeEnum.GIFT_CARD_SELL.getCode(), TransactionTypeEnum.GIFT_CARD_CANCEL_SELL.getCode()).collect(Collectors.toList()))
                                                            //卡券编号 IN
                                                            .andIn(TransactionData.C_VOUCHER_CODE, voucherCodeList)
                                                    ).build())
                                    //过滤空
                            ).filter(CollectionUtils::isNotEmpty)
                            // 将交易集合中的信息过滤获得卡券最新的交易
                            .map(e -> e.stream().collect(Collectors.toMap(TransactionData::getVoucherCode, Function.identity(), (e1, e2) -> e2)).values())
                            // 将交易集合中非sell的交易过滤 并转换成 卡券编号 -> 订单编号
                            .map(e -> e.stream().filter(t -> TransactionTypeEnum.GIFT_CARD_SELL.getCode().equals(t.getTransactionType())).collect(Collectors.toMap(TransactionData::getVoucherCode, TransactionData::getTransactionId)))
                            // 将 (卡券编号 -> 订单编号) 转换成 (卡券编号 -> 订单)
                            .map(e ->
                                            //of
                                            Optional.of(e)
                                                    //转换成订单编号
                                                    .map(Map::values)
                                                    //查询订单信息
                                                    .map(orderCodeList ->
                                                            customerOrderMapper.selectByCondition(Example.builder(CustomerOrder.class)
                                                                    //查询字段设置 createTime, invoiceNo
                                                                    .select(CustomerOrder.C_CREATE_TIME, CustomerOrder.C_INVOICE_NO, CustomerOrder.C_CUSTOMER_ORDER_CODE)
                                                                    //条件构建
                                                                    .where(Sqls.custom().andIn(CustomerOrder.C_CUSTOMER_ORDER_CODE, orderCodeList))
                                                                    .build())
                                                    //过滤空
                                                    ).filter(CollectionUtils::isNotEmpty)
                                                    //转换成 订单编号 -> 订单
                                                    .map(customerOrders -> customerOrders.stream()
                                                            .collect(Collectors.toMap(
                                                                    //key
                                                                    CustomerOrder::getCustomerOrderCode
                                                                    //减少内存负载 去除多余数据
                                                                    , Function.identity()
                                                                    //ignore
                                                                    , (e1, e2) -> e2))
                                                    //转换成 卡券编号 -> 订单
                                                    ).map(customerOrders -> {
                                                        // init
                                                        final Map<String, CustomerOrder> voucherCustomerOrderMap = new HashMap<>();
                                                        // match order
                                                        e.forEach((k, v) -> voucherCustomerOrderMap.put(k, GvConvertUtils.toObject(customerOrders.get(v), new CustomerOrder())));
                                                        //return
                                                        return voucherCustomerOrderMap;
                                                        //orElse
                                                    }).orElse(new HashMap<>())
                                    //存入结果
                            ).ifPresent(customerOrderMap::putAll);

                });


        final JoinDataMap<CustomerOrder> joinDataMap = new JoinDataMap<>();
        joinDataMap.putAll(customerOrderMap);

        return joinDataMap;
    }

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.EXPIRY_GV_DETAILED_REPORT;
    }

}
