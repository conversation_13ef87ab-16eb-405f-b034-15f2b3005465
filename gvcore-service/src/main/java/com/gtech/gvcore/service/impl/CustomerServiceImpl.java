package com.gtech.gvcore.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.gtech.basic.idm.dao.entity.OpUserAccountEntity;
import com.gtech.basic.idm.dao.entity.OpUserTenantMappingEntity;
import com.gtech.basic.idm.dao.mapper.IOpUserAccountMapper;
import com.gtech.basic.idm.dao.mapper.IOpUserTenantMappingMapper;
import com.gtech.basic.idm.service.OpUserAccountService;
import com.gtech.basic.idm.service.dto.CreateOpUserAccount;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.redis.GTechRedisTemplate;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.CryptoUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.MessageEnventEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.customer.CheckCustomerInfoRequest;
import com.gtech.gvcore.common.request.customer.CreateCustomerRequest;
import com.gtech.gvcore.common.request.customer.CreateIncompleteCustomerRequest;
import com.gtech.gvcore.common.request.customer.DeleteCustomerRequest;
import com.gtech.gvcore.common.request.customer.GetCustomerRequest;
import com.gtech.gvcore.common.request.customer.GetEmailValidateCodeRequest;
import com.gtech.gvcore.common.request.customer.QueryCustomerByUserCodeRequest;
import com.gtech.gvcore.common.request.customer.QueryCustomerCompanyNameRequest;
import com.gtech.gvcore.common.request.customer.QueryCustomerRequest;
import com.gtech.gvcore.common.request.customer.UpdateCustomerRequest;
import com.gtech.gvcore.common.request.customer.UpdateCustomerStatusRequest;
import com.gtech.gvcore.common.request.customer.ValidateEmailRequest;
import com.gtech.gvcore.common.request.customerpaymentmethod.CreateCustomerPaymentMethodRequest;
import com.gtech.gvcore.common.request.customerproductcategory.CreateCustomerProductCategoryRequest;
import com.gtech.gvcore.common.request.flow.SendEmailRequest;
import com.gtech.gvcore.common.response.customer.CustomerResponse;
import com.gtech.gvcore.common.response.customer.ProductCategory;
import com.gtech.gvcore.common.response.customerproductcategory.CustomerProductCategoryResponse;
import com.gtech.gvcore.dao.mapper.CustomerMapper;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.helper.VoucherNumberHelper;
import com.gtech.gvcore.service.CustomerPaymentMethodService;
import com.gtech.gvcore.service.CustomerProductCategoryService;
import com.gtech.gvcore.service.CustomerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/2/18 14:11
 */
@Service
@Slf4j
public class CustomerServiceImpl implements CustomerService {

    @Autowired
    private CustomerMapper customerMapper;

    @Autowired
    private CustomerProductCategoryService customerProductCategoryService;

    @Autowired
    private CustomerPaymentMethodService customerPaymentMethodService;

    @Autowired
    private MessageComponent messageComponent;

    @Autowired
    private GvCodeHelper codeHelper;

    @Autowired
    private GTechRedisTemplate gTechRedisTemplate;


    @Autowired
    private VoucherNumberHelper voucherNumberHelper;


    @Autowired
    private OpUserAccountService opUserAccountService;

    @Autowired
    private IOpUserTenantMappingMapper iOpUserTenantMappingMapper;


    @Autowired
    private IOpUserAccountMapper userAccountMapper;


    private static final String MIX_SALT = "26a711f75b90055fe169fca73a0e0f03";

    private static final String VERIFICATION_CODE = "GV:CUSTOMER:VERIFICATION_CODE:";

    private static final String NO_INPUT = "NO_INPUT";


    @Override
    public PageResult<String> queryCustomerCompanyName(QueryCustomerCompanyNameRequest request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<String> companyNames = customerMapper.queryCustomerCompanyName(request);

        PageInfo<String> info = PageInfo.of(companyNames);

        return new PageResult<>(info.getList(), info.getTotal());
    }

    @Transactional
    @Override
    public Result<String> createCustomer(CreateCustomerRequest param) {

        Customer entity = BeanCopyUtils.jsonCopyBean(param, Customer.class);
        entity.setCustomerCode(codeHelper.generateCustomerCode());
        entity.setCreateTime(new Date());
        //默认开启
        entity.setStatus(GvcoreConstants.STATUS_ENABLE);
        customerMapper.insertSelective(entity);


        if (CollectionUtils.isNotEmpty(param.getProductCategoryCodeList())){
            //Customer product category
            CreateCustomerProductCategoryRequest productRequest = BeanCopyUtils.jsonCopyBean(param, CreateCustomerProductCategoryRequest.class);
            productRequest.setCustomerCode(entity.getCustomerCode());
            customerProductCategoryService.createCustomerProductCategory(productRequest);
        }


        if (StringUtil.isNotEmpty(param.getMopGroup())){
            String mopGroup = param.getMopGroup();

            String[] split = mopGroup.split(",");
            for (String mop : split) {
                //Customer payment method
                CreateCustomerPaymentMethodRequest paymentRequest = BeanCopyUtils.jsonCopyBean(param, CreateCustomerPaymentMethodRequest.class);
                paymentRequest.setCustomerCode(entity.getCustomerCode());
                paymentRequest.setMopGroup(mop);
                paymentRequest.setCreateUser(param.getCreateUser());
                customerPaymentMethodService.createCustomerPaymentMethod(paymentRequest);
            }
        }



        /**
         * user Email
         */
        if (StringUtil.isNotEmpty(param.getDistributionFunction()) && "1".equals(param.getDistributionFunction()) && StringUtil.isNotEmpty(param.getUserEmail())){
            createOpUserAccount(param.getUserEmail(), entity.getCustomerCode());
        }

        return Result.ok(entity.getCustomerCode());


    }



    private void disabledOpUserAccount(UpdateCustomerRequest param) {

        //删除customer原有关系
        //遍历邮箱,判断此邮箱是否还绑定有其他customer
        //如果没有绑定其他customer,逻辑删除

        String customerCode = param.getCustomerCode();

        //查询所有的关联关系，删除关联关系
        deleteOpUserTenantMapping(customerCode);

        if (StringUtil.isEmpty(param.getUserEmail())){
            return;
        }


        String[] userEmailSplit = param.getUserEmail().split(";");
        List<String> list = Arrays.asList(userEmailSplit);
        HashSet<String> strings = new HashSet<>(list);

        for (String email : strings) {
            email = email.trim();
            OpUserAccountEntity byCondition = this.getOpUserAccount(email, null, email);
            if (null== byCondition){
                continue;
            }
            //查询剩余绑定的customer
            Example iOpUserTenantMapping = new Example(OpUserTenantMappingEntity.class);
            iOpUserTenantMapping.createCriteria().andEqualTo("userCode",byCondition.getUserCode());
            List<OpUserTenantMappingEntity> opUserTenantMappingEntities = iOpUserTenantMappingMapper.selectByCondition(iOpUserTenantMapping);
            if (0 == opUserTenantMappingEntities.size()){
                opUserAccountService.deleteByUserCode(byCondition.getUserCode(),"system");
            }
        }
    }



    public OpUserAccountEntity getOpUserAccount(String account, String mobile, String email) {

        OpUserAccountEntity entity = OpUserAccountEntity.builder().account(account)
                .mobile(mobile).email(email).build();

        encrypt(entity);

        entity = userAccountMapper.selectOne(entity);
        if (null != entity) {
            this.decrypt(entity);
        }

        return entity;
    }




    private void createOpUserAccount(String  userEmail, String customerCode) {

        //删除所有关联关系
        //遍历邮箱
        //查询opUserAccount数据
        //如果为空,创建账号并且发送邮件
        //如果是逻辑删除,改为解锁
        //如果不为空并且可用,添加关联关系




        //查询所有的关联关系，删除关联关系
        deleteOpUserTenantMapping(customerCode);

        String[] userEmailSplit = userEmail.split(";");
        List<String> list = Arrays.asList(userEmailSplit);
        HashSet<String> strings = new HashSet<>(list);

        for (String email : strings) {
            email = email.trim();
            OpUserAccountEntity byCondition = this.getOpUserAccount(email, null, email);
            String userCode = codeHelper.generateOpUserCode();
            if (null == byCondition){
                //创建账号并且发送邮件
                createOpUserAndSendEmail(customerCode, email, userCode);
            }else if (byCondition.getLogicDelete()==1){
                //解除逻辑删除
                updateLogicDelete(byCondition);
                userCode = byCondition.getUserCode();
            }else{
                userCode = byCondition.getUserCode();
            }
            //创建关联关系
            createOpUserTenantMapping(customerCode, userCode);
        }
    }

    private void deleteOpUserTenantMapping(String customerCode) {
        Example example = new Example(OpUserTenantMappingEntity.class);
        example.createCriteria().andEqualTo("tenantCode", customerCode);
        iOpUserTenantMappingMapper.deleteByCondition(example);
    }

    private void createOpUserTenantMapping(String customerCode, String userCode) {
        OpUserTenantMappingEntity opUserTenantMappingEntity = new OpUserTenantMappingEntity();
        opUserTenantMappingEntity.setTenantCode(customerCode);
        opUserTenantMappingEntity.setDomainCode("SYSTEM_DEFAULT");
        opUserTenantMappingEntity.setUserCode(userCode);
        opUserTenantMappingEntity.setLogicDelete(0);
        try {
            iOpUserTenantMappingMapper.insertSelective(opUserTenantMappingEntity);
        } catch (DuplicateKeyException e) {
            throw new GTechBaseException(ResultErrorCodeEnum.DUPLICATE_ACCOUNT.code(),ResultErrorCodeEnum.DUPLICATE_ACCOUNT.desc());
        }
    }

    private void createOpUserAndSendEmail(String customerCode, String email, String userCode) {
        String randomPswd = voucherNumberHelper.randomPassword(6);
        CreateOpUserAccount request = new CreateOpUserAccount();
        request.setUserCode(userCode);
        request.setAccount(email);
        request.setPassword(randomPswd);
        request.setEmail(email);
        request.setUserType(1);
        request.setUserCreateType(0);
        opUserAccountService.createUserAccount(request);
        try {
            sendAccountToEmail(customerCode, email,randomPswd, email,MessageEnventEnum.CREATE_B_CUSTOMER);
        } catch (IOException e) {
            log.error("发送email失败", email);
        }
    }

    private void updateLogicDelete(OpUserAccountEntity byCondition) {
        OpUserAccountEntity accountEntity = new OpUserAccountEntity();
        accountEntity.setLogicDelete(0);

        Example tombstone = new Example(OpUserAccountEntity.class);
        tombstone.createCriteria().andEqualTo("userCode", byCondition.getUserCode());

        userAccountMapper.updateByConditionSelective(accountEntity,tombstone);
    }


    private void sendAccountToEmail(String customerCode,String account,String randomPswd, String email, MessageEnventEnum type) throws IOException {
        HashMap<String, Object> map = new HashMap<>();
        map.put("account",email);
        map.put("password",randomPswd);

        SendEmailRequest request = new SendEmailRequest();
        request.setEmails(Lists.newArrayList(email));
        request.setBusinessCode(customerCode);
        request.setExtendParams(map);
        messageComponent.sendEmail(request,type.getCode());
    }



    @Transactional
    @Override
    public Result<Void> updateCustomer(UpdateCustomerRequest param) {


        Customer entity = BeanCopyUtils.jsonCopyBean(param, Customer.class);
        entity.setUpdateTime(new Date());

        Example example = new Example(Customer.class);
        example.createCriteria()
                .andEqualTo(Customer.C_CUSTOMER_CODE, param.getCustomerCode());
        customerMapper.updateByConditionSelective(entity, example);


        //Customer product category 先删除再添加
        if (CollectionUtils.isNotEmpty(param.getProductCategoryCodeList())) {
            customerProductCategoryService.deleteCustomerProductCategoryByCustomer(param.getCustomerCode());
            for (String productCode : param.getProductCategoryCodeList()) {
                CreateCustomerProductCategoryRequest request = new CreateCustomerProductCategoryRequest();
                request.setCustomerCode(param.getCustomerCode());
                request.setProductCategoryCode(productCode);
                request.setCreateUser(param.getUpdateUser());
                // 注意：此处不需要设置categoryType，CustomerProductCategoryService会根据productCode查询对应的类型
                customerProductCategoryService.createCustomerProductCategory(request);
            }
        }

        //Customer payment method  先删除再添加
        if (StringUtil.isNotEmpty(param.getMopGroup())) {
            customerPaymentMethodService.deleteCustomerPaymentMethodByCustomer(param.getCustomerCode());


            String mopGroup = param.getMopGroup();

            String[] split = mopGroup.split(",");
            for (String mop : split) {
                //Customer payment method
                CreateCustomerPaymentMethodRequest paymentRequest = new CreateCustomerPaymentMethodRequest();
                paymentRequest.setMopGroup(mop);
                paymentRequest.setCustomerCode(param.getCustomerCode());
                paymentRequest.setCreateUser(param.getUpdateUser());
                customerPaymentMethodService.createCustomerPaymentMethod(paymentRequest);
            }
        }



        /**
         * user Email
         */
        if (StringUtil.isNotEmpty(param.getDistributionFunction()) && "1".equals(param.getDistributionFunction()) && StringUtil.isNotEmpty(param.getUserEmail())){
            createOpUserAccount(param.getUserEmail(), entity.getCustomerCode());
        } else {
            disabledOpUserAccount(param);
        }

        return Result.ok();
    }

    @Override
    public Result<Void> deleteCustomer(DeleteCustomerRequest param) {
        Example example = new Example(Customer.class);
        example.createCriteria()
                .andEqualTo(Customer.C_CUSTOMER_CODE, param.getCustomerCode());
        customerMapper.deleteByCondition(example);
        return Result.ok();
    }

    @Override
    public PageResult<CustomerResponse> queryCustomerList(QueryCustomerRequest param) {

        PageHelper.startPage(param.getPageNum(), param.getPageSize());

        List<CustomerResponse> gvCustomerEntities = customerMapper.queryCustomerList(param);
        //product category
        PageInfo<CustomerResponse> info = PageInfo.of(gvCustomerEntities);

        List<String> customerCodeList = info.getList().stream().map(CustomerResponse::getCustomerCode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customerCodeList)) return new PageResult<>();

        List<CustomerProductCategoryResponse> response = customerProductCategoryService.queryCustomerProductCategoryListByCustomerList(customerCodeList);

        Map<String, List<CustomerProductCategoryResponse>> categoryByCustomerCode = response.stream().collect(Collectors.groupingBy(x -> x.getCustomerCode()));



        for (CustomerResponse customerResponse : info.getList()) {

            List<CustomerProductCategoryResponse> customerProductCategoryResponses = categoryByCustomerCode.get(customerResponse.getCustomerCode());

            if (null == response) {
                return new PageResult<>(BeanCopyUtils.jsonCopyList(info.getList(), CustomerResponse.class), info.getTotal());
            }
            List<ProductCategory> productCategories = BeanCopyUtils.jsonCopyList(customerProductCategoryResponses, ProductCategory.class);
            customerResponse.setProductCategoryList(productCategories);
            //创建订单,获取customerName
            //按照Company(customerName) 拼接companyName和customerName 如果customerName或者companyName其中一个为空,则不拼接
            String companyName = customerResponse.getCompanyName();
            String customerName = customerResponse.getCustomerName();

            if (StringUtils.isNotEmpty(companyName) && StringUtils.isNotEmpty(customerName)) {
                customerResponse.setCompanyAndBranchName(companyName + "(" + customerName + ")");
            } else if (StringUtils.isNotEmpty(companyName)) {
                customerResponse.setCompanyAndBranchName(companyName);
            } else if (StringUtils.isNotEmpty(customerName)) {
                customerResponse.setCompanyAndBranchName(customerName);
            }




            /*if (StringUtil.isNotEmpty(customerResponse.getCompanyName())){
                customerResponse.setCompanyAndBranchName(customerResponse.getCompanyName() + "(" + customerResponse.getCustomerName()+ ")");
            }else {
                customerResponse.setCompanyAndBranchName(customerResponse.getCustomerName());
            }*/
        }


        return new PageResult<>(BeanCopyUtils.jsonCopyList(info.getList(), CustomerResponse.class), info.getTotal());
    }

    @Override
    public CustomerResponse getCustomer(GetCustomerRequest param) {
        CustomerResponse customer = customerMapper.getCustomer(param);
        //product category
        List<CustomerProductCategoryResponse> response = customerProductCategoryService.queryCustomerProductCategoryListByCustomer(customer.getCustomerCode());
        if (null == response) {
            return customer;
        }
        List<ProductCategory> customerResponses = BeanCopyUtils.jsonCopyList(response, ProductCategory.class);
        customer.setProductCategoryList(customerResponses);
        return customer;
    }

    @Override
    public Customer getCustomerSummary(String customerCode) {

        if (StringUtils.isBlank(customerCode)) return null;

        return customerMapper.selectOne(new Customer().setCustomerCode(customerCode));
    }

    @Override
    public Map<String, Customer> getCustomerByCode(List<String> customerCode) {

        if (CollectionUtils.isEmpty(customerCode)) return new HashMap<>();

        Example example = new Example(Customer.class);
        example.createCriteria().andIn(Customer.C_CUSTOMER_CODE, customerCode);

        List<Customer> customers = this.customerMapper.selectByCondition(example);

        if (CollectionUtils.isEmpty(customers)) return new HashMap<>();

        return customers.stream().collect(Collectors.toMap(Customer::getCustomerCode, e -> e));
    }

    @Override
    public Result<Void> updateCustomerStatus(UpdateCustomerStatusRequest param) {
        Customer entity = BeanCopyUtils.jsonCopyBean(param, Customer.class);
        entity.setUpdateTime(new Date());

        Example example = new Example(Customer.class);
        example.createCriteria()
                .andEqualTo(Customer.C_CUSTOMER_CODE, param.getCustomerCode());


        customerMapper.updateByConditionSelective(entity, example);

        return Result.ok();
    }

    @Override
    public Result<Boolean> validateEmail(ValidateEmailRequest validateEmailRequest) {
        String code = gTechRedisTemplate.opsValueGet("GV", VERIFICATION_CODE + validateEmailRequest.getEmail(), String.class);
        if (code == null) {
            return Result.failed(ResultErrorCodeEnum.VALIDATED_CODE_NOT_EXISTS.code(), ResultErrorCodeEnum.VALIDATED_CODE_NOT_EXISTS.desc());
        }
        return code.equals(validateEmailRequest.getValidatedCode()) ? Result.ok(true) : Result.failed(ResultErrorCodeEnum.VALIDATED_CODE_ERROR.code(), ResultErrorCodeEnum.VALIDATED_CODE_ERROR.desc());
    }

    @Override
    public Result<Boolean> getEmailValidateCode(String email,String issuerCode) {
        Boolean aBoolean = customerMapper.selectEmailExists(email,issuerCode);
        if (Boolean.TRUE.equals(aBoolean)) {
            Integer code = (int) ((Math.random() * 9 + 1) * 100000);
            gTechRedisTemplate.opsValueSet("GV", VERIFICATION_CODE + email, code.toString(), 600000L);
            sendEmail(email, code);
            return Result.ok(true);
        } else {
            return Result.ok(false);
        }
    }

    private void sendEmail(String email, Integer code) {
        JSONObject messageRequest = new JSONObject();
		messageRequest.put("eventCode", MessageEnventEnum.CUSTOMER_VERIFICATION_CODE.getCode());
        JSONObject param = new JSONObject();
        param.put("email", email);
        param.put("verificationCode", code);
        messageRequest.put("param", param);
        messageComponent.send(messageRequest);
    }

    @Override
    public Result<String> createIncompleteCustomer(CreateIncompleteCustomerRequest createIncompleteCustomerRequest) {
        Customer customer = BeanCopyUtils.jsonCopyBean(createIncompleteCustomerRequest, Customer.class);
        customer.setCustomerCode(codeHelper.generateCustomerCode());
        // MER-1884 customer.setIssuerCode(NO_INPUT);
        customer.setContactDivision(NO_INPUT);
        customer.setTransferAccount(NO_INPUT);
        customer.setBankCardIssuer(NO_INPUT);
        customer.setNote(NO_INPUT);
        customer.setStatus(1);
        customer.setCreateUser(createIncompleteCustomerRequest.getCustomerName());
        customerMapper.insertSelective(customer);
        return Result.ok(customer.getCustomerCode());
    }

    @Override
    public Result<Boolean> checkCustomerInfo(CheckCustomerInfoRequest checkCustomerInfo) {

        Customer customer = customerMapper.selectOne(Customer.builder().customerCode(checkCustomerInfo.getCustomerCode()).build());
		return Result.ok(!customer.getBankCardIssuer().equals(NO_INPUT));
    }

	@Override
	public List<Customer> queryCustomerByCodes(List<String> codeList) {

        if (CollectionUtils.isEmpty(codeList)) return Collections.emptyList();

        Example example = new Example(Customer.class);
		example.createCriteria().andIn(Customer.C_CUSTOMER_CODE, codeList);

        List<Customer> customers = customerMapper.selectByCondition(example);
        if (CollectionUtils.isEmpty(customers)) return Collections.emptyList();

        return customers;
	}

    @Override
    public Result<List<CustomerResponse>> queryCustomerByEmail(GetEmailValidateCodeRequest request) {
        Example example = new Example(Customer.class);
        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo(Customer.C_CONTACT_EMAIL, request.getEmail());

        /* MER-1884
        if (StringUtil.isNotEmpty(request.getIssuerCode())){
            criteria.andEqualTo(Customer.C_ISSUER_CODE,request.getIssuerCode());
        }*/


        List<Customer> customers = customerMapper.selectByCondition(example);
        List<CustomerResponse> customerResponses = BeanCopyUtils.jsonCopyList(customers, CustomerResponse.class);
        return Result.ok(customerResponses);
    }

    @Override
    public Result<List<CustomerResponse>> queryCustomerListByUserCode(QueryCustomerByUserCodeRequest param) {

        Example opUserExample = new Example(OpUserTenantMappingEntity.class);
        opUserExample.createCriteria().andEqualTo("userCode",param.getUserCode());
        List<OpUserTenantMappingEntity> opUsers = iOpUserTenantMappingMapper.selectByCondition(opUserExample);

        if (CollectionUtils.isEmpty(opUsers)){
            return new Result<>();
        }


        List<String> customerCodes = opUsers.stream().map(OpUserTenantMappingEntity::getTenantCode).collect(Collectors.toList());


        Example customerExample = new Example(Customer.class);
        customerExample.createCriteria().andIn(Customer.C_CUSTOMER_CODE,customerCodes);
        List<Customer> customers = customerMapper.selectByCondition(customerExample);
        if (CollectionUtils.isEmpty(customers)){
            return new Result<>();
        }

        return Result.ok(BeanCopyUtils.jsonCopyList(customers,CustomerResponse.class));
    }



    // 加密用户信息
    private void encrypt(OpUserAccountEntity opUserAccountEntity) {

        opUserAccountEntity.setPassword(ConvertUtils.toString(CryptoUtils.sha512Encrypt(opUserAccountEntity.getPassword(), MIX_SALT), null));

        opUserAccountEntity.setLastName(CryptoUtils.aesEncrypt(opUserAccountEntity.getLastName(), MIX_SALT));
        opUserAccountEntity.setFirstName(CryptoUtils.aesEncrypt(opUserAccountEntity.getFirstName(), MIX_SALT));
        opUserAccountEntity.setMobile(ConvertUtils.toString(CryptoUtils.aesEncrypt(opUserAccountEntity.getMobile(), MIX_SALT), null));

        opUserAccountEntity.setAccount(ConvertUtils.toString(CryptoUtils.aesEncrypt(ConvertUtils.toString(opUserAccountEntity.getAccount(), "").toLowerCase(), MIX_SALT), null));
        opUserAccountEntity.setEmail(ConvertUtils.toString(CryptoUtils.aesEncrypt(ConvertUtils.toString(opUserAccountEntity.getEmail(), "").toLowerCase(), MIX_SALT), null));
    }


    // 解密用户信息
    private void decrypt(OpUserAccountEntity opUserAccountEntity) {

        opUserAccountEntity.setAccount(CryptoUtils.aesDecrypt(opUserAccountEntity.getAccount(), MIX_SALT));
        opUserAccountEntity.setLastName(CryptoUtils.aesDecrypt(opUserAccountEntity.getLastName(), MIX_SALT));
        opUserAccountEntity.setFirstName(CryptoUtils.aesDecrypt(opUserAccountEntity.getFirstName(), MIX_SALT));
        opUserAccountEntity.setMobile(CryptoUtils.aesDecrypt(opUserAccountEntity.getMobile(), MIX_SALT));
        opUserAccountEntity.setEmail(CryptoUtils.aesDecrypt(opUserAccountEntity.getEmail(), MIX_SALT));
    }


}
