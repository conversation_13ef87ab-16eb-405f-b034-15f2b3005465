package com.gtech.gvcore.service.report.base;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.IntFunction;
import java.util.stream.Collectors;

import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.page.PageData;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.enums.BookletStatusEnum;
import com.gtech.gvcore.common.enums.ReportStatusEnum;
import com.gtech.gvcore.common.enums.ReportVoucherStatusEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.common.request.orderreport.QueryOrderReportRequest;
import com.gtech.gvcore.common.response.orderreport.QueryReportFileUrlResponse;
import com.gtech.gvcore.common.response.orderreport.QueryReportReqResponse;
import com.gtech.gvcore.common.response.orderreport.ReportQueryConditions;
import com.gtech.gvcore.common.utils.ExceptionBuilder;
import com.gtech.gvcore.dao.mapper.OrderReportFileMapper;
import com.gtech.gvcore.dao.mapper.ReportRequestMapper;
import com.gtech.gvcore.dao.model.Company;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Issuer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.OrderReportFile;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.Printer;
import com.gtech.gvcore.dao.model.ReportRequest;
import com.gtech.gvcore.dao.model.SchedulerReport;
import com.gtech.gvcore.dao.model.UserAccount;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.helper.GvPageHelper;
import com.gtech.gvcore.service.GvUserAccountService;
import com.gtech.gvcore.service.impl.MessageComponent;
import com.gtech.gvcore.service.report.ReportRequestService;
import com.gtech.gvcore.service.report.ReportTaskRegisterService;
import com.gtech.gvcore.service.report.extend.ReportFactory;
import com.gtech.gvcore.service.report.extend.context.ReportContextHelper;
import com.gtech.gvcore.service.report.extend.joindate.GetJoinDateMapSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName ReportServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/1/20 15:56
 * @Version V1.0
 **/
@Service
@Slf4j
public class ReportRequestServiceImpl implements ReportRequestService, GetJoinDateMapSupport {

    @Autowired
    private GvCodeHelper gvCodeHelper;
    @Autowired
    private ReportRequestMapper reportRequestMapper;
    @Autowired
    private OrderReportFileMapper orderReportFileMapper;
    @Autowired
    private ReportTaskRegisterService registerService;
    @Autowired
    private GvUserAccountService userAccountService;

    @Override
    public String createReport(CreateReportRequest request, boolean local) {

        // 校验，报表是否支持
        this.valid(request);

        // 构建报表实体
        final ReportRequest report = builderReportEntity(request).setReceiveEmail(userAccountService.getUserEmail(request.getCreateUser()));

        // 保存并且执行回调
        return this.saveReportAndCallBack(report, local);
    }

    @Override
    public String createReport(SchedulerReport schedulerReport) {

        final CreateReportRequest requestParam = new CreateReportRequest()
                .setCreateUser(schedulerReport.getCreateUser())
                .setReportType(schedulerReport.getReportType());

        if (StringUtils.isNotBlank(schedulerReport.getIssuerCode()))
            requestParam.setIssuerCode(schedulerReport.getIssuerCode());
        if (StringUtils.isNotBlank(schedulerReport.getTransactionStatus()))
            requestParam.setTransactionStatus(schedulerReport.getTransactionStatus());
        if (StringUtils.isNotBlank(schedulerReport.getMerchantCode()))
            requestParam.setMerchantCodes(Collections.singletonList(schedulerReport.getMerchantCode()));
        if (StringUtils.isNotBlank(schedulerReport.getTransactionType()))
            requestParam.setTransactionTypes(Collections.singletonList(schedulerReport.getTransactionType()));
        if (null != schedulerReport.getVoucherStatus())
            requestParam.setVoucherStatus(Collections.singletonList(String.valueOf(schedulerReport.getVoucherStatus())));
        if (StringUtils.isNotBlank(schedulerReport.getVpgCode()))
            requestParam.setCpgCodes(Collections.singletonList(schedulerReport.getVpgCode()));
        if (StringUtils.isNotBlank(schedulerReport.getMerchantOutletCode()))
            requestParam.setOutletCodes(Collections.singletonList(schedulerReport.getMerchantOutletCode()));

        requestParam.setTransactionDateEnd(new Date());
        requestParam.setTransactionDateStart(DateUtil.getStartOfDay(DateUtil.addDay(-schedulerReport.getDataRange())));

        this.valid(requestParam);

        final String email = StringUtils.isBlank(schedulerReport.getEmails()) ? userAccountService.getUserEmail(schedulerReport.getCreateUser()) : schedulerReport.getEmails();

        final ReportRequest report = builderReportEntity(requestParam).setSchedulerReportCode(schedulerReport.getSchedulerReportCode()).setReceiveEmail(email);

        return this.saveReportAndCallBack(report, false);
    }

    private void valid(CreateReportRequest createReportRequest) {

        if (!ReportFactory.exist(createReportRequest.getReportType()))
            throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.CANNOT_PROCESS_REPORT);
    }


    public String saveReportAndCallBack(final ReportRequest report, final boolean local) {

        final String code = gvCodeHelper.generateOrderReport();
        report.setReportCode(code);

        reportRequestMapper.insertSelective(report);

        registerService.register(code, local);

        return code;
    }

    private static ReportRequest builderReportEntity(CreateReportRequest request) {

        List<String> voucherCodeList = getUploadVoucherCode(request);
        request.setVoucherCodeList(voucherCodeList);
        request.setUploadedFileUrl(null);
        return new ReportRequest()
                .setReportType(request.getReportType())
                .setReportStatus(ReportStatusEnum.PROCESSING.getCode())
                .setRequestUser(request.getCreateUser())
                .setRequestTime(new Date())
                .setRequestJson(JSON.toJSONString(request))
                .setCreateUser(request.getCreateUser());
    }

    private static List<String> getUploadVoucherCode(CreateReportRequest reportParam) {

        final List<String> voucherCodeList = new ArrayList<>();
        final String uploadedFileUrl = reportParam.getUploadedFileUrl();

        if (StringUtil.isBlank(uploadedFileUrl))
            return voucherCodeList;

        if (uploadedFileUrl.endsWith(".csv"))
            getUploadVoucherCodeByCSV(voucherCodeList, uploadedFileUrl);
        else
            getUploadVoucherCodeByEXCEL(voucherCodeList, uploadedFileUrl);

        return voucherCodeList;
    }

    private static void getUploadVoucherCodeByEXCEL(List<String> voucherCodeList, String uploadedFileUrl) {

        try (InputStream is = new URL(uploadedFileUrl).openStream(); XSSFWorkbook workbook = new XSSFWorkbook(is)) {
            XSSFSheet sheet = workbook.getSheetAt(0);
            int rows = sheet.getPhysicalNumberOfRows();
            XSSFRow row = sheet.getRow(0);
            int cells = row.getPhysicalNumberOfCells();

            if (cells > 1)
                throw new GTechBaseException(ResultErrorCodeEnum.UPLOADED_FILE_MORE_COLUMNS.code(), ResultErrorCodeEnum.UPLOADED_FILE_MORE_COLUMNS.desc());

            for (int i = 1; i < rows; i++) {
                row = sheet.getRow(i);
                voucherCodeList.add(row.getCell(0).getStringCellValue());
            }

        } catch (IOException e) {
            log.warn(ResultErrorCodeEnum.UPLOADED_FILE_NOT_MEET_STANDARDS.desc() + e.getMessage(), e);
            ReportContextHelper.findContext().error("解析文件失败");
        }
    }

    private static void getUploadVoucherCodeByCSV(List<String> voucherCodeList, String uploadedFileUrl) {

        String[] header = new String[0];

        try (InputStream is = new URL(uploadedFileUrl).openStream();
             InputStreamReader isr = new InputStreamReader(is, StandardCharsets.UTF_8);
             BufferedReader br = new BufferedReader(isr);
             CSVParser csvParser = CSVFormat.DEFAULT.builder().setAllowDuplicateHeaderNames(false).setHeader(header).build().parse(br)) {

            for (CSVRecord csvRecord : csvParser) {

                int recordSize = csvRecord.size();

                if (recordSize > 1)
                    throw new GTechBaseException(ResultErrorCodeEnum.UPLOADED_FILE_MORE_COLUMNS.code(), ResultErrorCodeEnum.UPLOADED_FILE_MORE_COLUMNS.desc());

                voucherCodeList.add(csvRecord.get(0));
            }

        } catch (IOException e) {
            log.warn(ResultErrorCodeEnum.UPLOADED_FILE_NOT_MEET_STANDARDS.desc() + e.getMessage(), e);
        }
    }

    @Override
    public PageData<QueryReportReqResponse> queryReport(QueryOrderReportRequest queryOrderReportRequest) {

        Example example = new Example(ReportRequest.class);
        example.createCriteria()
                .andEqualTo(ReportRequest.C_REPORT_TYPE, queryOrderReportRequest.getReportType())
                .andEqualTo(ReportRequest.C_CREATE_USER, queryOrderReportRequest.getUserCode());
        example.orderBy(ReportRequest.C_ID).desc();

        long count = this.reportRequestMapper.selectCountByCondition(example);
        List<ReportRequest> select = this.reportRequestMapper.selectByExampleAndRowBounds(example, GvPageHelper.getRowBounds(queryOrderReportRequest));

        if (CollectionUtils.isEmpty(select)) return new PageData<>(new ArrayList<>(), count);

        return new PageData<>(this.getQueryResult(select), count);
    }

    @Override
    public ReportRequest getReport(String code) {
        ReportRequest req = new ReportRequest();
        req.setReportCode(code);
        return reportRequestMapper.selectOne(req);
    }

    private List<QueryReportReqResponse> getQueryResult(List<ReportRequest> select) {

        //file detail
        Map<String, List<OrderReportFile>> fileMap = getReportFileDetail(select);

        Map<String, CreateReportRequest> requestMap = select.stream().collect(Collectors.toMap(ReportRequest::getReportCode
                , e -> Optional.of(e.getRequestJson()).map(w -> JSON.parseObject(w, CreateReportRequest.class)).orElseGet(CreateReportRequest::new)));

        //map
        List<CreateReportRequest> findJoinList = new ArrayList<>(requestMap.values());
        Map<String, String> issuerMap = this.getNameMapByString(findJoinList, Issuer.class, CreateReportRequest::getIssuerCode, Issuer::getIssuerName);
        Map<String, String> companyMap = this.getNameMapByList(findJoinList, Company.class, CreateReportRequest::getCompanyCodes, Company::getCompanyName);
        Map<String, String> merchantMap = this.getNameMapByList(findJoinList, Merchant.class, CreateReportRequest::getMerchantCodes, Merchant::getMerchantName);
        Map<String, String> customerMap = this.getNameMapByList(findJoinList, Customer.class, CreateReportRequest::getCustomerCodes, Customer::getCustomerName);
        Map<String, String> cpgMap = this.getNameMapByList(findJoinList, Cpg.class, CreateReportRequest::getCpgCodes, Cpg::getCpgName);
        Map<String, String> gcCpgMap = this.getNameMapByList(findJoinList, GcCpg.class, CreateReportRequest::getCpgCodes, GcCpg::getCpgName);
        Map<String, String> printerMap = this.getNameMapByList(findJoinList, Printer.class, CreateReportRequest::getPrintingVendorList, Printer::getPrinterName);
        Map<String, String> outletMap = this.getNameMapByList(findJoinList, Outlet.class,
                Arrays.asList(CreateReportRequest::getOutletCodes
                        , CreateReportRequest::getOutboundCodeList
                        , CreateReportRequest::getInboundCodeList
                        , CreateReportRequest::getVoucherRequestSourceList
                        , CreateReportRequest::getVoucherReturnTransferFromStoreList
                        , CreateReportRequest::getVoucherReturnTransferToStoreList
                ), Outlet::getOutletName);


        return select.stream()
                .map(e -> {
                    CreateReportRequest createReportRequest = requestMap.get(e.getReportCode());

                    QueryReportReqResponse queryReportReqResponse = new QueryReportReqResponse()
                            .setReportStatus(e.getReportStatus())
                            .setReportFileUrl(BeanCopyUtils.jsonCopyList(fileMap.get(e.getReportCode()), QueryReportFileUrlResponse.class))
                            .setOrderReportCode(e.getReportCode())
                            .setCreateUser(e.getCreateUser())
                            .setCreateTime(e.getCreateTime())
                            .setQueryConditions(BeanCopyUtils.jsonCopyBean(createReportRequest, ReportQueryConditions.class)
                                    .setCpgCodes(getNameByList(cpgMap, gcCpgMap, createReportRequest.getCpgCodes()))
                                    .setCompanyCodes(getNameByList(companyMap, createReportRequest.getCompanyCodes()))
                                    .setCustomerCodes(getNameByList(customerMap, createReportRequest.getCustomerCodes()))
                                    .setOutletCodes(getNameByList(outletMap, createReportRequest.getOutletCodes()))
                                    .setMerchantCodes(getNameByList(merchantMap, createReportRequest.getMerchantCodes()))
                                    .setIssuerCode(issuerMap.get(createReportRequest.getIssuerCode()))
                                    .setBookletStatus(getEnumStatusDesc(createReportRequest.getBookletStatus(), BookletStatusEnum::getDescByCode))
                                    .setVoucherStatus(getEnumStatusDesc(createReportRequest.getVoucherStatus(), ReportVoucherStatusEnum::getDescByCode))
                                    .setOutboundCodeList(getNameByList(outletMap, createReportRequest.getOutboundCodeList()))
                                    .setInboundCodeList(getNameByList(outletMap, createReportRequest.getInboundCodeList()))
                                    .setVoucherRequestSourceList(getNameByList(outletMap, createReportRequest.getVoucherRequestSourceList()))
                                    .setVoucherReturnTransferFromStoreList(getNameByList(outletMap, createReportRequest.getVoucherReturnTransferFromStoreList()))
                                    .setVoucherReturnTransferToStoreList(getNameByList(outletMap, createReportRequest.getVoucherReturnTransferToStoreList()))
                                    .setPrintingVendorList(getNameByList(printerMap, createReportRequest.getPrintingVendorList()))
                                    .setOperateUserName(this.getQueryConditionsOperateUser(createReportRequest.getOperateUserCode())));
                    return queryReportReqResponse;
                }).

                collect(Collectors.toList());

    }


    private String getQueryConditionsOperateUser(String userCode) {

        if (StringUtils.isBlank(userCode)) return StringUtils.EMPTY;

        UserAccount user = userAccountService.getUserNameInfo(userCode);
        if (null == user) return StringUtils.EMPTY;

        return user.getFullName();

    }

    private Map<String, List<OrderReportFile>> getReportFileDetail(List<ReportRequest> select) {

        List<String> reportCodeList = select.stream().map(ReportRequest::getReportCode).distinct().collect(Collectors.toList());

        Example fileExample = new Example(OrderReportFile.class);
        fileExample.createCriteria().andIn("orderReportCode", reportCodeList);
        List<OrderReportFile> reportFileList = orderReportFileMapper.selectByCondition(fileExample);

        return reportFileList.stream().collect(Collectors.groupingBy(OrderReportFile::getOrderReportCode));
    }

    private List<String> getEnumStatusDesc(List<String> statusCode, IntFunction<String> convertFunction) {

        if (CollectionUtils.isEmpty(statusCode)) return Collections.emptyList();

        return statusCode.stream()
                .filter(NumberUtils::isCreatable)
                .map(Integer::parseInt)
                .map(convertFunction::apply)
                .collect(Collectors.toList());
    }


    private List<String> getNameByList(Map<String, String> map, List<String> codes) {

        if (CollectionUtils.isEmpty(codes)) return Collections.emptyList();

        return codes.stream()
                .map(e -> map.getOrDefault(e, e))
                .collect(Collectors.toList());
    }

    private List<String> getNameByList(Map<String, String> map, Map<String, String> gcMap, List<String> codes) {

        if (CollectionUtils.isEmpty(codes)) return Collections.emptyList();

        return codes.stream()
                .map(e -> {
                    String s = map.get(e);
                    if (StringUtils.isBlank(s)) {
                        s = gcMap.get(e);
                    }
                    if (StringUtils.isBlank(s)) {
                        s = e;
                    }
                    return s;
                }).collect(Collectors.toList());
    }

    protected <T> Map<String, String> getNameMapByList(List<CreateReportRequest> reportList, Class<T> type, Function<CreateReportRequest, List<String>> getCodeFunction, Function<T, String> getNameFunction) {

        List<String> codeArray = new ArrayList<>();
        reportList.stream()
                .map(getCodeFunction)
                .filter(Objects::nonNull)
                .map(e -> e.stream().filter(StringUtils::isNoneBlank).collect(Collectors.toList()))
                .filter(CollectionUtils::isNotEmpty)
                .forEach(codeArray::addAll);

        return getMapByCode(codeArray, type, getNameFunction);
    }

    protected <T> Map<String, String> getNameMapByList(List<CreateReportRequest> reportList, Class<T> type, List<Function<CreateReportRequest, List<String>>> getCodeFunctionList, Function<T, String> getNameFunction) {

        List<String> codeArray = new ArrayList<>();

        getCodeFunctionList
                .forEach(getCodeFunction ->
                        reportList.stream()
                                .map(getCodeFunction)
                                .filter(Objects::nonNull)
                                .map(e -> e.stream().filter(StringUtils::isNoneBlank).collect(Collectors.toList()))
                                .filter(CollectionUtils::isNotEmpty)
                                .forEach(codeArray::addAll)
                );


        return getMapByCode(codeArray, type, getNameFunction);
    }

    protected <T> Map<String, String> getNameMapByString(Collection<CreateReportRequest> reportList, Class<T> type, Function<CreateReportRequest, String> getCodeFunction, Function<T, String> getNameFunction) {

        List<String> codeArray = new ArrayList<>();
        reportList.stream()
                .map(getCodeFunction)
                .filter(StringUtils::isNoneBlank)
                .forEach(codeArray::add);

        return getMapByCode(codeArray, type, getNameFunction);
    }

    protected <T> Map<String, String> getMapByCode(Collection<String> codes, Class<T> type, Function<T, String> getNameFunction) {

        JoinDataMap<T> map = this.getMapByCode(codes, type);

        Map<String, String> result = new HashMap<>();
        map.forEach((k, v) -> result.put(k, getNameFunction.apply(v)));

        return result;
    }

}
