package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/20 16:52
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class CancelSalesDetailBean {

    @ExcelProperty(value = "Voucher Number")
    private String voucherNumber;

    @ExcelProperty(value = "Merchant")
    private String merchant;

    @ExcelProperty(value = "Outlet")
    private String outlet;

    @ExcelProperty(value = "Voucher Program Group")
    private String voucherProgramGroup;

    @ExcelProperty(value = "Expiry Date")
    private String expiryDate;

    @ReportAmountValue
    @ExcelProperty(value = "Transaction Amount", converter = ExportExcelNumberConverter.class)
    private String transactionAmount;

    @ExcelProperty(value = "Transaction Date")
    private String transactionDate;

    @ExcelProperty(value = "Invoice Number")
    private String invoiceNumber;

    @ExcelProperty(value = "Client Name")
    private String corporateName;

}
