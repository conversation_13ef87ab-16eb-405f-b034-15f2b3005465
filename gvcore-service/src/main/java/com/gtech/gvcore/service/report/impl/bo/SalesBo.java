package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.service.report.extend.poll.GroupNewTransactionByVoucherCodeSupport;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName SalesBo
 * @Description sales report bo
 * <AUTHOR>
 * @Date 2023/3/22 14:52
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class SalesBo implements GroupNewTransactionByVoucherCodeSupport {

    private String transactionCode;

    private String transactionType;

    private String transactionId;

    private String merchantCode;

    private String billNumber;

    private String outletCode;

    private String cpgCode;

    private String transactionDate;

    private String voucherCode;

    private String posCode;

    private BigDecimal denomination;

    private String responseMessage;

    private String invoiceNumber;

    private String referenceNumber;

    private String approveCode;

    private BigDecimal transactionNumber;

    public void setTransactionCode(String transactionCode) {
        this.transactionNumber = new BigDecimal(transactionCode.substring(3));
        this.transactionCode = transactionCode;
    }

    public Date getTransactionDate() {

        return DateUtil.parseDate(transactionDate, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }


}
