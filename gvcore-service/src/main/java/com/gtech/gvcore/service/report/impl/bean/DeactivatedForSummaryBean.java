package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/20 13:43
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class DeactivatedForSummaryBean {

    /**
     * Merchant
     */
    @ExcelProperty(value = "Merchant")
    private String merchant;

    /**
     * Merchant Out
     */
    @ExcelProperty(value = "Outlet")
    private String merchantOut;

    /**
     * VPG
     */
    @ExcelProperty(value = "Voucher Program Group")
    private String vpg;


    /**
     * Number of Vouchers
     */
    @ExcelProperty(value = "Number of Vouchers", converter = ExportExcelNumberConverter.class)
    private String numberOfVouchers;

    /**
     * Voucher Amount
     */
    @ReportAmountValue
    @ExcelProperty(value = "Total Amount", converter = ExportExcelNumberConverter.class)
    private String voucherAmount;

    /**
     * Customer Name
     */
    @ExcelProperty(value = "Customer Name")
    private String customerName;

    /**
     * Deactivated Reason
     */
    @ExcelProperty(value = "Deactivated Reason")
    private String deactivatedReason;
}
