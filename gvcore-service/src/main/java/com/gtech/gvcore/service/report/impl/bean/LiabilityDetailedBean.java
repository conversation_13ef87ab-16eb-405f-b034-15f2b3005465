package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/21 11:36
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class LiabilityDetailedBean {

    @ExcelProperty(value = "Voucher Number")
    private String voucherNumber;

    @ExcelProperty(value = "Voucher Program Group (VPG)")
    private String voucherProgramGroup;

    @ExcelProperty(value = "Voucher Status")
    private String voucherStatus;

    @ReportAmountValue
    @ExcelProperty(value = "Denomination", converter = ExportExcelNumberConverter.class)
    private String denomination;

    @ExcelProperty(value = "Expiry Date")
    private String expiryDate;
}
