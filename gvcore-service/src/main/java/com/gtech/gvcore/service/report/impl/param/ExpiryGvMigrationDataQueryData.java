 package com.gtech.gvcore.service.report.impl.param;

 import com.gtech.gvcore.service.report.ReportQueryParam;
 import com.gtech.gvcore.service.report.extend.row.VoucherPageParam;
 import lombok.Getter;
 import lombok.Setter;
 import lombok.experimental.Accessors;

 import java.util.Date;
 import java.util.List;

/**
 * @ClassName ExpiryGvMigrationDataQueryData
 * @Description 超市时间明细报表
 * <AUTHOR>
 * @Date 2022/9/26 11:17
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class ExpiryGvMigrationDataQueryData extends VoucherPageParam implements ReportQueryParam {

    private List<String> cpgCodeList;
    private List<String> issuerCodeList;


    private Date voucherEffectiveDateStart;

    private Date voucherEffectiveDateEnd;

    private String customerCode;

}
