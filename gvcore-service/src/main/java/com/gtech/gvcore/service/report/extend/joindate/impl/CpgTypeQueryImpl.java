package com.gtech.gvcore.service.report.extend.joindate.impl;

import com.gtech.gvcore.dao.mapper.CpgTypeMapper;
import com.gtech.gvcore.dao.model.CpgType;
import com.gtech.gvcore.service.report.extend.joindate.QuerySupport;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

/**
 * @ClassName CpgTypeQueryImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/1/16 15:47
 * @Version V1.0
 **/
@Component
public class CpgTypeQueryImpl implements QuerySupport<CpgType> {

    public static final CpgType EMPTY = new CpgType();

    @Autowired
    private CpgTypeMapper cpgTypeMapper;


    @Override
    public List<CpgType> queryByCode(List<String> codes, String... selectFields) {

        if (CollectionUtils.isEmpty(codes)) return Collections.emptyList();

        Example example = new Example(CpgType.class);
        example.createCriteria().andIn(CpgType.CPG_TYPE_CODE, codes);

        if (ArrayUtils.isNotEmpty(selectFields)) example.selectProperties(selectFields);

        List<CpgType> list = cpgTypeMapper.selectByCondition(example);
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        return list;
    }

    @Override
    public Function<CpgType, String> codeMapper() {
        return CpgType::getCpgTypeCode;
    }

    @Override
    public CpgType emptyObject() {
        return EMPTY;
    }

    @Override
    public Class<CpgType> supportType() {
        return CpgType.class;
    }
}
