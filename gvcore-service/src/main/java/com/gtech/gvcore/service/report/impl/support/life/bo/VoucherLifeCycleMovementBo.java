package com.gtech.gvcore.service.report.impl.support.life.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @ClassName VoucherLifeCycleMovementBo
 * @Description
 * <AUTHOR>
 * @Date 2023/1/5 17:55
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class VoucherLifeCycleMovementBo {

    private String requestId;

    private String inbound;

    private String outbound;

    private Date requestTime;

    private Date approvedTime;

    private Date allLocationTime;

    private String receiverCode;


}
