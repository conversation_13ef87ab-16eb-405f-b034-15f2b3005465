package com.gtech.gvcore.service.report.extend.joindate.impl;

import com.gtech.gvcore.dao.mapper.PosMapper;
import com.gtech.gvcore.dao.model.Pos;
import com.gtech.gvcore.service.report.extend.joindate.QuerySupport;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

/**
 * @ClassName PosQueryImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/1/16 15:48
 * @Version V1.0
 **/
@Component
public class PosQueryImpl implements QuerySupport<Pos> {

    public static final Pos EMPTY = new Pos();

    @Autowired
    private PosMapper posMapper;

    @Override
    public List<Pos> queryByCode(List<String> codes, String... selectFields) {

        if (CollectionUtils.isEmpty(codes)) return Collections.emptyList();

        Example example = new Example(Pos.class);
        example.createCriteria().andIn(Pos.CONST_POS_CODE, codes);

        if (ArrayUtils.isNotEmpty(selectFields)) example.selectProperties(selectFields);

        List<Pos> posList = posMapper.selectByCondition(example);
        if (CollectionUtils.isEmpty(posList)) return Collections.emptyList();

        return posList;
    }

    @Override
    public Function<Pos, String> codeMapper() {
        return Pos::getPosCode;
    }

    @Override
    public Pos emptyObject() {
        return EMPTY;
    }

    @Override
    public Class<Pos> supportType() {
        return Pos.class;
    }
}
