package com.gtech.gvcore.service.impl;

import com.gtech.basic.idm.service.dto.OpUserAccountDto;
import com.gtech.basic.idm.web.vo.param.GetOpUserAccountParam;
import com.gtech.basic.idm.web.vo.param.UpdateOpUserAccountParam;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.CryptoUtils;
import com.gtech.gvcore.common.request.opuseraccount.CreateOpUserAccountRequest;
import com.gtech.gvcore.dao.mapper.GvOpUserAccountMapper;
import com.gtech.gvcore.dao.model.GvOpUserAccount;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.GvOpUserAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @Date 2022/6/13 13:51
 */
@Service
public class GvOpUserAccountImpl implements GvOpUserAccountService {


    @Autowired
    private GvOpUserAccountMapper userAccountMapper;

    @Autowired
    private GvCodeHelper gvCodeHelper;
    private static final String MIX_SALT = "26a711f75b90055fe169fca73a0e0f03";

    @Override
    public void   createOpUserAccount(CreateOpUserAccountRequest param) {


        GvOpUserAccount gvOpUserAccount = BeanCopyUtils.jsonCopyBean(param, GvOpUserAccount.class);
        gvOpUserAccount.setUserCode(gvCodeHelper.generateOpUserCode());

        this.encrypt(gvOpUserAccount);
        gvOpUserAccount.setUserType(1);

        try {
            int i = userAccountMapper.insertSelective(gvOpUserAccount);
        } catch (DuplicateKeyException e) {
            throw e;
        }

        OpUserAccountDto opUserAccountDto = BeanCopyUtils.jsonCopyBean(param, OpUserAccountDto.class);
        opUserAccountDto.setUserCode(gvOpUserAccount.getUserCode());

    }





    @Override
    public int updateOpUserAccount(UpdateOpUserAccountParam param) {
        return 0;
    }

    @Override
    public OpUserAccountDto getOpUserAccount(GetOpUserAccountParam paramDto) {
        return null;
    }

    @Override
    public void deleteByCustomer(String customerCode) {
        Example example = new Example(GvOpUserAccount.class);
        example.createCriteria().andEqualTo("customerCode",customerCode);
        userAccountMapper.deleteByCondition(example);
    }


    // 加密用户信息
    private void encrypt(GvOpUserAccount entity) {

        entity.setPassword(ConvertUtils.toString(CryptoUtils.sha512Encrypt(entity.getPassword(), MIX_SALT), null));

        entity.setLastName(CryptoUtils.aesEncrypt(entity.getLastName(), MIX_SALT));
        entity.setFirstName(CryptoUtils.aesEncrypt(entity.getFirstName(), MIX_SALT));

        entity.setMobile(ConvertUtils.toString(CryptoUtils.aesEncrypt(entity.getMobile(), MIX_SALT), null));
        entity.setAccount(ConvertUtils.toString(CryptoUtils.aesEncrypt(ConvertUtils.toString(entity.getAccount(), "").toLowerCase(), MIX_SALT), null));
        entity.setEmail(ConvertUtils.toString(CryptoUtils.aesEncrypt(ConvertUtils.toString(entity.getEmail(), "").toLowerCase(), MIX_SALT), null));
    }

    // 解密用户信息
    private void decrypt(GvOpUserAccount entity) {

        entity.setAccount(CryptoUtils.aesDecrypt(entity.getAccount(), MIX_SALT));
        entity.setLastName(CryptoUtils.aesDecrypt(entity.getLastName(), MIX_SALT));
        entity.setFirstName(CryptoUtils.aesDecrypt(entity.getFirstName(), MIX_SALT));
        entity.setMobile(CryptoUtils.aesDecrypt(entity.getMobile(), MIX_SALT));
        entity.setEmail(CryptoUtils.aesDecrypt(entity.getEmail(), MIX_SALT));
    }



}
