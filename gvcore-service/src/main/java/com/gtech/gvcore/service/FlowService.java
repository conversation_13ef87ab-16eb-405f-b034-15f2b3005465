package com.gtech.gvcore.service;


import com.gtech.commons.result.PageResult;
import com.gtech.gvcore.common.request.flow.CreateFlowRequest;
import com.gtech.gvcore.common.request.flow.FlowNodeRequest;
import com.gtech.gvcore.common.request.flow.QueryFlowRequest;
import com.gtech.gvcore.common.request.flow.UpdateFlowRequest;
import com.gtech.gvcore.common.response.flow.FlowResponse;

public interface FlowService {

	String createFlow(CreateFlowRequest request);
	
	String updateFlow(UpdateFlowRequest request);
	
	String saveFlowNode(FlowNodeRequest request);
	
	PageResult<FlowResponse> queryFlowList(QueryFlowRequest request);
	
}
