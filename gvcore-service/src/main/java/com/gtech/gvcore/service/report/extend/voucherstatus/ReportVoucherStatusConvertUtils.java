package com.gtech.gvcore.service.report.extend.voucherstatus;

import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ReportVoucherStatusEnum;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.report.extend.context.ReportContextHelper;

import java.util.Date;
import java.util.Objects;

/**
 * @ClassName ReportVoucherStatusConvertUtils
 * @Description
 * <AUTHOR>
 * @Date 2023/4/18 19:21
 * @Version V1.0
 **/
public class ReportVoucherStatusConvertUtils {

    private ReportVoucherStatusConvertUtils() {
    }

    public static final String BEGIN = "AND ( 1 != 1 ";
    public static final String SELECT_NEWLY_GENERATED = " OR (v.status = 0 AND v.voucher_status = 1 AND v.mop_code <> 'VCE' AND v.voucher_effective_date > NOW() )  ";
    public static final String SELECT_ACTIVATED = " OR (v.status = 1 AND v.voucher_status = 1 AND v.voucher_effective_date > NOW()) ";
    public static final String SELECT_REDEEMED = " OR (v.status = 2) ";
    public static final String SELECT_CANCELLED = " OR (v.status = 3 AND v.voucher_status = 1 AND v.voucher_effective_date > NOW()) ";
    public static final String SELECT_EXPIRED = " OR ( 1 = 1 " +
            "AND v.voucher_effective_date < NOW() " +
            "AND v.status <> 2  ) ";
    public static final String SELECT_PURCHASED = " OR (v.status = 0 AND v.voucher_status = 1 AND v.mop_code = 'VCE' AND v.voucher_effective_date > NOW()) ";
    public static final String SELECT_DEACTIVATED = " OR (v.voucher_status = 0 AND v.status <> 2 AND v.voucher_effective_date > NOW() ) ";
    public static final String END = ")";

    public static final String[] SELECT_VOUCHER_STATUS_FIELDS = new String[]{//NOSONAR
            Voucher.C_VOUCHER_CODE,
            Voucher.C_VOUCHER_EFFECTIVE_DATE,
            Voucher.C_STATUS,
            Voucher.C_VOUCHER_STATUS,
            Voucher.C_MOP_CODE,
    };

    public static ReportVoucherStatusEnum getVoucherStatus(Voucher voucher, Date now) {

        // 已使用
        if (ReportVoucherStatusEnum.VOUCHER_REDEEMED.getCode().equals(voucher.getStatus())) return ReportVoucherStatusEnum.VOUCHER_REDEEMED;

        // 已销毁
        if (Objects.equals(voucher.getVoucherStatus(), 2)) return ReportVoucherStatusEnum.VOUCHER_DESTROY;

        // 过期
        if (voucher.getVoucherEffectiveDate() != null // 过期时间为空则不过期
                && now.compareTo(voucher.getVoucherEffectiveDate()) > 0) // 过期时间小于当前时间则过期
            return ReportVoucherStatusEnum.VOUCHER_EXPIRED;

        // 已禁用
        if (Objects.equals(voucher.getVoucherStatus(), 0)) return ReportVoucherStatusEnum.VOUCHER_DEACTIVATED;


        //采购
        if (GvcoreConstants.MOP_CODE_VCE.equals(voucher.getMopCode())
                && ReportVoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode().equals(voucher.getStatus()))
            return ReportVoucherStatusEnum.VOUCHER_PURCHASED;

        //other
        return ReportVoucherStatusEnum.valueOfCode(voucher.getStatus());
    }

    public static String getVoucherStatusDesc(Voucher voucher, Date now) {

        return ReportVoucherStatusConvertUtils.getVoucherStatus(voucher, now).getDesc();
    }

    public static ReportVoucherStatusEnum getVoucherStatus(Integer status, Integer voucherStatus, Date effectiveDate, String mopCode) {

        return ReportVoucherStatusConvertUtils.getVoucherStatus(Voucher.builder().status(status).voucherStatus(voucherStatus).voucherEffectiveDate(effectiveDate).mopCode(mopCode).build(), ReportContextHelper.reportBuilderTime());
    }

    public static ReportVoucherStatusEnum getVoucherStatus(Integer status, Integer voucherStatus, Date effectiveDate, String mopCode, Date now) {

        return ReportVoucherStatusConvertUtils.getVoucherStatus(Voucher.builder().status(status).voucherStatus(voucherStatus).voucherEffectiveDate(effectiveDate).mopCode(mopCode).build(), now);
    }

    public static ReportVoucherStatusEnum getVoucherStatus(Voucher voucher) {

        return ReportVoucherStatusConvertUtils.getVoucherStatus(voucher, ReportContextHelper.reportBuilderTime());
    }

    public static String getVoucherStatusDesc(Voucher voucher) {

        return ReportVoucherStatusConvertUtils.getVoucherStatusDesc(voucher, ReportContextHelper.reportBuilderTime());
    }

}
