package com.gtech.gvcore.service.report.impl.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName LiabilitySummaryHeadBo
 * @Description 责任报表头
 * <AUTHOR>
 * @Date 2022/7/15 13:34
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class LiabilitySummaryHeadBo {

    private String generatedBy;

    private String generatedOn;

    private String issuer;

    private String merchant;

    private String cardProgramGroup;

    private String asOnDate;

    private String expiredIn;

}