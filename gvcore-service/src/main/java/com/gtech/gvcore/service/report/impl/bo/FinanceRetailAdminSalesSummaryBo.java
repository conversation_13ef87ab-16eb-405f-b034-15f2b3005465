package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.ConvertUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * @ClassName FinanceRetailAdminSalesSummaryBo
 * @Description
 * <AUTHOR>
 * @Date 2023/3/23 14:08
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class FinanceRetailAdminSalesSummaryBo  {

    private String merchantCode;

    private String outletCode;

    private String invoiceNumber;

    private String cpgCode;

    private Integer salesCount = 1;

    private BigDecimal salesAmount = BigDecimal.ZERO;

    public static FinanceRetailAdminSalesSummaryBo convert(FinanceRetailAdminSalesBo bo) {

        return new FinanceRetailAdminSalesSummaryBo()
                .setMerchantCode(bo.getMerchantCode())
                .setOutletCode(bo.getOutletCode())
                .setInvoiceNumber(bo.getInvoiceNumber())
                .setCpgCode(bo.getCpgCode())
                .setSalesAmount(bo.getDenomination());
    }

    public static String getGroupKey(FinanceRetailAdminSalesSummaryBo bean) {

        return StringUtils.join("_", bean.getMerchantCode() , bean.getOutletCode() , bean.getCpgCode() , bean.getInvoiceNumber());
    }

    public static FinanceRetailAdminSalesSummaryBo newInstance(FinanceRetailAdminSalesSummaryBo bean) {
        
        return new FinanceRetailAdminSalesSummaryBo()
                .setMerchantCode(bean.getMerchantCode())
                .setOutletCode(bean.getOutletCode())
                .setCpgCode(bean.getCpgCode())
                .setInvoiceNumber(bean.getInvoiceNumber())
                .setSalesAmount(bean.getSalesAmount());
    }
    
    public FinanceRetailAdminSalesSummaryBo merge(FinanceRetailAdminSalesSummaryBo bo) {

        salesCount += ConvertUtils.toInteger(bo.getSalesCount(), 0);
        //数量乘面额
        salesAmount = salesAmount.add(bo.getSalesAmount());

        return this;
    }
}
