package com.gtech.gvcore.service.report.impl.support.gclife;

import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.service.report.export.snapshoot.label.ReportLabelSupport;
import com.gtech.gvcore.service.report.impl.param.GcLifeCycleQueryData;
import com.gtech.gvcore.service.report.impl.support.gclife.excel.GcLifeCycleFileContext;
import com.gtech.gvcore.service.report.impl.support.gclife.excel.GcLifeCycleSheet;

import java.util.List;
import java.util.Map;

/**
 * @ClassName GcLifeCycle
 * @Description Gift Card Life Cycle Interface
 * <AUTHOR> based on VoucherLifeCycle
 * @Date 2025年6月19日
 * @Version V1.0
 **/
public interface GcLifeCycle extends ReportLabelSupport {

    default void builder(GcLifeCycleFileContext excelContext, Map<ReportExportTypeEnum, List<?>> reportData, GcLifeCycleQueryData param) {

        GcLifeCycleSheet sheet = builderSheet(param);

        excelContext.doFill(sheet);
        reportData.put(exportTypeEnum(), sheet.getData());
    }

    GcLifeCycleSheet builderSheet(GcLifeCycleQueryData param);
}
