package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/21 11:28
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class LatestGvStatusBean {

    @ExcelProperty(value = "Voucher Number")
    private String voucherNumber;

    @ExcelProperty(value = "Booklet Number")
    private String bookletNumber;

    @ExcelProperty(value = "Voucher Program Group")
    private String voucherProgramGroup;

    @ExcelProperty(value = "Selling Date")
    private String sellingDate;

    @ExcelProperty(value = "Voucher Status")
    private String voucherStatus;

    @ExcelProperty(value = "Transaction Type")
    private String transactionType;

    @ExcelProperty(value = "Last Action Date")
    private String lastActionDate;

    @ExcelProperty(value = "Merchant")
    private String merchant;

    @ExcelProperty(value = "Outlet Name")
    private String outletName;

    @ExcelProperty(value = "Expiry Date")
    private String expiryDate;

    @ExcelProperty(value = "Client Name")
    private String clientName;

    @ReportAmountValue
    @ExcelProperty(value = "Voucher Amount", converter = ExportExcelNumberConverter.class)
    private String voucherAmount;
}
