package com.gtech.gvcore.service.impl;

import com.alibaba.druid.util.StringUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.page.PageData;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.dao.mapper.CardNumberConfigMapper;
import com.gtech.gvcore.dao.model.CardNumberConfig;
import com.gtech.gvcore.dto.CardNumberConfigDto;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.CardNumberConfigService;
import io.jsonwebtoken.lang.Collections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.ArrayList;
import java.util.List;

@Service
public class CardNumberConfigServiceImpl implements CardNumberConfigService {

    @Autowired
    private CardNumberConfigMapper cardNumberConfigMapper;
    @Autowired
    private GvCodeHelper gvCodeHelper;

    @Override
    public String createCardNumberConfig(CardNumberConfigDto cardNumberConfigDto) {
        checkExist(cardNumberConfigDto);

        CardNumberConfig cardNumberConfig = new CardNumberConfig();
        cardNumberConfig = BeanCopyUtils.jsonCopyBean(cardNumberConfigDto, CardNumberConfig.class);
        cardNumberConfig.setConfigCode(gvCodeHelper.generateConfigCode());
        cardNumberConfigMapper.insertSelective(cardNumberConfig);

        return cardNumberConfig.getConfigCode();
    }

    private void checkExist(CardNumberConfigDto cardNumberConfigDto) {
        // Check description duplication
        Weekend<CardNumberConfig> desc = Weekend.of(CardNumberConfig.class);
        desc.weekendCriteria()
                .andEqualTo(CardNumberConfig::getDescription, cardNumberConfigDto.getDescription())
                .andEqualTo(CardNumberConfig::getType, cardNumberConfigDto.getType())
                .andNotEqualTo(CardNumberConfig::getConfigCode, cardNumberConfigDto.getConfigCode());
        if (1 <= cardNumberConfigMapper.selectCountByCondition(desc)){
            throw new GTechBaseException(
                ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                String.format("Description '%s' already exists", cardNumberConfigDto.getDescription())
            );
        }

        // Check code duplication
        Weekend<CardNumberConfig> code = Weekend.of(CardNumberConfig.class);
        code.weekendCriteria()
                .andEqualTo(CardNumberConfig::getCode, cardNumberConfigDto.getCode())
                .andEqualTo(CardNumberConfig::getType, cardNumberConfigDto.getType())
                .andNotEqualTo(CardNumberConfig::getConfigCode, cardNumberConfigDto.getConfigCode());
        if (1 <= cardNumberConfigMapper.selectCountByCondition(code)){
            throw new GTechBaseException(
                ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                String.format("Code '%s' already exists", cardNumberConfigDto.getCode())
            );
        }

        // Check denomination duplication (only if denomination is provided)
        if (!StringUtils.isEmpty(cardNumberConfigDto.getDenomination())){
            Weekend<CardNumberConfig> denomination = Weekend.of(CardNumberConfig.class);
            denomination.weekendCriteria()
                    .andEqualTo(CardNumberConfig::getDenomination, cardNumberConfigDto.getDenomination())
                    .andEqualTo(CardNumberConfig::getType, cardNumberConfigDto.getType())
                    .andNotEqualTo(CardNumberConfig::getConfigCode, cardNumberConfigDto.getConfigCode());
            if (1 <= cardNumberConfigMapper.selectCountByCondition(denomination)){
                throw new GTechBaseException(
                    ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                    String.format("Denomination '%s' already exists", cardNumberConfigDto.getDenomination())
                );
            }
        }
    }

    @Override
    public String updateCardNumberConfig(CardNumberConfigDto cardNumberConfigDto) {
        if (StringUtils.isEmpty(cardNumberConfigDto.getConfigCode())){
            throw  new GTechBaseException(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }

        checkExist(cardNumberConfigDto);

        CardNumberConfig cardNumberConfig = BeanCopyUtils.jsonCopyBean(cardNumberConfigDto, CardNumberConfig.class);
        Weekend<CardNumberConfig> weekend = Weekend.of(CardNumberConfig.class);
        weekend.weekendCriteria()
                .andEqualTo(CardNumberConfig::getConfigCode, cardNumberConfigDto.getConfigCode());
        cardNumberConfigMapper.updateByConditionSelective(cardNumberConfig,weekend);
        return cardNumberConfigDto.getConfigCode();
    }

    @Override
    public CardNumberConfigDto getCardNumberConfig(String configCode) {
        CardNumberConfig cardNumberConfig = new CardNumberConfig();
        cardNumberConfig.setConfigCode(configCode);
        CardNumberConfig config = cardNumberConfigMapper.selectOne(cardNumberConfig);
        if (null != config){
            return BeanCopyUtils.jsonCopyBean(config, CardNumberConfigDto.class);
        }
        return null;
    }

    @Override
    public PageData<CardNumberConfigDto> queryCardNumberConfigList(CardNumberConfigDto cardNumberConfigDto) {
        PageHelper.startPage(cardNumberConfigDto.getPageNum(), cardNumberConfigDto.getPageSize());
        Weekend<CardNumberConfig> weekend = Weekend.of(CardNumberConfig.class);
        WeekendCriteria<CardNumberConfig, Object> criteria = weekend.weekendCriteria();
        criteria .andEqualTo(CardNumberConfig::getStatus, ConvertUtils.toString(cardNumberConfigDto.getStatus()));
        criteria.andEqualTo(CardNumberConfig::getType, ConvertUtils.toString(cardNumberConfigDto.getType()));

        if (!StringUtils.isEmpty(cardNumberConfigDto.getDescription())){
            criteria.andLike(CardNumberConfig::getDescription, "%" + cardNumberConfigDto.getDescription() + "%");
        }
        weekend.orderBy("id").desc();
        List<CardNumberConfig> cardNumberConfigs = cardNumberConfigMapper.selectByCondition(weekend);
        if (Collections.isEmpty(cardNumberConfigs)) return new PageData<>();
        PageInfo<CardNumberConfig> info = PageInfo.of(cardNumberConfigs);
        List<CardNumberConfigDto> cardNumberConfigDtos = BeanCopyUtils.jsonCopyList(info.getList(), CardNumberConfigDto.class);
        return new PageData<>(cardNumberConfigDtos, info.getTotal());
    }

    @Override
    public List<CardNumberConfigDto> queryAllDenominationConfigList() {

        Weekend<CardNumberConfig> weekend = Weekend.of(CardNumberConfig.class);
        weekend.weekendCriteria()
                .andEqualTo(CardNumberConfig::getType, "3");
        List<CardNumberConfig> cardNumberConfigs = cardNumberConfigMapper.selectByCondition(weekend);
        if (Collections.isEmpty(cardNumberConfigs)) return new ArrayList<>();
        return BeanCopyUtils.jsonCopyList(cardNumberConfigs, CardNumberConfigDto.class);
    }
}
