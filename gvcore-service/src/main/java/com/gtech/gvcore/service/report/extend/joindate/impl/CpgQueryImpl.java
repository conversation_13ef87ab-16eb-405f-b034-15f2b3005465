package com.gtech.gvcore.service.report.extend.joindate.impl;

import com.gtech.gvcore.dao.mapper.CpgMapper;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.service.report.extend.joindate.QuerySupport;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

/**
 * @ClassName CpgQueryImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/1/16 15:46
 * @Version V1.0
 **/
@Component
public class CpgQueryImpl implements QuerySupport<Cpg> {

    public static final Cpg EMPTY = new Cpg();

    @Autowired
    private CpgMapper cpgMapper;

    @Override
    public List<Cpg> queryByCode(List<String> codes, String... selectFields) {
        if (CollectionUtils.isEmpty(codes)) return Collections.emptyList();

        Example example = new Example(Cpg.class);
        example.createCriteria().andIn(Cpg.C_CPG_CODE, codes);

        if (ArrayUtils.isNotEmpty(selectFields)) example.selectProperties(selectFields);

        List<Cpg> list = cpgMapper.selectByCondition(example);
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        return list;
    }

    @Override
    public Function<Cpg, String> codeMapper() {
        return Cpg::getCpgCode;
    }

    @Override
    public Cpg emptyObject() {
        return EMPTY;
    }

    @Override
    public Class<Cpg> supportType() {
        return Cpg.class;
    }
}
