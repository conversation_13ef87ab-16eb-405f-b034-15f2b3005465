package com.gtech.gvcore.service.report.impl.support.sales.bo;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "cancel_sales_data")
@Data
public class CancelSalesDataBo {
    @Id
    private Long id;
    @Column(name = "transaction_id")
    private Long transactionId;
    @Column(name = "voucher_code")
    private String voucherCode;
    @Column(name = "create_time")
    private Date createTime;

    public CancelSalesDataBo(String voucherCode, Date createTime) {
        this.voucherCode = voucherCode;
        this.createTime = createTime;
    }

    public CancelSalesDataBo(Long id,Long transactionId,String voucherCode, Date createTime) {
        this.id = id;
        this.transactionId = transactionId;
        this.voucherCode = voucherCode;
        this.createTime = createTime;
    }

    public CancelSalesDataBo(Long transactionId,String voucherCode, Date createTime) {
        this.transactionId = transactionId;
        this.voucherCode = voucherCode;
        this.createTime = createTime;
    }

}
