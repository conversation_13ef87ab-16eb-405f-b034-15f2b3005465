package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.DateUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * @ClassName RedemptionSummaryStatisticalBo
 * @Description
 * <AUTHOR>
 * @Date 2023/4/6 16:46
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GcRedemptionSummaryStatisticalBo {

    private String transactionDateDay;
    private String merchantCode;
    private String outletCode;
    private String invoiceNumber;
    private String cpgCode;
    private Integer redemptionCount = 1;
    private BigDecimal denomination = BigDecimal.ZERO;
    private String notes;
    private BigDecimal amount;

    public static GcRedemptionSummaryStatisticalBo convert(GcRedemptionBo bo) {

        return new GcRedemptionSummaryStatisticalBo()
                .setTransactionDateDay(DateUtil.format(bo.getTransactionDate(), DateUtil.FORMAT_YYYYMMDDHHMISS))
                .setMerchantCode(bo.getMerchantCode())
                .setOutletCode(bo.getOutletCode())
                .setInvoiceNumber(bo.getInvoiceNumber())
                .setCpgCode(bo.getCpgCode())
                .setDenomination(bo.getDenomination())
                .setAmount(bo.getAmount())
                ;

    }

    public GcRedemptionSummaryStatisticalBo merge(GcRedemptionSummaryStatisticalBo bo) {

        redemptionCount += 1;
        amount = amount.add(bo.getAmount());

        return this;
    }

    public String getGroupKey() {

        return StringUtils.join("_", this.getTransactionDateDay(), this.getMerchantCode(), this.getOutletCode(), this.getCpgCode(), this.getInvoiceNumber());
    }

    public static GcRedemptionSummaryStatisticalBo newInstance(GcRedemptionSummaryStatisticalBo t) {

        return new GcRedemptionSummaryStatisticalBo()
                .setTransactionDateDay(t.getTransactionDateDay())
                .setMerchantCode(t.getMerchantCode())
                .setOutletCode(t.getOutletCode())
                .setInvoiceNumber(t.getInvoiceNumber())
                .setCpgCode(t.getCpgCode())
                .setRedemptionCount(t.getRedemptionCount())
                .setDenomination(t.getDenomination())
                .setNotes(t.getNotes())
                .setAmount(t.getAmount());
    }
}
