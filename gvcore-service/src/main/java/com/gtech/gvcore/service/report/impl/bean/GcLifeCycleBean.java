package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import com.gtech.gvcore.service.report.extend.ReportBeanCustomerAutoFull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: Generated based on CardLifeCycleBean
 * @Date: 2025/6/19
 * @Description: Gift Card Life Cycle Bean
 */
@Getter
@Setter
@Accessors(chain = true)
public class GcLifeCycleBean implements ReportBeanCustomerAutoFull {

    @ExcelProperty(index = 0, value = "Issuance Date")
    private String issuanceDate;

    @ExcelProperty(index = 1, value = "Gift Card Number")
    private String giftCardNumber;

    @ExcelProperty(index = 2, value = "Gift Card Program")
    private String giftCardProgram;

    @ExcelProperty(index = 3, value = "Gift Card Status")
    private String giftCardStatus;

    @ReportAmountValue
    @ExcelProperty(index = 4, value = "Denomination", converter = ExportExcelNumberConverter.class)
    private String denomination;

    @ReportAmountValue
    @ExcelProperty(index = 5, value = "Remaining Balance", converter = ExportExcelNumberConverter.class)
    private String remainingBalance;

    @ExcelProperty(index = 6, value = "Activation Date")
    private String activationDate;

    @ExcelProperty(index = 7, value = "Activation Ended")
    private String activationEnded;

    @ExcelProperty(index = 8, value = "Grace Period Ended")
    private String gracePeriodEnded;

    @ExcelProperty(index = 9, value = "Expiry Date")
    private String expiryDate;
}
