package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @ClassName GcRegenerateActivationCodeBean
 * @Description Gift Card Regenerate Activation Code Bean
 * <AUTHOR> based on RegenerateActivationCodeBean
 * @Date 2025/6/19
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GcRegenerateActivationCodeDetailBean {

    @ExcelProperty(value = "Gift Card Number")
    private String cardNumber;

    @ExcelProperty(value = "Gift Card Program Group")
    private String cpgCode;

    @ExcelProperty(value = "Transaction Date")
    private Date transactionDate;

    @ReportAmountValue
    @ExcelProperty(value = "Total Amount", converter = ExportExcelNumberConverter.class)
    private String totalAmount;

    @ExcelProperty(value = "Customer Name")
    private String customerName;

    @ExcelProperty(value = "Expiry Date")
    private String expiryDate;

    @ExcelProperty(value = "Issuance Date")
    private Date issuanceDate;

    @ExcelProperty(value = "Activation Ended")
    private String activationEnded;
    @ExcelProperty(value = "Grace Period Ended")
    private String gracePeriodEnded;
    @ExcelProperty(value = "Request #")
    private String issueHandlingCode;
    @ExcelProperty(value = "Customer Email")
    private String customerEmail;

}
