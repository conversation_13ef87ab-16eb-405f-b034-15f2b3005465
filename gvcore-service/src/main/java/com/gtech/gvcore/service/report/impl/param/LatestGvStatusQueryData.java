package com.gtech.gvcore.service.report.impl.param;

import com.gtech.gvcore.service.report.ReportQueryParam;
import com.gtech.gvcore.service.report.extend.row.VoucherPageParam;
import com.gtech.gvcore.service.report.extend.voucherstatus.ReportQueryVoucherStatusParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 
 * <AUTHOR>
 * @date 2022年4月26日
 */
@Getter
@Setter
@Accessors(chain = true)
public class LatestGvStatusQueryData extends VoucherPageParam implements ReportQueryParam, ReportQueryVoucherStatusParam {

    private List<String> cpgCodeList;
    private List<String> voucherStatusList;
    private List<String> voucherCodeList;

    private Long voucherCodeNumStart;

    private Long voucherCodeNumEnd;

}


