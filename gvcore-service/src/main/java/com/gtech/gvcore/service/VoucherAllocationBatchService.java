package com.gtech.gvcore.service;

import com.gtech.gvcore.dao.model.VoucherAllocationBatch;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年3月11日
 */
public interface VoucherAllocationBatchService {

    /**
     *
     * <AUTHOR>
     * @param list
     * @date 2022年3月11日
     */
    int insertList(List<VoucherAllocationBatch> list);

    /**
     *
     * <AUTHOR>
     * @param voucherAllocationCode
     * @date 2022年3月11日
     */
    List<VoucherAllocationBatch> queryByVoucherAllocationCode(String voucherAllocationCode);

    /**
     * <AUTHOR>
     * @param voucherCode
     * @return
     */
    List<VoucherAllocationBatch> getAllocateByVoucherCode(String voucherCode);

	List<VoucherAllocationBatch> selectAllocationBatchByVoucherNums(List<String> voucherNumVCR);

    List<VoucherAllocationBatch> queryByVoucherNum(String voucherNumberStart, String voucherNumberEnd);
}
