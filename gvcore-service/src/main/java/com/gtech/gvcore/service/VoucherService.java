package com.gtech.gvcore.service;


import com.gtech.commons.page.PageParam;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.externalmapping.request.BulkCancelRedeemRequest;
import com.gtech.gvcore.common.request.activateonly.ActivateOnlyParam;
import com.gtech.gvcore.common.request.cancelactivate.CancelActivateParam;
import com.gtech.gvcore.common.request.cancelredeem.CancelRedeemRequest;
import com.gtech.gvcore.common.request.createandissue.CreateAndIssueParam;
import com.gtech.gvcore.common.request.distribution.QueryCpgInventoryDetailRequest;
import com.gtech.gvcore.common.request.distribution.QueryOwnedCpgInfoListRequest;
import com.gtech.gvcore.common.request.redemption.RedemptionRequest;
import com.gtech.gvcore.common.request.transaction.BulkCancelRedeemRequestV2;
import com.gtech.gvcore.common.request.transaction.CancelCreateandissueRequest;
import com.gtech.gvcore.common.request.transaction.OnetimebarcodeRequest;
import com.gtech.gvcore.common.request.v3.TransactionsRequest;
import com.gtech.gvcore.common.request.voucher.CheckDynamicBarCodeRequest;
import com.gtech.gvcore.common.request.voucher.CreateVoucherRequest;
import com.gtech.gvcore.common.request.voucher.GetStartAndEndVoucherRequest;
import com.gtech.gvcore.common.request.voucher.GetVoucherInformationRequest;
import com.gtech.gvcore.common.request.voucher.UpdateVoucherStatusRequest;
import com.gtech.gvcore.common.request.voucher.VerifyVoucherRequest;
import com.gtech.gvcore.common.request.voucherbatch.BarCodeToCodeRequest;
import com.gtech.gvcore.common.request.voucherbatch.GenerateDigitalVouchersRequest;
import com.gtech.gvcore.common.response.activateonly.ActivateOnlyGinseng;
import com.gtech.gvcore.common.response.cancelactivate.CancelActivateGinseng;
import com.gtech.gvcore.common.response.cancelcreateandissue.CancelCreateAndIssueGinseng;
import com.gtech.gvcore.common.response.cancelredeem.APIBulkCancelRedeemResponse;
import com.gtech.gvcore.common.response.cancelredeem.CancelRedeemResponse;
import com.gtech.gvcore.common.response.createandissue.CreateAndIssueGinseng;
import com.gtech.gvcore.common.response.customerorder.GetCustomerOrderDetailsResponse;
import com.gtech.gvcore.common.response.distribution.CpgInventoryDetailResponse;
import com.gtech.gvcore.common.response.distribution.CustomerCpgEffectiveDateResult;
import com.gtech.gvcore.common.response.distribution.OwnedCpgInfoResponse;
import com.gtech.gvcore.common.response.redemption.RedemptionResponse;
import com.gtech.gvcore.common.response.transaction.BulkCancelRedeemResponse;
import com.gtech.gvcore.common.response.transaction.BulkCancelRedeemResponseV2;
import com.gtech.gvcore.common.response.transaction.OnetimebarcodeResponse;
import com.gtech.gvcore.common.response.v3.CreateAndIssueResponse;
import com.gtech.gvcore.common.response.voucher.CheckDynamicBarCodeResponse;
import com.gtech.gvcore.common.response.voucher.GetStartAndEndVoucherResponse;
import com.gtech.gvcore.common.response.voucher.GetVoucherInformationResponse;
import com.gtech.gvcore.common.response.voucher.VerifyResponse;
import com.gtech.gvcore.common.request.voucher.VoucherRangeCheckRequest;
import com.gtech.gvcore.common.response.voucher.VoucherRangeCheckResponse;
import com.gtech.gvcore.dao.dto.CpgVoucherCodeNumDto;
import com.gtech.gvcore.dao.dto.VoucherCodeNumDto;
import com.gtech.gvcore.dao.dto.VoucherDto;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.dto.CustomerCpgGroup;
import io.swagger.models.auth.In;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2022/3/2 14:47
 */
public interface VoucherService {

    int createVoucher(CreateVoucherRequest request);

    int createVoucherList(List<CreateVoucherRequest> request);

    Voucher getVoucherByCode(String voucherCode);

    /**
     * @param issuerCode 发行方
     * @param voucherCodeList 券码列表
     * @return 券码列表对应的券信息列表
     * <AUTHOR>
     * @date 2022年3月9日
     */
    List<Voucher> queryByVoucherCodeList(String issuerCode, List<String> voucherCodeList);

    /**
     * @param issuerCode    发行方
     * @param voucherOwnerCode 券拥有方
     * @param voucherCodeList   券码列表
     * @return 券码列表对应的券信息列表
     * <AUTHOR>
     * @date 2022年3月21日
     */
    List<Voucher> queryByVoucherCodeList(String issuerCode, String voucherOwnerCode, List<String> voucherCodeList);


    int deleteByVoucherBatch(String voucherBatchCode);

    int updateVoucherStatus(UpdateVoucherStatusRequest request);
    int updateVoucherStatusByTable(UpdateVoucherStatusRequest request, Integer index);

    List<Voucher> queryNotReceiveVoucher(String voucherStartNo, String voucherEndNo, String sourceType);

    List<Voucher> queryVoucherByInterval(String voucherStartNo, String voucherEndNo);

    /**
     * @param voucherCodeNumDtoList 券码数量列表
     * @return 券码数量列表对应的券信息列表
     * <AUTHOR>
     * @date 2022年3月21日
     */
    List<VoucherDto> countGroupByDenominationAndCirculationStatus(List<VoucherCodeNumDto> voucherCodeNumDtoList);

    /**
     * @param voucherCodeNumDtoList     券码数量列表
     * @return 券码数量列表对应的券信息列表
     * <AUTHOR>
     * @date 2022年3月23日
     */
    List<VoucherDto> countGroupByCustomerOrderAllocation(List<VoucherCodeNumDto> voucherCodeNumDtoList);

    /**
     * @param voucherDtoList    券信息列表
     * @param pageSize 分页大小
     * @return 券信息列表对应的券信息列表，按照券码数量分组，每组券码数量不超过pageSize，
     * 券码数量相同的券信息按照券码排序，券码相同的券信息按照券状态排序，券状态相同的券信息按照券id排序
     * <AUTHOR>
     * @date 2022年4月1日
     */
    List<Voucher> queryMismatchByAllocationCountDto(List<VoucherDto> voucherDtoList, Integer pageSize);

    /**
     * @param issuerCode 发行方
     * @param voucherCodeNumDtoList 券码数量列表
     * @return 券码数量列表对应的券信息列表，按照券码数量分组，每组券码数量不超过pageSize，
     * <AUTHOR>
     * @date 2022年3月11日
     */
    List<CpgVoucherCodeNumDto> groupByDenominationAndCpg(String issuerCode,
                                                         List<VoucherCodeNumDto> voucherCodeNumDtoList);

    /**
     * @param issuerCode 发行方
     * @return 券码数量列表对应的券信息列表，按照券码数量分组，每组券码数量不超过pageSize，
     * <AUTHOR>
     * @date 2022年3月11日
     */
	Map<Long, Voucher> queryByVoucherCodeNumList(String issuerCode, List<String> voucherCodeList);

    /**
     * @param issuerCode 发行方
     * @param oldVoucherOwnerCode 券拥有方
     * @param voucherCodeNumLlist 券码数量列表
     * @param newVoucherOwnerCode 新券拥有方
     * @param newVoucherOwnerType 新券拥有方类型
     * @return 更新的券数量
     * <AUTHOR>
     * @date 2022年4月8日
     */
    int allocateVoucher(String issuerCode, String oldVoucherOwnerCode, List<CpgVoucherCodeNumDto> voucherCodeNumLlist,
                        String newVoucherOwnerCode, String newVoucherOwnerType);

    /**
     * @param issuerCode          发行方
     * @param oldVoucherOwnerCode 券拥有方
     * @param voucherCodeNumLlist 券码数量列表
     * @param newVoucherOwnerCode 新券拥有方
     * @param newVoucherOwnerType 新券拥有方类型
     * @param customerOrder
     * @return 更新的券数量
     * <AUTHOR>
     * @date 2022年4月8日
     */
    int activatPhysicalVoucherByCustomerOrder(String issuerCode, String oldVoucherOwnerCode,
                                              List<CpgVoucherCodeNumDto> voucherCodeNumLlist, String newVoucherOwnerCode, String newVoucherOwnerType, CustomerOrder customerOrder);

    /**
     * @param issuerCode 发行方
     * @param voucherOwnerCode 券拥有方
     * @param voucherCodeNumLlist   券码数量列表
     * @return 更新的券数量
     */
    int updateByCustomerOrderReleaseReject(String issuerCode, String voucherOwnerCode,
            List<CpgVoucherCodeNumDto> voucherCodeNumLlist);

    /**
     * @param customerOrder 订单信息
     * @param updateUser    更新人
     * @param approvalCode
     * @return 更新的券数量
     * <AUTHOR>
     * @date 2022年4月28日
     */
    int activatDigitalVoucherByCustomerOrder(CustomerOrder customerOrder, String updateUser, String approvalCode);

    List<GetStartAndEndVoucherResponse> getStartAndEndVoucher(GetStartAndEndVoucherRequest request);

    VerifyResponse verifyVoucher(VerifyVoucherRequest request);

    CancelRedeemResponse cancelRedeem(CancelRedeemRequest request);

    CancelActivateGinseng cancelActivate(CancelActivateParam request, String batchId);

    CreateAndIssueGinseng createAndIssue(CreateAndIssueParam request);

    ActivateOnlyGinseng activateOnly(ActivateOnlyParam request);

    void regenerateDigitalVoucher(GetCustomerOrderDetailsResponse detail, String voucherBatchCode);

    void generateECouponsAsynchronously(GenerateDigitalVouchersRequest request, GetCustomerOrderDetailsResponse detail, String voucherBatchCode, Set<String> digitalVoucherCodes);

    OnetimebarcodeResponse generateDigitalBarCode(OnetimebarcodeRequest voucherCode);



    void updateVoucherOwner(String voucherBatchCode, String issuerCode);

	void cancelSales(String voucherOwnerCode, String voucherOwnerType, List<String> voucherCodeList, Integer voucherStatus, Integer circulationStatus, Integer status);
	Integer cancelSalesByStartAndEnd(String voucherOwnerCode, String voucherOwnerType, String startVoucherCode,String endVoucherCode, Integer voucherStatus, Integer circulationStatus, Integer status);

    Result<RedemptionResponse> validateRedemption(RedemptionRequest request);

    Result<Void> redemption(RedemptionRequest request);

    CheckDynamicBarCodeResponse checkDynamicBarCode(CheckDynamicBarCodeRequest request);

    /**
     * 
     * @return 券信息列表
     * <AUTHOR>
     * @date 2022年4月26日
     */
    List<Voucher> queryByVoucherCodeNumRange(String voucherCode, List<String> issuerCodeList);

    /**
     *
     * <AUTHOR>
     * @return 券信息列表
     * @date 2022年4月26日
     */
    Voucher getByVoucherCodeNumRangeReturnByPage(String voucherCode, List<String> issuerCodeList);

    Long countByVoucherCodeNumRangeReturn(String voucherCode, Long voucherCodeNumStart, Long voucherCodeNumEnd, List<String> issuerCodeList);

    String getVoucherByBarCode(BarCodeToCodeRequest barCode);

    List<Voucher> queryCpgCodeByNumberAndExpiryStatus(String voucherNumberStart, String voucherNumberEnd, Date expiryStatusStart, Date expiryStatusEnd);

    Result<GetVoucherInformationResponse> getVoucherInformation(GetVoucherInformationRequest request);

    CreateAndIssueResponse transactionsCreateAndIssue(TransactionsRequest param);

    List<OwnedCpgInfoResponse> queryOwnedCpgInfoList(QueryOwnedCpgInfoListRequest request);

    List<CustomerCpgEffectiveDateResult>  queryCustomerCpgInventoryDetail(String customerCode, String cpgCode);

    CpgInventoryDetailResponse queryCpgInventoryDetail(QueryCpgInventoryDetailRequest request);

    List<CustomerCpgGroup> queryCustomerCpgGroupPage(PageParam pageParam);

    void ready2Distribute(Collection<String> voucherCodeSet, int requiredQuantity, String customerCode, String updateUserCode);

    Set<Voucher> queryDistributableVoucher(String cpgCode, Date expiryDate, String customerCode, int requiredQuantity);

    CancelCreateAndIssueGinseng cancelCreateAndIssue(CancelCreateandissueRequest request);

    int countDistributableVoucher(String cpgCode, String customerCode);

    void disableVoucherBatch(String voucherBatchCode);

    List<Voucher> queryVoucherListByVoucherBatchCode(String voucherBatchCode);

    APIBulkCancelRedeemResponse bulkCancelRedeem(BulkCancelRedeemRequest request, String terminalId, String batchId);

    VoucherRangeCheckResponse checkVoucherRange(VoucherRangeCheckRequest request);

    BulkCancelRedeemResponseV2 bulkCancelRedeemV2(BulkCancelRedeemRequestV2 request, String terminalId, String batchId);
}

