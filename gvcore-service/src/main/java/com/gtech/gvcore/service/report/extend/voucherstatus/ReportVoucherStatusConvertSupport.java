package com.gtech.gvcore.service.report.extend.voucherstatus;

import com.gtech.gvcore.common.enums.ReportVoucherStatusEnum;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.report.extend.context.ReportContextHelper;

import java.util.Date;

/**
 * @ClassName ReportVoucherStatusSupport
 * @Description 转换卡券状态支持接口
 * @see ReportVoucherStatusEnum
 * <AUTHOR>
 * @Date 2023/1/17 16:41
 * @Version V1.0
 **/
public interface ReportVoucherStatusConvertSupport {

    /**
     * 卡券voucher 状态文本获取
     */
    default String getVoucherStatusDesc(Voucher voucher, Date now) {

        return this.getVoucherStatus(voucher, now).getDesc();
    }

    default ReportVoucherStatusEnum getVoucherStatus(Integer status, Integer voucherStatus, Date effectiveDate, String mopCode) {

        return this.getVoucherStatus(Voucher.builder().status(status).voucherStatus(voucherStatus).voucherEffectiveDate(effectiveDate).mopCode(mopCode).build(), ReportContextHelper.reportBuilderTime());
    }

    default ReportVoucherStatusEnum getVoucherStatus(Integer status, Integer voucherStatus, Date effectiveDate, String mopCode, Date now) {

        return this.getVoucherStatus(Voucher.builder().status(status).voucherStatus(voucherStatus).voucherEffectiveDate(effectiveDate).mopCode(mopCode).build(), now);
    }

    default ReportVoucherStatusEnum getVoucherStatus(Voucher voucher, Date now) {

        return ReportVoucherStatusConvertUtils.getVoucherStatus(voucher, now);
    }

    default ReportVoucherStatusEnum getVoucherStatus(Voucher voucher) {

        return this.getVoucherStatus(voucher, ReportContextHelper.reportBuilderTime());
    }

    default String getVoucherStatusDesc(Voucher voucher) {

        return this.getVoucherStatusDesc(voucher, ReportContextHelper.reportBuilderTime());
    }

}
