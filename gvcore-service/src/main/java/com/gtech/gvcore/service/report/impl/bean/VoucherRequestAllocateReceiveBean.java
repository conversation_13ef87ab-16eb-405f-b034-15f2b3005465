package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName VoucherRequestAllocateReceiveBean
 * @Description VoucherRequestAllocateReceiveBean
 * <AUTHOR>
 * @Date 2023/2/13 11:12
 * @Version V1.0
 **/

@Getter
@Setter
@Accessors(chain = true)
public class VoucherRequestAllocateReceiveBean {

    @ExcelProperty(value = "Request #")
    private String requestId;

    @ExcelProperty(value = "Request Source")
    private String requestSourceName;

    @ExcelProperty(value = "Voucher Program Group")
    private String vpgName;

    @ExcelProperty(value = "Number of Voucher", converter = ExportExcelNumberConverter.class)
    private String numberOfVoucher;

    @ReportAmountValue
    @ExcelProperty(value = "Voucher Amount", converter = ExportExcelNumberConverter.class)
    private String voucherAmount;

    @ExcelProperty(value = "Create On")
    private String createOn;

    @ExcelProperty(value = "Create By")
    private String createBy;

    @ExcelProperty(value = "Booklet Number (Number of Booklets)")
    private String bookletNumber;

    @ExcelProperty(value = "Voucher Number (Number of Vouchers)")
    private String voucherNumber;

    @ExcelProperty(value = "Status")
    private String status;

}