package com.gtech.gvcore.service.report.impl.param;

import com.gtech.gvcore.service.report.ReportQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年6月15日
 */
@Getter
@Setter
@Accessors(chain = true)
public class ReportBasicQueryData implements ReportQueryParam {

    private List<String> issuerCodeList;

    private List<String> outletCodeList;

    private Boolean isNotFoundOutlet;

    private Long voucherCodeNumStart;

    private Long voucherCodeNumEnd;

    private Integer pageNum;

    private Integer pageSize;

    private Long startId = 0L;

    private String voucherCode;

}
