package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.service.report.extend.poll.GroupNewTransactionByVoucherCodeSupport;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName AgingVoucherTransactionBo
 * @Description
 * <AUTHOR>
 * @Date 2023/1/13 17:21
 * @Version V1.0
 **/
//merchant_code, voucher_code, DATE_FORMAT(MAX(transaction_date), '%Y-%m-%d %H:%i:%S') AS transaction_date
@Getter
@Setter
@Accessors(chain = true)
public class AgingVoucherTransactionBoVoucher implements GroupNewTransactionByVoucherCodeSupport {

    private String merchantCode;

    private String voucherCode;

    private String transactionDate;

    public Date getTransactionDate() {

        return DateUtil.parseDate(transactionDate, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

    private String transactionCode;

    private String transactionType;

    private BigDecimal transactionNumber;

    public void setTransactionCode(String transactionCode) {
        this.transactionNumber = new BigDecimal(transactionCode.substring(3));
        this.transactionCode = transactionCode;
    }

}
