package com.gtech.gvcore.service.report.impl;

import com.gtech.gvcore.common.enums.CustomerTypeEnum;
import com.gtech.gvcore.common.enums.GcTransactionTypeEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.Pos;
import com.gtech.gvcore.giftcard.infrastructure.entity.GiftCardEntity;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.PollReport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.ManualPollBusinessReport;
import com.gtech.gvcore.service.report.impl.bean.GcTransactionsDetailedBean;
import com.gtech.gvcore.service.report.impl.bo.GcTransactionDetailedBo;
import com.gtech.gvcore.service.report.impl.param.GcTransactionDetailQueryData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;


@Service
public class GcTransactionsDetailedImpl extends ManualPollBusinessReport<GcTransactionDetailQueryData, GcTransactionsDetailedBean, GcTransactionDetailedBo>
        implements BusinessReport<GcTransactionDetailQueryData, GcTransactionsDetailedBean>, PollReport {

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_TRANSACTIONS_DETAILED_SUMMARY_REPORT;
    }


    public Map<String, Supplier<List<GcTransactionDetailedBo>>> buildQueryMap(GcTransactionDetailQueryData param, RowBounds rowBounds) {
        Map<String, Supplier<List<GcTransactionDetailedBo>>> actionMap = new HashMap<>();

        actionMap.put(GcTransactionTypeEnum.GIFT_CARD_SELL.getCode(), () -> gcReportBusinessMapper.findSalesTransactions(param, rowBounds));
        actionMap.put(GcTransactionTypeEnum.GIFT_CARD_REDEEM.getCode(), () -> gcReportBusinessMapper.findRedemptionTransactions(param, rowBounds));
        actionMap.put(GcTransactionTypeEnum.GIFT_CARD_CANCEL_SELL.getCode(), () -> gcReportBusinessMapper.findCancelSalesTransactions(param, rowBounds));
        actionMap.put(GcTransactionTypeEnum.GIFT_CARD_CANCEL_REDEEM.getCode(), () -> gcReportBusinessMapper.findCancelRedemptionTransactions(param, rowBounds));
        actionMap.put(GcTransactionTypeEnum.GIFT_CARD_ACTIVATE.getCode(), () -> gcReportBusinessMapper.findActivationTransactions(param, rowBounds));
        actionMap.put(GcTransactionTypeEnum.GIFT_CARD_ACTIVATION_EXTENSION.getCode(), () -> gcReportBusinessMapper.findActivationExtensionTransactions(param, rowBounds));
        actionMap.put(GcTransactionTypeEnum.GIFT_CARD_DEACTIVATE.getCode(), () -> gcReportBusinessMapper.findDeactivateTransactions(param, rowBounds));
        actionMap.put(GcTransactionTypeEnum.GIFT_CARD_REACTIVATE.getCode(), () -> gcReportBusinessMapper.findReactivateTransactions(param, rowBounds));
        return actionMap;
    }

    @Override
    public GcTransactionDetailQueryData builderQueryParam(CreateReportRequest reportParam) {
        GcTransactionDetailQueryData queryData = new GcTransactionDetailQueryData();

        // Set transaction date range
        queryData.setTransactionDateStart(reportParam.getTransactionDateStart());
        queryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());

        // Set issuer codes
        queryData.setIssuerCodes(reportParam.getIssuerCodeList());

        // Set merchant and outlet codes
        queryData.setMerchantCodes(reportParam.getMerchantCodes());
        queryData.setOutletCodes(reportParam.getOutletCodes());

        // Set CPG codes
        queryData.setCpgCodes(reportParam.getCpgCodes());

        // Set transaction types - when empty, query all types; when provided, query specific types
        if (CollectionUtils.isEmpty(reportParam.getTransactionTypes())) {
            // When transactionTypes is empty, set all available transaction type codes
            List<String> allTransactionTypes = Arrays.asList(
                    GcTransactionTypeEnum.GIFT_CARD_SELL.getCode(),
                    GcTransactionTypeEnum.GIFT_CARD_REDEEM.getCode(),
                    GcTransactionTypeEnum.GIFT_CARD_CANCEL_SELL.getCode(),
                    GcTransactionTypeEnum.GIFT_CARD_CANCEL_REDEEM.getCode(),
                    GcTransactionTypeEnum.GIFT_CARD_ACTIVATE.getCode(),
                    GcTransactionTypeEnum.GIFT_CARD_ACTIVATION_EXTENSION.getCode(),
                    GcTransactionTypeEnum.GIFT_CARD_DEACTIVATE.getCode(),
                    GcTransactionTypeEnum.GIFT_CARD_REACTIVATE.getCode()
            );
            queryData.setTransactionType(allTransactionTypes);
        } else {
            // When transactionTypes has values, use the provided types
            queryData.setTransactionType(reportParam.getTransactionTypes());
        }
        queryData.setCardStatusList(reportParam.getVoucherStatus());

        // Set invoice number
        queryData.setInvoiceNumber(reportParam.getInvoiceNo());

        // Set customer information
        queryData.setCustomerCodeList(reportParam.getCustomerCodes());

        // Set bulk order status
        queryData.setExpiryDateStart(reportParam.getVoucherEffectiveDateStart());
        queryData.setExpiryDateEnd(reportParam.getVoucherEffectiveDateEnd());

        // Set card number range
        if (reportParam.getVoucherCode() != null) {
            queryData.setCardNumberList(Arrays.asList(reportParam.getVoucherCode().split(",")));
        }
        return queryData;
    }

    @Override
    public List<GcTransactionDetailedBo> filter(GcTransactionDetailQueryData param, List<GcTransactionDetailedBo> boList) {
        // Filter card status
        this.filterCardStatus(boList, param.getCardStatusList());
        return boList;
    }

    @Override
    protected List<GcTransactionDetailedBo> findDate(GcTransactionDetailQueryData param, RowBounds rowBounds) {
        Map<String, Supplier<List<GcTransactionDetailedBo>>> actionMap = buildQueryMap(param, rowBounds);
        List<GcTransactionDetailedBo> gcTransactionDetailedBos = new ArrayList<>();

        // Query specific transaction types (already handled in builderQueryParam method)
        for (String transType : param.getTransactionType()) {
            gcTransactionDetailedBos.addAll(actionMap.get(transType).get());
        }

        final JoinDataMap<GcCpg> cpgMap = super.getMapByCode(gcTransactionDetailedBos, GcTransactionDetailedBo::getCpgCode, GcCpg.class);
        gcTransactionDetailedBos.forEach(x -> x.setCpgCode(cpgMap.findValue(x.getCpgCode()).getCpgName()));
        gcTransactionDetailedBos.sort(Comparator.comparing(GcTransactionDetailedBo::getTransactionDate).reversed());
        return gcTransactionDetailedBos;
    }

    @Override
    protected List<GcTransactionsDetailedBean> getExportData(GcTransactionDetailQueryData param, List<GcTransactionDetailedBo> boList) {
        // Get related data maps
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(boList, GcTransactionDetailedBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(boList, GcTransactionDetailedBo::getOutletCode, Outlet.class);
        final JoinDataMap<Pos> posMap = super.getMapByCode(boList, GcTransactionDetailedBo::getPosCode, Pos.class);
        final JoinDataMap<Customer> customerMap = super.getMapByCode(boList, GcTransactionDetailedBo::getBuyerName, Customer.class);
        final JoinDataMap<GcCpg> cpgMap = super.getMapByCode(boList, GcTransactionDetailedBo::getCpgCode, GcCpg.class);

        if (CollectionUtils.isEmpty(boList)) {
            return Collections.emptyList();
        }

        // Convert to export data
        return boList.stream()
                .map(e -> {
                    Customer value = customerMap.findValue(e.getBuyerName());

                    String customerName;
                    if (Objects.equals(value.getCustomerType(), CustomerTypeEnum.CORPORATE.code())) {
                        customerName = value.getCompanyName();
                    } else {
                        customerName = value.getCustomerName();
                    }
                    Pos pos = posMap.findValue(e.getPosCode());
                    pos = pos == null ? new Pos() : pos;
                    return new GcTransactionsDetailedBean()
                            .setTransactionType(e.getTransactionType())
                            .setMerchant(merchantMap.findValue(e.getMerchantCode()).getMerchantName())
                            .setOutlet(outletMap.findValue(e.getOutletCode()).getOutletName())
                            .setTransactionDate(e.getTransactionDate())
                            .setCardNumber(e.getCardNumber())
                            .setCreateUser(e.getCreateUser())
                            .setCpgCode(e.getCpgCode())
                            .setPosName(pos.getPosName())
                            .setTerminal(posMap.findValue(e.getPosCode()).getMachineId())
                            .setOrganizationName(merchantMap.findValue(e.getMerchantCode()).getMerchantName())
                            .setBatchNumber(e.getBatchNumber())
                            .setLoginSource(e.getLoginSource())
                            .setDenomination(toAmount(e.getDenomination()))
                            .setAmount(toAmount(e.getAmount()))
                            .setActualOutlet(e.getActualOutlet())
                            .setForwardingActualId(e.getForwardingActualId())
                            .setTransactionMode("Transaction successful")
                            .setTransactionMode(e.getTransactionMode())
                            .setBuyerName(customerName)
                            .setCustomerFirstName(e.getCustomerFirstName())
                            .setCustomerLastName(e.getCustomerLastName())
                            .setMobile(e.getMobile())
                            .setCustomerFirstName(value.getContactFirstName())
                            .setCustomerLastName(value.getContactLastName())
                            .setResponseMessage("Transaction successful")
                            .setInvoiceNumber(e.getInvoiceNumber())
                            .setOtherInputParameter(e.getOtherInputParameter());
                }).collect(Collectors.toList());
    }


    /**
     * Filter card status
     */
    private void filterCardStatus(List<GcTransactionDetailedBo> boList, List<String> cardStatusList) {
        if (CollectionUtils.isEmpty(cardStatusList)) {
            return;
        }

        // Get gift card map with only status field
        JoinDataMap<GiftCardEntity> cardMap = super.getMapByCode(boList, GcTransactionDetailedBo::getCardNumber, GiftCardEntity.class,
                new String[]{"status"});

        // Filter card status
        boList.removeIf(transaction -> {
            GiftCardEntity giftCard = cardMap.get(transaction.getCardNumber());
            return giftCard != null && !cardStatusList.contains(giftCard.getStatus());
        });
    }
}
