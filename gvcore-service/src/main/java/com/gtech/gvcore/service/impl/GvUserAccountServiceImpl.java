package com.gtech.gvcore.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.gtech.gvcore.helper.PermissionHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.threads.ThreadPoolExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageRowBounds;
import com.gtech.basic.idm.common.constants.IdmConstants;
import com.gtech.basic.idm.common.enums.AccessTypeEnum;
import com.gtech.basic.idm.common.exceptions.ErrorCodes;
import com.gtech.basic.idm.dao.entity.MenuEntity;
import com.gtech.basic.idm.dao.mapper.IMenuMapper;
import com.gtech.basic.idm.dao.model.QueryRoleResourcesCondition;
import com.gtech.basic.idm.service.dto.GetUserAccountParamDto;
import com.gtech.basic.idm.service.dto.UserAccountDto;
import com.gtech.commons.exception.Exceptions;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.page.PageData;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.CryptoUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.request.useraccount.GvCreateUserAccountRequest;
import com.gtech.gvcore.common.request.useraccount.GvQueryUserAccountRequest;
import com.gtech.gvcore.common.request.useraccount.GvQueryUserAccountsCondition;
import com.gtech.gvcore.common.request.useraccount.GvUpdateUserAccountRequest;
import com.gtech.gvcore.common.response.useraccount.DataPermissionResponse;
import com.gtech.gvcore.common.response.useraccount.IssuerPermissionResponse;
import com.gtech.gvcore.common.response.useraccount.PermissionCodeResponse;
import com.gtech.gvcore.common.response.useraccount.UserAccountResponse;
import com.gtech.gvcore.dao.mapper.UserAccountMapper;
import com.gtech.gvcore.dao.model.Company;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.UserAccount;
import com.gtech.gvcore.service.CompanyService;
import com.gtech.gvcore.service.GvUserAccountService;
import com.gtech.gvcore.service.MerchantService;
import com.gtech.gvcore.service.OutletService;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

@Service
@Slf4j
public class GvUserAccountServiceImpl implements GvUserAccountService {

	private static final ThreadPoolExecutor executor = new ThreadPoolExecutor(20, 50, 100, TimeUnit.SECONDS, new LinkedBlockingDeque<>(1000),
			new ThreadPoolExecutor.CallerRunsPolicy());

	@Autowired
	private UserAccountMapper userAccountMapper;

	private static final String MIX_SALT = "26a711f75b90055fe169fca73a0e0f03";

	@Autowired
	private CompanyService companyService;

	@Autowired
	private MerchantService merchantService;

	@Autowired
	private OutletService outletService;

	@Autowired
	private IMenuMapper menuMapper;
    @Autowired
    private PermissionHelper permissionHelper;

	/**
	 * 创建用户账号
	 *
	 * @param param UserAccountDto
	 */
	@Override
	@Transactional
	public UserAccountDto createUserAccount(GvCreateUserAccountRequest param) {

		String mobileCountry = setMobileCountry(param.getMobileCountry());
		param.setMobileCountry(mobileCountry);

		UserAccount entity = BeanCopyUtils.jsonCopyBean(param, UserAccount.class);

		entity.setCreateUser(param.getOperateUser());
		if (!CollectionUtils.isEmpty(param.getIssuerPermissionList())) {
			entity.setExtendParams(JSON.toJSONString(param.getIssuerPermissionList()));
		}
		entity.setFullName(param.getFirstName() + " " + param.getLastName());
		// 加密用户数据
		this.encrypt(entity);

		try {

			int id = userAccountMapper.insertSelective(entity);

			UserAccountDto resultDto = BeanCopyUtils.jsonCopyBean(param, UserAccountDto.class);

			resultDto.setId((long) id);

			return resultDto;

		}  catch (DuplicateKeyException e) {

			throw this.toGTechBaseException(e);
		}

	}

	private String setMobileCountry(String mobileCountry) {
		if (StringUtils.isNotBlank(mobileCountry)){
			mobileCountry = mobileCountry.replace("\\+", "");
		}
		return mobileCountry;
	}


	private GTechBaseException toGTechBaseException(DuplicateKeyException e) {

		String message = e.getMessage();

		if (message.contains("uidx_usercode")) {
			return Exceptions.fail(ErrorCodes.DUPLICATED_USERCODE_ERROR, e);
		}
		if (message.contains("uidx_tenant_account")) {
			return Exceptions.fail(ErrorCodes.DUPLICATED_ACCOUNT_ERROR, e);
		}
		if (message.contains("uidx_tenant_email")) {
			return Exceptions.fail(ErrorCodes.DUPLICATED_EMAIL_ERROR, e);
		}
		if (message.contains("uidx_tenant_mobile")) {
			return Exceptions.fail(ErrorCodes.DUPLICATED_MOBILE_ERROR, e);
		}
		if (message.contains("uidx_third_id")) {
			return Exceptions.fail(ErrorCodes.DUPLICATED_THIRD_ID, e);
		}
		return Exceptions.fail(ErrorCodes.DUPLICATED_UNKNOWN_ERROR, e);
	}

	/**
	 * 更新用户账号信息
	 *
	 * @param param UserAccountDto
	 */
	@Override
	@Transactional
	public int updateUserAccount(GvUpdateUserAccountRequest param) {

		String mobileCountry = setMobileCountry(param.getMobileCountry());
		param.setMobileCountry(mobileCountry);

		UserAccount entity = BeanCopyUtils.jsonCopyBean(param, UserAccount.class);

		entity.setUpdateUser(param.getOperateUser());
		entity.setUpdateTime(new Date());
		if (!CollectionUtils.isEmpty(param.getIssuerPermissionList())) {
			entity.setExtendParams(JSON.toJSONString(param.getIssuerPermissionList()));
		}
		entity.setFullName(param.getFirstName() + " " + param.getLastName());
		// 加密用户数据
		this.encrypt(entity);

		Example condition = new Example(UserAccount.class, true, true);
		condition.createCriteria()
				.andEqualTo(UserAccount.C_DOMAIN_CODE, param.getDomainCode())
				.andEqualTo(UserAccount.C_TENANT_CODE, param.getTenantCode())
				.andEqualTo(UserAccount.C_USER_CODE, param.getUserCode());

		try {
			return this.userAccountMapper.updateByConditionSelective(entity, condition);

		}  catch (DuplicateKeyException e) {

			throw this.toGTechBaseException(e);
		}
	}

	// 加密用户信息
	private void encrypt(UserAccount entity) {

		entity.setPassword(ConvertUtils.toString(CryptoUtils.sha512Encrypt(entity.getPassword(), MIX_SALT), null));
		entity.setPayPassword(ConvertUtils.toString(CryptoUtils.sha512Encrypt(entity.getPayPassword(), MIX_SALT), null));

		entity.setLastName(CryptoUtils.aesEncrypt(entity.getLastName(), MIX_SALT));
		entity.setFirstName(CryptoUtils.aesEncrypt(entity.getFirstName(), MIX_SALT));

		entity.setMobile(ConvertUtils.toString(CryptoUtils.aesEncrypt(entity.getMobile(), MIX_SALT), null));
		entity.setAccount(ConvertUtils.toString(CryptoUtils.aesEncrypt(ConvertUtils.toString(entity.getAccount(), "").toLowerCase(), MIX_SALT), null));
		entity.setEmail(ConvertUtils.toString(CryptoUtils.aesEncrypt(ConvertUtils.toString(entity.getEmail(), "").toLowerCase(), MIX_SALT), null));
	}

	// 解密用户信息
	private void decrypt(UserAccount entity) {

		entity.setAccount(CryptoUtils.aesDecrypt(entity.getAccount(), MIX_SALT));
		entity.setLastName(CryptoUtils.aesDecrypt(entity.getLastName(), MIX_SALT));
		entity.setFirstName(CryptoUtils.aesDecrypt(entity.getFirstName(), MIX_SALT));
		entity.setMobile(CryptoUtils.aesDecrypt(entity.getMobile(), MIX_SALT));
		entity.setEmail(CryptoUtils.aesDecrypt(entity.getEmail(), MIX_SALT));
	}

	public Map<String, String> queryFullNameByCodeList(List<String> userCodeList) {
		if (CollectionUtils.isEmpty(userCodeList)) {
			return Collections.emptyMap();
		}
		Example example = new Example(UserAccount.class);
		example.createCriteria().andIn(UserAccount.C_USER_CODE, userCodeList);
		List<UserAccount> accountList = userAccountMapper.selectByCondition(example);
		return accountList.stream().collect(Collectors.toMap(UserAccount::getUserCode, UserAccount::getFullName));
	}

	public String getUserEmail(String userCode) {
		UserAccount userAccount = new UserAccount();
		userAccount.setUserCode(userCode);
		userAccount = userAccountMapper.selectOne(userAccount);
		if (userAccount == null) {
			return null;
		}
		UserAccount account = BeanCopyUtils.jsonCopyBean(userAccount, UserAccount.class);
		try {
			decrypt(account);
		} catch (Exception e) {
			log.error("解密失败{}",JSON.toJSONString(account));
		}
		return account.getEmail();
	}

	@Override
	public UserAccount getUserNameInfo(String userCode) {

		if (StringUtils.isBlank(userCode)) {
			return null;
		}
		UserAccount userAccount = new UserAccount();
		userAccount.setUserCode(userCode);
		UserAccount foundEntity = userAccountMapper.selectOne(userAccount);
		if (foundEntity == null) {
			return null;
		}
		try {
			decrypt(foundEntity);
		} catch (Exception e) {
			log.error("解密失败{}",JSON.toJSONString(foundEntity));
		}
		return foundEntity;
	}

	/**
	 * Retrieve a user account information by user code.
	 */
	@Override
	public UserAccountResponse getUserAccount(GetUserAccountParamDto param) {

		UserAccount foundEntity = null;

		if (StringUtil.isBlank(param.getUserCode()) && StringUtil.isBlank(param.getAccount()) && StringUtil.isBlank(param.getEmail())
				&& StringUtil.isBlank(param.getMobile())) {
			return null;
		}

		UserAccount condition = UserAccount.builder().domainCode(param.getDomainCode()).tenantCode(param.getTenantCode())
				.status(ConvertUtils.toInteger(param.getStatus(), null)).build();

		if (StringUtils.isNotBlank(param.getUserCode())) {
			condition.setUserCode(param.getUserCode());

		} else if (StringUtils.isNotBlank(param.getAccount())) {
			condition.setAccount(param.getAccount());

		} else if (StringUtils.isNotBlank(param.getMobile())) {
			condition.setMobile(param.getMobile());

		} else if (StringUtils.isNotBlank(param.getEmail())) {
			condition.setEmail(param.getEmail());
		}

		this.encrypt(condition);
		foundEntity = this.userAccountMapper.selectOne(condition);

		if (null == foundEntity) {
			return null;
		}

		this.decrypt(foundEntity);
		UserAccountResponse response = BeanCopyUtils.jsonCopyBean(foundEntity, UserAccountResponse.class);
		if (!StringUtil.isEmpty(foundEntity.getExtendParams())) {
			response.setIssuerPermissionList(JSON.parseArray(foundEntity.getExtendParams(), IssuerPermissionResponse.class));
		}
		return response;
	}

	@Override
	public List<PermissionCodeResponse> queryPerrmissionCodeList(String userCode) {
		UserAccount userAccount = new UserAccount();
		userAccount.setUserCode(userCode);
		UserAccount user = userAccountMapper.selectOne(userAccount);
		if (user == null || StringUtil.isEmpty(user.getExtendParams())) {
			return Collections.emptyList();
		}

		List<Company> companyList = companyService.queryAllCompany();
		List<Merchant> merchantList = merchantService.queryAllMerchant();
		List<Outlet> outletList = outletService.queryAllOutLet();
		Map<String, List<String>> companyMerchantMap = new HashMap<>();
		merchantList.stream().collect(Collectors.groupingBy(Merchant::getCompanyCode)).forEach((companyCode, list) -> {
			companyMerchantMap.put(companyCode, list.stream().map(Merchant::getMerchantCode).collect(Collectors.toList()));
		});
		Map<String, List<String>> merchantOutMap = new HashMap<>();
		outletList.stream().collect(Collectors.groupingBy(Outlet::getMerchantCode)).forEach((merchantCode, list) -> {
			merchantOutMap.put(merchantCode, list.stream().map(Outlet::getOutletCode).collect(Collectors.toList()));
		});
		List<IssuerPermissionResponse> issuerPermissionList = JSON.parseArray(user.getExtendParams(), IssuerPermissionResponse.class);

		List<PermissionCodeResponse> responseList = new ArrayList<>();
		for (IssuerPermissionResponse issuerPermissionResponse : issuerPermissionList) {
			String issuerCode = issuerPermissionResponse.getIssuerCode();
			Boolean isLeaf = issuerPermissionResponse.getIsLeaf();
			PermissionCodeResponse permissionCodeResponse = new PermissionCodeResponse();
			permissionCodeResponse.setIssuerCode(issuerCode);
			permissionCodeResponse.setHaveIssuer(true);
			if (Boolean.TRUE.equals(isLeaf)) {
				permissionCodeResponse.setCompanyCodeList(companyList.stream().map(Company::getCompanyCode).collect(Collectors.toList()));
				permissionCodeResponse.setOutletCodeList(outletList.stream().map(Outlet::getOutletCode).collect(Collectors.toList()));
				permissionCodeResponse.setMerchentCodeList(merchantList.stream().map(Merchant::getMerchantCode).collect(Collectors.toList()));
			} else {
				List<DataPermissionResponse> dataPermissionList = issuerPermissionResponse.getDataPermissionList();
				makeDatapermissionList(permissionCodeResponse, companyMerchantMap, merchantOutMap, dataPermissionList);
			}
			responseList.add(permissionCodeResponse);

		}
		return responseList;
	}

	private void makeDatapermissionList(PermissionCodeResponse permissionCodeResponse, Map<String, List<String>> companyMerchantMap,
	                                    Map<String, List<String>> merchantOutletMap,
	                                    List<DataPermissionResponse> dataPermissionList) {
		if (CollectionUtils.isEmpty(dataPermissionList)) {
			return;
		}

		List<String> companyCodeList = new ArrayList<>();
		List<String> merchentCodeList = new ArrayList<>();
		List<String> outletCodeList = new ArrayList<>();

		Map<Integer, List<DataPermissionResponse>> permissionMap = dataPermissionList.stream().collect(Collectors.groupingBy(DataPermissionResponse::getType));
		for (Iterator<Integer> iterator = permissionMap.keySet().iterator(); iterator.hasNext();) {
			Integer type = iterator.next();
			List<String> leafCodeList = new ArrayList<>();
			List<DataPermissionResponse> list = permissionMap.get(type);
			List<String> codeList = new ArrayList<>();
			for (DataPermissionResponse dataPermissionResponse : list) {
				Boolean leaf = dataPermissionResponse.getIsLeaf();
				String dataPermissionCode = dataPermissionResponse.getCode();
				codeList.add(dataPermissionCode);
				if (Boolean.TRUE.equals(leaf)) {
					leafCodeList.add(dataPermissionCode);
				}
			}
			switch (type) {
				case 2:
					companyCodeList.addAll(codeList);
					makePermissionByCompany(companyMerchantMap, merchantOutletMap,  merchentCodeList, outletCodeList, leafCodeList);
					break;
				case 3:
					merchentCodeList.addAll(codeList);
					makePermissionByMerchent(merchantOutletMap, outletCodeList, leafCodeList);
					break;
				case 4:
					outletCodeList.addAll(codeList);
					break;
				default:
					break;
			}
		}
		permissionCodeResponse.setCompanyCodeList(companyCodeList);
		permissionCodeResponse.setMerchentCodeList(merchentCodeList);
		permissionCodeResponse.setOutletCodeList(outletCodeList);
	}

	private void makePermissionByMerchent(Map<String, List<String>> merchantOutletMap, List<String> outletCodeList,
	                                      List<String> leafCodeList) {
		if (CollectionUtils.isEmpty(leafCodeList)) {
			return;
		}
		for (String code : leafCodeList) {
			List<String> outletList = merchantOutletMap.get(code);
			if (!CollectionUtils.isEmpty(outletList)) {
				outletCodeList.addAll(outletList);
			}
		}
	}

	private void makePermissionByCompany(Map<String, List<String>> companyMerchantMap, Map<String, List<String>> merchantOutletMap,
	                                     List<String> merchentCodeList, List<String> outletCodeList, List<String> leafCodeList) {
		if (CollectionUtils.isEmpty(leafCodeList)) {
			return;
		}
		for (String code : leafCodeList) {
			List<String> merchantlist = companyMerchantMap.get(code);
			if (CollectionUtils.isEmpty(merchantlist)) {
				continue;
			}
			merchentCodeList.addAll(merchantlist);
			for (String merchantCode : merchantlist) {
				List<String> outletList = merchantOutletMap.get(merchantCode);
				if (!CollectionUtils.isEmpty(outletList)) {
					outletCodeList.addAll(outletList);
				}
			}
		}
	}

	/**
	 * 根据指定的查询参数，查询用户信息列表
	 */
	@Override
	public PageData<UserAccountResponse> queryUserAccountList(GvQueryUserAccountRequest param) {

		int pageSize = ConvertUtils.toInteger(param.getPageSize(), IdmConstants.DEFAULT_PAGE_SIZE_10);
		int pageNum = ConvertUtils.toInteger(param.getPageNum(), 1);

		if (pageSize <= 0 || pageSize > IdmConstants.MAX_PAGE_SIZE_1000) {
			pageSize = IdmConstants.DEFAULT_PAGE_SIZE_10;
		}
		if (pageNum < 1) {
			pageNum = 1;
		}

		PageRowBounds rowBounds = new PageRowBounds((pageNum - 1) * pageSize, pageSize);
		rowBounds.setCount(true);

		UserAccount condition = BeanCopyUtils.jsonCopyBean(param, UserAccount.class);
		this.encrypt(condition);

		GvQueryUserAccountsCondition queryCond = BeanCopyUtils.jsonCopyBean(condition, GvQueryUserAccountsCondition.class);
		queryCond.setRoleCode(param.getRoleCode());
		Page<UserAccount> pageList = this.userAccountMapper.queryUserAccountList(queryCond, rowBounds);

		if (CollectionUtils.isNotEmpty(pageList)) {
			for (UserAccount entity : pageList.getResult()) {
				this.decrypt(entity);
			}
		}

		return new PageData<>(BeanCopyUtils.jsonCopyList(pageList.getResult(), UserAccountResponse.class), pageList.getTotal());
	}

	@Override
	public List<UserAccount> queryUserAccountByRoles(List<String> roleCodeList) {
		if (CollectionUtils.isEmpty(roleCodeList)) {
			return Collections.emptyList();
		}
		PageRowBounds rowBounds = new PageRowBounds(0, 1000);
		GvQueryUserAccountsCondition queryCond = new GvQueryUserAccountsCondition();
		queryCond.setRoleCodeList(roleCodeList);
		queryCond.setTenantCode(GvcoreConstants.SYSTEM_DEFAULT);
		queryCond.setDomainCode(GvcoreConstants.SYSTEM_DEFAULT);
		queryCond.setStatus(GvcoreConstants.STATUS_ENABLE);
		List<UserAccount> userAccountList = userAccountMapper.queryUserAccountList(queryCond, rowBounds);
		if (CollectionUtils.isEmpty(userAccountList)) {
			return Collections.emptyList();
		}
		for (UserAccount userAccount : userAccountList) {
			this.decrypt(userAccount);
		}
		return userAccountList;
	}

	@Override
	public List<UserAccount> queryUserByRolesAndDataPermissions(List<String> roleCodeList, String permissionCode) {
		List<UserAccount> userList = queryUserAccountByRoles(roleCodeList);
		if (CollectionUtils.isEmpty(userList)) {
			return Collections.emptyList();
		}
		if (StringUtil.isEmpty(permissionCode)) {
			return userList;
		}
		List<UserAccount> userAccountList = new CopyOnWriteArrayList<>();
		CountDownLatch countDown = new CountDownLatch(userList.size());
		for (UserAccount userAccount : userList) {
			executor.execute(() -> {
				try {
					for (String permission : permissionCode.split(",")) {
						boolean havePermission = checkHavePermission(permission, userAccount.getUserCode());
						if (Boolean.TRUE.equals(havePermission)) {
							userAccountList.add(userAccount);
						}
					}

				} catch (Exception e) {
					log.error(e.getMessage(), e);
				} finally {
					countDown.countDown();
				}
			});
		}
		try {
			countDown.await();
		} catch (InterruptedException e) {
			log.error(e.getMessage());
			Thread.currentThread().interrupt();
		}
		return userAccountList;
	}

	private boolean checkHavePermission(String permissionCode, String userCode) {
		List<PermissionCodeResponse> permissionList = queryPerrmissionCodeList(userCode);
		for (PermissionCodeResponse permissionCodeResponse : permissionList) {
			String issuerCode = permissionCodeResponse.getIssuerCode();
			Boolean haveIssuer = permissionCodeResponse.getHaveIssuer();
			if (Boolean.TRUE.equals(haveIssuer) && permissionCode.equals(issuerCode)) {
				return true;
			}
			List<String> companyCodeList = permissionCodeResponse.getCompanyCodeList();
			if (!CollectionUtils.isEmpty(companyCodeList) && companyCodeList.contains(permissionCode)) {
				return true;
			}
			List<String> merchentCodeList = permissionCodeResponse.getMerchentCodeList();
			if (!CollectionUtils.isEmpty(merchentCodeList) && merchentCodeList.contains(permissionCode)) {
				return true;
			}
			List<String> outletCodeList = permissionCodeResponse.getMerchentCodeList();
			if (!CollectionUtils.isEmpty(outletCodeList) && outletCodeList.contains(permissionCode)) {
				return true;
			}
		}
		return false;
	}

	@Override
	public List<String> getMenuCodeListByRoles(List<String> roleCodeList) {
		QueryRoleResourcesCondition queryRoleResourcesCondition = new QueryRoleResourcesCondition();
		queryRoleResourcesCondition.setDomainCode(GvcoreConstants.SYSTEM_DEFAULT);
		queryRoleResourcesCondition.setAppCode(GvcoreConstants.APP_CODE);
		queryRoleResourcesCondition.setAccessType(AccessTypeEnum.LOGIN.number());
		List<MenuEntity> menuEntityList = menuMapper.queryResourceMenuList(queryRoleResourcesCondition);
		if (!CollectionUtils.isEmpty(roleCodeList)) {
			for (String roleCode : roleCodeList) {
				queryRoleResourcesCondition = new QueryRoleResourcesCondition();
				queryRoleResourcesCondition.setDomainCode(GvcoreConstants.SYSTEM_DEFAULT);
				queryRoleResourcesCondition.setTenantCode(GvcoreConstants.SYSTEM_DEFAULT);
				queryRoleResourcesCondition.setAppCode(GvcoreConstants.APP_CODE);
				queryRoleResourcesCondition.setRoleCode(roleCode);
				List<MenuEntity> menuList = menuMapper.queryRoleMenuListByRoleCode(queryRoleResourcesCondition);
				menuEntityList.addAll(menuList);
			}
		}
		return menuEntityList.stream().map(MenuEntity::getMenuCode).distinct().collect(Collectors.toList());
	}

	@Override
	public List<UserAccount> queryUserByCodes(List<String> codes, String... fields) {

		if (CollectionUtils.isEmpty(codes)) return Collections.emptyList();

		Example example = new Example(UserAccount.class);
		example.createCriteria().andIn(UserAccount.C_USER_CODE, codes);

		if (ArrayUtils.isNotEmpty(fields)) example.selectProperties(fields);

		List<UserAccount> userAccountList = userAccountMapper.selectByCondition(example);
		if (CollectionUtils.isEmpty(userAccountList)) return Collections.emptyList();

		userAccountList.forEach(this::decrypt);

		return userAccountList;
	}
}
