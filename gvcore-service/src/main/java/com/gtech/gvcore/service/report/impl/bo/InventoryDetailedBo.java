package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.DateUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/20 14:25
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class InventoryDetailedBo {

    private String outletCode;

    private String issuerCode;

    private String cpgCode;

    private String bookletNumber;

    private String cardNumber;

    private String voucherEffectiveDate;

    private Integer status;

    private Integer voucherStatus;

    private Date createTime;

    private String bookletStatus;

    private BigDecimal denomination;


    public Date getVoucherEffectiveDate() {

        return DateUtil.parseDate(voucherEffectiveDate, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

}
