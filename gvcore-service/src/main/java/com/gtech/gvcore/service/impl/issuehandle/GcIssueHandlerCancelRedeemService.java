package com.gtech.gvcore.service.impl.issuehandle;

import com.gtech.gvcore.common.enums.IssueHandlingProcessStatusEnum;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.common.enums.RedemptionStatusEnum;
import com.gtech.gvcore.common.request.issuehandling.GetIssueHandlingRequest;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.giftcard.domain.model.GcCardStatus;
import com.gtech.gvcore.giftcard.domain.model.GcRedemptionRecord;
import com.gtech.gvcore.giftcard.infrastructure.entity.GcRedemptionEntity;
import com.gtech.gvcore.giftcard.infrastructure.entity.GiftCardEntity;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GcRedemptionMapper;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.IssueHandlerBaseService;
import com.gtech.gvcore.service.IssueHandlingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GcIssueHandlerCancelRedeemService extends GcIssueHandlerValidateService implements IssueHandlerBaseService {

    @Autowired
    GvCodeHelper gcCodeHelper;

    @Autowired
    IssueHandlingService issueHandlingService;

    @Autowired
    GcRedemptionMapper gcRedemptionMapper;

    @Override
    public IssueHandlingTypeEnum getIssueHandlingType() {
        return IssueHandlingTypeEnum.GC_CANCEL_REDEEM;
    }

    public void checkOriginalRecord(List<IssueHandlingDetails> details) {
        for (IssueHandlingDetails x : details) {
            if (x.getInvoiceNo() == null) {
                x.setResult("Invoice number is empty");
                x.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
                continue;
            }
            if (x.getApprovalCode() == null) {
                x.setResult("Approval code is empty");
                x.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
                continue;
            }
            GcRedemptionRecord gcRedemptionRecord = redemptionDomainService.getRedemption(x.getVoucherCode(), x.getInvoiceNo(), x.getApprovalCode());
            if (gcRedemptionRecord == null) {
                x.setResult("Original record doesn't exist");
                x.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
            } else if (gcRedemptionRecord.getRedemptionCanceled() == 1) {
                x.setResult("Redemption already canceled");
                x.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
            }
        }
    }

    @Override
    public List<IssueHandlingDetails> validate(List<IssueHandlingDetails> details, String issuerCode) {
        check(details, issuerCode);
        return details;
    }

    @Override
    public List<IssueHandlingDetails> execute(List<IssueHandlingDetails> details, String issuerCode) {

        List<IssueHandlingDetails> check = check(details, issuerCode);

        List<IssueHandlingDetails> successVoucherCodes = check.stream()
                .filter(detail -> IssueHandlingProcessStatusEnum.SUCCESS.code().equals(detail.getProcessStatus()))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(successVoucherCodes)) {
            successVoucherCodes.forEach(this::performAction);
        }
        return check;
    }

    private List<IssueHandlingDetails> check(List<IssueHandlingDetails> details, String issuerCode) {

        if (CollectionUtils.isEmpty(details)) {
            return Collections.emptyList();
        }
        checkIfExist(details, getIssueHandlingType(), issuerCode);
        checkOriginalRecord(details);
        checkOutletName(details);
        return details;
    }

    /**
     * 执行取消兑换操作
     * 使用事务确保数据一致性，包含异常处理和并发控制
     */
    @Transactional(rollbackFor = Exception.class)
    private int performAction(IssueHandlingDetails handlingDetails) {
        try {
            log.info("开始执行取消兑换操作: 卡号={}, 发票号={}, 审批码={}",
                    handlingDetails.getVoucherCode(), handlingDetails.getInvoiceNo(), handlingDetails.getApprovalCode());

            // 1. 获取当前礼品卡信息
            GiftCardEntity balanceCheckEntity = new GiftCardEntity();
            balanceCheckEntity.setCardNumber(handlingDetails.getVoucherCode());
            balanceCheckEntity = giftCardMapper.selectOne(balanceCheckEntity);

            if (balanceCheckEntity == null) {
                log.error("礼品卡不存在: {}", handlingDetails.getVoucherCode());
                handlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
                handlingDetails.setResult("Gift card not found");
                return 0;
            }

            BigDecimal currentBalance = balanceCheckEntity.getBalance();

            // 2. 获取原始兑换记录
            GcRedemptionRecord gcRedemptionRecord = redemptionDomainService.getRedemption(
                    handlingDetails.getVoucherCode(),
                    handlingDetails.getInvoiceNo(),
                    handlingDetails.getApprovalCode());

            if (gcRedemptionRecord == null) {
                log.error("原始兑换记录不存在: 卡号={}, 发票号={}, 审批码={}",
                        handlingDetails.getVoucherCode(), handlingDetails.getInvoiceNo(), handlingDetails.getApprovalCode());
                handlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
                handlingDetails.setResult("Original redemption record not found");
                return 0;
            }

            // 3. 更新礼品卡余额和状态
            GiftCardEntity giftCard = new GiftCardEntity();
            giftCard.setBalance(gcRedemptionRecord.getAmount());
            giftCard.setCardNumber(handlingDetails.getVoucherCode());

            // 如果当前状态是零余额，恢复为已激活状态
            if (GcCardStatus.ZERO_BALANCE.name().equals(balanceCheckEntity.getStatus())) {
                giftCard.setStatus(GcCardStatus.ACTIVATED.name());
            }

            int updateResult = giftCardMapper.updateBalanceByCardNumber(giftCard);
            if (updateResult > 0) {
                log.info("礼品卡余额更新成功: 卡号={}, 恢复金额={}", handlingDetails.getVoucherCode(), gcRedemptionRecord.getAmount());

                // 4. 创建取消兑换记录
                Date createTime = new Date();
                GcRedemptionEntity gcRedemptionEntity = new GcRedemptionEntity();
                gcRedemptionEntity.setRedemptionCode(gcCodeHelper.generateRedemptionCode());
                gcRedemptionEntity.setOriginalRedemptionCode(gcRedemptionRecord.getRedemptionCode());
                gcRedemptionEntity.setCardNumber(handlingDetails.getVoucherCode());

                // 获取网点信息
                OutletResponse outletByOutletName = null;
                if (StringUtils.isNotEmpty(handlingDetails.getOutletName())) {
                    try {
                        outletByOutletName = outletService.getOutletByOutletName(handlingDetails.getOutletName());
                    } catch (Exception e) {
                        log.warn("获取网点信息失败: {}, 错误: {}", handlingDetails.getOutletName(), e.getMessage());
                    }
                }

                if (outletByOutletName != null) {
                    gcRedemptionEntity.setOutletCode(outletByOutletName.getOutletCode());
                    gcRedemptionEntity.setMerchantCode(outletByOutletName.getMerchantCode());
                }

                gcRedemptionEntity.setIssuerCode(balanceCheckEntity.getIssuerCode());
                gcRedemptionEntity.setBalanceBefore(currentBalance);
                gcRedemptionEntity.setBalanceAfter(currentBalance.add(gcRedemptionRecord.getAmount()));
                gcRedemptionEntity.setAmount(gcRedemptionRecord.getAmount());
                gcRedemptionEntity.setCpgCode(balanceCheckEntity.getCpgCode());
                gcRedemptionEntity.setInvoiceNumber(handlingDetails.getInvoiceNo());
                gcRedemptionEntity.setRedemptionTime(createTime);
                gcRedemptionEntity.setTransactionType(RedemptionStatusEnum.CANCELLED_REDEEMED.getCode());
                gcRedemptionEntity.setDenomination(balanceCheckEntity.getDenomination());

                // 获取备注信息
                try {
                    String remarks = issueHandlingService.getIssueHandling(GetIssueHandlingRequest.builder()
                            .issueHandlingCode(handlingDetails.getIssueHandlingCode()).build())
                            .getData().getRemarks();
                    gcRedemptionEntity.setNotes(remarks);
                } catch (Exception e) {
                    log.warn("获取Issue Handling备注失败: {}, 错误: {}", handlingDetails.getIssueHandlingCode(), e.getMessage());
                    gcRedemptionEntity.setNotes("Cancel redemption via issue handling");
                }

                String approveCode = gcCodeHelper.generateApproveCode();
                gcRedemptionEntity.setApprovalCode(approveCode);
                gcRedemptionMapper.insertSelective(gcRedemptionEntity);
                // 5. 标记原始兑换记录为已取消（使用乐观锁防止并发问题）
                WeekendSqls<GcRedemptionEntity> weekendSqls = WeekendSqls.custom();
                weekendSqls.andEqualTo(GcRedemptionEntity::getInvoiceNumber, handlingDetails.getInvoiceNo())
                        .andEqualTo(GcRedemptionEntity::getRedemptionCanceled, 0)
                        .andEqualTo(GcRedemptionEntity::getTransactionType, RedemptionStatusEnum.REDEEMED.getCode())
                        .andEqualTo(GcRedemptionEntity::getCardNumber, handlingDetails.getVoucherCode())
                        .andEqualTo(GcRedemptionEntity::getApprovalCode, handlingDetails.getApprovalCode());

                Example example = Example.builder(GcRedemptionEntity.class).where(weekendSqls).build();
                GcRedemptionEntity updEntity = new GcRedemptionEntity();
                updEntity.setRedemptionCanceled(1);
                updEntity.setUpdateTime(new Date());

                int cancelResult = gcRedemptionMapper.updateByConditionSelective(updEntity, example);

                if (cancelResult > 0) {
                    log.info("取消兑换操作成功: 卡号={}, 发票号={}, 审批码={}",
                            handlingDetails.getVoucherCode(), handlingDetails.getInvoiceNo(), handlingDetails.getApprovalCode());
                    handlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.SUCCESS.code());
                    handlingDetails.setResult("Cancel redemption successful");
                } else {
                    log.warn("标记原始兑换记录失败，可能已被其他操作修改: 卡号={}, 发票号={}",
                            handlingDetails.getVoucherCode(), handlingDetails.getInvoiceNo());
                    handlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
                    handlingDetails.setResult("Original redemption record already modified");
                    // 事务会回滚，恢复礼品卡状态
                    throw new RuntimeException("Concurrent modification detected");
                }

                return updateResult;
            } else {
                log.error("礼品卡余额更新失败: 卡号={}", handlingDetails.getVoucherCode());
                handlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
                handlingDetails.setResult("Failed to update gift card balance");
                return 0;
            }

        } catch (Exception e) {
            log.error("取消兑换操作异常: 卡号={}, 错误: {}", handlingDetails.getVoucherCode(), e.getMessage(), e);
            handlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
            handlingDetails.setResult("Cancel redemption failed: " + e.getMessage());
            throw e; // 重新抛出异常以触发事务回滚
        }
    }
}


