package com.gtech.gvcore.service.report.impl;

import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.helper.TableHelper;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.export.snapshoot.ReportDateContext;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import com.gtech.gvcore.service.report.extend.context.ReportContextBuilder;
import com.gtech.gvcore.service.report.impl.bean.CardLifeCycleBean;
import com.gtech.gvcore.service.report.impl.param.VoucherLifeCycleQueryData;
import com.gtech.gvcore.service.report.impl.support.life.VoucherLifeCycle;
import com.gtech.gvcore.service.report.impl.support.life.excel.VoucherLifeCycleFileContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.EnumMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022年4月26日
 */
@Slf4j
@Service
public class VoucherLifeCycleImpl extends ReportSupport
        implements BusinessReport<VoucherLifeCycleQueryData, CardLifeCycleBean>, SingleReport {

    @Autowired
    private List<VoucherLifeCycle> voucherLifeCycleList;

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.CARD_LIFE_CYCLE_REPORT;
    }

    @Override
    public void customContext(ReportContextBuilder builder) {

        builder.bindFileContext(new VoucherLifeCycleFileContext());
    }

    @Override
    public VoucherLifeCycleQueryData builderQueryParam(CreateReportRequest reportParam) {
        VoucherLifeCycleQueryData reportBasicQueryData = new VoucherLifeCycleQueryData();

        reportBasicQueryData.setVoucherCodeNumStart(reportParam.getVoucherCodeNumStart());
        reportBasicQueryData.setVoucherCodeNumEnd(reportParam.getVoucherCodeNumEnd());

        return reportBasicQueryData;
    }

    @Override
    public List<CardLifeCycleBean> getExportData(VoucherLifeCycleQueryData param) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void builder(ReportContext context) {

        //data
        VoucherLifeCycleFileContext excelContext = context.getFileContext(VoucherLifeCycleFileContext.class);
        ReportDateContext reportDateContext = context.getReportDateContext(ReportDateContext.class);

        //param
        VoucherLifeCycleQueryData queryParam = (VoucherLifeCycleQueryData) context.getQueryParam();
        queryParam.setPageNum(1);

        //no data
        if (!isExist(queryParam)) {
            context.noData();
            return;
        }

        //builder
        Map<ReportExportTypeEnum, List<?>> reportData = new EnumMap<>(ReportExportTypeEnum.class);
        voucherLifeCycleList.forEach(e -> e.builder(excelContext, reportData, queryParam));

        //fast save
        reportDateContext.fastSaveAll(reportData);

    }

    private boolean isExist(final VoucherLifeCycleQueryData queryParam) {
        //结束不存在值
        final Long voucherCodeNumStart = queryParam.getVoucherCodeNumStart();
        final Long voucherCodeNumEnd = queryParam.getVoucherCodeNumEnd();

        if (null == voucherCodeNumStart && null == voucherCodeNumEnd) return false;

        if (null == voucherCodeNumEnd) {
            return reportBusinessMapper.voucherLifeCycleEexistVoucher(voucherCodeNumStart, TableHelper.getTableIndex(voucherCodeNumStart)) != null;
        }

        if (null == voucherCodeNumStart) {
            return reportBusinessMapper.voucherLifeCycleEexistVoucher(voucherCodeNumEnd, TableHelper.getTableIndex(voucherCodeNumEnd)) != null;
        }
        //都存在值
        for (long i = voucherCodeNumStart; i <= voucherCodeNumEnd; i++) {

            if (reportBusinessMapper.voucherLifeCycleEexistVoucher(voucherCodeNumEnd, TableHelper.getTableIndex(voucherCodeNumEnd)) != null) return true;
        }

        return false;
    }


    @Override
    public Map<ReportExportTypeEnum, Class<?>> getResultDateType() {

        return new EnumMap<>(ReportExportTypeEnum.class);
    }

}


