package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/20 17:21
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class CardLifeCycleTransactionBean {

    @ExcelProperty(index = 0, value = "Merchant")
    private String merchantName;

    @ExcelProperty(index = 1, value = "Outlet Name")
    private String outletName;

    @ExcelProperty(index = 2, value = "Outlet Code")
    private String outletCode;

    @ExcelProperty(index = 1, value = "Voucher Number")
    private String voucherNumber;

    @ExcelProperty(index = 3, value = "Invoice Number")
    private String invoiceNumber;

    @ExcelProperty(index = 4, value = "Currency")
    private String baseCurrencyOfTheVoucher;

    @ReportAmountValue
    @ExcelProperty(index = 5, value = "Transaction Amount", converter = ExportExcelNumberConverter.class)
    private String transactionAmount;

    @ExcelProperty(index = 6, value = "Transaction Date")
    private String transactionDate;

    @ExcelProperty(index = 7, value = "Transaction Time Zone")
    private String transactionTimeZone;

    @ExcelProperty(index = 8, value = "Transaction Type")
    private String transactionType;

    @ExcelProperty(index = 9, value = "ResponseMessage")
    private String responseMessage;

    @ExcelProperty(index = 10, value = "Expiry Date")
    private String expiryDate;

    @ExcelProperty(index = 11, value = "Expiry Time Zone")
    private String expiryTimeZone;
}
