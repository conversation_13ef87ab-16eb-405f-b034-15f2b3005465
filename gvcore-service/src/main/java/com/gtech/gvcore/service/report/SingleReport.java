package com.gtech.gvcore.service.report;

import com.gtech.gvcore.service.report.extend.context.ReportContext;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * @ClassName SingleReport
 *     明确标识报表不分页逻辑
 *     非分页报表往往涉及较大资源消耗因此后续可能需要根据该接口进行限制
 * @Description 不分页报表接口
 * <AUTHOR>
 * @Date 2023/1/5 10:54
 * @Version V1.0
 **/
public interface SingleReport {

    /**
     * 构建报表
     * @param context
     */
    default void builder(ReportContext context) {

        final BusinessReport<ReportQueryParam, ?> reportImpl = context.getBusinessReport();
        final ReportQueryParam reportParam = context.getQueryParam();

        LoggerFactory.getLogger(SingleReport.class).info("builderReportByContextFindSingleReport executing. orderReportCode:{}", context.getReportCode());

        //find
        final List<?> dataList = reportImpl.getExportData(reportParam);

        //no data
        if (CollectionUtils.isEmpty(dataList)) context.noData();

        // add data
        context.appendDate(dataList);

        this.dataFinish(context);
    }

    default void dataFinish (ReportContext context) {}
}
