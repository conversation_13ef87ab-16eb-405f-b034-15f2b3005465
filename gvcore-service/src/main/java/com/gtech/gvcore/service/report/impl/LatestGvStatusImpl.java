package com.gtech.gvcore.service.report.impl;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.ReportVoucherStatusEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.enums.VoucherOwnerTypeEnum;
import com.gtech.gvcore.common.enums.VoucherStatusEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Company;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.helper.GvPageHelper;
import com.gtech.gvcore.service.TransactionDataService;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.PollReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.LatestGvStatusBean;
import com.gtech.gvcore.service.report.impl.param.LatestGvStatusQueryData;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description
 * @date 2022年6月21日
 */
@Slf4j
@Service
public class LatestGvStatusImpl extends ReportSupport
        implements BusinessReport<LatestGvStatusQueryData, LatestGvStatusBean>, PollReport {

    @Autowired private TransactionDataService transactionDataService;

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.LATEST_GV_STATUS_REPORT;
    }


    @Override
    public LatestGvStatusQueryData builderQueryParam(CreateReportRequest reportParam) {

        LatestGvStatusQueryData latestGvStatusQueryData = new LatestGvStatusQueryData();
        latestGvStatusQueryData.setCpgCodeList(reportParam.getCpgCodes());
        latestGvStatusQueryData.setVoucherCodeNumStart(reportParam.getVoucherCodeNumStart());
        latestGvStatusQueryData.setVoucherCodeNumEnd(reportParam.getVoucherCodeNumEnd());
        latestGvStatusQueryData.setVoucherStatusList(reportParam.getVoucherStatus());

		// List<String> voucherCodeList = getUploadVoucherCode(reportParam); 改为创建时解析完保存
		latestGvStatusQueryData.setVoucherCodeList(reportParam.getVoucherCodeList());

        return latestGvStatusQueryData;
    }

    private static List<String> getUploadVoucherCode(CreateReportRequest reportParam) {

        final List<String> voucherCodeList = new ArrayList<>();
        final String uploadedFileUrl = reportParam.getUploadedFileUrl();

        if (StringUtil.isBlank(uploadedFileUrl)) return voucherCodeList;

        if (uploadedFileUrl.endsWith(".csv")) getUploadVoucherCodeByCSV(voucherCodeList, uploadedFileUrl);
        else getUploadVoucherCodeByEXCEL(voucherCodeList, uploadedFileUrl);

        return voucherCodeList;
    }

    private static void getUploadVoucherCodeByEXCEL(List<String> voucherCodeList, String uploadedFileUrl) {

        try(InputStream is = new URL(uploadedFileUrl).openStream();

            XSSFWorkbook workbook = new XSSFWorkbook(is)) {
            XSSFSheet sheet = workbook.getSheetAt(0);
            int rows = sheet.getPhysicalNumberOfRows();
            XSSFRow row = sheet.getRow(0);
            int cells = row.getPhysicalNumberOfCells();

            if (cells > 1) throw new GTechBaseException(ResultErrorCodeEnum.UPLOADED_FILE_MORE_COLUMNS.code(), ResultErrorCodeEnum.UPLOADED_FILE_MORE_COLUMNS.desc());

            for (int i = 1; i < rows; i++) {
                row = sheet.getRow(i);
                voucherCodeList.add(row.getCell(0).getStringCellValue());
            }

        } catch (IOException e) {
            log.warn(ResultErrorCodeEnum.UPLOADED_FILE_NOT_MEET_STANDARDS.desc() + e.getMessage(), e);
        }
    }

    private static void getUploadVoucherCodeByCSV(List<String> voucherCodeList, String uploadedFileUrl) {

        String[] header = new String[0];

        try (InputStream is = new URL(uploadedFileUrl).openStream();
             InputStreamReader isr = new InputStreamReader(is, StandardCharsets.UTF_8);
             BufferedReader br = new BufferedReader(isr);
             CSVParser csvParser = CSVFormat.DEFAULT.builder().setAllowDuplicateHeaderNames(false).setHeader(header).build().parse(br)) {

            for (CSVRecord csvRecord : csvParser) {

                int recordSize = csvRecord.size();

                if (recordSize > 1) throw new GTechBaseException(ResultErrorCodeEnum.UPLOADED_FILE_MORE_COLUMNS.code(), ResultErrorCodeEnum.UPLOADED_FILE_MORE_COLUMNS.desc());

                voucherCodeList.add(csvRecord.get(0));
            }

        } catch (IOException e) {
            log.warn(ResultErrorCodeEnum.UPLOADED_FILE_NOT_MEET_STANDARDS.desc() + e.getMessage(), e);
        }
    }


    @Override
    public List<LatestGvStatusBean> getExportData(LatestGvStatusQueryData queryData) {

        List<Voucher> voucherList = reportBusinessMapper.latestGvStatusReport(queryData, GvPageHelper.getRowBounds(queryData));
        if (CollectionUtils.isEmpty(voucherList)) return Collections.emptyList();

        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(voucherList, Voucher::getCpgCode, Cpg.class);
        final JoinDataMap<TransactionData> lastStatusTransactionMap = this.lastStatusTransaction(voucherList);
        final JoinDataMap<TransactionData> sellTransactionMap = this.sellTransaction(voucherList);

        final List<LatestGvStatusBean> result = voucherList.stream().map(voucher -> {

            final TransactionData lastTransaction = lastStatusTransactionMap.findValue(voucher.getVoucherCode());
            final TransactionData sellTransaction = sellTransactionMap.findValue(voucher.getVoucherCode());
            final ReportVoucherStatusEnum voucherStatus = this.getVoucherStatus(voucher);

            return new LatestGvStatusBean()
                    .setVoucherNumber(voucher.getVoucherCode())
                    .setBookletNumber(voucher.getBookletCode())
                    .setExpiryDate(DateUtil.format(voucher.getVoucherEffectiveDate(), DateUtil.FORMAT_YYYYMMDDHHMISS))
                    .setVoucherAmount(super.toAmount(voucher.getDenomination()))
                    .setVoucherProgramGroup(cpgMap.findValue(voucher.getCpgCode()).getCpgName())
                    .setOutletName(lastTransaction.getOutletCode())
                    .setLastActionDate(DateUtil.format(lastTransaction.getTransactionDate(), DateUtil.FORMAT_YYYYMMDDHHMISS))
                    .setSellingDate(DateUtil.format(sellTransaction.getTransactionDate(), DateUtil.FORMAT_YYYYMMDDHHMISS))
                    .setVoucherStatus(voucherStatus != null ? voucherStatus.getDesc() : StringUtils.EMPTY)
                    .setTransactionType(getTransactionTypeEnum(voucherStatus));
        }).collect(Collectors.toList());


        // 根据结关联 merchant, outlet, company
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(result, LatestGvStatusBean::getOutletName, Outlet.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(outletMap.values(), Outlet::getMerchantCode, Merchant.class);
        final JoinDataMap<Company> companyMap = super.getMapByCode(merchantMap.values(), Merchant::getCompanyCode, Company.class);

        // 根据结关联 merchant, outlet, company
        return result.stream()
                .map(e -> {

                    final Outlet outlet = outletMap.findValue(e.getOutletName());
                    final Merchant merchant = merchantMap.findValue(outlet.getMerchantCode());
                    final Company company = companyMap.findValue(merchant.getCompanyCode());

                    return e.setMerchant(merchant.getMerchantName())
                            .setOutletName(outlet.getOutletName())
                            .setClientName(company.getCompanyName());

                }).collect(Collectors.toList());
    }

    private JoinDataMap<TransactionData> lastStatusTransaction (final List<Voucher> vouchers) {

        final JoinDataMap<TransactionData> joinDataMap = new JoinDataMap<>(new TransactionData());

        joinDataMap.putAll(
                transactionDataService.selectLastTransactionDataByVoucherCodesAndType(
                        vouchers.stream()
                                .filter(e -> VoucherStatusEnum.VOUCHER_USED.equalsCode(e.getStatus()))
                                .map(Voucher::getVoucherCode)
                                .collect(Collectors.toList())
                        , TransactionTypeEnum.GIFT_CARD_REDEEM)
        );

        joinDataMap.putAll(
                transactionDataService.selectLastTransactionDataByVoucherCodesAndType(
                        vouchers.stream()
                                .filter(e -> VoucherStatusEnum.VOUCHER_ACTIVATED.equalsCode(e.getStatus()))
                                .map(Voucher::getVoucherCode)
                                .collect(Collectors.toList())
                        , TransactionTypeEnum.GIFT_CARD_ACTIVATE, TransactionTypeEnum.GIFT_CARD_ACTIVATE_ONLY)
        );

        joinDataMap.putAll(
                transactionDataService.selectLastTransactionDataByVoucherCodesAndType(
                        vouchers.stream()
                                .filter(e -> GvcoreConstants.MOP_CODE_VCE.equals(e.getMopCode()))
                                .filter(e -> VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.equalsCode(e.getStatus()))
                                .map(Voucher::getVoucherCode)
                                .collect(Collectors.toList())
                        , TransactionTypeEnum.GIFT_CARD_NEW_GENERATE)
        );

        joinDataMap.putAll(
                transactionDataService.selectLastTransactionDataByVoucherCodesAndType(
                        vouchers.stream()
                                .filter(e -> GvcoreConstants.MOP_CODE_VCR.equals(e.getMopCode()))
                                .filter(e -> VoucherOwnerTypeEnum.CUSTOMER.equalsCode(e.getVoucherOwnerType()))
                                .filter(e -> VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.equalsCode(e.getStatus()))
                                .map(Voucher::getVoucherCode)
                                .collect(Collectors.toList())
                        , TransactionTypeEnum.GIFT_CARD_SELL)
        );

        joinDataMap.putAll(
                vouchers.stream()
                        .filter(e -> GvcoreConstants.MOP_CODE_VCR.equals(e.getMopCode()))
                        .filter(e -> !VoucherOwnerTypeEnum.CUSTOMER.equalsCode(e.getVoucherOwnerType()))
                        .filter(e -> VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.equalsCode(e.getStatus()))
                        .collect(Collectors.toMap(Voucher::getVoucherCode, e -> TransactionData.builder().outletCode(e.getVoucherOwnerCode()).transactionDate(e.getCreateTime()).build()))
        );


        return joinDataMap;
    }

    private JoinDataMap<TransactionData> sellTransaction (final List<Voucher> vouchers) {

        final JoinDataMap<TransactionData> joinDataMap = new JoinDataMap<>(new TransactionData());

        joinDataMap.putAll(
                transactionDataService.selectLastTransactionDataByVoucherCodesAndType(
                                vouchers.stream()
                                        .filter(e -> VoucherStatusEnum.VOUCHER_USED.equalsCode(e.getStatus()) || VoucherStatusEnum.VOUCHER_ACTIVATED.equalsCode(e.getStatus()))
                                        .map(Voucher::getVoucherCode)
                                        .collect(Collectors.toList())
                                , TransactionTypeEnum.GIFT_CARD_SELL, TransactionTypeEnum.GIFT_CARD_CANCEL_SELL)
                        .values().stream().filter(e -> TransactionTypeEnum.GIFT_CARD_SELL.equalsCode(e.getTransactionType()))
                        .collect(Collectors.toMap(TransactionData::getVoucherCode, Function.identity()))
        );

        return joinDataMap;
    }

    public String getTransactionTypeEnum(ReportVoucherStatusEnum voucherStatusEnum) {

        TransactionTypeEnum transactionTypeEnum;
        if (ReportVoucherStatusEnum.VOUCHER_NEWLY_GENERATED == voucherStatusEnum) transactionTypeEnum = TransactionTypeEnum.GIFT_CARD_NEW_GENERATE;
        else if (ReportVoucherStatusEnum.VOUCHER_ACTIVATED == voucherStatusEnum) transactionTypeEnum = TransactionTypeEnum.GIFT_CARD_ACTIVATE;
        else if (ReportVoucherStatusEnum.VOUCHER_REDEEMED == voucherStatusEnum) transactionTypeEnum = TransactionTypeEnum.GIFT_CARD_REDEEM;
        else if (ReportVoucherStatusEnum.VOUCHER_EXPIRED == voucherStatusEnum) transactionTypeEnum = TransactionTypeEnum.GIFT_CARD_EXPIRY;
        else if (ReportVoucherStatusEnum.VOUCHER_DEACTIVATED == voucherStatusEnum) transactionTypeEnum = TransactionTypeEnum.GIFT_CARD_DEACTIVATE;
        else if (ReportVoucherStatusEnum.VOUCHER_PURCHASED == voucherStatusEnum) transactionTypeEnum = TransactionTypeEnum.GIFT_CARD_NEW_GENERATE;
        else transactionTypeEnum = null;

        return (Objects.isNull(transactionTypeEnum)) ? StringUtils.EMPTY : transactionTypeEnum.getDesc();
    }

}
