package com.gtech.gvcore.service.report.impl.support;

import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bo.GcCancelSalesBo;
import com.gtech.gvcore.service.report.impl.param.GcCancelSalesQueryData;

import java.util.*;

/**
 * @ClassName CancelSalesBaseImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/4/14 17:49
 * @Version V1.0
 **/
public abstract class GcCancelSalesBaseImpl<T> extends ReportSupport implements BusinessReport<GcCancelSalesQueryData, T> {

    @Override
    public GcCancelSalesQueryData builderQueryParam(CreateReportRequest reportParam) {

        GcCancelSalesQueryData param = new GcCancelSalesQueryData();

        //transaction
        param.setTransactionDateEnd(reportParam.getTransactionDateEnd());
        param.setTransactionDateStart(reportParam.getTransactionDateStart());

        // issuer merchant outlet
        param.setIssuerCode(reportParam.getIssuerCode());
        param.setMerchantCodeList(reportParam.getMerchantCodes());
        param.setOutletCodeList(reportParam.getOutletCodes());

        //cpg
        param.setCpgCodeList(reportParam.getCpgCodes());

        //invoice number
        param.setInvoiceNumber(reportParam.getInvoiceNo());

        //customer
        param.setCustomerCodeList(reportParam.getCustomerCodes());

        param.setPurchaseOrderNo(reportParam.getPurchaseOrderNo());
        if (reportParam.getVoucherCode() != null) {
            param.setVoucherCode(Arrays.asList(reportParam.getVoucherCode().split(",")));
        }
        return param;
    }

    public List<GcCancelSalesBo> getBoList(GcCancelSalesQueryData paramBean) {
        List<GcCancelSalesBo> list = new ArrayList<>();
        PollPageHelper.pollSelect(gcReportBusinessMapper::selectCancelSales, paramBean, list::addAll);
        return list;
    }

}
    