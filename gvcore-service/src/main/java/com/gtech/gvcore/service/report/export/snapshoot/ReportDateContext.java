package com.gtech.gvcore.service.report.export.snapshoot;

import com.gtech.gvcore.common.enums.ReportExportTypeEnum;

import java.util.List;
import java.util.Map;

/**
 * @ClassName ReportDateContext
 * @Description report context
 * <AUTHOR>
 * @Date 2022/10/28 14:33
 * @Version V1.0
 **/
public interface ReportDateContext {

    default void init() {}

    /**
     * 结束
     */
    default void finish() {}

    /**
     * 错误处理
     */
    default void error() {}

    /**
     * 添加数据
     * @param data
     */
    default void appendData(List<?> data) {}


    default void fastSaveAll(Map<ReportExportTypeEnum, List<?>> reportData) {}
}
