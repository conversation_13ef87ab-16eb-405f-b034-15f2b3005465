package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.cpg.CreateCpgRequest;
import com.gtech.gvcore.common.request.cpg.GetCpgRequest;
import com.gtech.gvcore.common.request.cpg.QueryCpgVoucherInventory;
import com.gtech.gvcore.common.request.cpg.QueryCpgsByPageRequest;
import com.gtech.gvcore.common.request.cpg.UpdateCpgRequest;
import com.gtech.gvcore.common.request.cpg.UpdateCpgStatusRequest;
import com.gtech.gvcore.common.response.cpg.GetCpgResponse;
import com.gtech.gvcore.common.response.cpg.QueryCpgByOutletCodeResponse;
import com.gtech.gvcore.common.response.cpg.QueryCpgVoucherInventoryResponse;
import com.gtech.gvcore.common.response.cpg.QueryCpgsByPageResponse;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Voucher;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022年2月16日
 */
public interface CpgService {

    /**
     * @param request
     * @return
     * <AUTHOR>
     * @date 2022年2月21日
     */
    Result<Long> createCpg(CreateCpgRequest request);

    /**
     * @param request
     * @return
     * <AUTHOR>
     * @date 2022年2月21日
     */
    Result<String> updateCpg(UpdateCpgRequest request);

    /**
     * @param request
     * @return
     * <AUTHOR>
     * @date 2022年2月21日
     */
    Result<String> updateCpgStatus(UpdateCpgStatusRequest request);

    /**
     * @param request
     * @return
     * <AUTHOR>
     * @date 2022年2月22日
     */
    PageResult<QueryCpgsByPageResponse> queryCpgsByPage(QueryCpgsByPageRequest request);
    List<Cpg> queryCpgAll();

    /**
     * @param cpgCodeList
     * @return
     * <AUTHOR>
     * @date 2022年2月24日
     */
    Map<String, Cpg> queryCpgMapByCpgCodeList(List<String> cpgCodeList);

    Map<String, GetCpgResponse> queryCpgResponseMapByCpgCodeList(List<String> cpgCodeList);

    /**
     * @param request
     * @return
     * <AUTHOR>
     * @date 2022年3月7日
     */
    Result<GetCpgResponse> getCpg(GetCpgRequest request);

    /**
     * @param outletCode
     * @return
     * <AUTHOR>
     */
    Result<List<QueryCpgByOutletCodeResponse>> queryOutletDenomination(String outletCode);

    /**
     * 
     * @param cpgCodeList
     * @return
     * <AUTHOR>
     * @date 2022年4月28日
     */
    List<String> queryAutomaticActivateCpg(List<String> cpgCodeList, String issuerCode);

    Result<GetCpgResponse> getCpgByCpgName(String cpgName);

    Result<QueryCpgVoucherInventoryResponse> queryCpgVoucherInventory(QueryCpgVoucherInventory queryCpgVoucherInventory);

    List<Voucher> getVoucherInventory(QueryCpgVoucherInventory queryCpgVoucherInventory);
    Integer queryInventoryCount(QueryCpgVoucherInventory inventory);
    Cpg getCpgByCode(String cpgCode);
}
