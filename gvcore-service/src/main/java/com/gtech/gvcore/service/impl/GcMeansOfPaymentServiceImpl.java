package com.gtech.gvcore.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.gtech.basic.masterdata.web.entity.MasterDataDdLangEntity;
import com.gtech.basic.masterdata.web.service.MasterDataDdLangService;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.meansofpayment.CreateMeansOfPaymentRequest;
import com.gtech.gvcore.common.request.meansofpayment.QueryMeansOfPaymentsByPageRequest;
import com.gtech.gvcore.common.request.meansofpayment.UpdateMeansOfPaymentRequest;
import com.gtech.gvcore.common.request.meansofpayment.UpdateMeansOfPaymentStatusRequest;
import com.gtech.gvcore.common.response.meansofpayment.OutletInfo;
import com.gtech.gvcore.common.response.meansofpayment.QueryMeansOfPaymentsByPageResponse;
import com.gtech.gvcore.common.utils.UUIDUtils;
import com.gtech.gvcore.dao.dto.GcMeansOfPaymentDto;
import com.gtech.gvcore.dao.dto.MeansOfPaymentDto;
import com.gtech.gvcore.dao.dto.OutletDto;
import com.gtech.gvcore.dao.dto.OutletIssuerNameInfo;
import com.gtech.gvcore.dao.mapper.GcMeansOfPaymentMapper;
import com.gtech.gvcore.dao.model.GcMeansOfPayment;
import com.gtech.gvcore.dao.model.MeansOfPayment;
import com.gtech.gvcore.dao.model.MeansOfPaymentOutlet;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.service.GcMeansOfPaymentService;
import com.gtech.gvcore.service.MeansOfPaymentOutletService;
import com.gtech.gvcore.service.OutletService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class GcMeansOfPaymentServiceImpl implements GcMeansOfPaymentService {

    @Value("${gv.masterdata.ddcode.mopgroup.grp:MOP_GROUP_GRP}")
    private String ddCodeMopGroupGrp;

    @Value("${gv.masterdata.ddcode.mopgroup.externalpaymentmode:MOP_GROUP_EXTERNAL_PAYMENT_MODE}")
    private String ddCodeMopGroupExternalPaymentMode;

    @Value("${gv.outlet.outlettype.mvstore:MVStore}")
    private String outletTypeMvStore;

    @Autowired
    private GcMeansOfPaymentMapper gcMeansOfPaymentMapper;

    @Autowired
    private OutletService outletService;

    @Autowired
    private MasterDataDdLangService masterDataDdLangService;

    @Autowired
    private MeansOfPaymentOutletService meansOfPaymentOutletService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> createMeansOfPayment(CreateMeansOfPaymentRequest request) {
        GcMeansOfPayment meansOfPayment = new GcMeansOfPayment();
        meansOfPayment.setMopName(request.getMopName());
        int count = gcMeansOfPaymentMapper.selectCount(meansOfPayment);
        if (count > 0) {
            return Result.failed(ResultErrorCodeEnum.NAME_ALREADY_USED.code(),
                    ResultErrorCodeEnum.NAME_ALREADY_USED.desc());
        }

        List<String> outletCodeList = request.getOutletCodeList().stream().distinct().collect(Collectors.toList());
        Result<GcMeansOfPayment> result = checkData(request.getMopGroup(), outletCodeList);
        if (!result.isSuccess()) {
            return Result.failed(result.getCode(), result.getMessage());
        }

        meansOfPayment = result.getData();
        BeanUtils.copyProperties(request, meansOfPayment);
        meansOfPayment.setMeansOfPaymentCode(UUIDUtils.generateCode());
        meansOfPayment.setStatus(GvcoreConstants.STATUS_ENABLE);
        meansOfPayment.setCreateTime(new Date());
        try {
            gcMeansOfPaymentMapper.insertSelective(meansOfPayment);
        } catch (DuplicateKeyException e) {
            return Result.failed(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                    ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
        }

        meansOfPaymentOutletService.insesrt(meansOfPayment.getMeansOfPaymentCode(), outletCodeList,
                meansOfPayment.getCreateUser());

        return Result.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateMeansOfPayment(UpdateMeansOfPaymentRequest request) {
        GcMeansOfPayment queryMeansOfPayment = new GcMeansOfPayment();
        queryMeansOfPayment.setId(request.getId());
        queryMeansOfPayment = gcMeansOfPaymentMapper.selectOne(queryMeansOfPayment);
        if (queryMeansOfPayment == null) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }

        if(!queryMeansOfPayment.getMopName().equals(request.getMopName())) {
            GcMeansOfPayment meansOfPayment = new GcMeansOfPayment();
            meansOfPayment.setMopName(request.getMopName());
            int count = gcMeansOfPaymentMapper.selectCount(meansOfPayment);
            if (count > 0) {
                return Result.failed(ResultErrorCodeEnum.NAME_ALREADY_USED.code(),
                        ResultErrorCodeEnum.NAME_ALREADY_USED.desc());
            }
        }

        List<String> outletCodeList = request.getOutletCodeList().stream().distinct().collect(Collectors.toList());
        Result<GcMeansOfPayment> result = checkData(request.getMopGroup(), outletCodeList);
        if (!result.isSuccess()) {
            return Result.failed(result.getCode(), result.getMessage());
        }

        GcMeansOfPayment meansOfPayment = result.getData();
        List<MeansOfPaymentOutlet> outletList = meansOfPaymentOutletService
                .queryByMeansOfPaymentCode(queryMeansOfPayment.getMeansOfPaymentCode());

        List<String> oldOutletCodeList = outletList.stream().map(MeansOfPaymentOutlet::getOutletCode)
                .collect(Collectors.toList());

        List<String> needDeleteOutletCodeList = oldOutletCodeList.stream()
                .filter(code -> !outletCodeList.contains(code)).collect(Collectors.toList());
        List<String> needInsertOutletCodeList = outletCodeList.stream()
                .filter(code -> !oldOutletCodeList.contains(code)).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(needDeleteOutletCodeList)) {
            for (String outletCode : needDeleteOutletCodeList) {
                MeansOfPaymentOutlet outlet = new MeansOfPaymentOutlet();
                outlet.setMeansOfPaymentCode(queryMeansOfPayment.getMeansOfPaymentCode());
                outlet.setOutletCode(outletCode);
                outlet.setDeleteStatus(GvcoreConstants.DELETE_STATUS_ENABLE);
                outlet.setUpdateUser(request.getUpdateUser());
                outlet.setUpdateTime(new Date());
                meansOfPaymentOutletService.updateByPrimaryKeySelective(outlet);
            }
        }

        if (CollectionUtils.isNotEmpty(needInsertOutletCodeList)) {
            meansOfPaymentOutletService.insesrt(queryMeansOfPayment.getMeansOfPaymentCode(),
                    needInsertOutletCodeList, request.getUpdateUser());
        }

        meansOfPayment.setId(request.getId());
        meansOfPayment.setUpdateUser(request.getUpdateUser());
        meansOfPayment.setUpdateTime(new Date());
        gcMeansOfPaymentMapper.updateByPrimaryKeySelective(meansOfPayment);

        return Result.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateMeansOfPaymentStatus(UpdateMeansOfPaymentStatusRequest request) {
        GcMeansOfPayment meansOfPayment = new GcMeansOfPayment();
        meansOfPayment.setId(request.getId());
        meansOfPayment = gcMeansOfPaymentMapper.selectOne(meansOfPayment);
        if (meansOfPayment == null) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }

        meansOfPayment.setStatus(request.getStatus());
        meansOfPayment.setUpdateUser(request.getUpdateUser());
        meansOfPayment.setUpdateTime(new Date());
        gcMeansOfPaymentMapper.updateByPrimaryKeySelective(meansOfPayment);

        return Result.ok();
    }

    @Override
    public PageResult<QueryMeansOfPaymentsByPageResponse> queryMeansOfPaymentsByPage(
            QueryMeansOfPaymentsByPageRequest request) {

        GcMeansOfPaymentDto meansOfPayment = new GcMeansOfPaymentDto();
        BeanUtils.copyProperties(request, meansOfPayment);
        meansOfPayment.setDeleteStatus(GvcoreConstants.DELETE_STATUS_DISABLE);

        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<GcMeansOfPayment> list = gcMeansOfPaymentMapper.selectSelective(meansOfPayment);
        PageInfo<GcMeansOfPayment> pageInfo = new PageInfo<>(list);

        List<String> meansOfPaymentCodeList = list.stream().map(GcMeansOfPayment::getMeansOfPaymentCode)
                .collect(Collectors.toList());
        List<MeansOfPaymentOutlet> mpOutletList = meansOfPaymentOutletService
                .queryByMeansOfPaymentCodeList(meansOfPaymentCodeList, GvcoreConstants.DELETE_STATUS_DISABLE);
        List<String> outletCodeList = mpOutletList.stream().map(MeansOfPaymentOutlet::getOutletCode)
                .collect(Collectors.toList());

        OutletDto dto = new OutletDto();
        dto.setOutletCodeList(outletCodeList);
        List<OutletIssuerNameInfo> outletIssuerNameInfoList = outletService.queryOutletIssuerNameInfo(dto);

        Map<String, OutletIssuerNameInfo> outletIssuerNameMap = outletIssuerNameInfoList.stream()
                .collect(Collectors.toMap(OutletIssuerNameInfo::getOutletCode, v -> v));
        Map<String, List<MeansOfPaymentOutlet>> mpOutletMap = mpOutletList.stream()
                .collect(Collectors.groupingBy(MeansOfPaymentOutlet::getMeansOfPaymentCode));

        List<QueryMeansOfPaymentsByPageResponse> responses = new ArrayList<>(request.getPageSize());
        list.forEach(item -> {
            QueryMeansOfPaymentsByPageResponse response = new QueryMeansOfPaymentsByPageResponse();
            BeanUtils.copyProperties(item, response);
            responses.add(response);

            List<MeansOfPaymentOutlet> outlets = mpOutletMap.getOrDefault(item.getMeansOfPaymentCode(),
                    Collections.emptyList());
            List<OutletInfo> outletInfoList = new ArrayList<>(outlets.size());
            response.setOutletInfoList(outletInfoList);
            for (MeansOfPaymentOutlet meansOfPaymentOutlet : outlets) {
                OutletInfo outletInfo = new OutletInfo();
                outletInfo.setOutletCode(meansOfPaymentOutlet.getOutletCode());
                OutletIssuerNameInfo nameInfo = outletIssuerNameMap.get(meansOfPaymentOutlet.getOutletCode());
                if (nameInfo != null) {
                    outletInfo.setOutletName(nameInfo.getOutletName());
                    /*outletInfo.setIssuerCode(nameInfo.getIssuerCode());
                    outletInfo.setIssuerName(nameInfo.getIssuerName());*/
                }
                outletInfoList.add(outletInfo);
            }
        });

        return PageResult.ok(responses, pageInfo.getTotal());
    }

    @Override
    public QueryMeansOfPaymentsByPageResponse getMeansOfPayments(String meansOfPaymentsCode) {
        GcMeansOfPayment meansOfPayment = new GcMeansOfPayment();
        meansOfPayment.setMeansOfPaymentCode(meansOfPaymentsCode);
        meansOfPayment = gcMeansOfPaymentMapper.selectOne(meansOfPayment);
        if (meansOfPayment == null) {
            return null;
        }

        QueryMeansOfPaymentsByPageResponse response = new QueryMeansOfPaymentsByPageResponse();
        BeanUtils.copyProperties(meansOfPayment, response);

        List<MeansOfPaymentOutlet> outletList = meansOfPaymentOutletService
                .queryByMeansOfPaymentCode(meansOfPayment.getMeansOfPaymentCode());
        if (CollectionUtils.isNotEmpty(outletList)) {
            List<String> outletCodeList = outletList.stream().map(MeansOfPaymentOutlet::getOutletCode)
                    .collect(Collectors.toList());

            OutletDto dto = new OutletDto();
            dto.setOutletCodeList(outletCodeList);
            List<OutletIssuerNameInfo> outletIssuerNameInfoList = outletService.queryOutletIssuerNameInfo(dto);

            Map<String, OutletIssuerNameInfo> outletIssuerNameMap = outletIssuerNameInfoList.stream()
                    .collect(Collectors.toMap(OutletIssuerNameInfo::getOutletCode, v -> v));

            List<OutletInfo> outletInfoList = new ArrayList<>(outletList.size());
            for (MeansOfPaymentOutlet outlet : outletList) {
                OutletInfo outletInfo = new OutletInfo();
                outletInfo.setOutletCode(outlet.getOutletCode());
                OutletIssuerNameInfo issuerNameInfo = outletIssuerNameMap.get(outlet.getOutletCode());
                if (issuerNameInfo != null) {
                    outletInfo.setOutletName(issuerNameInfo.getOutletName());
                    outletInfo.setIssuerCode(issuerNameInfo.getIssuerCode());
                    outletInfo.setIssuerName(issuerNameInfo.getIssuerName());
                }
                outletInfoList.add(outletInfo);
            }
            response.setOutletInfoList(outletInfoList);
        }

        return response;
    }

    @Override
    public Map<String, GcMeansOfPayment> queryByCodeList(List<String> meansOfPaymentCodeList) {
        if (CollectionUtils.isEmpty(meansOfPaymentCodeList)) {
            return Collections.emptyMap();
        }

        List<GcMeansOfPayment> list = gcMeansOfPaymentMapper.queryByCodeList(meansOfPaymentCodeList);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(GcMeansOfPayment::getMeansOfPaymentCode, v -> v));
    }

    @Override
    public GcMeansOfPayment getMeansOfPaymentsByName(String meansOfPaymentsName) {
        GcMeansOfPayment meansOfPayment = new GcMeansOfPayment();
        meansOfPayment.setMopName(meansOfPaymentsName);
        return gcMeansOfPaymentMapper.selectOne(meansOfPayment);
    }

    private Result<GcMeansOfPayment> checkData(String mopGroup, List<String> outletCodeList) {
        GcMeansOfPayment meansOfPayment = new GcMeansOfPayment();
        MasterDataDdLangEntity mopGroupGrp = getMasterDataDdLangEntity(ddCodeMopGroupGrp, mopGroup);
        MasterDataDdLangEntity externalPaymentMode = getMasterDataDdLangEntity(ddCodeMopGroupExternalPaymentMode,
                mopGroup);
        if (mopGroupGrp == null || externalPaymentMode == null) {
            return Result.failed(ResultErrorCodeEnum.SAP_MOP_GROUP_ERROR.code(),
                    ResultErrorCodeEnum.SAP_MOP_GROUP_ERROR.desc());
        }

        meansOfPayment.setGrp(mopGroupGrp.getDdText());
        meansOfPayment.setExternalPaymentMode(externalPaymentMode.getDdText());

        List<Outlet> outletList = outletService.queryByOutletCodeList(outletCodeList);
        Map<String, Outlet> outletMap = outletList.stream().collect(Collectors.toMap(Outlet::getOutletCode, v -> v));
        for (String outletCode : outletCodeList) {
            Outlet outlet = outletMap.get(outletCode);
            if (outlet == null) {
                return Result.failed(ResultErrorCodeEnum.NO_STORE_DATA_FOUND.code(),
                        outletCode + " " + ResultErrorCodeEnum.NO_STORE_DATA_FOUND.desc());
            }
            if (!outletTypeMvStore.equals(outlet.getOutletType())) {
                return Result.failed(ResultErrorCodeEnum.STORE_TYPE_IS_NOT_MVSTORE.code(),
                        outletCode + " " + ResultErrorCodeEnum.STORE_TYPE_IS_NOT_MVSTORE.desc());
            }
        }
        return Result.ok(meansOfPayment);
    }

    private MasterDataDdLangEntity getMasterDataDdLangEntity(String ddCode, String ddValue) {
        MasterDataDdLangEntity dataEntity = new MasterDataDdLangEntity();
        dataEntity.setTenantCode(GvcoreConstants.SYSTEM_DEFAULT);
        dataEntity.setDdCode(ddCode);
        dataEntity.setDdValue(ddValue);
        dataEntity.setState(1);
        return masterDataDdLangService.getByCode(dataEntity);
    }
} 