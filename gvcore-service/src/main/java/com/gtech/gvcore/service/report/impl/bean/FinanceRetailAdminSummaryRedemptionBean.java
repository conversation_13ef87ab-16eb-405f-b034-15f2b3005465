package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/21 13:28
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class FinanceRetailAdminSummaryRedemptionBean {

    @ExcelProperty(value = "Merchant Name")
    private String merchantName;

    @ExcelProperty(value = "Merchant outlet code")
    private String merchantOutletCode;

    @ExcelProperty(value = "Merchant outlet name")
    private String outletName;

    @ExcelProperty(value = "Transaction Type")
    private String transactionType;

    @ExcelProperty(value = "Invoice No")
    private String invoiceNo;

    @ExcelProperty(value = "VPG")
    private String voucherProgramGroup;

    @ExcelProperty(value = "Redemption Count", converter = ExportExcelNumberConverter.class)
    private String qtyOfVoucher;

    @ReportAmountValue
    @ExcelProperty(value = "Redemption Amount", converter = ExportExcelNumberConverter.class)
    private String amount;

}
