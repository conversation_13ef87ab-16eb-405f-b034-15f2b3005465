package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.utils.GvDateUtil;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.helper.GvPageHelper;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.PollReport;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.GoodsInTransitDetailedBean;
import com.gtech.gvcore.service.report.impl.bo.GoodsBookletBo;
import com.gtech.gvcore.service.report.impl.bo.GoodsInTransitDetailBo;
import com.gtech.gvcore.service.report.impl.bo.GoodsInTransitDetailVoucherBo;
import com.gtech.gvcore.service.report.impl.bo.GoodsReceiveBo;
import com.gtech.gvcore.service.report.impl.param.GoodsInTransitQueryData;
import com.gtech.gvcore.service.report.impl.support.GoodsInTransitBaseImpl;
import com.gtech.message.util.DateUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName GoodsInTransitDetailedImpl
 * @Description goods in transit detailed impl
 * <AUTHOR>
 * @Date 2022/12/7 16:54
 * @Version V1.0
 **/
@Service
public class GoodsInTransitDetailedImpl extends GoodsInTransitBaseImpl<GoodsInTransitDetailedBean>
        implements BusinessReport<GoodsInTransitQueryData, GoodsInTransitDetailedBean>,  PollReport {

    public static final String VOUCHER_NUMBER_PREFIX = "[^0-9]";//NOSONAR

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GOODS_IN_TRANSIT_DETAILED_REPORT;
    }

    @Override
    public List<GoodsInTransitDetailedBean> getExportData(GoodsInTransitQueryData queryData) {

        final List<GoodsInTransitDetailBo> goodsInTransitDetailBos = reportBusinessMapper.selectGoodsInTransitDetail(queryData, GvPageHelper.getRowBounds(queryData.getPageSize(), queryData.getPageNum()));
        if (CollectionUtils.isEmpty(goodsInTransitDetailBos)) return Collections.emptyList();

        final List<GoodsInTransitDetailVoucherBo> voucherBos = getGoodsInTransitDetailVoucherBoList(queryData, goodsInTransitDetailBos);
        if (CollectionUtils.isEmpty(voucherBos)) return Collections.emptyList();

        final Function<Date, String> dateFormat = time -> null == time ? StringUtils.EMPTY : GvDateUtil.formatUs(time, GvDateUtil.FORMAT_US_DATETIME_DD_MM_YYYY_HH_MM_A);

        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(voucherBos, GoodsInTransitDetailVoucherBo::getCpgCode, Cpg.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(voucherBos
                , Arrays.asList(GoodsInTransitDetailVoucherBo::getInbound, GoodsInTransitDetailVoucherBo::getOutbound)
                , Outlet.class);


        return voucherBos.stream().map(e -> new GoodsInTransitDetailedBean()
                        .setRequestId(e.getRequestId())
                        .setBookletNumber(e.getBookletNumber())
                        .setVoucherNumber(e.getVoucherNumber())
                        .setInbound(outletMap.findValue(e.getInbound()).getOutletName())
                        .setOutbound(outletMap.findValue(e.getOutbound()).getOutletName())
                        .setVoucherProgramGroup(cpgMap.findValue(e.getCpgCode()).getCpgName())
                        .setExpiryDate(DateUtil.format(e.getExpiryDate(), DateUtil.FORMAT_DEFAULT))
                        .setAllocatedTime(dateFormat.apply(e.getAllLocationTime()))
                        .setReceivedTime(dateFormat.apply(e.getReceiveTime())))
                .collect(Collectors.toList());

    }

    private List<GoodsInTransitDetailVoucherBo> getGoodsInTransitDetailVoucherBoList (GoodsInTransitQueryData queryData, List<GoodsInTransitDetailBo> goodsInTransitDetailBos) {

        List<GoodsInTransitDetailVoucherBo> voucherBos;
        voucherBos = joinVoucherInfo(goodsInTransitDetailBos);
        voucherBos = filterVoucherNumber(queryData, voucherBos);

        return filterExpiryDate(queryData, voucherBos);
    }


    private List<GoodsInTransitDetailVoucherBo> joinVoucherInfo (List<GoodsInTransitDetailBo> goodsInTransitDetailBos) {

        return goodsInTransitDetailBos.stream()
                .flatMap(e -> {
                    //卡券编号信息转换 去除非数字信息
                    final long voucherStartNum = StringUtils.isBlank(e.getVoucherStartNo()) ? 0L : Long.parseLong(e.getVoucherStartNo().replaceAll(VOUCHER_NUMBER_PREFIX, ""));
                    final long voucherEndNum = StringUtils.isBlank(e.getVoucherEndNo()) ? 0L : Long.parseLong(e.getVoucherEndNo().replaceAll(VOUCHER_NUMBER_PREFIX, ""));

                    //根据卡券编号范围查询符合条件的册子
                    final List<GoodsBookletBo> goodsBookletList = reportBusinessMapper.selectGoodsBookletDto(e.getVoucherStartNo(), e.getVoucherEndNo());
                    //查询每本册子中第一张卡券的过期时间
                    final Map<String, Optional<Date>> effectiveDateMap = goodsBookletList.stream()
                            .collect(Collectors.toMap(GoodsBookletBo::getBookletCode, b -> {
                                final Voucher voucher = super.getByCode(b.getVoucherStartNoSource(), Voucher.class);
                                return Optional.ofNullable(null == voucher ? null : voucher.getVoucherEffectiveDate());
                            }, (a, b) -> b));
                    //根据接收编码获取所有的接收信息集合
                    final List<GoodsReceiveBo> receiveBoList = reportBusinessMapper.selectReceiveTimeByVoucherCodeAndReceiveCode(e.getReceiveCode());

                    //根据卡券编号组装信息

                    List<GoodsInTransitDetailVoucherBo> result = new ArrayList<>();

                    for (long i = voucherStartNum; i <= voucherEndNum ; i++) {

                        final long vNumber = i;
                        //根据卡券编号查询对应的册子信息
                        final GoodsBookletBo booklet = goodsBookletList.stream().filter(b -> b.match(vNumber)).findFirst().orElse(new GoodsBookletBo());
                        //根据册子编号查询对应的过期时间
                        final Date effectiveDate = effectiveDateMap.get(booklet.getBookletCode()).orElse(null);
                        //根据卡券编号查询对应的接收时间
                        final Date receiveTime = receiveBoList.stream().filter(r -> r.match(vNumber)).map(GoodsReceiveBo::getReceiveDate).findFirst().orElse(null);

                        result.add(GoodsInTransitDetailVoucherBo.copyByGoodsInTransitDetailedImpl(e)
                                .setVoucherNumber(ConvertUtils.toString(i, StringUtils.EMPTY))
                                .setBookletNumber(ConvertUtils.toString(booklet.getBookletCode(), StringUtils.EMPTY))
                                .setExpiryDate(effectiveDate)
                                .setReceiveTime(receiveTime));
                    }

                    return result.stream();
                }).collect(Collectors.toList());
    }

    private List<GoodsInTransitDetailVoucherBo> filterVoucherNumber(GoodsInTransitQueryData queryData, List<GoodsInTransitDetailVoucherBo> goodsInTransitDetailBos) {

        final Long begin = queryData.getVoucherCodeNumStart();
        final Long end = queryData.getVoucherCodeNumEnd();

        if (null == begin && null == end) return goodsInTransitDetailBos;

        final long beginLong = null == begin ? Long.MIN_VALUE : begin;
        final long endLong = null == end ? Long.MAX_VALUE : end;

        return goodsInTransitDetailBos
                .parallelStream()
                .filter(e -> {
                    long voucherNumber = Long.parseLong(e.getVoucherNumber());

                    if (voucherNumber < beginLong) return false;
                    return voucherNumber <= endLong;
                }).collect(Collectors.toList());
    }

    private List<GoodsInTransitDetailVoucherBo> filterExpiryDate(GoodsInTransitQueryData queryData, List<GoodsInTransitDetailVoucherBo> goodsInTransitDetailBos) {

        if (null == queryData.getVoucherEffectiveDateStart() || null == queryData.getVoucherEffectiveDateEnd()) return goodsInTransitDetailBos;

        long begin = queryData.getVoucherEffectiveDateStart().getTime();
        long end = queryData.getVoucherEffectiveDateEnd().getTime();

        return goodsInTransitDetailBos.stream()
                .filter(e -> {
                    long effective = null != e.getExpiryDate() ? e.getExpiryDate().getTime() : 0L;

                    if (effective < begin) return false;

                    return effective <= end;
                }).collect(Collectors.toList());
    }

    @Override
    public Object getHeadObject(ReportContext context) {

        final GoodsInTransitQueryData queryData = (GoodsInTransitQueryData) context.getQueryParam();

        return new GoodsInTransitSummaryImpl.Head()
                .setTransactionDate(GvDateUtil.formatUs(queryData.getTransactionDateStart(), GvDateUtil.FORMAT_US_DATETIME_DD_MMM_YY)
                        + " - " + GvDateUtil.formatUs(queryData.getTransactionDateEnd(), GvDateUtil.FORMAT_US_DATETIME_DD_MMM_YY))
                .setGeneratedDate(GvDateUtil.formatUs(queryData.getTransactionDateEnd(), GvDateUtil.FORMAT_US_DATETIME_DD_MMM_YY_HH_MM_A));
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class Head {

        private String transactionDate;
        private String generatedDate;

    }

}
