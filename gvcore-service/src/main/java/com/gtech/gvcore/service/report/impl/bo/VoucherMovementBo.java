package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.gvcore.service.report.extend.poll.GroupNewTransactionByVoucherCodeSupport;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @ClassName VoucherMovementBo
 * @Description
 * <AUTHOR>
 * @Date 2023/3/29 10:20
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class VoucherMovementBo implements GroupNewTransactionByVoucherCodeSupport {

    private String voucherCode;

    private String transactionType;

    private String transactionCode;

    private BigDecimal transactionNumber;

    public void setTransactionCode(String transactionCode) {
        this.transactionNumber = new BigDecimal(transactionCode.substring(3));
        this.transactionCode = transactionCode;
    }

}
