package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.DateUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-12-12 11:04
 */
@Getter
@Setter
@Accessors(chain = true)
public class SalesDetailedBo {

    private String merchantCode;

    private String billNumber;

    private String outletCode;

    private String cpgCode;

    private String transactionDate;

    private String voucherCode;

    private String posCode;

    private BigDecimal denomination;

    private String responseMessage;

    private String transactionMode;

    private String invoiceNumber;

    private String referenceNumber;

    private String approveCode;

    public Date getTransactionDate() {

        return DateUtil.parseDate(transactionDate, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

}
