package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/20 16:52
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class GcCancelSalesSummaryBean {

    /**
     * Merchant code.
     */
    @ExcelProperty(value = "Merchant Name")
    private String merchantCode;

    /**
     * Cpg code.
     */
    @ExcelProperty(value = "Gift Card Program Group")
    private String cpgCode;

    /**
     * Number of vouchers.
     */
    @ExcelProperty(value = "Total Cards")
    private int totalCards;

    /**
     * Total amount.
     */
    @ExcelProperty(value = "Total Amount", converter = ExportExcelNumberConverter.class)
    private String totalAmount;
    @ExcelProperty(value = "Customer Name")
    private String customerName;
    @ExcelProperty(value = "Invoice Number")
    private String invoiceNumber;
    @ExcelProperty(value = "Notes")
    private String notes;

}
