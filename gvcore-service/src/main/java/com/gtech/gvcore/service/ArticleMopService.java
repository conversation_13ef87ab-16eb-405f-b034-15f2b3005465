package com.gtech.gvcore.service;

import java.util.List;
import java.util.Map;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.articlemop.CreateArticleMopRequest;
import com.gtech.gvcore.common.request.articlemop.QueryArticleMopsByPageRequest;
import com.gtech.gvcore.common.request.articlemop.UpdateArticleMopRequest;
import com.gtech.gvcore.common.request.articlemop.UpdateArticleMopStatusRequest;
import com.gtech.gvcore.common.response.articlemop.QueryArticleMopsByPageResponse;
import com.gtech.gvcore.dao.model.ArticleMop;

/**
 * <AUTHOR>
 * @date 2022年2月16日
 */
public interface ArticleMopService {

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月17日
     */
    Result<Void> createArticleMop(CreateArticleMopRequest request);

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月17日
     */
    Result<Void> updateArticleMop(UpdateArticleMopRequest request);

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月21日
     */
    Result<Void> updateArticleMopStatus(UpdateArticleMopStatusRequest request);

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月21日
     */
    PageResult<QueryArticleMopsByPageResponse> queryArticleMopsByPage(QueryArticleMopsByPageRequest request);

    /**
     * 
     * <AUTHOR>
     * @param articleMopCode
     * @return
     * @date 2022年3月22日
     */
    ArticleMop queryByArticleMopCode(String articleMopCode);
    
    /**
     * 
     * @param articleMopCodeList
     * @return
     */
    Map<String, ArticleMop> queryByArticleMopCodeList(List<String> articleMopCodeList);

}
