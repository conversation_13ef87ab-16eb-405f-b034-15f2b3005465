package com.gtech.gvcore.service.report.extend.row;

import com.gtech.commons.page.PageParam;
import lombok.Getter;
import org.apache.ibatis.session.RowBounds;

/**
 * @ClassName ReportPageParam
 * @Description
 * <AUTHOR>
 * @Date 2023/4/23 10:09
 * @Version V1.0
 **/
public abstract class ReportPageParam extends PageParam {

    public static final int MAX_TABLE_INDEX = 63;

    // 当前 table 的 前置 offset
    @Getter
    private int tableOffset;

    // 当前 table 的 index
    @Getter
    private int tableIndex = 0;

    // 当前 table 的 name
    @Getter
    private String tableName;

    // table template name
    @Getter
    private final String tableTemplateName;

    // table template name
    protected ReportPageParam(String tableTemplateName) {
        this.tableTemplateName = tableTemplateName;
        this.tableName = tableTemplateName + "_" +tableIndex;
    }

    /**
     * 设置当前表前置偏移量
     * 前置偏移量会记录夸表查询时上一页[已经返回过的][当前表的]数据量以此来保证数据不会重复
     * 例如:
     *      case_1: 单次夸表查询 取 夸表后查询的limit
     *          table_0 存在300条数据,table_1 存在800条数据 且 pageSize = 500
     *          则 第一页 返回 table_0 的300条数据 + table_1 的200条数据 该页面返回后 tableOffset = 200
     *          第二页 返回 table_1 的第 201 - 700 条数据 且 tableOffset = 200
     *      case_2: 多次夸表查询 取 最后一次夸表查询的limit
     *          table_0 存在300条数据,table_1 存在400条数据,table_2 存在800条数据, table_3 存在500条数据 且 pageSize = 1000
     *          则 第一页返回 table_0 的300条数据 + table_1 的400条数据 + table_2 的300条数据 该页面返回后 tableOffset = 300
     *          第二页返回 table_2 的第 301 - 800条数据 + table_3 的500条数据 且 tableOffset 重置为0
     *      case_3: 不夸表查询
     *          table_0 存在800条数据 且 pageSize = 500
     *          则 第一页返回 table_0 的500条数据 该页面返回后 tableOffset = 0
     * @param tableOffset
     */
    void settingTableOffset(int tableOffset) {
        this.tableOffset = tableOffset;
    }

    // 自增当前table 的 index
    public void incrementAndUpdateTable() {

        //重置前置偏移
        this.tableOffset = 0;

        //自增
        ++this.tableIndex;

        //更新表名
        this.tableName = this.tableTemplateName + "_" + this.tableIndex;

    }

    /**
     * 生成 RowBounds
     * offset = (pageNumber - 1) * pageSize + tableOffset
     * limit default = pageSize
     * 分页逻辑不会对offset 进行变更因为
     *  1.如果当前表满足数据需求则不会继续查询
     *  2.如果当前表数据不满足则会从0开始查询下一张表
     * limit 当作参数为了夸表查询时可以控制查询条数 避免不必要的数据查询
     * @param limit
     * @return
     */
    public RowBounds generateRowBounds(int limit) {

        int pageSize = getPageSize();
        int pageNumber = getPageNum();

        if (pageSize < 1) pageSize = PageParam.DEFAULT_PAGE_SIZE;
        if (pageNumber < 1) pageNumber = PageParam.DEFAULT_PAGE_NUM;

        int offset = ((pageNumber - 1) * pageSize) + tableOffset;

        return new RowBounds(offset, limit);
    }
}
