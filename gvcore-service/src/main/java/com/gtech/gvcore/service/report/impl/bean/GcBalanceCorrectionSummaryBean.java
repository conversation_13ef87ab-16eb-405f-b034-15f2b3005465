package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName BulkOrderDetailedBean
 * @Description BulkOrderDetailedBean
 * <AUTHOR>
 * @Date 2022/7/12 16:54
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GcBalanceCorrectionSummaryBean {

    @ExcelProperty(value = "Merchant")
    private String merchant;

    @ExcelProperty(value = "Outlet")
    private String outlet;

    @ExcelProperty(value = "Transaction Date")
    private String transactionDate;

    @ExcelProperty(value = "Gift Card Program Group")
    private String cpgCode;

    @ExcelProperty(value = "Total Cards")
    private Integer totalCards;

    @ReportAmountValue
    @ExcelProperty(value = "Total Correction Balance")
    private String totalAmount;

    @ExcelProperty(value = "Transaction Type")
    private String transactionType;

    @ExcelProperty(value = "Notes")
    private String notes;
}
