package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.ReportRequest;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import com.gtech.gvcore.service.report.extend.context.ReportContextHelper;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.ReissuedSummaryBean;
import com.gtech.gvcore.service.report.impl.bo.ReissuedBo;
import com.gtech.gvcore.service.report.impl.param.ReissuedQueryData;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 14:00
 * @Description:
 */
@Service
public class ReissuedSummaryImpl extends ReportSupport
        implements BusinessReport<ReissuedQueryData, ReissuedSummaryBean>, SingleReport {

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.REISSUED_SUMMARY_REPORT;
    }

    @Override
    public ReissuedQueryData builderQueryParam(CreateReportRequest reportParam) {

        ReissuedQueryData param = new ReissuedQueryData();

        //transaction
        param.setTransactionDateEnd(reportParam.getTransactionDateEnd());
        param.setTransactionDateStart(reportParam.getTransactionDateStart());

        // issuer merchant outlet
        param.setMerchantCodeList(reportParam.getMerchantCodes());
        param.setOutletCodeList(reportParam.getOutletCodes());

        //cpg
        param.setCpgCodeList(reportParam.getCpgCodes());

        //invoice number
        param.setInvoiceNumber(reportParam.getInvoiceNo());

        final ReportContext context = ReportContextHelper.findContext();
        final ReportRequest reportRequest = context.getReportRequest();
        final CreateReportRequest sourceParam = BeanCopyUtils.jsonCopyBean(reportRequest.getRequestJson(), CreateReportRequest.class);

        param.setNullMerchantCode(CollectionUtils.isEmpty(sourceParam.getMerchantCodes()));
        param.setNullOutletCode(CollectionUtils.isEmpty(sourceParam.getOutletCodes()));


        return param;
    }

    @Override
    public List<ReissuedSummaryBean> getExportData(ReissuedQueryData queryData) {

        Map<String, ReissuedSummaryStatisticalBo> boMap = new HashMap<>();

        PollPageHelper.pollSelect(reportBusinessMapper::selectReissued, queryData, e ->
                e.forEach(l ->
                        boMap.merge(ReissuedSummaryStatisticalBo.groupKey(l),
                                ReissuedSummaryStatisticalBo.convert(l),
                                ReissuedSummaryStatisticalBo::merge)
                ));

        //find
        List<ReissuedSummaryStatisticalBo> reissuedSummaryBos = new ArrayList<>(boMap.values());

        if (CollectionUtils.isEmpty(reissuedSummaryBos)) return new ArrayList<>();

        //init map
        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(reissuedSummaryBos, ReissuedSummaryStatisticalBo::getCpgCode, Cpg.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(reissuedSummaryBos, ReissuedSummaryStatisticalBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(reissuedSummaryBos, ReissuedSummaryStatisticalBo::getOutletCode, Outlet.class);

        return reissuedSummaryBos.stream()
                .map(e -> new ReissuedSummaryBean()
                        .setNumberOfVouchers(ConvertUtils.toString(e.getNumberOfVouchers(), ""))
                        .setVoucherAmount(super.toAmount(e.getTotalAmount()))
                        .setMerchantOut(outletMap.findValue(e.getOutletCode()).getOutletName())
                        .setVpg(cpgMap.findValue(e.getCpgCode()).getCpgName())
                        .setMerchant(merchantMap.findValue(e.getMerchantCode()).getMerchantName())
                ).collect(Collectors.toList());
    }


    @Getter
    @Setter
    @Accessors(chain = true)
    public static class ReissuedSummaryStatisticalBo {

        /**
         * Merchant code.
         */
        private String merchantCode;

        /**
         * Outlet code.
         */
        private String outletCode;

        /**
         * Cpg code.
         */
        private String cpgCode;

        /**
         * Number of vouchers.
         */
        private int numberOfVouchers = 1;

        /**
         * Total amount.
         */
        private BigDecimal totalAmount;

        public static ReissuedSummaryStatisticalBo convert (ReissuedBo reactivatedBo) {

            return new ReissuedSummaryStatisticalBo()
                    .setMerchantCode(reactivatedBo.getMerchantCode())
                    .setOutletCode(reactivatedBo.getOutletCode())
                    .setCpgCode(reactivatedBo.getCpgCode())
                    .setTotalAmount(reactivatedBo.getDenomination());
        }

        public ReissuedSummaryStatisticalBo merge (ReissuedSummaryStatisticalBo bo) {

            this.numberOfVouchers += bo.getNumberOfVouchers();
            this.totalAmount = this.totalAmount.add(bo.getTotalAmount());

            return this;
        }

        public static String groupKey (ReissuedBo reactivatedBo) {

            return StringUtils.join(",", reactivatedBo.getMerchantCode(), reactivatedBo.getOutletCode(), reactivatedBo.getCpgCode());
        }

    }
    
}
