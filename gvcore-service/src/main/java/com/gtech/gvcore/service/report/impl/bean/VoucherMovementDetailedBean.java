package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/21 13:18
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class VoucherMovementDetailedBean {

    @ExcelProperty(value = "Voucher Number")
    private String cardNumber;

    @ExcelProperty(value = "Client Name")
    private String customerName;

    @ExcelProperty(value = "Company")
    private String companyName;

    @ExcelProperty(value = "Voucher Program Group")
    private String voucherProgramGroup;

    @ExcelProperty(value = "Status")
    private String status;

    @ReportAmountValue
    @ExcelProperty(value = "Denomination", converter = ExportExcelNumberConverter.class)
    private String denomination;

}
