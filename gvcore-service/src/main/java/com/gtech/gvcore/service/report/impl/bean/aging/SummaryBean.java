package com.gtech.gvcore.service.report.impl.bean.aging;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportLabel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class SummaryBean {

    @ExcelProperty(value = "Voucher Details", index = 0)
    private String voucherDetails;

    @ExcelProperty(value = "Sales Value", index = 1)
    private String salesValue;

    @ExcelProperty(index = 2)
    @ReportLabel(value = {"Aging Usage", "Usage Value"})
    private String auUsageValue;

    @ExcelProperty(index = 3)
    @ReportLabel(value = {"Aging Usage", "1 month"})
    private String auOne;

    @ExcelProperty(index = 4)
    @ReportLabel(value = {"Aging Usage", "2 month"})
    private String auTwo;

    @ExcelProperty(index = 5)
    @ReportLabel(value = {"Aging Usage", "3 month"})
    private String auThree;

    @ExcelProperty(index = 6)
    @ReportLabel(value = {"Aging Usage", "4 to 6 month"})
    private String auFour;

    @ExcelProperty(index = 7)
    @ReportLabel(value = {"Aging Usage", "6 to 12 month"})
    private String auSix;

    @ExcelProperty(index = 8)
    @ReportLabel(value = {"Aging Usage", "More than 12 months"})
    private String auYear;

    @ExcelProperty(index = 9)
    @ReportLabel(value = {"Aging Non Use", "Unredeem Value"})
    private String anuUnredeemValue;

    @ExcelProperty(index = 10)
    @ReportLabel(value = {"Aging Non Use", "1 month"})
    private String anuOne;

    @ExcelProperty(index = 11)
    @ReportLabel(value = {"Aging Non Use", "2 month"})
    private String anuTwo;

    @ExcelProperty(index = 12)
    @ReportLabel(value = {"Aging Non Use", "3 month"})
    private String anuThree;

    @ExcelProperty(index = 13)
    @ReportLabel(value = {"Aging Non Use", "4 to 6 month"})
    private String anuFour;

    @ExcelProperty(index = 14)
    @ReportLabel(value = {"Aging Non Use", "6 to 12 month"})
    private String anuSix;

    @ExcelProperty(index = 15)
    @ReportLabel(value = {"Aging Non Use", "More than 12 months"})
    private String anuYear;

}