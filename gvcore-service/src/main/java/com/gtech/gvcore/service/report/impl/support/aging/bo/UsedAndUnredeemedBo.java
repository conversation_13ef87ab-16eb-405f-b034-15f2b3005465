package com.gtech.gvcore.service.report.impl.support.aging.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName AgingUsedAndUnredeemedReportBo
 * @Description Aging Used And Unredeemed Report Bo
 * <AUTHOR>
 * @Date 2022/10/31 20:16
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class UsedAndUnredeemedBo {

    private String customerCode;

    private String customerName;

    private int use;

    private int unUse;

    public int getCount() {
        return use + unUse;
    }

    public UsedAndUnredeemedBo addUse() {
        this.use++;
        return this;
    }

    public UsedAndUnredeemedBo addUnUse() {
        this.unUse++;
        return this;
    }

    public UsedAndUnredeemedBo addUse(int value) {
        this.use += value;
        return this;
    }

    public UsedAndUnredeemedBo addUnUse(int value) {
        this.unUse += value;
        return this;
    }
}