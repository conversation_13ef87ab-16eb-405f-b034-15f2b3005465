package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.exchangerate.CreateExchangeRateRequest;
import com.gtech.gvcore.common.request.exchangerate.DeleteExchangeRateRequest;
import com.gtech.gvcore.common.request.exchangerate.GetExchangeRateRequest;
import com.gtech.gvcore.common.request.exchangerate.QueryExchangeRateByPageRequest;
import com.gtech.gvcore.common.request.exchangerate.UpdateExchangeRateRequest;
import com.gtech.gvcore.common.request.exchangerate.UpdateExchangeRateStatusRequest;
import com.gtech.gvcore.common.response.exchangerate.GvExchangeRateByPageResponse;

/**
 * exchange rate(GvExchangeRate)表服务接口
 *
 * <AUTHOR>
 * @since 2022-02-25 15:13:56
 */
public interface GvExchangeRateService {

    Result<Object> createExchangeRate(CreateExchangeRateRequest createExchangeRateRequest);

    Result<Object> getExchangeRate(GetExchangeRateRequest getExchangeRateRequest);

    Result<Object> deleteExchangeRate(DeleteExchangeRateRequest deleteExchangeRateRequest);

    Result<Object> updateExchangeRate(UpdateExchangeRateRequest updateExchangeRateRequest);

    Result<Object> updateExchangeRateStatus(UpdateExchangeRateStatusRequest request);

    PageResult<GvExchangeRateByPageResponse> queryExchangeRateByPage(QueryExchangeRateByPageRequest request);
}
