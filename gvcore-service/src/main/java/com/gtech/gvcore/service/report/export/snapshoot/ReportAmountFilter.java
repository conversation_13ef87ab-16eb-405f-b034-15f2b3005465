package com.gtech.gvcore.service.report.export.snapshoot;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.common.utils.AmountUtils;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName ReportAmountFilter
 * @Description Amount Filter
 * <AUTHOR>
 * @Date 2023/2/15 18:08
 * @Version V1.0
 **/
@Component
public class ReportAmountFilter {

    /**
     * filter
     *
     * @param queryOrderReportDataResponse
     * @return
     */
    public QueryOrderReportDataResponse filter(QueryOrderReportDataResponse queryOrderReportDataResponse) {

        // null or empty return
        if (null == queryOrderReportDataResponse || CollectionUtils.isEmpty(queryOrderReportDataResponse.getList())) return queryOrderReportDataResponse;

        // result bean list
        List<?> list = queryOrderReportDataResponse.getList();

        // result bean type
        Class<?> resultBeanType = list.get(0).getClass();

        // filter count > 0
        final List<Field> fields = Arrays.stream(resultBeanType.getDeclaredFields())
                .filter(field -> field.getAnnotation(ReportAmountValue.class) != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fields)) return queryOrderReportDataResponse;

        // for setting amount value
        list.forEach(e -> fields.forEach(field -> {
            try {
                field.setAccessible(true);
                ReflectionUtils.setField(field, e, AmountUtils.idrFormat(ConvertUtils.toString(field.get(e))));
            } catch (IllegalArgumentException ignore) {
                //ignore
            } catch (IllegalAccessException ex) {
                throw new IllegalArgumentException(ex);
            }
        }));

        // return
        return queryOrderReportDataResponse;

    }


}
