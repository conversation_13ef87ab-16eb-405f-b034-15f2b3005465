package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/21 13:28
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class GcRedemptionSummaryBean {

    @ExcelProperty(value = "Date")
    private String date;

    @ExcelProperty(value = "Merchant Name")
    private String merchantName;

    @ExcelProperty(value = "Merchant Outlet Name")
    private String merchantOutletName;

    @ExcelProperty(value = "Invoice Number")
    private String invoiceNumber;

    @ExcelProperty(value = "Gift Card Program Group")
    private String voucherProgramGroup;

    @ExcelProperty(value = "Redemption Count", converter = ExportExcelNumberConverter.class)
    private String redemptionCount;

    @ReportAmountValue
    @ExcelProperty(value = "Redemption Amount", converter = ExportExcelNumberConverter.class)
    private String redemptionAmount;

    @ExcelProperty(value = "SBU Company Name")
    private String sbuCompanyName;

    @ExcelProperty(value = "Notes")
    private String notes;
}
