package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/21 14:12
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class GcSalesDetailedBean {

    @ExcelProperty(value = "Transaction Date")
    private String transactionDate;

    @ExcelProperty(value = "Gift Card Number")
    private String cardNumber;

    @ExcelProperty(value = "Merchant Name")
    private String merchant;

    @ExcelProperty(value = "Outlet Name")
    private String outlet;

    @ExcelProperty(value = "Gift Card Program Group")
    private String programGroup;

    @ExcelProperty(value = "Transaction Type")
    private String transactionType;

    @ReportAmountValue
    @ExcelProperty(value = "Denomination", converter = ExportExcelNumberConverter.class)
    private String denomination;

    @ExcelProperty(value = "Invoice Number")
    private String invoiceNumber;

    @ExcelProperty(value = "Company/Buyer Name")
    private String customerName;

    @ExcelProperty(value = "Issuance Date")
    private String issuanceYear;

    @ExcelProperty(value = "Approval Code")
    private String approvalCode;

}
