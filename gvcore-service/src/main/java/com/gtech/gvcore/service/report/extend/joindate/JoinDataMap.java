package com.gtech.gvcore.service.report.extend.joindate;

import org.apache.commons.collections4.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName ReportMap
 * @Description 报表关联数据Map
 * <AUTHOR>
 * @Date 2023/1/16 16:11
 * @Version V1.0
 **/
public class JoinDataMap<V> extends HashMap<String, V> {

    private final transient V empty;

    public JoinDataMap() {

        super();

        this.empty = null;
    }

    public JoinDataMap(V empty) {

        super();

        this.empty = empty;

    }

    public void putAll(List<V> value, Function<V, String> mapper) {

        if (CollectionUtils.isEmpty(value)) return;

        super.putAll(value.stream().collect(Collectors.toMap(mapper, Function.identity(), (a, b) -> b)));

    }

    /**
     * 查找一个key
     *  必定获得一个有效对象
     * @param key
     * @return
     */
    public V findValue (String key) {

        if(!super.containsKey(key)) return empty;

        V value = super.get(key);

        if (Objects.isNull(value)) return empty;

        return value;

    }

    @Override
    public boolean equals(Object o) {
        return super.equals(o);
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }
}
