package com.gtech.gvcore.service.report.impl.support.liability.model;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.ReportVoucherStatusEnum;
import com.gtech.gvcore.common.utils.GvConvertUtils;
import com.gtech.gvcore.dao.model.ReportTempLiabilityDStructure;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;

/**
 * @ClassName LiabilityDetailStatisticDto
 * @Description LiabilityDetailStatisticDto
 * <AUTHOR>
 * @Date 2023/4/19 14:33
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class LiabilityDetailStatisticDto {

    public static final int PAGE_SIZE = 1000;

    private String tableCode;

    private String issuerCode;

    private String merchantCode;

    private String outletCode;

    private String cpgCode;

    private Integer voucherStatus;

    private BigDecimal denomination;

    private String effectiveDate;

    private AtomicInteger voucherCodePageIndex = new AtomicInteger(1);

    private AtomicReference<String> voucherCodes;

    private AtomicInteger refreshPageIndex = new AtomicInteger(0);

    private Consumer<LiabilityDetailStatisticDto> refreshFunction;

    public static LiabilityDetailStatisticDto convert(LiabilityVoucherBo bo, Consumer<LiabilityDetailStatisticDto> refreshFunction) {

        final LiabilityVoucherMode voucher = GvConvertUtils.toObject(bo.getVoucher(), new LiabilityVoucherMode());
        final String voucherCode = voucher.getVoucherCode();
        final ReportVoucherStatusEnum voucherStatusEnum = bo.getVoucherStatusEnum();
        final LiabilityTransactionModel statusTransactionData = GvConvertUtils.toObject(bo.getStatusTransactionData(), new LiabilityTransactionModel());

        return new LiabilityDetailStatisticDto()
                .setTableCode(bo.getTableCode())
                .setIssuerCode(GvConvertUtils.toString(statusTransactionData.getIssuerCode(), StringUtils.EMPTY))
                .setMerchantCode(GvConvertUtils.toString(statusTransactionData.getMerchantCode(), StringUtils.EMPTY))
                .setOutletCode(GvConvertUtils.toString(statusTransactionData.getOutletCode(), StringUtils.EMPTY))
                .setCpgCode(GvConvertUtils.toString(voucher.getCpgCode(), StringUtils.EMPTY))
                .setVoucherStatus(voucherStatusEnum.getCode())
                .setDenomination(GvConvertUtils.toBigDecimal(voucher.getDenomination(), BigDecimal.ZERO))
                .setEffectiveDate(GvConvertUtils.toString(DateUtil.format(voucher.getVoucherEffectiveDate(), DateUtil.FORMAT_YYYYMMDDHHMISS_14), StringUtils.EMPTY))
                .setVoucherCodes(new AtomicReference<>(voucherCode))
                .setRefreshFunction(refreshFunction);

    }

    public static String getGroupKey(LiabilityDetailStatisticDto bean) {

        return StringUtils.join("_", bean.getIssuerCode(), bean.getMerchantCode(), bean.getOutletCode()
                , bean.getCpgCode(), bean.getVoucherStatus(), bean.getDenomination(), bean.getEffectiveDate());
    }

    public synchronized LiabilityDetailStatisticDto merge(LiabilityDetailStatisticDto bean) {

        this.voucherCodePageIndex.incrementAndGet();
        this.voucherCodes.updateAndGet(voucherCodeString -> voucherCodeString + "," + bean.getVoucherCodes());

        this.refresh();

        return this;
    }


    public  void refresh() {

        if (voucherCodePageIndex.get() - refreshPageIndex.get() >= PAGE_SIZE) {

            // save
            refreshFunction.accept(this);

            // reset
            refreshPageIndex.set(voucherCodePageIndex.get());
            voucherCodes.set(StringUtils.EMPTY);
        }
    }
    
    public ReportTempLiabilityDStructure toEntity() {

        final String voucherCodeString = this.getVoucherCodes().get();

        return new ReportTempLiabilityDStructure()
                .setIssuerCode(this.getIssuerCode())
                .setMerchantCode(this.getMerchantCode())
                .setOutletCode(this.getOutletCode())
                .setCpgCode(this.getCpgCode())
                .setVoucherStatus(this.getVoucherStatus())
                .setDenomination(this.getDenomination())
                .setEffectiveDate(DateUtil.parseDate(this.getEffectiveDate(), DateUtil.FORMAT_YYYYMMDDHHMISS_14))
                //由于 detail merge 方法 synchronized 所以 此处可以直接获取page index 与 voucher codes 不会有并发问题
                //最后save 结果时由于是单线程所以不需要考虑并发问题
                .setVoucherCodePageIndex(this.getVoucherCodePageIndex().get())
                .setVoucherCodes(voucherCodeString);
    }
    
}
