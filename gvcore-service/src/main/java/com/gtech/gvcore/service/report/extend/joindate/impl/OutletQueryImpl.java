package com.gtech.gvcore.service.report.extend.joindate.impl;

import com.gtech.gvcore.dao.mapper.OutletMapper;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.service.report.extend.joindate.QuerySupport;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

/**
 * @ClassName OutletQueryImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/1/16 15:41
 * @Version V1.0
 **/
@Component
public class OutletQueryImpl implements QuerySupport<Outlet> {

    private static final Outlet EMPTY = new Outlet();

    @Autowired
    private OutletMapper outletMapper;

    @Override
    public List<Outlet> queryByCode(List<String> codes, String... selectFields) {

        if (CollectionUtils.isEmpty(codes)) return Collections.emptyList();

        Example example = new Example(Outlet.class);
        example.createCriteria().andIn(Outlet.C_OUTLET_CODE, codes);

        if (ArrayUtils.isNotEmpty(selectFields)) example.selectProperties(selectFields);

        List<Outlet> list = outletMapper.selectByCondition(example);
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        return list;
    }

    @Override
    public Function<Outlet, String> codeMapper() {
        return Outlet::getOutletCode;
    }

    @Override
    public Outlet emptyObject() {
        return EMPTY;
    }

    @Override
    public Class<Outlet> supportType() {
        return Outlet.class;
    }

}
