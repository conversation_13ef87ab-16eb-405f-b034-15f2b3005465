package com.gtech.gvcore.service.report.impl.support;

import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.impl.param.GoodsInTransitQueryData;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;

/**
 * @ClassName GoodsInTransitBaseImpl
 * @Description GoodsInTransitReport
 * <AUTHOR>
 * @Date 2022/12/7 15:51
 * @Version V1.0
 **/
public abstract class GoodsInTransitBaseImpl<R> extends ReportSupport
        implements BusinessReport<GoodsInTransitQueryData, R> {

    @Override
    public GoodsInTransitQueryData builderQueryParam(CreateReportRequest reportParam) {

        GoodsInTransitQueryData goodsInTransit = new GoodsInTransitQueryData();

        goodsInTransit.setTransactionDateStart(reportParam.getTransactionDateStart());
        goodsInTransit.setTransactionDateEnd(reportParam.getTransactionDateEnd());

        goodsInTransit.setVoucherCodeNumStart(reportParam.getVoucherCodeNumStart());
        goodsInTransit.setVoucherCodeNumEnd(reportParam.getVoucherCodeNumEnd());

        goodsInTransit.setInboundCodeList(reportParam.getInboundCodeList());
        goodsInTransit.setOutboundCodeList(reportParam.getOutboundCodeList());

        goodsInTransit.setCpgCodeList(reportParam.getCpgCodes());

        if (StringUtils.isNotBlank(reportParam.getVoucherRequestId())) goodsInTransit.setRequestIdList(Collections.singletonList(reportParam.getVoucherRequestId()));

        goodsInTransit.setVoucherEffectiveDateStart(reportParam.getVoucherEffectiveDateStart());
        goodsInTransit.setVoucherEffectiveDateEnd(reportParam.getVoucherEffectiveDateEnd());

        return goodsInTransit;
    }

}
