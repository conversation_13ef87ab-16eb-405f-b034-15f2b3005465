package com.gtech.gvcore.service.report.extend;

import com.alibaba.fastjson.TypeReference;
import com.gtech.commons.utils.LocalCacheUtil;
import com.gtech.gvcore.dao.mapper.OutletMapper;
import com.gtech.gvcore.dao.model.Outlet;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName ReportOutletParamBuilder
 * @Description outlet 参数构建
 * <AUTHOR>
 * @Date 2022/11/24 16:00
 * @Version V1.0
 **/
@Component
public class ReportOutletTreeParamBuilder {

    @Autowired
    private OutletMapper outletMapper;

    private static final String NODE_INDEX_MAP_KEY = "outlet_node_tree";
    private static final long NODE_INDEX_MAP_TIMEOUT = 10 * 1000L;

    /**
     * 绑定传入outletCode相关的子编码并构建一个新的code集合
     *
     * @param outletCodes
     * @return
     */
    public List<String> builderOutletCodeList(List<String> outletCodes) {

        if (CollectionUtils.isEmpty(outletCodes)) return new ArrayList<>();

        return getCodeList(outletCodes);

    }

    /**
     * 获得编码集合
     *
     * @param outletCodes
     * @return
     */
    private List<String> getCodeList(List<String> outletCodes) {

        Map<String, Node> nodeMap = getNodeMap();

        List<String> codeList = new ArrayList<>();
        outletCodes.stream()
                .filter(nodeMap::containsKey)
                .map(nodeMap::get)
                .map(Node::nodeCodes)
                .forEach(codeList::addAll);

        return codeList.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 获得outlet tree 的索引MAP
     *
     * @return
     */
    public Map<String, Node> getNodeMap() {

        Map<String, Node> load = LocalCacheUtil.load(NODE_INDEX_MAP_KEY, new TypeReference<Map<String, Node>>() {});

        if (null == load) {

            List<Outlet> outlets = outletMapper.selectAll();

            LocalCacheUtil.save(NODE_INDEX_MAP_KEY, getNodeMap(outlets), NODE_INDEX_MAP_TIMEOUT);

            return getNodeMap();
        }

        return load;
    }


    /**
     * 通过传入的outlet 构建一个新的outlet tree 索引MAP
     *
     * @param outlets
     * @return
     */
    private Map<String, Node> getNodeMap(List<Outlet> outlets) {

        //构建节点集合
        List<Node> allNode = outlets.stream().map(Node::getInstance).collect(Collectors.toList());

        //构建节点索引
        Map<String, Node> indexMap = allNode.stream().collect(Collectors.toMap(Node::getCode, Function.identity()));

        //获得顶层节点
        List<Node> nodeList = allNode.stream()
                .filter(e -> StringUtils.isBlank(e.getParentCode()))
                .collect(Collectors.toList());

        //通过顶层节点与节点集合构建outlet tree
        nodeList.forEach(n -> findOutletCode(n, allNode));

        return indexMap;
    }

    /**
     * 查询子节点
     *
     * @param node
     * @param allNode
     * @return
     */
    private Node findOutletCode(Node node, List<Node> allNode) {

        //当前节点查询时没有后续节点 返回当前节点
        if (CollectionUtils.isEmpty(allNode)) return node;

        //从所有节点中删除当前节点 避免循环引用 和 多引用
        allNode.remove(node);

        //构建节点集合的流对象
        allNode.stream()
                //查询并过滤 当前节点的子节点
                .filter(n -> node.getCode().equals(n.getParentCode()))
                //查询过滤节点的子节点
                .map(n -> findOutletCode(n, new LinkedList<>(allNode)))
                //把所有节点追加到当前节点的子节点集合
                .forEach(node::addNode);

        //返回当前节点
        return node;
    }


    /**
     * 节点对象
     */
    @Getter
    @Setter
    @Accessors(chain = true)
    public static class Node {

        /**
         * 节点编码
         */
        private String code;

        /**
         * 父节点编码
         */
        private String parentCode;

        /**
         * 子节点集合
         */
        private List<Node> childNodes = new ArrayList<>();

        /**
         * 追加子节点
         *
         * @param node
         */
        public void addNode(Node node) {
            childNodes.add(node);
        }

        /**
         * 创建一个新的节点实例
         *
         * @param outlet
         * @return
         */
        public static Node getInstance(Outlet outlet) {

            return new Node().setCode(outlet.getOutletCode()).setParentCode(outlet.getParentOutlet());
        }

        /**
         * 获得当前节点 以及 当前节点所有叶子节点 的编码集合
         *
         * @return
         */
        public List<String> nodeCodes() {

            return getNodeCode(this);
        }

        /**
         * 获得传入节点的所有叶子节点编码集合
         *
         * @param node
         * @return
         */
        private static List<String> getNodeCode(Node node) {

            //保存当前节点编码
            List<String> result = new ArrayList<>();
            result.add(node.getCode());

            //获得当前节点的所有子节点
            List<Node> nodes = node.getChildNodes();
            if (CollectionUtils.isEmpty(nodes)) return result;

            //循环获得子节点的所有子节点并追加进入结果集合
            nodes.stream().map(Node::getNodeCode).forEach(result::addAll);

            return result;
        }

    }

}
