package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.ReportParamConvertHelper;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.FinanceRetailAdminSalesSummaryBean;
import com.gtech.gvcore.service.report.impl.bo.FinanceRetailAdminSalesBo;
import com.gtech.gvcore.service.report.impl.bo.FinanceRetailAdminSalesSummaryBo;
import com.gtech.gvcore.service.report.impl.param.FinanceRetailAdminSalesQueryData;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @ClassName FinanceRetailAdminSalesSummaryImpl
 * @Description FinanceRetailAdminSalesSummaryImpl
 **/
@Service
public class FinanceRetailAdminSalesSummaryImpl extends ReportSupport
        implements BusinessReport<FinanceRetailAdminSalesQueryData, FinanceRetailAdminSalesSummaryBean>, SingleReport {

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.FINANCE_RETAIL_ADMIN_SALES_SUMMARY_REPORT;
    }

    @Override
    public FinanceRetailAdminSalesQueryData builderQueryParam(CreateReportRequest reportParam) {

        //param
        FinanceRetailAdminSalesQueryData param = new FinanceRetailAdminSalesQueryData();

        //convert
        ReportParamConvertHelper.convertQueryDateCompanyCodeToMerchantCode(reportParam);

        //transaction
        param.setTransactionDateEnd(reportParam.getTransactionDateEnd());
        param.setTransactionDateStart(reportParam.getTransactionDateStart());

        // issuer merchant outlet
        param.setMerchantCodeList(reportParam.getMerchantCodes());
        param.setOutletCodeList(reportParam.getOutletCodes());

        //voucher code
        param.setVoucherCodeNumStart(reportParam.getVoucherCodeNumStart());
        param.setVoucherCodeNumEnd(reportParam.getVoucherCodeNumEnd());

        //cpg
        param.setCpgCodeList(reportParam.getCpgCodes());

        //invoice number
        param.setInvoiceNumber(reportParam.getInvoiceNo());

        return param;
    }

    @Override
    public List<FinanceRetailAdminSalesSummaryBean> getExportData(FinanceRetailAdminSalesQueryData queryData) {

        final Collection<FinanceRetailAdminSalesSummaryBo> list = this.queryBoList(queryData);
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(list, FinanceRetailAdminSalesSummaryBo::getCpgCode, Cpg.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(list, FinanceRetailAdminSalesSummaryBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(list, FinanceRetailAdminSalesSummaryBo::getOutletCode, Outlet.class);

        return list.stream()
                .map(e -> new FinanceRetailAdminSalesSummaryBean()
                        .setMerchantName(merchantMap.findValue(e.getMerchantCode()).getMerchantName())
                        .setOutletName(outletMap.findValue(e.getOutletCode()).getOutletName())
                        .setMerchantOutletCode(outletMap.findValue(e.getOutletCode()).getBusinessOutletCode())
                        .setInvoiceNo(e.getInvoiceNumber())
                        .setTransactionType(TransactionTypeEnum.GIFT_CARD_SELL.getDesc())
                        .setVoucherProgramGroup(cpgMap.findValue(e.getCpgCode()).getCpgName())
                        .setQtyOfVoucher(ConvertUtils.toBigDecimal(e.getSalesCount(), BigDecimal.ZERO).toPlainString())
                        .setAmount(super.toAmount(ConvertUtils.toBigDecimal(e.getSalesAmount(), BigDecimal.ZERO))))
                .collect(Collectors.toList());
    }


    private Collection<FinanceRetailAdminSalesSummaryBo> queryBoList(FinanceRetailAdminSalesQueryData queryData) {

        // 查询最新并且是兑换的交易
        List<FinanceRetailAdminSalesBo> redemptionBoList =
                Optional.of(PollPageHelper.pollGroupNewTransactionByCodeSelect(reportBusinessMapper::financeRetailAdminSales, queryData))
                        .map(e -> e.stream().filter(d -> TransactionTypeEnum.GIFT_CARD_SELL.equalsCode(d.getTransactionType())).collect(Collectors.toList()))
                        .orElse(Collections.emptyList());

        //根据条件分组
        return redemptionBoList.stream()
                .map(FinanceRetailAdminSalesSummaryBo::convert)
                .collect(Collectors.toMap(
                                // key
                                FinanceRetailAdminSalesSummaryBo::getGroupKey,
                                // value
                                FinanceRetailAdminSalesSummaryBo::newInstance,
                                // merge
                                FinanceRetailAdminSalesSummaryBo::merge)
                        //to map
                ).values();

    }

}