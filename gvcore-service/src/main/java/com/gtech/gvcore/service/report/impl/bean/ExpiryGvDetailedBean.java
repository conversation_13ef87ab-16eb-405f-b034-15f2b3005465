package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import com.gtech.gvcore.service.report.extend.ReportBeanCustomerAutoFull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/21 16:54
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class ExpiryGvDetailedBean implements ReportBeanCustomerAutoFull {

    @ExcelProperty(value = "Voucher Number")
    private String voucherNumber;

    @ExcelProperty(value = "Booklet Number")
    private String bookletNumber;

    @ExcelProperty(value = "Voucher Status")
    private String cardStatus;

    @ExcelProperty(value = "Client Name")
    private String customerName;

    @ExcelProperty(value = "Department/Division/Branch")
    private String departmentDivisionBranch;

    @ExcelProperty(value = "Company Name")
    private String companyName;

    @ExcelProperty(value = "First Name")
    private String firstName;

    @ExcelProperty(value = "Last Name")
    private String lastName;

    @ExcelProperty(value = "Email")
    private String email;

    @ExcelProperty(value = "Purchase Date")
    private String purchaseDate;

    @ExcelProperty(value = "Invoice Number")
    private String invoiceNumber;

    @ExcelProperty(value = "Voucher Program Group")
    private String voucherProgramGroup;

    @ReportAmountValue
    @ExcelProperty(value = "Denomination", converter = ExportExcelNumberConverter.class)
    private String denomination;

    @ExcelProperty(value = "Expiry Date")
    private String expiryDate;

    @ExcelProperty(value = "Issuance Year")
    private String issuanceYear;
}
