package com.gtech.gvcore.service.impl;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.request.businesslogdetail.CreateBusinessLogDetailRequest;
import com.gtech.gvcore.common.request.businesslogdetail.QueryBusinessLogDetailRequest;
import com.gtech.gvcore.common.response.businesslogdetail.BusinessLogDetailResponse;
import com.gtech.gvcore.dao.mapper.BusinessLogDetailMapper;
import com.gtech.gvcore.dao.model.BusinessLogDetails;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.BusinessLogDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/3/11 17:35
 */
@Service
public class BusinessLogDetailImpl implements BusinessLogDetailService {

    @Autowired
    private BusinessLogDetailMapper businessLogMapper;
    
    
    @Autowired
    private GvCodeHelper codeHelper;


    @Override
    public int createBusinessLogDetail(CreateBusinessLogDetailRequest request) {
        BusinessLogDetails businessLog = BeanCopyUtils.jsonCopyBean(request, BusinessLogDetails.class);
        businessLog.setBusinessCode(codeHelper.generateBusinessLogDetailCode());
        return businessLogMapper.insertSelective(businessLog);
    }

    @Override
    public int createBusinessLogDetailList(List<CreateBusinessLogDetailRequest> request) {

        List<BusinessLogDetails> businessLogDetails = BeanCopyUtils.jsonCopyList(request, BusinessLogDetails.class);
        for (BusinessLogDetails businessLogDetail : businessLogDetails) {
            businessLogDetail.setBusinessDetailsCode(codeHelper.generateBusinessLogDetailCode());
        }

        return businessLogMapper.insertList(businessLogDetails);
    }

    @Override
    public List<BusinessLogDetailResponse> queryBusinessLogDetail(QueryBusinessLogDetailRequest request) {
        Example example = new Example(BusinessLogDetails.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtil.isNotEmpty(request.getBusinessCode())){
            criteria.andEqualTo(BusinessLogDetails.C_BUSINESS_CODE, request.getBusinessCode());
        }
        List<BusinessLogDetails> businessLogs = businessLogMapper.selectByCondition(example);
        return BeanCopyUtils.jsonCopyList(businessLogs,BusinessLogDetailResponse.class);
    }
}
