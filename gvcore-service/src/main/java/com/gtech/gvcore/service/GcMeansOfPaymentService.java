package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.meansofpayment.CreateMeansOfPaymentRequest;
import com.gtech.gvcore.common.request.meansofpayment.QueryMeansOfPaymentsByPageRequest;
import com.gtech.gvcore.common.request.meansofpayment.UpdateMeansOfPaymentRequest;
import com.gtech.gvcore.common.request.meansofpayment.UpdateMeansOfPaymentStatusRequest;
import com.gtech.gvcore.common.response.meansofpayment.QueryMeansOfPaymentsByPageResponse;
import com.gtech.gvcore.dao.model.GcMeansOfPayment;

import java.util.List;
import java.util.Map;

public interface GcMeansOfPaymentService {

    /**
     * 创建支付方式
     * @param request 创建请求
     * @return 创建结果
     */
    Result<Void> createMeansOfPayment(CreateMeansOfPaymentRequest request);

    /**
     * 更新支付方式
     * @param request 更新请求
     * @return 更新结果
     */
    Result<Void> updateMeansOfPayment(UpdateMeansOfPaymentRequest request);

    /**
     * 更新支付方式状态
     * @param request 状态更新请求
     * @return 更新结果
     */
    Result<Void> updateMeansOfPaymentStatus(UpdateMeansOfPaymentStatusRequest request);

    /**
     * 分页查询支付方式
     * @param request 查询请求
     * @return 查询结果
     */
    PageResult<QueryMeansOfPaymentsByPageResponse> queryMeansOfPaymentsByPage(
            QueryMeansOfPaymentsByPageRequest request);

    /**
     * 根据编码查询支付方式
     * @param meansOfPaymentsCode 支付方式编码
     * @return 支付方式信息
     */
    QueryMeansOfPaymentsByPageResponse getMeansOfPayments(String meansOfPaymentsCode);

    /**
     * 根据编码列表查询支付方式
     * @param meansOfPaymentCodeList 支付方式编码列表
     * @return 支付方式Map
     */
    Map<String, GcMeansOfPayment> queryByCodeList(List<String> meansOfPaymentCodeList);

    /**
     * 根据名称查询支付方式
     * @param meansOfPaymentsName 支付方式名称
     * @return 支付方式信息
     */
    GcMeansOfPayment getMeansOfPaymentsByName(String meansOfPaymentsName);
} 