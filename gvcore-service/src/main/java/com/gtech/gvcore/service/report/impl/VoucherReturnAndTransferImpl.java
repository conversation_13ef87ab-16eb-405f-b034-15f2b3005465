package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.VoucherRequestStatusEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.common.utils.GvConvertUtils;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.UserAccount;
import com.gtech.gvcore.dao.model.VoucherRequestDetails;
import com.gtech.gvcore.helper.GvPageHelper;
import com.gtech.gvcore.service.VoucherRequestDetailsService;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.PollReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.VoucherReturnAndTransferBean;
import com.gtech.gvcore.service.report.impl.bo.VoucherReturnAndTransferBo;
import com.gtech.gvcore.service.report.impl.param.VoucherReturnAndTransferQueryDate;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName VoucherReturnTransferImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/2/14 16:19
 * @Version V1.0
 **/
@Service
public class VoucherReturnAndTransferImpl
extends ReportSupport
implements BusinessReport<VoucherReturnAndTransferQueryDate, VoucherReturnAndTransferBean>, PollReport {

    @Autowired
    private VoucherRequestDetailsService voucherRequestDetailsService;
    // voucher request status
    private static final VoucherRequestStatusEnum[] ENUMS = {
            VoucherRequestStatusEnum.PENDING_APPROVAL
            , VoucherRequestStatusEnum.CANCELED
            , VoucherRequestStatusEnum.REJECTED
            , VoucherRequestStatusEnum.PENDING_ALLOCATION
    };

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        // return report type
        return ReportExportTypeEnum.VOUCHER_RETURN_AND_TRANSFER_REPORT;
    }

    @Override
    public VoucherReturnAndTransferQueryDate builderQueryParam(CreateReportRequest reportParam) {

        // create param
        VoucherReturnAndTransferQueryDate param = new VoucherReturnAndTransferQueryDate();

        // set create time
        param.setCreateTimeStart(reportParam.getTransactionDateStart());
        param.setCreateTimeEnd(reportParam.getTransactionDateEnd());

        // issuer code
        param.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);

        // set request id
        param.setRequestId(reportParam.getVoucherRequestId());

        // set cpg code
        param.setCpgCodeList(reportParam.getCpgCodes());

        // set from store code
        param.setFromStoreCodeList(reportParam.getVoucherReturnTransferFromStoreList());

        // set to store code
        param.setToStoreCodeList(reportParam.getVoucherReturnTransferToStoreList());

        // set status
        List<String> returnTransferStatusList = GvConvertUtils.toObject(reportParam.getVoucherReturnTransferStatusList(), new ArrayList<>());
        param.setVoucherReturnAndTransferStatusList(returnTransferStatusList);
        if (CollectionUtils.containsAny(returnTransferStatusList, "transfer4")) param.setFindPendingTransfer(true);
        if (CollectionUtils.containsAny(returnTransferStatusList, "return4")) param.setFindPendingTransfer(true);

        return param;
    }

    @Override
    public List<VoucherReturnAndTransferBean> getExportData(VoucherReturnAndTransferQueryDate param) {

        // 获取数据
        List<VoucherReturnAndTransferBo> boList = getBoList(param);

        // EMPTY
        if (CollectionUtils.isEmpty(boList)) return new ArrayList<>();

        // 查询关联数据
        JoinDataMap<UserAccount> userMap = super.getMapByCode(boList, VoucherReturnAndTransferBo::getCreateUser, UserAccount.class);
        JoinDataMap<Cpg> cpgMap = super.getMapByCode(boList, VoucherReturnAndTransferBo::getCpgCode, Cpg.class);

        // 转换数据
        return boList.stream()
                // convert
                .map(e -> new VoucherReturnAndTransferBean()
                        // voucher request id
                        .setRequestId(e.getRequestId())
                        // from store name
                        .setFromStoreName(e.getFromStoreName())
                        // to store name
                        .setToStoreName(e.getToStoreName())
                        // cpg
                        .setCpgName(cpgMap.findValue(e.getCpgCode()).getCpgName())
                        // number of voucher
                        .setNumberOfVoucher(String.valueOf(e.getNumberOfVoucher().intValue()))
                        // voucher amount
                        .setVoucherAmount(super.toAmount(e.getVoucherAmount()))
                        // create on
                        .setCreateTime(DateUtil.format(e.getCreateTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_2))
                        // create by
                        .setCreatedBy(e.getCreateBy(userMap))
                        // booklet number
                        .setBookletNumber(e.getBookletNumber())
                        // voucher number
                        .setVoucherNumber(e.getVoucherNumber())
                        // voucher request status
                        .setStatus(e.getStatusDesc())
                ).collect(Collectors.toList());
    }

    /**
     * 获取数据
     * @param param
     * @return
     */
    private List<VoucherReturnAndTransferBo> getBoList(VoucherReturnAndTransferQueryDate param) {

        // 查询数据
        List<VoucherReturnAndTransferBo> selectResult = super.reportBusinessMapper.selectReturnTransfer(param, GvPageHelper.getRowBounds(param));
        if (CollectionUtils.isEmpty(selectResult)) return selectResult;// EMPTY

        // 筛选request数据并查询其明细数据进行合并
        List<VoucherReturnAndTransferBo> requestResult = new ArrayList<>();
        List<VoucherReturnAndTransferBo> requestList = selectResult.stream().filter(e -> StringUtils.isBlank(e.getCpgCode()) || ArrayUtils.contains(ENUMS, VoucherRequestStatusEnum.valueOfCode(e.getStatus()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(requestList)) return selectResult; // EMPTY
        requestList
                .forEach(e -> {
                    // 查询明细数据
                    List<VoucherRequestDetails> voucherRequestDetails = voucherRequestDetailsService.queryByVoucherRequestCode(e.getRequestId());
                    // 合并数据
                    voucherRequestDetails
                            .forEach(d -> requestResult.add(new VoucherReturnAndTransferBo()
                                    .setRequestId(e.getRequestId())
                                    .setFromStoreName(e.getFromStoreName())
                                    .setToStoreName(e.getToStoreName())
                                    .setCpgCode(d.getCpgCode())
                                    .setNumberOfVoucher(ConvertUtils.toBigDecimal(d.getVoucherNum(), BigDecimal.ZERO))
                                    .setVoucherAmount(d.getVoucherAmount())
                                    .setCreateTime(e.getSourceCreateTime())
                                    .setCreateUser(e.getCreateUser())
                                    .setStatus(e.getStatus()))
                            );
                });

        // 移除request数据
        selectResult.removeAll(requestList);

        // 合并数据
        List<VoucherReturnAndTransferBo> boList = new ArrayList<>(selectResult);
        boList.addAll(requestResult);

        // return
        return boList;
    }

}