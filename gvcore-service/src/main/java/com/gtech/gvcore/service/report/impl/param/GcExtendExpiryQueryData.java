package com.gtech.gvcore.service.report.impl.param;

import com.gtech.gvcore.service.report.ReportQueryParam;
import com.gtech.gvcore.service.report.extend.row.TransactionDataPageParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * @ClassName GcExtendExpiryQueryData
 * @Description Gift Card Extend Expiry Query Data
 * <AUTHOR>
 * @Date 2023/5/10 10:36
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GcExtendExpiryQueryData extends TransactionDataPageParam implements ReportQueryParam {

    //transaction date start 交易时间 开始 (对应extension_time)
    private Date transactionDateStart;

    //transaction date end 交易时间 结束 (对应extension_time)
    private Date transactionDateEnd;

    //merchant code list
    private List<String> merchantCodeList;

    //cpg code list
    private List<String> cpgCodeList;

    //issuer code list
    private List<String> issuerCodeList;

    //outlet code list
    private List<String> outletCodeList;

    //source list
    private List<String> sourceList;

    //batch number
    private String batchNumber;

    //approval code
    private String approvalCode;
}
