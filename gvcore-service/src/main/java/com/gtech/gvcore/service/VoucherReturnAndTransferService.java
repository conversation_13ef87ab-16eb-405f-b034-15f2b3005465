package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.releaseapprove.ApproveNodeRecordRequest;
import com.gtech.gvcore.common.request.releaseapprove.ReleaseApproveAbleRequest;
import com.gtech.gvcore.common.request.voucherrequest.CreateVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.UpdateVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherreturnantransfer.QueryVoucherReturnAndTransferRequest;
import com.gtech.gvcore.common.request.voucherreturnantransfer.ReturnAndTransferRequest;
import com.gtech.gvcore.common.response.voucherrequest.QueryVoucherRequestResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/26 14:31
 */


public interface VoucherReturnAndTransferService {
    Result<String> addVoucherRequest(CreateVoucherRequestRequest createVoucherRequestRequest);

	Result<Void> updateVoucherRequest(UpdateVoucherRequestRequest updateVoucherRequestRequest);

    PageResult<QueryVoucherRequestResponse> queryVoucherRequest(QueryVoucherReturnAndTransferRequest queryRequest);

    Result<Boolean> approveVoucherReturnAndTransferAble(ReleaseApproveAbleRequest releaseApproveAbleRequest);

    Result<String> approveVoucherReturnAndTransfer(ApproveNodeRecordRequest approveNodeRecordRequest);

    Result<Void> returnAndTransfer(ReturnAndTransferRequest returnAndTransferRequest);
}
