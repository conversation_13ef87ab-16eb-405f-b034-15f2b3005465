package com.gtech.gvcore.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @ClassName GetCpgQuantityNumParam
 * @Description GetCpgQuantityNumParam
 * <AUTHOR>
 * @Date 2022/7/6 16:00
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GetCpgQuantityNumParam {

    /**
     * CPG 编码
     */
    private String cpgCode;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 时间限制,在beforeTime之前
     */
    private Date beforeTime;

}
