package com.gtech.gvcore.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @ClassName GetCpgDistributedNumParam
 * @Description 获取已分发数量参数
 * <AUTHOR>
 * @Date 2022/7/6 16:06
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GetCpgDistributedNumParam {

    /**
     * CPG 编码
     */
    private String cpgCode;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 时间限制,在beforeTime之前
     */
    private Date beforeTime;

}
