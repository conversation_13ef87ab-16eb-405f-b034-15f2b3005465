package com.gtech.gvcore.dto;

import com.gtech.gvcore.common.request.voucherrequest.QueryVoucherRequestRequest;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName QueryVoucherRequestPermissionDto
 * @Description QueryVoucherRequestRequest 的查询条件权限包装类
 * <AUTHOR>
 * @Date 2022/8/1 10:10
 * @Version V1.0
 **/
@Getter
@Setter
public class QueryVoucherRequestPermissionDto extends QueryVoucherRequestRequest {

    private List<String> outletCodeRangeList;

}
