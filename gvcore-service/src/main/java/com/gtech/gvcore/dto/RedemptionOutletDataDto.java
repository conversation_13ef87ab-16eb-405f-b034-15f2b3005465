package com.gtech.gvcore.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2022/9/7 14:24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RedemptionOutletDataDto {
    private String sbu;

    private BigDecimal amount;

    private BigDecimal voucherCount;
}
