package com.gtech.gvcore.dto;

import com.gtech.gvcore.common.request.voucherbatch.GenerateDigitalVouchersRequest;
import com.gtech.gvcore.dao.model.CustomerOrder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Iterator;

/**
 * <AUTHOR>
 * @Date 2022/12/9 18:02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetCreateVoucherRequestInactivatedDto {

    private GenerateDigitalVouchersRequest request;
    private String voucherBatchCode;
    private CustomerOrder customerOrder;
    private BigDecimal detailBigDecimal;
    private String cpgCode;
    private String issuerCode;
    private String mopCode;
    private Date voucherEffectiveDate;
    private String createUser;
    private Iterator iterator;




    }
