package com.gtech.gvcore.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @ClassName DistributionEmailDataModel
 * @Description 分发邮件模板数据模型
 * <AUTHOR>
 * @Date 2022/8/12 10:37
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class DistributionEmailDataModel {

    private List<EmailVoucherInfo> voucherList;

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class EmailVoucherInfo {

        private String voucherNumber;

        private String activationCode;

        private String voucherExpiryDate;

        private String activationURL;

        private String pinCode;

        private String denomination;

        private String barCode;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class EmailVoucherCodeInfo extends EmailVoucherInfo{

        private String voucherCode;

    }

}
