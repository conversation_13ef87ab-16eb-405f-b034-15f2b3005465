package com.gtech.gvcore.dto;

import com.gtech.gvcore.common.enums.MessageEnventEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName DistributionEmailData
 * @Description DistributionEmailData
 * <AUTHOR>
 * @Date 2022/8/12 13:55
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class DistributionEmailData {

    private String distributionCode;

    private String distributionItemCode;

    private String customerCode;

    private String messageId;

    private MessageEnventEnum event;

    private String emailAddress;

    private String subject;

    private String emailTemplateContent;

    /**
     * 调用发送是否成功
     */
    private boolean sendSuccess;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 邮件模板填充数据
     */
    private DistributionEmailDataModel emailDataModel;

}
