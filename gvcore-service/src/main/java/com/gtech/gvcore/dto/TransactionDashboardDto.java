package com.gtech.gvcore.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/8/29 14:07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TransactionDashboardDto {


    private Date transactionTime;

    private BigDecimal amount;

    private BigDecimal discount;

    private Integer voucherCount;

    private String outletCode;

    private String outletType;

    private String issuerCode;









}
