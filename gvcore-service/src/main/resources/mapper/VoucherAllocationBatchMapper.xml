<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.VoucherAllocationBatchMapper">
  <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.VoucherAllocationBatch">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="voucher_allocation_code" jdbcType="VARCHAR" property="voucherAllocationCode" />
    <result column="cpg_code" jdbcType="VARCHAR" property="cpgCode" />
    <result column="voucher_start_no" jdbcType="VARCHAR" property="voucherStartNo" />
    <result column="voucher_end_no" jdbcType="VARCHAR" property="voucherEndNo" />
    <result column="voucher_num" jdbcType="INTEGER" property="voucherNum" />
    <result column="denomination" jdbcType="DECIMAL" property="denomination" />
    <result column="received_num" jdbcType="INTEGER" property="receivedNum" />
    <result column="booklet_start_no" jdbcType="VARCHAR" property="bookletStartNo" />
    <result column="booklet_end_no" jdbcType="VARCHAR" property="bookletEndNo" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    id, voucher_allocation_code, cpg_code, voucher_start_no, voucher_end_no, voucher_num, 
    denomination, received_num, booklet_start_no, booklet_end_no, create_user, create_time, 
    update_user, update_time
  </sql>
  <sql id="gv_voucher_allocation_batch_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="voucherAllocationCode != null and voucherAllocationCode.trim().length() != 0">
        AND (voucher_allocation_code = #{voucherAllocationCode})
      </if>
      <if test="cpgCode != null and cpgCode.trim().length() != 0">
        AND (cpg_code = #{cpgCode})
      </if>
      <if test="voucherStartNo != null and voucherStartNo.trim().length() != 0">
        AND (voucher_start_no = #{voucherStartNo})
      </if>
      <if test="voucherEndNo != null and voucherEndNo.trim().length() != 0">
        AND (voucher_end_no = #{voucherEndNo})
      </if>
      <if test="voucherNum != null">
        AND (voucher_num = #{voucherNum})
      </if>
      <if test="denomination != null">
        AND (denomination = #{denomination})
      </if>
      <if test="receivedNum != null">
        AND (received_num = #{receivedNum})
      </if>
      <if test="bookletStartNo != null and bookletStartNo.trim().length() != 0">
        AND (booklet_start_no = #{bookletStartNo})
      </if>
      <if test="bookletEndNo != null and bookletEndNo.trim().length() != 0">
        AND (booklet_end_no = #{bookletEndNo})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
    </trim>
  </sql>

  <select id="getAllocateByVoucherCode" resultType="com.gtech.gvcore.dao.model.VoucherAllocationBatch">
    select
    <include refid="Base_Column_List"/>
    from gv_voucher_allocation_batch
    where voucher_start_no &lt;= #{voucherCode} and voucher_end_no &gt;= #{voucherCode}
  </select>

  <select id="selectAllocationBatchByVoucherNums"
          resultType="com.gtech.gvcore.dao.model.VoucherAllocationBatch">

    SELECT
    <include refid="Base_Column_List"/>
    FROM
    gv_voucher_allocation_batch
    WHERE
    voucher_allocation_code IN (
    SELECT
    voucher_allocation_code
    FROM
    gv_voucher_allocation
    WHERE
    voucher_allocation_code IN (
    SELECT
    voucher_allocation_code
    FROM
    gv_voucher_allocation_batch
    WHERE
    <foreach collection="voucherNumVCR" item="voucherNum" separator="OR" open="(" close=")">
      cast(#{voucherNum} AS SIGNED ) &gt;=voucher_start_no and cast(#{voucherNum} AS SIGNED ) &lt;= voucher_end_no
    </foreach>
    )
    AND business_type = 'customerorder'
    );
  </select>


</mapper>