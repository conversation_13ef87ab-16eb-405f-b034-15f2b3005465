<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.VoucherMapper">
  <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.Voucher">
    <!--
      WARNING - @mbg.generated
    -->
<!--    <id column="id" jdbcType="BIGINT" property="id" />-->
    <result column="issuer_code" jdbcType="VARCHAR" property="issuerCode" />
    <result column="voucher_batch_code" jdbcType="VARCHAR" property="voucherBatchCode" />
    <result column="booklet_code" jdbcType="VARCHAR" property="bookletCode" />
    <result column="booklet_code_num" jdbcType="BIGINT" property="bookletCodeNum" />
    <result column="voucher_code" jdbcType="VARCHAR" property="voucherCode" />
    <result column="voucher_code_num" jdbcType="BIGINT" property="voucherCodeNum" />
    <result column="cpg_code" jdbcType="VARCHAR" property="cpgCode" />
    <result column="mop_code" jdbcType="VARCHAR" property="mopCode" />
    <result column="denomination" jdbcType="DECIMAL" property="denomination" />
    <result column="voucher_pin" jdbcType="VARCHAR" property="voucherPin" />
    <result column="voucher_barcode" jdbcType="VARCHAR" property="voucherBarcode" />
    <result column="voucher_effective_date" jdbcType="TIMESTAMP" property="voucherEffectiveDate" />
    <result column="voucher_status" jdbcType="INTEGER" property="voucherStatus" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="circulation_status" jdbcType="INTEGER" property="circulationStatus" />
    <result column="voucher_active_code" jdbcType="VARCHAR" property="voucherActiveCode" />
    <result column="voucher_active_url" jdbcType="VARCHAR" property="voucherActiveUrl" />
    <result column="voucher_used_time" jdbcType="TIMESTAMP" property="voucherUsedTime" />
    <result column="voucher_owner_code" jdbcType="VARCHAR" property="voucherOwnerCode" />
    <result column="voucher_owner_type" jdbcType="VARCHAR" property="voucherOwnerType" />
    <result column="permission_code" jdbcType="VARCHAR" property="permissionCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
   issuer_code, voucher_batch_code, booklet_code, booklet_code_num, voucher_code,
    voucher_code_num, cpg_code, mop_code, denomination, voucher_pin, voucher_barcode, 
    voucher_effective_date, voucher_status, status, circulation_status, voucher_active_code, 
    voucher_active_url, voucher_used_time, voucher_owner_code, voucher_owner_type, permission_code, 
    create_time, update_time, create_user, update_user
  </sql>
  <sql id="gv_voucher_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="issuerCode != null and issuerCode.trim().length() != 0">
        AND (issuer_code = #{issuerCode})
      </if>
      <if test="voucherBatchCode != null and voucherBatchCode.trim().length() != 0">
        AND (voucher_batch_code = #{voucherBatchCode})
      </if>
      <if test="bookletCode != null and bookletCode.trim().length() != 0">
        AND (booklet_code = #{bookletCode})
      </if>
      <if test="bookletCodeNum != null">
        AND (booklet_code_num = #{bookletCodeNum})
      </if>
      <if test="voucherCode != null and voucherCode.trim().length() != 0">
        AND (voucher_code = #{voucherCode})
      </if>
      <if test="voucherCodeNum != null">
        AND (voucher_code_num = #{voucherCodeNum})
      </if>
      <if test="cpgCode != null and cpgCode.trim().length() != 0">
        AND (cpg_code = #{cpgCode})
      </if>
      <if test="mopCode != null and mopCode.trim().length() != 0">
        AND (mop_code = #{mopCode})
      </if>
      <if test="denomination != null">
        AND (denomination = #{denomination})
      </if>
      <if test="voucherPin != null and voucherPin.trim().length() != 0">
        AND (voucher_pin = #{voucherPin})
      </if>
      <if test="voucherBarcode != null and voucherBarcode.trim().length() != 0">
        AND (voucher_barcode = #{voucherBarcode})
      </if>
      <if test="voucherEffectiveDate != null">
        AND (voucher_effective_date = #{voucherEffectiveDate})
      </if>
      <if test="voucherStatus != null">
        AND (voucher_status = #{voucherStatus})
      </if>
      <if test="status != null">
        AND (status = #{status})
      </if>
      <if test="circulationStatus != null">
        AND (circulation_status = #{circulationStatus})
      </if>
      <if test="voucherActiveCode != null and voucherActiveCode.trim().length() != 0">
        AND (voucher_active_code = #{voucherActiveCode})
      </if>
      <if test="voucherActiveUrl != null and voucherActiveUrl.trim().length() != 0">
        AND (voucher_active_url = #{voucherActiveUrl})
      </if>
      <if test="voucherUsedTime != null">
        AND (voucher_used_time = #{voucherUsedTime})
      </if>
      <if test="voucherOwnerCode != null and voucherOwnerCode.trim().length() != 0">
        AND (voucher_owner_code = #{voucherOwnerCode})
      </if>
      <if test="voucherOwnerType != null and voucherOwnerType.trim().length() != 0">
        AND (voucher_owner_type = #{voucherOwnerType})
      </if>
      <if test="permissionCode != null and permissionCode.trim().length() != 0">
        AND (permission_code = #{permissionCode})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
    </trim>
  </sql>

    <select id="queryByVoucherCodeList" parameterType="com.gtech.gvcore.dao.dto.VoucherDto" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM gv_voucher
        WHERE voucher_code IN
        <foreach collection="voucherCodeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="voucherOwnerCode != null">
            AND voucher_owner_code = #{voucherOwnerCode}
        </if>
        <if test="issuerCode != null">
            AND issuer_code = #{issuerCode}
        </if>
    </select>

    <select id="countGroupByDenomination" parameterType="com.gtech.gvcore.dao.dto.VoucherDto"
            resultType="com.gtech.gvcore.dao.dto.DenominationCountDto">
        SELECT denomination, COUNT(1) count
        FROM gv_voucher
        WHERE issuer_code = #{issuerCode}
        AND status = #{status}
        AND voucher_owner_code = #{voucherOwnerCode}
        AND voucher_owner_type = #{voucherOwnerType}
        AND circulation_status = #{circulationStatus}
        AND denomination IN
        <foreach collection="denominationList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY denomination
    </select>

    <select id="countGroupByDenominationAndCirculationStatus" parameterType="list"
            resultType="com.gtech.gvcore.dao.dto.VoucherDto">
        SELECT issuer_code, status, voucher_status, voucher_owner_code, voucher_owner_type, denomination, circulation_status, COUNT(1) count
        FROM gv_voucher
        WHERE
        <foreach collection="list" item="item" separator=" OR ">
            (voucher_code_num BETWEEN #{item.voucherCodeStart} AND #{item.voucherCodeEnd})
        </foreach>
        GROUP BY issuer_code, status, voucher_status, voucher_owner_code, voucher_owner_type, denomination,
        circulation_status
    </select>

    <select id="countGroupByCustomerOrderAllocation" parameterType="list"
            resultType="com.gtech.gvcore.dao.dto.VoucherDto">
        SELECT issuer_code, status, voucher_status, voucher_owner_code, voucher_owner_type, cpg_code,
        circulation_status, COUNT(1) count
        FROM gv_voucher
        WHERE
        <foreach collection="list" item="item" separator=" OR ">
            (voucher_code BETWEEN #{item.voucherCodeStart} AND #{item.voucherCodeEnd})
        </foreach>
        GROUP BY issuer_code, status, voucher_status, voucher_owner_code, voucher_owner_type, cpg_code,
        circulation_status
    </select>

    <select id="queryMismatchByAllocationCountDto" resultMap="BaseResultMap">
        SELECT booklet_code, voucher_code, voucher_barcode, issuer_code, status, voucher_status, voucher_owner_code,
        voucher_owner_type, cpg_code, circulation_status, denomination
        FROM gv_voucher
        WHERE
        <foreach collection="voucherDtoList" item="item" separator=" OR ">
            (issuer_code = #{item.issuerCode} AND status = #{item.status} AND circulation_status =
            #{item.circulationStatus}
            AND voucher_owner_code = #{item.voucherOwnerCode} AND voucher_owner_type = #{item.voucherOwnerType}
            <if test="item.cpgCode != null and item.cpgCode.trim().length() != 0">AND cpg_code = #{item.cpgCode}</if>
            <if test="item.denomination != null">AND denomination = #{item.denomination}</if>
            )
        </foreach>
        ORDER BY id LIMIT #{pageSize}
    </select>

    <select id="groupByDenominationAndCpg" parameterType="com.gtech.gvcore.dao.dto.VoucherDto"
            resultType="com.gtech.gvcore.dao.dto.CpgVoucherCodeNumDto">
            SELECT cpg_code, denomination, MIN(voucher_code_num) voucherCodeNumStart, MAX(voucher_code_num)
            voucherCodeNumEnd
            FROM gv_voucher
            WHERE issuer_code = #{issuerCode}
            AND voucher_code BETWEEN #{voucherCodeStart} AND #{voucherCodeEnd}
            GROUP BY cpg_code, denomination
    </select>

    <select id="queryByVoucherCodeNumList" parameterType="com.gtech.gvcore.dao.dto.VoucherDto"
            resultMap="BaseResultMap">
        SELECT voucher_code, voucher_code_num, booklet_code, booklet_code_num 
        FROM gv_voucher
        WHERE issuer_code = #{issuerCode}
        AND voucher_code IN
        <foreach collection="voucherCodeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="allocateVoucher">
        UPDATE gv_voucher
        SET circulation_status = #{voucherDto.circulationStatus},
        <if test="voucherDto.voucherOwnerCode != null and voucherDto.voucherOwnerCode.trim().length() != 0 and voucherDto.voucherOwnerType != null and voucherDto.voucherOwnerType.trim().length() != 0">
            voucher_owner_code = #{voucherDto.voucherOwnerCode},
            voucher_owner_type = #{voucherDto.voucherOwnerType},
        </if>
        update_time = #{voucherDto.updateTime}
        WHERE issuer_code = #{voucherDto.issuerCode}
        AND circulation_status = #{voucherDto.oldCirculationStatus}
        AND status = #{voucherDto.status}
        AND voucher_owner_code = #{voucherDto.oldVoucherOwnerCode}
        AND voucher_status = #{voucherDto.voucherStatus}
        AND voucher_code BETWEEN #{cpgVoucherCodeNum.voucherCodeStart} AND #{cpgVoucherCodeNum.voucherCodeEnd}
        <if test="voucherDto.voucherEffectiveDate != null">
            AND voucher_effective_date &gt; #{voucherDto.voucherEffectiveDate} 
        </if>
    </update>

    <update id="activatPhysicalVoucherByCustomerOrder">
    UPDATE gv_voucher 
       SET voucher_owner_code = #{voucherDto.voucherOwnerCode},
           voucher_owner_type = #{voucherDto.voucherOwnerType},
<!--            status = #{voucherDto.status},-->
           update_time = #{voucherDto.updateTime},
        sales_time = #{voucherDto.salesTime},
        sales_outlet = #{voucherDto.salesOutlet}

    WHERE issuer_code = #{voucherDto.issuerCode}
      AND circulation_status = #{voucherDto.circulationStatus}
      AND status = #{voucherDto.oldStatus}
      AND voucher_owner_code = #{voucherDto.oldVoucherOwnerCode}
      AND voucher_status = #{voucherDto.voucherStatus}
      AND
      <foreach collection="voucherCodeNumLlist" item="item" open="(" separator=" OR " close=")">
        (voucher_code BETWEEN #{item.voucherCodeStart} AND #{item.voucherCodeEnd})
      </foreach>
      <if test="voucherDto.voucherEffectiveDate != null">
        AND voucher_effective_date &gt; #{voucherDto.voucherEffectiveDate} 
      </if>
  </update>



    <update id="cancelSales">
        update gv_voucher set
        <if test="voucherStatus != null">
        	voucher_status=#{voucherStatus},
        </if>
        <if test="circulationStatus != null">
        	circulation_status=#{circulationStatus},
        </if>
        <if test="voucherOwnerCode != null">
        voucher_owner_code=#{voucherOwnerCode},
        </if>
        <if test="voucherOwnerType != null">
        voucher_owner_type=#{voucherOwnerType},
        </if>
        <if test="status != null">
            `status`= #{status},
        </if>
        sales_time = null,
        sales_outlet = null,
        update_time = update_time

        where voucher_code in
        <foreach collection="voucherCodeList" item="voucherCode" open="(" separator="," close=")">
            #{voucherCode}
        </foreach>
    </update>


    <update id="updateVoucherStatusByTableName">
        update ${tableName} set
        <if test="voucherStatus != null">
            voucher_status=#{voucherStatus},
        </if>
        <if test="circulationStatus != null">
            circulation_status=#{circulationStatus},
        </if>
        <if test="voucherOwnerCode != null">
            voucher_owner_code=#{voucherOwnerCode},
        </if>
        <if test="voucherOwnerType != null">
            voucher_owner_type=#{voucherOwnerType},
        </if>
        <if test="status != null">
            `status`= #{status},
        </if>

        update_time = update_time
        ,sales_time = null,
        sales_outlet = null



        where voucher_code in
        <foreach collection="voucherCodeList" item="voucherCode" open="(" separator="," close=")">
            #{voucherCode}
        </foreach>
    </update>
    
    <select id="latestGvStatusReport" parameterType="com.gtech.gvcore.service.report.impl.param.LatestGvStatusQueryData" resultMap="BaseResultMap">
        SELECT voucher.voucher_code, voucher.booklet_code, voucher.cpg_code, voucher.voucher_owner_code, voucher.denomination, 
            voucher.voucher_status, voucher.status, voucher.circulation_status, voucher.voucher_effective_date, voucher.create_time 
        FROM gv_voucher voucher 
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test="cpgCodeList != null and cpgCodeList.size() != 0">
                AND cpg_code IN
                <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="(voucherStatusList != null and voucherStatusList.size() != 0) or findExpired or findDeactivated">
                AND (
                    1 != 1
                    <if test="voucherStatusList != null and voucherStatusList.size() != 0">
                        OR voucher.status
                        <foreach collection="voucherStatusList" item="item" open=" IN (" separator="," close=") ">
                            #{item}
                        </foreach>
                    </if>
                    <if test="findExpired"> OR (voucher.voucher_effective_date <![CDATA[ < ]]> #{now} AND voucher.voucher_status = 1) </if>
                    <if test="findDeactivated"> OR voucher.voucher_status = 0 </if>
                )
            </if>
            <if test="voucherCodeNumStart != null"> AND voucher.voucher_code_num <![CDATA[ >= ]]> #{voucherCodeNumStart} </if>
            <if test="voucherCodeNumEnd != null"> AND voucher.voucher_code_num <![CDATA[ <= ]]> #{voucherCodeNumEnd} </if>
            <if test="voucherCodeList != null and voucherCodeList.size() != 0">
                AND voucher.voucher_code IN
                <foreach collection="voucherCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </trim>
        ORDER BY voucher.voucher_owner_code, voucher.cpg_code, voucher.voucher_code
    </select>

    <select id="queryByVoucherCodeNumRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM gv_voucher
        <where>

            <if test="(voucherCode == null or voucherCode == '') and voucherCodeNumStart == null and voucherCodeNumEnd == null">
            <!-- 都没有不允许命中数据 -->
                AND 1 = 0
            </if>
            <if test="voucherCode != null and voucherCode != '' ">
                AND voucher_code_num = #{voucherCode}
            </if>
            <if test="voucherCodeNumStart != null and voucherCodeNumEnd == null">
                <!-- voucherCodeNumStart -->
                AND voucher_code = #{voucherCodeNumStart}
            </if>
            <if test="voucherCodeNumStart == null and voucherCodeNumEnd != null">
                <!-- voucherCodeNumEnd -->
                AND voucher_code = #{voucherCodeNumEnd}
            </if>
            <if test="voucherCodeNumStart != null and voucherCodeNumEnd != null">
                AND voucher_code_num <![CDATA[ >= ]]> #{voucherCodeNumStart}
                AND voucher_code_num <![CDATA[ <= ]]> #{voucherCodeNumEnd}
            </if>
            <if test="issuerCodeList != null and issuerCodeList.size() != 0">
                AND issuer_code IN
                <foreach collection="issuerCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

        </where>

        ORDER BY voucher_code
    </select>

    <select id="queryCpgCodeCountByVoucherBatchCode" parameterType="string"
            resultType="com.gtech.gvcore.dto.CpgCodeCount">
        SELECT voucher_code,cpg_code cpgCode, COUNT(1) count
        FROM gv_voucher
        WHERE voucher_batch_code = #{voucherBatchCode}
        GROUP BY voucher_code,cpg_code
    </select>

    <select id="selectVoucherBatchCodesByVoucherNums" resultType="java.lang.String">
        select voucher_batch_code
        from gv_voucher
        where voucher_code_num in
        <foreach collection="voucherNumVCE" item="voucherNum" separator="," open="(" close=")">
            #{voucherNum}
        </foreach>
        group by voucher_batch_code
    </select>
    
    <update id="receiveDigitalVoucherByCustomerOrder" parameterType="com.gtech.gvcore.dao.dto.VoucherDto">
        UPDATE gv_voucher_${index}
        SET circulation_status = #{dto.circulationStatus},
            update_user        = #{dto.updateUser},
        sales_time = #{dto.salesTime},
        sales_outlet = #{dto.salesOutlet}
        <if test="dto.status != null and dto.status != ''">
            ,`status` = #{dto.status}
        </if>
        WHERE voucher_batch_code = #{dto.voucherBatchCode}
          AND status = #{dto.oldStatus}
          AND voucher_owner_code = #{dto.voucherOwnerCode}
          AND voucher_owner_type = #{dto.voucherOwnerType}
          AND voucher_status = #{dto.voucherStatus}
          AND circulation_status = #{dto.oldCirculationStatus}
          <if test="dto.voucherEffectiveDate != null">
            AND voucher_effective_date &gt; #{dto.voucherEffectiveDate}
          </if>
        AND voucher_code in
        <foreach collection="dto.voucherCodeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    
    <update id="activatDigitalVoucherByCustomerOrder" parameterType="com.gtech.gvcore.dao.dto.VoucherDto">
        UPDATE gv_voucher 
           SET status = #{status},
<!--               update_time = #{updateTime},-->
               update_user = #{updateUser} 
        WHERE voucher_batch_code = #{voucherBatchCode} 
          AND status = #{oldStatus}
          AND voucher_owner_code = #{voucherOwnerCode}
          AND voucher_owner_type = #{voucherOwnerType}
          AND voucher_status = #{voucherStatus}
          AND cpg_code IN
          <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
            #{item}
          </foreach>
    </update>

    <select id="queryByVoucherCodeListByTableName" resultMap="BaseResultMap">
        SELECT
        issuer_code, voucher_batch_code, booklet_code, booklet_code_num, voucher_code,
        voucher_code_num, cpg_code, mop_code, denomination, voucher_pin, voucher_barcode,
        voucher_effective_date, voucher_status, status, circulation_status, voucher_active_code,
        voucher_active_url, voucher_used_time, voucher_owner_code, voucher_owner_type, permission_code,
        create_time, update_time, create_user, update_user
        FROM ${tableName}
        WHERE voucher_code IN
        <foreach collection="voucherCodeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>

    <update id="cancelSalesByStartAndEnd">
        update gv_voucher set
        <if test="voucherStatus != null">
            voucher_status=#{voucherStatus},
        </if>
        <if test="circulationStatus != null">
            circulation_status=#{circulationStatus},
        </if>
        <if test="voucherOwnerCode != null">
            voucher_owner_code=#{voucherOwnerCode},
        </if>
        <if test="voucherOwnerType != null">
            voucher_owner_type=#{voucherOwnerType},
        </if>
        <if test="status != null">
            `status`= #{status},
        </if>
            update_time = update_time
        <if test=" status != null and status == 0">
            ,sales_time = null,
            sales_outlet = null
        </if>
        WHERE
        voucher_code BETWEEN #{startVoucherCode} AND #{endVoucherCode}
    </update>




        <update id="setUsedOrSalesNull" parameterType="map">
            UPDATE gv_voucher
            <set>
                <if test="record.status != null">
                    status = #{record.status},
                </if>

                <if test="type == 0">
                    used_outlet = NULL,
                    used_time = NULL,
                </if>
                <if test="type == 1">
                    sales_time = NULL,
                    sales_outlet = NULL,
                </if>
            </set>
            WHERE
            <foreach collection="example.oredCriteria" item="criteria" separator="AND">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="AND | OR">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    AND ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    AND ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    AND ${criterion.condition} #{criterion.value} AND #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    AND ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </update>

    <update id="updateVoucherStatusByTable" parameterType="com.gtech.gvcore.common.request.voucher.UpdateVoucherStatusRequest">
        UPDATE gv_voucher_${index}
        <set>
            <if test="request.status != null">
                status = #{request.status},
            </if>
            <if test="request.circulationStatus != null">
                circulation_status = #{request.circulationStatus},
            </if>
            <if test="request.voucherStatus != null">
                voucher_status = #{request.voucherStatus},
            </if>
            <if test="request.updateUser != null and request.updateUser != ''">
                update_user = #{request.updateUser},
            </if>
            <if test="request.salesOutlet != null and request.salesOutlet != ''">
                sales_outlet = #{request.salesOutlet},
            </if>
            <if test="request.salesTime != null">
                sales_time = #{request.salesTime},
            </if>
            <if test="request.usedOutlet != null and request.usedOutlet != ''">
                used_outlet = #{request.usedOutlet},
            </if>
            <if test="request.usedTime != null">
                used_time = #{request.usedTime},
            </if>
        </set>
        <where>
            <if test="request.voucherStartNo != null and request.voucherStartNo != ''">
                voucher_code &gt;= #{request.voucherStartNo}
            </if>
            <if test="request.voucherEndNo != null and request.voucherEndNo != ''">
                AND voucher_code &lt;= #{request.voucherEndNo}
            </if>
            <if test="request.voucherCode != null and request.voucherCode != ''">
                AND voucher_code = #{request.voucherCode}
            </if>
            <if test="request.oldStatus != null">
                AND status = #{request.oldStatus}
            </if>
            <if test="request.oldCirculationStatus != null">
                AND circulation_status = #{request.oldCirculationStatus}
            </if>
            <if test="request.oldVoucherStatus != null">
                AND voucher_status = #{request.oldVoucherStatus}
            </if>
            <if test="request.voucherCodeList != null and !request.voucherCodeList.isEmpty()">
                AND voucher_code IN
                <foreach item="code" collection="request.voucherCodeList" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
        </where>
    </update>




</mapper>