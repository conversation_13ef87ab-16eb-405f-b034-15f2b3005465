<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.VoucherAllocationMapper">
  <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.VoucherAllocation">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="voucher_allocation_code" jdbcType="VARCHAR" property="voucherAllocationCode" />
    <result column="source_data_code" jdbcType="VARCHAR" property="sourceDataCode" />
    <result column="issuer_code" jdbcType="VARCHAR" property="issuerCode" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="voucher_owner_code" jdbcType="VARCHAR" property="voucherOwnerCode" />
    <result column="voucher_owner_name" jdbcType="VARCHAR" property="voucherOwnerName" />
    <result column="receiver_code" jdbcType="VARCHAR" property="receiverCode" />
    <result column="receiver_name" jdbcType="VARCHAR" property="receiverName" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="permission_code" jdbcType="VARCHAR" property="permissionCode" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    id, voucher_allocation_code, source_data_code, issuer_code, business_type, voucher_owner_code, 
    voucher_owner_name, receiver_code, receiver_name, status, permission_code, create_user, 
    create_time, update_user, update_time
  </sql>
  <sql id="gv_voucher_allocation_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="voucherAllocationCode != null and voucherAllocationCode.trim().length() != 0">
        AND (voucher_allocation_code = #{voucherAllocationCode})
      </if>
      <if test="sourceDataCode != null and sourceDataCode.trim().length() != 0">
        AND (source_data_code = #{sourceDataCode})
      </if>
      <if test="issuerCode != null and issuerCode.trim().length() != 0">
        AND (issuer_code = #{issuerCode})
      </if>
      <if test="businessType != null and businessType.trim().length() != 0">
        AND (business_type = #{businessType})
      </if>
      <if test="voucherOwnerCode != null and voucherOwnerCode.trim().length() != 0">
        AND (voucher_owner_code = #{voucherOwnerCode})
      </if>
      <if test="voucherOwnerName != null and voucherOwnerName.trim().length() != 0">
        AND (voucher_owner_name = #{voucherOwnerName})
      </if>
      <if test="receiverCode != null and receiverCode.trim().length() != 0">
        AND (receiver_code = #{receiverCode})
      </if>
      <if test="receiverName != null and receiverName.trim().length() != 0">
        AND (receiver_name = #{receiverName})
      </if>
      <if test="status != null">
        AND (status = #{status})
      </if>
      <if test="permissionCode != null and permissionCode.trim().length() != 0">
        AND (permission_code = #{permissionCode})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
    </trim>
  </sql>
  
  <update id="updateStatus" parameterType="com.gtech.gvcore.dao.dto.VoucherAllocationDto">
  	UPDATE gv_voucher_allocation 
  	SET status = #{status},
  		update_user = #{updateUser},
  		update_time = #{updateTime} 
  	WHERE voucher_allocation_code = #{voucherAllocationCode}
  	  AND status = #{oldStatus}
  </update>

    <select id="queryVoucherAllocationByPage" parameterType="com.gtech.gvcore.dto.VoucherAllocationPermissionDto"
            resultMap="BaseResultMap">
        SELECT allocation.voucher_allocation_code, allocation.source_data_code, allocation.receiver_code,
        allocation.create_time
        FROM gv_voucher_allocation allocation
        <if test="cpgCode != null and cpgCode.trim().length() != 0">
            LEFT JOIN gv_voucher_request_details requestdetails ON allocation.source_data_code =
            requestdetails.voucher_request_code
        </if>
<!--        <if test="cpgCode != null  and cpgCode.trim().length() != 0">
            LEFT JOIN gv_voucher_allocation_batch allocateDetails ON allocateDetails.voucher_allocation_code =
            allocation.voucher_allocation_code

        </if>-->
        WHERE allocation.business_type = #{businessType}
        AND issuer_code = #{issuerCode}
        AND voucher_owner_code IN
        <foreach collection="outletCodeRangeList" item="outletCode" open="(" separator="," close=")">
            #{outletCode}
        </foreach>
        <if test="receiverCode != null and receiverCode.trim().length() != 0">
            AND receiver_code = #{receiverCode}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="cpgCode != null and cpgCode.trim().length() != 0">
            AND requestdetails.cpg_code = #{cpgCode}
        </if>
        <if test="denomination != null">
            AND requestdetails.denomination = #{denomination}
            <!-- GROUP BY allocation.voucher_allocation_code, allocation.voucher_request_code, allocation.outlet_code, allocation.create_time  -->
        </if>
        ORDER BY allocation.create_time DESC
    </select>

    <select id="queryByVoucherAllocationCodeList" parameterType="list" resultMap="BaseResultMap">
  	SELECT <include refid="Base_Column_List" /> 
    FROM gv_voucher_allocation 
    WHERE voucher_allocation_code IN
    <foreach collection="list" item="item" open="(" separator="," close=")">
    	#{item}
    </foreach>
    ORDER BY create_time DESC 
  </select>
  
</mapper>