<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.VoucherReceiveRecordMapper">
  <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.VoucherReceiveRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="voucher_receive_code" jdbcType="VARCHAR" property="voucherReceiveCode" />
    <result column="cpg_code" jdbcType="VARCHAR" property="cpgCode" />
    <result column="voucher_start_no" jdbcType="VARCHAR" property="voucherStartNo" />
    <result column="voucher_end_no" jdbcType="VARCHAR" property="voucherEndNo" />
    <result column="denomination" jdbcType="DECIMAL" property="denomination" />
    <result column="received_num" jdbcType="INTEGER" property="receivedNum" />
    <result column="booklet_start_no" jdbcType="VARCHAR" property="bookletStartNo" />
    <result column="booklet_end_no" jdbcType="VARCHAR" property="bookletEndNo" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    id, voucher_receive_code, cpg_code, voucher_start_no, voucher_end_no, denomination, 
    received_num, booklet_start_no, booklet_end_no, create_user, create_time, update_user, 
    update_time
  </sql>
  <sql id="gv_voucher_receive_record_query_fuzzy_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="voucherReceiveCode != null and voucherReceiveCode.trim().length() != 0">
         AND (voucher_receive_code like concat('%',#{voucherReceiveCode},'%')) 
      </if>
      <if test="cpgCode != null and cpgCode.trim().length() != 0">
         AND (cpg_code like concat('%',#{cpgCode},'%')) 
      </if>
      <if test="voucherStartNo != null and voucherStartNo.trim().length() != 0">
         AND (voucher_start_no like concat('%',#{voucherStartNo},'%')) 
      </if>
      <if test="voucherEndNo != null and voucherEndNo.trim().length() != 0">
         AND (voucher_end_no like concat('%',#{voucherEndNo},'%')) 
      </if>
      <if test="denomination != null">
        AND (denomination = #{denomination})
      </if>
      <if test="receivedNum != null">
        AND (received_num = #{receivedNum})
      </if>
      <if test="bookletStartNo != null and bookletStartNo.trim().length() != 0">
         AND (booklet_start_no like concat('%',#{bookletStartNo},'%')) 
      </if>
      <if test="bookletEndNo != null and bookletEndNo.trim().length() != 0">
         AND (booklet_end_no like concat('%',#{bookletEndNo},'%')) 
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
         AND (create_user like concat('%',#{createUser},'%')) 
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
         AND (update_user like concat('%',#{updateUser},'%')) 
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
    </trim>
  </sql>
  <sql id="gv_voucher_receive_record_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="voucherReceiveCode != null and voucherReceiveCode.trim().length() != 0">
        AND (voucher_receive_code = #{voucherReceiveCode})
      </if>
      <if test="cpgCode != null and cpgCode.trim().length() != 0">
        AND (cpg_code = #{cpgCode})
      </if>
      <if test="voucherStartNo != null and voucherStartNo.trim().length() != 0">
        AND (voucher_start_no = #{voucherStartNo})
      </if>
      <if test="voucherEndNo != null and voucherEndNo.trim().length() != 0">
        AND (voucher_end_no = #{voucherEndNo})
      </if>
      <if test="denomination != null">
        AND (denomination = #{denomination})
      </if>
      <if test="receivedNum != null">
        AND (received_num = #{receivedNum})
      </if>
      <if test="bookletStartNo != null and bookletStartNo.trim().length() != 0">
        AND (booklet_start_no = #{bookletStartNo})
      </if>
      <if test="bookletEndNo != null and bookletEndNo.trim().length() != 0">
        AND (booklet_end_no = #{bookletEndNo})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
    </trim>
  </sql>
  <select id="query" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
     from gv_voucher_receive_record 
    <if test="fuzzy">
      <include refid="gv_voucher_receive_record_query_fuzzy_condition" />
    </if>
    <if test="!fuzzy">
      <include refid="gv_voucher_receive_record_query_condition" />
    </if>
     order by id desc 
  </select>
  
  <select id="queryReceiveRecord" resultType="com.gtech.gvcore.dao.dto.VoucherReceiveDto">
    select 
    a.voucher_receive_code as voucherReceiveCode,
    a.cpg_code as cpgCode,
    a.voucher_start_no as voucherStartNo,
    a.voucher_end_no as voucherEndNo,
    a.denomination,
    a.received_num as receivedNum,
    a.booklet_start_no as bookletStartNo,
    a.booklet_end_no as bookletEndNo,
    a.create_time as createTime,
    b.source_data_code as sourceDataCode,
    b.issuer_code as issuerCode,
    b.source_type as sourceType,
    c.purchase_order_no as purchaseOrderNo,
	e.article_code as articleCode
     from gv_voucher_receive_record a
     join gv_voucher_receive b on a.voucher_receive_code = b.voucher_receive_code
     	and b.source_type = #{sourceType}
   	 left join gv_voucher_batch c on c.voucher_batch_code = b.source_data_code
	 left join gv_cpg d on a.cpg_code = d.cpg_code
	 left join gv_article_mop e on e.article_mop_code = d.article_mop_code
     where date_format(a.create_time, '%Y-%m-%d') = date_format(#{createTime}, '%Y-%m-%d')
      <if test="issuerCode != null and issuerCode.trim().length() != 0">
        AND b.issuer_code = #{issuerCode}
      </if>
     order by a.id desc 
  </select>
</mapper>