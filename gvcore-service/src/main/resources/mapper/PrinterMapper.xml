<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.PrinterMapper">
  <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.Printer">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="printer_code" jdbcType="VARCHAR" property="printerCode" />
    <result column="printer_name" jdbcType="VARCHAR" property="printerName" />
    <result column="issuer_code" jdbcType="VARCHAR" property="issuerCode" />
    <result column="state_code" jdbcType="VARCHAR" property="stateCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="longitude" jdbcType="VARCHAR" property="longitude" />
    <result column="latitude" jdbcType="VARCHAR" property="latitude" />
    <result column="first_name" jdbcType="VARCHAR" property="firstName" />
    <result column="last_name" jdbcType="VARCHAR" property="lastName" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="receiving_method" jdbcType="VARCHAR" property="receivingMethod" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="ftp_authorization_type" jdbcType="VARCHAR" property="ftpAuthorizationType" />
    <result column="ftp_url" jdbcType="VARCHAR" property="ftpUrl" />
    <result column="ftp_username" jdbcType="VARCHAR" property="ftpUsername" />
    <result column="ftp_password" jdbcType="VARCHAR" property="ftpPassword" />
    <result column="ftp_key_file_url" jdbcType="VARCHAR" property="ftpKeyFileUrl" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.gtech.gvcore.dao.model.Printer">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="ftp_key_file" jdbcType="LONGVARBINARY" property="ftpKeyFile" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    id, printer_code, printer_name, issuer_code, state_code, city_code, district_code, 
    address, longitude, latitude, first_name, last_name, mobile, receiving_method, email, 
    ftp_authorization_type, ftp_url, ftp_username, ftp_password, ftp_key_file_url, status, 
    create_user, create_time, update_user, update_time
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    ftp_key_file
  </sql>
  <sql id="gv_printer_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="printerCode != null and printerCode.trim().length() != 0">
        AND (printer_code = #{printerCode})
      </if>
      <if test="printerName != null and printerName.trim().length() != 0">
        AND (printer_name = #{printerName})
      </if>
      <if test="issuerCode != null and issuerCode.trim().length() != 0">
        AND (issuer_code = #{issuerCode})
      </if>
      <if test="stateCode != null and stateCode.trim().length() != 0">
        AND (state_code = #{stateCode})
      </if>
      <if test="cityCode != null and cityCode.trim().length() != 0">
        AND (city_code = #{cityCode})
      </if>
      <if test="districtCode != null and districtCode.trim().length() != 0">
        AND (district_code = #{districtCode})
      </if>
      <if test="address != null and address.trim().length() != 0">
        AND (address = #{address})
      </if>
      <if test="longitude != null and longitude.trim().length() != 0">
        AND (longitude = #{longitude})
      </if>
      <if test="latitude != null and latitude.trim().length() != 0">
        AND (latitude = #{latitude})
      </if>
      <if test="firstName != null and firstName.trim().length() != 0">
        AND (first_name = #{firstName})
      </if>
      <if test="lastName != null and lastName.trim().length() != 0">
        AND (last_name = #{lastName})
      </if>
      <if test="mobile != null and mobile.trim().length() != 0">
        AND (mobile = #{mobile})
      </if>
      <if test="receivingMethod != null and receivingMethod.trim().length() != 0">
        AND (receiving_method = #{receivingMethod})
      </if>
      <if test="email != null and email.trim().length() != 0">
        AND (email = #{email})
      </if>
      <if test="ftpAuthorizationType != null and ftpAuthorizationType.trim().length() != 0">
        AND (ftp_authorization_type = #{ftpAuthorizationType})
      </if>
      <if test="ftpUrl != null and ftpUrl.trim().length() != 0">
        AND (ftp_url = #{ftpUrl})
      </if>
      <if test="ftpUsername != null and ftpUsername.trim().length() != 0">
        AND (ftp_username = #{ftpUsername})
      </if>
      <if test="ftpPassword != null and ftpPassword.trim().length() != 0">
        AND (ftp_password = #{ftpPassword})
      </if>
      <if test="ftpKeyFileUrl != null and ftpKeyFileUrl.trim().length() != 0">
        AND (ftp_key_file_url = #{ftpKeyFileUrl})
      </if>
      <if test="status != null">
        AND (status = #{status})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
    </trim>
  </sql>
  
  <select id="queryBasicInfoByPrinterCodeList" parameterType="list" resultMap="BaseResultMap">
  	SELECT printer_code, printer_name 
  	FROM gv_printer 
  	WHERE printer_code IN
  	<foreach collection="list" item="item" open="(" separator="," close=")">
  		#{item}
  	</foreach>
  </select>
  
  <select id="countByPrinterCodeList" parameterType="com.gtech.gvcore.dao.dto.PrinterDto" resultType="int">
  	SELECT COUNT(1) 
  	FROM gv_printer 
  	WHERE printer_code IN
  	<foreach collection="printerCodeList" item="item" open="(" separator="," close=")">
  		#{item}
  	</foreach>
  	  <!--AND issuer_code = #{issuerCode} -->
  </select>
  
</mapper>