<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.CustomerOrderDetailsMapper">
  <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.CustomerOrderDetails">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="customer_order_details_code" jdbcType="VARCHAR" property="customerOrderDetailsCode" />
    <result column="customer_order_code" jdbcType="VARCHAR" property="customerOrderCode" />
    <result column="cpg_code" jdbcType="VARCHAR" property="cpgCode" />
    <result column="voucher_num" jdbcType="INTEGER" property="voucherNum" />
    <result column="denomination" jdbcType="DECIMAL" property="denomination" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="discount" jdbcType="DECIMAL" property="discount" />
    <result column="voucher_amount" jdbcType="DECIMAL" property="voucherAmount" />
    <result column="delete_status" jdbcType="INTEGER" property="deleteStatus" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    id, customer_order_details_code, customer_order_code, cpg_code, voucher_num, denomination, 
    amount, discount, voucher_amount, delete_status, create_user, create_time, update_user, 
    update_time
  </sql>
  <sql id="gv_customer_order_details_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="customerOrderDetailsCode != null and customerOrderDetailsCode.trim().length() != 0">
        AND (customer_order_details_code = #{customerOrderDetailsCode})
      </if>
      <if test="customerOrderCode != null and customerOrderCode.trim().length() != 0">
        AND (customer_order_code = #{customerOrderCode})
      </if>
      <if test="cpgCode != null and cpgCode.trim().length() != 0">
        AND (cpg_code = #{cpgCode})
      </if>
      <if test="voucherNum != null">
        AND (voucher_num = #{voucherNum})
      </if>
      <if test="denomination != null">
        AND (denomination = #{denomination})
      </if>
      <if test="amount != null">
        AND (amount = #{amount})
      </if>
      <if test="discount != null">
        AND (discount = #{discount})
      </if>
      <if test="voucherAmount != null">
        AND (voucher_amount = #{voucherAmount})
      </if>
      <if test="deleteStatus != null">
        AND (delete_status = #{deleteStatus})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
    </trim>
  </sql>
  
</mapper>