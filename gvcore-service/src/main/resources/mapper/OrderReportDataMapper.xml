<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.OrderReportDataMapper">
    <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.OrderReportData">
        <!--@mbg.generated-->
        <!--@Table `gv_order_report_data`-->
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="order_report_code" jdbcType="VARCHAR" property="orderReportCode" />
        <result column="report_type" jdbcType="INTEGER" property="reportType" />
        <result column="report_index" jdbcType="INTEGER" property="reportIndex" />
        <result column="report_data" jdbcType="VARCHAR" property="reportData" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        `id`, `order_report_code`, `report_type`, `report_index`, `report_data`, `create_time`
    </sql>
    <!--<select id="selectOne" resultMap="BaseResultMap">
        &lt;!&ndash;@mbg.generated&ndash;&gt;
        select
        <include refid="Base_Column_List" />
        from gv_order_report_data_#{index}
        where `order_report_code` = #{data.orderReportCode}
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.gtech.gvcore.dao.model.OrderReportData" useGeneratedKeys="true">
        &lt;!&ndash;@mbg.generated&ndash;&gt;
        insert into gv_order_report_data_#{index}
        (`order_report_code`, `report_type`, `report_index`,
        `report_data`, `create_time`)
        values (#{data.orderReportCode,jdbcType=VARCHAR}, #{data.reportType,jdbcType=INTEGER}, #{data.reportIndex,jdbcType=INTEGER},
        #{data.reportData,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.gtech.gvcore.dao.model.OrderReportData" useGeneratedKeys="true">
        &lt;!&ndash;@mbg.generated&ndash;&gt;
        insert into gv_order_report_data_#{index}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="data.orderReportCode != null and data.orderReportCode != ''">
                `order_report_code`,
            </if>
            <if test="data.reportType != null">
                `report_type`,
            </if>
            <if test="data.reportIndex != null">
                `report_index`,
            </if>
            <if test="data.reportData != null and data.reportData != ''">
                `report_data`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="data.orderReportCode != null and data.orderReportCode != ''">
                #{data.orderReportCode,jdbcType=VARCHAR},
            </if>
            <if test="data.reportType != null">
                #{data.reportType,jdbcType=INTEGER},
            </if>
            <if test="data.reportIndex != null">
                #{data.reportIndex,jdbcType=INTEGER},
            </if>
            <if test="data.reportData != null and data.reportData != ''">
                #{data.reportData,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.gtech.gvcore.dao.model.OrderReportData">
        &lt;!&ndash;@mbg.generated&ndash;&gt;
        update `gv_order_report_data`
        <set>
            <if test="orderReportCode != null and orderReportCode != ''">
                `order_report_code` = #{orderReportCode,jdbcType=VARCHAR},
            </if>
            <if test="reportType != null">
                `report_type` = #{reportType,jdbcType=INTEGER},
            </if>
            <if test="reportIndex != null">
                `report_index` = #{reportIndex,jdbcType=INTEGER},
            </if>
            <if test="reportData != null and reportData != ''">
                `report_data` = #{reportData,jdbcType=VARCHAR},
            </if>

        </set>
        where `id` = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.gtech.gvcore.dao.model.OrderReportData">
        &lt;!&ndash;@mbg.generated&ndash;&gt;
        update `gv_order_report_data`
        set `order_report_code` = #{orderReportCode,jdbcType=VARCHAR},
        `report_type` = #{reportType,jdbcType=INTEGER},
        `report_index` = #{reportIndex,jdbcType=INTEGER},
        `report_data` = #{reportData,jdbcType=VARCHAR}
        where `id` = #{id,jdbcType=INTEGER}
    </update>-->
</mapper>