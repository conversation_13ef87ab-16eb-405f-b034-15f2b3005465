<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.ProductCategoryDisscountDetailsMapper">
  <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.ProductCategoryDisscountDetails">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="product_category_disscount_details_code" jdbcType="VARCHAR" property="productCategoryDisscountDetailsCode" />
    <result column="product_category_disscount_code" jdbcType="VARCHAR" property="productCategoryDisscountCode" />
    <result column="from_purchase_value" jdbcType="DECIMAL" property="fromPurchaseValue" />
    <result column="upto_purchase_value" jdbcType="DECIMAL" property="uptoPurchaseValue" />
    <result column="discount_type" jdbcType="VARCHAR" property="discountType" />
    <result column="discount" jdbcType="DECIMAL" property="discount" />
    <result column="maximum_discount_value" jdbcType="DECIMAL" property="maximumDiscountValue" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="delete_status" jdbcType="INTEGER" property="deleteStatus" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    id, product_category_disscount_details_code, product_category_disscount_code, from_purchase_value, 
    upto_purchase_value, discount_type, discount, maximum_discount_value, status, delete_status, 
    create_user, create_time, update_user, update_time
  </sql>
  <sql id="gv_product_category_disscount_details_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="productCategoryDisscountDetailsCode != null and productCategoryDisscountDetailsCode.trim().length() != 0">
        AND (product_category_disscount_details_code = #{productCategoryDisscountDetailsCode})
      </if>
      <if test="productCategoryDisscountCode != null and productCategoryDisscountCode.trim().length() != 0">
        AND (product_category_disscount_code = #{productCategoryDisscountCode})
      </if>
      <if test="fromPurchaseValue != null">
        AND (from_purchase_value = #{fromPurchaseValue})
      </if>
      <if test="uptoPurchaseValue != null">
        AND (upto_purchase_value = #{uptoPurchaseValue})
      </if>
      <if test="discountType != null and discountType.trim().length() != 0">
        AND (discount_type = #{discountType})
      </if>
      <if test="discount != null">
        AND (discount = #{discount})
      </if>
      <if test="maximumDiscountValue != null">
        AND (maximum_discount_value = #{maximumDiscountValue})
      </if>
      <if test="status != null">
        AND (status = #{status})
      </if>
      <if test="deleteStatus != null">
        AND (delete_status = #{deleteStatus})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
    </trim>
  </sql>
  
  <update id="updateById" >
  	UPDATE gv_product_category_disscount_details 
     SET 
        from_purchase_value = #{fromPurchaseValue},
        upto_purchase_value = #{uptoPurchaseValue},
        discount_type = #{discountType},
        discount = #{discount},
        maximum_discount_value = #{maximumDiscountValue},
        status = #{status},
        delete_status = #{deleteStatus},
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="updateTime == null">
        UPDATE_TIME=SYSDATE(),
      </if>
      update_user = #{updateUser} 
    WHERE id = #{id,jdbcType=BIGINT}
  </update>
  
</mapper>