<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.GCPureReportBusinessMapper">
    <select id="selectWpubonCData" resultType="com.gtech.gvcore.service.report.impl.bo.WpubonCBo">
        SELECT co.outlet_code                                AS outletCode,
               o.business_outlet_code                        AS businessOutletCode,
               co.mop_code                                   AS mopCode,
               mp.mop_name                                   AS mopName,
               SUM(co.voucher_amount - IFNULL(co.amount, 0)) AS totalAmount
        FROM gv_customer_order co
                 left join gv_means_of_payment mp on co.means_of_payment_code = mp.means_of_payment_code
                 left join gv_outlet o on co.outlet_code = o.outlet_code
        WHERE co.status != 'Cancel'
          AND co.mop_code = 'GC'
          AND DATE(co.release_time) = DATE(#{queryDate})
        <if test="outletCodes != null and outletCodes.size() > 0">
            AND co.outlet_code IN
            <foreach item="item" collection="outletCodes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY co.outlet_code, co.mop_code
    </select>

    <select id="selectWpubonData" resultType="com.gtech.gvcore.service.report.impl.bo.WpubonBo">
        SELECT co.outlet_code AS outletCode,
               o.business_outlet_code AS businessOutletCode,
               co.mop_code    AS mopCode,
               mp.mop_name    AS mopName,
               SUM(co.voucher_amount) AS totalAmount
        FROM gv_customer_order co
        left join gv_means_of_payment mp on co.means_of_payment_code = mp.means_of_payment_code
        left join gv_outlet o on co.outlet_code = o.outlet_code
        WHERE co.status != 'Cancel'
          AND co.mop_code = 'GC'
          AND DATE(co.release_time) = DATE(#{queryDate})
        <if test="outletCodes != null and outletCodes.size() > 0">
            AND co.outlet_code IN
            <foreach item="item" collection="outletCodes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY co.outlet_code, co.means_of_payment_code
    </select>

    <select id="selectWpuumsData" resultType="com.gtech.gvcore.service.report.impl.bo.WpuumsBo">
        SELECT co.outlet_code             AS outletCode,
               o.business_outlet_code     AS businessOutletCode,
               article.sap_article_code   AS articleCode,
               SUM(cod.voucher_num)       AS quantity,
               SUM(cod.voucher_amount)    AS totalAmount,
               SUM(IFNULL(cod.discount, 0)) AS discountAmount
        FROM gv_customer_order co
                 LEFT JOIN
             gv_customer_order_details cod ON co.customer_order_code = cod.customer_order_code
                 LEFT JOIN
             gv_outlet o ON co.outlet_code = o.outlet_code
                 LEFT JOIN
             gc_cpg cpg ON cod.cpg_code = cpg.cpg_code
                 LEFT JOIN
             gc_article_mop article ON cpg.article_mop_code = article.article_mop_code
        WHERE co.status != 'Cancel'
          AND co.mop_code = 'GC'
          AND DATE(co.release_time) = DATE(#{queryDate})
        <if test="outletCodes != null and outletCodes.size() > 0">
            AND co.outlet_code IN
            <foreach item="item" collection="outletCodes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY co.outlet_code, article.sap_article_code
    </select>
</mapper>