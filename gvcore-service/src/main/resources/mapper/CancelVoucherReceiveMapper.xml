<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.gtech.gvcore.dao.mapper.CancelVoucherReceiveMapper">



    <select id="selectCancelByXml" resultType="com.gtech.gvcore.dao.dto.VoucherReceiveDto">
        select
        a.receive_code as voucherReceiveCode,
        a.cpg_code as cpgCode,
        a.voucher_start_no as voucherStartNo,
        a.voucher_end_no as voucherEndNo,
        a.denomination,
        a.received_num as receivedNum,
        a.booklet_start_no as bookletStartNo,
        a.booklet_end_no as bookletEndNo,
        a.create_time as createTime,
        b.source_data_code as sourceDataCode,
        b.issuer_code as issuerCode,
        b.source_type as sourceType,
        c.purchase_order_no as purchaseOrderNo,
        e.article_code as articleCode
        from gv_cancel_voucher_receive a
        join gv_voucher_receive b on a.receive_code = b.voucher_receive_code
        and b.source_type = 'generate'
        left join gv_voucher_batch c on c.voucher_batch_code = b.source_data_code
        left join gv_cpg d on a.cpg_code = d.cpg_code
        left join gv_article_mop e on e.article_mop_code = d.article_mop_code
        where date_format(a.create_time, '%Y-%m-%d') = #{createTime}
        <if test="issuerCode != null and issuerCode.trim().length() != 0">
            AND b.issuer_code = #{issuerCode}
        </if>
        order by a.id desc
    </select>
</mapper>