<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.ReleaseApproveAmountMapper">
    <delete id="deleteAll">
        delete from gv_release_approve_amount where 1=1
    </delete>

    <select id="selectAllAndSort" resultType="com.gtech.gvcore.dao.model.ReleaseApproveAmount">
        select release_approve_amount_code,
               type,
               range_name,
               start_num,
               end_num
        from gv_release_approve_amount
        where 1= 1
        <if test="issuerCode != null and issuerCode.trim().length() != 0">
  			  and issuer_code = #{issuerCode}    
        </if>
        order by type, range_name;
    </select>
</mapper>