<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.CpgPrinterMapper">
  <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.CpgPrinter">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cpg_code" jdbcType="VARCHAR" property="cpgCode" />
    <result column="printer_code" jdbcType="VARCHAR" property="printerCode" />
    <result column="delete_status" jdbcType="INTEGER" property="deleteStatus" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    id, cpg_code, printer_code, delete_status, create_user, create_time, update_user, 
    update_time
  </sql>
  <sql id="gv_cpg_printer_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="cpgCode != null and cpgCode.trim().length() != 0">
        AND (cpg_code = #{cpgCode})
      </if>
      <if test="printerCode != null and printerCode.trim().length() != 0">
        AND (printer_code = #{printerCode})
      </if>
      <if test="deleteStatus != null">
        AND (delete_status = #{deleteStatus})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
    </trim>
  </sql>
  <select id="query" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
     from gv_cpg_printer 
    <include refid="gv_cpg_printer_query_condition" />
    <if test="order and orderStr == null">
      order by id desc
    </if>
  </select>
  <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true">
    insert into gv_cpg_printer ( 
    id,cpg_code,printer_code,delete_status,create_user,create_time,update_user,update_time ) values 
    <foreach collection="list" index="index" item="item" separator=",">
       ( #{item.id,jdbcType=BIGINT},#{item.cpgCode,jdbcType=VARCHAR},#{item.printerCode,jdbcType=VARCHAR},#{item.deleteStatus,jdbcType=INTEGER},#{item.createUser,jdbcType=VARCHAR},#{item.createTime,jdbcType=TIMESTAMP},#{item.updateUser,jdbcType=VARCHAR},#{item.updateTime,jdbcType=TIMESTAMP} ) 
    </foreach>
  </insert>
  <select id="count" resultType="int">
    select count(*) from gv_cpg_printer 
    <include refid="gv_cpg_printer_query_condition" />
  </select>
  <select id="selectSysDate" resultType="java.util.Date">
     SELECT NOW() 
  </select>
  <update id="updateStatusByPrimaryKey" parameterType="Map">
    update gv_cpg_printer 
     set STATUS = #{status,jdbcType=INTEGER},UPDATE_TIME=SYSDATE() 
    where  id = #{id,jdbcType=BIGINT}
    <if test="oldStatus != null">
       and STATUS = #{oldStatus,jdbcType=INTEGER} 
    </if>
  </update>
</mapper>