<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.GvExchangeRateMapper">

    <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.GvExchangeRateEntity">
        <!--@Table gv_exchange_rate-->
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="exchangeRateCode" column="exchange_rate_code" jdbcType="VARCHAR"/>
        <result property="currencyCode" column="currency_code" jdbcType="VARCHAR"/>
        <result property="exchangeRate" column="exchange_rate" jdbcType="DECIMAL"/>
        <result property="exchangeCurrencyCode" column="exchange_currency_code" jdbcType="VARCHAR"/>
        <result property="exchangeRateDate" column="exchange_rate_date" jdbcType="TIMESTAMP"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>
</mapper>