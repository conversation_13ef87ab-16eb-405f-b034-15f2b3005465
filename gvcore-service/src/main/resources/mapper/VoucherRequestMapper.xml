<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.VoucherRequestMapper">
    <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.VoucherRequest">
        <!--
        WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="voucher_request_code" jdbcType="VARCHAR" property="voucherRequestCode"/>
        <result column="issuer_code" jdbcType="VARCHAR" property="issuerCode"/>
        <result column="business_type" jdbcType="VARCHAR" property="businessType"/>
        <result column="voucher_owner_code" jdbcType="VARCHAR" property="voucherOwnerCode"/>
        <result column="voucher_owner_name" jdbcType="VARCHAR" property="voucherOwnerName"/>
        <result column="receiver_code" jdbcType="VARCHAR" property="receiverCode"/>
        <result column="receiver_name" jdbcType="VARCHAR" property="receiverName"/>
        <result column="state_code" jdbcType="VARCHAR" property="stateCode"/>
        <result column="city_code" jdbcType="VARCHAR" property="cityCode"/>
        <result column="district_code" jdbcType="VARCHAR" property="districtCode"/>
        <result column="address1" jdbcType="VARCHAR" property="address1"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="request_remarks" jdbcType="VARCHAR" property="requestRemarks"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="voucher_num" jdbcType="INTEGER" property="voucherNum"/>
        <result column="voucher_amount" jdbcType="DECIMAL" property="voucherAmount"/>
        <result column="currency_code" jdbcType="VARCHAR" property="currencyCode"/>
        <result column="permission_code" jdbcType="VARCHAR" property="permissionCode"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
        -->
        id, voucher_request_code, issuer_code, business_type, voucher_owner_code, voucher_owner_name,
        receiver_code, receiver_name, state_code, city_code, district_code, address1, email,
        phone, mobile, request_remarks, status, voucher_num, voucher_amount, currency_code,
        permission_code, create_user, create_time, update_user, update_time
    </sql>

    <select id="queryByVoucherRequestCode" parameterType="string" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM gv_voucher_request
        WHERE voucher_request_code = #{voucherRequestCode}
    </select>

    <resultMap id="ListResponse" type="com.gtech.gvcore.common.response.voucherrequest.QueryVoucherRequestResponse">
        <result column="voucher_request_code" property="voucherRequestCode"/>
        <result column="voucher_num" property="numberOfVouchers"/>
        <result column="voucher_amount" property="voucherAmount"/>
        <result column="create_user" property="createdBy"/>
        <result column="create_time" property="createdOn"/>
        <result column="receiver_name" property="requestSource"/>
        <result column="status" property="status"/>
    </resultMap>
    <select id="queryVoucherRequestList"
            parameterType="com.gtech.gvcore.dto.QueryVoucherRequestPermissionDto"
            resultMap="ListResponse">
        select
        gvr.voucher_request_code,
        gvr.business_type,
        gvr.voucher_num,
        gvr.voucher_amount,
        gvr.create_user,
        gvr.create_time,
        gvr.receiver_name,
        gvr.status,
        gvr.voucher_owner_code outletCode
        from gv_voucher_request as gvr where business_type='sales'
        AND gvr.issuer_code = #{queryVoucherRequestRequest.issuerCode}
        AND (
        gvr.voucher_owner_code IN
        <foreach collection="queryVoucherRequestRequest.outletCodeRangeList" item="outletCode" open="(" separator="," close=")">
            #{outletCode}
        </foreach>
        OR
        gvr.receiver_code IN
        <foreach collection="queryVoucherRequestRequest.outletCodeRangeList" item="outletCode" open="(" separator="," close=")">
            #{outletCode}
        </foreach>
        )
        <if test="queryVoucherRequestRequest.outletCode!=null and queryVoucherRequestRequest.outletCode.trim().length() != 0">
            and gvr.receiver_code=#{queryVoucherRequestRequest.outletCode}
        </if>
        <if test="queryVoucherRequestRequest.requestCode!=null and queryVoucherRequestRequest.requestCode.trim().length() != 0">
            and gvr.voucher_request_code=#{queryVoucherRequestRequest.requestCode}
        </if>
        <if test="queryVoucherRequestRequest.startDateTime != null and queryVoucherRequestRequest.endDateTime != null">
            AND (gvr.create_time BETWEEN #{queryVoucherRequestRequest.startDateTime} AND #{queryVoucherRequestRequest.endDateTime})
        </if>
        <if test="queryVoucherRequestRequest.denomination!=null">
            and gvr.voucher_request_code in (select gvrd.voucher_request_code from gv_voucher_request_details as
            gvrd where
            denomination=#{queryVoucherRequestRequest.denomination})
        </if>
        <if test="queryVoucherRequestRequest.status!=null">
            and gvr.status=#{queryVoucherRequestRequest.status}
        </if>
        order by gvr.create_time desc
    </select>

    <select id="queryByVoucherRequestCodeList" parameterType="list" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM gv_voucher_request
        WHERE voucher_request_code IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryVoucherReturnOrTransfer"
            resultType="com.gtech.gvcore.common.response.voucherrequest.QueryVoucherRequestResponse">
        select
            gvr.voucher_request_code,
            gvr.business_type,
            gvr.voucher_num as numberOfVouchers,
            gvr.voucher_amount,
            gvr.create_user as createdBy,
            gvr.create_time as createdOn,
            gvr.voucher_owner_name as requestSource,
            gvr.voucher_owner_code as outletCode,
            gvr.status
        from gv_voucher_request as gvr where business_type != 'sales'
        AND gvr.issuer_code = #{queryVoucherRequestRequest.issuerCode}
        <if test="!ignoreFromAndToStore">
            AND (
                gvr.voucher_owner_code IN <foreach collection="queryVoucherRequestRequest.outletCodeRangeList" item="outletCode" open="(" separator="," close=")"> #{outletCode} </foreach>
                OR gvr.receiver_code IN <foreach collection="queryVoucherRequestRequest.outletCodeRangeList" item="outletCode" open="(" separator="," close=")"> #{outletCode} </foreach>
            )
        </if>
        <if test="queryVoucherRequestRequest.outletCode!=null and queryVoucherRequestRequest.outletCode.trim().length() != 0"> and gvr.voucher_owner_code=#{queryVoucherRequestRequest.outletCode} </if>
        <if test="queryVoucherRequestRequest.denomination!=null">
           and gvr.voucher_request_code in (
                   select gvrd.voucher_request_code
                   from gv_voucher_request_details as gvrd
                   where denomination = #{queryVoucherRequestRequest.denomination}
           )
        </if>
        <if test="queryVoucherRequestRequest.status!=null"> and gvr.status=#{queryVoucherRequestRequest.status} </if>
        <if test="queryVoucherRequestRequest.businessType != null and queryVoucherRequestRequest.businessType.trim().length() != 0"> AND business_type = #{queryVoucherRequestRequest.businessType} </if>
        order by gvr.create_time desc
    </select>

    <select id="countGoodsInTransit" parameterType="com.gtech.gvcore.service.report.impl.param.GoodsInTransitQueryData" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM (
            SELECT 1
            FROM `gv_voucher_request` r
                JOIN gv_voucher_allocation a ON a.business_type != 'customerorder' AND a.source_data_code = r.voucher_request_code
                JOIN gv_voucher_receive rc ON a.voucher_allocation_code = rc.source_data_code
                JOIN gv_voucher_receive_batch d ON rc.voucher_receive_code = d.voucher_receive_code
            WHERE r.status != 2
                <if test="requestIdList != null and requestIdList.size() > 0">           AND r.voucher_request_code IN <foreach open="(" separator="," close=")" collection="requestIdList" item="requestId"> #{requestId} </foreach> </if>
                <if test="cpgCodeList != null and cpgCodeList.size() > 0">               AND d.cpg_code             IN <foreach open="(" separator="," close=")" collection="cpgCodeList" item="cpgCode"> #{cpgCode} </foreach> </if>
                <if test="outboundCodeList != null and outboundCodeList.size() > 0">     AND r.voucher_owner_code   IN <foreach open="(" separator="," close=")" collection="outboundCodeList" item="outboundCode"> #{outboundCode} </foreach> </if>
                <if test="inboundCodeList != null and inboundCodeList.size() > 0">       AND r.receiver_code        IN <foreach open="(" separator="," close=")" collection="inboundCodeList" item="inboundCode" > #{inboundCode} </foreach> </if>
                <if test="transactionDateStart != null and transactionDateEnd != null "> AND rc.create_time         BETWEEN #{transactionDateStart} AND #{transactionDateEnd} </if>
                <if test="voucherCodeNumStart != null">                                  AND d.voucher_end_no       <![CDATA[ >= ]]> #{voucherCodeNumStart} </if>
                <if test="voucherCodeNumEnd != null">                                    AND d.voucher_start_no     <![CDATA[ <= ]]> #{voucherCodeNumEnd} </if>
            LIMIT 1
        ) a
    </select>

</mapper>