<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.SchedulerReportMapper">

    <select id="queryList"
            resultType="com.gtech.gvcore.common.response.schedulerreport.QuerySchedulerReportResponse">
        select
        scheduler_report_code,
        scheduler_name,
        scheduler_cron,
        execution_time,
        frequency,
        repeat_end,
        repeat_end_time,
        repeat_end_after,
        every,
        report_type,
        report_name,
        issuer_code,
        merchant_code,
        merchant_outlet_code,
        transaction_type,
        data_range,
        transaction_status,
        voucher_status,
        vpg_code,
        emails,
        email_subject,
        ftp_address,
        login_method,
        encryption_key,
        status,
        update_time,
        last_execution_time,
        ftp_username,
        ftp_password

        from gv_scheduler_report
        <where>
            <if test="querySchedulerReportRequest.schedulerName != null and querySchedulerReportRequest.schedulerName.trim().length()>0">
                scheduler_name like concat('%',#{querySchedulerReportRequest.schedulerName},'%')
            </if>
            <if test="querySchedulerReportRequest.reportType != null and querySchedulerReportRequest.reportType.trim().length()>0">
                report_type=#{querySchedulerReportRequest.reportType}
            </if>
            <if test="querySchedulerReportRequest.frequency !=null and querySchedulerReportRequest.frequency.trim().length()>0">
                frequency=#{querySchedulerReportRequest.frequency}
            </if>
            <if test="querySchedulerReportRequest.status != null">
                `status`=#{querySchedulerReportRequest.status}
            </if>
        </where>
        order by id desc
    </select>

    <update id="updateSchedulerReportStatus">

        UPDATE gv_scheduler_report
        SET status = #{status}
        WHERE scheduler_report_code IN
            <foreach collection="codeList" open="(" close=")" item="code" separator="," >#{code}</foreach>
    </update>

    <update id="updateIncreasingNumberOfExecutions">
        UPDATE `gv_scheduler_report`
        SET `number_of_executions` = `number_of_executions` + 1
            , `last_execution_time` = #{lastExecutionTime}
        WHERE `id` = #{id}
            AND `number_of_executions` = #{sourceNumberOfExecutions}
    </update>

</mapper>