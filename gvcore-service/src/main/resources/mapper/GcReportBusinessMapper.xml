<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.GcReportBusinessMapper">

    <select id="gcSalesReport" parameterType="com.gtech.gvcore.service.report.impl.param.SalesDetailedQueryData"
            resultType="com.gtech.gvcore.service.report.impl.bo.GcSalesBo">
        SELECT
        sales.card_number,
        sales.sales_code as transaction_code,
--         DATE_FORMAT(sales.sales_time, '%Y-%m-%d %H:%i:%S') AS transaction_date,
        sales.merchant_code,
        sales.outlet_code,
        sales.invoice_number,
        sales.cpg_code,
        card.denomination,
        sales.notes,
        sales.customer_code,
        sales.approval_code
        FROM
        gc_sales sales
        LEFT JOIN gc_gift_card card ON sales.card_number = card.card_number
        WHERE
        card.management_status != 'DESTROY'

        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (sales.sales_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
        <if test="issuerCode != null and issuerCode.length() != 0">AND card.issuer_code = #{issuerCode}</if>
        <if test="merchantCodeList != null and merchantCodeList.size() != 0">
            AND sales.merchant_code IN
            <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="outletCodeList != null and outletCodeList.size() != 0">
            AND sales.outlet_code IN
            <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="voucherCode != null and voucherCode.size() != 0">
            AND sales.card_number IN
            <foreach collection="voucherCode" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="cpgCodeList != null and cpgCodeList.size() != 0">
            AND sales.cpg_code IN
            <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="invoiceNumber != null and invoiceNumber.length() != 0">AND sales.invoice_number = #{invoiceNumber}
        </if>
        order by sales.sales_time, sales.id
    </select>

    <select id="selectGcRedemption" parameterType="com.gtech.gvcore.service.report.impl.param.RedemptionQueryData"
            resultType="com.gtech.gvcore.service.report.impl.bo.GcRedemptionBo">
        SELECT
        DATE_FORMAT(t.redemption_time, '%Y-%m-%d %H:%i:%S') AS transaction_date,
        t.redemption_code AS transaction_code,
        t.merchant_code,
        t.outlet_code,
        t.invoice_number,
        t.amount,
        t.denomination,
        t.cpg_code,
        t.card_number,
        t.notes,
        t.approval_code,
        balance_after,
        balance_before
        FROM
        gc_redemption t
        WHERE
        t.`transaction_type` = 'REDEEMED'
        and redemption_cancelled = 0
        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (t.redemption_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
        <if test="invoiceNumber != null and invoiceNumber.trim().length() != 0 ">AND t.invoice_number =
            #{invoiceNumber}
        </if>
        <if test="merchantCodeList != null and merchantCodeList.size() != 0 ">
            AND t.merchant_code IN
            <foreach collection="merchantCodeList" item="item" open="( " separator=", " close=") ">
                #{item}
            </foreach>
        </if>
        <if test="outletCodeList != null and outletCodeList.size() != 0 ">
            AND t.outlet_code IN
            <foreach collection="outletCodeList" item="item" open="( " separator=", " close=") ">
                #{item}
            </foreach>
        </if>
        <if test="cpgCodeList != null and cpgCodeList.size() != 0 ">
            AND t.cpg_code IN
            <foreach collection="cpgCodeList" item="item" open="( " separator=", " close=") ">
                #{item}
            </foreach>
        </if>
        <if test="voucherCodeNumStart != null ">AND t.card_number <![CDATA[ >= ]]> #{voucherCodeNumStart}</if>
        <if test="voucherCodeNumEnd != null ">AND t.card_number <![CDATA[ <= ]]> #{voucherCodeNumEnd}</if>
        order by t.redemption_time desc, t.id desc
    </select>
    <select id="selectCancelSales" parameterType="com.gtech.gvcore.service.report.impl.param.GcCancelSalesQueryData"
            resultType="com.gtech.gvcore.service.report.impl.bo.GcCancelSalesBo">
        select
        cancel.card_number,
        cancel.cancel_code AS transaction_code,
        cancel.merchant_code,
        cancel.cpg_code,
        cancel.denomination,
        DATE_FORMAT(cancel.cancel_time, '%Y-%m-%d %H:%i:%S') as cancel_time,
        cancel.invoice_number,
        cancel.customer_code as customerCode,
        cancel.cancel_reason as notes
        from gc_cancel_sales cancel
        left join gc_sales sales on cancel.card_number = sales.card_number
        left join gv_customer_order ord on sales.customer_order_code = ord.customer_order_code
        <where>
            <if test="transactionDateStart != null and transactionDateEnd != null ">
                AND (cancel.cancel_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
            </if>
            <if test="issuerCode != null and issuerCode.trim().length() != 0">AND cancel.issuer_code = #{issuerCode}
            </if>
            <if test="merchantCodeList != null and merchantCodeList.size() != 0">
                AND cancel.merchant_code IN
                <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="outletCodeList != null and outletCodeList.size() != 0">
                AND cancel.outlet_code IN
                <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cpgCodeList != null and cpgCodeList.size() != 0">
                AND cancel.cpg_code IN
                <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="invoiceNumber != null and invoiceNumber.trim().length() != 0">
                AND cancel.invoice_number = #{invoiceNumber}
            </if>
            <if test="purchaseOrderNo != null and purchaseOrderNo.trim().length() != 0">
                AND ord.purchase_order_no = #{purchaseOrderNo}
            </if>
            <if test="customerCodeList != null and customerCodeList.size() != 0">
                AND cancel.customer_code IN
                <foreach collection="customerCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="voucherCode != null and voucherCode.size() != 0">
                AND cancel.card_number IN
                <foreach collection="voucherCode" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            order by cancel.cancel_time desc, cancel.id desc
        </where>
    </select>

    <select id="selectGcDeactivated" resultType="com.gtech.gvcore.service.report.impl.bo.DeactivatedBo">
        SELECT
        t.card_number as transaction_code
        , t.card_number as voucherCode
        , t.merchant_code
        , t.outlet_code
        , t.cpg_code
        , t.denomination
        , t.owner_customer as customerCode
        , t.invoice_number
        , t.block_reason
        ,t.denomination
        , DATE_FORMAT(t.block_time, '%Y-%m-%d %H:%i:%s') AS transaction_date
        FROM gc_block t
        <where>
            <if test="transactionDateStart != null and transactionDateEnd != null ">
                AND (t.block_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
            </if>
            <if test="issuerCodeList != null and issuerCodeList.size() != 0">
                AND t.issuer_code IN
                <foreach collection="issuerCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="merchantCodeList != null and merchantCodeList.size() != 0">
                AND t.merchant_code IN
                <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="outletCodeList != null and outletCodeList.size() != 0">
                AND t.outlet_code IN
                <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cpgCodeList != null and cpgCodeList.size() != 0">
                AND t.cpg_code IN
                <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="customerCodes != null and customerCodes.size() != 0">
                AND t.owner_customer IN
                <foreach collection="customerCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="voucherCode != null and voucherCode.size() != 0">
                AND t.card_number IN
                <foreach collection="voucherCode" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            order by t.block_time desc, t.id desc
        </where>
    </select>

    <select id="selectGcReactivated" resultType="com.gtech.gvcore.service.report.impl.bo.GcReactivatedBo">

        SELECT
        t.card_number as transaction_code
        , t.card_number as voucher_code
        , t.merchant_code
        , t.outlet_code
        , t.cpg_code
        , t.denomination
        , t.owner_customer
        , t.invoice_number
        , t.owner_customer as customer_code
        , t.unblock_reason
        , DATE_FORMAT(t.unblock_time, '%Y-%m-%d %H:%i:%S') AS transaction_date
        FROM gc_unblock t
        <where>
            <if test="transactionDateStart != null and transactionDateEnd != null ">
                AND (t.unblock_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
            </if>
            <if test="issuerCodeList != null and issuerCodeList.size() != 0">
                AND t.issuer_code IN
                <foreach collection="issuerCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="merchantCodeList != null and merchantCodeList.size() != 0">
                AND t.merchant_code IN
                <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="outletCodeList != null and outletCodeList.size() != 0">
                AND t.outlet_code IN
                <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cpgCodeList != null and cpgCodeList.size() != 0">
                AND t.cpg_code IN
                <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="customerCodes != null and customerCodes.size() != 0">
                AND t.owner_customer IN
                <foreach collection="customerCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="voucherCode != null and voucherCode.size() != 0">
                AND t.card_number IN
                <foreach collection="voucherCode" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            order by t.unblock_time desc, t.id desc
        </where>
    </select>

    <select id="selectGcExpiry" resultType="com.gtech.gvcore.service.report.impl.bo.GcExpiryBo">

        SELECT t.card_number as voucher_code
        , t.sales_outlet as outlet_code
        , t.cpg_code
        , t.denomination
        , t.balance
        , t.owner_customer
        , t.status as card_status
        , DATE_FORMAT(t.expiry_time, '%Y-%m-%d %H:%i:%S') AS expiry_time
        , DATE_FORMAT(t.sales_time, '%Y-%m-%d %H:%i:%S') AS sales_time
        , DATE_FORMAT(t.activation_time, '%Y-%m-%d %H:%i:%S') AS activation_time
        , t.activation_extension_count
        , t.activation_deadline
        , t.management_status
        FROM gc_gift_card t
        <where>
            t.management_status != 'DESTROY' and t.sales_time is not null and t.sales_outlet is not null

            <if test="expiryDateStart != null and expiryDateEnd != null ">
                AND (t.expiry_time BETWEEN #{expiryDateStart} AND #{expiryDateEnd})
            </if>
            <if test="activationTimeStart != null and activationTimeEnd != null ">
                AND (t.activation_time BETWEEN #{activationTimeStart} AND #{activationTimeEnd})
            </if>
            <if test="issuerCodeList != null and issuerCodeList.size() != 0">
                AND t.issuer_code IN
                <foreach collection="issuerCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cpgCodeList != null and cpgCodeList.size() != 0">
                AND t.cpg_code IN
                <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="customerCodeList != null and customerCodeList.size() != 0">
                AND t.owner_customer IN
                <foreach collection="customerCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="voucherCode != null and voucherCode.size() != 0">
                AND t.card_number IN
                <foreach collection="voucherCode" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="purchaseDateStart != null and purchaseDateEnd != null ">
                AND (t.sales_time BETWEEN #{purchaseDateStart} AND #{purchaseDateEnd})
            </if>
            order by t.expiry_time desc, t.id desc
        </where>
    </select>

    <select id="selectGcExtendExpiry" resultType="com.gtech.gvcore.service.report.impl.bo.GcExtendExpiryBo">

        SELECT t.card_number
        , gc.activation_extension_count as extensionCount
        , t.outlet_code
        , t.issuer_code
        , t.merchant_code
        , t.invoice_number
        , t.approval_code
        , t.notes
        , t.batch_number
        , t.source
        , DATE_FORMAT(t.extension_time, '%Y-%m-%d %H:%i:%S') AS extension_time
        , DATE_FORMAT(gc.activation_deadline, '%Y-%m-%d %H:%i:%S') AS activation_deadline
        , gc.cpg_code
        , gc.owner_customer
        , gc.denomination AS amount
        , DATE_FORMAT(gc.sales_time, '%Y-%m-%d %H:%i:%S') AS sales_time
        , DATE_FORMAT(gc.activation_time, '%Y-%m-%d %H:%i:%S') AS activation_time,
        gc.activation_grace_period as activationGracePeriod
        FROM gc_extend_activation_period t
        LEFT JOIN gc_gift_card gc ON t.card_number = gc.card_number

        <where>
            gc.activation_extension_count > 0
            <if test="transactionDateStart != null and transactionDateEnd != null ">
                AND (t.extension_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
            </if>
            <if test="issuerCodeList != null and issuerCodeList.size() != 0">
                AND t.issuer_code IN
                <foreach collection="issuerCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="merchantCodeList != null and merchantCodeList.size() != 0">
                AND t.merchant_code IN
                <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="outletCodeList != null and outletCodeList.size() != 0">
                AND t.outlet_code IN
                <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cpgCodeList != null and cpgCodeList.size() != 0">
                AND gc.cpg_code IN
                <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sourceList != null and sourceList.size() != 0">
                AND t.source IN
                <foreach collection="sourceList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <!-- Batch queries for latest transaction times -->
    <select id="getBatchLatestGcActivationTime" resultType="java.util.HashMap">
        SELECT
        a.card_number AS cardNumber,
        DATE_FORMAT(a.create_time, '%Y-%m-%d %H:%i:%S') AS latestTime,
        a.outlet_code AS outletCode
        FROM gc_activation a
        JOIN (
        SELECT card_number, MAX(create_time) AS max_create_time
        FROM gc_activation
        WHERE card_number IN
        <foreach collection="cardNumbers" item="cardNumber" open="(" separator="," close=")">
            #{cardNumber}
        </foreach>
        GROUP BY card_number
        ) b ON a.card_number = b.card_number AND a.create_time = b.max_create_time

    </select>

    <select id="getBatchLatestGcSalesTime" resultType="java.util.HashMap">
        SELECT
        s.card_number AS cardNumber,
        DATE_FORMAT(s.create_time, '%Y-%m-%d %H:%i:%S') AS latestTime,
        s.outlet_code AS outletCode
        FROM gc_sales s
        JOIN (
        SELECT card_number, MAX(create_time) AS max_create_time
        FROM gc_sales
        WHERE card_number IN
        <foreach collection="cardNumbers" item="cardNumber" open="(" separator="," close=")">
            #{cardNumber}
        </foreach>
        GROUP BY card_number
        ) max_s ON s.card_number = max_s.card_number AND s.create_time = max_s.max_create_time

    </select>

    <select id="getBatchLatestGcRedemptionTime" resultType="java.util.HashMap">
        SELECT
        r.card_number AS cardNumber,
        DATE_FORMAT(r.create_time, '%Y-%m-%d %H:%i:%S') AS latestTime,
        r.outlet_code AS outletCode,
        r.amount AS redeemAmount
        FROM
        gc_redemption r
        JOIN (
        SELECT
        card_number,
        MAX(create_time) AS latest_time
        FROM
        gc_redemption
        WHERE
        transaction_type = 'REDEEMED'
        and card_number IN
        <foreach collection="cardNumbers" item="cardNumber" open="(" separator="," close=")">
            #{cardNumber}
        </foreach>
        GROUP BY
        card_number
        ) t ON r.card_number = t.card_number AND r.create_time = t.latest_time
        WHERE
        r.transaction_type = 'REDEEMED';

    </select>

    <select id="getBatchLatestGcCancelRedemptionTime" resultType="java.util.HashMap">
        SELECT
        r.card_number AS cardNumber,
        DATE_FORMAT(r.create_time, '%Y-%m-%d %H:%i:%S') AS latestTime,
        r.outlet_code AS outletCode,
        r.amount AS redeemAmount
        FROM
        gc_redemption r
        JOIN (
        SELECT
        card_number,
        MAX(create_time) AS latest_time
        FROM
        gc_redemption
        WHERE
        transaction_type = 'CANCELLED_REDEEM'
        and card_number IN
        <foreach collection="cardNumbers" item="cardNumber" open="(" separator="," close=")">
            #{cardNumber}
        </foreach>
        GROUP BY
        card_number
        ) t ON r.card_number = t.card_number AND r.create_time = t.latest_time
        WHERE
        r.transaction_type = 'CANCELLED_REDEEM';
    </select>

    <select id="getBatchLatestGcBlockTime" resultType="java.util.HashMap">
        SELECT
        card_number AS cardNumber,
        DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%S') AS latestTime,
        outlet_code AS outletCode
        FROM gc_block
        WHERE
        card_number IN
        <foreach collection="cardNumbers" item="cardNumber" open="(" separator="," close=")">
            #{cardNumber}
        </foreach>
        AND create_time = (
        SELECT MAX(b2.create_time)
        FROM gc_block b2
        WHERE b2.card_number = gc_block.card_number
        )
    </select>

    <select id="getBatchLatestGcUnblockTime" resultType="java.util.HashMap">
        SELECT
        u.card_number AS cardNumber,
        DATE_FORMAT(u.create_time, '%Y-%m-%d %H:%i:%S') AS latestTime,
        u.outlet_code AS outletCode
        FROM gc_unblock u
        JOIN (
        SELECT
        card_number,
        MAX(create_time) AS latest_time
        FROM gc_unblock
        WHERE card_number IN
        <foreach collection="cardNumbers" item="cardNumber" open="(" separator="," close=")">
            #{cardNumber}
        </foreach>
        GROUP BY card_number
        ) t ON u.card_number = t.card_number AND u.create_time = t.latest_time
    </select>

    <select id="getBatchLatestGcCancelSalesTime" resultType="java.util.HashMap">
        SELECT
        c.card_number AS cardNumber,
        DATE_FORMAT(c.create_time, '%Y-%m-%d %H:%i:%S') AS latestTime,
        c.outlet_code AS outletCode
        FROM gc_cancel_sales c
        JOIN (
        SELECT
        card_number,
        MAX(create_time) AS latest_time
        FROM gc_cancel_sales
        WHERE card_number IN
        <foreach collection="cardNumbers" item="cardNumber" open="(" separator="," close=")">
            #{cardNumber}
        </foreach>
        GROUP BY card_number
        ) t ON c.card_number = t.card_number AND c.create_time = t.latest_time;
    </select>

    <select id="getBatchLatestGcExtendActivationTime" resultType="java.util.HashMap">
        SELECT
        a.card_number AS cardNumber,
        DATE_FORMAT(a.create_time, '%Y-%m-%d %H:%i:%S') AS latestTime,
        a.outlet_code AS outletCode
        FROM gc_extend_activation_period a
        JOIN (
        SELECT
        card_number,
        MAX(create_time) AS latest_time
        FROM gc_extend_activation_period
        WHERE card_number IN
        <foreach collection="cardNumbers" item="cardNumber" open="(" separator="," close=")">
            #{cardNumber}
        </foreach>
        GROUP BY card_number
        ) t ON a.card_number = t.card_number AND a.create_time = t.latest_time

    </select>

    <!-- Get card to merchant/outlet mapping from sales records -->
    <select id="getCardToMerchantMapFromGcSales" resultType="java.util.HashMap">
        SELECT card_number as `key`, merchant_code as `value`
        FROM gc_sales
        WHERE card_number IN
        <foreach collection="cardNumbers" item="cardNumber" open="(" separator="," close=")">
            #{cardNumber}
        </foreach>
        AND merchant_code IS NOT NULL
    </select>

    <select id="getCardToOutletMapFromGcSales" resultType="java.util.HashMap">
        SELECT card_number as `key`, outlet_code as `value`
        FROM gc_sales
        WHERE card_number IN
        <foreach collection="cardNumbers" item="cardNumber" open="(" separator="," close=")">
            #{cardNumber}
        </foreach>
        AND outlet_code IS NOT NULL
    </select>

    <!-- Gift Card Transaction Detail Report - Decomposed Queries -->

    <!-- Common Conditions for Gift Card Transaction Reports -->
    <sql id="commonConditions">
        <!-- Card Number Range Filter -->
        <if test="cardNumberList != null and cardNumberList.size() > 0">
            AND card.card_number IN
            <foreach collection="cardNumberList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <!-- Code List Filters -->
        <if test="issuerCodes != null and issuerCodes.size() > 0">
            AND card.issuer_code IN
            <foreach collection="issuerCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="merchantCodes != null and merchantCodes.size() > 0">
            AND temp.merchant_code IN
            <foreach collection="merchantCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="outletCodes != null and outletCodes.size() > 0">
            AND temp.outlet_code IN
            <foreach collection="outletCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="cpgCodes != null and cpgCodes.size() > 0">
            AND card.cpg_code IN
            <foreach collection="cpgCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="cardStatusList != null and cardStatusList.size() != 0">
            AND card.status IN
            <foreach collection="cardStatusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="customerCodeList != null and customerCodeList.size() != 0">
            AND card.owner_customer IN
            <foreach collection="customerCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!-- Invoice Number Filter -->
        <if test="invoiceNumber != null and invoiceNumber.length() > 0">
            AND temp.invoice_number = #{invoiceNumber}
        </if>
        <if test="expiryDateStart != null and expiryDateEnd != null ">
            AND (card.expiry_time BETWEEN #{expiryDateStart} AND #{expiryDateEnd})
        </if>
    </sql>
    <!-- Activation Transactions -->
    <select id="findActivationTransactions"
            parameterType="com.gtech.gvcore.service.report.impl.param.GcTransactionDetailQueryData"
            resultType="com.gtech.gvcore.service.report.impl.bo.GcTransactionDetailedBo">
        SELECT
        'Gift Card Activate' as transactionType,
        temp.merchant_code as merchantCode,
        temp.outlet_code as outletCode,
        temp.create_time as transactionDate,
        temp.card_number as cardNumber,
        card.cpg_code as cpgCode,
        temp.create_user as createUser,
        card.denomination as denomination,
        card.denomination as amount,
        temp.batch_number as batchNumber,
        temp.invoice_number as invoiceNumber
        FROM gc_activation temp
        left join gc_gift_card card on temp.card_number = card.card_number
        WHERE 1=1
        <include refid="commonConditions"/>
        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (temp.create_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
        ORDER BY temp.create_time DESC
    </select>

    <!-- Sales Transactions -->
    <select id="findSalesTransactions"
            parameterType="com.gtech.gvcore.service.report.impl.param.GcTransactionDetailQueryData"
            resultType="com.gtech.gvcore.service.report.impl.bo.GcTransactionDetailedBo">
        SELECT
        'Gift Card Sell' as transactionType,
        temp.merchant_code as merchantCode,
        temp.outlet_code as outletCode,
        temp.sales_time as transactionDate,
        temp.card_number as cardNumber,
        card.cpg_code as cpgCode,
        temp.create_user as createUser,
        card.denomination as denomination,
        card.denomination as amount,
        temp.batch_number as batchNumber,
        temp.denomination as denomination,
        temp.customer_code as buyerName,
        temp.invoice_number as invoiceNumber
        FROM gc_sales temp left join gc_gift_card card on temp.card_number = card.card_number
        WHERE 1=1
        <include refid="commonConditions"/>
        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (temp.sales_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
        ORDER BY temp.sales_time DESC
    </select>

    <!-- Sales Transactions -->
    <select id="findCancelSalesTransactions"
            parameterType="com.gtech.gvcore.service.report.impl.param.GcTransactionDetailQueryData"
            resultType="com.gtech.gvcore.service.report.impl.bo.GcTransactionDetailedBo">
        SELECT
        'Gift Card Cancel Sell' as transactionType,
        temp.merchant_code as merchantCode,
        temp.outlet_code as outletCode,
        temp.create_time as transactionDate,
        temp.card_number as cardNumber,
        card.cpg_code as cpgCode,
        temp.create_user as createUser,
        temp.denomination as denomination,
        card.denomination as amount,
        temp.customer_code as buyerName,
        temp.invoice_number as invoiceNumber
        FROM gc_cancel_sales temp left join gc_gift_card card on temp.card_number = card.card_number
        WHERE 1=1
        <include refid="commonConditions"/>
        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (temp.create_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
        ORDER BY temp.create_time DESC
    </select>

    <!--Cancelled Redemption Transactions -->
    <select id="findRedemptionTransactions"
            parameterType="com.gtech.gvcore.service.report.impl.param.GcTransactionDetailQueryData"
            resultType="com.gtech.gvcore.service.report.impl.bo.GcTransactionDetailedBo">
        SELECT
        'Gift Card Redeem' as transactionType,
        temp.merchant_code,
        temp.outlet_code,
        temp.redemption_time as transactionDate,
        temp.card_number,
        temp.cpg_code,
        temp.create_user,
        temp.terminal_id as terminal,
        temp.batch_number,
        temp.denomination,
        temp.amount,
        temp.invoice_number
        FROM gc_redemption temp left join gc_gift_card card on temp.card_number = card.card_number
        WHERE 1=1 and temp.transaction_type = 'REDEEMED'
        <include refid="commonConditions"/>
        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (temp.redemption_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
        ORDER BY transactionDate DESC
    </select>

    <!-- Redemption Transactions -->
    <select id="findCancelRedemptionTransactions"
            parameterType="com.gtech.gvcore.service.report.impl.param.GcTransactionDetailQueryData"
            resultType="com.gtech.gvcore.service.report.impl.bo.GcTransactionDetailedBo">
        SELECT
        'Gift Card Cancel Redeem' as transactionType,
        temp.merchant_code,
        temp.outlet_code,
        temp.redemption_time as transactionDate,
        temp.card_number,
        temp.cpg_code,
        temp.create_user,
        temp.terminal_id as terminal,
        temp.batch_number,
        temp.denomination,
        temp.amount,
        temp.invoice_number
        FROM gc_redemption temp left join gc_gift_card card on temp.card_number = card.card_number
        WHERE 1=1 and temp.transaction_type = 'CANCELLED_REDEEM'
        <include refid="commonConditions"/>
        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (temp.redemption_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
        ORDER BY transactionDate DESC
    </select>

    <!-- Activation Extension Transactions -->
    <select id="findActivationExtensionTransactions"
            parameterType="com.gtech.gvcore.service.report.impl.param.GcTransactionDetailQueryData"
            resultType="com.gtech.gvcore.service.report.impl.bo.GcTransactionDetailedBo">
        SELECT
        'Gift Card Activation Extension' as transactionType,
        temp.merchant_code,
        temp.outlet_code,
        temp.extension_time as transactionDate,
        temp.card_number,
        temp.cpg_code,
        temp.create_user,
        temp.batch_number,
        temp.denomination,
        card.denomination as amount,
        temp.invoice_number
        FROM gc_extend_activation_period temp left join gc_gift_card card on temp.card_number = card.card_number
        WHERE 1=1
        <include refid="commonConditions"/>
        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (temp.extension_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
        ORDER BY transactionDate DESC
    </select>

    <!-- Deactivate Transactions -->
    <select id="findDeactivateTransactions"
            parameterType="com.gtech.gvcore.service.report.impl.param.GcTransactionDetailQueryData"
            resultType="com.gtech.gvcore.service.report.impl.bo.GcTransactionDetailedBo">
        SELECT
        'Gift Card Deactivate' as transactionType,
        temp.merchant_code,
        temp.outlet_code,
        temp.block_time as transactionDate,
        temp.card_number,
        temp.cpg_code,
        temp.create_user,
        temp.denomination,
        card.denomination as amount,
        temp.invoice_number
        FROM gc_block temp left join gc_gift_card card on temp.card_number = card.card_number
        WHERE 1=1
        <include refid="commonConditions"/>
        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (temp.block_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
        ORDER BY transactionDate DESC
    </select>
    <!-- Unblock Transactions -->
    <select id="findReactivateTransactions"
            parameterType="com.gtech.gvcore.service.report.impl.param.GcTransactionDetailQueryData"
            resultType="com.gtech.gvcore.service.report.impl.bo.GcTransactionDetailedBo">
        SELECT
        'Gift Card Deactivate' as transactionType,
        temp.merchant_code,
        temp.outlet_code,
        temp.unblock_time as transactionDate,
        temp.card_number,
        temp.cpg_code,
        temp.create_user,
        temp.denomination,
        card.denomination as amount,
        temp.invoice_number
        FROM gc_unblock temp left join gc_gift_card card on temp.card_number = card.card_number
        WHERE 1=1
        <include refid="commonConditions"/>
        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (temp.unblock_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
        ORDER BY transactionDate DESC
    </select>

    <!-- Gift Card Transaction Detail Report - Decomposed Queries -->

    <select id="selectGcRegenerateActivationCodeSummary"
            resultType="com.gtech.gvcore.service.report.impl.bean.GcRegenerateActivationCodeBean">
        SELECT detail.issue_handling_code as issueHandlingCode,
        issue.uploaded_file_name as giftCardListFileName,
        issue.create_time as requestDate,
        count(1) AS totalCards,
        sum(card.denomination) as totalAmount,
        issue.create_user as requestor,
        issue.`status` as status
        FROM gv_issue_handling_details detail
        INNER JOIN gc_gift_card card ON detail.voucher_code = card.card_number
        LEFT JOIN gv_issue_handling issue ON detail.issue_handling_code = issue.issue_handling_code
        WHERE detail.issue_type = 'gc_bulk_regenerate_activation_code'
        AND card.card_number IS NOT NULL
        <if test="issueHandlingCode != null and issueHandlingCode.length() > 0 ">
            AND (detail.issue_handling_code = #{issueHandlingCode})
        </if>
        <if test="startDate != null and endDate != null ">
            AND (detail.create_time BETWEEN #{startDate} AND #{endDate})
        </if>
        <if test="status != null and status.size() != 0">
            AND issue.`status`IN
            <foreach collection="status" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="cardNumbers != null and cardNumbers.size() != 0">
            AND detail.voucher_code IN
            <foreach collection="cardNumbers" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY detail.issue_handling_code
        order by detail.create_time desc, detail.id desc
    </select>

    <select id="selectGcRegenerateActivationCodeDetail"
            resultType="com.gtech.gvcore.service.report.impl.bo.GcRegenerateActivationCodeDetailBo">
        SELECT
        card.card_number as cardNumber,
        card.cpg_code as cpgCode,
        issue.create_time as transactionDate,
        card.denomination as totalAmount,
        card.owner_customer as customerName,
        card.expiry_time as expiryDate,
        card.create_time as issuanceDate,
        card.activation_deadline,
        card.activation_grace_period,
        detail.issue_handling_code as issueHandlingCode,
        issue.create_user_email as customerEmail,
        card.activation_extension_count as activationExtensionCount
        FROM gv_issue_handling_details detail
        INNER JOIN gc_gift_card card ON detail.voucher_code = card.card_number
        LEFT JOIN gv_issue_handling issue ON detail.issue_handling_code = issue.issue_handling_code
        WHERE detail.issue_type = 'gc_bulk_regenerate_activation_code'
        AND card.card_number IS NOT NULL
        AND detail.process_status = 2
        <if test="issueHandlingCode != null and issueHandlingCode.length() > 0 ">
            AND (detail.issue_handling_code = #{issueHandlingCode})
        </if>
        <if test="startDate != null and endDate != null ">
            AND (detail.create_time BETWEEN #{startDate} AND #{endDate})
        </if>
        <if test="status != null and status.size() != 0">
            AND issue.`status`IN
            <foreach collection="status" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="cardNumbers != null and cardNumbers.size() != 0">
            AND detail.voucher_code IN
            <foreach collection="cardNumbers" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="cpgCodes != null and cpgCodes.size() != 0">
            AND card.cpg_code IN
            <foreach collection="cpgCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by detail.create_time desc, detail.id desc
    </select>

    <select id="liabilitySummaryReport" parameterType="com.gtech.gvcore.service.report.impl.param.LiabilitySummaryQueryData" resultType="com.gtech.gvcore.dao.model.GcReportTempLiabilitySStructure">
        SELECT `id`
        , `issuer_code`
        , `merchant_code`
        , `outlet_code`
        , `cpg_code`
        , `activated_amount`
        , `purchased_amount`
        , `deactivated_amount`
        , `expired_amount`
        , `total_amount`
        , `expiry_date`
        FROM gc_report_temp_liability_s_${tableCode}
        WHERE 1 = 1
        <if test="issuerCode != null and issuerCode.trim().length() != 0">AND issuer_code = #{issuerCode}</if>
        <if test="merchantCodeList != null and merchantCodeList.size() != 0">
            AND merchant_code IN
            <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="outletCodeList != null and outletCodeList.size() != 0">
            AND outlet_code IN
            <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="cpgCodeList != null and cpgCodeList.size() != 0">
            AND cpg_code IN
            <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="liabilityDetailReport" parameterType="com.gtech.gvcore.service.report.impl.param.LiabilityDetailQueryData" resultType="com.gtech.gvcore.dao.model.GcReportTempLiabilityDStructure">
        SELECT `id`
        , `issuer_code`
        , `merchant_code`
        , `outlet_code`
        , `cpg_code`
        , `voucher_status`
        , `denomination`
        , `balance`
        , `expiry_date`
        , `voucher_code_page_index`
        , `voucher_codes`
        FROM  gc_report_temp_liability_d_${tableCode}
        WHERE  1 = 1
        <if test="issuerCode != null and issuerCode.trim().length() != 0"> AND issuer_code = #{issuerCode} </if>
        <!--        <if test="merchantCodeList != null and merchantCodeList.size() != 0">-->
        <!--            AND merchant_code IN-->
        <!--            <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">-->
        <!--                #{item}-->
        <!--            </foreach>-->
        <!--        </if>-->
        <!--        <if test="outletCodeList != null and outletCodeList.size() != 0">-->
        <!--            AND outlet_code IN-->
        <!--            <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">-->
        <!--                #{item}-->
        <!--            </foreach>-->
        <!--        </if>-->
        <if test="cpgCodeList != null and cpgCodeList.size() != 0">
            AND cpg_code IN
            <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reportVoucherStatusList != null and reportVoucherStatusList.size() != 0">
            AND voucher_status IN
            <foreach collection="reportVoucherStatusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectBalanceCorrection" resultType="com.gtech.gvcore.service.report.impl.bo.GcBalanceCorrectionBo">
        SELECT
        adjustment.card_number AS cardNumber,
        adjustment.cpg_code AS cpgCode,
        adjustment.outlet_code AS outletCode,
        adjustment.merchant_code AS merchantCode,
        adjustment.create_time AS createTime,
        adjustment.balance_before AS initialBalance,
        adjustment.amount AS correctionBalance,
        adjustment.balance_after AS afterBalance,
        adjustment.notes,
        card.owner_customer AS customerCode,
        IF
        (adjustment.transaction_type = 'CANCELLED_REDEEM', 'Bulk cancel redeem', 'Bulk redeem') AS transactionType,
        card.expiry_time AS expiryDate
        FROM
        gc_redemption adjustment
        LEFT JOIN gc_gift_card card ON adjustment.card_number = card.card_number
        WHERE
         adjustment.redemption_channel is null

        <if test="transactionType != null and transactionType.size() >0">
            AND adjustment.transaction_type IN
            <foreach collection="transactionType" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (adjustment.create_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
        <if test="cpgCodeList != null and cpgCodeList.size() != 0">
            AND card.cpg_code IN
            <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="outletCodeList != null and outletCodeList.size() != 0">
            AND adjustment.outlet_code IN
            <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="merchantCodeList != null and merchantCodeList.size() != 0">
            AND adjustment.merchant_code IN
            <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="customerCodeList != null and customerCodeList.size() != 0">
            AND card.owner_customer IN
            <foreach collection="customerCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="cardNumbers != null and cardNumbers.size() != 0">
            AND adjustment.card_number IN
            <foreach collection="cardNumbers" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectLatestGcStatus" resultType="com.gtech.gvcore.giftcard.infrastructure.entity.GiftCardEntity">
        SELECT * FROM gc_gift_card
        <where>
            <if test="cardNumberList != null and cardNumberList.size() != 0">
                AND card_number IN
                <foreach collection="cardNumberList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="cpgCodeList != null and cpgCodeList.size() != 0">
                AND cpg_code IN
                <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="cardStatusList != null and cardStatusList.size() != 0">
                AND (
                <foreach collection="cardStatusList" item="item" separator=" OR ">
                    <choose>
                        <when test="item == '0'.toString()">
                            (management_status = 'ENABLE' AND status = 'PURCHASED' AND now() &lt; activation_deadline AND activation_extension_count = 0)
                        </when>
                        <when test="item == '1'.toString()">
                            (management_status = 'ENABLE' AND status = 'PURCHASED' AND now() &gt; activation_deadline AND activation_extension_count = 0 AND now() &lt; expiry_time)
                        </when>
                        <when test="item == '2'.toString()">
                            (management_status = 'ENABLE' AND status = 'PURCHASED' AND activation_extension_count = 1 AND now() &lt; expiry_time)
                        </when>
                        <when test="item == '3'.toString()">
                            (management_status = 'ENABLE' AND status = 'ACTIVATED' AND now() &lt; expiry_time)
                        </when>
                        <when test="item == '4'.toString()">
                            (management_status = 'ENABLE' AND status = 'ZERO_BALANCE')
                        </when>
                        <when test="item == '5'.toString()">
                            (now() &gt; expiry_time AND status != 'ZERO_BALANCE')
                        </when>
                        <when test="item == '6'.toString()">
                            (management_status = 'DISABLE' AND now() &lt; expiry_time)
                        </when>
                        <when test="item == '7'.toString()">
                            (management_status = 'DESTROY')
                        </when>
                    </choose>
                </foreach>
                )
            </if>
        </where>
        order by id desc
    </select>


</mapper>