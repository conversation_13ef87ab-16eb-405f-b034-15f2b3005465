<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.FlowMapper">
  <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.Flow">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="issuer_code" jdbcType="VARCHAR" property="issuerCode" />
    <result column="flow_code" jdbcType="VARCHAR" property="flowCode" />
    <result column="flow_name" jdbcType="VARCHAR" property="flowName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    id, issuer_code, flow_code, flow_name, remark, status, create_time, update_time, 
    create_user, update_user
  </sql>
  <sql id="gv_flow_query_fuzzy_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="issuerCode != null and issuerCode.trim().length() != 0">
         AND (issuer_code like concat('%',#{issuerCode},'%')) 
      </if>
      <if test="flowCode != null and flowCode.trim().length() != 0">
         AND (flow_code like concat('%',#{flowCode},'%')) 
      </if>
      <if test="flowName != null and flowName.trim().length() != 0">
         AND (flow_name like concat('%',#{flowName},'%')) 
      </if>
      <if test="remark != null and remark.trim().length() != 0">
         AND (remark like concat('%',#{remark},'%')) 
      </if>
      <if test="status != null">
        AND (status = #{status})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
         AND (create_user like concat('%',#{createUser},'%')) 
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
         AND (update_user like concat('%',#{updateUser},'%')) 
      </if>
    </trim>
  </sql>
  <sql id="gv_flow_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="issuerCode != null and issuerCode.trim().length() != 0">
        AND (issuer_code = #{issuerCode})
      </if>
      <if test="flowCode != null and flowCode.trim().length() != 0">
        AND (flow_code = #{flowCode})
      </if>
      <if test="flowName != null and flowName.trim().length() != 0">
        AND (flow_name = #{flowName})
      </if>
      <if test="remark != null and remark.trim().length() != 0">
        AND (remark = #{remark})
      </if>
      <if test="status != null">
        AND (status = #{status})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
    </trim>
  </sql>
  <select id="query" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
     from gv_flow 
    <if test="fuzzy">
      <include refid="gv_flow_query_fuzzy_condition" />
    </if>
    <if test="!fuzzy">
      <include refid="gv_flow_query_condition" />
    </if>
     order by id desc 
  </select>
</mapper>