<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.GcMeansOfPaymentMapper">
  <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.GcMeansOfPayment">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="means_of_payment_code" jdbcType="VARCHAR" property="meansOfPaymentCode" />
    <result column="mop_name" jdbcType="VARCHAR" property="mopName" />
    <result column="mop_group" jdbcType="VARCHAR" property="mopGroup" />
    <result column="grp" jdbcType="VARCHAR" property="grp" />
    <result column="external_payment_mode" jdbcType="VARCHAR" property="externalPaymentMode" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, means_of_payment_code, mop_name, mop_group, grp, external_payment_mode, 
    remarks, status, create_user, create_time, update_user, update_time
  </sql>
  <sql id="gc_means_of_payment_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="meansOfPaymentCode != null and meansOfPaymentCode.trim().length() != 0">
        AND (means_of_payment_code = #{meansOfPaymentCode})
      </if>
      <if test="mopName != null and mopName.trim().length() != 0">
        AND (mop_name = #{mopName})
      </if>
      <if test="mopGroup != null and mopGroup.trim().length() != 0">
        AND (mop_group = #{mopGroup})
      </if>
      <if test="grp != null and grp.trim().length() != 0">
        AND (grp = #{grp})
      </if>
      <if test="externalPaymentMode != null and externalPaymentMode.trim().length() != 0">
        AND (external_payment_mode = #{externalPaymentMode})
      </if>
      <if test="remarks != null and remarks.trim().length() != 0">
        AND (remarks = #{remarks})
      </if>
      <if test="status != null">
        AND (status = #{status})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
    </trim>
  </sql>
  
  <select id="selectSelective" parameterType="com.gtech.gvcore.dao.dto.GcMeansOfPaymentDto" resultMap="BaseResultMap">
  	SELECT mp.id, mp.means_of_payment_code, mp.mop_name, mp.mop_group, mp.grp, mp.external_payment_mode, 
        mp.remarks, mp.status, mp.create_user, mp.create_time, mp.update_user, mp.update_time 
    FROM gc_means_of_payment mp 
    
    LEFT JOIN gv_means_of_payment_outlet outlet ON mp.means_of_payment_code = outlet.means_of_payment_code 
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="meansOfPaymentCode != null and meansOfPaymentCode.trim().length() != 0">
        AND mp.means_of_payment_code = #{meansOfPaymentCode}
      </if>
      <if test="mopName != null and mopName.trim().length() != 0">
        AND mp.mop_name LIKE CONCAT('%',#{mopName},'%') 
      </if>
      <if test="mopGroup != null and mopGroup.trim().length() != 0">
        AND mp.mop_group = #{mopGroup}
      </if>
      <if test="grp != null and grp.trim().length() != 0">
        AND mp.grp = #{grp}
      </if>
      <if test="status != null">
        AND mp.status = #{status}
      </if>
      <if test="outletCode != null and outletCode.trim().length() != 0">
        AND outlet.outlet_code = #{outletCode} 
        AND outlet.delete_status = #{deleteStatus} 
      </if>
    </trim>
    ORDER BY id DESC
  </select>
  
  <select id="queryByCodeList" parameterType="list" resultMap="BaseResultMap">
  	SELECT <include refid="Base_Column_List"/> 
  	FROM gc_means_of_payment 
  	WHERE means_of_payment_code IN 
  	<foreach collection="list" item="item" open="(" separator="," close=")">
  		#{item}
  	</foreach>
  </select>
  
</mapper> 