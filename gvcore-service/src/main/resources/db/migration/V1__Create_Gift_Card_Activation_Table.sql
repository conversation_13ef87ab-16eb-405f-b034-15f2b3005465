-- 创建礼品卡激活表
CREATE TABLE gc_activation (
    id BIGSERIAL PRIMARY KEY,
    card_number VARCHAR(50) NOT NULL,
    activation_code VARCHAR(50) NOT NULL,
    activation_time TIMESTAMP NOT NULL,
    expiry_time TIMESTAMP NOT NULL,
    status VARCHAR(20) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT uk_card_number UNIQUE (card_number),
    CONSTRAINT uk_activation_code UNIQUE (activation_code),
    CONSTRAINT chk_status CHECK (status IN ('ACTIVATED', 'CANCELLED')),
    CONSTRAINT chk_expiry_time CHECK (expiry_time > activation_time)
);

-- 创建索引
CREATE INDEX idx_gc_activation_card_number ON gc_activation(card_number);
CREATE INDEX idx_gc_activation_activation_code ON gc_activation(activation_code);
CREATE INDEX idx_gc_activation_status ON gc_activation(status);
CREATE INDEX idx_gc_activation_expiry_time ON gc_activation(expiry_time);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_gc_activation_updated_at
    BEFORE UPDATE ON gc_activation
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 添加注释
COMMENT ON TABLE gc_activation IS '礼品卡激活记录表';
COMMENT ON COLUMN gc_activation.id IS '主键ID';
COMMENT ON COLUMN gc_activation.card_number IS '礼品卡号';
COMMENT ON COLUMN gc_activation.activation_code IS '激活流水号';
COMMENT ON COLUMN gc_activation.activation_time IS '激活时间';
COMMENT ON COLUMN gc_activation.expiry_time IS '过期时间';
COMMENT ON COLUMN gc_activation.status IS '激活状态';
COMMENT ON COLUMN gc_activation.created_at IS '创建时间';
COMMENT ON COLUMN gc_activation.updated_at IS '更新时间'; 