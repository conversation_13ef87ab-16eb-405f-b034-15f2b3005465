
package com.gtech.gvcore.external;

import com.alibaba.fastjson.JSON;
import com.gtech.basic.masterdata.web.service.MasterDataValueService;
import com.gtech.gvcore.common.request.authorize.AuthorizePayload;
import com.gtech.gvcore.common.request.transaction.TransactionRequest;
import com.gtech.gvcore.common.response.transaction.TransactionResponse;
import com.gtech.gvcore.common.response.voucher.VerifyResponse;
import com.gtech.gvcore.service.PosService;
import com.gtech.gvcore.service.VoucherService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

@RunWith(MockitoJUnitRunner.class)
public class GvPosServiceTest {

	@InjectMocks
	GvPosService gvPosService;

	@Mock
	JwtService jwtService;
	@Mock
	PosService posService;
	@Mock
	VoucherService voucherService;
	@Mock
	MasterDataValueService masterDataValueService;

	@Mock
	private RedisTemplate redisTemplate;
	@Mock
	private ValueOperations valueOperations;


	/*@Test
	public void authorize() {
		AuthorizeRequest param = new AuthorizeRequest();
		param.setTerminalId("Pos test machine id");
		Result<OutletResponse> queryOutletByPosIdResult = new Result<>();
		queryOutletByPosIdResult.setCode("0");
		queryOutletByPosIdResult.setData(new OutletResponse());
		Mockito.when(posService.queryOutletByPosId(Mockito.any())).thenReturn(queryOutletByPosIdResult);
		Mockito.when(jwtService.create(Mockito.any())).thenReturn(
				"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJhdXRoMCIsImV4cCI6MTY0OTc0MzY0MywiR1YiOiJ7XCJmb3J3YXJkaW5nRW50aXR5SWRcIjpcIjEyM1wiLFwiZm9yd2FyZGluZ0VudGl0eVBhc3N3b3JkXCI6XCIxMjM0NTZcIixcInBhc3N3b3JkXCI6XCIxMjM0NTZcIixcInRlcm1pbmFsSWRcIjpcIlBvcyB0ZXN0IG1hY2hpbmUgaWRcIixcInVzZXJOYW1lXCI6XCJ0b255XCJ9In0.SxncDMz5qruG2iEUHsELiYyLb1XUmu5IAphC40HCsu0");
		Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
		AuthorizeResponse authorizeResponse = gvPosService.authorize(param);
		Assert.assertTrue(!ObjectUtils.isEmpty(authorizeResponse));
	}
*/
	@Test
	public void getAuthorizePayloadByToken() {
		AuthorizePayload authorizePayload = new AuthorizePayload();
		authorizePayload.setTerminalId("Pos test machine id");
		Mockito.when(jwtService.resolve(Mockito.any(), Mockito.any())).thenReturn(authorizePayload);
		AuthorizePayload response = gvPosService.getAuthorizePayloadByToken("eyJ0eXAiOiJ");
		Assert.assertTrue(!ObjectUtils.isEmpty(response));
	}

	@Test
	public void transactionValidateActivate() {
		AuthorizePayload authorizePayload = new AuthorizePayload();
		authorizePayload.setTerminalId("Pos test machine id");
		authorizePayload.setCurrentBatchNumber("123123");
		TransactionRequest param = this.getTransactionRequest();
		param.setActionType("2");
		param.setTransactionTypeId(705);
		VerifyResponse verifyResponse = this.getVerifyResponse();
		Mockito.when(voucherService.verifyVoucher(Mockito.any())).thenReturn(verifyResponse);
		TransactionResponse response = gvPosService.transactionValidateActivate(param, authorizePayload);
		Assert.assertTrue(!ObjectUtils.isEmpty(response));
	}

	@Test
	public void transactionExecuteActivate() {
		AuthorizePayload authorizePayload = new AuthorizePayload();
		authorizePayload.setTerminalId("Pos test machine id");
		authorizePayload.setCurrentBatchNumber("123123");
		TransactionRequest param = this.getTransactionRequest();
		param.setActionType("1");
		param.setTransactionTypeId(705);
		VerifyResponse verifyResponse = this.getVerifyResponse();
		Mockito.when(voucherService.verifyVoucher(Mockito.any())).thenReturn(verifyResponse);
		TransactionResponse response = gvPosService.transactionExecuteActivate(param, authorizePayload);
		Assert.assertTrue(!ObjectUtils.isEmpty(response));
	}

	@Test
	public void transactionValidateRedeem() {
		AuthorizePayload authorizePayload = new AuthorizePayload();
		authorizePayload.setTerminalId("Pos test machine id");
		authorizePayload.setCurrentBatchNumber("123123");
		TransactionRequest param = this.getTransactionRequest();
		param.setActionType("2");
		param.setTransactionTypeId(702);
		VerifyResponse verifyResponse = this.getVerifyResponse();
		Mockito.when(voucherService.verifyVoucher(Mockito.any())).thenReturn(verifyResponse);
		TransactionResponse response = gvPosService.transactionValidateRedeem(param, authorizePayload);
		Assert.assertTrue(!ObjectUtils.isEmpty(response));
	}

	@Test
	public void transactionExecuteRedeem() {
		AuthorizePayload authorizePayload = new AuthorizePayload();
		authorizePayload.setTerminalId("Pos test machine id");
		authorizePayload.setCurrentBatchNumber("123123");
		TransactionRequest param = this.getTransactionRequest();
		param.setActionType("1");
		param.setTransactionTypeId(702);
		VerifyResponse verifyResponse = this.getVerifyResponse();
		Mockito.when(voucherService.verifyVoucher(Mockito.any())).thenReturn(verifyResponse);
		TransactionResponse response = gvPosService.transactionExecuteRedeem(param, authorizePayload);
		Assert.assertTrue(!ObjectUtils.isEmpty(response));
	}

	private TransactionRequest getTransactionRequest() {
		String transactionRequestStr = "{" + "    \"actionType\": \"2\"," + "    \"transactionTypeId\": 705,"
				+ "    \"lineItems\": [" + "        {" + "            \"lineItemNo\": \"01\","
				+ "            \"inputType\": \"1\"," + "            \"numberOfCards\": \"1\","
				+ "            \"cardInfo\": {" + "                \"cardNumber\": \"1004224220000001\""
				+ "            }" + "        }," + "        {" + "            \"lineItemNo\": \"02\","
				+ "            \"inputType\": \"1\"," + "            \"numberOfCards\": \"1\","
				+ "            \"cardInfo\": {" + "                \"cardNumber\": \"1004224220000002\""
				+ "            }" + "        }," + "        {" + "            \"lineItemNo\": \"03\","
				+ "            \"inputType\": \"1\"," + "            \"numberOfCards\": \"1\","
				+ "            \"cardInfo\": {" + "                \"cardNumber\": \"1004224220000003\""
				+ "            }" + "        }," + "        {" + "            \"lineItemNo\": \"04\","
				+ "            \"inputType\": \"2\"," + "            \"numberOfCards\": \"3\","
				+ "            \"startCardInfo\": {" + "                \"cardNumber\": \"1004224220000004\""
				+ "            }," + "            \"endCardInfo\": {"
				+ "                \"cardNumber\": \"1004224220000006\"" + "            }" + "        }" + "    ],"
				+ "    \"actualMerchantOutletName\": \"123456\"," + "    \"cardholderInfo\": {"
				+ "        \"address1\": \"123456\"," + "        \"address2\": \"123456\","
				+ "        \"address3\": \"123456\"," + "        \"city\": \"123456\","
				+ "        \"corporatename\": \"123456\"," + "        \"country\": \"123456\","
				+ "        \"dateOfBirth\": \"2022-02-02\"," + "        \"email\": \"123456\","
				+ "        \"empid\": \"123456\"," + "        \"firstName\": \"123456\","
				+ "        \"lastName\": \"123456\"," + "        \"mobile\": \"123456\","
				+ "        \"salutation\": \"123456\"," + "        \"state\": \"123456\"" + "    },"
				+ "    \"corporateName\": \"123456\"," + "    \"dateAtClient\": \"2022-02-02 18:00:00\","
				+ "    \"discountInfo\": {" + "        \"discountAmount\": 1," + "        \"discountPercentage\": 1"
				+ "    }," + "    \"idempotencyKey\": \"123456\"," + "    \"invoiceAmount\": 0,"
				+ "    \"invoiceDate\": \"2022-02-02 18:00:00\"," + "    \"invoiceNumber\": \"123456\","
				+ "    \"isSync\": \"0\"," + "    \"messageId\": \"123456\"," + "    \"notes\": \"123456\","
				+ "    \"orderNumber\": \"123456\"," + "    \"orderType\": \"1\"," + "    \"paymentInfo\": ["
				+ "        {" + "            \"paymentDetails\": \"1\"," + "            \"paymentModeId\": 1"
				+ "        }" + "    ]," + "    \"purchaserInfo\": {" + "        \"address1\": \"123456\","
				+ "        \"address2\": \"123456\"," + "        \"address3\": \"123456\","
				+ "        \"city\": \"123456\"," + "        \"corporatename\": \"123456\","
				+ "        \"country\": \"123456\"," + "        \"dateOfBirth\": \"2022-02-02\","
				+ "        \"email\": \"123456\"," + "        \"empid\": \"123456\","
				+ "        \"firstName\": \"123456\"," + "        \"lastName\": \"123456\","
				+ "        \"mobile\": \"123456\"," + "        \"salutation\": \"123456\","
				+ "        \"state\": \"123456\"" + "    }," + "    \"sendOnlyFailedCards\": \"0\","
				+ "    \"sourceId\": \"123456\"," + "    \"transactionAmount\": 0," + "    \"transactionId\": 123,"
				+ "    \"transactionModeId\": \"0\"," + "    \"validationToken\": \"123456\"" + "}";
		return JSON.parseObject(transactionRequestStr, TransactionRequest.class);
	}

	private VerifyResponse getVerifyResponse() {
		String verifyResponseStr = "{" + "	\"failVoucherList\": [" + "		{"
				+ "			\"cardProgramGroupName\": \"100K-TEST\"," + "			\"designCode\": \"100K-TEST\","
				+ "			\"endCardNumber\": \"1004224220000001\"," + "			\"isSuccess\": false,"
				+ "			\"productCode\": \"\"," + "			\"startCardNumber\": \"1004224220000001\","
				+ "			\"successCardCount\": 0," + "			\"totalAmount\": 1000000.0,"
				+ "			\"totalCardCount\": 1," + "			\"verifyVoucherResponses\": [" + "				{"
				+ "					\"isSuccess\": false," + "					\"responseCode\": 10249,"
				+ "					\"responseMessage\": \"Voucher expired\"," + "					\"voucherInfo\": {"
				+ "						\"bookletCode\": \"9001220000000001\","
				+ "						\"bookletCodeNum\": 9001220000000001,"
				+ "						\"circulationStatus\": 2,"
				+ "						\"cpgCode\": \"7878d31562a5455c8eed8e609fa1da83\","
				+ "						\"cpgName\": \"100K-TEST\","
				+ "						\"denomination\": 1000000.0,"
				+ "						\"issuerCode\": \"IS102202231543001237\","
				+ "						\"mopCode\": \"100K-TEST\"," + "						\"status\": 0,"
				+ "						\"voucherActiveCode\": \"930291bf0ab943049fde23d6a0bb7352\","
				+ "						\"voucherBarcode\": \"21060492274292000060060170\","
				+ "						\"voucherBatchCode\": \"IS102202231543001237-001\","
				+ "						\"voucherCode\": \"1004224220000001\","
				+ "						\"voucherCodeNum\": 1004224220000001,"
				+ "						\"voucherEffectiveDate\": 1638201600000,"
				+ "						\"voucherOwnerCode\": \"WH01\","
				+ "						\"voucherOwnerType\": \"warehouse\","
				+ "						\"voucherStatus\": 1" + "					}" + "				}"
				+ "			]" + "		}," + "		{" + "			\"cardProgramGroupName\": \"100K-TEST\","
				+ "			\"designCode\": \"100K-TEST\"," + "			\"endCardNumber\": \"1004224220000002\","
				+ "			\"isSuccess\": false," + "			\"productCode\": \"\","
				+ "			\"startCardNumber\": \"1004224220000002\"," + "			\"successCardCount\": 0,"
				+ "			\"totalAmount\": 1000000.0," + "			\"totalCardCount\": 1,"
				+ "			\"verifyVoucherResponses\": [" + "				{"
				+ "					\"isSuccess\": false," + "					\"responseCode\": 10249,"
				+ "					\"responseMessage\": \"Voucher expired\"," + "					\"voucherInfo\": {"
				+ "						\"bookletCode\": \"9001220000000001\","
				+ "						\"bookletCodeNum\": 9001220000000001,"
				+ "						\"circulationStatus\": 2,"
				+ "						\"cpgCode\": \"7878d31562a5455c8eed8e609fa1da83\","
				+ "						\"cpgName\": \"100K-TEST\","
				+ "						\"denomination\": 1000000.0,"
				+ "						\"issuerCode\": \"IS102202231543001237\","
				+ "						\"mopCode\": \"100K-TEST\"," + "						\"status\": 0,"
				+ "						\"voucherActiveCode\": \"930291bf0ab943049fde23d6a0bb7352\","
				+ "						\"voucherBarcode\": \"91030462254252020010080268\","
				+ "						\"voucherBatchCode\": \"IS102202231543001237-001\","
				+ "						\"voucherCode\": \"1004224220000002\","
				+ "						\"voucherCodeNum\": 1004224220000002,"
				+ "						\"voucherEffectiveDate\": 1638201600000,"
				+ "						\"voucherOwnerCode\": \"WH01\","
				+ "						\"voucherOwnerType\": \"warehouse\","
				+ "						\"voucherStatus\": 1" + "					}" + "				}"
				+ "			]" + "		}," + "		{" + "			\"cardProgramGroupName\": \"100K-TEST\","
				+ "			\"designCode\": \"100K-TEST\"," + "			\"endCardNumber\": \"1004224220000003\","
				+ "			\"isSuccess\": false," + "			\"productCode\": \"\","
				+ "			\"startCardNumber\": \"1004224220000003\"," + "			\"successCardCount\": 0,"
				+ "			\"totalAmount\": 1000000.0," + "			\"totalCardCount\": 1,"
				+ "			\"verifyVoucherResponses\": [" + "				{"
				+ "					\"isSuccess\": false," + "					\"responseCode\": 10249,"
				+ "					\"responseMessage\": \"Voucher expired\"," + "					\"voucherInfo\": {"
				+ "						\"bookletCode\": \"9001220000000001\","
				+ "						\"bookletCodeNum\": 9001220000000001,"
				+ "						\"circulationStatus\": 2,"
				+ "						\"cpgCode\": \"7878d31562a5455c8eed8e609fa1da83\","
				+ "						\"cpgName\": \"100K-TEST\","
				+ "						\"denomination\": 1000000.0,"
				+ "						\"issuerCode\": \"IS102202231543001237\","
				+ "						\"mopCode\": \"100K-TEST\"," + "						\"status\": 0,"
				+ "						\"voucherActiveCode\": \"930291bf0ab943049fde23d6a0bb7352\","
				+ "						\"voucherBarcode\": \"11060472284282090030020339\","
				+ "						\"voucherBatchCode\": \"IS102202231543001237-001\","
				+ "						\"voucherCode\": \"1004224220000003\","
				+ "						\"voucherCodeNum\": 1004224220000003,"
				+ "						\"voucherEffectiveDate\": 1638201600000,"
				+ "						\"voucherOwnerCode\": \"WH01\","
				+ "						\"voucherOwnerType\": \"warehouse\","
				+ "						\"voucherStatus\": 1" + "					}" + "				}"
				+ "			]" + "		}," + "		{" + "			\"cardProgramGroupName\": \"100K-TEST\","
				+ "			\"designCode\": \"100K-TEST\"," + "			\"endCardNumber\": \"1004224220000006\","
				+ "			\"isSuccess\": false," + "			\"productCode\": \"\","
				+ "			\"startCardNumber\": \"1004224220000004\"," + "			\"successCardCount\": 0,"
				+ "			\"totalAmount\": 3000000.0," + "			\"totalCardCount\": 3,"
				+ "			\"verifyVoucherResponses\": [" + "				{"
				+ "					\"isSuccess\": false," + "					\"responseCode\": 10249,"
				+ "					\"responseMessage\": \"Voucher expired\"," + "					\"voucherInfo\": {"
				+ "						\"bookletCode\": \"9001220000000001\","
				+ "						\"bookletCodeNum\": 9001220000000001,"
				+ "						\"circulationStatus\": 2,"
				+ "						\"cpgCode\": \"7878d31562a5455c8eed8e609fa1da83\","
				+ "						\"cpgName\": \"100K-TEST\","
				+ "						\"denomination\": 1000000.0,"
				+ "						\"issuerCode\": \"IS102202231543001237\","
				+ "						\"mopCode\": \"100K-TEST\"," + "						\"status\": 0,"
				+ "						\"voucherActiveCode\": \"930291bf0ab943049fde23d6a0bb7352\","
				+ "						\"voucherBarcode\": \"91080432294222070020010416\","
				+ "						\"voucherBatchCode\": \"IS102202231543001237-001\","
				+ "						\"voucherCode\": \"1004224220000004\","
				+ "						\"voucherCodeNum\": 1004224220000004,"
				+ "						\"voucherEffectiveDate\": 1638201600000,"
				+ "						\"voucherOwnerCode\": \"WH01\","
				+ "						\"voucherOwnerType\": \"warehouse\","
				+ "						\"voucherStatus\": 1" + "					}" + "				},"
				+ "				{" + "					\"isSuccess\": false,"
				+ "					\"responseCode\": 10249,"
				+ "					\"responseMessage\": \"Voucher expired\"," + "					\"voucherInfo\": {"
				+ "						\"bookletCode\": \"9001220000000001\","
				+ "						\"bookletCodeNum\": 9001220000000001,"
				+ "						\"circulationStatus\": 2,"
				+ "						\"cpgCode\": \"7878d31562a5455c8eed8e609fa1da83\","
				+ "						\"cpgName\": \"100K-TEST\","
				+ "						\"denomination\": 1000000.0,"
				+ "						\"issuerCode\": \"IS102202231543001237\","
				+ "						\"mopCode\": \"100K-TEST\"," + "						\"status\": 0,"
				+ "						\"voucherActiveCode\": \"930291bf0ab943049fde23d6a0bb7352\","
				+ "						\"voucherBarcode\": \"21080412214242080040070578\","
				+ "						\"voucherBatchCode\": \"IS102202231543001237-001\","
				+ "						\"voucherCode\": \"1004224220000005\","
				+ "						\"voucherCodeNum\": 1004224220000005,"
				+ "						\"voucherEffectiveDate\": 1638201600000,"
				+ "						\"voucherOwnerCode\": \"WH01\","
				+ "						\"voucherOwnerType\": \"warehouse\","
				+ "						\"voucherStatus\": 1" + "					}" + "				},"
				+ "				{" + "					\"isSuccess\": false,"
				+ "					\"responseCode\": 10249,"
				+ "					\"responseMessage\": \"Voucher expired\"," + "					\"voucherInfo\": {"
				+ "						\"bookletCode\": \"9001220000000001\","
				+ "						\"bookletCodeNum\": 9001220000000001,"
				+ "						\"circulationStatus\": 2,"
				+ "						\"cpgCode\": \"7878d31562a5455c8eed8e609fa1da83\","
				+ "						\"cpgName\": \"100K-TEST\","
				+ "						\"denomination\": 1000000.0,"
				+ "						\"issuerCode\": \"IS102202231543001237\","
				+ "						\"mopCode\": \"100K-TEST\"," + "						\"status\": 0,"
				+ "						\"voucherActiveCode\": \"930291bf0ab943049fde23d6a0bb7352\","
				+ "						\"voucherBarcode\": \"51010492204282010000080600\","
				+ "						\"voucherBatchCode\": \"IS102202231543001237-001\","
				+ "						\"voucherCode\": \"1004224220000006\","
				+ "						\"voucherCodeNum\": 1004224220000006,"
				+ "						\"voucherEffectiveDate\": 1638201600000,"
				+ "						\"voucherOwnerCode\": \"WH01\","
				+ "						\"voucherOwnerType\": \"warehouse\","
				+ "						\"voucherStatus\": 1" + "					}" + "				}"
				+ "			]" + "		}," + "		{" + "			\"$ref\": \"$.failVoucherList[3]\"" + "		},"
				+ "		{" + "			\"$ref\": \"$.failVoucherList[3]\"" + "		}" + "	],"
				+ "	\"successVoucherList\": [" + "		{" + "			\"cardProgramGroupName\": \"100K-TEST\","
				+ "			\"designCode\": \"100K-TEST\"," + "			\"endCardNumber\": \"1004224220000001\","
				+ "			\"isSuccess\": true," + "			\"productCode\": \"\","
				+ "			\"startCardNumber\": \"1004224220000001\"," + "			\"successCardCount\": 0,"
				+ "			\"totalAmount\": 1000000.0," + "			\"totalCardCount\": 1,"
				+ "			\"verifyVoucherResponses\": [" + "				{"
				+ "					\"$ref\": \"$.failVoucherList[0].verifyVoucherResponses[0]\"" + "				}"
				+ "			]" + "		}," + "		{" + "			\"cardProgramGroupName\": \"100K-TEST\","
				+ "			\"designCode\": \"100K-TEST\"," + "			\"endCardNumber\": \"1004224220000002\","
				+ "			\"isSuccess\": true," + "			\"productCode\": \"\","
				+ "			\"startCardNumber\": \"1004224220000002\"," + "			\"successCardCount\": 0,"
				+ "			\"totalAmount\": 1000000.0," + "			\"totalCardCount\": 1,"
				+ "			\"verifyVoucherResponses\": [" + "				{"
				+ "					\"$ref\": \"$.failVoucherList[1].verifyVoucherResponses[0]\"" + "				}"
				+ "			]" + "		}," + "		{" + "			\"cardProgramGroupName\": \"100K-TEST\","
				+ "			\"designCode\": \"100K-TEST\"," + "			\"endCardNumber\": \"1004224220000003\","
				+ "			\"isSuccess\": true," + "			\"productCode\": \"\","
				+ "			\"startCardNumber\": \"1004224220000003\"," + "			\"successCardCount\": 0,"
				+ "			\"totalAmount\": 1000000.0," + "			\"totalCardCount\": 1,"
				+ "			\"verifyVoucherResponses\": [" + "				{"
				+ "					\"$ref\": \"$.failVoucherList[2].verifyVoucherResponses[0]\"" + "				}"
				+ "			]" + "		}," + "		{" + "			\"cardProgramGroupName\": \"100K-TEST\","
				+ "			\"designCode\": \"100K-TEST\"," + "			\"endCardNumber\": \"1004224220000006\","
				+ "			\"isSuccess\": true," + "			\"productCode\": \"\","
				+ "			\"startCardNumber\": \"1004224220000004\"," + "			\"successCardCount\": 0,"
				+ "			\"totalAmount\": 3000000.0," + "			\"totalCardCount\": 3,"
				+ "			\"verifyVoucherResponses\": [" + "				{"
				+ "					\"$ref\": \"$.failVoucherList[3].verifyVoucherResponses[0]\"" + "				},"
				+ "				{" + "					\"$ref\": \"$.failVoucherList[3].verifyVoucherResponses[1]\""
				+ "				}," + "				{"
				+ "					\"$ref\": \"$.failVoucherList[3].verifyVoucherResponses[2]\"" + "				}"
				+ "			]" + "		}" + "	]" + "}";
		return JSON.parseObject(verifyResponseStr, VerifyResponse.class);
	}

}
