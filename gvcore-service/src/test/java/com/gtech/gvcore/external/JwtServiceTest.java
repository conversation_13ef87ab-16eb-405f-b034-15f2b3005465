
package com.gtech.gvcore.external;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import com.gtech.basic.masterdata.web.service.MasterDataValueService;
import com.gtech.gvcore.common.request.authorize.AuthorizePayload;

@RunWith(MockitoJUnitRunner.class)
public class JwtServiceTest {

	@InjectMocks
	JwtService jwtService;
	@Mock
	MasterDataValueService masterDataValueService;

	@Before
	public void init() {
		ReflectionTestUtils.setField(jwtService, "jwtSecret", "123");
		ReflectionTestUtils.setField(jwtService, "effectiveHours", 1);
	}

	@Test
	public void test() {

		AuthorizePayload payload = new AuthorizePayload();
		payload.setAcquirerId("1234");
		String token = jwtService.create(payload);
		jwtService.resolve(token, AuthorizePayload.class);
		Assert.assertTrue(true);
	}
}
