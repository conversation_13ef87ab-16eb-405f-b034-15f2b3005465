package com.gtech.gvcore.service;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.exchangerate.CreateExchangeRateRequest;
import com.gtech.gvcore.common.request.exchangerate.DeleteExchangeRateRequest;
import com.gtech.gvcore.common.request.exchangerate.GetExchangeRateRequest;
import com.gtech.gvcore.common.request.exchangerate.UpdateExchangeRateRequest;
import com.gtech.gvcore.common.request.exchangerate.UpdateExchangeRateStatusRequest;
import com.gtech.gvcore.common.utils.UUIDUtils;
import com.gtech.gvcore.dao.mapper.GvExchangeRateMapper;
import com.gtech.gvcore.dao.model.GvExchangeRateEntity;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.impl.GvExchangeRateServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@Slf4j
class GvExchangeRateServiceImplTest {

    @Mock
    private GvExchangeRateMapper mockGvExchangeRateMapper;
    @Mock
    private GvCodeHelper mockGvCodeHelper;

    @InjectMocks
    private GvExchangeRateServiceImpl gvExchangeRateServiceImplUnderTest;

    @Test
    void createExchangeRateTest() {
        when(mockGvExchangeRateMapper.insertSelective(Mockito.any())).thenReturn(1);
        CreateExchangeRateRequest createExchangeRateRequest = getCreateExchangeRateRequest();
        final Result<Object> result = gvExchangeRateServiceImplUnderTest.createExchangeRate(createExchangeRateRequest);
        Assert.assertTrue(result.isSuccess());
    }


    private CreateExchangeRateRequest getCreateExchangeRateRequest() {

        CreateExchangeRateRequest createExchangeRateRequest = new CreateExchangeRateRequest();

        createExchangeRateRequest.setCurrencyCode(UUIDUtils.generateCode());
        createExchangeRateRequest.setExchangeRate(BigDecimal.valueOf(1.3456));
        createExchangeRateRequest.setExchangeCurrencyCode(UUIDUtils.generateCode());
        createExchangeRateRequest.setExchangeRateDate(new Date());
        createExchangeRateRequest.setCreateUser("Admin");
        return createExchangeRateRequest;
    }
}
