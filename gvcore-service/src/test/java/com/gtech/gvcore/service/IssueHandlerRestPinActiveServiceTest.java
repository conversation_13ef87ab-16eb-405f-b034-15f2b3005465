
package com.gtech.gvcore.service;

import java.util.Arrays;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.gtech.gvcore.common.enums.IssueHandlingProcessStatusEnum;
import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.helper.VoucherNumberHelper;
import com.gtech.gvcore.service.impl.issuehandle.IssueHandlerResetPinActiveService;

import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

@RunWith(MockitoJUnitRunner.class)
public class IssueHandlerRestPinActiveServiceTest {

	@InjectMocks
	IssueHandlerResetPinActiveService resetPinActiveService;
	@Mock
	protected VoucherService voucherService;
	@Mock
	VoucherMapper voucherMapper;
	
	@Mock
	VoucherNumberHelper voucherNumberHelper;
	
	@Mock
	TransactionDataService transactionDataService;
	@Before
	public void before() {
		EntityHelper.initEntityNameMap(Voucher.class, new MapperHelper().getConfig());
	}
	
	@Test
	public void getIssueHandlingTypeTest() {
		resetPinActiveService.getIssueHandlingType();
	}

	@Test
	public void executeTest() {
		resetPinActiveService.execute(null, "1");
		IssueHandlingDetails issueHandlingDetails = new IssueHandlingDetails();
		resetPinActiveService.execute(Arrays.asList(issueHandlingDetails), "1");
		issueHandlingDetails.setVoucherCode("123");
		resetPinActiveService.execute(Arrays.asList(issueHandlingDetails), "1");
		Voucher voucher = new Voucher();
		voucher.setVoucherCode("123");
		Mockito.when(voucherService.queryByVoucherCodeList(Mockito.any(),Mockito.any())).thenReturn(Arrays.asList(voucher));
		resetPinActiveService.execute(Arrays.asList(issueHandlingDetails), "1");
	}
	
	@Test
	public void makeIssueHandlingTest() {
		IssueHandlingDetails issueHandlingDetails = new IssueHandlingDetails();
		issueHandlingDetails.setVoucherCode("123");
		issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.SUCCESS.code());
		resetPinActiveService.makeIssueHandling(issueHandlingDetails);
		Mockito.when(voucherMapper.updateByConditionSelective(Mockito.any(), Mockito.any())).thenReturn(1);
		issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.SUCCESS.code());
		resetPinActiveService.makeIssueHandling(issueHandlingDetails);
	}
}
