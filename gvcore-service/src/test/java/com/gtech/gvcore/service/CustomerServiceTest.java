package com.gtech.gvcore.service;

import com.gtech.basic.idm.dao.entity.OpUserTenantMappingEntity;
import com.gtech.basic.idm.dao.mapper.IOpUserTenantMappingMapper;
import com.gtech.commons.redis.GTechRedisTemplate;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.customer.*;
import com.gtech.gvcore.common.response.customer.CustomerResponse;
import com.gtech.gvcore.dao.mapper.CustomerMapper;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.impl.CustomerServiceImpl;
import com.gtech.gvcore.service.impl.MessageComponent;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/16 18:19
 */
@RunWith(MockitoJUnitRunner.class)
public class CustomerServiceTest {

    @InjectMocks
    private CustomerServiceImpl customerService;

    @Mock
    private CompanyService companyService;

    @Mock
    private CustomerPaymentMethodService customerPaymentMethodService;

    @Mock
    private CustomerProductCategoryService customerProductCategoryService;

    @Mock
    private MeansOfPaymentService meansOfPaymentService;

    @Mock
    private GvCodeHelper codeHelper;

    @Mock
    private CustomerMapper customerMapper;

    @Mock
    private GTechRedisTemplate gTechRedisTemplate;
    @Mock
    private MessageComponent messageComponent;

    @Mock
    private GvOpUserAccountService gvOpUserAccountService;
    @Mock
    private IOpUserTenantMappingMapper iOpUserTenantMappingMapper;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(Customer.class, new MapperHelper().getConfig());
        EntityHelper.initEntityNameMap(OpUserTenantMappingEntity.class, new MapperHelper().getConfig());
    }


    @Test
    public void createCustomer() {
        CreateCustomerRequest request = new CreateCustomerRequest();
        request.setCustomerName("name");
        request.setCreateUser("user");
        request.setMopGroup("123");

        request.setCreateUser("123");
        Result<String> ok = Result.ok("123123");

        customerService.createCustomer(request);
    }

    @Test
    public void updateCustomer() {
        UpdateCustomerRequest request = new UpdateCustomerRequest();
        request.setCustomerCode("code");
        request.setCustomerName("123");
        request.setStatus(0);
        request.setUpdateUser("123");

        Result<String> ok = Result.ok("123123");

        customerService.updateCustomer(request);
    }

    @Test
    public void updateCustomerStatus() {
        UpdateCustomerStatusRequest request = new UpdateCustomerStatusRequest();
        request.setCustomerCode("code");
        request.setStatus(0);
        request.setUpdateUser("123");


        customerService.updateCustomerStatus(request);
    }


    @Test
    public void deleteCustomer() {
        DeleteCustomerRequest request = new DeleteCustomerRequest();
        request.setCustomerCode("123");

        customerService.deleteCustomer(request);
    }


    @Test
    public void queryCustomerList() {

        QueryCustomerRequest request = new QueryCustomerRequest();
        ArrayList<Customer> objects = new ArrayList<>();
        List<Customer> gvCustomerEntities = new ArrayList<>();
        Customer Customer = new Customer();
        Customer.setId(0L);
        Customer.setCustomerCode("123");
        Customer.setCustomerName("123");
        Customer.setStatus(0);
        Customer.setCreateUser("123");
        Customer.setCreateTime(new Date());
        Customer.setUpdateUser("123");
        Customer.setUpdateTime(new Date());
        gvCustomerEntities.add(Customer);


        customerService.queryCustomerList(request);

    }


    @Test
    public void getCustomer() {

        GetCustomerRequest request = new GetCustomerRequest();
        request.setCustomerCode("123");
        CustomerResponse Customer = new CustomerResponse();
        Customer.setCustomerCode("123");
        Mockito.when(customerMapper.getCustomer(Mockito.any())).thenReturn(Customer);
        customerService.getCustomer(request);

    }


    @Test
    public void validateEmail() {
        Mockito.when(gTechRedisTemplate.opsValueGet(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("000000");
        ValidateEmailRequest validateEmailRequest = new ValidateEmailRequest();
        validateEmailRequest.setEmail("xxx");
        validateEmailRequest.setValidatedCode("000000");
        Assert.assertNotNull(customerService.validateEmail(validateEmailRequest));
    }

    @Test
    public void getEmailValidateCode() {
        Mockito.when(customerMapper.selectEmailExists(Mockito.any(),Mockito.any())).thenReturn(true);
        Assert.assertNotNull(customerService.getEmailValidateCode("<EMAIL>","MAP"));
    }

    @Test
    public void createIncompleteCustomer() {
        CreateIncompleteCustomerRequest createIncompleteCustomerRequest = new CreateIncompleteCustomerRequest();
        Mockito.when(codeHelper.generateCustomerCode()).thenReturn("xxx");
        createIncompleteCustomerRequest.setCustomerName("ssss");
        Assert.assertNotNull(customerService.createIncompleteCustomer(createIncompleteCustomerRequest));
    }

    @Test
    public void checkCustomerInfo() {
        Customer customer = new Customer();
        customer.setBankCardIssuer("xxx");
        Mockito.when(customerMapper.selectOne(Mockito.any())).thenReturn(customer);

        Assert.assertNotNull(customerService.checkCustomerInfo(new CheckCustomerInfoRequest()));
    }

    @Test
    public void queryCustomerByEmail() {
        Mockito.when(customerMapper.selectByCondition(Mockito.any())).thenReturn(Arrays.asList());
        Assert.assertNotNull(customerService.queryCustomerByEmail(new GetEmailValidateCodeRequest()));
    }
}
