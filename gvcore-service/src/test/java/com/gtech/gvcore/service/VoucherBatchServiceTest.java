package com.gtech.gvcore.service;

import com.google.common.collect.Lists;
import com.gtech.basic.filecloud.exports.management.FileExportManager;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.voucherbatch.*;
import com.gtech.gvcore.common.response.cpg.GetCpgResponse;
import com.gtech.gvcore.common.response.voucherbatch.VoucherBatchResponse;
import com.gtech.gvcore.dao.mapper.VoucherBatchMapper;
import com.gtech.gvcore.dao.mapper.VoucherBookletMapper;
import com.gtech.gvcore.dao.model.ArticleMop;
import com.gtech.gvcore.dao.model.VoucherBatch;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.helper.VoucherNumberHelper;
import com.gtech.gvcore.service.impl.VoucherBatchServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;

@RunWith(MockitoJUnitRunner.class)
public class VoucherBatchServiceTest {
    
    
    @InjectMocks
    private VoucherBatchServiceImpl voucherBatchService;

    @Mock
    private VoucherService voucherService;

    @Mock
    private VoucherDistributeService voucherDistributeService;

    @Mock
    private FileExportManager fileExportManager;

    @Mock
    private VoucherBatchMapper voucherBatchMapper;

    @Mock
    private VoucherBookletMapper voucherBookletMapper;


    @Mock
    private VoucherBookletService voucherBookletService;

    @Mock
    private VoucherNumberHelper voucherNumberHelper;

    @Mock
    private VoucherReceiveService voucherReceiveService;

    @Mock
    private ThreadPoolExecutor executor;

    @Mock
    private ArticleMopService articleMopService;

    @Mock
    private RedisTemplate redisTemplate;

    @Mock
    private GvUserAccountService userAccountService;

    @Mock
    private ValueOperations<String, String> valueOperations;

    @Mock
    private CpgService cpgService;

    @Mock
    private GvCodeHelper gvCodeHelper;

    @Mock
    @Value("#{${gv.issuer.warehouse}}: ")
    private Map<String, String> issuerWarehouseMap;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(VoucherBatch.class, new MapperHelper().getConfig());
    }

    @Test
    public void createVoucherBatch(){
        CreateVoucherBatchRequest request = new CreateVoucherBatchRequest();
        request.setPurchaseOrderNo("123123");
        request.setIssuerCode("********************");
        request.setCpgCode("12312");
        request.setVoucherBatchCode("MAP-003");
        request.setArticleCode("52dfd3cb01524d938f042f85aac912b7");
        request.setMopCode("setMopCode");
        request.setPrinterCode("CPC102202251417000264");
        request.setBookletStartNo("****************");
        request.setBookletEndNo("****************");
        request.setBookletPerNum(10000);
        request.setBookletNum(30);
        request.setVoucherStartNo("1002222220033001");
        request.setVoucherEndNo("1002222220033001");
        request.setVoucherNum(0);
        request.setDenomination(new BigDecimal("100.0"));
        request.setVoucherEffectiveDate(new Date());
        request.setFileName("20201221-01");
        request.setFileFormat("Excel");
        request.setPermissionCode("setPermissionCode");
        request.setCreateUser("test");
        request.setCreateTime(new Date());

        ArticleMop mop = new ArticleMop();
        mop.setMopCode("123");

        Result<GetCpgResponse> cpg = new Result<>();
        GetCpgResponse getCpgResponse = new GetCpgResponse();
        getCpgResponse.setCpgCode("123");
        getCpgResponse.setEffectiveDay(1);
        getCpgResponse.setEffectiveHour(1);
        getCpgResponse.setEffectiveMonth(1);
        getCpgResponse.setEffectiveYears(1);
        getCpgResponse.setGracePeriods(1);
        getCpgResponse.setDisableGeneration("0");
        cpg.setData(getCpgResponse);

        Mockito.when(cpgService.getCpg(Mockito.any())).thenReturn(cpg);


        Mockito.when(articleMopService.queryByArticleMopCode(Mockito.anyString())).thenReturn(mop);


        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);

        voucherBatchService.createVoucherBatch(request);

    }



    @Test
    public void regenerateVoucherBatch(){

        RegenerateVoucherBatchRequest request = new RegenerateVoucherBatchRequest();
        request.setVoucherBatchCode("");
        request.setPageNum(0);
        request.setPageSize(0);


        VoucherBatchResponse response = new VoucherBatchResponse();


        response.setPurchaseOrderNo("123123");
        response.setIssuerCode("123123");
        response.setCpgCode("12312");
        response.setVoucherBatchCode("MAP-003");
        response.setArticleCode("52dfd3cb01524d938f042f85aac912b7");
        response.setMopCode("setMopCode");
        response.setPrinterCode("CPC102202251417000264");
        response.setBookletStartNo("****************");
        response.setBookletEndNo("9001220000000091");
        response.setBookletPerNum(10000);
        response.setBookletNum(30);
        response.setVoucherStartNo("1002222220033001");
        response.setVoucherEndNo("1002222220043001");
        response.setVoucherNum(0);
        response.setDenomination(new BigDecimal("100.0"));
        response.setVoucherEffectiveDate(new Date());
        response.setFileName("20201221-01");
        response.setFileFormat("Excel");
        response.setPermissionCode("setPermissionCode");
        response.setCreateUser("test");
        response.setCreateTime(new Date());




        Mockito.when(voucherBatchMapper.getVoucherBatch(Mockito.any())).thenReturn(response);
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        voucherBatchService.regenerateVoucherBatch(request);
    }




    @Test
    public void queryVoucherBatch(){
        QueryVoucherBatchRequest request = new QueryVoucherBatchRequest();
        request.setCpgCode("");
        request.setVoucherBatchCode("");
        request.setVoucherStartNo("");
        request.setVoucherEndNo("");
        request.setStatus("");
        request.setPageNum(0);
        request.setPageSize(0);
        VoucherBatchResponse response = new VoucherBatchResponse();
        response.setPurchaseOrderNo("");
        response.setIssuerCode("");
        response.setIssuerName("");
        response.setCpgName("");
        response.setCpgCode("");
        response.setVoucherBatchCode("");
        response.setArticleCode("");
        response.setArticleName("");
        response.setMopCode("");
        response.setMopName("");
        response.setPrinterCode("");
        response.setPrinterName("");
        response.setBookletStartNo("");
        response.setBookletEndNo("");
        response.setBookletPerNum(0);
        response.setBookletNum(0);
        response.setVoucherStartNo("");
        response.setVoucherEndNo("");
        response.setVoucherNum(0);
        response.setDenomination(new BigDecimal("0"));
        response.setVoucherEffectiveDate(new Date());
        response.setFileName("");
        response.setFileFormat("");
        response.setPermissionCode("");
        response.setVoucherNumActive(0);
        response.setVoucherNumUsed(0);
        response.setRealTimeProgress(0);
        response.setStatus(0);
        response.setCreateUser("");
        response.setCreateTime(new Date());
        response.setUpdateUser("");
        response.setUpdateTime(new Date());


        Mockito.when(voucherBatchMapper.queryVoucherBatchList(Mockito.any())).thenReturn(Lists.newArrayList(response));
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        voucherBatchService.queryVoucherBatch(request);


    }



    @Test
    public void getVoucherBatch(){
        GetVoucherBatchRequest request = new GetVoucherBatchRequest();


        voucherBatchService.getVoucherBatch(request);

    }



//    @Test
//    public void realTimeProgressBar(){
//        QueryRealTimeProgressBarRequest request = new QueryRealTimeProgressBarRequest();
//        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
//        voucherBatchService.realTimeProgressBar(request);
//
//    }



    @Test
    public void queryStartCode(){
        QueryStartCodeRequest request = new QueryStartCodeRequest();
        request.setDenomination(new BigDecimal("100"));


        voucherBatchService.queryStartCode(request);

    }



    /*public void export() throws IOException {
        SendVoucherRequest request = new SendVoucherRequest();
        request.setVoucherBatchCode("MAP-001");
        request.setCreateUser("test");


        VoucherBatchResponse response = new VoucherBatchResponse();
        response.setPurchaseOrderNo("");
        response.setIssuerCode("");
        response.setIssuerName("");
        response.setCpgName("");
        response.setCpgCode("");
        response.setVoucherBatchCode("");
        response.setArticleCode("");
        response.setArticleName("");
        response.setMopCode("");
        response.setMopName("");
        response.setPrinterCode("");
        response.setPrinterName("");
        response.setBookletStartNo("");
        response.setBookletEndNo("");
        response.setBookletPerNum(0);
        response.setBookletNum(0);
        response.setVoucherStartNo("");
        response.setVoucherEndNo("");
        response.setVoucherNum(0);
        response.setDenomination(new BigDecimal("0"));
        response.setVoucherEffectiveDate(new Date());
        response.setFileName("");
        response.setFileFormat("");
        response.setPermissionCode("");
        response.setVoucherNumActive(0);
        response.setVoucherNumUsed(0);
        response.setRealTimeProgress(0);
        response.setStatus(0);
        response.setCreateUser("");
        response.setCreateTime(new Date());
        response.setUpdateUser("");
        response.setUpdateTime(new Date());


        CompletableFuture<FileExportResult> exportResultCompletableFuture = new CompletableFuture<>();


        Resp<FileExportResult> resp = new Resp();


        Mockito.when(voucherBatchMapper.getVoucherBatch(Mockito.any())).thenReturn(response);
        Mockito.when(voucherNumberHelper.randomPassword(Mockito.anyInt())).thenReturn("123123");

        *//*Mockito.when(fileExportManager.export(Mockito.any())).thenReturn(exportResultCompletableFuture);
        Mockito.doAnswer(
                (InvocationOnMock invocation) -> {
                    ((Runnable) invocation.getArguments()[0]).run();
                    return null;
                }
        ).when(executor).execute(Mockito.any(Runnable.class));*//*

        voucherBatchService.export(request);

    }*/

}
