package com.gtech.gvcore.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.gtech.commons.redis.GTechRedisTemplate;
import com.gtech.gvcore.common.enums.ApproveNodeRecordTypeEnum;
import com.gtech.gvcore.common.request.releaseapprove.ApproveNodeRecordRequest;
import com.gtech.gvcore.common.request.releaseapprove.CreateLogRecode;
import com.gtech.gvcore.common.request.releaseapprove.QueryApproveNodeRequest;
import com.gtech.gvcore.common.request.releaseapprove.ReleaseApproveAbleRequest;
import com.gtech.gvcore.common.request.releaseapprove.ReleaseApproveAmountRequest;
import com.gtech.gvcore.common.request.releaseapprove.ReleaseApproveNodeRequest;
import com.gtech.gvcore.dao.mapper.ApproveNodeRecordMapper;
import com.gtech.gvcore.dao.mapper.CustomerOrderMapper;
import com.gtech.gvcore.dao.mapper.ReleaseApproveAmountMapper;
import com.gtech.gvcore.dao.mapper.ReleaseApproveNodeMapper;
import com.gtech.gvcore.dao.model.ApproveNodeRecord;
import com.gtech.gvcore.dao.model.ReleaseApproveAmount;
import com.gtech.gvcore.dao.model.ReleaseApproveNode;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.impl.MessageComponent;
import com.gtech.gvcore.service.impl.ReleaseApproveServiceImpl;

import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

@RunWith(MockitoJUnitRunner.Silent.class)
public class ReleaseApproveServiceImplTest {

    @InjectMocks
    private ReleaseApproveServiceImpl releaseApproveService;

    @Mock
    private ReleaseApproveAmountMapper releaseApproveAmountMapper;
    @Mock
    private ReleaseApproveNodeMapper releaseApproveNodeMapper;
    @Mock
    private GvCodeHelper gvCodeHelper;
    @Mock
    private CustomerOrderMapper customerOrderMapper;
    @Mock
    private ApproveNodeRecordMapper approveNodeRecordMapper;
    @Mock
    private MessageComponent messageComponent;

	@Mock
	private GvUserAccountService gvUserAccountService;

    @Mock
    private GTechRedisTemplate gTechRedisTemplate;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(ApproveNodeRecord.class, new MapperHelper().getConfig());
        EntityHelper.initEntityNameMap(ReleaseApproveAmount.class, new MapperHelper().getConfig());
    }

    @Test
    public void settingApproveConfig() {
        List<ReleaseApproveAmountRequest> releaseApproveAmountRequests = new ArrayList<>();
        ReleaseApproveAmountRequest releaseApproveAmountRequest1 = ReleaseApproveAmountRequest.builder()
                .createUser("createUser")
                .rangeName(1)
                .startNum(BigDecimal.ONE)
                .endNum(new BigDecimal(200))
                .type("ReleaseApproveConfig").build();
        ReleaseApproveAmountRequest releaseApproveAmountRequest2 = ReleaseApproveAmountRequest.builder()
                .createUser("createUser")
                .rangeName(2)
                .startNum(new BigDecimal(201))
                .endNum(new BigDecimal(1000))
                .type("ReleaseApproveConfig").build();
        ReleaseApproveNodeRequest automaticApprove1 = new ReleaseApproveNodeRequest(1, "AUTOMATIC_APPROVE");
        ReleaseApproveNodeRequest automaticApprove2 = new ReleaseApproveNodeRequest(1, "admin");
        ReleaseApproveNodeRequest automaticApprove3 = new ReleaseApproveNodeRequest(2, "admin1");
        List<ReleaseApproveNodeRequest> releaseApproveNodeResponses1 = new ArrayList<>();
        List<ReleaseApproveNodeRequest> releaseApproveNodeResponses2 = new ArrayList<>();
        List<ReleaseApproveNodeRequest> releaseApproveNodeResponses3 = new ArrayList<>();

        releaseApproveNodeResponses1.add(automaticApprove1);
        releaseApproveNodeResponses2.add(automaticApprove2);
        releaseApproveNodeResponses2.add(automaticApprove3);
        releaseApproveNodeResponses3.add(automaticApprove3);

        releaseApproveAmountRequest1.setReleaseApproveNodeRequests(releaseApproveNodeResponses1);
        releaseApproveAmountRequest2.setReleaseApproveNodeRequests(releaseApproveNodeResponses2);

        releaseApproveAmountRequests.add(releaseApproveAmountRequest2);
        releaseApproveAmountRequests.add(releaseApproveAmountRequest1);

        Assert.assertNotNull(releaseApproveService.settingApproveConfig(releaseApproveAmountRequests));
    }

    @Test
    public void queryApproveConfig() {
		Mockito.when(releaseApproveAmountMapper.selectAllAndSort("123"))
                .thenReturn(Arrays.asList(
                        ReleaseApproveAmount.builder().releaseApproveAmountCode("1").build(),
                        ReleaseApproveAmount.builder().releaseApproveAmountCode("2").build(),
                        ReleaseApproveAmount.builder().releaseApproveAmountCode("3").build()
                ));
        Mockito.when(releaseApproveNodeMapper.selectByAmountCodeAndSort(Mockito.any()))
                .thenReturn(Arrays.asList(
                        ReleaseApproveNode.builder().build(),
                        ReleaseApproveNode.builder().build()
                ));
		Assert.assertNotNull(releaseApproveService.queryApproveConfig("123"));

    }

    @Test
    public void releaseApproveAble() {
        ReleaseApproveAbleRequest releaseApproveAbleRequest = new ReleaseApproveAbleRequest();
        releaseApproveAbleRequest.setApproveRoleCode("approveRoleCode");

        List<ReleaseApproveNode> selectNodesByAmountAndType = new ArrayList<>();
        ReleaseApproveNode approveNode = new ReleaseApproveNode();
        approveNode.setRoleCode("roleCode");
        selectNodesByAmountAndType.add(approveNode);

		Mockito.when(releaseApproveNodeMapper.selectNodesByAmountAndType(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(selectNodesByAmountAndType);

        List<ApproveNodeRecord> selectByBusinessAndType = new ArrayList<>();
        ApproveNodeRecord approveNodeRecord = new ApproveNodeRecord();
        approveNodeRecord.setReleaseApproveNodeName(1);
        selectByBusinessAndType.add(approveNodeRecord);
        Mockito.when(approveNodeRecordMapper.selectByBusinessAndType(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(selectByBusinessAndType);
        Assert.assertNotNull(releaseApproveService.approveAble(releaseApproveAbleRequest));
    }

    @Test
    public void approveAble() {
        ReleaseApproveAbleRequest releaseApproveAbleRequest = new ReleaseApproveAbleRequest();
        releaseApproveAbleRequest.setApproveRoleCode("admin");
        List<ReleaseApproveNode> selectNodesByAmountAndType = new ArrayList<>();
        ReleaseApproveNode releaseApproveNode = new ReleaseApproveNode();
        releaseApproveNode.setNodeName(1);
        releaseApproveNode.setRoleCode("AUTOMATIC_APPROVE");
        releaseApproveNode.setRoleCode("admin");
        selectNodesByAmountAndType.add(releaseApproveNode);
		Mockito.when(releaseApproveNodeMapper.selectNodesByAmountAndType(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(selectNodesByAmountAndType);

        List<ApproveNodeRecord> selectByBusinessAndType = new ArrayList<>();
//        ApproveNodeRecord approveNodeRecord = new ApproveNodeRecord();
//        approveNodeRecord.setReleaseApproveNodeName(1);
//        selectByBusinessAndType.add(approveNodeRecord);
        Mockito.when(approveNodeRecordMapper.selectByBusinessAndType(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(selectByBusinessAndType);
        Assert.assertNotNull(releaseApproveService.approveAble(releaseApproveAbleRequest));
    }

    @Test
    public void approve() {
        ApproveNodeRecordRequest approveNodeRecordRequest = new ApproveNodeRecordRequest();
        approveNodeRecordRequest.setRoleCode("xxxx");

        List<ReleaseApproveNode> releaseApproveNodes = new ArrayList<>();
        ReleaseApproveNode releaseApproveNode = new ReleaseApproveNode();
        releaseApproveNode.setRoleCode("AUTOMATIC_APPROVE");
        releaseApproveNode.setNodeName(1);
        releaseApproveNodes.add(releaseApproveNode);

        List<ApproveNodeRecord> approveNodeRecords = new ArrayList<>();
        ApproveNodeRecord approveNodeRecord = new ApproveNodeRecord();
        approveNodeRecord.setReleaseApproveNodeName(1);
        approveNodeRecord.setNextRoleCode("AUTOMATIC_APPROVE");
        approveNodeRecords.add(approveNodeRecord);
		Mockito.when(releaseApproveNodeMapper.selectNodesByAmountAndType(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(releaseApproveNodes);
        Mockito.when(approveNodeRecordMapper.selectByBusinessAndType(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(approveNodeRecords);

        Assert.assertNotNull(releaseApproveService.approve(approveNodeRecordRequest));

        Mockito.when(gTechRedisTemplate.opsValueSetIfAbsent(Mockito.any(String.class), Mockito.any(String.class),
                Mockito.any(String.class), Mockito.any(Long.class))).thenReturn(true);
        Assert.assertNotNull(releaseApproveService.approve(approveNodeRecordRequest));

    }

    @Test
    public void createLogRecord() {
        Mockito.when(gvCodeHelper.generateApproveNodeRecordCode()).thenReturn("xxx");
        Assert.assertNotNull(releaseApproveService.createLogRecord(new CreateLogRecode()));
    }

    @Test
    public void getLogBuyBusinessCodeAndType() {
        Mockito.when(approveNodeRecordMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(releaseApproveService.queryLogByBusinessCode("xx", "dd"));
    }

    @Test
    public void updateByDeleted() {

        releaseApproveService.updateByDeleted("businessCode", "updateUser",
                ApproveNodeRecordTypeEnum.RELEASE.getType());
        Assert.assertTrue(true);
    }

	@Test
	public void queryUserByApproveNodeTest() {
		QueryApproveNodeRequest queryApproveNodeRequest = new QueryApproveNodeRequest();
		ReleaseApproveNode releaseApproveNode = new ReleaseApproveNode();
		releaseApproveNode.setNodeName(1);
		releaseApproveNode.setRoleCode("12");
		Mockito.when(releaseApproveNodeMapper.selectNodesByAmountAndType(Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(Arrays.asList(releaseApproveNode));

		releaseApproveService.queryUserByApproveNode(queryApproveNodeRequest);

		Assert.assertTrue(true);
	}

}