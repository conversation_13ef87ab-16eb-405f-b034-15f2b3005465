package com.gtech.gvcore.service;

import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.VoucherAllocationBusinessTypeEnum;
import com.gtech.gvcore.common.enums.VoucherAllocationStatusEnum;
import com.gtech.gvcore.common.enums.VoucherCirculationStatusEnum;
import com.gtech.gvcore.common.enums.VoucherOwnerTypeEnum;
import com.gtech.gvcore.common.enums.VoucherRequestStatusEnum;
import com.gtech.gvcore.common.enums.VoucherStatusEnum;
import com.gtech.gvcore.common.request.allocation.AllocateRequest;
import com.gtech.gvcore.common.request.allocation.AllocateVoucherBatch;
import com.gtech.gvcore.common.response.voucherreceive.VoucherReceiveBatchResponse;
import com.gtech.gvcore.dao.dto.CpgVoucherCodeNumDto;
import com.gtech.gvcore.dao.dto.VoucherAllocationDto;
import com.gtech.gvcore.dao.dto.VoucherDto;
import com.gtech.gvcore.dao.mapper.VoucherAllocationMapper;
import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.dao.model.VoucherAllocation;
import com.gtech.gvcore.dao.model.VoucherAllocationBatch;
import com.gtech.gvcore.dao.model.VoucherRequest;
import com.gtech.gvcore.dao.model.VoucherRequestDetails;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.impl.VoucherAllocationServiceImpl;
import org.apache.commons.collections4.map.HashedMap;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2022年3月14日
 */
@RunWith(MockitoJUnitRunner.class)
public class VoucherAllocationServiceTests {

    @InjectMocks
    private VoucherAllocationServiceImpl voucherAllocationServiceImpl;

    @Mock
    private VoucherAllocationMapper voucherAllocationMapper;

    @Mock
    private GvCodeHelper gvCodeHelper;

    @Mock
    private VoucherRequestService voucherRequestService;

    @Mock
    private VoucherService voucherService;
    @Mock
    private VoucherMapper voucherMapper;

    @Mock
    private TransactionDataService transactionDataService;

    @Mock
    private VoucherRequestDetailsService voucherRequestDetailsService;

    @Mock
    private VoucherAllocationBatchService voucherAllocationBatchService;

    @Mock
    private VoucherReceiveService voucherReceiveService;
    
    @Mock
	private VoucherReceiveBatchService voucherReceiveBatchService;
    
    @Mock
    private BusinessLogService businessLogService;
    
    @Mock
    private ReleaseApproveService releaseApproveService;
    
    @Mock
    private OutletService outletService;
    
    @Mock
    private GvOperateLogService gvOperateLogService;

    @Mock
    private FlowNoticeService flowNoticeService;

	@Mock
	private GvUserAccountService userAccountService;


    @Before
    public void before() {
        EntityHelper.initEntityNameMap(Voucher.class, new MapperHelper().getConfig());
    }

    @Test
    public void allocate() {

		String issuerCode = "issuerCode";
		Map<String, String> issuerWarehouseMap = new HashMap<String, String>();
		issuerWarehouseMap.put(issuerCode, "warehouse");

        Map<String, String> issuerBusinessWarehouseMap = new HashMap<>();
        issuerBusinessWarehouseMap.put(issuerCode, "HO");
		try {
			Field issuerWarehouseMapField = voucherAllocationServiceImpl.getClass()
					.getDeclaredField("issuerWarehouseMap");
			issuerWarehouseMapField.setAccessible(true);
			issuerWarehouseMapField.set(voucherAllocationServiceImpl, issuerWarehouseMap);

            Field issuerBusinessWarehouseMapField = voucherAllocationServiceImpl.getClass()
                    .getDeclaredField("issuerBusinessWarehouseMap");
            issuerBusinessWarehouseMapField.setAccessible(true);
            issuerBusinessWarehouseMapField.set(voucherAllocationServiceImpl, issuerBusinessWarehouseMap);
		} catch (NoSuchFieldException | SecurityException | IllegalArgumentException | IllegalAccessException e1) {
			e1.printStackTrace();
		}

        AllocateRequest request = new AllocateRequest();
        request.setVoucherAllocationCode("22031410580001");
        request.setUpdateUser("min.he");
        List<AllocateVoucherBatch> voucherBatchList = new ArrayList<AllocateVoucherBatch>();
        AllocateVoucherBatch batch = new AllocateVoucherBatch();
        batch.setVoucherStartNo("1002222220033001");
        batch.setVoucherEndNo("1002222220033100");
        batch.setDenomination(new BigDecimal("100"));
        voucherBatchList.add(batch);
        request.setVoucherBatchList(voucherBatchList);

        Result<Void> result = voucherAllocationServiceImpl.allocate(request);

        VoucherAllocation voucherAllocation = new VoucherAllocation();
        voucherAllocation.setVoucherAllocationCode("voucherAllocationCode");
        voucherAllocation.setSourceDataCode("voucherRequestCode");
        voucherAllocation.setIssuerCode("issuerCode");
        voucherAllocation.setVoucherOwnerCode(voucherAllocation.getIssuerCode());
        voucherAllocation.setBusinessType(VoucherAllocationBusinessTypeEnum.SALES.code());
        voucherAllocation.setReceiverCode("outletCode");
        voucherAllocation.setStatus(VoucherAllocationStatusEnum.PENDING_RECEIPT.code());
        voucherAllocation.setBusinessType(VoucherAllocationBusinessTypeEnum.SALES.code());
        voucherAllocation.setSourceDataCode("VoucherRequestCode365823648438");
        when(voucherAllocationMapper.selectOne(any(VoucherAllocation.class))).thenReturn(voucherAllocation);
        result = voucherAllocationServiceImpl.allocate(request);

        voucherAllocation.setStatus(VoucherAllocationStatusEnum.PENDING_ALLOCATION.code());
        when(voucherAllocationMapper.selectOne(any(VoucherAllocation.class))).thenReturn(voucherAllocation);
        result = voucherAllocationServiceImpl.allocate(request);

        VoucherRequest voucherRequest = new VoucherRequest();
        voucherRequest.setVoucherRequestCode(voucherAllocation.getSourceDataCode());
        voucherRequest.setStatus(VoucherRequestStatusEnum.PENDING_RECEIPT.getCode());
        when(voucherRequestService.queryByVoucherRequestCode(any(String.class))).thenReturn(voucherRequest);
        result = voucherAllocationServiceImpl.allocate(request);

        voucherRequest.setStatus(VoucherRequestStatusEnum.PENDING_ALLOCATION.getCode());
        when(voucherRequestService.queryByVoucherRequestCode(any(String.class))).thenReturn(voucherRequest);
        result = voucherAllocationServiceImpl.allocate(request);

        List<VoucherRequestDetails> requestDetails = new ArrayList<>();
        VoucherRequestDetails details = new VoucherRequestDetails();
        details.setVoucherRequestCode(voucherRequest.getVoucherRequestCode());
        details.setDenomination(batch.getDenomination());
        details.setVoucherNum(100);
        details.setVoucherAmount(batch.getDenomination().multiply(new BigDecimal("100")));
        requestDetails.add(details);
        when(voucherRequestDetailsService.queryByVoucherRequestCode(any(String.class))).thenReturn(requestDetails);
        result = voucherAllocationServiceImpl.allocate(request);

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, 1);
        List<Voucher> voucherList = new ArrayList<>();
        Voucher voucher1 = new Voucher();
        voucher1.setIssuerCode(voucherAllocation.getIssuerCode());
        voucher1.setVoucherOwnerCode(voucherAllocation.getVoucherOwnerCode());
        voucher1.setVoucherEffectiveDate(calendar.getTime());
        voucher1.setMopCode(GvcoreConstants.MOP_CODE_VCE);
        voucher1.setDenomination(details.getDenomination().add(BigDecimal.ONE));
        voucher1.setVoucherCode(batch.getVoucherStartNo());
        voucher1.setVoucherCodeNum(Long.parseLong(voucher1.getVoucherCode()));
        voucherList.add(voucher1);

        when(voucherService.queryByVoucherCodeList(Mockito.isNull(), any(List.class))).thenReturn(voucherList);
        result = voucherAllocationServiceImpl.allocate(request);

        Voucher voucher2 = new Voucher();
        voucher2.setIssuerCode(voucherAllocation.getIssuerCode());
        voucher2.setVoucherOwnerCode(voucherAllocation.getVoucherOwnerCode());
        voucher2.setVoucherEffectiveDate(calendar.getTime());
        voucher2.setMopCode(GvcoreConstants.MOP_CODE_VCE);
        voucher2.setDenomination(details.getDenomination().add(BigDecimal.ONE));
        voucher2.setVoucherCode(batch.getVoucherEndNo());
        voucher2.setVoucherCodeNum(Long.parseLong(voucher2.getVoucherCode()));
        voucherList.add(voucher2);

        when(voucherService.queryByVoucherCodeList(Mockito.isNull(), any(List.class))).thenReturn(voucherList);
        result = voucherAllocationServiceImpl.allocate(request);

        voucher1.setMopCode(GvcoreConstants.MOP_CODE_VCR);
        when(voucherService.queryByVoucherCodeList(Mockito.isNull(), any(List.class))).thenReturn(voucherList);
        result = voucherAllocationServiceImpl.allocate(request);

        voucher2.setMopCode(GvcoreConstants.MOP_CODE_VCR);
        when(voucherService.queryByVoucherCodeList(Mockito.isNull(), any(List.class))).thenReturn(voucherList);
        result = voucherAllocationServiceImpl.allocate(request);

        voucher1.setDenomination(details.getDenomination());
        when(voucherService.queryByVoucherCodeList(Mockito.isNull(), any(List.class))).thenReturn(voucherList);
        result = voucherAllocationServiceImpl.allocate(request);

        voucher2.setDenomination(details.getDenomination());
        when(voucherService.queryByVoucherCodeList(Mockito.isNull(), any(List.class))).thenReturn(voucherList);
        result = voucherAllocationServiceImpl.allocate(request);

        List<VoucherDto> countDtos = new ArrayList<>();
        VoucherDto countDto = new VoucherDto();
        countDto.setIssuerCode(issuerCode);
        countDto.setVoucherOwnerCode(voucherAllocation.getVoucherOwnerCode());
        countDto.setVoucherOwnerType(VoucherOwnerTypeEnum.OUTLET.code());
        countDto.setStatus(VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
        countDto.setVoucherStatus(GvcoreConstants.STATUS_ENABLE);
        countDto.setCirculationStatus(VoucherCirculationStatusEnum.VOUCHER_RECEIVED.getCode());
        countDto.setDenomination(details.getDenomination());
        countDto.setCount(100);
        countDtos.add(countDto);
        when(voucherService.countGroupByDenominationAndCirculationStatus(any(List.class))).thenReturn(countDtos);
        try {
            result = voucherAllocationServiceImpl.allocate(request);
        } catch (GTechBaseException e) {

        }

        List<CpgVoucherCodeNumDto> cpgNumDtos = new ArrayList<>();
        CpgVoucherCodeNumDto numDto1 = new CpgVoucherCodeNumDto();
        numDto1.setCpgCode("cpgCode");
        numDto1.setDenomination(details.getDenomination());
        numDto1.setVoucherCodeNumStart(voucher1.getVoucherCodeNum());
        numDto1.setVoucherCodeNumEnd(voucher2.getVoucherCodeNum());
        cpgNumDtos.add(numDto1);
        CpgVoucherCodeNumDto numDto2 = new CpgVoucherCodeNumDto();
        numDto2.setCpgCode("cpgCode");
        numDto2.setDenomination(details.getDenomination());
        numDto2.setVoucherCodeNumStart(voucher1.getVoucherCodeNum() - 10);
        numDto2.setVoucherCodeNumEnd(voucher2.getVoucherCodeNum() - 10);
        cpgNumDtos.add(numDto2);
        when(voucherService.groupByDenominationAndCpg(any(String.class), any(List.class))).thenReturn(cpgNumDtos);

        int i = details.getVoucherNum();
        //when(voucherService.allocateVoucher(any(String.class), any(String.class), any(List.class), any(), any())).thenReturn(i);

        //when(voucherAllocationMapper.updateStatus(any(VoucherAllocationDto.class))).thenReturn(1);


        Map<Long, Voucher> numCodeMap = new HashedMap<>();
        numCodeMap.putIfAbsent(numDto1.getVoucherCodeNumStart(), voucher1);
        numCodeMap.putIfAbsent(numDto1.getVoucherCodeNumEnd(), voucher2);
        numCodeMap.putIfAbsent(numDto2.getVoucherCodeNumStart() - 10, voucher1);
        numCodeMap.putIfAbsent(numDto2.getVoucherCodeNumEnd() - 10, voucher2);
        //when(voucherService.queryByVoucherCodeNumList(any(String.class), any(List.class))).thenReturn(numCodeMap);
        
        Result<Void> requesstResult = Result.failed("failed");
        //when(voucherRequestService.updateRequestStatus(any(String.class), any(Integer.class), any(String.class)))
                //.thenReturn(requesstResult);
        try {
            result = voucherAllocationServiceImpl.allocate(request);
        } catch (GTechBaseException e) {

        }

        requesstResult = Result.ok();
		//when(voucherReceiveService.getVoucherReceiveBySourceDataCode(any(String.class))).thenReturn(new VoucherReceive());
		VoucherReceiveBatchResponse rs = new VoucherReceiveBatchResponse();
		rs.setVoucherNum(1);
		rs.setDenomination(BigDecimal.TEN);
		//when(voucherReceiveBatchService.queryReceiveBatchList(any(QueryVoucherReceiveBatchRequest.class))).thenReturn(Arrays.asList(rs));
        //when(voucherRequestService.updateRequestStatus(any(String.class), any(Integer.class), any(String.class)))
                //.thenReturn(requesstResult);
        result = voucherAllocationServiceImpl.allocate(request);

//        VoucherRequestStatusEnum.PENDING_ALLOCATION.getCode() 
        assertTrue(result.isSuccess());
    }
    
    /*@Test
    public void allocateByCustomerOrder() {
    	
        String issuerCode = "issuerCode";
        Map<String, String> issuerWarehouseMap = new HashMap<String, String>();
        issuerWarehouseMap.put(issuerCode, "warehouse");


        Map<String, String> issuerBusinessWarehouseMap = new HashMap<>();
        issuerBusinessWarehouseMap.put(issuerCode, "HO");
        try {
            Field issuerWarehouseMapField = voucherAllocationServiceImpl.getClass()
                    .getDeclaredField("issuerWarehouseMap");
            issuerWarehouseMapField.setAccessible(true);
            issuerWarehouseMapField.set(voucherAllocationServiceImpl, issuerWarehouseMap);

            Field issuerBusinessWarehouseMapField = voucherAllocationServiceImpl.getClass()
                    .getDeclaredField("issuerBusinessWarehouseMap");
            issuerBusinessWarehouseMapField.setAccessible(true);
            issuerBusinessWarehouseMapField.set(voucherAllocationServiceImpl, issuerBusinessWarehouseMap);
        } catch (NoSuchFieldException | SecurityException | IllegalArgumentException | IllegalAccessException e1) {
            e1.printStackTrace();
        }
    	
    	IssuanceRequest request = new IssuanceRequest();
    	request.setCustomerOrderCode("customerOrderCode");
    	request.setUpdateUser("updateUser");
    	List<IssuanceVoucherBatch> voucherBatchList = new ArrayList<>();
    	IssuanceVoucherBatch batch = new IssuanceVoucherBatch();
        batch.setVoucherStartNo("1002222220033001");
        batch.setVoucherEndNo("1002222220033010");
        batch.setDenomination(new BigDecimal("100"));
        voucherBatchList.add(batch);
        request.setVoucherBatchList(voucherBatchList);
    	
    	CustomerOrder customerOrder = new CustomerOrder();
    	customerOrder.setCustomerOrderCode(request.getCustomerOrderCode());
    	customerOrder.setIssuerCode("IssuerCode");
    	
		List<CustomerOrderDetails> details = new ArrayList<>();
		CustomerOrderDetails detail = new CustomerOrderDetails();
		detail.setCpgCode("cpgCode");
		detail.setCustomerOrderCode(customerOrder.getCustomerCode());
		detail.setDenomination(batch.getDenomination());
		detail.setVoucherNum(10);
		details.add(detail);
    	Result<String> result =  voucherAllocationServiceImpl.allocateByCustomerOrder(request, customerOrder, details);
    	
    	VoucherAllocation voucherAllocation = new VoucherAllocation();
    	voucherAllocation.setSourceDataCode(customerOrder.getCustomerOrderCode());
    	voucherAllocation.setIssuerCode(customerOrder.getIssuerCode());
    	voucherAllocation.setBusinessType(VoucherAllocationBusinessTypeEnum.CUSTOMERORDER.code());
    	voucherAllocation.setVoucherOwnerCode(customerOrder.getIssuerCode());
    	voucherAllocation.setReceiverCode(customerOrder.getCustomerCode());
    	voucherAllocation.setStatus(VoucherAllocationStatusEnum.PENDING_ALLOCATION.code());
        when(voucherAllocationMapper.selectOne(any(VoucherAllocation.class))).thenReturn(voucherAllocation);
        result =  voucherAllocationServiceImpl.allocateByCustomerOrder(request, customerOrder, details);
        
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, 1);
        List<Voucher> voucherList = new ArrayList<>();
        Voucher voucher1 = new Voucher();
        voucher1.setIssuerCode(voucherAllocation.getIssuerCode());
        voucher1.setVoucherOwnerCode(voucherAllocation.getVoucherOwnerCode());
        voucher1.setVoucherEffectiveDate(calendar.getTime());
        voucher1.setMopCode(GvcoreConstants.MOP_CODE_VCR);
        voucher1.setCpgCode(detail.getCpgCode());
        voucher1.setDenomination(batch.getDenomination());
        voucher1.setVoucherCode(batch.getVoucherStartNo());
        voucher1.setVoucherCodeNum(Long.parseLong(voucher1.getVoucherCode()));
        voucherList.add(voucher1);
        Voucher voucher2 = new Voucher();
        voucher2.setIssuerCode(voucherAllocation.getIssuerCode());
        voucher2.setVoucherOwnerCode(voucherAllocation.getVoucherOwnerCode());
        voucher2.setVoucherEffectiveDate(calendar.getTime());
        voucher2.setMopCode(GvcoreConstants.MOP_CODE_VCR);
        voucher2.setCpgCode(detail.getCpgCode());
        voucher2.setDenomination(batch.getDenomination());
        voucher2.setVoucherCode(batch.getVoucherEndNo());
        voucher2.setVoucherCodeNum(Long.parseLong(voucher2.getVoucherCode()));
        voucherList.add(voucher2);
        when(voucherService.queryByVoucherCodeList(Mockito.isNull(), any(List.class))).thenReturn(voucherList);
        result =  voucherAllocationServiceImpl.allocateByCustomerOrder(request, customerOrder, details);
        
        List<VoucherDto> countDtos = new ArrayList<>();
        VoucherDto countDto = new VoucherDto();
        countDto.setIssuerCode(voucherAllocation.getIssuerCode());
        countDto.setCpgCode(detail.getCpgCode());
        countDto.setVoucherOwnerCode(voucherAllocation.getVoucherOwnerCode());
        countDto.setVoucherOwnerType(VoucherOwnerTypeEnum.OUTLET.code());
        countDto.setStatus(VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
        countDto.setVoucherStatus(GvcoreConstants.STATUS_ENABLE);
        countDto.setCirculationStatus(VoucherCirculationStatusEnum.VOUCHER_RECEIVED.getCode());
        countDto.setDenomination(batch.getDenomination());
        countDto.setCount(detail.getVoucherNum());
        countDtos.add(countDto);
        when(voucherService.countGroupByCustomerOrderAllocation(any(List.class))).thenReturn(countDtos);
        try {
            result = voucherAllocationServiceImpl.allocateByCustomerOrder(request, customerOrder, details);
        } catch (GTechBaseException e) {

        }
        
        List<CpgVoucherCodeNumDto> cpgNumDtos = new ArrayList<>();
        CpgVoucherCodeNumDto numDto1 = new CpgVoucherCodeNumDto();
        numDto1.setCpgCode(detail.getCpgCode());
        numDto1.setDenomination(batch.getDenomination());
        numDto1.setVoucherCodeNumStart(voucher1.getVoucherCodeNum());
        numDto1.setVoucherCodeNumEnd(voucher2.getVoucherCodeNum());
        cpgNumDtos.add(numDto1);
        CpgVoucherCodeNumDto numDto2 = new CpgVoucherCodeNumDto();
        numDto2.setCpgCode(detail.getCpgCode());
        numDto2.setDenomination(batch.getDenomination());
        numDto2.setVoucherCodeNumStart(voucher1.getVoucherCodeNum() - 10);
        numDto2.setVoucherCodeNumEnd(voucher2.getVoucherCodeNum() - 10);
        cpgNumDtos.add(numDto2);
        when(voucherService.groupByDenominationAndCpg(any(String.class), any(List.class))).thenReturn(cpgNumDtos);

        int i = detail.getVoucherNum();
        when(voucherService.allocateVoucher(any(String.class), any(String.class), any(List.class), any(), any())).thenReturn(i);

        when(voucherAllocationMapper.updateStatus(any(VoucherAllocationDto.class))).thenReturn(1);
        
        Map<Long, Voucher> numCodeMap = new HashedMap<>();
        numCodeMap.putIfAbsent(numDto1.getVoucherCodeNumStart(), voucher1);
        numCodeMap.putIfAbsent(numDto1.getVoucherCodeNumEnd(), voucher2);
        numCodeMap.putIfAbsent(numDto2.getVoucherCodeNumStart() - 10, voucher1);
        numCodeMap.putIfAbsent(numDto2.getVoucherCodeNumEnd() - 10, voucher2);
        when(voucherService.queryByVoucherCodeNumList(any(String.class), any(List.class))).thenReturn(numCodeMap);

        result = voucherAllocationServiceImpl.allocateByCustomerOrder(request, customerOrder, details);
        
//        batch.setVoucherEndNo("1002222220033010");
//
//        result = voucherAllocationServiceImpl.allocateByCustomerOrder(request, customerOrder, details);


//        VoucherRequestStatusEnum.PENDING_ALLOCATION.getCode() 
        assertTrue(result.isSuccess());
    	
    }*/
    
    @Test
    public void customerOrderRelease() {
    	
    	String issuerCode = "issuerCode";
    	Map<String, String> issuerWarehouseMap = new HashMap<String, String>();
		issuerWarehouseMap.put(issuerCode, "warehouse");
		try {
			Field issuerWarehouseMapField = voucherAllocationServiceImpl.getClass()
					.getDeclaredField("issuerWarehouseMap");
			issuerWarehouseMapField.setAccessible(true);
			issuerWarehouseMapField.set(voucherAllocationServiceImpl, issuerWarehouseMap);
		} catch (NoSuchFieldException | SecurityException | IllegalArgumentException | IllegalAccessException e1) {
			e1.printStackTrace();
		}
    	
    	CustomerOrder customerOrder = new CustomerOrder();
    	customerOrder.setCustomerOrderCode("CustomerOrderCode");
    	customerOrder.setCustomerCode("customerCode");
    	customerOrder.setIssuerCode(issuerCode);
    	
//		List<CustomerOrderDetails> details = new ArrayList<>();
//		CustomerOrderDetails detail = new CustomerOrderDetails();
//		detail.setCpgCode("cpgCode");
//		detail.setCustomerOrderCode(customerOrder.getCustomerCode());
//		detail.setDenomination(batch.getDenomination());
//		detail.setVoucherNum(10);
//		details.add(detail);
		
		String updateUser = "updateUser";
		try {
			voucherAllocationServiceImpl.customerOrderRelease(customerOrder, true, updateUser,"" );
        } catch (GTechBaseException e) {

        }
		
		
		VoucherAllocation voucherAllocation = new VoucherAllocation();
		voucherAllocation.setVoucherAllocationCode("voucherAllocationCode");
    	voucherAllocation.setSourceDataCode(customerOrder.getCustomerOrderCode());
    	voucherAllocation.setIssuerCode(customerOrder.getIssuerCode());
    	voucherAllocation.setBusinessType(VoucherAllocationBusinessTypeEnum.CUSTOMERORDER.code());
    	voucherAllocation.setVoucherOwnerCode(customerOrder.getIssuerCode());
    	voucherAllocation.setReceiverCode(customerOrder.getCustomerCode());
    	voucherAllocation.setStatus(VoucherAllocationStatusEnum.PENDING_ALLOCATION.code());
        when(voucherAllocationMapper.selectOne(any(VoucherAllocation.class))).thenReturn(voucherAllocation);
        try {
			voucherAllocationServiceImpl.customerOrderRelease(customerOrder, true, updateUser, "" );
        } catch (GTechBaseException e) {

        }
        
        
        voucherAllocation.setStatus(VoucherAllocationStatusEnum.PENDING_RECEIPT.code());
        when(voucherAllocationMapper.selectOne(any(VoucherAllocation.class))).thenReturn(voucherAllocation);
        
        List<VoucherAllocationBatch> batchLisst = new ArrayList<>();
        VoucherAllocationBatch batch1 = new VoucherAllocationBatch();
        batch1.setCpgCode("cpgCode1");
        batch1.setVoucherAllocationCode(voucherAllocation.getVoucherAllocationCode());
        batch1.setVoucherStartNo("11");
        batch1.setVoucherEndNo("12");
        batch1.setDenomination(new BigDecimal("10"));
        batch1.setVoucherNum(1);
        VoucherAllocationBatch batch2 = new VoucherAllocationBatch();
        batch2.setCpgCode("cpgCode2");
        batch2.setVoucherAllocationCode(voucherAllocation.getVoucherAllocationCode());
        batch2.setVoucherStartNo("15");
        batch2.setVoucherEndNo("16");
        batch2.setDenomination(new BigDecimal("20"));
        batch2.setVoucherNum(1);
        batchLisst.add(batch1);
        batchLisst.add(batch2);
        when(voucherAllocationBatchService.queryByVoucherAllocationCode(voucherAllocation.getVoucherAllocationCode())).thenReturn(batchLisst);
        
        List<Voucher> voucherList = new ArrayList<>();
        Voucher voucher1 = new Voucher();
        voucher1.setMopCode(GvcoreConstants.MOP_CODE_VCR);
        voucher1.setDenomination(batch1.getDenomination());
        voucher1.setVoucherCode(batch1.getVoucherStartNo());
        voucher1.setVoucherCodeNum(Long.parseLong(voucher1.getVoucherCode()));
        Voucher voucher2 = new Voucher();
        voucher2.setMopCode(GvcoreConstants.MOP_CODE_VCR);
        voucher2.setDenomination(batch1.getDenomination());
        voucher2.setVoucherCode(batch1.getVoucherEndNo());
        voucher2.setVoucherCodeNum(Long.parseLong(voucher2.getVoucherCode()));
        Voucher voucher3 = new Voucher();
        voucher3.setMopCode(GvcoreConstants.MOP_CODE_VCR);
        voucher3.setDenomination(batch2.getDenomination());
        voucher3.setVoucherCode(batch2.getVoucherStartNo());
        voucher3.setVoucherCodeNum(Long.parseLong(voucher3.getVoucherCode()));
        Voucher voucher4 = new Voucher();
        voucher4.setMopCode(GvcoreConstants.MOP_CODE_VCR);
        voucher4.setDenomination(batch2.getDenomination());
        voucher4.setVoucherCode(batch2.getVoucherEndNo());
        voucher4.setVoucherCodeNum(Long.parseLong(voucher4.getVoucherCode()));
        voucherList.add(voucher1);
        voucherList.add(voucher2);
        voucherList.add(voucher3);
        voucherList.add(voucher4);
        //when(voucherService.queryByVoucherCodeList(any(String.class), any(String.class), any(List.class)))
                //.thenReturn(voucherList);
        
        try {
        	voucherAllocationServiceImpl.customerOrderRelease(customerOrder, true, updateUser, "" );
        } catch (GTechBaseException e) {

        }
        
        int total = batchLisst.stream().mapToInt(VoucherAllocationBatch :: getVoucherNum).sum();
		/*when(voucherService.activatPhysicalVoucherByCustomerOrder(any(String.class), any(String.class), any(List.class),
				any(String.class), any(String.class))).thenReturn(total);*/
        try {
            voucherAllocationServiceImpl.customerOrderRelease(customerOrder, true, updateUser, "" );
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
        	voucherAllocationServiceImpl.customerOrderRelease(customerOrder, false, updateUser, "" );
        } catch (GTechBaseException e) {

        }
        
        when(voucherAllocationMapper.updateStatus(any(VoucherAllocationDto.class))).thenReturn(1);
        try {
        	voucherAllocationServiceImpl.customerOrderRelease(customerOrder, false, updateUser, "" );
        } catch (GTechBaseException e) {

        }
        
        
        when(voucherService.updateByCustomerOrderReleaseReject(any(String.class), any(String.class), any(List.class))).thenReturn(total);
        voucherAllocationServiceImpl.customerOrderRelease(customerOrder, false, updateUser, "" );
        
        assertTrue(true);

    }

    @Test
    public void queryByVoucherAllocationCode() {

        String voucherAllocationCode = "";
        voucherAllocationServiceImpl.queryByVoucherAllocationCode(voucherAllocationCode);

        voucherAllocationCode = "voucherAllocationCode";
        voucherAllocationServiceImpl.queryByVoucherAllocationCode(voucherAllocationCode);

        assertTrue(true);
    }

}
