
package com.gtech.gvcore.service;

import java.util.Arrays;
import java.util.Collections;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.gtech.gvcore.common.request.receive.QueryVoucherReceiveBatchRequest;
import com.gtech.gvcore.common.request.receive.VoucherReceiveBatchRequest;
import com.gtech.gvcore.dao.mapper.VoucherReceiveBatchMapper;
import com.gtech.gvcore.dao.model.VoucherReceiveBatch;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.impl.VoucherReceiveBatchServiceImpl;

import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

@RunWith(MockitoJUnitRunner.class)
public class VoucherReceiveBatchServiceTest {

	@InjectMocks
	VoucherReceiveBatchServiceImpl voucherReceiveBatchService;


	@Mock
	private VoucherReceiveBatchMapper voucherReceiveBatchMapper;

	@Mock
	private GvCodeHelper gvCodeHelper;

	@Mock
	private CpgService cpgService;

	@Before
	public void before() {
		EntityHelper.initEntityNameMap(VoucherReceiveBatch.class, new MapperHelper().getConfig());
	}

	@Test
	public void queryReceiveBatchListTest() {
		QueryVoucherReceiveBatchRequest request = new QueryVoucherReceiveBatchRequest();
		voucherReceiveBatchService.queryReceiveBatchList(request);
		VoucherReceiveBatch voucherReceiveBatch = new VoucherReceiveBatch();
		Mockito.when(voucherReceiveBatchMapper.query(Mockito.any())).thenReturn(Arrays.asList(voucherReceiveBatch));
		Mockito.when(cpgService.queryCpgMapByCpgCodeList(Mockito.any())).thenReturn(Collections.emptyMap());
		voucherReceiveBatchService.queryReceiveBatchList(request);
		Assert.assertTrue(true);
	}

	@Test
	public void saveReceiveBatchTest() {
		voucherReceiveBatchService.saveReceiveBatch(null);
		VoucherReceiveBatchRequest voucherReceiveBatchRequest = new VoucherReceiveBatchRequest();
		voucherReceiveBatchService.saveReceiveBatch(Arrays.asList(voucherReceiveBatchRequest));
		Assert.assertTrue(true);
	}

	@Test
	public void updateReceivedNumTest() {
		voucherReceiveBatchService.updateReceivedNum("", 3);
		Assert.assertTrue(true);
	}

}
