package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.printer.CreatePrinterRequest;
import com.gtech.gvcore.common.request.printer.QueryPrinterByPageRequest;
import com.gtech.gvcore.common.response.productcategory.printer.QueryPrinterByPageResponse;
import com.gtech.gvcore.dao.mapper.PrinterMapper;
import com.gtech.gvcore.dao.model.Printer;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.impl.PrinterServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

/**
 * <AUTHOR> Gao.Yuhua
 * @date : 2022-02-23 10:52
 **/
@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class PrinterServiceTest {

    @InjectMocks
    private PrinterServiceImpl printerService;

    @Mock
    private PrinterMapper printerMapper;

    @Mock
    private GvCodeHelper gvCodeHelper;


    @Before
    public void before() {
        EntityHelper.initEntityNameMap(Printer.class, new MapperHelper().getConfig());
    }

    @Test
    public void createPrinter() {

        CreatePrinterRequest createPrinterRequest = getCreatePrinterRequest();
        Mockito.when(printerMapper.insertSelective(Mockito.any())).thenReturn(1);
        Result<Object> printer = printerService.createPrinter(createPrinterRequest);
        Assert.assertNotNull(printer);
        Assert.assertTrue(printer.isSuccess());
    }

    @Test
    public void queryPrinterDataByPage() {
        PageResult<QueryPrinterByPageResponse> result = printerService.queryPrinterDataByPage(new QueryPrinterByPageRequest());
        Assert.assertTrue(result.isSuccess());
    }

    private CreatePrinterRequest getCreatePrinterRequest() {

        CreatePrinterRequest createPrinterRequest = new CreatePrinterRequest();

        createPrinterRequest.setPrinterName("PrinterName".concat(System.currentTimeMillis() + ""));
        createPrinterRequest.setStateCode("001");
        createPrinterRequest.setCityCode("002");
        createPrinterRequest.setDistrictCode("003");
        createPrinterRequest.setAddress("Address");
        createPrinterRequest.setLongitude("Longitud");
        createPrinterRequest.setLatitude("latitude");
        createPrinterRequest.setFirstName("FirstName");
        createPrinterRequest.setLastName("LastName");
        createPrinterRequest.setMobile("+62-811109949");
        createPrinterRequest.setEmail("<EMAIL>");
        createPrinterRequest.setFtpAuthorizationType("PASSWORD");
        createPrinterRequest.setFtpUrl("ftp://************");
        createPrinterRequest.setFtpUsername("test");
        createPrinterRequest.setFtpPassword("test");
        createPrinterRequest.setCreateUser("gv");
        Mockito.when(printerMapper.insertSelective(Mockito.any())).thenReturn(1);
        return createPrinterRequest;
    }
}
