package com.gtech.gvcore.service;

import com.gtech.gvcore.common.request.customerpaymentmethod.*;
import com.gtech.gvcore.dao.mapper.CustomerPaymentMethodMapper;
import com.gtech.gvcore.dao.model.CustomerPaymentMethod;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.impl.CustomerPaymentMethodServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/16 18:19
 */
@RunWith(MockitoJUnitRunner.class)
public class CustomerPaymentMethodServiceTest {

    @InjectMocks
    private CustomerPaymentMethodServiceImpl customerPaymentMethodService;
    
    @Mock
    private CustomerPaymentMethodMapper customerPaymentMethodMapper;
    
    @Mock
    private GvCodeHelper codeHelper;
    
    
    @Before
    public void before() {
        EntityHelper.initEntityNameMap(CustomerPaymentMethod.class, new MapperHelper().getConfig());
    }



    @Test
    public void createCustomerPaymentMethod(){
        CreateCustomerPaymentMethodRequest request = new CreateCustomerPaymentMethodRequest();
        request.setCreateUser("user");

        request.setCreateUser("123");
        customerPaymentMethodService.createCustomerPaymentMethod(request);
    }

    @Test
    public void updateCustomerPaymentMethod(){
        UpdateCustomerPaymentMethodRequest request = new UpdateCustomerPaymentMethodRequest();
        request.setCustomerPaymentMethodCode("code");
        request.setStatus(0);
        request.setUpdateUser("123");

        customerPaymentMethodService.updateCustomerPaymentMethod(request);
    }


    @Test
    public void deleteCustomerPaymentMethod(){
        DeleteCustomerPaymentMethodRequest request = new DeleteCustomerPaymentMethodRequest();
        request.setCustomerPaymentMethodCode("123");

        customerPaymentMethodService.deleteCustomerPaymentMethod(request);
    }


    @Test
    public void queryCustomerPaymentMethodList(){

        QueryCustomerPaymentMethodRequest request = new QueryCustomerPaymentMethodRequest();
        ArrayList<CustomerPaymentMethod> objects = new ArrayList<>();
        List<CustomerPaymentMethod> gvCustomerPaymentMethodEntities = new ArrayList<>();
        CustomerPaymentMethod CustomerPaymentMethod = new CustomerPaymentMethod();
        CustomerPaymentMethod.setId(0L);
        CustomerPaymentMethod.setCustomerPaymentMethodCode("123");
        CustomerPaymentMethod.setStatus(0);
        CustomerPaymentMethod.setCreateUser("123");
        CustomerPaymentMethod.setCreateTime(new Date());
        CustomerPaymentMethod.setUpdateUser("123");
        CustomerPaymentMethod.setUpdateTime(new Date());
        gvCustomerPaymentMethodEntities.add(CustomerPaymentMethod);



        customerPaymentMethodService.queryCustomerPaymentMethodList(request);

    }



    @Test
    public void getCustomerPaymentMethod(){

        GetCustomerPaymentMethodRequest request = new GetCustomerPaymentMethodRequest();
        request.setCustomerPaymentMethodCode("123");

        customerPaymentMethodService.getCustomerPaymentMethod(request);

    }
    
    
}
