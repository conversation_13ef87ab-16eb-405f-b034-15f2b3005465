package com.gtech.gvcore.service;

import com.google.common.collect.Lists;
import com.gtech.basic.masterdata.web.dao.MasterDataDistrictMapper;
import com.gtech.gvcore.common.request.pos.CreatePosRequest;
import com.gtech.gvcore.common.request.pos.GetPosRequest;
import com.gtech.gvcore.common.request.pos.QueryOutletByPosIdRequest;
import com.gtech.gvcore.common.request.pos.QueryPosListRequest;
import com.gtech.gvcore.common.request.pos.UpdatePosRequest;
import com.gtech.gvcore.common.request.pos.UpdatePosStatusRequest;
import com.gtech.gvcore.common.response.pos.PosCpgResponse;
import com.gtech.gvcore.dao.mapper.PosMapper;
import com.gtech.gvcore.dao.model.Pos;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.impl.PosServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.Date;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class PosServiceTest {


    @InjectMocks
    private PosServiceImpl posService;
    @Mock
    private PosCpgService posCpgService;

    @Mock
    private PosMapper posMapper;

    @Mock
    private OutletService outletService;

    @Mock
    private MasterDataDistrictMapper masterDataDistrictMapper;
    @Mock
    private GvCodeHelper gvCodeHelper;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(Pos.class, new MapperHelper().getConfig());

    }

    @Test
    public void createPos(){
        CreatePosRequest request = new CreatePosRequest();
        request.setIssuerCode("");
        request.setPosName("");
        request.setMachineId("");
        request.setPosEntryModeId("");
        request.setOutletCode("");
        request.setCpgCodes(Lists.newArrayList());
        request.setCreateUser("");
        request.setCreateTime(new Date());

        posService.createPos(request);
    }

    @Test
    public void updatePos(){

        UpdatePosRequest request  = new UpdatePosRequest();
        request.setPosCode("");
        request.setIssuerCode("");
        request.setPosName("");
        request.setMachineId("");
        request.setPosEntryModeId("");
        request.setOutletCode("");
        request.setCpgCodes(Lists.newArrayList("123"));
        request.setUpdateUser("");
        request.setUpdateTime(new Date());


        posService.updatePos(request);
    }

    @Test
    public void queryPosList(){

        QueryPosListRequest request = new QueryPosListRequest();
        request.setPosCode("1");
        request.setPosName("1");
        request.setStatus("1");
        request.setPageNum(0);
        request.setPageSize(0);


        posService.queryPosList(request);
    }

    @Test
    public void updatePosStatus(){
        UpdatePosStatusRequest request = new UpdatePosStatusRequest();
        request.setPosCode("1");
        request.setStatus("1");


        posService.updatePosStatus(request);
    }

    @Test
    public void getPos(){
        GetPosRequest request = new GetPosRequest();
        request.setPosCode("123");
        request.setMachineId("12");

        PosCpgResponse build = PosCpgResponse.builder().cpgCode("123").cpgName("123").build();

        Pos pos = new Pos();
        pos.setId(0L);
        pos.setPosCode("12");
        //pos.setIssuerCode("12");
        pos.setPosName("12");
        pos.setMachineId("12");
        pos.setPosEntryModeId("12");
        pos.setOutletCode("12");
        pos.setStatus(0);
        pos.setCreateUser("12");
        pos.setCreateTime(new Date());
        pos.setUpdateUser("21");
        pos.setUpdateTime(new Date());


        List<PosCpgResponse> response = Lists.newArrayList(build);
        Mockito.when( posMapper.selectOne(Mockito.any())).thenReturn(pos);

        Mockito.when(posCpgService.queryPosCpgListByPos(Mockito.any())).thenReturn(response);
        posService.getPos(request);

    }

    @Test
    public void queryOutletByPosId(){
        QueryOutletByPosIdRequest request = new QueryOutletByPosIdRequest();
        request.setMachineId("123");



        posService.queryOutletByPosId(request);
    }

}




