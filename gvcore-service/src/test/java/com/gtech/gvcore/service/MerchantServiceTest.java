package com.gtech.gvcore.service;

import com.google.common.collect.Lists;
import com.gtech.gvcore.common.request.merchant.CreateMerchantRequest;
import com.gtech.gvcore.common.request.merchant.DeleteMerchantRequest;
import com.gtech.gvcore.common.request.merchant.GetMerchantRequest;
import com.gtech.gvcore.common.request.merchant.QueryMerchantByCompanyCodesRequest;
import com.gtech.gvcore.common.request.merchant.QueryMerchantRequest;
import com.gtech.gvcore.common.request.merchant.UpdateMerchantRequest;
import com.gtech.gvcore.common.request.merchant.UpdateMerchantStatusRequest;
import com.gtech.gvcore.dao.mapper.MerchantMapper;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.impl.MerchantServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/16 18:19
 */
@RunWith(MockitoJUnitRunner.class)
public class MerchantServiceTest {

    @InjectMocks
    private MerchantServiceImpl merchantService;

    @Mock
    private CompanyService companyService;

    @Mock
    private MerchantMapper merchantMapper;
    
    @Mock
    private GvCodeHelper codeHelper;
    
    
    @Before
    public void before() {
        EntityHelper.initEntityNameMap(Merchant.class, new MapperHelper().getConfig());
    }



    @Test
    public void createMerchant(){
        CreateMerchantRequest request = new CreateMerchantRequest();
        request.setMerchantName("name");
        request.setCreateUser("user");

        request.setCreateUser("123");
        merchantService.createMerchant(request);
    }

    @Test
    public void updateMerchant(){
        UpdateMerchantRequest request = new UpdateMerchantRequest();
        request.setMerchantCode("code");
        request.setMerchantName("123");
        request.setStatus(0);
        request.setUpdateUser("123");

        merchantService.updateMerchant(request);
    }



    @Test
    public void updateMerchantStatus(){
        UpdateMerchantStatusRequest request = new UpdateMerchantStatusRequest();
        request.setMerchantCode("code");
        request.setStatus(0);
        request.setUpdateUser("123");

        merchantService.updateMerchantStatus(request);
    }

    @Test
    public void deleteMerchant(){
        DeleteMerchantRequest request = new DeleteMerchantRequest();
        request.setMerchantCode("123");

        merchantService.deleteMerchant(request);
    }


    @Test
    public void queryMerchantList(){

        QueryMerchantRequest request = new QueryMerchantRequest();
        ArrayList<Merchant> objects = new ArrayList<>();
        List<Merchant> gvMerchantEntities = new ArrayList<>();
        Merchant Merchant = new Merchant();
        Merchant.setId(0L);
        Merchant.setMerchantCode("123");
        Merchant.setMerchantName("123");
        Merchant.setStatus(0);
        Merchant.setCreateUser("123");
        Merchant.setCreateTime(new Date());
        Merchant.setUpdateUser("123");
        Merchant.setUpdateTime(new Date());
        gvMerchantEntities.add(Merchant);



        merchantService.queryMerchantList(request);

    }



    @Test
    public void getMerchant(){

        GetMerchantRequest request = new GetMerchantRequest();
        request.setMerchantCode("123");

        merchantService.getMerchant(request);

    }

    @Test
    public void queryMerchantByCompanyCodes(){
        QueryMerchantByCompanyCodesRequest queryCompanyByIssuerCodesRequest = new QueryMerchantByCompanyCodesRequest();
        queryCompanyByIssuerCodesRequest.setCompanyCodes(Lists.newArrayList("123123"));
        merchantService.queryMerchantByCompanyCodes(queryCompanyByIssuerCodesRequest);
    }
    
	@Test
	public void queryMerchantByCodes() {
		merchantService.queryMerchantListByCodeList(Arrays.asList("123"));
	}
    
}
