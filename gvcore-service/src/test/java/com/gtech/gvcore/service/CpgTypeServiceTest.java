package com.gtech.gvcore.service;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.cpg.CreateCpgTypeRequest;
import com.gtech.gvcore.common.request.cpg.QueryCpgTypeByPageRequest;
import com.gtech.gvcore.common.response.cpg.QueryCpgTypeByPageResponse;
import com.gtech.gvcore.dao.mapper.CpgTypeMapper;
import com.gtech.gvcore.dao.model.CpgType;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.impl.CpgTypeServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

/**
 * <AUTHOR> Gao.Yuhua
 * @date : 2022-02-23 10:52
 **/
@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class CpgTypeServiceTest {

    @InjectMocks
    private CpgTypeServiceImpl cpgTypeService;

    @Mock
    private CpgTypeMapper cpgTypeMapper;

    @Mock
    private GvCodeHelper gvCodeHelper;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(CpgType.class, new MapperHelper().getConfig());
    }

    @Test
    public void createCpgType() {

        CreateCpgTypeRequest createCpgTypeRequest = new CreateCpgTypeRequest();

        createCpgTypeRequest.setCpgTypeName("cpg type1");
        createCpgTypeRequest.setPrefix("213");
        createCpgTypeRequest.setCreateUser("system");

        Result<String> cpgType = cpgTypeService.createCpgType(createCpgTypeRequest);
        log.info("cpgType:{}", JSON.toJSONString(cpgType));

        Assert.assertNotNull(cpgType);
        Assert.assertTrue(cpgType.isSuccess());
    }

    @Test
    public void queryCpgTypeDataByPage(){
        QueryCpgTypeByPageRequest queryCpgTypeByPageRequest = new QueryCpgTypeByPageRequest();
        queryCpgTypeByPageRequest.setCpgTypeName("name1");
        PageResult<QueryCpgTypeByPageResponse> pageResult = cpgTypeService.queryCpgTypeDataByPage(queryCpgTypeByPageRequest);

        Assert.assertNotNull(pageResult);
        Assert.assertTrue(pageResult.isSuccess());
    }
}