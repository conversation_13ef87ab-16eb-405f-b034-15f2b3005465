
package com.gtech.gvcore.service;

import java.io.IOException;
import java.util.Arrays;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import com.gtech.basic.idm.dao.entity.ResourceEntity;
import com.gtech.basic.idm.dao.entity.RoleResourceMappingEntity;
import com.gtech.basic.idm.dao.mapper.IResourceMapper;
import com.gtech.basic.idm.dao.mapper.IRoleResourceMappingMapper;
import com.gtech.gvcore.IgnoreCommonException;
import com.gtech.gvcore.common.enums.FlowEnum;
import com.gtech.gvcore.common.enums.FlowNodeEnum;
import com.gtech.gvcore.common.enums.FlowNoticeTypeEnum;
import com.gtech.gvcore.common.request.flow.FlowNoticeRequest;
import com.gtech.gvcore.common.request.flow.GetFlowNoticeRequest;
import com.gtech.gvcore.common.request.flow.SendNoticeRequest;
import com.gtech.gvcore.dao.mapper.FlowNoticeMapper;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.FlowNotice;
import com.gtech.gvcore.dao.model.IssueHandling;
import com.gtech.gvcore.dao.model.UserAccount;
import com.gtech.gvcore.dao.model.VoucherRequest;
import com.gtech.gvcore.service.impl.FlowNoticeServiceImpl;
import com.gtech.gvcore.service.impl.MessageComponent;

import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

@RunWith(MockitoJUnitRunner.class)
public class FlowNoticeServiceTest {

	@InjectMocks
	FlowNoticeServiceImpl flowNoticeService;
	@Mock
	GvUserAccountService userAccountService;

	@Mock
	FlowNoticeMapper flowNoticeMapper;

	@Mock
	IResourceMapper resourceMapper;
	@Mock
	IRoleResourceMappingMapper roleResourceMapper;
	
	@Mock
	CustomerOrderService customerOrderService;
	@Mock
	VoucherRequestService voucherRequestService;
	@Mock
	IssueHandlingService issueHandlingService;

	@Mock
	private MessageComponent messageComponent;
	@Before
	public void before() {
		EntityHelper.initEntityNameMap(FlowNotice.class, new MapperHelper().getConfig());
		EntityHelper.initEntityNameMap(ResourceEntity.class, new MapperHelper().getConfig());
		EntityHelper.initEntityNameMap(RoleResourceMappingEntity.class, new MapperHelper().getConfig());
	}

	@Test
	public void saveTest() {
		FlowNoticeRequest request = new FlowNoticeRequest();
		request.setFlowCode("123");
		request.setFlowNodeCode("123");
		request.setNoticeCodeList(Arrays.asList("123"));
		request.setNoticeCodeCCList(Arrays.asList("12"));
		flowNoticeService.saveFlowNotice(request);
	}
	
	@Test
	public void getTest() {
		GetFlowNoticeRequest request = new GetFlowNoticeRequest();
		flowNoticeService.getFlowNotice(request);
		FlowNotice flowNotice = new FlowNotice();
		flowNotice.setFlowNoticeType(FlowNoticeTypeEnum.CC_FLAG.getCode());
		FlowNotice flowNotice1 = new FlowNotice();
		flowNotice1.setFlowNoticeType(FlowNoticeTypeEnum.SEND_FLAG.getCode());
		Mockito.when(flowNoticeMapper.query(Mockito.any())).thenReturn(Arrays.asList(flowNotice, flowNotice1));
		flowNoticeService.getFlowNotice(request);
	}
	
	@Test
	public void sendTest() {
		ReflectionTestUtils.setField(flowNoticeService, "emailList", Arrays.asList("<EMAIL>", "<EMAIL>"));
		SendNoticeRequest request = new SendNoticeRequest();
		IgnoreCommonException.executeV2((x)->flowNoticeService.send(request));
		request.setFlowCode("123");
		IgnoreCommonException.executeV2((x)->flowNoticeService.send(request));
		request.setFlowCode(null);
		request.setFlowNodeCode("123");
		IgnoreCommonException.executeV2((x)->flowNoticeService.send(request));
		request.setFlowCode("123");
		flowNoticeService.send(request);
	}
	
	@Test
	public void sendNoticeTest() throws IOException {
		ReflectionTestUtils.setField(flowNoticeService, "emailList", Arrays.asList("<EMAIL>", "<EMAIL>"));
		SendNoticeRequest request = new SendNoticeRequest();
		request.setFlowCode("123");
		request.setFlowNodeCode("123");
		FlowNotice flowNotice= new FlowNotice();
		Mockito.when(flowNoticeMapper.select(Mockito.any())).thenReturn(Arrays.asList(flowNotice));
		flowNoticeService.sendToNotice(request);
		UserAccount userAccount = new UserAccount();
		Mockito.when(userAccountService.queryUserByRolesAndDataPermissions(Mockito.any(), Mockito.any())).thenReturn(Arrays.asList(userAccount));
		Mockito.when(voucherRequestService.queryByVoucherRequestCode(Mockito.any())).thenReturn(new VoucherRequest());
		request.setFlowCode(FlowEnum.SALES_VOUCHER_FLOW.getCode());
		request.setFlowNodeCode(FlowNodeEnum.CREATED.getCode());
		flowNoticeService.sendToNotice(request);
		request.setFlowNodeCode(FlowNodeEnum.APPROVE.getCode());
		flowNoticeService.sendToNotice(request);
		request.setFlowNodeCode(FlowNodeEnum.REJECTED.getCode());
		flowNoticeService.sendToNotice(request);
		request.setFlowNodeCode(FlowNodeEnum.ALLOCATION.getCode());
		flowNoticeService.sendToNotice(request);
		request.setFlowNodeCode(FlowNodeEnum.RECEIVE.getCode());
		flowNoticeService.sendToNotice(request);
		request.setFlowNodeCode(FlowNodeEnum.EXECUTE.getCode());
		flowNoticeService.sendToNotice(request);
		
		request.setFlowCode(FlowEnum.RETURN_VOUCHER_FLOW.getCode());
		request.setFlowNodeCode(FlowNodeEnum.CREATED.getCode());
		flowNoticeService.sendToNotice(request);
		request.setFlowNodeCode(FlowNodeEnum.APPROVE.getCode());
		flowNoticeService.sendToNotice(request);
		request.setFlowCode(FlowEnum.TRANSFER_ORDER_FLOW.getCode());
		request.setFlowNodeCode(FlowNodeEnum.REJECTED.getCode());
		flowNoticeService.sendToNotice(request);
		request.setFlowNodeCode(FlowNodeEnum.ALLOCATION.getCode());
		flowNoticeService.sendToNotice(request);
		request.setFlowNodeCode(FlowNodeEnum.RECEIVE.getCode());
		flowNoticeService.sendToNotice(request);
		request.setFlowNodeCode(FlowNodeEnum.EXECUTE.getCode());
		flowNoticeService.sendToNotice(request);
		
		Mockito.when(issueHandlingService.queryByIssueHandlingCode(Mockito.any())).thenReturn(new IssueHandling());
		request.setFlowCode(FlowEnum.ISSUE_HANDLING.getCode());
		request.setFlowNodeCode(FlowNodeEnum.SUBMIT.getCode());
		flowNoticeService.sendToNotice(request);
		request.setFlowNodeCode(FlowNodeEnum.APPROVE.getCode());
		flowNoticeService.sendToNotice(request);
		request.setFlowNodeCode(FlowNodeEnum.REJECTED.getCode());
		flowNoticeService.sendToNotice(request);
		request.setFlowNodeCode(FlowNodeEnum.ALLOCATION.getCode());
		flowNoticeService.sendToNotice(request);
		request.setFlowNodeCode(FlowNodeEnum.EXECUTE.getCode());
		flowNoticeService.sendToNotice(request);
		
		Mockito.when(customerOrderService.queryByCustomerOrderCode(Mockito.any())).thenReturn(new CustomerOrder());
		request.setFlowCode(FlowEnum.CUSTOMER_ORDER_FLOW.getCode());
		request.setFlowNodeCode(FlowNodeEnum.CREATED.getCode());
		flowNoticeService.sendToNotice(request);
		request.setFlowNodeCode(FlowNodeEnum.CUSTOMER_CREATED.getCode());
		flowNoticeService.sendToNotice(request);
		request.setFlowNodeCode(FlowNodeEnum.SUBMIT.getCode());
		flowNoticeService.sendToNotice(request);
		request.setFlowNodeCode(FlowNodeEnum.ISSUANCE.getCode());
		flowNoticeService.sendToNotice(request);
		request.setFlowNodeCode(FlowNodeEnum.RELEASE.getCode());
		flowNoticeService.sendToNotice(request);
		request.setFlowNodeCode(FlowNodeEnum.APPROVE.getCode());
		flowNoticeService.sendToNotice(request);
		request.setFlowNodeCode(FlowNodeEnum.REJECTED.getCode());
		flowNoticeService.sendToNotice(request);
		request.setFlowNodeCode(FlowNodeEnum.ALLOCATION.getCode());
		flowNoticeService.sendToNotice(request);
		request.setFlowNodeCode(FlowNodeEnum.RECEIVE.getCode());
		flowNoticeService.sendToNotice(request);
		request.setFlowNodeCode(FlowNodeEnum.DELIVER.getCode());
		flowNoticeService.sendToNotice(request);
		request.setFlowNodeCode(FlowNodeEnum.EXECUTE.getCode());
		flowNoticeService.sendToNotice(request);
	}
}
