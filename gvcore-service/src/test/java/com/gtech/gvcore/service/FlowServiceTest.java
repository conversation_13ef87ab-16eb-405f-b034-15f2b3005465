
package com.gtech.gvcore.service;

import java.util.Arrays;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;

import com.github.pagehelper.Page;
import com.gtech.basic.idm.service.dto.GetUserAccountParamDto;
import com.gtech.gvcore.IgnoreCommonException;
import com.gtech.gvcore.common.request.flow.CreateFlowRequest;
import com.gtech.gvcore.common.request.flow.FlowNodeRequest;
import com.gtech.gvcore.common.request.flow.QueryFlowRequest;
import com.gtech.gvcore.common.request.flow.UpdateFlowRequest;
import com.gtech.gvcore.common.request.useraccount.GvCreateUserAccountRequest;
import com.gtech.gvcore.common.request.useraccount.GvQueryUserAccountRequest;
import com.gtech.gvcore.common.request.useraccount.GvUpdateUserAccountRequest;
import com.gtech.gvcore.common.response.company.CompanyResponse;
import com.gtech.gvcore.common.response.merchant.MerchantResponse;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.dao.mapper.FlowMapper;
import com.gtech.gvcore.dao.mapper.FlowNodeMapper;
import com.gtech.gvcore.dao.mapper.UserAccountMapper;
import com.gtech.gvcore.dao.model.Flow;
import com.gtech.gvcore.dao.model.FlowNode;
import com.gtech.gvcore.dao.model.UserAccount;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.impl.FlowServiceImpl;
import com.gtech.gvcore.service.impl.GvUserAccountServiceImpl;

import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

@RunWith(MockitoJUnitRunner.class)
public class FlowServiceTest {

	@InjectMocks
	FlowServiceImpl flowService;
	@Mock
	FlowMapper flowMapper;

	@Mock
	FlowNodeMapper flowNodeMapper;
	@Mock
	GvCodeHelper codeHelper;

	@Before
	public void before() {
		EntityHelper.initEntityNameMap(Flow.class, new MapperHelper().getConfig());
		EntityHelper.initEntityNameMap(FlowNode.class, new MapperHelper().getConfig());
	}

	@Test
	public void createTest() {
		CreateFlowRequest request = new CreateFlowRequest();
		flowService.createFlow(request);
		FlowNodeRequest flowNode = new FlowNodeRequest();
		FlowNodeRequest flowNode1 = new FlowNodeRequest();
		flowNode1.setDeleteFlag(true);
		request.setFlowNodeList(Arrays.asList(flowNode, flowNode1));
		flowService.createFlow(request);
	}
	@Test
	public void createNodeTest() {
		FlowNodeRequest flowNode = new FlowNodeRequest();
		Mockito.when(flowNodeMapper.selectOne(Mockito.any())).thenReturn(new FlowNode());
		flowService.saveFlowNode(flowNode);
	}
	@Test
	public void updateTest() {
		UpdateFlowRequest request = new UpdateFlowRequest();
		IgnoreCommonException.execute((x)->flowService.updateFlow(request));
		Mockito.when(flowMapper.selectOne(Mockito.any())).thenReturn(new Flow());
		FlowNodeRequest flowNode = new FlowNodeRequest();
		FlowNodeRequest flowNode1 = new FlowNodeRequest();
		flowNode1.setDeleteFlag(true);
		request.setFlowNodeList(Arrays.asList(flowNode, flowNode1));
		flowService.updateFlow(request);
	}
	
	@Test
	public void queryTest() {
		QueryFlowRequest request = new QueryFlowRequest();
		Flow flow = new Flow();
		flow.setFlowCode("123");
		Mockito.when(flowMapper.query(Mockito.any())).thenReturn(Arrays.asList(flow));
		flowService.queryFlowList(request);
		FlowNode flowNode= new FlowNode();
		flowNode.setFlowCode("123");
		Mockito.when(flowNodeMapper.selectByCondition(Mockito.any())).thenReturn(Arrays.asList(flowNode));
		flowService.queryFlowList(request);
	}
}
