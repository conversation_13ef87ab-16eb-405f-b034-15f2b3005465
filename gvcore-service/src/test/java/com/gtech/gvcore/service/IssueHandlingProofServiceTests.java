package com.gtech.gvcore.service;

import java.util.ArrayList;
import java.util.List;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.gtech.gvcore.dao.mapper.IssueHandlingProofMapper;
import com.gtech.gvcore.dao.model.IssueHandlingProof;
import com.gtech.gvcore.service.impl.IssueHandlingProofServiceImpl;

/**
 * 
 * <AUTHOR>
 * @date 2022年5月26日
 */
@RunWith(MockitoJUnitRunner.class)
public class IssueHandlingProofServiceTests {

    @InjectMocks
    private IssueHandlingProofServiceImpl issueHandlingProofServiceImpl;

    @Mock
    private IssueHandlingProofMapper issueHandlingProofMapper;

    @Test
    public void insertList() {

        issueHandlingProofServiceImpl.insertList(new ArrayList<>());

        Assert.assertTrue(true);
    }

    /**
     * 
     * @param issueHandlingCode
     * @return
     * <AUTHOR>
     * @date 2022年4月6日
     */
    @Test
    public void queryByIssueHandlingCode() {

        issueHandlingProofServiceImpl.queryByIssueHandlingCode("");

        List<IssueHandlingProof> list = new ArrayList<>();
        list.add(new IssueHandlingProof());
        Mockito.when(issueHandlingProofMapper.select(ArgumentMatchers.any(IssueHandlingProof.class))).thenReturn(list);
        issueHandlingProofServiceImpl.queryByIssueHandlingCode("");

        Assert.assertTrue(true);
    }

    /**
     * 
     * @param issueHandlingCode
     * @return
     * <AUTHOR>
     * @date 2022年4月16日
     */
    @Test
    public void deleteByIssueHandlingCode() {

        issueHandlingProofServiceImpl.deleteByIssueHandlingCode("");

        issueHandlingProofServiceImpl.deleteByIssueHandlingCode("123132");

        Assert.assertTrue(true);
    }

}


