package com.gtech.gvcore.service;

import java.math.BigDecimal;
import java.util.HashMap;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.VoucherRequestStatusEnum;
import com.gtech.gvcore.common.request.releaseapprove.ApproveNodeRecordRequest;
import com.gtech.gvcore.common.request.releaseapprove.ReleaseApproveAbleRequest;
import com.gtech.gvcore.common.request.voucherrequest.CreateVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherreturnantransfer.QueryVoucherReturnAndTransferRequest;
import com.gtech.gvcore.common.request.voucherreturnantransfer.ReturnAndTransferRequest;
import com.gtech.gvcore.dao.mapper.VoucherRequestMapper;
import com.gtech.gvcore.dao.model.ApproveNodeRecord;
import com.gtech.gvcore.dao.model.VoucherRequest;
import com.gtech.gvcore.service.impl.MessageComponent;
import com.gtech.gvcore.service.impl.VoucherReturnAndTransferServiceImpl;

@RunWith(MockitoJUnitRunner.class)
public class VoucherReturnAndTransferServiceImplTest {

    @InjectMocks
    private VoucherReturnAndTransferServiceImpl voucherReturnAndTransferService;
    @Mock
    private VoucherRequestService voucherRequestService;
    @Mock
    private VoucherRequestMapper voucherRequestMapper;
    @Mock
    private ReleaseApproveService releaseApproveService;
    @Mock
    private VoucherAllocationService voucherAllocationService;
    @Mock
    private FlowNoticeService flowNoticeService;
    @Mock
    private MessageComponent messageComponent;
	@Mock
	GvUserAccountService userAccountService;

    @Test
    public void addVoucherRequest() {
        Mockito.when(voucherRequestService.addVoucherRequest(Mockito.any(),Mockito.any() )).thenReturn(new Result<>());
        Assert.assertNotNull(voucherReturnAndTransferService.addVoucherRequest(new CreateVoucherRequestRequest()));
    }

    @Test
    public void queryVoucherRequest() {
        Mockito.when(voucherRequestService.queryVoucherRequest(Mockito.any())).thenReturn(new PageResult<>());
        Assert.assertNotNull(
                voucherReturnAndTransferService.queryVoucherRequest(new QueryVoucherReturnAndTransferRequest()));

        QueryVoucherReturnAndTransferRequest request = new QueryVoucherReturnAndTransferRequest();
        request.setShowStatus(GvcoreConstants.RETURN + VoucherRequestStatusEnum.PENDING_ALLOCATION.getCode());

        Assert.assertNotNull(voucherReturnAndTransferService.queryVoucherRequest(request));

        request.setShowStatus(GvcoreConstants.TRANSFER + VoucherRequestStatusEnum.PENDING_ALLOCATION.getCode());
        Assert.assertNotNull(voucherReturnAndTransferService.queryVoucherRequest(request));

        request.setShowStatus(String.valueOf(VoucherRequestStatusEnum.PENDING_ALLOCATION.getCode()));
        Assert.assertNotNull(voucherReturnAndTransferService.queryVoucherRequest(request));
    }

    @Test
    public void approveVoucherReturnAndTransferAble() {
        VoucherRequest voucherRequest = new VoucherRequest();
        voucherRequest.setVoucherAmount(BigDecimal.TEN);
        Mockito.when(voucherRequestMapper.selectOne(Mockito.any())).thenReturn(voucherRequest);
        Mockito.when(releaseApproveService.approveAble(Mockito.any())).thenReturn(new Result<>(1));
        ReleaseApproveAbleRequest releaseApproveAbleRequest = new ReleaseApproveAbleRequest();
        releaseApproveAbleRequest.setBusinessCode("xxx");
        Assert.assertNotNull(voucherReturnAndTransferService.approveVoucherReturnAndTransferAble(releaseApproveAbleRequest));

        Mockito.when(releaseApproveService.approveAble(Mockito.any())).thenReturn(new Result<>(-1));
        Assert.assertNotNull(
                voucherReturnAndTransferService.approveVoucherReturnAndTransferAble(releaseApproveAbleRequest));

    }

    @Test
    public void approveVoucherReturnAndTransfer() {
        VoucherRequest voucherRequest = new VoucherRequest();
        ApproveNodeRecordRequest releaseApproveAbleRequest = new ApproveNodeRecordRequest();
        releaseApproveAbleRequest.setBusinessCode("xxx");
        voucherRequest.setVoucherAmount(BigDecimal.TEN);
        Mockito.when(voucherRequestMapper.selectOne(Mockito.any())).thenReturn(voucherRequest);
        Mockito.when(releaseApproveService.approve(Mockito.any())).thenReturn(new Result<>());
        Assert.assertNotNull(voucherReturnAndTransferService.approveVoucherReturnAndTransfer(releaseApproveAbleRequest));
    }

    @Test
    public void testApproveVoucherReturnAndTransfer() {
        VoucherRequest voucherRequest = new VoucherRequest();
        ApproveNodeRecordRequest releaseApproveAbleRequest = new ApproveNodeRecordRequest();
        releaseApproveAbleRequest.setBusinessCode("xxx");
        releaseApproveAbleRequest.setStatus(true);
        voucherRequest.setVoucherAmount(BigDecimal.TEN);
        voucherRequest.setBusinessType(GvcoreConstants.RETURN);
        Mockito.when(voucherRequestMapper.selectOne(Mockito.any())).thenReturn(voucherRequest);
        ApproveNodeRecord approveNodeRecord = new ApproveNodeRecord();
        Result<ApproveNodeRecord> ok = Result.ok(approveNodeRecord);
        Mockito.when(releaseApproveService.approve(Mockito.any())).thenReturn(ok);
		releaseApproveAbleRequest.setExtendParams(new HashMap<>());
        Assert.assertNotNull(voucherReturnAndTransferService.approveVoucherReturnAndTransfer(releaseApproveAbleRequest));

    }

    @Test
    public void returnAndTransfer() {
        VoucherRequest voucherRequest = new VoucherRequest();
        voucherRequest.setBusinessType("return");
        Mockito.when(voucherRequestMapper.selectOne(Mockito.any())).thenReturn(voucherRequest);
        ReturnAndTransferRequest returnAndTransferRequest = new ReturnAndTransferRequest();
        returnAndTransferRequest.setVoucherRequestCode("xxx");
        Assert.assertNotNull(voucherReturnAndTransferService.returnAndTransfer(returnAndTransferRequest));
    }
}