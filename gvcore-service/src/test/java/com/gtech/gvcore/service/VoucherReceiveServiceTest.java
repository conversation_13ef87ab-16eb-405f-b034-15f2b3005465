
package com.gtech.gvcore.service;

import com.gtech.gvcore.IgnoreCommonException;
import com.gtech.gvcore.common.enums.VoucherReceiveSourceTypeEnum;
import com.gtech.gvcore.common.request.receive.QueryVoucherReceiveRequest;
import com.gtech.gvcore.common.request.receive.ReceiveVoucherRequest;
import com.gtech.gvcore.common.request.receive.VoucherReceiveRecordRequest;
import com.gtech.gvcore.common.response.useraccount.PermissionCodeResponse;
import com.gtech.gvcore.common.response.voucherreceive.VoucherReceiveBatchResponse;
import com.gtech.gvcore.dao.mapper.VoucherReceiveMapper;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.dao.model.VoucherAllocation;
import com.gtech.gvcore.dao.model.VoucherReceive;
import com.gtech.gvcore.dao.model.VoucherRequest;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.helper.PermissionHelper;
import com.gtech.gvcore.service.impl.VoucherReceiveServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.Arrays;
import java.util.Collections;
import java.util.UUID;

@RunWith(MockitoJUnitRunner.class)
public class VoucherReceiveServiceTest {

	@InjectMocks
	VoucherReceiveServiceImpl voucherReceiveService;


	@Mock
	private VoucherReceiveMapper voucherReceiveMapper;

	@Mock
	private TransactionDataService transactionDataService;

	@Mock
	private VoucherReceiveBatchService voucherReceiveBatchService;

	@Mock
	private VoucherAllocationService voucherAllocationService;

	@Mock
	private VoucherReceiveRecordService voucherReceiveRecordService;

	@Mock
	private VoucherService voucherService;
	
	@Mock
	private FlowNoticeService flowNoticeService;
	@Mock
	private CustomerOrderService customerOrderService;
	@Mock
	private VoucherBatchService voucherBatchService;

	@Mock
	private BusinessLogService businessLogService;

	@Mock
	private GvCodeHelper gvCodeHelper;

	@Mock
	private VoucherRequestService voucherRequestService;
	
	@Mock
	private GvUserAccountService userAccountService;
	
	@Mock
	private OutletService outletService;

	@Mock
	private IssuerService issuerService;

	@Mock
	private PermissionHelper permissionHelper;

	@Before
	public void before() {
		EntityHelper.initEntityNameMap(VoucherReceive.class, new MapperHelper().getConfig());
	}

	@Test
	public void queryVoucherReceivePageTest() {
		final PermissionCodeResponse permissionCodeResponse = new PermissionCodeResponse();
		permissionCodeResponse.setOutletCodeList(Collections.singletonList(UUID.randomUUID().toString()));
		Mockito.when(this.permissionHelper.getPermission(Mockito.any(), Mockito.any())).thenReturn(permissionCodeResponse);

		QueryVoucherReceiveRequest request = new QueryVoucherReceiveRequest();
		voucherReceiveService.queryVoucherReceivePage(request);
		request.setVoucherEndNo("123");
		voucherReceiveService.queryVoucherReceivePage(request);
		request.setVoucherStartNo("123");
		VoucherReceiveBatchResponse voucherReceiveBatchResponse = new VoucherReceiveBatchResponse();
		voucherReceiveBatchResponse.setVoucherReceiveCode("123");
		Mockito.when(voucherReceiveBatchService.queryReceiveBatchList(Mockito.any())).thenReturn(Arrays.asList(voucherReceiveBatchResponse));
		voucherReceiveService.queryVoucherReceivePage(request);
		request.setBookletEndNo("123");
		VoucherReceive voucherReceive = new VoucherReceive();
		voucherReceive.setSourceType("sales");
		Mockito.when(voucherReceiveMapper.query(Mockito.any())).thenReturn(Arrays.asList(voucherReceive));
		voucherReceiveService.queryVoucherReceivePage(request);
		request.setBookletStartNo("123");
		voucherReceiveService.queryVoucherReceivePage(request);
		Assert.assertTrue(true);
	}

	/*@Test
	public void getVoucherReceiveTest() {
		GetVoucherReceiveRequest request = new GetVoucherReceiveRequest();
		request.setVoucherReceiveCode("aaa");
		voucherReceiveService.getVoucherReceive(request);
		VoucherReceive voucherReceive1 = new VoucherReceive();
		voucherReceive1.setSourceType(VoucherReceiveSourceTypeEnum.SALES.getCode());
		VoucherReceive voucherReceive2 = new VoucherReceive();
		voucherReceive2.setSourceType(VoucherReceiveSourceTypeEnum.GENERATE.getCode());

		GetVoucherAllocationResponse response = new GetVoucherAllocationResponse();
		response.setSourceDataCode("123");
		
		Mockito.when(voucherAllocationService.getVoucherAllocation(Mockito.any())).thenReturn(Result.ok());
		Mockito.when(voucherReceiveMapper.selectOne(Mockito.any())).thenReturn(voucherReceive1, voucherReceive2);
		Mockito.when(voucherAllocationService.getVoucherAllocation(Mockito.any())).thenReturn(Result.ok(response));
		voucherReceiveService.getVoucherReceive(request);
		voucherReceiveService.getVoucherReceive(request);
		Assert.assertTrue(true);
	}*/

	/*@Test
	public void createVoucherReceiveTest() {
		CreateVoucherReceiveRequest request = new CreateVoucherReceiveRequest();
		voucherReceiveService.createVoucherReceive(request);
		VoucherReceiveBatchRequest batchRequest = new VoucherReceiveBatchRequest();
		request.setReceiveBatchList(Arrays.asList(batchRequest));
		voucherReceiveService.createVoucherReceive(request);
		Assert.assertTrue(true);
	}*/

	@Test
	public void receiveErrorTest() {
		ReceiveVoucherRequest receiveVoucherRequest = new ReceiveVoucherRequest();
		IgnoreCommonException.executeV2((x) -> voucherReceiveService.receive(receiveVoucherRequest));
		VoucherReceive voucherReceive = new VoucherReceive();
		voucherReceive.setStatus(1);
		VoucherReceive voucherReceive1 = new VoucherReceive();
		voucherReceive1.setStatus(0);
		Mockito.when(voucherReceiveMapper.selectOne(Mockito.any())).thenReturn(voucherReceive, voucherReceive1);
		IgnoreCommonException.executeV2((x) -> voucherReceiveService.receive(receiveVoucherRequest));
		voucherReceiveService.receive(receiveVoucherRequest);
		VoucherReceiveBatchResponse voucherReceiveBatchResponse = new VoucherReceiveBatchResponse();
		Mockito.when(voucherReceiveBatchService.queryReceiveBatchList(Mockito.any())).thenReturn(Arrays.asList(voucherReceiveBatchResponse));
		voucherReceiveService.receive(receiveVoucherRequest);
		Assert.assertTrue(true);
	}

	@Test
	public void receiveTest() {
		ReceiveVoucherRequest receiveVoucherRequest = new ReceiveVoucherRequest();
		VoucherReceiveRecordRequest voucherReceiveRecordRequest = new VoucherReceiveRecordRequest();
		voucherReceiveRecordRequest.setVoucherStartNo("11");
		voucherReceiveRecordRequest.setVoucherEndNo("22");
		receiveVoucherRequest.setReceiveRecordList(Arrays.asList(voucherReceiveRecordRequest));
		VoucherReceive generateReceive = new VoucherReceive();
		generateReceive.setStatus(0);
		generateReceive.setVoucherNum(1);
		generateReceive.setReceivedNum(2);
		generateReceive.setSourceType(VoucherReceiveSourceTypeEnum.GENERATE.getCode());

		VoucherReceive allocationReceive = new VoucherReceive();
		allocationReceive.setStatus(0);
		allocationReceive.setVoucherNum(2);
		allocationReceive.setReceivedNum(2);
		allocationReceive.setSourceType(VoucherReceiveSourceTypeEnum.SALES.getCode());
		Mockito.when(voucherReceiveMapper.selectOne(Mockito.any())).thenReturn(generateReceive, allocationReceive);
		VoucherReceiveBatchResponse voucherReceiveBatchResponse = new VoucherReceiveBatchResponse();
		voucherReceiveBatchResponse.setVoucherStartNo("11");
		voucherReceiveBatchResponse.setVoucherEndNo("22");
		Mockito.when(voucherReceiveBatchService.queryReceiveBatchList(Mockito.any())).thenReturn(Arrays.asList(voucherReceiveBatchResponse));
		voucherReceiveService.receive(receiveVoucherRequest);

		Mockito.when(voucherService.updateVoucherStatus(Mockito.any())).thenReturn(12);
		Voucher voucher = new Voucher();
		voucher.setStatus(1);
		voucher.setCirculationStatus(1);
		VoucherAllocation voucherAllocation = new VoucherAllocation();
//		Mockito.when(voucherAllocationService.getAllocationByCode(Mockito.any())).thenReturn(voucherAllocation);
		
		VoucherRequest voucherRequest = new VoucherRequest();
//		Mockito.when(voucherRequestService.queryByVoucherRequestCode(Mockito.any())).thenReturn(voucherRequest);
		Outlet outlet = new Outlet();
		
//		Mockito.when(outletService.queryByOutletCode(Mockito.any())).thenReturn(outlet);
		//Mockito.when(voucherService.queryNotReceiveVoucher(Mockito.any(), Mockito.any(),"123")).thenReturn(Arrays.asList(voucher));
		voucherReceiveService.receive(receiveVoucherRequest);
		Assert.assertTrue(true);
	}
	

	@Test
	public void receiveCustomerTest() {
		VoucherReceive allocationReceive = new VoucherReceive();
		allocationReceive.setStatus(0);
		allocationReceive.setVoucherNum(2);
		allocationReceive.setReceivedNum(2);
		allocationReceive.setSourceType(VoucherReceiveSourceTypeEnum.CUSTOMER_ORDER.getCode());
		VoucherAllocation voucherAllocation = new VoucherAllocation();
		Mockito.when(voucherAllocationService.getAllocationBySourceDataCode(Mockito.any(),Mockito.any())).thenReturn(voucherAllocation);
		Mockito.when(voucherReceiveMapper.selectOne(Mockito.any())).thenReturn(allocationReceive);
		VoucherReceiveBatchResponse voucherReceiveBatchResponse = new VoucherReceiveBatchResponse();
		voucherReceiveBatchResponse.setVoucherStartNo("11");
		voucherReceiveBatchResponse.setVoucherEndNo("22");
		voucherReceiveBatchResponse.setVoucherReceiveCode("123");
		CustomerOrder customerOrder = new CustomerOrder();
		Mockito.when(voucherReceiveBatchService.queryReceiveBatchList(Mockito.any())).thenReturn(Arrays.asList(voucherReceiveBatchResponse));
		Mockito.when(voucherAllocationService.getAllocationByCode(Mockito.any())).thenReturn(voucherAllocation);
		Mockito.when(customerOrderService.queryByCustomerOrderCode(Mockito.any())).thenReturn(customerOrder);
		voucherReceiveService.customerOrderReceive("12", "11");
		Assert.assertTrue(true);
	}
}
