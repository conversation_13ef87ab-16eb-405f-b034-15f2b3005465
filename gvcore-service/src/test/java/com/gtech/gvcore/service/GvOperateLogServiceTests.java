package com.gtech.gvcore.service;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.gtech.gvcore.dao.mapper.GvOperateLogMapper;
import com.gtech.gvcore.dao.model.UserAccount;
import com.gtech.gvcore.service.impl.GvOperateLogServiceImpl;

/**
 * 
 * <AUTHOR>
 * @date 2022年4月14日
 */
@RunWith(MockitoJUnitRunner.class)
public class GvOperateLogServiceTests {
	
	@InjectMocks
	private GvOperateLogServiceImpl gvOperateLogServiceImpl;
	
	@Mock
	private GvOperateLogMapper gvOperateLogMapper;
	
    @Mock
    private GvUserAccountService gvUserAccountService;

	@Test
    public void createSuccessLog() {
		
        gvOperateLogServiceImpl.createSuccessLog("businessCode", "method", "operateUser", "");
		
        UserAccount userAccount = new UserAccount();
        when(gvUserAccountService.getUserNameInfo(any(String.class))).thenReturn(userAccount);
        gvOperateLogServiceImpl.createSuccessLog("businessCode", "method", "operateUser", "*********");

		assertTrue(true);
	}

}


