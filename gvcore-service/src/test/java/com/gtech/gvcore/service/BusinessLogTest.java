package com.gtech.gvcore.service;


import com.google.common.collect.Lists;
import com.gtech.gvcore.common.request.businesslog.CreateBusinessLogRequest;
import com.gtech.gvcore.common.request.businesslog.QueryBusinessLogRequest;
import com.gtech.gvcore.common.request.businesslogdetail.CreateBusinessLogDetailRequest;
import com.gtech.gvcore.common.response.businesslogdetail.BusinessLogDetailResponse;
import com.gtech.gvcore.dao.mapper.BusinessLogMapper;
import com.gtech.gvcore.dao.model.BusinessLog;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.impl.BusinessLogImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

@RunWith(MockitoJUnitRunner.class)
public class BusinessLogTest {

    @InjectMocks
    private BusinessLogImpl businessLog;


    @Mock
    private BusinessLogDetailService businessLogDetailService;

    @Mock
    private BusinessLogMapper businessLogMapper;


    @Mock
    private GvCodeHelper codeHelper;

    @Mock
    private GvUserAccountService userAccountService;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(BusinessLog.class, new MapperHelper().getConfig());
    }



    @Test
    public void createBusinessLog(){
        CreateBusinessLogRequest request = new CreateBusinessLogRequest();

        CreateBusinessLogDetailRequest detailRequest = new CreateBusinessLogDetailRequest();
        detailRequest.setDetailContentCode("1`23");
        detailRequest.setBusinessCode("123");
        request.setBusinessLogDetailList(Lists.newArrayList(detailRequest));
        request.setContentCode("123");
        businessLog.createBusinessLog(request);

    }


    @Test
    public void queryBusinessLog(){

        QueryBusinessLogRequest request = new QueryBusinessLogRequest();
        request.setBusinessLogCode("123");
        request.setContentCode("123");

        BusinessLogDetailResponse businessLogDetailResponse = new BusinessLogDetailResponse();
        businessLogDetailResponse.setBusinessCode("123");

        BusinessLog log = new BusinessLog();

        log.setBusinessCode("123");
        log.setCreateUser("123");


//        Mockito.when(businessLogMapper.selectByCondition(Mockito.any())).thenReturn(Lists.newArrayList(log));
//        Mockito.when(userAccountService.queryFullNameByCodeList(Mockito.any())).thenReturn(Collections.emptyMap());
//        Mockito.when(businessLogDetailService.queryBusinessLogDetail(Mockito.any())).thenReturn(Lists.newArrayList(businessLogDetailResponse));
//        businessLog.queryBusinessLog(request);

    }










}
