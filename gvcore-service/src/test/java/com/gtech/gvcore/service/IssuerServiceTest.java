package com.gtech.gvcore.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.gtech.gvcore.common.request.issuer.CreateIssuerRequest;
import com.gtech.gvcore.common.request.issuer.DeleteIssuerRequest;
import com.gtech.gvcore.common.request.issuer.GetIssuerRequest;
import com.gtech.gvcore.common.request.issuer.QueryIssuerRequest;
import com.gtech.gvcore.common.request.issuer.UpdateIssuerRequest;
import com.gtech.gvcore.common.request.issuer.UpdateIssuerStatusRequest;
import com.gtech.gvcore.dao.mapper.IssuerMapper;
import com.gtech.gvcore.dao.model.Issuer;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.impl.IssuerServiceImpl;

import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

/**
 * <AUTHOR>
 * @Date 2022/2/16 18:19
 */
@RunWith(MockitoJUnitRunner.class)
public class IssuerServiceTest {

    @InjectMocks
    private IssuerServiceImpl issuerService;
    
    @Mock
    private IssuerMapper issuerMapper;
    
    @Mock
    private GvCodeHelper codeHelper;
    
    
    @Before
    public void before() {
        EntityHelper.initEntityNameMap(Issuer.class, new MapperHelper().getConfig());
    }



    @Test
    public void createIssuer(){
        CreateIssuerRequest request = new CreateIssuerRequest();
        request.setIssuerName("name");
        request.setCreateUser("user");

        request.setCreateUser("123");
        issuerService.createIssuer(request);
    }

    @Test
    public void updateIssuer(){
        UpdateIssuerRequest request = new UpdateIssuerRequest();
        request.setIssuerCode("code");
        request.setIssuerName("123");
        request.setStatus(0);
        request.setUpdateUser("123");

        issuerService.updateIssuer(request);
    }


    @Test
    public void updateIssuerStatus(){
        UpdateIssuerStatusRequest request = new UpdateIssuerStatusRequest();
        request.setIssuerCode("code");
        request.setStatus(0);
        request.setUpdateUser("123");

        issuerService.updateIssuerStatus(request);
    }


    @Test
    public void deleteIssuer(){
        DeleteIssuerRequest request = new DeleteIssuerRequest();
        request.setIssuerCode("123");

        issuerService.deleteIssuer(request);
    }


    @Test
    public void queryIssuerList(){

        QueryIssuerRequest request = new QueryIssuerRequest();
        ArrayList<Issuer> objects = new ArrayList<>();
        List<Issuer> gvIssuerEntities = new ArrayList<>();
        Issuer Issuer = new Issuer();
        Issuer.setId(0L);
        Issuer.setIssuerCode("123");
        Issuer.setIssuerName("123");
        Issuer.setStatus(0);
        Issuer.setCreateUser("123");
        Issuer.setCreateTime(new Date());
        Issuer.setUpdateUser("123");
        Issuer.setUpdateTime(new Date());




        gvIssuerEntities.add(Issuer);



        issuerService.queryIssuerList(request);

    }



    @Test
    public void getIssuer(){

        GetIssuerRequest request = new GetIssuerRequest();
        request.setIssuerCode("123");

        issuerService.getIssuer(request);

    }
    
    @Test
    public void queryByIssuerCode() {

        String issuerCode = null;
        issuerService.queryByIssuerCode(issuerCode);

        issuerCode = "issuerCode";
        issuerService.queryByIssuerCode(issuerCode);

    }
    
}
