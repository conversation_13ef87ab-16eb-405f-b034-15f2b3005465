package com.gtech.gvcore.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.VoucherAllocationBusinessTypeEnum;
import com.gtech.gvcore.dao.mapper.ApproveNodeRecordMapper;
import com.gtech.gvcore.dao.mapper.CustomerOrderMapper;
import com.gtech.gvcore.dao.mapper.OutletMapper;
import com.gtech.gvcore.dao.mapper.VoucherAllocationMapper;
import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.dao.model.ApproveNodeRecord;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.dao.model.VoucherAllocation;
import com.gtech.gvcore.dao.model.VoucherAllocationBatch;
import com.gtech.gvcore.service.impl.issuehandle.IssueHandlerCancelSalesService;

import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

@RunWith(MockitoJUnitRunner.class)
public class IssueHandlerCancelSalesServiceTest {

    @InjectMocks
    private IssueHandlerCancelSalesService issueHandlerCancelSalesService;

    @Mock
    private VoucherAllocationBatchService voucherAllocationBatchService;

    @Mock
    private ApproveNodeRecordMapper approveNodeRecordMapper;

    @Mock
    protected VoucherService voucherService;

    @Mock
    private VoucherAllocationMapper voucherAllocationMapper;

    @Mock
    private CustomerOrderMapper customerOrderMapper;

    @Mock
    private VoucherBatchService voucherBatchService;

    @Mock
    protected VoucherMapper voucherMapper;

    @Mock
    private OutletMapper outletMapper;

	@Mock
	private OutletService outletService;

    @Mock
    private TransactionDataService transactionDataService;


    @Before
    public void before() {
        EntityHelper.initEntityNameMap(Voucher.class, new MapperHelper().getConfig());
    }

    @Test
    public void getIssueHandlingType() {
        Assert.assertNotNull(issueHandlerCancelSalesService.getIssueHandlingType());
    }

    @Test
    public void validate() {

        List<IssueHandlingDetails> details = new ArrayList<>();

		Assert.assertNotNull(issueHandlerCancelSalesService.validate(details, "1"));
    }

    @Test
    public void execute() {
        Date date = DateUtil.parseDate("2022-04-12 11:00:00", "yyyy-MM-dd HH:mm:ss");
        List<IssueHandlingDetails> details = new ArrayList<>();
        IssueHandlingDetails issueHandlingDetails = new IssueHandlingDetails();
        issueHandlingDetails.setVoucherCode("1");
        details.add(issueHandlingDetails);

        List<VoucherAllocationBatch> voucherAllocationBatches = new ArrayList<>();
        VoucherAllocationBatch voucherAllocationBatch = new VoucherAllocationBatch();
        voucherAllocationBatch.setVoucherAllocationCode("xxx");
        voucherAllocationBatches.add(voucherAllocationBatch);

        VoucherAllocation voucherAllocation = new VoucherAllocation();
        voucherAllocation.setSourceDataCode("xxx");
        voucherAllocation.setCreateTime(date);

        Outlet outlet = new Outlet();
        outlet.setOutletCode("xxx");
        outlet.setOutletType("xxx");

        Voucher voucher = new Voucher();
        voucher.setMopCode(GvcoreConstants.MOP_CODE_VCR);

        ApproveNodeRecord approveNodeRecord = new ApproveNodeRecord();
        approveNodeRecord.setCreateTime(date);
        approveNodeRecord.setBusinessCode("Xxx");

        VoucherAllocation voucherAllocation1 = new VoucherAllocation();
        voucherAllocation1.setBusinessType(VoucherAllocationBusinessTypeEnum.CUSTOMERORDER.code());
        voucherAllocation1.setSourceDataCode("xxx");


        List<String> voucherNumVCR = new ArrayList<>();
        voucherNumVCR.add("1");

        List<VoucherAllocationBatch> voucherAllocationBatches1 = new ArrayList<>();
        VoucherAllocationBatch voucherAllocationBatch1 = new VoucherAllocationBatch();
        voucherAllocationBatch1.setVoucherStartNo("1");
        voucherAllocationBatch1.setVoucherEndNo("1");
        voucherAllocationBatch1.setVoucherAllocationCode("xxx");
        voucherAllocationBatches1.add(voucherAllocationBatch1);
		Assert.assertNotNull(issueHandlerCancelSalesService.execute(details, "1"));

        voucher.setMopCode(GvcoreConstants.MOP_CODE_VCE);
		Assert.assertNotNull(issueHandlerCancelSalesService.execute(details, "1"));
    }
}