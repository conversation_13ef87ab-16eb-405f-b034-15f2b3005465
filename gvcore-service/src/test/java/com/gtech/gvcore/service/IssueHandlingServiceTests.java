package com.gtech.gvcore.service;

import com.alibaba.fastjson.JSON;
import com.gtech.basic.masterdata.web.service.MasterDataDdLangService;
import com.gtech.commons.redis.GTechRedisTemplate;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.enums.IssueHandlingStatusEnum;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.common.request.issuehandling.CreateIssueHandlingRequest;
import com.gtech.gvcore.common.request.issuehandling.EditIssueHandlingRequest;
import com.gtech.gvcore.common.request.issuehandling.GetIssueHandlingRequest;
import com.gtech.gvcore.common.request.issuehandling.IssueHandlingApproveRequest;
import com.gtech.gvcore.common.request.issuehandling.IssueHandlingCancelRequest;
import com.gtech.gvcore.common.request.issuehandling.IssueHandlingSubmitRequest;
import com.gtech.gvcore.common.request.issuehandling.ProofFile;
import com.gtech.gvcore.common.request.issuehandling.QueryIssueHandlingByPageRequest;
import com.gtech.gvcore.common.request.issuehandling.ValidateUploadedFileRequest;
import com.gtech.gvcore.common.response.issuehandling.GetIssueHandlingResponse;
import com.gtech.gvcore.common.response.issuehandling.QueryIssueHandlingByPageResponse;
import com.gtech.gvcore.common.response.issuehandling.ValidateUploadedFileResponse;
import com.gtech.gvcore.dao.dto.IssueHandlingDto;
import com.gtech.gvcore.dao.mapper.IssueHandlingMapper;
import com.gtech.gvcore.dao.model.IssueHandling;
import com.gtech.gvcore.dao.model.IssueHandlingProof;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.helper.OssHelper;
import com.gtech.gvcore.service.impl.IssueHandlingServiceImpl;
import com.gtech.gvcore.service.impl.issuehandle.IssueHandlerCancelSalesService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * 
 * <AUTHOR>
 * @date 2022年4月7日
 */
@RunWith(MockitoJUnitRunner.Silent.class)
public class IssueHandlingServiceTests {
	
	@InjectMocks
	private IssueHandlingServiceImpl issueHandlingServiceImpl;
    @Mock
    private OssHelper ossHelper;
	@Mock
	private IssueHandlingMapper issueHandlingMapper;
	
	@Mock
	private IssueHandlingProofService issueHandlingProofService;
	
	@Mock
	private IssueHandlingDetailsService issueHandlingDetailsService;
	
	@Mock
    private GvCodeHelper gvCodeHelper;
	
	@Mock
	private ApplicationContext applicationContext;
	
	@Mock
	private GTechRedisTemplate gTechRedisTemplate;
	
    @Mock
    private FlowNoticeService flowNoticeService;

    @Mock
    private MasterDataDdLangService masterDataDdLangService;

    @Mock
    private GvUserAccountService gvUserAccountService;
    
    @Mock
    private VoucherService voucherService;
	
	private static Map<String, String> headerFieldMap = null;
	
	static {
	    String json = "{'Voucher Number':'voucherCode','Original Invoice Number':'invoiceNo', 'Original Approval Code':'approvalCode', 'Original Voucher Number': 'voucherCode', 'New Voucher Number': 'newVoucherCode', 'Email': 'receiverEmail', 'Reissuing Reason': 'remarks', 'Change Expiry Date': 'voucherEffectiveDate'}";
	    headerFieldMap = JSON.parseObject(json, Map.class);
        EntityHelper.initEntityNameMap(IssueHandling.class,new MapperHelper().getConfig());
	}
	
	@Test
	public void createIssueHandling() {
		
        try {
            Field issuerWarehouseMapField = issueHandlingServiceImpl.getClass()
                    .getDeclaredField("headerFieldMap");
            issuerWarehouseMapField.setAccessible(true);
            issuerWarehouseMapField.set(issueHandlingServiceImpl, headerFieldMap);
            
            Field fieldDatePatternMapField = issueHandlingServiceImpl.getClass()
                    .getDeclaredField("fieldDatePatternMap");
            fieldDatePatternMapField.setAccessible(true);
            fieldDatePatternMapField.set(issueHandlingServiceImpl, new HashMap<>());
        } catch (NoSuchFieldException | SecurityException | IllegalArgumentException | IllegalAccessException e1) {
            e1.printStackTrace();
        }
		
		CreateIssueHandlingRequest request = new  CreateIssueHandlingRequest();
		request.setIssueType("");
		Result<String> result = issueHandlingServiceImpl.createIssueHandling(request);
		
		request.setIssueType(IssueHandlingTypeEnum.CANCEL_SALES.code());
		request.setUploadedFileUrl("https://gtech.oss-cn-shanghai.aliyuncs.com/dev/SYSTEM_DEFAULT/20220419/7a86eb2d-ac90-463c-91ed-0ed64d1c5122-工作簿2.csv");

        List<ProofFile> proofFileList = new ArrayList<>();
        ProofFile proofFile = new ProofFile();
        proofFile.setProofFileName("proofFileName");
        proofFile.setProofFileUrl("proofFileUrl");
        proofFileList.add(proofFile);
        request.setProofFileList(proofFileList);
		result = issueHandlingServiceImpl.createIssueHandling(request);
		
		when(gTechRedisTemplate.opsValueSetIfAbsent(any(String.class), any(String.class), any(String.class), any(Long.class))).thenReturn(true);
		result = issueHandlingServiceImpl.createIssueHandling(request);
		
//		List<ProofFile> proofFileList = new ArrayList<>();
//		ProofFile proofFile = new ProofFile();
//		proofFile.setProofFileName("proofFileName");
//		proofFile.setProofFileUrl("proofFileUrl");
//		proofFileList.add(proofFile);
//		request.setProofFileList(proofFileList);
//		result = issueHandlingServiceImpl.createIssueHandling(request);
		
//		assertTrue(result.isSuccess());
	}
	
	@Test
	public void validateUploadedFile() {
		
	    try {
            Field issuerWarehouseMapField = issueHandlingServiceImpl.getClass()
                    .getDeclaredField("headerFieldMap");
            issuerWarehouseMapField.setAccessible(true);
            issuerWarehouseMapField.set(issueHandlingServiceImpl, headerFieldMap);
            
            Field fieldDatePatternMapField = issueHandlingServiceImpl.getClass()
                    .getDeclaredField("fieldDatePatternMap");
            fieldDatePatternMapField.setAccessible(true);
            fieldDatePatternMapField.set(issueHandlingServiceImpl, new HashMap<>());
        } catch (NoSuchFieldException | SecurityException | IllegalArgumentException | IllegalAccessException e1) {
            e1.printStackTrace();
        }
		
		ValidateUploadedFileRequest request = new ValidateUploadedFileRequest();
		request.setIssueType("");
		
		Result<ValidateUploadedFileResponse> result = issueHandlingServiceImpl.validateUploadedFile(request);
		
		request.setUploadedFileUrl("https://www.baidu.com/img/flexible/logo/pc/result.png");
		Map<String, IssueHandlerBaseService> serviceMap = new HashMap<String, IssueHandlerBaseService>();
		serviceMap.put("IssueHandlerCancelSalesService", new IssueHandlerCancelSalesService());
//		when(applicationContext.getBeansOfType(IssueHandlerBaseService.class)).thenReturn(serviceMap);
		for (IssueHandlingTypeEnum typeEnum : IssueHandlingTypeEnum.values()) {
			request.setIssueType(typeEnum.code());
			result = issueHandlingServiceImpl.validateUploadedFile(request);
		}
		
		request.setIssueType(IssueHandlingTypeEnum.CANCEL_SALES.code());
		result = issueHandlingServiceImpl.validateUploadedFile(request);
		
//		assertTrue(result.isSuccess());
	}
	
    @Test
    public void submit() {

        IssueHandlingSubmitRequest request = new IssueHandlingSubmitRequest();
        request.setIssueHandlingCode("issueHandlingCode");
        Result<String> result = issueHandlingServiceImpl.submit(request);

        IssueHandling issueHandling = new IssueHandling();
        issueHandling.setStatus(IssueHandlingStatusEnum.SUBMIT.code());
        issueHandling.setIssueType("issueType");
        when(issueHandlingMapper.selectOne(any(IssueHandling.class))).thenReturn(issueHandling);
        result = issueHandlingServiceImpl.submit(request);

        issueHandling.setStatus(IssueHandlingStatusEnum.CREATED.code());
        issueHandling.setIssueType("issueType");
        when(issueHandlingMapper.selectOne(any(IssueHandling.class))).thenReturn(issueHandling);
        result = issueHandlingServiceImpl.submit(request);

        issueHandling.setStatus(IssueHandlingStatusEnum.CREATED.code());
        issueHandling.setIssueType(IssueHandlingTypeEnum.CANCEL_SALES.code());
        when(issueHandlingMapper.selectOne(any(IssueHandling.class))).thenReturn(issueHandling);

        when(issueHandlingMapper.updateStatus(any(IssueHandlingDto.class))).thenReturn(1);
        result = issueHandlingServiceImpl.submit(request);

        assertTrue(result.isSuccess());
    }

	@Test
	public void approve() {
		
		IssueHandlingApproveRequest request = new IssueHandlingApproveRequest();
		request.setIssueHandlingCode("issueHandlingCode");
		request.setStatus(true);
		Result<String> result = issueHandlingServiceImpl.approve(request);
		
		IssueHandling issueHandling = new IssueHandling();
		issueHandling.setStatus(IssueHandlingStatusEnum.APPROVE.code());
		issueHandling.setIssueType(IssueHandlingTypeEnum.CANCEL_SALES.code());
		when(issueHandlingMapper.selectOne(any(IssueHandling.class))).thenReturn(issueHandling);
		result = issueHandlingServiceImpl.approve(request);
		
        issueHandling.setStatus(IssueHandlingStatusEnum.SUBMIT.code());
		when(issueHandlingMapper.selectOne(any(IssueHandling.class))).thenReturn(issueHandling);
		result = issueHandlingServiceImpl.approve(request);
		
		when(issueHandlingMapper.updateStatus(any(IssueHandlingDto.class))).thenReturn(1);
		result = issueHandlingServiceImpl.approve(request);
		
		assertTrue(result.isSuccess());
	}
	
	/*@Test
	public void editIssueHandling() {
	    
	    try {
            Field issuerWarehouseMapField = issueHandlingServiceImpl.getClass()
                    .getDeclaredField("headerFieldMap");
            issuerWarehouseMapField.setAccessible(true);
            issuerWarehouseMapField.set(issueHandlingServiceImpl, headerFieldMap);
            
            Field fieldDatePatternMapField = issueHandlingServiceImpl.getClass()
                    .getDeclaredField("fieldDatePatternMap");
            fieldDatePatternMapField.setAccessible(true);
            fieldDatePatternMapField.set(issueHandlingServiceImpl, new HashMap<>());
        } catch (NoSuchFieldException | SecurityException | IllegalArgumentException | IllegalAccessException e1) {
            e1.printStackTrace();
        }
	    
	    EditIssueHandlingRequest request = new EditIssueHandlingRequest();
        request.setIssueHandlingCode("issueHandlingCode");
        request.setProofFileList(null);
        request.setUploadedFileName("uploadedFileName");
        request.setUploadedFileUrl("https://gtech.oss-cn-shanghai.aliyuncs.com/dev/SYSTEM_DEFAULT/20220419/7a86eb2d-ac90-463c-91ed-0ed64d1c5122-工作簿2.csv");
        Result<String> result = issueHandlingServiceImpl.editIssueHandling(request);
        
        IssueHandling issueHandling = new IssueHandling();
        issueHandling.setStatus(IssueHandlingStatusEnum.APPROVE.code());
        issueHandling.setIssueType("issueType");
        when(issueHandlingMapper.selectOne(any(IssueHandling.class))).thenReturn(issueHandling);
        result = issueHandlingServiceImpl.editIssueHandling(request);
        
        issueHandling.setStatus(IssueHandlingStatusEnum.CREATED.code());
        issueHandling.setUploadedFileUrl(request.getUploadedFileUrl() + "111");
        when(issueHandlingMapper.selectOne(any(IssueHandling.class))).thenReturn(issueHandling);

        List<ProofFile> proofFileList = new ArrayList<>();
        ProofFile proofFile1 = new ProofFile();
        proofFile1.setProofFileName("proofFileName1");
        proofFile1.setProofFileUrl("proofFileUrl1");
        ProofFile proofFile2 = new ProofFile();
        proofFile2.setProofFileName("proofFileName2");
        proofFile2.setProofFileUrl("proofFileUrl2");
        proofFileList.add(proofFile1);
        proofFileList.add(proofFile2);
        request.setProofFileList(proofFileList);
        result = issueHandlingServiceImpl.editIssueHandling(request);
        
        when(gTechRedisTemplate.opsValueSetIfAbsent(any(String.class), any(String.class), any(String.class), any(Long.class))).thenReturn(true);
        result = issueHandlingServiceImpl.editIssueHandling(request);
        
        issueHandling.setUploadedFileUrl(request.getUploadedFileUrl());
        when(issueHandlingMapper.selectOne(any(IssueHandling.class))).thenReturn(issueHandling);
        result = issueHandlingServiceImpl.editIssueHandling(request);
        
        when(issueHandlingMapper.updateByEdit(any(IssueHandlingDto.class))).thenReturn(1);
        when(gTechRedisTemplate.opsValueSetIfAbsent(any(String.class), any(String.class), any(String.class), any(Long.class))).thenReturn(true);

        result = issueHandlingServiceImpl.editIssueHandling(request);
        
        
        List<IssueHandlingProof> proofList = new ArrayList<>();
        IssueHandlingProof proof = new IssueHandlingProof();
        proof.setProofFileName(proofFile1.getProofFileName());
        proof.setProofFileUrl(proofFile1.getProofFileUrl());
        proofList.add(proof);
        when(issueHandlingProofService.queryByIssueHandlingCode(any())).thenReturn(proofList);
        result = issueHandlingServiceImpl.editIssueHandling(request);
        
        assertTrue(result.isSuccess());
	}*/
	
//	@Test
//	public void execute() {
//		
//		IssueHandlingExecuteRequest request = new IssueHandlingExecuteRequest();
//		request.setIssueHandlingCode("issueHandlingCode");
//		Result<String> result = issueHandlingServiceImpl.execute(request);
//		
//		IssueHandling issueHandling = new IssueHandling();
//		issueHandling.setStatus(IssueHandlingStatusEnum.EXECUTE.code());
//		issueHandling.setIssueType("issueType");
//		when(issueHandlingMapper.selectOne(any(IssueHandling.class))).thenReturn(issueHandling);
//		result = issueHandlingServiceImpl.execute(request);
//		
//		issueHandling.setStatus(IssueHandlingStatusEnum.APPROVE.code());
//		issueHandling.setIssueType("issueType");
//		when(issueHandlingMapper.selectOne(any(IssueHandling.class))).thenReturn(issueHandling);
//		result = issueHandlingServiceImpl.execute(request);
//		
//		issueHandling.setStatus(IssueHandlingStatusEnum.APPROVE.code());
//		issueHandling.setIssueType(IssueHandlingTypeEnum.CANCEL_SALES.code());
//		when(issueHandlingMapper.selectOne(any(IssueHandling.class))).thenReturn(issueHandling);
//		
//		Map<String, IssueHandlerBaseService> serviceMap = new HashMap<String, IssueHandlerBaseService>();
//		serviceMap.put("IssueHandlerCancelSalesService", new IssueHandlerCancelSalesService());
//		when(applicationContext.getBeansOfType(IssueHandlerBaseService.class)).thenReturn(serviceMap);
//		when(issueHandlingMapper.updateStatus(any(IssueHandlingDto.class))).thenReturn(1);
//		result = issueHandlingServiceImpl.execute(request);
//		
//		assertTrue(result.isSuccess());
//	}
	
	@Test
    public void cancel() {
        
	    IssueHandlingCancelRequest request = new IssueHandlingCancelRequest();
        request.setIssueHandlingCode("issueHandlingCode");
        request.setUpdateUser("updateUser");;
        Result<String> result = issueHandlingServiceImpl.cancel(request);
        
        IssueHandling issueHandling = new IssueHandling();
        issueHandling.setStatus(IssueHandlingStatusEnum.APPROVE.code());
        when(issueHandlingMapper.selectOne(any(IssueHandling.class))).thenReturn(issueHandling);
        result = issueHandlingServiceImpl.cancel(request);
        
        issueHandling.setStatus(IssueHandlingStatusEnum.CREATED.code());
        when(issueHandlingMapper.selectOne(any(IssueHandling.class))).thenReturn(issueHandling);
        result = issueHandlingServiceImpl.cancel(request);
        
        when(issueHandlingMapper.updateStatus(any(IssueHandlingDto.class))).thenReturn(1);
        result = issueHandlingServiceImpl.cancel(request);
        
        assertTrue(result.isSuccess());
    }
	
	@Test
	public void queryIssueHandlingByPage() {
		
		QueryIssueHandlingByPageRequest request = new QueryIssueHandlingByPageRequest();
		PageResult<QueryIssueHandlingByPageResponse> pageResult = issueHandlingServiceImpl.queryIssueHandlingByPage(request);
		
		
		List<IssueHandling> issueHandlingList = new ArrayList<>();
		IssueHandling issueHandling = new IssueHandling();
		issueHandlingList.add(issueHandling);
		when(issueHandlingMapper.selectSelective(any(IssueHandlingDto.class))).thenReturn(issueHandlingList);
		pageResult = issueHandlingServiceImpl.queryIssueHandlingByPage(request);
		
		assertTrue(pageResult.isSuccess());
	}
	
	@Test
	public void getIssueHandling() {
		
		GetIssueHandlingRequest request = new GetIssueHandlingRequest();
		Result<GetIssueHandlingResponse> result = issueHandlingServiceImpl.getIssueHandling(request);
		
		IssueHandling issueHandling = new IssueHandling();
        issueHandling.setStatus(IssueHandlingStatusEnum.APPROVE.code());
		when(issueHandlingMapper.selectOne(any(IssueHandling.class))).thenReturn(issueHandling);
		List<IssueHandlingProof> proofList = new ArrayList<>();
		IssueHandlingProof proof = new IssueHandlingProof();
		proofList.add(proof);
		
		when(issueHandlingProofService.queryByIssueHandlingCode(any())).thenReturn(proofList);
		result = issueHandlingServiceImpl.getIssueHandling(request);
		
		assertTrue(result.isSuccess());
	}

	@Test
	public void queryMapIssueHandingByCodes(){
        Map<String, IssueHandling> map = issueHandlingServiceImpl.queryMapIssueHandingByCodes(Collections.emptyList());
        List<String> list = Arrays.asList("1","2");
        Map<String, IssueHandling> map1 = issueHandlingServiceImpl.queryMapIssueHandingByCodes(list);
        List<IssueHandling> issueHandlingList = new ArrayList<>();
        IssueHandling issueHandling = new IssueHandling();
        issueHandling.setIssueHandlingCode("1");
        issueHandlingList.add(issueHandling);
        Mockito.when(issueHandlingMapper.selectByCondition(Mockito.any())).thenReturn(issueHandlingList);
        Map<String, IssueHandling> map2 = issueHandlingServiceImpl.queryMapIssueHandingByCodes(list);
        assertEquals(1,map2.size());
    }

}


