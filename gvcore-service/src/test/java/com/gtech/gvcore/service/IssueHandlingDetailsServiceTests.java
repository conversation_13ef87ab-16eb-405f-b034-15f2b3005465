package com.gtech.gvcore.service;

import com.gtech.gvcore.dao.dto.IssueHandlingDetailsDto;
import com.gtech.gvcore.dao.mapper.IssueHandlingDetailsMapper;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.service.impl.IssueHandlingDetailsServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @date 2022年5月26日
 */
@RunWith(MockitoJUnitRunner.class)
public class IssueHandlingDetailsServiceTests {

    @InjectMocks
    private IssueHandlingDetailsServiceImpl issueHandlingDetailsServiceImpl;

    @Mock
    private IssueHandlingDetailsMapper issueHandlingDetailsMapper;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(IssueHandlingDetails.class,new MapperHelper().getConfig());
    }

    @Test
    public void insertList() {
        
        issueHandlingDetailsServiceImpl.insertList(new ArrayList<>());

        Assert.assertTrue(true);
    }

    @Test
    public void queryByIssueHandlingCode() {

        String issueHandlingCode = "";
        Integer processStatus = 1;
        Integer pageSize = 2;
        Long startId = 1L;
        issueHandlingDetailsServiceImpl.queryByIssueHandlingCode(issueHandlingCode, processStatus, pageSize, startId);
        
        List<IssueHandlingDetails> list = new ArrayList<>();
        list.add(new IssueHandlingDetails());
        Mockito.when(issueHandlingDetailsMapper
                .queryByIssueHandlingCode(ArgumentMatchers.any(IssueHandlingDetailsDto.class))).thenReturn(list);
        issueHandlingDetailsServiceImpl.queryByIssueHandlingCode(issueHandlingCode, processStatus, pageSize, startId);

        Assert.assertTrue(true);
    }

    @Test
    public void updateByProcess() {

        issueHandlingDetailsServiceImpl.updateByProcess(new ArrayList<>(), "updateUser");

        Assert.assertTrue(true);
    }

    @Test
    public void deleteByIssueHandlingCode() {

        issueHandlingDetailsServiceImpl.deleteByIssueHandlingCode("");

        issueHandlingDetailsServiceImpl.deleteByIssueHandlingCode("123123");

        Assert.assertTrue(true);
    }

}


