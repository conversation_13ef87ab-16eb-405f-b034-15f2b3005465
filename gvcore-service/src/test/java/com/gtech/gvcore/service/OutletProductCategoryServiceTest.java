package com.gtech.gvcore.service;

import com.gtech.gvcore.common.request.outletproductcategory.*;
import com.gtech.gvcore.dao.mapper.OutletProductCategoryMapper;
import com.gtech.gvcore.dao.model.OutletProductCategory;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.impl.OutletProductCategoryServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/16 18:19
 */
@RunWith(MockitoJUnitRunner.class)
public class OutletProductCategoryServiceTest {

    @InjectMocks
    private OutletProductCategoryServiceImpl outletproductcategoryService;
    
    @Mock
    private OutletProductCategoryMapper outletproductcategoryMapper;
    
    @Mock
    private GvCodeHelper codeHelper;
    
    
    @Before
    public void before() {
        EntityHelper.initEntityNameMap(OutletProductCategory.class, new MapperHelper().getConfig());
    }



    @Test
    public void createOutletProductCategory(){
        CreateOutletProductCategoryRequest request = new CreateOutletProductCategoryRequest();
        request.setCreateUser("user");

        request.setCreateUser("123");
        outletproductcategoryService.createOutletProductCategory(request);
    }

    @Test
    public void updateOutletProductCategory(){
        UpdateOutletProductCategoryRequest request = new UpdateOutletProductCategoryRequest();
        request.setOutletProductCategoryCode("code");
        request.setStatus(0);
        request.setUpdateUser("123");

        outletproductcategoryService.updateOutletProductCategory(request);
    }


    @Test
    public void deleteOutletProductCategory(){
        DeleteOutletProductCategoryRequest request = new DeleteOutletProductCategoryRequest();
        request.setOutletProductCategoryCode("123");

        outletproductcategoryService.deleteOutletProductCategory(request);
    }


    @Test
    public void queryOutletProductCategoryList(){

        QueryOutletProductCategoryRequest request = new QueryOutletProductCategoryRequest();
        ArrayList<OutletProductCategory> objects = new ArrayList<>();
        List<OutletProductCategory> gvOutletProductCategoryEntities = new ArrayList<>();
        OutletProductCategory OutletProductCategory = new OutletProductCategory();
        OutletProductCategory.setId(0L);
        OutletProductCategory.setOutletProductCategoryCode("123");
        OutletProductCategory.setStatus(0);
        OutletProductCategory.setCreateUser("123");
        OutletProductCategory.setCreateTime(new Date());
        OutletProductCategory.setUpdateUser("123");
        OutletProductCategory.setUpdateTime(new Date());
        gvOutletProductCategoryEntities.add(OutletProductCategory);



        outletproductcategoryService.queryOutletProductCategoryList(request);

    }



    @Test
    public void getOutletProductCategory(){

        GetOutletProductCategoryRequest request = new GetOutletProductCategoryRequest();
        request.setOutletProductCategoryCode("123");

        outletproductcategoryService.getOutletProductCategory(request);

    }
    
    
}
