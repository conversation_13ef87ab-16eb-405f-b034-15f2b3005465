package com.gtech.gvcore.service;

import com.gtech.basic.masterdata.core.controller.QueryResult;
import com.gtech.basic.masterdata.web.entity.MasterDataDDEntity;
import com.gtech.basic.masterdata.web.service.MasterDataDdService;
import com.gtech.gvcore.common.request.vouchertype.*;
import com.gtech.gvcore.service.impl.VoucherTypeServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @Date 2022/2/15 10:29
 */
@RunWith(MockitoJUnitRunner.class)
public class VoucherTypeServiceTest {


    @InjectMocks
    private VoucherTypeServiceImpl voucherTypeService;

    @Mock
    private MasterDataDdService masterDataDdService;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(MasterDataDDEntity.class, new MapperHelper().getConfig());
    }


    @Test
    public void createVoucherType(){
        CreateVoucherTypeRequest request = new CreateVoucherTypeRequest();
        request.setState(0);
        request.setCreateUser("123");
        voucherTypeService.createVoucherType(request);
    }

    @Test
    public void updateVoucherType(){
        UpdateVoucherTypeRequest request = new UpdateVoucherTypeRequest();
        request.setState(0);
        voucherTypeService.updateVoucherType(request);
    }

    @Test
    public void updateVoucherTypeStatus(){
        UpdateVoucherTypeStatusRequest request = new UpdateVoucherTypeStatusRequest();
        request.setState(0);
        voucherTypeService.updateVoucherTypeStatus(request);
    }


    @Test
    public void deleteVoucherType(){
        DeleteVoucherTypeRequest request = new DeleteVoucherTypeRequest();
        voucherTypeService.deleteVoucherType(request);
    }


    @Test
    public void queryVoucherTypeList(){

        QueryVoucherTypeRequest request = new QueryVoucherTypeRequest();
        ArrayList<MasterDataDDEntity> objects = new ArrayList<>();
        QueryResult<MasterDataDDEntity> result = new QueryResult<MasterDataDDEntity>(objects,1L);

        Mockito.when(masterDataDdService.queryByPages(Mockito.anyMap())).thenReturn(result);
        voucherTypeService.queryVoucherTypeList(request);

    }



    @Test
    public void getVoucherType(){

        GetVoucherTypeRequest request = new GetVoucherTypeRequest();
        voucherTypeService.getVoucherType(request);

    }


}
