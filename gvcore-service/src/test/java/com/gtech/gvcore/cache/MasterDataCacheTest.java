package com.gtech.gvcore.cache;

import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.cpg.GetCpgRequest;
import com.gtech.gvcore.common.request.outlet.GetOutletRequest;
import com.gtech.gvcore.common.request.pos.GetPosRequest;
import com.gtech.gvcore.common.response.cpg.GetCpgResponse;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.common.response.outletcpg.OutletCpgResponse;
import com.gtech.gvcore.common.response.pos.PosCpgResponse;
import com.gtech.gvcore.common.response.pos.PosResponse;
import com.gtech.gvcore.dao.model.*;
import com.gtech.gvcore.helper.GvRedisTemplate;
import com.gtech.gvcore.service.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.TestPropertySource;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@SpringBootTest(classes = MasterDataCache.class)
@TestPropertySource(properties = {
    "local.cache.expire.time=1000"
})
class MasterDataCacheTest {

    @Autowired
    private MasterDataCache masterDataCache;

    @MockBean private PosService posService;
    @MockBean private CpgService cpgService;
    @MockBean private OutletService outletService;
    @MockBean private PosCpgService posCpgService;
    @MockBean private OutletCpgService outletCpgService;
    @MockBean private GvRedisTemplate gvRedisTemplate;

    @BeforeEach
    void setUp() {
        Mockito.reset(posService, cpgService, outletService, posCpgService, outletCpgService, gvRedisTemplate);
        
        // Mock for gvRedisTemplate.delete(String group, String key)
        doReturn(true).when(gvRedisTemplate).delete(anyString(), anyString());
    }

    private <T> Result<T> createSuccessResult(T data) {
        Result<T> result = new Result<>();
        result.setData(data);
        return result;
    }

    @Nested
    @DisplayName("POS Cache Tests")
    class PosCacheTests {
        private final String TEST_POS_ID = "pos123";
        private PosResponse testPosResponse;
        private String expectedRedisKey;
        private final String POS_CACHE_GROUP = "POS";

        @BeforeEach
        void initPosData() {
            testPosResponse = new PosResponse();
            testPosResponse.setMachineId(TEST_POS_ID);
            testPosResponse.setPosName("Test POS");
            expectedRedisKey = "GV:CACHE:POS:" + TEST_POS_ID;
        }

        @Test
        @DisplayName("getPos - Cache Miss, Service Called")
        void testGetPos_CacheMiss_ServiceCalled() {
            when(posService.getPos(any(GetPosRequest.class))).thenReturn(createSuccessResult(testPosResponse));
            Pos result1 = masterDataCache.getPos(TEST_POS_ID);
            assertNotNull(result1);
            assertEquals(TEST_POS_ID, result1.getMachineId());
            verify(posService, times(1)).getPos(argThat(req -> req.getMachineId().equals(TEST_POS_ID)));

            Pos result2 = masterDataCache.getPos(TEST_POS_ID);
            assertSame(result1, result2);
            verifyNoMoreInteractions(posService);
        }
        
        @Test
        @DisplayName("getPos - DataLoader service returns null")
        void testGetPos_DataLoaderServiceReturnsNull() {
            when(posService.getPos(any(GetPosRequest.class))).thenReturn(null); 
            Pos result = masterDataCache.getPos(TEST_POS_ID);
            assertNull(result);
            verify(posService, times(1)).getPos(any(GetPosRequest.class));
        }
        
        @Test
        @DisplayName("getPos - DataLoader returns Result with null data")
        void testGetPos_DataLoaderReturnsResultWithNullData() {
            when(posService.getPos(any(GetPosRequest.class))).thenReturn(createSuccessResult(null));
            Pos result = masterDataCache.getPos(TEST_POS_ID);
            assertNull(result);
            verify(posService, times(1)).getPos(any(GetPosRequest.class));
        }

        @Test
        @DisplayName("updatePosCache - Updates local cache")
        void testUpdatePosCache() {
            Pos updatedPos = new Pos();
            updatedPos.setMachineId(TEST_POS_ID);
            updatedPos.setPosName("Updated POS Name");
            masterDataCache.updatePosCache(updatedPos);

            Pos result = masterDataCache.getPos(TEST_POS_ID);
            assertNotNull(result);
            assertEquals("Updated POS Name", result.getPosName());
            assertSame(updatedPos, result);
            verifyNoInteractions(posService);
        }

        @Test
        @DisplayName("hasPos - Returns true when POS exists")
        void testHasPos_Exists() {
            when(posService.getPos(any(GetPosRequest.class))).thenReturn(createSuccessResult(testPosResponse));
            assertTrue(masterDataCache.hasPos(TEST_POS_ID));
        }

        @Test
        @DisplayName("hasPos - Returns false when POS does not exist")
        void testHasPos_NotExists() {
            when(posService.getPos(any(GetPosRequest.class))).thenReturn(createSuccessResult(null));
            assertFalse(masterDataCache.hasPos(TEST_POS_ID));
        }
        
        @Test
        @DisplayName("hasPos - Returns false for blank ID")
        void testHasPos_BlankId() {
            assertFalse(masterDataCache.hasPos(""));
            assertFalse(masterDataCache.hasPos(null));
            verifyNoInteractions(posService);
        }
    }

    @Nested
    @DisplayName("CPG Cache Tests")
    class CpgCacheTests {
        private final String TEST_CPG_CODE = "cpg123";
        private GetCpgResponse testCpgResponse;
        private String expectedRedisKey;
        private final String CPG_CACHE_GROUP = "CPG";

        @BeforeEach
        void initCpgData() {
            testCpgResponse = new GetCpgResponse();
            testCpgResponse.setCpgCode(TEST_CPG_CODE);
            testCpgResponse.setCpgName("Test CPG");
            expectedRedisKey = "GV:CACHE:CPG:" + TEST_CPG_CODE;
        }

        @Test
        @DisplayName("getCpg - Cache Miss, Service Called")
        void testGetCpg_CacheMiss_ServiceCalled() {
            when(cpgService.getCpg(any(GetCpgRequest.class))).thenReturn(createSuccessResult(testCpgResponse));
            Cpg result1 = masterDataCache.getCpg(TEST_CPG_CODE);
            assertNotNull(result1);
            assertEquals(TEST_CPG_CODE, result1.getCpgCode());
            verify(cpgService, times(1)).getCpg(argThat(req -> req.getCpgCode().equals(TEST_CPG_CODE)));

            Cpg result2 = masterDataCache.getCpg(TEST_CPG_CODE);
            assertSame(result1, result2);
            verifyNoMoreInteractions(cpgService);
        }
        
        @Test
        @DisplayName("updateCpgCache - Updates local cache")
        void testUpdateCpgCache() {
            Cpg updatedCpg = new Cpg();
            updatedCpg.setCpgCode(TEST_CPG_CODE);
            updatedCpg.setCpgName("Updated CPG Name");
            masterDataCache.updateCpgCache(updatedCpg);

            Cpg result = masterDataCache.getCpg(TEST_CPG_CODE);
            assertNotNull(result);
            assertEquals("Updated CPG Name", result.getCpgName());
            assertSame(updatedCpg, result);
            verifyNoInteractions(cpgService);
        }
    }

    @Nested
    @DisplayName("Outlet Cache Tests")
    class OutletCacheTests {
        private final String TEST_OUTLET_CODE = "out123";
        private OutletResponse testOutletResponse;
        private String expectedRedisKey;
        private final String OUTLET_CACHE_GROUP = "OUTLET";

        @BeforeEach
        void initOutletData() {
            testOutletResponse = new OutletResponse();
            testOutletResponse.setOutletCode(TEST_OUTLET_CODE);
            testOutletResponse.setOutletName("Test Outlet");
            expectedRedisKey = "GV:CACHE:OUTLET:" + TEST_OUTLET_CODE;
        }

        @Test
        @DisplayName("getOutlet - Cache Miss, Service Called")
        void testGetOutlet_CacheMiss_ServiceCalled() {
            when(outletService.getOutlet(any(GetOutletRequest.class))).thenReturn(testOutletResponse);
            Outlet result1 = masterDataCache.getOutlet(TEST_OUTLET_CODE);
            assertNotNull(result1);
            assertEquals(TEST_OUTLET_CODE, result1.getOutletCode());
            verify(outletService, times(1)).getOutlet(argThat(req -> req.getOutletCode().equals(TEST_OUTLET_CODE)));

            Outlet result2 = masterDataCache.getOutlet(TEST_OUTLET_CODE);
            assertSame(result1, result2);
            verifyNoMoreInteractions(outletService);
        }
        
        @Test
        @DisplayName("updateOutletCache - Updates local cache")
        void testUpdateOutletCache() {
            Outlet updatedOutlet = new Outlet();
            updatedOutlet.setOutletCode(TEST_OUTLET_CODE);
            updatedOutlet.setOutletName("Updated Outlet Name");
            masterDataCache.updateOutletCache(updatedOutlet);
            
            Outlet result = masterDataCache.getOutlet(TEST_OUTLET_CODE);
            assertNotNull(result);
            assertEquals("Updated Outlet Name", result.getOutletName());
            assertSame(updatedOutlet, result);
            verifyNoInteractions(outletService);
        }

        @Test
        @DisplayName("getOutletByOutletCode - Batch Get, Service Calls for Misses")
        void testGetOutletByOutletCode_BatchGet_ServiceCallsForMisses() {
            String code1 = "out1";
            String code2 = "out2";
            String code3 = "out3";

            OutletResponse resp1Preload = new OutletResponse(); resp1Preload.setOutletCode(code1); resp1Preload.setOutletName("Outlet 1 Preloaded");
            OutletResponse resp2Service = new OutletResponse(); resp2Service.setOutletCode(code2); resp2Service.setOutletName("Outlet 2 From Service");
            OutletResponse resp3Service = new OutletResponse(); resp3Service.setOutletCode(code3); resp3Service.setOutletName("Outlet 3 From Service");
            
            Outlet preloadedOutlet = new Outlet();
            preloadedOutlet.setOutletCode(code1);
            preloadedOutlet.setOutletName("Outlet 1 Preloaded");
            masterDataCache.updateOutletCache(preloadedOutlet);
            clearInvocations(outletService);

            when(outletService.getOutlet(argThat(req -> req.getOutletCode().equals(code2)))).thenReturn(resp2Service);
            when(outletService.getOutlet(argThat(req -> req.getOutletCode().equals(code3)))).thenReturn(resp3Service);

            List<String> outletCodesToGet = Arrays.asList(code1, code2, code3);
            List<Outlet> results = masterDataCache.getOutletByOutletCode(outletCodesToGet);

            assertNotNull(results);
            assertEquals(3, results.size());
            assertTrue(results.stream().anyMatch(o -> o.getOutletCode().equals(code1) && o.getOutletName().equals("Outlet 1 Preloaded")));
            assertTrue(results.stream().anyMatch(o -> o.getOutletCode().equals(code2) && o.getOutletName().equals("Outlet 2 From Service")));
            assertTrue(results.stream().anyMatch(o -> o.getOutletCode().equals(code3) && o.getOutletName().equals("Outlet 3 From Service")));

            verify(outletService, times(1)).getOutlet(argThat(req -> req.getOutletCode().equals(code2)));
            verify(outletService, times(1)).getOutlet(argThat(req -> req.getOutletCode().equals(code3)));
            verify(outletService, never()).getOutlet(argThat(req -> req.getOutletCode().equals(code1)));

            clearInvocations(outletService);
            List<Outlet> resultsCached = masterDataCache.getOutletByOutletCode(outletCodesToGet);
            assertEquals(3, resultsCached.size());
            verifyNoInteractions(outletService); 
        }
        
        @Test
        @DisplayName("getOutletByOutletCode - Empty List input")
        void testGetOutletByOutletCode_EmptyList() {
            List<Outlet> results = masterDataCache.getOutletByOutletCode(Collections.emptyList());
            assertNotNull(results);
            assertTrue(results.isEmpty());
            verifyNoInteractions(outletService);
        }
    }

    @Nested
    @DisplayName("PosCpg Cache Tests")
    class PosCpgCacheTests {
        private final String TEST_POS_CODE = "pos789";
        private final String POS_CPG_CACHE_GROUP = "POS_CPG";

        @BeforeEach
        void initKeys() {
            
        }

        private List<PosCpgResponse> createMockPosCpgResponseList(int count) {
            List<PosCpgResponse> list = new ArrayList<>();
            for (int i=0; i < count; i++) {
                PosCpgResponse respItem = new PosCpgResponse();
                list.add(respItem);
            }
            return list;
        }

        @Test
        @DisplayName("getPosCpg - Cache Miss, Service Called")
        void testGetPosCpg_CacheMiss_ServiceCalled() {
            List<PosCpgResponse> mockResponseList = createMockPosCpgResponseList(2);
            when(posCpgService.queryPosCpgListByPos(TEST_POS_CODE)).thenReturn(mockResponseList);
            List<PosCpg> result1 = masterDataCache.getPosCpg(TEST_POS_CODE); 
            assertNotNull(result1);
            assertEquals(2, result1.size());
            verify(posCpgService, times(1)).queryPosCpgListByPos(TEST_POS_CODE);

            List<PosCpg> result2 = masterDataCache.getPosCpg(TEST_POS_CODE);
            assertEquals(result1.size(), result2.size());
            verifyNoMoreInteractions(posCpgService);
        }
        
        @Test
        @DisplayName("deletePosCpgCache - Evicts local and calls Redis delete")
        void testDeletePosCpgCache() {
            List<PosCpgResponse> mockResponseList = createMockPosCpgResponseList(1);
            when(posCpgService.queryPosCpgListByPos(TEST_POS_CODE)).thenReturn(mockResponseList);

            masterDataCache.getPosCpg(TEST_POS_CODE);
            clearInvocations(posCpgService, gvRedisTemplate);
            when(posCpgService.queryPosCpgListByPos(TEST_POS_CODE)).thenReturn(mockResponseList); 
            doReturn(true).when(gvRedisTemplate).delete(anyString(), anyString()); 
            
            masterDataCache.deletePosCpgCache(TEST_POS_CODE); 
            verify(gvRedisTemplate, times(1)).delete(eq(POS_CPG_CACHE_GROUP), eq(TEST_POS_CODE));

            masterDataCache.getPosCpg(TEST_POS_CODE); 
            verify(posCpgService, times(1)).queryPosCpgListByPos(TEST_POS_CODE); 
        }

        @Test
        @DisplayName("hasPosCpgWithCpgCode - Basic null/blank checks")
        void testHasPosCpgWithCpgCode_BasicChecks() {
            assertFalse(masterDataCache.hasPosCpgWithCpgCode(TEST_POS_CODE, null));
            assertFalse(masterDataCache.hasPosCpgWithCpgCode(null, "cpgX"));

            when(posCpgService.queryPosCpgListByPos("emptyPos")).thenReturn(Collections.emptyList());
            assertFalse(masterDataCache.hasPosCpgWithCpgCode("emptyPos", "cpgAny"));
        }

        @Test
        @DisplayName("hasPosCpg - True and False cases")
        void testHasPosCpg() {
            when(posCpgService.queryPosCpgListByPos(TEST_POS_CODE)).thenReturn(createMockPosCpgResponseList(1));
            assertTrue(masterDataCache.hasPosCpg(TEST_POS_CODE));

            when(posCpgService.queryPosCpgListByPos("posNoCpgs")).thenReturn(Collections.emptyList());
            assertFalse(masterDataCache.hasPosCpg("posNoCpgs"));

            assertFalse(masterDataCache.hasPosCpg(null));
            assertFalse(masterDataCache.hasPosCpg(""));
        }
    }
    
    @Nested
    @DisplayName("OutletCpg Cache Tests")
    class OutletCpgCacheTests {
        private final String TEST_OUTLET_CODE = "outlet789";
        private final String OUTLET_CPG_CACHE_GROUP = "OUTLET_CPG";

        @BeforeEach
        void initKeys() {
            
        }

        private List<OutletCpgResponse> createMockOutletCpgResponseList(int count) {
            List<OutletCpgResponse> list = new ArrayList<>();
            for (int i=0; i < count; i++) {
                OutletCpgResponse respItem = new OutletCpgResponse();
                list.add(respItem);
            }
            return list;
        }

        @Test
        @DisplayName("getOutletCpg - Cache Miss, Service Called")
        void testGetOutletCpg_CacheMiss_ServiceCalled() {
            List<OutletCpgResponse> mockResponseList = createMockOutletCpgResponseList(2);
            when(outletCpgService.queryOutletCpgListByOutlet(TEST_OUTLET_CODE)).thenReturn(mockResponseList);
            List<OutletCpg> result1 = masterDataCache.getOutletCpg(TEST_OUTLET_CODE);
            assertNotNull(result1);
            assertEquals(mockResponseList.size(), result1.size());
            verify(outletCpgService, times(1)).queryOutletCpgListByOutlet(TEST_OUTLET_CODE);

            List<OutletCpg> result2 = masterDataCache.getOutletCpg(TEST_OUTLET_CODE);
            assertEquals(mockResponseList.size(), result2.size());
            verifyNoMoreInteractions(outletCpgService);
        }
        
        @Test
        @DisplayName("deleteOutletCpgCache - Evicts local and calls Redis delete")
        void testDeleteOutletCpgCache() {
            List<OutletCpgResponse> mockResponseList = createMockOutletCpgResponseList(1);
            when(outletCpgService.queryOutletCpgListByOutlet(TEST_OUTLET_CODE)).thenReturn(mockResponseList);

            masterDataCache.getOutletCpg(TEST_OUTLET_CODE);
            clearInvocations(outletCpgService, gvRedisTemplate);
            when(outletCpgService.queryOutletCpgListByOutlet(TEST_OUTLET_CODE)).thenReturn(mockResponseList);
            doReturn(true).when(gvRedisTemplate).delete(anyString(), anyString());
            
            masterDataCache.deleteOutletCpgCache(TEST_OUTLET_CODE); 
            verify(gvRedisTemplate, times(1)).delete(eq(OUTLET_CPG_CACHE_GROUP), eq(TEST_OUTLET_CODE));

            masterDataCache.getOutletCpg(TEST_OUTLET_CODE); 
            verify(outletCpgService, times(1)).queryOutletCpgListByOutlet(TEST_OUTLET_CODE); 
        }
    }

    @Test
    @DisplayName("clearAllLocalCaches - Test local cache clearing behavior")
    void testClearAllLocalCaches() {
        String posId = "posToClear";
        PosResponse posResp = new PosResponse(); posResp.setMachineId(posId);
        when(posService.getPos(argThat(req -> req.getMachineId().equals(posId)))).thenReturn(createSuccessResult(posResp));
        masterDataCache.getPos(posId);

        String cpgCode = "cpgToClear";
        GetCpgResponse cpgResp = new GetCpgResponse(); cpgResp.setCpgCode(cpgCode);
        when(cpgService.getCpg(argThat(req -> req.getCpgCode().equals(cpgCode)))).thenReturn(createSuccessResult(cpgResp));
        masterDataCache.getCpg(cpgCode);

        clearInvocations(posService, cpgService, gvRedisTemplate);
        when(posService.getPos(argThat(req -> req.getMachineId().equals(posId)))).thenReturn(createSuccessResult(posResp));
        when(cpgService.getCpg(argThat(req -> req.getCpgCode().equals(cpgCode)))).thenReturn(createSuccessResult(cpgResp));
        doReturn(true).when(gvRedisTemplate).delete(anyString(), anyString());

        masterDataCache.clearAllLocalCaches();

        masterDataCache.getPos(posId); 
        verify(posService, times(1)).getPos(any(GetPosRequest.class)); 

        masterDataCache.getCpg(cpgCode); 
        verify(cpgService, times(1)).getCpg(any(GetCpgRequest.class)); 
    }
} 