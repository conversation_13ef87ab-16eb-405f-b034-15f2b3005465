package com.example.sql;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import org.apache.poi.ss.usermodel.*;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

public class LiabilityUtil {
    public static void createDirectoryIfNotExists(String directoryPath) {
        File directory = new File(directoryPath);
        if (!directory.exists()) {
            directory.mkdirs();
        }
    }

    public static List<String> getTxtFilesContentFromFolder(String folderPath) {
        File folder = new File(folderPath);
        File[] files = folder.listFiles((dir, name) -> name.toLowerCase().endsWith(".txt"));

        List<String> contents = new ArrayList<>();
        if (files != null) {
            for (File file : files) {
                System.out.println("Reading txt file: " + file.getAbsolutePath());
                try {
                    contents.add(new String(Files.readAllBytes(Paths.get(file.getAbsolutePath()))));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } else {
            System.out.println("No txt files found in folder: " + folderPath);
        }
        return contents;
    }
    public static void writeDataToFile(String filePath, Set<String> data) {
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
            for (String line : data) {
                writer.write(line);
                writer.newLine();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    public static List<LiabilityScript.TransactionData> parseLiabilityData(List<String> l98Data) {
        List<LiabilityScript.TransactionData> transactionDataList = new ArrayList<>();
        for (String data : l98Data) {
            String[] lines = data.split("\n");
            for (String line : lines) {
                LiabilityScript.TransactionData transactionData = new LiabilityScript.TransactionData();
                String[] fields = line.split("\t");
                transactionData.setVoucherCode(fields[0]);
                transactionDataList.add(transactionData);
            }
        }
        return transactionDataList;
    }
    public static Set<LiabilityScript.TransactionData> readTransactionDataFromFile(String filePath) {
        Set<LiabilityScript.TransactionData> data = new HashSet<>();
        System.out.println("Reading data from file: " + filePath);
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = reader.readLine()) != null) {

                String[] fields = line.split(",");
                LiabilityScript.TransactionData transactionData = new LiabilityScript.TransactionData();
                transactionData.setVoucherCode(fields[0]);
                transactionData.setTransactionDate(DateUtil.parse(fields[1]));
                data.add(transactionData);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        System.out.println("Finished reading data from file: " + filePath + ", total records: " + data.size());
        return data;
    }

    public static void readAndStoreFilteredDataUsingPOIByStartAndEnd(String folderPath, String outputFile, DateTime start, DateTime end) {
        File folder = new File(folderPath);
        File[] files = folder.listFiles((dir, name) -> name.toLowerCase().endsWith(".xlsx") || name.toLowerCase().endsWith(".xls"));
        Map<String, Integer> columnIndices = new HashMap<>();
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile))) {
            if (files != null) {
                for (File file : files) {
                    System.gc();
                    System.out.println("Processing file: " + file.getAbsolutePath());
                    try (InputStream inputStream = new FileInputStream(file);
                         Workbook workbook = WorkbookFactory.create(inputStream)) {

                        Sheet sheet = workbook.getSheetAt(0);
                        columnIndices = getColumnIndices(sheet);
                        for (Row row : sheet) {
                            handleRowByStartAndEnd(row, writer, start, end, columnIndices);
                        }
                    } catch (Exception e) {
                        System.out.println("Error reading file: " + file.getAbsolutePath());
                        e.printStackTrace();
                    }
                }
            } else {
                System.out.println("No files found in folder: " + folderPath);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    public static void readAndStoreFilteredDataUsingPOI(String folderPath, String outputFile) {
        File folder = new File(folderPath);
        File[] files = folder.listFiles((dir, name) -> name.toLowerCase().endsWith(".xlsx") || name.toLowerCase().endsWith(".xls"));
        Map<String, Integer> columnIndices = new HashMap<>();
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile))) {
            if (files != null) {
                for (File file : files) {
                    System.gc();
                    System.out.println("Processing file: " + file.getAbsolutePath());
                    try (InputStream inputStream = new FileInputStream(file);
                         Workbook workbook = WorkbookFactory.create(inputStream)) {

                        Sheet sheet = workbook.getSheetAt(0);
                        columnIndices = getColumnIndices(sheet);
                        for (Row row : sheet) {
                            handleRow(row, writer, columnIndices);
                        }
                    } catch (Exception e) {
                        System.out.println("Error reading file: " + file.getAbsolutePath());
                        e.printStackTrace();
                    }
                }
            } else {
                System.out.println("No files found in folder: " + folderPath);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    public static Map<String, Integer> getColumnIndices(Sheet sheet) {
        Map<String, Integer> columnIndices = new HashMap<>();
        Row headerRow = sheet.getRow(0);
        for (Cell cell : headerRow) {
            columnIndices.put(cell.getStringCellValue().trim(), cell.getColumnIndex());
        }
        return columnIndices;
    }


    public static void handleRowByStartAndEnd(Row row, BufferedWriter writer, DateTime start, DateTime end, Map<String, Integer> columnIndices) throws IOException {
        if (row.getRowNum() == 0) {
            System.out.println("Header row: " + row);
            return; // 忽略标题行
        }

        String voucherCode = getCellValue(row.getCell(columnIndices.get("Voucher Number")));
        String dateStr = getCellValue(row.getCell(columnIndices.get("Transaction Date")));
        String transactionType = getCellValue(row.getCell(columnIndices.get("Transaction Type")));
        String cpgName = getCellValue(row.getCell(columnIndices.get("Voucher Program Group")));

        //System.out.println("Row index: " + row.getRowNum() + ", Voucher Code: " + voucherCode + ", Date: " + dateStr + ", Transaction Type: " + transactionType);

        try {
            DateTime transactionDate = DateUtil.parse(dateStr);
            if (DateUtil.isIn(transactionDate, start, end)) {
                String line = voucherCode + "," + dateStr + "," + transactionType + "," + cpgName;
                writer.write(line);
                writer.newLine();
            } else {
                //System.out.println("Date " + dateStr + " is not in the range.");
            }
        } catch (Exception e) {
            System.out.println("Error processing row: " + e.getMessage());
            e.printStackTrace();
        }
    }


    public static void handleRow(Row row, BufferedWriter writer, Map<String, Integer> columnIndices) throws IOException {
        if (row.getRowNum() == 0) {
            System.out.println("Header row: " + row);
            return; // 忽略标题行
        }

        String voucherCode = getCellValue(row.getCell(columnIndices.get("Voucher Number")));
        String dateStr = getCellValue(row.getCell(columnIndices.get("Transaction Date")));
        String transactionType = getCellValue(row.getCell(columnIndices.get("Transaction Type")));
        String cpgName = getCellValue(row.getCell(columnIndices.get("Voucher Program Group")));

        //System.out.println("Row index: " + row.getRowNum() + ", Voucher Code: " + voucherCode + ", Date: " + dateStr + ", Transaction Type: " + transactionType);

        try {
            DateTime transactionDate = DateUtil.parse(dateStr);

            String line = voucherCode + "," + dateStr + "," + transactionType + "," + cpgName;
            writer.write(line);
            writer.newLine();

        } catch (Exception e) {
            System.out.println("Error processing row: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }


}
