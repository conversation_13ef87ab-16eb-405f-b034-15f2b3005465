package com.example.sql;

import cn.hutool.core.annotation.Alias;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

import java.io.*;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/6/7 10:46
 */
@Slf4j
public class csvToSql {

    private Connection conn = null;

    private String driverName = "com.mysql.cj.jdbc.Driver";

    private String url = "rm-d9jv2396il7s83zy1po.mysql.ap-southeast-5.rds.aliyuncs.com";

    private String password = "heT5MDxtk5PxIrYj";

    private String userName = "gtechtest";

    private String database = "test_gvcore_db";




    /**
     * 获取原生jdbc连接
     *
     * @return
     * @throws ClassNotFoundException
     * @throws SQLException
     */
    private Connection getConnection() throws ClassNotFoundException, SQLException {
        Class.forName(driverName);
        conn = DriverManager.getConnection(url, userName, password);
        return conn;
    }



    /**
     * 将数据从输入流加载到MySQL。
     *
     * @param loadDataSql SQL语句。
     * @param dataStream  输入流。
     * @return int         成功插入的行数。
     */
    private int bulkLoadFromInputStream(String loadDataSql,
                                        InputStream dataStream) throws SQLException, ClassNotFoundException {
        if (null == dataStream) {
            log.info("输入流为NULL，没有数据导入。");
            return 0;
        }
        //做测试发现连接池好像不支持，所以获取原生的jdbc连接
        conn = getConnection();
        PreparedStatement statement = null;
        int result = 0;
        try {
            statement = conn.prepareStatement(loadDataSql);
//            mysql8  使用下面的方式
        if (statement.isWrapperFor(com.mysql.cj.jdbc.JdbcStatement.class)) {
            com.mysql.cj.jdbc.ClientPreparedStatement mysqlStatement = statement.unwrap(com.mysql.cj.jdbc.ClientPreparedStatement.class);
            mysqlStatement.setLocalInfileInputStream(dataStream);
            mysqlStatement.executeUpdate();
        }
//        mysql5 使用下面的方式
//            if (statement.isWrapperFor(com.mysql.jdbc.Statement.class)) {
//                com.mysql.jdbc.PreparedStatement mysqlStatement = statement.unwrap(com.mysql.jdbc.PreparedStatement.class);
//                mysqlStatement.setLocalInfileInputStream(dataStream);
//                result = mysqlStatement.executeUpdate();
//            }
        } finally {
            if (statement != null) {
                statement.close();
            }
        }
        return result;
    }



    /**
     * 组装 SQL 语句
     *
     * @param dataBaseName 数据库名。
     * @param tableName    表名。
     * @param columnName   要插入数据的列名。
     */
    public String assembleSql(String dataBaseName, String tableName, String[] columnName) {
        String insertColumnName = org.apache.commons.lang3.StringUtils.join(columnName, ",");
        //插入方案的核心就是下面这个语句
        String sql = "LOAD DATA LOCAL INFILE 'sql.csv' IGNORE INTO TABLE " + dataBaseName + "." + tableName + " (" + insertColumnName + ")";
        return sql;
    }
    /**
     * 通过 LOAD DATA LOCAL INFILE 大批量导入数据到 MySQL。
     *
     * 原理是使用 setLocalInfileInputStream 会忽略 sql.csv 文件名，不从文件读取，直接从输入流读取数据
     * @param sql     SQL语句。
     * @param builder 组装好的数据。
     */
    public int fastInsertData(String sql, StringBuilder builder) {
        int rows = 0;
        InputStream is = null;
        try {
            byte[] bytes = builder.toString().getBytes();
            is = new ByteArrayInputStream(bytes);
            //批量插入数据。
            long beginTime = System.currentTimeMillis();
            rows = bulkLoadFromInputStream(sql, is);
            long endTime = System.currentTimeMillis();
            log.info(" LOAD DATA LOCAL INFILE :【插入" + rows + "行数据至MySql中，耗时" + (endTime - beginTime) + "ms。】");
        } catch (SQLException | ClassNotFoundException e) {
            e.printStackTrace();
        } finally {
            try {
                if (null != is) {
                    is.close();
                }
                if (null != conn) {
                    conn.close();
                }
            } catch (SQLException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return rows;
    }

    /**
     * 批量插入方法
     *
     * @return
     */
    public void insertData(String dataBaseName, String tableName, String[] columns, StringBuilder tableColumnValue, int size) {

        //插入语句
        String sql = assembleSql(dataBaseName, tableName, columns);
        //待插入行数，为了判断是否全部插入
        int mainTableInsertRows = fastInsertData(sql, tableColumnValue);
        //插入数据未全部成功视为全部失败
        if (mainTableInsertRows != size)
            log.info("插入失败!");
    }

    public static void main(String[] args) throws IOException, SQLException, ExecutionException, InterruptedException {
        StopWatch stopWatch = new StopWatch("csvToSql");
        csvToSql csvToSql = new csvToSql();
//        log.info("开始执行");
//        log.info("开始执行Company");
//        csvToSql.Company();//插入数据库
//        log.info("开始执行Merchant");
//        csvToSql.Merchant();//插入数据库
//        log.info("开始执行Outlet");
//        csvToSql.Outlet();//插入数据库
//        log.info("开始执行outletCpg");
//        csvToSql.outletCpg();//sql文件
//        log.info("开始执行pos");
//        csvToSql.pos();
//        log.info("开始执行posCpg");
//        csvToSql.posCpg();//sql文件
//        log.info("开始执行userAccount");
//        csvToSql.userAccount();
//        csvToSql.accountRole();
//        log.info("开始执行Customer");
//        csvToSql.Customer();
//        log.info("开始执行voucherBatch");
//        csvToSql.voucherBatch();//sql文件
//        log.info("开始执行booklet");
//        csvToSql.booklet();//sql文件
//        log.info("开始执行voucher");
//        csvToSql.voucher();//sql文件
//        log.info("执行结束");

        log.info("开始执行实体券");
        //实体券
        importPhysicalCoupon();

        log.info("开始执行电子券");
        //电子券
        importDigitalVoucher();
        log.info("开始执行M19");
        //M19
        importM19PhysicalCoupon();


    }
    public  String transformToBatchInsert(List<String> insertStatements) {
        // 正则表达式，用于匹配 INSERT 语句
        Pattern insertPattern = Pattern.compile("INSERT INTO (.+?) \\((.+?)\\) VALUES \\((.+?)\\);");

        StringBuilder batchInsert = new StringBuilder();
        String tableName = null;
        String columns = null;
        List<String> allValues = new ArrayList<>();

        for (String insert : insertStatements) {
            Matcher matcher = insertPattern.matcher(insert);
            if (matcher.matches()) {
                if (tableName == null) {
                    tableName = matcher.group(1);
                    columns = matcher.group(2);
                }
                allValues.add("(" + matcher.group(3) + ")");
            }
        }

        if (tableName != null) {
            batchInsert.append("INSERT INTO ").append(tableName)
                    .append(" (").append(columns).append(") VALUES ")
                    .append(String.join(", ", allValues)).append(";");
        }

        return batchInsert.toString();
    }

    private static void importPhysicalCoupon() throws IOException, ExecutionException, InterruptedException {
        //csv/voucher/physical
        //获取resources下csv/voucher/physical文件中所有文件的名字

        List<String> fileNameList = Lists.newArrayList(
                //"1-Voucher Inventory Data which GV still at Printing Vendor.csv",
                        "2-Voucher Inventory Data stock at WH01-output.csv",
                        "3-Voucher Inventory Data stock at HO01-output.csv",
                        "4-Voucher Inventory Data stock at MV01-output.csv",
                        "5-Voucher Inventory Data stock at MV04-output.csv",
                        "6-Voucher Inventory Data stock at MAP stores-output.csv");
        ExecutorService executorService = Executors.newFixedThreadPool(fileNameList.size());
        int totalFiles = fileNameList.size();
        AtomicInteger completedFiles = new AtomicInteger();

        List<Future<?>> futures = new ArrayList<>();
        for (String file : fileNameList) {
            //Future<?> future = executorService.submit(() -> {
                try {
                    log.info("开始处理{"+file+"}");
                    String path = "csv/voucher/physical/" + file;
                    csvToSql.voucherBatch(path);
                    csvToSql.booklet(path);
                    csvToSql.voucher(path);

                    completedFiles.incrementAndGet();
                    int i = completedFiles.get();
                    int totalFiles1 = totalFiles;

                    log.info("已完成{"+i+"} / {"+totalFiles1+"} 个文件，当前文件: {"+file+"}");
                } catch (IOException e) {
                    log.error("处理文件 " + file + " 时发生IO异常", e);
                }
           // });

           // futures.add(future);
        }

        // 等待所有任务完成
        /*for (Future<?> future : futures) {
            future.get();
        }*/

        //executorService.shutdown();
    }

    private static void importM19PhysicalCoupon() throws IOException, ExecutionException, InterruptedException {




        //csv/voucher/M19
        //获取resources下csv/voucher/physical文件中所有文件的名字

        List<String> fileNameList = Lists.newArrayList(
                "7-Voucher Active Data for Paper Voucher M19 series-output.csv" ,
                        "9-Voucher Active Data for Paper Voucher SOGO Gift Voucher-output.csv");
        ExecutorService executorService = Executors.newFixedThreadPool(fileNameList.size());
        int totalFiles = fileNameList.size();
        AtomicInteger completedFiles = new AtomicInteger();

        List<Future<?>> futures = new ArrayList<>();
        for (String file : fileNameList) {
            //Future<?> future = executorService.submit(() -> {
                try {
                    log.info("开始处理{"+file+"}");
                    String path = "csv/voucher/M19/" + file;
                    csvToSql.voucherBatch(path);
                    csvToSql.booklet(path);
                    csvToSql.voucher(path);
                    completedFiles.incrementAndGet();
                    int i = completedFiles.get();
                    int totalFiles1 = totalFiles;
                    log.info("已完成{"+i+"} / {"+totalFiles1+"} 个文件，当前文件: {"+file+"}");
                } catch (IOException e) {
                    log.info( "处理文件 " + file + " 时发生IO异常", e);
                }
            //});

            //futures.add(future);
        }

        // 等待所有任务完成
        //for (Future<?> future : futures) {
            //future.get();
        //}

        //executorService.shutdown();

    }

    private static void importDigitalVoucher() throws IOException, ExecutionException, InterruptedException {


        //csv/voucher/digital
        //获取resources下csv/voucher/physical文件中所有文件的名字
        List<String> fileNameList = Lists.newArrayList(

                "8-Voucher Active Data for Paper Voucher QC Series-output.csv" ,
                        "10-Voucher Deactivated Data (Paper)-output.csv" ,
                        "11-Voucher Active Data Digital- NOT in MAPCLUB-output.csv",
                        "12-Voucher Active Data Digital- IN MAPCLUB-output.csv" ,
                        "13-Voucher Active Data Digital-Barcode (Ultra Voucher)-output.csv" ,
                        "14-Voucher Active Data for Starbucks Digital Voucher-output.csv" ,
                        "15-Voucher Deactivated Data (Digital)-output.csv");
        ExecutorService executorService = Executors.newFixedThreadPool(fileNameList.size());
        int totalFiles = fileNameList.size();
        AtomicInteger completedFiles = new AtomicInteger();

        List<Future<?>> futures = new ArrayList<>();
        for (String file : fileNameList) {
            //Future<?> future = executorService.submit(() -> {
                try {

                    log.info("开始处理{"+file+"}");
                    String path = "csv/voucher/digital/" + file;
                    csvToSql.importDigitalVoucherBatch(path);
                    csvToSql.DigitalVoucher(path);
                    completedFiles.incrementAndGet();
                    int i = completedFiles.get();
                    int totalFiles1 = totalFiles;
                    log.info("已完成{"+i+"} / {"+totalFiles1+"} 个文件，当前文件: {"+file+"}");
                } catch (IOException e) {
                    log.info( "处理文件 " + file + " 时发生IO异常", e);
                }
            //});

            //futures.add(future);
        }

        // 等待所有任务完成
        /*for (Future<?> future : futures) {
            future.get();
        }*/

        //executorService.shutdown();
    }



    private static final String MIX_SALT = "26a711f75b90055fe169fca73a0e0f03";


    private static void ErrorWriteFile(String fileName, String content)  {

        try {
            //FileWriter file = new java.io.FileWriter("D:\\project\\gvcore\\SQL\\src\\main\\resources\\sqlFile\\error\\"+fileName+".txt");
            String projectPath = System.getProperty("user.dir");
            String filePath = projectPath + "/SQL/src/main/resources/sqlFile/error/"+fileName+".txt";
            FileWriter file = new java.io.FileWriter(filePath);
            BufferedWriter finalWriter = null;
            finalWriter = new BufferedWriter(file);
            finalWriter.append(content);
            finalWriter.close();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }


    private void Merchant() throws SQLException {


        /*CsvReader reader = CsvUtil.getReader();
        //读取resources下csv里的文件
        List<Merchant> read = reader.read(ResourceUtil.getUtf8Reader("csv.base/MD - Merchant.xlsx"), Merchant.class);
*/
        ExcelReader reader = ExcelUtil.getReader("csv.base/MD - Merchant.xlsx");
        List<Merchant> read = reader.readAll(Merchant.class);

        List<String> errorList = checkCompany(read.stream().map(Merchant::getCompanyCode).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(errorList)){
            ErrorWriteFile("MerchantError","[Merchant:数据库中不存在的company] "+errorList.toString());
        }


        //判断是否增量数据
        List<Entity> gvMerchant = queryMysql("gv_merchant");
        List<Entity> gvCompany = queryMysql("gv_company");
        //获取gvCompany的companyCode和companyName
        Map<String, String> companyMap = gvCompany.stream().collect(Collectors.toMap(x -> x.getStr("company_name").toLowerCase(), x -> x.getStr("company_code")));


        //去除掉重复的数据
        List<Merchant> filterList = read.stream().filter(x -> gvMerchant.stream().noneMatch(entity -> entity.getStr("merchant_name").toLowerCase().equals(x.getMerchantName().toLowerCase()))).collect(Collectors.toList());

        for (Merchant merchant : filterList) {
            merchant.setMerchantCode(UUID.randomUUID().toString());
            merchant.setCompanyCode(companyMap.get(merchant.getCompanyCode().toLowerCase()));
        }



        //遍历行
        for (Merchant merchant : filterList) {
            if (StringUtils.isEmpty(merchant.getMerchantCode())){
                merchant.setMerchantCode(merchant.getMerchantName());
            }
            if (StringUtils.isEmpty(merchant.getRemarks())){
                merchant.setRemarks("");
            }
            //输出mysql语句
            //log.info("INSERT INTO `gv_merchant`(`merchant_code`, `merchant_name`, `issuer_code`, `company_code`, `status`, `remarks`) VALUES ('"+merchant.getMerchantCode()+"', '"+merchant.getMerchantName()+"', '"+merchant.getIssuerCode()+"', '"+merchant.getCompanyCode()+"', '"+merchant.getStatus()+"', '"+merchant.getRemarks()+"');");
            try {
                Db.use().insert(
                        Entity.create("gv_merchant")
                                .set("merchant_code", merchant.getMerchantCode())
                                .set("merchant_name", merchant.getMerchantName())
                                .set("issuer_code", merchant.getIssuerCode())
                                .set("company_code", merchant.getCompanyCode())
                                .set("status", merchant.getStatus())
                                .set("remarks", merchant.getRemarks())
                );
            } catch (SQLException e) {
                log.info("添加merchant失败"+e.getMessage()+ "--"+ merchant.toString());
            }

        }
    }

    private void Company() throws SQLException {
        /*CsvReader reader = CsvUtil.getReader();
        //读取resources下csv里的文件
        List<Company> read = reader.read(ResourceUtil.getUtf8Reader("csv.base/MD - SBU-Company.xlsx"), Company.class);
*/

        ExcelReader reader = ExcelUtil.getReader("csv.base/MD - SBU-Company.xlsx");
        List<Company> read = reader.readAll(Company.class);


        //判断是否增量数据
        List<Entity> gvCompany = queryMysql("gv_company");
        //去除掉重复的数据
        //List<Company> filterList = read.stream().filter(x -> gvCompany.stream().noneMatch(entity -> entity.getStr("company_name").toLowerCase().equals(x.getCompanyName().toLowerCase()))).collect(Collectors.toList());
        List<Company> filterList = read;
        if (CollectionUtils.isEmpty(filterList)){
            log.info("没有增量数据");
            return;
        }


        for (Company company : filterList) {
            company.setCompanyCode(UUID.randomUUID().toString());
        }



        //遍历行
        for (Company company : filterList) {
            if (StringUtils.isEmpty(company.getCompanyCode())){
                company.setCompanyCode(company.getCompanyName());
            }
            //输出mysql语句
            //log.info("INSERT INTO `gv_company`(`company_code`, `company_name`, `issuer_code`, `sbu`, `status`) VALUES ('"+company.getCompanyCode()+"', '"+company.getCompanyName()+"', '"+company.getIssuerCode()+"', '"+company.getSbu()+"', '"+company.getStatus()+"');");
            try {
                Db.use().insert(
                        Entity.create("gv_company")
                                .set("company_code", company.getCompanyCode())
                                .set("company_name", company.getCompanyName())
                                .set("issuer_code", company.getIssuerCode())
                                .set("sbu", company.getSbu())
                                .set("status", company.getStatus())
                );
            } catch (Exception e) {
                log.error("添加company失败"+e.getMessage());
            }
        }
    }

    private void Outlet() throws SQLException {

        ExcelReader reader = ExcelUtil.getReader("csv.base/MD - Outlet.xlsx");
        List<Outlet> read = reader.readAll(Outlet.class);

        List<String> checkMerchantErrorList = checkMerchant(read.stream().map(Outlet::getMerchantCode).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(checkMerchantErrorList)){
            ErrorWriteFile("OutletError","[Outlet:数据库中不存在的merchant] "+checkMerchantErrorList.toString());
        }

        //判断是否增量数据
        List<Entity> gvOutlet = queryMysql("gv_outlet");
        List<Entity> gvMerchant = queryMysql("gv_merchant");
        List<Outlet> filterList = new ArrayList<>();
        //去除掉重复的数据
        if(read != null && gvOutlet != null) {
            filterList = read.stream().filter(x -> x.getBusinessOutletCode() != null &&
                            gvOutlet.stream()
                                    .noneMatch(entity -> entity != null &&
                                            entity.getStr("business_outlet_code") != null &&
                                            entity.getStr("business_outlet_code").toLowerCase()
                                                    .equals(x.getBusinessOutletCode().toLowerCase())))
                    .collect(Collectors.toList());
        }
        Map<String, String> merchantMap = gvMerchant.stream().collect(Collectors.toMap(x -> x.getStr("merchant_name").toLowerCase(), x -> x.getStr("merchant_code")));



        //遍历行
        for (Outlet outlet : filterList) {
            if ( merchantMap.containsKey(outlet.getMerchantCode().toLowerCase())){
                outlet.setMerchantCode(merchantMap.get(outlet.getMerchantCode().toLowerCase()));
            }else {
                continue;
            }
            outlet.setOutletCode(UUID.randomUUID().toString());

            //输出mysql语句,注意这里的逗号

            /*log.info("INSERT INTO `gv_outlet`(`outlet_code`," +
                    " `outlet_name`," +
                    " `merchant_code`," +
                    " `business_outlet_code`," +
                    " `outlet_type`," +
                    " `state_code`," +
                    " `city_code`," +
                    " `district_code`," +
                    " `address1`," +
                    " `address2`," +
                    " `pin_code`," +
                    " `first_name`, " +
                    "`last_name`," +
                    " `email`," +
                    " `phone`," +
                    " `mobile`," +
                    " `alertnate_email`," +
                    " `alertnate_phone`," +
                    " `descriptive`," +
                    " `status`," +
                    " `parent_outlet`) VALUES ('"+outlet.getOutletCode()+"', '"+outlet.getOutletName()+"', '"+outlet.getMerchantCode()+"', '"+outlet.getBusinessOutletCode()+"', '"+outlet.getOutletType()+"', '"+outlet.getStateCode()+"', '"+outlet.getCityCode()+"', '"+outlet.getDistrictCode()+"', '"+outlet.getAddress1()+"', '"+outlet.getAddress2()+"', '"+outlet.getPinCode()+"', '"+outlet.getFirstName()+"', '"+outlet.getLastName()+"', '"+outlet.getEmail()+"', '"+outlet.getPhone()+"', '"+outlet.getMobile()+"', '"+outlet.getAlternateEmail()+"', '"+outlet.getAlternatePhone()+"', '"+outlet.getDescriptive()+"', '"+outlet.getStatus()+"', '"+outlet.getParentOutlet()+"');");
*/
            try {
                Db.use().insert(
                        Entity.create("gv_outlet")
                                .set("outlet_code", outlet.getOutletCode())
                                .set("outlet_name", outlet.getOutletName())
                                .set("merchant_code", outlet.getMerchantCode())
                                .set("business_outlet_code", outlet.getBusinessOutletCode())
                                .set("outlet_type", outlet.getOutletType())
                                .set("state_code", outlet.getStateCode())
                                .set("city_code", outlet.getCityCode())
                                .set("district_code", outlet.getDistrictCode())
                                .set("address1", outlet.getAddress1())
                                .set("address2", outlet.getAddress2())
                                .set("pin_code", outlet.getPinCode())
                                .set("first_name", outlet.getFirstName())
                                .set("last_name", outlet.getLastName())
                                .set("email", outlet.getEmail())
                                .set("phone", outlet.getPhone())
                                .set("mobile", outlet.getMobile())
                                .set("alertnate_email", outlet.getAlternateEmail())
                                .set("alertnate_phone", outlet.getAlternatePhone())
                                .set("descriptive", outlet.getDescriptive())
                                .set("status", outlet.getStatus())
                                .set("parent_outlet", outlet.getParentOutlet())
                );
            } catch (SQLException e) {
                log.info("添加outlet失败"+e.getMessage());
            }
        }
    }





    private  void Customer() throws SQLException {
        //读取resources下csv里的文件

        ExcelReader read = ExcelUtil.getReader("csv.base/MD - Customer.xlsx");
        List<Customer> customerList = read.readAll(Customer.class);
        List<String> checkOutletErrorList = checkOutlet(customerList.stream().map(Customer::getOutletCode).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(checkOutletErrorList)){
            ErrorWriteFile("CustomerError","[Customer:数据库中不存在的outlet] "+checkOutletErrorList.toString());

        }


        List<String> checkCompanyErrorList = checkCompany(customerList.stream().map(Customer::getCompanyName).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(checkCompanyErrorList)) {
            ErrorWriteFile("MerchantError", "[Customer:数据库中不存在的company] " + checkCompanyErrorList.toString());
        }


        //判断是否增量数据
        List<Entity> gvCustomer = queryMysql("gv_customer");
        List<Entity> gvCompany = queryMysql("gv_company");
        List<Entity> gvOutlet = queryMysql("gv_outlet");
        //去除掉重复的数据
        List<Customer> filterList = new ArrayList<>();
        if (customerList != null && gvCustomer != null) {
            filterList = customerList.stream()
                    .filter(x -> x.getCompanyName() != null &&
                            gvCustomer.stream()
                                    .filter(entity -> entity.getStr("company_name") != null)
                                    .noneMatch(entity -> entity.getStr("company_name").equals(x.getCompanyName())))
                    .collect(Collectors.toList());
        }
        Map<String, String> companyMap = gvCompany.stream().collect(Collectors.toMap(x -> x.getStr("company_name").toLowerCase(), x -> x.getStr("company_code")));
        Map<String, String> outletMap = gvOutlet.stream().collect(Collectors.toMap(x -> x.getStr("outlet_name").toLowerCase(), x -> x.getStr("outlet_code")));


        //遍历行
        for (Customer customer : filterList) {
            customer.setCustomerCode(UUID.fastUUID().toString());

            if (outletMap.containsKey(customer.getOutletCode().toLowerCase())){
                customer.setOutletCode(outletMap.get(customer.getOutletCode().toLowerCase()));
            }else {
                log.info("outletCode 不存在 : "+customer.getOutletCode());
                return;
            }

            if (StringUtil.isEmpty(customer.getNote())){
                customer.setNote(" ");
            }
            if (StringUtil.isEmpty(customer.getIssuerCode())){
                customer.setIssuerCode("MAP");
            }



            //输出customer对象的insert语句
            String insertQuery = "INSERT INTO gv_customer (customer_code, outlet_code, customer_name, customer_type, company_name, contact_first_name, contact_last_name, contact_division, contact_phone, contact_email, shipping_address1, shipping_address2, note, status, beneficiary_name, branch_name, bank_name, account_number, pph, distribution_function, user_email, channel, registration_year) VALUES ("
                    + "'" + customer.getCustomerCode() + "', "
                    + "'" + customer.getOutletCode() + "', "
                    + "'" + customer.getCustomerName() + "', "
                    + "'" + customer.getCustomerType() + "', "
                    + "'" + customer.getCompanyName() + "', "
                    + "'" + customer.getContactFirstName() + "', "
                    + "'" + customer.getContactLastName() + "', "
                    + "'" + customer.getContactDivision() + "', "
                    + "'" + customer.getContactPhone() + "', "
                    + "'" + customer.getContactEmail() + "', "
                    + "'" + customer.getShippingAddress1() + "', "
                    + "'" + customer.getShippingAddress2() + "', "
                    + "'" + customer.getNote() + "', "
                    + "'" + customer.getStatus() + "', "
                    + "'" + customer.getBeneficiaryName() + "', "
                    + "'" + customer.getBranchName() + "', "
                    + "'" + customer.getBankName() + "', "
                    + "'" + customer.getAccountNumber() + "', "
                    + "'" + customer.getPph() + "', "
                    + "'" + customer.getDistributionFunction() + "', "
                    + "'" + customer.getUserEmail() + "', "
                    + "'" + customer.getChannel() + "', "
                    + "'" + customer.getRegistrationYear() + "'" +
                    ");";
            Db.use().insert(
                    Entity.create("gv_customer")
                            .set("customer_code", customer.getCustomerCode())
                            .set("issuer_code", customer.getIssuerCode())
                            .set("outlet_code", customer.getOutletCode())
                            .set("customer_name", customer.getCustomerName())
                            .set("customer_type", customer.getCustomerType())
                            .set("company_name", customer.getCompanyName())
                            .set("contact_first_name", customer.getContactFirstName())
                            .set("contact_last_name", customer.getContactLastName())
                            .set("contact_division", customer.getContactDivision())
                            .set("contact_phone", customer.getContactPhone())
                            .set("contact_email", customer.getContactEmail())
                            .set("shipping_address1", customer.getShippingAddress1())
                            .set("shipping_address2", customer.getShippingAddress2())
                            .set("note", customer.getNote())
                            .set("status", customer.getStatus())
                            .set("beneficiary_name", customer.getBeneficiaryName())
                            .set("branch_name", customer.getBranchName())
                            .set("bank_name", customer.getBankName())
                            .set("account_number", customer.getAccountNumber())
                            .set("pph", customer.getPph())
                            .set("distribution_function", customer.getDistributionFunction())
                            .set("user_email", customer.getUserEmail())
                            .set("channel", customer.getChannel())
                            .set("registration_year", customer.getRegistrationYear())
                            .set("create_user", "DataMigration")
            );
            //log.info(insertQuery);

        }




    }


    private void outletCpg() throws IOException, SQLException {
    ExcelReader read = ExcelUtil.getReader("csv.base/NU61.xlsx");
    List<OutletCpg> outletCpgs = read.readAll(OutletCpg.class);



        //判断是否增量数据
        List<Entity> gvOutletCpg = queryMysql("gv_outlet_cpg");
        List<Entity> gvOutlet = queryMysql("gv_outlet");
        List<Entity> gvCpg = queryMysql("gv_cpg");
        //获取所有outletCode对应的cpgList
        Map<String, List<Entity>> dbList = gvOutletCpg.stream().collect(Collectors.groupingBy(entity -> entity.getStr("outlet_code")));
        Map<String, List<OutletCpg>> csvList = outletCpgs.stream().collect(Collectors.groupingBy(OutletCpg::getOutletCode));


        List<String> checkOutletErrorList = checkOutlet(outletCpgs.stream().map(OutletCpg::getOutletCode).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(checkOutletErrorList)){
            ErrorWriteFile("OutletCpgError","[OutletCpg:数据库中不存在的outlet] "+checkOutletErrorList.toString());
        }


        List<String> checkCpgErrorList = checkCpg(outletCpgs.stream().map(OutletCpg::getVpg).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(checkCpgErrorList)){
            ErrorWriteFile("OutletCpgError","[OutletCpg:数据库中不存在的cpg] "+checkCpgErrorList.toString());
        }




        Map<String, String> outletMap = gvOutlet.stream().collect(Collectors.toMap(x -> x.getStr("business_outlet_code").toLowerCase(), x -> x.getStr("outlet_code")));
        Map<String, String> cpgMap = gvCpg.stream().collect(Collectors.toMap(x -> x.getStr("cpg_name").toLowerCase(), x -> x.getStr("cpg_code")));






        dbList.forEach((k,v)->{
            if (csvList.containsKey(k.toLowerCase())){
                csvList.get(k).removeIf(x->v.stream().anyMatch(entity -> entity.getStr("cpg_code").toLowerCase().equals(x.getVpg().toLowerCase())));
            }

        });

        //csvList中剩下的数据就是需要插入的数据
        List<OutletCpg> list = csvList.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        AtomicInteger i = new AtomicInteger();
        //FileWriter file = new java.io.FileWriter("D:\\project\\gvcore\\SQL\\src\\main\\resources\\sqlFile\\outletCpg\\outletCpg.txt");
        String projectPath = System.getProperty("user.dir");
        String filePath = projectPath + "/SQL/src/main/resources/sqlFile/outletCpg/outletCpg.txt";
        FileWriter file = new java.io.FileWriter(filePath);
        BufferedWriter writer = null;
        try {
            writer = new BufferedWriter(file);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        List<String> sqlList = new ArrayList<>();

        //遍历行
        for (OutletCpg customer : list){

            if (outletMap.containsKey(customer.getOutletCode().toLowerCase())){
                customer.setOutletCode(outletMap.get(customer.getOutletCode().toLowerCase()));
            }else {
                log.info("outletCode 不存在 : "+customer.getOutletCode());
            }

            if (cpgMap.containsKey(customer.getVpg().toLowerCase())){
                customer.setVpg(cpgMap.get(customer.getVpg().toLowerCase()));
            }else {
                log.info("cpgCode 不存在 : "+customer.getVpg());
            }

            customer.setOutletCpgCode(UUID.fastUUID().toString());
            //输出mysql
            String insertQuery = "INSERT INTO gv_outlet_cpg (outlet_cpg_code, outlet_code, cpg_code, status) VALUES ("
                    + "'" + customer.getOutletCpgCode() + "', "
                    + "'" + customer.getOutletCode() + "', "
                    + "'" + customer.getVpg() + "', "
                    + "'" + customer.getStatus() + "'" +
                    ");";
            /*Db.use().insert(Entity.create("gv_outlet_cpg")
                    .set("outlet_cpg_code", customer.getOutletCpgCode())
                    .set("outlet_code", customer.getOutletCode())
                    .set("cpg_code", customer.getVpg())
                    .set("status", customer.getStatus())
            );*/
            sqlList.add(insertQuery);


            if (sqlList.size()>=500){
                String sql = transformToBatchInsert(sqlList);
                writer.write(sql);
                writer.newLine();
                sqlList.clear();
            }

        }
        if (sqlList.size()> 0){
            String sql = transformToBatchInsert(sqlList);
            writer.write(sql);
            writer.newLine();
            sqlList.clear();
        }

        writer.flush();
        System.out.println(i.incrementAndGet());
        //遍历行

        log.info("写入成功！");
    }


    private void posCpg() throws IOException, SQLException {
        ExcelReader read = ExcelUtil.getReader("csv.base/MD - POS-VPG Mapping.xlsx");
        List<PosCpg> posCpgs = read.readAll(PosCpg.class);


        List<String> checkCpgErrorList = checkCpg(posCpgs.stream().map(PosCpg::getVpg).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(checkCpgErrorList)){
            ErrorWriteFile("OutletCpgError","[OutletCpg:数据库中不存在的cpg] "+checkCpgErrorList.toString());
        }
        List<String> checkPosErrorList = checkPos(posCpgs.stream().map(PosCpg::getVpg).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(checkPosErrorList)){
            ErrorWriteFile("OutletCpgError","[OutletCpg:数据库中不存在的pos] "+checkPosErrorList.toString());
        }


        //判断是否增量数据
        List<Entity> gvPosCpg = queryMysql("gv_pos_cpg");
        List<Entity> gvPos = queryMysql("gv_pos");
        List<Entity> gvCpg = queryMysql("gv_cpg");


        Map<String, String> posMap = gvPos.stream().collect(Collectors.toMap(x -> x.getStr("machine_id").toLowerCase(), x -> x.getStr("pos_code")));
        Map<String, String> cpgMap = gvCpg.stream().collect(Collectors.toMap(x -> x.getStr("cpg_name").toLowerCase(), x -> x.getStr("cpg_code")));


        posCpgs.forEach(x->x.setPosCode(posMap.get(x.getMachineId().toLowerCase())));
        //Map<String, String> dbPosCpg = gvPosCpg.stream().collect(Collectors.toMap(x -> x.getStr("pos_code"), x -> x.getStr("cpg_code")));

        //获取所有outletCode对应的cpgList
        Map<String, List<Entity>> dbList = gvPosCpg.stream().collect(Collectors.groupingBy(entity -> entity.getStr("pos_code")));
        Map<String, List<PosCpg>> posCpgMap = posCpgs.stream().collect(Collectors.groupingBy(PosCpg::getMachineId));

        //去除重复数据
        dbList.forEach((k,v)->{
            if (posCpgMap.containsKey(k)){
                posCpgMap.get(k).removeIf(x->v.stream().anyMatch(entity -> entity.getStr("cpg_code").equals(x.getVpg())));
            }

        });


        List<PosCpg> posCpgList = posCpgMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());


        AtomicInteger i = new AtomicInteger();

        //FileWriter file = new java.io.FileWriter("D:\\project\\gvcore\\SQL\\src\\main\\resources\\sqlFile\\posCpg\\posCpg.txt");
        String projectPath = System.getProperty("user.dir");
        String filePath = projectPath + "/SQL/src/main/resources/sqlFile/posCpg/posCpg.txt";
        FileWriter file = new java.io.FileWriter(filePath);
        BufferedWriter writer = null;
        try {
            writer = new BufferedWriter(file);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        List<String> sqlList = new ArrayList<>();
        for (PosCpg posCpg : posCpgList) {
            posCpg.setPosCpgCode(UUID.fastUUID().toString());

            if (posMap.containsKey(posCpg.getMachineId().toLowerCase())){
                posCpg.setMachineId(posMap.get(posCpg.getMachineId().toLowerCase()));
            }else {
                log.info("posCode 不存在 : "+posCpg.getMachineId());
                continue;
            }

            if (cpgMap.containsKey(posCpg.getVpg().toLowerCase())){
                posCpg.setVpg(cpgMap.get(posCpg.getVpg().toLowerCase()));
            }else {
                log.info("cpgCode 不存在 : "+posCpg.getVpg());
                continue;
            }



            //输出mysql
            String insertQuery = "INSERT INTO gv_pos_cpg (pos_cpg_code, pos_code, cpg_code, status) VALUES ("
                    + "'" + posCpg.getPosCpgCode() + "', "
                    + "'" + posCpg.getMachineId() + "', "
                    + "'" + posCpg.getVpg() + "', "
                    + "'" + posCpg.getStatus() + "'" +
                    ");";

            /*Db.use().insert(Entity.create("gv_pos_cpg")
                    .set("pos_cpg_code", posCpg.getPosCpgCode())
                    .set("pos_code", posCpg.getMachineId())
                    .set("cpg_code", posCpg.getVpg())
                    .set("status", posCpg.getStatus())
            );*/

            sqlList.add(insertQuery);

            if (sqlList.size()>=500){
                String insertQueryList = transformToBatchInsert(sqlList);
                writer.write(insertQueryList);
                writer.newLine();
                sqlList.clear();
            }
        }
        if (sqlList.size() > 0){
            String insertQueryList = transformToBatchInsert(sqlList);
            writer.write(insertQueryList);
            writer.newLine();
            sqlList.clear();
        }
        writer.flush();
        System.out.println(i.incrementAndGet());


        //遍历行

        log.info("写入成功！");

    }





    public static String toString(String text, String defalutValue) {

        if (StringUtils.isEmpty(text)) {
            return defalutValue;
        }

        return text;
    }

    private  void userAccount() throws SQLException, IOException {

        ExcelReader read = ExcelUtil.getReader("csv.base/MD - User Submit_New Update.xlsx");
        List<UserAccount> userAccounts = read.readAll(UserAccount.class);

        String projectPath = System.getProperty("user.dir");
        String filePath = projectPath + "/SQL/src/main/resources/sqlFile/userAccount/userAccount.txt";
        FileWriter file = new java.io.FileWriter(filePath);
        BufferedWriter writer = null;
        try {
            writer = new BufferedWriter(file);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        List<String> sqlList = new ArrayList<>();
        //遍历行
        for (UserAccount user : userAccounts){
            user.setUserCode(user.getAccount());
            user.setPassword(ConvertUtils.toString(CryptoUtils.sha512Encrypt(user.getPassword(), MIX_SALT), null));

            user.setLastName(CryptoUtils.aesEncrypt(user.getLastName(), MIX_SALT));
            user.setFirstName(CryptoUtils.aesEncrypt(user.getFirstName(), MIX_SALT));

            user.setMobile(ConvertUtils.toString(CryptoUtils.aesEncrypt(ConvertUtils.toString(user.getMobile(), "").toLowerCase(), MIX_SALT), ""));
            user.setAccount(ConvertUtils.toString(CryptoUtils.aesEncrypt(ConvertUtils.toString(user.getAccount(), "").toLowerCase(), MIX_SALT), null));
            user.setEmail(ConvertUtils.toString(CryptoUtils.aesEncrypt(ConvertUtils.toString(user.getEmail(), "").toLowerCase(), MIX_SALT), null));



            //输出mysql语句
            String insertQuery  = "INSERT INTO idm_user_account (issuer_code, user_code, user_type, account, password, full_name, last_name, first_name, email, mobile, status) VALUES ("
                    + "'" + user.getIssuerCode() + "',"
                    + "'" + user.getUserCode() + "',"
                    + "'" + user.getUserType() + "',"
                    + "'" + user.getAccount() + "',"
                    + "'" + user.getPassword() + "',"
                    + "'" + user.getFullName() + "',"
                    + "'" + user.getLastName() + "',"
                    + "'" + user.getFirstName() + "',"
                    + "'" + user.getEmail() + "',"
                    + "'" + user.getMobile() + "',"
                    + "'" + user.getStatus() + "'"+
                    /*"',"
                    + "'" + user.getRole() + "'" +*/
                    ");";
            /*try {
                Db.use().insert(Entity.create("idm_user_account")
                        .set("domain_code", "SYSTEM_DEFAULT")
                        .set("tenant_code", "SYSTEM_DEFAULT")
                        .set("issuer_code", user.getIssuerCode())
                        .set("user_code", user.getUserCode())
                        .set("user_type", user.getUserType())
                        .set("account", user.getAccount())
                        .set("password", user.getPassword())
                        .set("full_name", user.getFullName())
                        .set("last_name", user.getLastName())
                        .set("first_name", user.getFirstName())
                        .set("email", user.getEmail())
                        .set("mobile", user.getMobile())
                        .set("status", user.getStatus())
                );
            } catch (SQLException e) {
                log.info("userAccount 插入失败"+e.getMessage());
            }*/

            //log.info(insertQuery);

            //sqlList.add(insertQuery);
            writer.write(insertQuery);
            writer.newLine();
           /* if (sqlList.size()>=500){
                String insertQueryList = transformToBatchInsert(sqlList);
                writer.write(insertQueryList);
                writer.newLine();
                sqlList.clear();
            }*/
        }
        /*if (sqlList.size() > 0){
            String insertQueryList = transformToBatchInsert(sqlList);
            writer.write(insertQueryList);
            writer.newLine();
            sqlList.clear();
        }*/
        writer.flush();


        //遍历行

        log.info("写入成功！");


    }


    private  void accountRole() throws IOException {

        ExcelReader read = ExcelUtil.getReader("csv.base/MD - User Submit_New Update.xlsx");
        List<UserAccount> userAccounts = read.readAll(UserAccount.class);

        List<Entity> idmRole = queryMysql("idm_role");
        //idmRole根据roleCode分组
        Map<String, List<Entity>> roleMap = idmRole.stream().collect(Collectors.groupingBy(x -> x.getStr("role_name").trim()));


//        FileWriter file = new java.io.FileWriter("D:\\project\\gvcore\\SQL\\src\\main\\resources\\sqlFile\\userAccount\\userAccountRole.txt");
        String projectPath = System.getProperty("user.dir");
        String filePath = projectPath + "/SQL/src/main/resources/sqlFile/userAccount/userAccountRole.txt";
        FileWriter file = new java.io.FileWriter(filePath);
        BufferedWriter writer = null;
        try {
            writer = new BufferedWriter(file);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        List<String> sqlList = new ArrayList<>();

        //遍历行
        for (UserAccount user : userAccounts){
            user.setUserCode(user.getAccount());
            String[] split = user.getRole().split(",");
            for (String role : split) {
                List<Entity> entities = roleMap.get(role.trim());
                if (CollectionUtils.isEmpty(entities)){
                    log.info("roleCode 不存在 : "+role);
                    continue;
                }
                for (Entity entity : entities) {
                    user.setRole(entity.getStr("role_code"));
                    //输出mysql语句
                    String insertQuery  = "INSERT INTO idm_user_role_mapping (domain_code,tenant_code,user_code, role_code,logic_delete) VALUES ("
                            + "'" + "SYSTEM_DEFAULT" + "',"
                            + "'" + "SYSTEM_DEFAULT" + "',"
                            + "'" + user.getUserCode() + "',"
                            + "'" + user.getRole() + "',"
                            + "'" + "0" + "'"+
                            ");";
                    //log.info(insertQuery);
                    writer.write(insertQuery);
                    writer.newLine();
                }
            }


        }


        writer.flush();


        //遍历行

        log.info("写入成功！");


    }





    private void pos() throws IOException, SQLException {

        ExcelReader read = ExcelUtil.getReader("csv.base/MD - POS-Terminal ID All.xlsx");
        List<Pos> posList = read.readAll(Pos.class);

        //判断是否增量数据
        List<Entity> gvPos = queryMysql("gv_pos");
        //去除掉重复的数据
        List<Pos> filterList = posList.stream().filter(x -> gvPos.stream().noneMatch(entity -> entity.getStr("pos_code").toLowerCase().equals(x.getMachineId().toLowerCase()))).collect(Collectors.toList());

        List<Entity> gvOutlet = queryMysql("gv_outlet");
        Map<String, String> outletMap = gvOutlet.stream().collect(Collectors.toMap(x -> x.getStr("business_outlet_code").toLowerCase(), x -> x.getStr("outlet_code")));


//        FileWriter file = new java.io.FileWriter("D:\\project\\gvcore\\SQL\\src\\main\\resources\\sqlFile\\pos\\pos.txt");


        String projectPath = System.getProperty("user.dir");
        String filePath = projectPath + "/SQL/src/main/resources/sqlFile/pos/pos.txt";
        FileWriter file = new java.io.FileWriter(filePath);

        BufferedWriter writer = null;
        try {
            writer = new BufferedWriter(file);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        //遍历行
        for (Pos pos : filterList){

            if (outletMap.containsKey(pos.getOutletCode().toLowerCase())){
                pos.setOutletCode(outletMap.get(pos.getOutletCode().toLowerCase()));
            }else {
                log.info("outletCode 不存在 : "+pos.getOutletCode());
            }


            //输出mysql语句
            String insertQuery  = "INSERT INTO gv_pos (pos_code, pos_name, machine_id, pos_entry_mode_Id" +
                    ", outlet_code, status, account, password, forwarding_entity_id, forwarding_entity_password) VALUES ("
                    + "'" + UUID.randomUUID().toString() + "',"
                    + "'" + pos.getPosName() + "',"
                    + "'" + pos.getMachineId() + "',"
                    + "'" + pos.getPosEntryModeId() + "',"
                    + "'" + pos.getOutletCode() + "',"
                    + "'" + pos.getStatus() + "',"
                    + "'" + pos.getAccount() + "',"
                    + "'" + pos.getPassword() + "',"
                    + "'" + pos.getForwardingEntityId() + "',"
                    + "'" + pos.getForwardingEntityPassword() + "'"+
                    ");";

            try {
                Db.use().insert(Entity.create("gv_pos")
                        .set("pos_code",UUID.randomUUID().toString())
                        .set("pos_name",pos.getPosName())
                        .set("machine_id",pos.getMachineId())
                        .set("pos_entry_mode_Id",pos.getPosEntryModeId())
                        .set("outlet_code",pos.getOutletCode())
                        .set("status",pos.getStatus())
                        .set("account",pos.getAccount())
                        .set("password",pos.getPassword())
                        .set("forwarding_entity_id",pos.getForwardingEntityId())
                        .set("forwarding_entity_password",pos.getForwardingEntityPassword())
                );
            } catch (SQLException e) {
                log.info("插入失败"+e.getMessage());
            }

            writer.write(insertQuery);
            writer.newLine();
        }
        writer.flush();
        log.info("写入成功！");
    }



    private static void booklet(String path) throws IOException {
        /*ExcelReader reader = ExcelUtil.getReader(path);
        List<Voucher> voucherList = reader.readAll(Voucher.class);*/

        CsvReader reader = CsvUtil.getReader();
        reader.setFieldSeparator(';');
        //读取resources下csv里的文件
        List<Voucher> voucherList = reader.read(ResourceUtil.getUtf8Reader(path), Voucher.class);
        if (CollectionUtils.isEmpty(voucherList)){
            reader.setFieldSeparator(',');
            //读取resources下csv里的文件
            voucherList = reader.read(ResourceUtil.getUtf8Reader(path), Voucher.class);
        }
        //根据批次分组
        Map<String, List<Voucher>> bookletMap = voucherList.stream().collect(Collectors.groupingBy(Voucher::getBookletCode));
        List<Entity> entitys = new ArrayList<>();

        // 创建并检查父目录是否存在，如果不存在则创建
        //String filePath = "D:\\project\\gvcore\\SQL\\src\\main\\resources\\sqlFile\\voucherBooklet\\"+path.replace(".csv",".txt");
        String projectPath = System.getProperty("user.dir");
        String filePath = projectPath + "/SQL/src/main/resources/sqlFile/voucherBooklet/"+path.replace(".csv",".txt");
        File newPath = new File(filePath);
        File parentDir = newPath.getParentFile();
        if (!parentDir.exists()) {
            boolean mkdirsSuccess = parentDir.mkdirs();
            if (!mkdirsSuccess) {
                System.out.println("Failed to create directories: " + parentDir.getAbsolutePath());
                // 可以选择在这里处理异常或返回错误
                return;
            }
        }
        FileWriter file = new java.io.FileWriter(newPath);

        BufferedWriter finalWriter = null;
        try {
            finalWriter = new BufferedWriter(file);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        BufferedWriter finalWriter1 = finalWriter;
        bookletMap.forEach((k, v)->{
            String poNo = UUID.fastUUID().toString();
            //输出sql语句
                String insertQuery  = "INSERT INTO gv_voucher_booklet ( issuer_code, booklet_code, booklet_barcode" +
                        ", voucher_batch_code,voucher_start_no, voucher_end_no, booklet_per_num,status,create_user) VALUES ("
                        + "'" + v.get(0).getIssuerCode() + "',"
                        + "'" + v.get(0).getBookletCode() + "',"
                        + "'" + v.get(0).getBookletBarcode() + "',"
                        + "'" + v.get(0).getVoucherBatchCode() + "',"
                        + "'" + v.get(0).getBookletVoucherStartNo() + "',"
                        + "'" + v.get(0).getBookletVoucherEndNo() + "',"
                        + "'" + v.get(0).getBookletPerNum() + "',"
                        + "'" + "sqlFile/1" + "',"
                        + "'" + "DataMigration" + "'"+
                        ");";

            try {
                finalWriter1.write(insertQuery);
                finalWriter1.newLine();

            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });
        finalWriter.flush();
    }



    private static void voucherBatch(String path) throws IOException {


        CsvReader reader = CsvUtil.getReader();
        reader.setFieldSeparator(';');
        //读取resources下csv里的文件
        List<Voucher> voucherList = reader.read(ResourceUtil.getUtf8Reader(path), Voucher.class);
        if (CollectionUtils.isEmpty(voucherList)){
            reader.setFieldSeparator(',');
            //读取resources下csv里的文件
            voucherList = reader.read(ResourceUtil.getUtf8Reader(path), Voucher.class);
        }


        //根据批次分组
        Map<String, List<Voucher>> batchMap = voucherList.stream().collect(Collectors.groupingBy(Voucher::getVoucherBatchCode));


        List<Entity> gvCpg = queryMysql("gv_cpg");
        Map<String, Entity> cpgMap = gvCpg.stream().collect(Collectors.toMap(x -> x.getStr("cpg_name").toLowerCase(), x -> x));
        List<Entity> gvArticleMop = queryMysql("gv_article_mop");
        Map<String, Entity> articleMopCode = gvArticleMop.stream().collect(Collectors.toMap(x -> x.getStr("article_mop_code").toLowerCase(), x -> x));
        List<Entity> entities = new ArrayList<>();

        // 创建并检查父目录是否存在，如果不存在则创建
        //String filePath = "D:\\project\\gvcore\\SQL\\src\\main\\resources\\sqlFile\\voucherBatch\\"+path.replace(".csv",".txt");

        String projectPath = System.getProperty("user.dir");
        String filePath = projectPath + "/SQL/src/main/resources/sqlFile/voucherBatch/"+path.replace(".csv",".txt");
        File newPath = new File(filePath);
        File parentDir = newPath.getParentFile();
        if (!parentDir.exists()) {
            boolean mkdirsSuccess = parentDir.mkdirs();
            if (!mkdirsSuccess) {
                System.out.println("Failed to create directories: " + parentDir.getAbsolutePath());
                // 可以选择在这里处理异常或返回错误
                return;
            }
        }
        FileWriter file = new java.io.FileWriter(newPath);





        BufferedWriter finalWriter = null;
        try {
            finalWriter = new BufferedWriter(file);
        } catch (Exception e) {
            throw new RuntimeException(e);

        }



        BufferedWriter finalWriter1 = finalWriter;
        batchMap.forEach((k, v)->{
            Entity entity = cpgMap.get(v.get(0).getCpgCode().toLowerCase());
            Entity articleCode = null;
            if (entity == null){
                log.info("cpgCode 不存在 : "+v.get(0).getCpgCode());
            }else {
                articleCode = articleMopCode.get(entity.getStr("article_mop_code"));
            }

            String poNo = UUID.fastUUID().toString();
            //输出sql语句
            String insertQuery  = "INSERT INTO gv_voucher_batch (purchase_order_no, issuer_code, voucher_batch_code, article_code" +
                    ", mop_code, booklet_start_no, booklet_end_no, booklet_per_num, booklet_num, voucher_start_no,voucher_end_no,voucher_num,denomination,voucher_effective_date,cpg_code,status,create_user) VALUES ("
                    + "'" + poNo + "',"
                    + "'" + v.get(0).getIssuerCode() + "',"
                    + "'" + v.get(0).getVoucherBatchCode() + "',"
                    + "'" + v.get(0).getArticleCodeName() + "',"
                    + "'" + (articleCode != null ? articleCode.getStr("mop_code") : "") + "',"
                    + "'" + v.get(0).getBookletStartNo() + "',"
                    + "'" + v.get(0).getBookletEndNo() + "',"
                    + "'" + v.get(0).getBookletPerNum() + "',"
                    + "'" + String.valueOf(countNum(v.get(0).getBookletStartNo(),v.get(0).getBookletEndNo())) + "',"
                    + "'" + v.get(0).getBatchVoucherStartNo() + "',"
                    + "'" + v.get(0).getBatchVoucherEndNo() + "',"
                    + "'" + String.valueOf(countNum(v.get(0).getBatchVoucherStartNo(),v.get(0).getBatchVoucherEndNo())) + "',"
                    + "'" + v.get(0).getDenomination() + "',"
                    + "'" + v.get(0).getVoucherEffectiveDate() + "',"
                    + "'" + v.get(0).getCpgCode() + "',"
                    + "'" + "sqlFile/1" + "',"
                    + "'" + "DataMigration" + "'" +
                    ");";
            /*entities.add(Entity.create("gv_voucher_batch")
                    .set("purchase_order_no",poNo)
                    .set("issuer_code",v.get(0).getIssuerCode())
                    .set("voucher_batch_code",v.get(0).getVoucherBatchCode())
                    .set("article_code",v.get(0).getArticleCodeName())
                    .set("mop_code",entity.getStr("mop_code"))
                    .set("booklet_start_no",v.get(0).getBookletStartNo())
                    .set("booklet_end_no",v.get(0).getBookletEndNo())
                    .set("booklet_per_num",v.get(0).getBookletPerNum())
                    .set("booklet_num",String.valueOf(countNum(v.get(0).getBookletStartNo(),v.get(0).getBookletEndNo())))
                    .set("voucher_start_no",v.get(0).getBatchVoucherStartNo())
                    .set("voucher_end_no",v.get(0).getBatchVoucherEndNo())
                    .set("voucher_num",String.valueOf(v.size()))
                    .set("denomination",v.get(0).getDenomination())
                    .set("voucher_effective_date",v.get(0).getVoucherEffectiveDate())
                    .set("cpg_code",v.get(0).getCpgCode())
                    .set("status","1")
                    .set("create_user","DataMigration")
            );

            if ( entities.size() == 1000){


                try {
                    Db.use().insert(entities);
                }catch (DuplicateKeyException e){
                    log.info("重复数据："+v.get(0).getVoucherBatchCode());
                }catch (SQLIntegrityConstraintViolationException e){
                    log.info("重复数据："+v.get(0).getVoucherBatchCode());
                }catch (SQLException e) {
                    throw new RuntimeException(e);
                }
                entities.clear();
            }*/
            try {
                finalWriter1.write(insertQuery);
                finalWriter1.newLine();

            } catch (IOException e) {
                throw new RuntimeException(e);
            }


        });
        finalWriter1.flush();

        /*if (CollectionUtils.isNotEmpty(entities)){
            try {
                Db.use().insert(entities);
            }catch (DuplicateKeyException e){
                log.info("重复数据："+entities.get(0).getStr("voucher_batch_code"));
            }catch (SQLIntegrityConstraintViolationException e){
                log.info("重复数据："+entities.get(0).getStr("voucher_batch_code"));
            }catch (SQLException e) {
                throw new RuntimeException(e);
            }finally {
                entities.clear();
            }

        }*/

    }




    private static void importDigitalVoucherBatch(String path) throws IOException {
        /*ExcelReader read = ExcelUtil.getReader("excel/Voucher Data EGV.xlsx");
        List<Voucher> voucherList = read.readAll(Voucher.class);*/

        CsvReader reader = CsvUtil.getReader();
        reader.setFieldSeparator(';');
        //读取resources下csv里的文件
        List<Voucher> voucherList = reader.read(ResourceUtil.getUtf8Reader(path), Voucher.class);
        if (CollectionUtils.isEmpty(voucherList)){
            reader.setFieldSeparator(',');
            //读取resources下csv里的文件
            voucherList = reader.read(ResourceUtil.getUtf8Reader(path), Voucher.class);
        }



        //打印出voucherBatch为空的数据
        List<Voucher> collect = voucherList.stream().filter(x -> StringUtils.isEmpty(x.getVoucherBatchCode())).collect(Collectors.toList());

        log.error("voucherBatch为空的数据："+collect.size());
        //log.error(JSON.toJSONString(collect));

        //根据批次分组
        Map<String, List<Voucher>> batchMap = voucherList.stream().filter(x->StringUtil.isNotEmpty(x.getVoucherBatchCode())).collect(Collectors.groupingBy(Voucher::getVoucherBatchCode));


        List<Entity> gvCpg = queryMysql("gv_cpg");
        Map<String, Entity> cpgMap = gvCpg.stream().collect(Collectors.toMap(x -> x.getStr("cpg_name").toLowerCase(), x -> x));
        List<Entity> gvArticleMop = queryMysql("gv_article_mop");
        Map<String, Entity> articleMopCode = gvArticleMop.stream().collect(Collectors.toMap(x -> x.getStr("article_mop_code").toLowerCase(), x -> x));
        // 创建并检查父目录是否存在，如果不存在则创建
        //String filePath = "D:\\project\\gvcore\\SQL\\src\\main\\resources\\sqlFile\\voucherBatch\\"+path.replace(".csv",".txt");
        String projectPath = System.getProperty("user.dir");
        String filePath = projectPath + "/SQL/src/main/resources/sqlFile/voucherBatch/"+path.replace(".csv",".txt");
        //File newPath = new File(filePath);
        File newPath = new File(filePath);
        File parentDir = newPath.getParentFile();
        if (!parentDir.exists()) {
            boolean mkdirsSuccess = parentDir.mkdirs();
            if (!mkdirsSuccess) {
                System.out.println("Failed to create directories: " + parentDir.getAbsolutePath());
                // 可以选择在这里处理异常或返回错误
                return;
            }
        }
        FileWriter file = new java.io.FileWriter(newPath);

        BufferedWriter finalWriter = null;
        try {
            finalWriter = new BufferedWriter(file);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        BufferedWriter finalWriter1 = finalWriter;

        batchMap.forEach((k,v)->{
            Entity articleCode = null;
            Entity entity = cpgMap.get(v.get(0).getCpgCode().toLowerCase());
            if (entity == null){
                log.info("cpgCode 不存在 : "+v.get(0).getCpgCode());
            }else {
                articleCode = articleMopCode.get(entity.getStr("article_mop_code"));
            }


            String poNo = UUID.fastUUID().toString();
            //输出sql语句
            String insertQuery  = "INSERT INTO gv_voucher_batch (purchase_order_no, issuer_code, voucher_batch_code, article_code" +
                    ", mop_code, booklet_start_no, booklet_end_no, booklet_per_num, booklet_num, voucher_start_no,voucher_end_no,voucher_num,denomination,voucher_effective_date,cpg_code,status) VALUES ("
                    + "'" + poNo + "',"
                    + "'" + v.get(0).getIssuerCode() + "',"
                    + "'" + v.get(0).getVoucherBatchCode() + "',"
                    + "'" + v.get(0).getArticleCodeName() + "',"
                    + "'" + (articleCode != null ? articleCode.getStr("mop_code") : "") + "',"
                    + "'" + v.get(0).getBookletStartNo() + "',"
                    + "'" + v.get(0).getBookletEndNo() + "',"
                    + "'" + v.get(0).getBookletPerNum() + "',"
                    //+ "'" + String.valueOf(countNum(v.get(0).getBookletStartNo(),v.get(0).getBookletEndNo())) + "',"
                    //+ "'" + v.get(0).getBatchVoucherStartNo() + "',"
                    //+ "'" + v.get(0).getBatchVoucherEndNo() + "',"
                    //+ "'" + String.valueOf(countNum(v.get(0).getBatchVoucherStartNo(),v.get(0).getBatchVoucherEndNo())) + "',"
                    + "'" + v.get(0).getDenomination() + "',"
                    + "'" + v.get(0).getVoucherEffectiveDate() + "',"
                    + "'" + v.get(0).getCpgCode() + "',"
                    + "'" + "1" + "',"
                    + "'" + "DataMigration" + "'"+
                    ");";

            /*try {
                Db.use().insert(Entity.create("gv_voucher_batch")
                        .set("purchase_order_no",poNo)
                        .set("issuer_code",v.get(0).getIssuerCode())
                        .set("voucher_batch_code",v.get(0).getVoucherBatchCode())
                        .set("article_code",v.get(0).getArticleCodeName())
                        .set("mop_code",entity.getStr("mop_code"))
                        //.set("booklet_start_no",v.get(0).getBookletStartNo())
                        //.set("booklet_end_no",v.get(0).getBookletEndNo())
                        //.set("booklet_per_num",v.get(0).getBookletPerNum())
                        //.set("booklet_num",String.valueOf(countNum(v.get(0).getBookletStartNo(),v.get(0).getBookletEndNo())))
                        //.set("voucher_start_no",v.get(0).getBatchVoucherStartNo())
                        //.set("voucher_end_no",v.get(0).getBatchVoucherEndNo())
                        .set("voucher_num",String.valueOf(v.size()))
                        .set("denomination",v.get(0).getDenomination())
                        .set("voucher_effective_date",v.get(0).getVoucherEffectiveDate())
                        .set("cpg_code",v.get(0).getCpgCode())
                        .set("status","1")
                );
            }catch (DuplicateKeyException e){
                log.info("重复数据："+v.get(0).getVoucherBatchCode());
            }catch (SQLIntegrityConstraintViolationException e){
                log.info("重复数据："+v.get(0).getVoucherBatchCode());
            }catch (SQLException e) {
                throw new RuntimeException(e);
            }*/
            //log.info(insertQuery);

            try {
                finalWriter1.write(insertQuery);
                finalWriter1.newLine();

            } catch (IOException e) {
                throw new RuntimeException(e);
            }


        });
        finalWriter1.flush();
    }




    private static void voucher(String path) throws IOException {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("voucher");
        /*ExcelReader read = ExcelUtil.getReader(path);
        List<Voucher> voucherList = read.readAll(Voucher.class);*/

        CsvReader reader = CsvUtil.getReader();
        //读取resources下csv里的文件
        /*List<Voucher> voucherList = reader.read(ResourceUtil.getUtf8Reader("csv/Voucher Data EGV.csv"), Voucher.class);

        CsvReader reader = CsvUtil.getReader();*/
        reader.setFieldSeparator(';');
        //读取resources下csv里的文件
        List<Voucher> voucherList = reader.read(ResourceUtil.getUtf8Reader(path), Voucher.class);
        if (CollectionUtils.isEmpty(voucherList)){
            reader.setFieldSeparator(',');
            //读取resources下csv里的文件
            voucherList = reader.read(ResourceUtil.getUtf8Reader(path), Voucher.class);
        }



        //去除掉vouchernumber为空的数据
        voucherList = voucherList.stream().filter(x -> !StringUtils.isEmpty(x.getVoucherNumber())).collect(Collectors.toList());


        List<Voucher> filterList = new ArrayList<>();
        List<Entity> gvVoucher = new CopyOnWriteArrayList<>();

        for (int i = 0; i < 64; i++) {
            //判断是否增量数据
            /*gvVoucher = queryMysql("gv_voucher_"+i);
            log.info("gv_voucher_"+i);*/
        }
        List<Entity> finalGvVoucher = gvVoucher;

        filterList.addAll( voucherList.stream().filter(x -> finalGvVoucher.stream().noneMatch(entity -> entity.getStr("voucher_code").equals(x.getVoucherNumber()))).collect(Collectors.toList()));

        List<Entity> gvOutlet = queryMysql("gv_outlet");
        Map<String, String> outletMap = gvOutlet.stream().collect(Collectors.toMap(x -> x.getStr("business_outlet_code").toLowerCase(), x -> x.getStr("outlet_code"),(x1, x2) -> x2));

        List<Entity> gvCustomer = queryMysql("gv_customer");
        Map<String, List<Entity>> customerByIssuerCode = gvCustomer.stream().collect(Collectors.groupingBy(x -> x.getStr("issuer_code"), Collectors.toList()));


        List<Entity> gvCpg = queryMysql("gv_cpg");
        Map<String, Entity> cpgMap = gvCpg.stream().collect(Collectors.toMap(x -> x.getStr("cpg_name").toLowerCase(), x -> x));

        List<Entity> gvArticleMop = queryMysql("gv_article_mop");
        Map<String, Entity> articleMopMap = gvArticleMop.stream().collect(Collectors.toMap(x -> x.getStr("article_mop_code").toLowerCase(), x -> x));



        // 创建并检查父目录是否存在，如果不存在则创建
        //String filePath = "D:\\project\\gvcore\\SQL\\src\\main\\resources\\sqlFile\\voucher\\"+path.replace(".csv",".txt");
        String projectPath = System.getProperty("user.dir");
        String filePath = projectPath + "/SQL/src/main/resources/sqlFile/voucher/"+path.replace(".csv",".txt");
        File newPath = new File(filePath);
        File parentDir = newPath.getParentFile();
        if (!parentDir.exists()) {
            boolean mkdirsSuccess = parentDir.mkdirs();
            if (!mkdirsSuccess) {
                System.out.println("Failed to create directories: " + parentDir.getAbsolutePath());
                // 可以选择在这里处理异常或返回错误
                return;
            }
        }
        FileWriter file = new java.io.FileWriter(newPath);

        BufferedWriter finalWriter = null;
        try {
            finalWriter = new BufferedWriter(file);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }


        //根据批次分组
        List<Entity> entitys = new ArrayList<>();
        for (Voucher v : filterList) {
            //去除字母
            String originalVoucherNumber = v.getVoucherNumber();
            v.setVoucherCode(originalVoucherNumber);
            v.setVoucherNumber(originalVoucherNumber.replaceAll("[a-zA-Z]",""));

            long index = Long.valueOf(v.getVoucherNumber())%64;
            Entity entity = cpgMap.get(v.getCpgCode().toLowerCase());
            if (null != entity){
                String articleMopCode = entity.getStr("article_mop_code");
                String mopCode = articleMopMap.get(articleMopCode).getStr("mop_code");
                if (cpgMap.containsKey(v.getCpgCode().toLowerCase())){
                    v.setCpgCode(cpgMap.get(v.getCpgCode().toLowerCase()).getStr("cpg_code"));
                    v.setMopCode(mopCode);
                }else {
                    log.info("cpgCode 不存在 : "+v.getCpgCode());
                }
            }else {
                log.info("cpgCode 不存在 : "+v.getCpgCode());
            }


            if (v.getVoucherOwnerType().equals("customer")|| v.getVoucherOwnerType().equals("Customer")){

                if(customerByIssuerCode.containsKey(v.getIssuerCode())){
                    List<Entity> entities = customerByIssuerCode.get(v.getIssuerCode());
                    Map<String, String> customerMap = new HashMap<>();
                    if (entities != null) {
                        customerMap = entities.stream()
                                .filter(x -> x.getStr("customer_name") != null && x.getStr("customer_code") != null)
                                .collect(Collectors.toMap(
                                        x -> x.getStr("customer_name").toLowerCase(),
                                        x -> x.getStr("customer_code"),
                                        (oldValue, newValue) -> oldValue
                                ));
                    }

                    Map<String, String> customerCompanyMap = new HashMap<>();
                    if (entities != null) {
                        customerCompanyMap = entities.stream()
                                .filter(x -> x.getStr("company_name") != null && x.getStr("customer_code") != null)
                                .collect(Collectors.toMap(
                                        x -> x.getStr("company_name").toLowerCase(),
                                        x -> x.getStr("customer_code"),
                                        (oldValue, newValue) -> oldValue
                                ));
                    }

                    if (customerMap.containsKey(v.getVoucherOwnerCode().toLowerCase())){
                        v.setVoucherOwnerCode(customerMap.get(v.getVoucherOwnerCode().toLowerCase()));
                    }else if (customerCompanyMap.containsKey(v.getVoucherOwnerCode().toLowerCase())){
                        v.setVoucherOwnerCode(customerCompanyMap.get(v.getVoucherOwnerCode().toLowerCase()));
                    }else {
                        log.info("customerCode 不存在 : "+v.getVoucherOwnerCode());
                    }


                }
            }else if (v.getVoucherOwnerType().equals("outlet") || v.getVoucherOwnerType().equals("Outlet")){

                if (v.getVoucherOwnerCode().equals("MV01")){
                    v.setVoucherOwnerCode("OU102205191659000007");
                }else if (v.getVoucherOwnerCode().equals("MV03")){
                    v.setVoucherOwnerCode("OU102208081300000029");
                } else if (v.getVoucherOwnerCode().equals("MV04")) {
                    v.setVoucherOwnerCode("OU102209061528000032");
                }else if (v.getVoucherOwnerCode().equals("HO01")) {
                    v.setVoucherOwnerCode("OU102205191658000006");
                    v.setVoucherOwnerType("warehouse");
                }else if (v.getVoucherOwnerCode().equals("WH01")) {
                    v.setVoucherOwnerCode("OU102205191658000005");
                    v.setVoucherOwnerType("warehouse");
                }else if (outletMap.containsKey(v.getVoucherOwnerCode().toLowerCase())){
                    v.setVoucherOwnerCode(outletMap.get(v.getVoucherOwnerCode().toLowerCase()));
                }else {
                    log.info("outletCode 不存在 : "+v.getVoucherOwnerCode());
                }
            }

            //输出sql语句
            String insertQuery  = "INSERT INTO gv_voucher_"+index+" ( issuer_code, voucher_batch_code, booklet_code,booklet_code_num, voucher_code,voucher_code_num" +
                    ",cpg_code, mop_code,denomination,voucher_effective_date,status,voucher_status,circulation_status,voucher_owner_code,voucher_owner_type,create_user) VALUES ("
                    + "'" + v.getIssuerCode() + "',"
                    + "'" + v.getVoucherBatchCode() + "',"
                    + "'" + v.getBookletCode() + "',"
                    + "'" + v.getBookletCode() + "',"
                    + "'" + v.getVoucherCode() + "',"
                    + "'" + v.getVoucherNumber() + "',"
                    + "'" + v.getCpgCode() + "',"
                    + "'" + v.getMopCode() + "',"
                    + "'" + v.getDenomination() + "',"
                    + "'" + v.getVoucherEffectiveDate() + "',"
                    + "'" + v.getStatus()  + "',"
                    + "'" + v.getVoucherStatus()  + "',"
                    + "'" + "3"  + "',"
                    + "'" + v.getVoucherOwnerCode()  + "',"
                    + "'" + v.getVoucherOwnerType()  + "',"
                    + "'" + "SYSTEM_IMPORT"  + "'"+
                    ");";

            /*entitys.add(Entity.create("gv_voucher_"+index)
                    .set("issuer_code",v.getIssuerCode())
                    .set("voucher_batch_code",v.getVoucherBatchCode())
                    .set("booklet_code",v.getBookletCode())
                    .set("booklet_code_num",v.getBookletCode())
                    .set("voucher_code",v.getVoucherNumber())
                    .set("voucher_code_num",v.getVoucherNumber())
                    .set("cpg_code",v.getCpgCode())
                    .set("mop_code",v.getMopCode())
                    .set("denomination",v.getDenomination())
                    .set("voucher_effective_date",v.getVoucherEffectiveDate())
                    .set("status",v.getStatus())
                    .set("voucher_status",v.getVoucherStatus())
                    .set("circulation_status","3")
                    .set("voucher_owner_code",v.getVoucherOwnerCode())
                    .set("voucher_owner_type",v.getVoucherOwnerType())
                    .set("create_user","DataMigration"));

            if (v.getVoucherNumber().equals(filterList.get(filterList.size()-1).getVoucherNumber()) ||entitys.size() == 1000  ){
                try {
                    Db.use().insert(entitys);

                }catch (DuplicateKeyException e){
                    log.info("重复数据："+v.getVoucherNumber());
                }catch (SQLIntegrityConstraintViolationException e){
                    log.info("重复数据："+v.getVoucherNumber());
                } catch (SQLException e) {
                    log.info("插入失败"+e.getMessage());
                }finally {
                    entitys.clear();
                }
            }*/
            try {
                finalWriter.write(insertQuery);
                finalWriter.newLine();

            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            //log.info(insertQuery);

        }
        finalWriter.flush();
        log.info("插入成功");
        stopWatch.stop();

    }






    private static void DigitalVoucher(String path) throws IOException {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("voucher");
        /*ExcelReader read = ExcelUtil.getReader("excel/Voucher Data EGV.xlsx");
        List<Voucher> voucherList = read.readAll(Voucher.class);*/

        /*CsvReader reader = CsvUtil.getReader();
        //读取resources下csv里的文件
        List<Voucher> voucherList = reader.read(ResourceUtil.getUtf8Reader("csv/Voucher Data EGV.csv"), Voucher.class);
*/

        CsvReader reader = CsvUtil.getReader();
        reader.setFieldSeparator(';');
        //读取resources下csv里的文件
        List<Voucher> voucherList = reader.read(ResourceUtil.getUtf8Reader(path), Voucher.class);
        if (CollectionUtils.isEmpty(voucherList)){
            reader.setFieldSeparator(',');
            //读取resources下csv里的文件
            voucherList = reader.read(ResourceUtil.getUtf8Reader(path), Voucher.class);
        }



        //去除掉vouchernumber为空的数据
        voucherList = voucherList.stream().filter(x -> !StringUtils.isEmpty(x.getVoucherNumber())).collect(Collectors.toList());


        List<Voucher> filterList = new ArrayList<>();
        List<Entity> gvVoucher = new CopyOnWriteArrayList<>();

        for (int i = 0; i < 64; i++) {
            //判断是否增量数据
            /*gvVoucher = queryMysql("gv_voucher_"+i);
            log.info("gv_voucher_"+i);*/
        }
        List<Entity> finalGvVoucher = gvVoucher;

        filterList.addAll( voucherList.stream().filter(x -> finalGvVoucher.stream().noneMatch(entity -> entity.getStr("voucher_code").equals(x.getVoucherNumber()))).collect(Collectors.toList()));

        List<Entity> gvOutlet = queryMysql("gv_outlet");
        Map<String, String> outletMap = gvOutlet.stream().collect(Collectors.toMap(x -> x.getStr("business_outlet_code").toLowerCase(), x -> x.getStr("outlet_code")));

        List<Entity> gvCustomer = queryMysql("gv_customer");
        Map<String, List<Entity>> customerByIssuerCode = gvCustomer.stream().collect(Collectors.groupingBy(x -> x.getStr("issuer_code"), Collectors.toList()));


        List<Entity> gvCpg = queryMysql("gv_cpg");
        Map<String, Entity> cpgMap = gvCpg.stream().collect(Collectors.toMap(x -> x.getStr("cpg_name").toLowerCase(), x -> x));

        List<Entity> gvArticleMop = queryMysql("gv_article_mop");
        Map<String, Entity> articleMopMap = gvArticleMop.stream().collect(Collectors.toMap(x -> x.getStr("article_mop_code").toLowerCase(), x -> x));


        // 创建并检查父目录是否存在，如果不存在则创建
        //String filePath = "D:\\project\\gvcore\\SQL\\src\\main\\resources\\sqlFile\\voucher\\"+path.replace(".csv",".txt");
        String projectPath = System.getProperty("user.dir");
        String filePath = projectPath + "/SQL/src/main/resources/sqlFile/voucher/"+path.replace(".csv",".txt");
        File newPath = new File(filePath);
        File parentDir = newPath.getParentFile();
        if (!parentDir.exists()) {
            boolean mkdirsSuccess = parentDir.mkdirs();
            if (!mkdirsSuccess) {
                System.out.println("Failed to create directories: " + parentDir.getAbsolutePath());
                // 可以选择在这里处理异常或返回错误
                return;
            }
        }
        FileWriter file = new java.io.FileWriter(newPath);




        BufferedWriter finalWriter = null;
        try {
            finalWriter = new BufferedWriter(file);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }


        //根据批次分组


        ArrayList<Entity> entitys = new ArrayList<>();
        for (Voucher v : filterList) {
            //去除字母
            String originalVoucherNumber = v.getVoucherNumber();
            v.setVoucherCode(originalVoucherNumber);
            v.setVoucherNumber(v.getVoucherNumber().replaceAll("[a-zA-Z]",""));

            long index = Long.valueOf(v.getVoucherNumber())%64;
            Entity entity = cpgMap.get(v.getCpgCode().toLowerCase());
            if (null != entity){
                String articleMopCode = entity.getStr("article_mop_code");
                String mopCode = articleMopMap.get(articleMopCode).getStr("mop_code");
                if (cpgMap.containsKey(v.getCpgCode().toLowerCase())){
                    v.setCpgCode(cpgMap.get(v.getCpgCode().toLowerCase()).getStr("cpg_code"));
                    v.setMopCode(mopCode);
                }else {
                    log.info("cpgCode 不存在 : "+v.getCpgCode());
                }
            }else {
                log.info("cpgCode 不存在 : "+v.getCpgCode());
            }


            if (v.getVoucherOwnerType().equals("customer") || v.getVoucherOwnerType().equals("Customer")){

                if(customerByIssuerCode.containsKey(v.getIssuerCode())){
                    List<Entity> entities = customerByIssuerCode.get(v.getIssuerCode());
                    Map<String, String> customerMap = new HashMap<>();
                    if (entities != null) {
                        customerMap = entities.stream()
                                .filter(x -> StringUtil.isNotEmpty(x.getStr("customer_name")) && StringUtil.isNotEmpty(x.getStr("customer_code")))
                                .collect(Collectors.toMap(
                                        x -> x.getStr("customer_name").toLowerCase(),
                                        x -> x.getStr("customer_code"),
                                        (oldValue, newValue) -> oldValue
                                ));
                    }

                    Map<String, String> customerCompanyMap = new HashMap<>();
                    if (entities != null) {
                        customerCompanyMap = entities.stream()
                                .filter(x -> StringUtil.isNotEmpty(x.getStr("company_name")) && StringUtil.isNotEmpty(x.getStr("customer_code")))
                                .collect(Collectors.toMap(
                                        x -> x.getStr("company_name").toLowerCase(),
                                        x -> x.getStr("customer_code"),
                                        (oldValue, newValue) -> oldValue
                                ));
                    }

                    if (customerMap.containsKey(v.getVoucherOwnerCode().toLowerCase())){
                        v.setVoucherOwnerCode(customerMap.get(v.getVoucherOwnerCode().toLowerCase()));
                    }else if (customerCompanyMap.containsKey(v.getVoucherOwnerCode().toLowerCase())){
                        v.setVoucherOwnerCode(customerCompanyMap.get(v.getVoucherOwnerCode().toLowerCase()));
                    }else {
                        log.info("customerCode 不存在 : "+v.getVoucherOwnerCode());
                    }


                }
            }else if (v.getVoucherOwnerType().equals("outlet") || v.getVoucherOwnerType().equals("Outlet")){
                if (outletMap.containsKey(v.getVoucherOwnerCode().toLowerCase())){
                    v.setVoucherOwnerCode(outletMap.get(v.getVoucherOwnerCode().toLowerCase()));
                }
            }

            //输出sql语句
            String insertQuery  = "INSERT INTO gv_voucher_"+index+" ( issuer_code, voucher_batch_code, booklet_code,booklet_code_num, voucher_code,voucher_code_num" +
                    ",cpg_code, mop_code,denomination,voucher_effective_date,status,voucher_status,circulation_status,voucher_owner_code,voucher_owner_type) VALUES ("
                    + "'" + v.getIssuerCode() + "',"
                    + "'" + v.getVoucherBatchCode() + "',"
                    + "'" + v.getBookletCode() + "',"
                    + "'" + v.getBookletCode() + "',"
                    + "'" + v.getVoucherCode() + "',"
                    + "'" + v.getVoucherNumber() + "',"
                    + "'" + v.getCpgCode() + "',"
                    + "'" + v.getMopCode() + "',"
                    + "'" + v.getDenomination() + "',"
                    + "'" + v.getVoucherEffectiveDate() + "',"
                    + "'" + v.getStatus()  + "',"
                    + "'" + v.getVoucherStatus()  + "',"
                    + "'" + "3"  + "',"
                    + "'" + v.getVoucherOwnerCode()  + "',"
                    + "'" + v.getVoucherOwnerType()  + "'"+
                    ");";

            /*entitys.add(Entity.create("gv_voucher_"+index)
                    .set("issuer_code",v.getIssuerCode())
                    .set("voucher_batch_code",v.getVoucherBatchCode())
                    *//*.set("booklet_code",v.getBookletCode())
                    .set("booklet_code_num",v.getBookletCode())*//*
                    .set("voucher_code",v.getVoucherNumber())
//                    .set("voucher_code_num",v.getVoucherNumber())
                    .set("cpg_code",v.getCpgCode())
                    .set("mop_code",v.getMopCode())
                    .set("denomination",v.getDenomination())
                    .set("voucher_effective_date",v.getVoucherEffectiveDate())
                    .set("status",v.getStatus())
                    .set("voucher_status",v.getVoucherStatus())
                    .set("circulation_status","3")
                    .set("voucher_owner_code",v.getVoucherOwnerCode())
                    .set("voucher_owner_type",v.getVoucherOwnerType())
                    .set("create_user","DataMigration"));

            if (v.getVoucherNumber().equals(filterList.get(filterList.size()-1).getVoucherNumber()) ||entitys.size() == 1000  ){
                try {
                    Db.use().insert(entitys);

                }catch (DuplicateKeyException e){
                    log.info("重复数据："+v.getVoucherNumber());
                }catch (SQLIntegrityConstraintViolationException e){
                    log.info("重复数据："+v.getVoucherNumber());
                } catch (SQLException e) {
                    log.info("插入失败"+e.getMessage());
                }finally {
                    entitys.clear();
                }
            }*/
            try {
                finalWriter.write(insertQuery);
                finalWriter.newLine();

            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            //log.info(insertQuery);

        }
        finalWriter.flush();
        log.info("插入成功");
        stopWatch.stop();

    }





    
    
    private List<String> checkMerchant(List<String> merchantList){
        List<Entity> read = queryMysql("gv_merchant");
        List<String> errorList = new ArrayList<>();
        //判断read中是否包含outletList中的所有元素
        for (String merchant : merchantList) {
            boolean flag = false;
            for (Entity entity : read) {
                if (merchant.equals(entity.get("merchant_name"))) {
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                log.info("merchantList中的元素在db中不存在-------- " + merchant);
                errorList.add(merchant);
            }
        }

        return errorList;
    }


    private List<String> checkCpg(List<String> cpgList){
        List<Entity> read = queryMysql("gv_cpg");
        //判断read中是否包含outletList中的所有元素
        ArrayList<String> errorList = new ArrayList<>();
        for (String cpg : cpgList) {
            boolean flag = false;
            for (Entity entity : read) {
                if (cpg.equals(entity.get("cpg_name"))) {
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                errorList.add(cpg);
                //return false;
            }
        }
        return errorList;
    }


    private List<String> checkPos(List<String> posList){
        List<Entity> read = queryMysql("gv_pos");
        //判断read中是否包含outletList中的所有元素
        ArrayList<String> errorList = new ArrayList<>();
        for (String pos : posList) {
            boolean flag = false;
            for (Entity entity : read) {
                if (pos.equals(entity.get("machine_id"))) {
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                errorList.add(pos);
                //return false;
            }
        }
        return errorList;
    }

    
    
    private List<String> checkOutlet(List<String> outletList){
        List<Entity> read = queryMysql("gv_outlet");

        ArrayList<String> errorList = new ArrayList<>();
        //判断companyList中的元素是否在read中都存在,并打印具体哪个不存在
        for (String outlet : outletList) {
            boolean flag = false;
            for (Entity entity : read) {
                if (outlet.equals(entity.get("outlet_name"))) {
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                errorList.add(outlet);

                //return false;
            }
        }
        return errorList;
    }
    
    
    private List<String> checkCompany(List<String> companyList){

        ArrayList<String> errorList = new ArrayList<>();
        List<Entity> read = queryMysql("gv_company");

        //判断companyList中的元素是否在read中都存在,并打印具体哪个不存在
        for (String company : companyList) {
            boolean flag = false;
            for (Entity entity : read) {
                if (company.equals(entity.get("company_name"))) {
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                errorList.add(company);
            }
        }

        return errorList;
    }

    public static List<Entity> queryMysql(String sql) {
        List<Entity> query = null;
        try {
            query = Db.use().findAll(sql);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return query;
    }





    //一个计算从起始值到结束值数量的方法
    private static int countNum(String start, String end){
        int count = 0;
        for (long i = Long.valueOf(start); i <= Long.valueOf(end); i++) {
            count++;
        }
        return count;
    }


    @Data
    public static class Voucher {
        @Alias("purchase_order_no")
        private String purchaseOrderNo;

        @Alias("issuer_code")
        private String issuerCode;

        private String mopCode;

        @Alias("voucher_batch_code")
        private String voucherBatchCode;

        @Alias("article_code_name")
        private String articleCodeName;

        @Alias("printer_name")
        private String printerName;

        @Alias("batch_voucher_start_no")
        private String batchVoucherStartNo;

        @Alias("batch_voucher_end_no")
        private String batchVoucherEndNo;

        @Alias("file_name")
        private String fileName;

        @Alias("file_format")
        private String fileFormat;

        @Alias("voucher_num_active")
        private int voucherNumActive;

        @Alias("voucher_num_used")
        private int voucherNumUsed;

        @Alias("voucher_printing_status")
        private String voucherPrintingStatus;

        @Alias("booklet_code")
        private String bookletCode;

        @Alias("booklet_barcode")
        private String bookletBarcode;

        @Alias("booklet_start_no")
        private String bookletStartNo;

        @Alias("booklet_end_no")
        private String bookletEndNo;

        @Alias("booklet_voucher_start_no")
        private String bookletVoucherStartNo;

        @Alias("booklet_voucher_end_no")
        private String bookletVoucherEndNo;

        @Alias("booklet_per_num")
        private int bookletPerNum;

        @Alias("voucher_generation_status")
        private String voucherGenerationStatus;

        @Alias("voucher_number")
        private String voucherNumber;

        @Alias("voucher_code")
        private String voucherCode;

        @Alias("cpg_code")
        private String cpgCode;

        @Alias("denomination")
        private double denomination;

        @Alias("voucher_pin")
        private String voucherPin;

        @Alias("voucher_barcode")
        private String voucherBarcode;

        @Alias("voucher_effective_date")
        private String voucherEffectiveDate;

        @Alias("voucher_status")
        private String voucherStatus;

        @Alias("status")
        private String status;

        @Alias("voucher_active_code")
        private String voucherActiveCode;

        @Alias("voucher_active_url")
        private String voucherActiveUrl;

        @Alias("voucher_owner_code")
        private String voucherOwnerCode;

        @Alias("voucher_owner_type")
        private String voucherOwnerType;


        public String getIssuerCode(){
            if (issuerCode.equals("Starbucks") || issuerCode.equals("STARBUCKS")){
                return "IS102205191134000001";
            }else {
                return issuerCode;
            }
        }


    }



    @Data
    public class PosCpg{
        private String posCpgCode;
        @Alias("machine_id")
        private String machineId;

        private String posCode;
        @Alias("cpg_code")
        private String vpg;

        private int status = 1;


    }


    @Data
    public class OutletCpg{
        private String outletCpgCode;

        @Alias("Outlet Code")
        private String outletCode;

        @Alias("VPG")
        private String vpg;

        private int status = 1;


    }
    
    @Data
    public class Outlet {
        @Alias("outlet_code")
        private String outletCode;

        @Alias("outlet_name")
        private String outletName;

        @Alias("merchant_code")
        private String merchantCode;

        @Alias("business_outlet_code")
        private String businessOutletCode;

        @Alias("outlet_type")
        private String outletType;

        @Alias("state_code")
        private String stateCode;

        @Alias("city_code")
        private String cityCode;

        @Alias("district_code")
        private String districtCode;

        @Alias("address1")
        private String address1;

        @Alias("address2")
        private String address2;

        @Alias("pin_code")
        private String pinCode;

        @Alias("first_name")
        private String firstName;

        @Alias("last_name")
        private String lastName;

        @Alias("email")
        private String email;

        @Alias("phone")
        private String phone;

        @Alias("mobile")
        private String mobile;

        @Alias("alertnate_email")
        private String alternateEmail;

        @Alias("alertnate_phone")
        private String alternatePhone;

        @Alias("descriptive")
        private String descriptive;

        @Alias("status")
        private int status = 1;

        @Alias("parent_outlet")
        private String parentOutlet;

    }


    @Data
    public class UserAccount {
        @Alias("issuer_code")
        private String issuerCode;

        private String userCode;

        @Alias("user_type")
        private String userType;

        @Alias("account")
        private String account;

        @Alias("password")
        private String password;

        @Alias("full_name")
        private String fullName;

        @Alias("last_name")
        private String lastName;

        @Alias("first_name")
        private String firstName;

        @Alias("email")
        private String email;

        @Alias("mobile")
        private String mobile;

        @Alias("status")
        private int status = 1;

        @Alias("role")
        private String role;
    }



    @Data
    public class Customer {
        @Alias("customer_code")
        private String customerCode;
        @Alias("issuer_code")
        private String issuerCode;
        @Alias("outlet_code")
        private String outletCode;

        @Alias("customer_name")
        private String customerName;

        @Alias("customer_type")
        private String customerType;

        @Alias("company_name")
        private String companyName;

        @Alias("contact_first_name")
        private String contactFirstName;

        @Alias("contact_last_name")
        private String contactLastName;

        @Alias("contact_division")
        private String contactDivision;

        @Alias("contact_phone")
        private String contactPhone;

        @Alias("contact_email")
        private String contactEmail;

        @Alias("shipping_address1")
        private String shippingAddress1;

        @Alias("shipping_address2")
        private String shippingAddress2;

        @Alias("note")
        private String note;

        @Alias("status")
        private String status;

        @Alias("beneficiary_name")
        private String beneficiaryName;

        @Alias("branch_name")
        private String branchName;

        @Alias("bank_name")
        private String bankName;

        @Alias("account_number")
        private String accountNumber;

        @Alias("pph")
        private String pph;

        @Alias("distribution_function")
        private String distributionFunction;

        @Alias("user_email")
        private String userEmail;

        @Alias("channel")
        private String channel;

        @Alias("registration_year")
        private int registrationYear;

        @Alias("means_of_Payment_name")
        private String meansOfPaymentName;

        @Alias("product_category_name")
        private String productCategoryName;

        // Constructors, getters, and setters are omitted for brevity
    }




    @Data
    @ToString
    class Merchant {
        @Alias("merchant_code")
        private String merchantCode;
        @Alias("merchant_name")
        private String merchantName;
        @Alias("issuer_code")
        private String issuerCode;
        @Alias("company_code")
        private String companyCode;
        @Alias("status")
        private String status;
        @Alias("remarks")
        private String remarks;
    }


    @Data
    public class Pos {

        @Alias("pos_code")
        private String posCode;

        @Alias("pos_name")
        private String posName;

        @Alias("machine_id")
        private String machineId;

        @Alias("pos_entry_mode_Id")
        private String posEntryModeId;

        @Alias("outlet_code")
        private String outletCode;

        @Alias("status")
        private String status;

        @Alias("account")
        private String account;

        @Alias("password")
        private String password;

        @Alias("forwarding_entity_id")
        private String forwardingEntityId;

        @Alias("forwarding_entity_password")
        private String forwardingEntityPassword;
    }

    @Data
    class Company {

        @Alias("company_code")
        private String companyCode;

        @Alias("company_name")
        private String companyName;

        @Alias("issuer_code")
        private String issuerCode;

        @Alias("sbu")
        private String sbu;

        @Alias("status")
        private String status;

        @Alias("remarks")
        private String remarks;

    }


}
