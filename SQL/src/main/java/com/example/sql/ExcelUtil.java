package com.example.sql;

import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;

import java.io.FileInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

public class ExcelUtil {

    public static List<List<String>> getExcel(String filePath, String sheetName) {
        List<List<String>> dataList = new ArrayList<>();//用来存放从Excel中读取的数据
        Workbook wb = null;
        try {
            InputStream is = new FileInputStream(filePath);//打开Excel文件
            wb = WorkbookFactory.create(is);//创建一个Workbook对象
            is.close();//关闭输入流
            if (wb != null) {
				Sheet sheet = wb.getSheetAt(0);// 根据sheet页的名字来获取sheet对象
                //Sheet sheet = wb.getSheetAt(0);//获取下标为0的sheet页
                int maxRownum = sheet.getLastRowNum();//获取的是最后一行的编号（编号从0开始）。
                //int maxRownum = sheet.getPhysicalNumberOfRows();//获取的是物理行数，也就是不包括那些空行（隔行）的情况。
                Row firstRow = sheet.getRow(0);//获取第一行的row对象
                int maxColnum = firstRow.getPhysicalNumberOfCells();//获取第一行的最大列的下标
                System.out.println(maxColnum);
                for (int i=0;i<=maxRownum;i++){//循环行
                    List<String> list = new ArrayList<>();//创建一个list用来存放每一行的值
                    String estr ="";//用来存放单元格的值
                    for (int j=0;j<maxColnum;j++){//循环列
                        if (sheet != null && sheet.getRow(i) != null && sheet.getRow(i).getCell(j) != null) {//判断单元格是否为空
                            sheet.getRow(i).getCell(j).setCellType(CellType.STRING);
                            estr = sheet.getRow(i).getCell(j).toString();//取出这个单元格的值
                        }else {
                            estr="";
                        }
                        //System.out.println(firstColumns+"---"+estr);
                        list.add(estr);//把单元格的值放到list里
                    }
                    dataList.add(list);//list添加到list里
                }
                //System.out.println(dataList);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dataList;//返回list
    }
}