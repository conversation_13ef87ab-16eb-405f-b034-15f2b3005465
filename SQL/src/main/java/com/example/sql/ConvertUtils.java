package com.example.sql;

import java.math.BigDecimal;

/**
 * 变量转换Utils
 */
public abstract class ConvertUtils {

    /**
     * toInteger
     *
     * @param text 数值文本
     * @param defalutValue 默认值
     * @return Integer
     * <AUTHOR>
     */
    public static Integer toInteger(String text, Integer defalutValue) {

        if (StringUtil.isBlank(text)) {
            return defalutValue;
        }

        return Integer.valueOf(text);
    }

    /**
     * toInteger
     *
     * @param obj 待转换对象
     * @return Integer
     * <AUTHOR>
     */
    public static Integer toInteger(Object obj) {

        return toInteger(obj, null);
    }

    /**
     * toInteger
     *
     * @param obj 待转换对象
     * @param defalutValue 默认值
     * @return Integer
     * <AUTHOR>
     */
    public static Integer toInteger(Object obj, Integer defaultValue) {

        if (obj == null) {
            return defaultValue;
        }

        return toInteger(String.valueOf(obj), defaultValue);
    }

    /**
     * toLong
     *
     * @param text 数值文本
     * @param defalutValue 默认值
     * @return Long
     * <AUTHOR>
     */
    public static Long toLong(String text, Long defalutValue) {

        if (StringUtil.isBlank(text)) {
            return defalutValue;
        }

        return Long.valueOf(text);
    }

    /**
     * toLong
     *
     * @param obj 待转换对象
     * @return Long
     * <AUTHOR>
     */
    public static Long toLong(Object obj) {

        return toLong(obj, null);
    }

    /**
     * toLong
     *
     * @param obj 待转换对象
     * @param defalutValue 默认值
     * @return Long
     * <AUTHOR>
     */
    public static Long toLong(Object obj, Long defaultValue) {

        if (obj == null) {
            return defaultValue;
        }

        return toLong(String.valueOf(obj), defaultValue);
    }

    /**
     * toDouble
     *
     * @param text 数值文本
     * @param defalutValue 默认值
     * @return Double
     * <AUTHOR>
     */
    public static Double toDouble(String text, Double defalutValue) {

        if (StringUtil.isBlank(text)) {
            return defalutValue;
        }

        return Double.valueOf(text);
    }

    /**
     * toDouble
     *
     * @param obj 待转换对象
     * @return Double
     * <AUTHOR>
     */
    public static Double toDouble(Object obj) {

        return toDouble(obj, null);
    }

    /**
     * toDouble
     *
     * @param obj 待转换对象
     * @param defalutValue 默认值
     * @return Double
     * <AUTHOR>
     */
    public static Double toDouble(Object obj, Double defaultValue) {

        if (obj == null) {
            return defaultValue;
        }

        return toDouble(String.valueOf(obj), defaultValue);
    }

    /**
     * toBigDecimal
     *
     * @param text 数值文本
     * @param defalutValue 默认值
     * @return BigDecimal
     * <AUTHOR>
     */
    public static BigDecimal toBigDecimal(String text, BigDecimal defalutValue) {

        if (StringUtil.isBlank(text)) {
            return defalutValue;
        }

        return BigDecimal.valueOf(Double.valueOf(text));
    }

    /**
     * toBigDecimal
     *
     * @param obj 待转换对象
     * @return BigDecimal
     * <AUTHOR>
     */
    public static BigDecimal toBigDecimal(Object obj) {

        return toBigDecimal(obj, null);
    }

    /**
     * toBigDecimal
     *
     * @param obj 待转换对象
     * @param defalutValue 默认值
     * @return BigDecimal
     * <AUTHOR>
     */
    public static BigDecimal toBigDecimal(Object obj, BigDecimal defaultValue) {

        if (obj == null) {
            return defaultValue;
        }

        return toBigDecimal(String.valueOf(obj), defaultValue);
    }

    /**
     * toString
     *
     * @param text 数值文本
     * @param defalutValue 默认值
     * @return String
     * <AUTHOR>
     */
    public static String toString(String text, String defalutValue) {

        if (StringUtil.isBlank(text)) {
            return defalutValue;
        }

        return text;
    }

    /**
     * toString
     *
     * @param obj 待转换对象
     * @return String
     * <AUTHOR>
     */
    public static String toString(Object obj) {

        return toString(obj, null);
    }

    /**
     * toString
     *
     * @param obj 待转换对象
     * @param defalutValue 默认值
     * @return String
     * <AUTHOR>
     */
    public static String toString(Object obj, String defaultValue) {

        if (obj == null) {
            return defaultValue;
        }

        return toString(String.valueOf(obj), defaultValue);
    }

    /**
     * toBoolean
     *
     * @param text 数值文本
     * @param defalutValue 默认值
     * @return Boolean
     */
    public static Boolean toBoolean(String text, Boolean defalutValue) {

        if (StringUtil.isBlank(text)) {
            return defalutValue;
        }

        return Boolean.valueOf(text);
    }

    /**
     * toBoolean
     *
     * @param obj 待转换对象
     * @return Boolean
     */
    public static Boolean toBoolean(Object obj) {

        return toBoolean(obj, null);
    }

    /**
     * toBoolean
     *
     * @param obj 待转换对象
     * @param defalutValue 默认值
     * @return Boolean
     */
    public static Boolean toBoolean(Object obj, Boolean defaultValue) {

        if (obj == null) {
            return defaultValue;
        }

        return toBoolean(String.valueOf(obj), defaultValue);
    }





}

