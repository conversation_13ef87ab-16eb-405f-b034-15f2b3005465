package com.example.sql;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.text.csv.*;
import com.sun.imageio.plugins.common.ReaderUtil;

import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class script {



    public static void main(String[] args) {

        String filePath = "C:\\Users\\<USER>\\Desktop\\第二次数据导入\\voucher.csv";

        count();


        //dis(filePath);
    }

    private static void dis(String filePath) {
        Map<String, Integer> voucherCountMap = new HashMap<>();
        int totalLines = 0;

        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            // Read and process each line in the CSV file
            String line;
            while ((line = reader.readLine()) != null) {
                // Assuming voucher_number is the first field separated by commas
                String[] fields = line.split(";");
                if (fields.length > 20) {
                    String voucherNumber = fields[20];
                    voucherCountMap.put(voucherNumber, voucherCountMap.getOrDefault(voucherNumber, 0) + 1);
                } else if (line.split(",").length > 20) {
                    String[] split = line.split(",");
                    String voucherNumber = split[20];
                    voucherCountMap.put(voucherNumber, voucherCountMap.getOrDefault(voucherNumber, 0) + 1);
                }else {

                    System.out.println("错误行"+ line);
                }
                totalLines++;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        // Print the total number of lines and count of duplicate voucher_numbers
        System.out.println("The CSV file has " + totalLines + " lines.");
        voucherCountMap.entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .forEach(entry -> System.out.println("Voucher number '" + entry.getKey() + "' appears " + entry.getValue() + " times."));
    }

    private static void count() {

        //读取路径下所有csv文件
        File file = new File("C:\\Users\\<USER>\\Desktop\\第二次数据导入");
        File[] files = file.listFiles();
        int allCount= 0;
        for (File file1 : files) {
            if (file1.getName().endsWith(".csv")) {
                //输出行数
                //读取resources下csv里的文件
                CsvReadConfig config = new CsvReadConfig();
                config.setHeaderLineNo(0);
                try (CsvReader reader = CsvUtil.getReader(config)) {
                    reader.setFieldSeparator(';');
                    ArrayList<csvToSql.Voucher> vouchers = new ArrayList<>();
                    CsvData read = reader.read(file1);
                    if (read.getHeader()==null){
                        continue;
                    }
                    //read转对象
                    List<CsvRow> rows = read.getRows();
                    if (rows.size()==0){
                        continue;
                    }
                    for (CsvRow row : rows) {
                        vouchers.add(row.toBean(csvToSql.Voucher.class));
                    }
                    allCount+= read.getRowCount();
                    System.out.println(file1.getName() + " 行数：" + read.getRowCount());
                    //根据cpg分组，打印每个cpg的行数
                    Map<String, List<csvToSql.Voucher>> byCpg = vouchers.stream().collect(Collectors.groupingBy(csvToSql.Voucher::getCpgCode));
                    byCpg.forEach((k, v) -> {
                        System.out.println(k + " 行数：" + v.size());
                    });

                    //根据过期时间分组，打印每个过期时间的行数
                    Map<String, List<csvToSql.Voucher>> byExpire = vouchers.stream().collect(Collectors.groupingBy(csvToSql.Voucher::getVoucherEffectiveDate));
                    byExpire.forEach((k, v) -> {
                        System.out.println(k + " 行数：" + v.size());
                    });




                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        System.out.println("总行数：" + allCount);
    }
}

