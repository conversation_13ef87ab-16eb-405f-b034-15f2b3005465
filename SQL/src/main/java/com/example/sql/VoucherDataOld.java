package com.example.sql;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
public class VoucherDataOld {
    static Map<String, Entity> cpgMap = new HashMap<>();
    static Map<String, Entity> articleMopCode = new HashMap<>();
    static Map<String, String> outletMap = new HashMap<>();
    static List<Entity> gvCpg = new ArrayList<>();
    static List<Entity> gvArticleMop = new ArrayList<>();
    static List<Entity> gvOutlet = new ArrayList<>();
    static List<Entity> gvCustomer = new ArrayList<>();

    static {
        gvCpg = queryMysql("gv_cpg");
        gvArticleMop = queryMysql("gv_article_mop");
        gvOutlet = queryMysql("gv_outlet");
        gvCustomer = queryMysql("gv_customer");

        cpgMap = gvCpg.stream().collect(Collectors.toMap(x -> x.getStr("cpg_name").toLowerCase(), x -> x));
        articleMopCode = gvArticleMop.stream().collect(Collectors.toMap(x -> x.getStr("article_mop_code").toLowerCase(), x -> x));
        outletMap = gvOutlet.stream().collect(Collectors.toMap(x -> x.getStr("business_outlet_code").toLowerCase(), x -> x.getStr("outlet_code"), (x1, x2) -> x2));
    }

    public static void main(String[] args) throws IOException, ExecutionException, InterruptedException {
        VoucherDataOld voucherData = new VoucherDataOld();

        log.info("开始执行M19");
        //M19
        voucherData.importM19PhysicalCoupon();

        log.info("开始执行电子券");
        //电子券
        voucherData.importDigitalVoucher();


        log.info("开始执行实体券");
        //实体券
        voucherData.importPhysicalCoupon();
    }



    public static List<Entity> queryMysql(String sql) {
        List<Entity> query = null;
        try {
            query = Db.use().findAll(sql);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return query;
    }

    //一个计算从起始值到结束值数量的方法
    private static int countNum(String start, String end){
        int count = 0;
        for (long i = Long.valueOf(start); i <= Long.valueOf(end); i++) {
            count++;
        }
        return count;
    }



    private void importM19PhysicalCoupon() throws IOException, ExecutionException, InterruptedException {




        //csv/voucher/M19
        //获取resources下csv/voucher/physical文件中所有文件的名字

        List<String> fileNameList = Lists.newArrayList(
                "7-Voucher Active Data for Paper Voucher M19 series-output.csv" ,
                "9-Voucher Active Data for Paper Voucher SOGO Gift Voucher-output.csv");
        ExecutorService executorService = Executors.newFixedThreadPool(fileNameList.size());
        int totalFiles = fileNameList.size();
        AtomicInteger completedFiles = new AtomicInteger();

        List<Future<?>> futures = new ArrayList<>();
        for (String file : fileNameList) {
            //Future<?> future = executorService.submit(() -> {
            try {
                log.info("开始处理{"+file+"}");
                String path = "csv/voucher/M19/" + file;
                this.importDigitalVoucherBatch(path);
                this.booklet(path);
                this.DigitalVoucher(path);
                completedFiles.incrementAndGet();
                int i = completedFiles.get();
                int totalFiles1 = totalFiles;
                log.info("已完成{"+i+"} / {"+totalFiles1+"} 个文件，当前文件: {"+file+"}");
            } catch (IOException e) {
                log.info( "处理文件 " + file + " 时发生IO异常", e);
            }
            //});

            //futures.add(future);
        }

        // 等待所有任务完成
        //for (Future<?> future : futures) {
        //future.get();
        //}

        //executorService.shutdown();

    }

    private void importDigitalVoucher() throws IOException, ExecutionException, InterruptedException {


        //csv/voucher/digital
        //获取resources下csv/voucher/physical文件中所有文件的名字
        List<String> fileNameList = Lists.newArrayList(


                "11-Voucher Active Data Digital- NOT in MAPCLUB-output.csv",
                "12-Voucher Active Data Digital- IN MAPCLUB-output.csv" ,
                "13-Voucher Active Data Digital-Barcode (Ultra Voucher)-output.csv" ,
                "14-Voucher Active Data for Starbucks Digital Voucher-output.csv" ,
                "15-Voucher Deactivated Data (Digital)-output.csv");
        ExecutorService executorService = Executors.newFixedThreadPool(fileNameList.size());
        int totalFiles = fileNameList.size();
        AtomicInteger completedFiles = new AtomicInteger();

        List<Future<?>> futures = new ArrayList<>();
        for (String file : fileNameList) {
            //Future<?> future = executorService.submit(() -> {
            try {

                log.info("开始处理{"+file+"}");
                String path = "csv/voucher/digital/" + file;
                this.importDigitalVoucherBatch(path);
                this.DigitalVoucher(path);
                completedFiles.incrementAndGet();
                int i = completedFiles.get();
                int totalFiles1 = totalFiles;
                log.info("已完成{"+i+"} / {"+totalFiles1+"} 个文件，当前文件: {"+file+"}");
            } catch (IOException e) {
                log.info( "处理文件 " + file + " 时发生IO异常", e);
            }
            //});

            //futures.add(future);
        }

        // 等待所有任务完成
        /*for (Future<?> future : futures) {
            future.get();
        }*/

        //executorService.shutdown();
    }


    private void importPhysicalCoupon() throws IOException, ExecutionException, InterruptedException {
        //csv/voucher/physical
        //获取resources下csv/voucher/physical文件中所有文件的名字

        List<String> fileNameList = Lists.newArrayList(
                //"1-Voucher Inventory Data which GV still at Printing Vendor.csv",
                "2-Voucher Inventory Data stock at WH01-output.csv",
                "3-Voucher Inventory Data stock at HO01-output.csv",
                "4-Voucher Inventory Data stock at MV01-output.csv",
                "5-Voucher Inventory Data stock at MV04-output.csv",
                "6-Voucher Inventory Data stock at MAP stores-output.csv",
                "8-Voucher Active Data for Paper Voucher QC Series-output.csv",
                "10-Voucher Deactivated Data (Paper)-output.csv");
        ExecutorService executorService = Executors.newFixedThreadPool(fileNameList.size());
        int totalFiles = fileNameList.size();
        AtomicInteger completedFiles = new AtomicInteger();

        List<Future<?>> futures = new ArrayList<>();
        for (String file : fileNameList) {
            //Future<?> future = executorService.submit(() -> {
            try {
                log.info("开始处理{"+file+"}");
                String path = "csv/voucher/physical/" + file;
                this.voucherBatch(path);
                this.booklet(path);
                this.voucher(path);

                completedFiles.incrementAndGet();
                int i = completedFiles.get();
                int totalFiles1 = totalFiles;

                log.info("已完成{"+i+"} / {"+totalFiles1+"} 个文件，当前文件: {"+file+"}");
            } catch (IOException e) {
                log.error("处理文件 " + file + " 时发生IO异常", e);
            }
            // });

            // futures.add(future);
        }

        // 等待所有任务完成
        /*for (Future<?> future : futures) {
            future.get();
        }*/

        //executorService.shutdown();
    }


    private static void importDigitalVoucherBatch(String path) throws IOException {


        CsvReader reader = CsvUtil.getReader();
        reader.setFieldSeparator(';');
        //读取resources下csv里的文件
        List<csvToSql.Voucher> voucherList = reader.read(ResourceUtil.getUtf8Reader(path), csvToSql.Voucher.class);
        if (CollectionUtils.isEmpty(voucherList)){
            reader.setFieldSeparator(',');
            //读取resources下csv里的文件
            voucherList = reader.read(ResourceUtil.getUtf8Reader(path), csvToSql.Voucher.class);
        }



        //打印出voucherBatch为空的数据
        List<csvToSql.Voucher> collect = voucherList.stream().filter(x -> StringUtils.isEmpty(x.getVoucherBatchCode())).collect(Collectors.toList());

        log.error("voucherBatch为空的数据："+collect.size());
        //log.error(JSON.toJSONString(collect));

        //根据批次分组
        Map<String, List<csvToSql.Voucher>> batchMap = voucherList.stream().filter(x->StringUtil.isNotEmpty(x.getVoucherBatchCode())).collect(Collectors.groupingBy(csvToSql.Voucher::getVoucherBatchCode));



        // 创建并检查父目录是否存在，如果不存在则创建
        //String filePath = "D:\\project\\gvcore\\SQL\\src\\main\\resources\\sqlFile\\voucherBatch\\"+path.replace(".csv",".txt");
        String projectPath = System.getProperty("user.dir");
        String filePath = projectPath + "/SQL/src/main/resources/sqlFile/voucherBatch/"+path.replace(".csv",".txt");
        //File newPath = new File(filePath);
        File newPath = new File(filePath);
        File parentDir = newPath.getParentFile();
        if (!parentDir.exists()) {
            boolean mkdirsSuccess = parentDir.mkdirs();
            if (!mkdirsSuccess) {
                System.out.println("Failed to create directories: " + parentDir.getAbsolutePath());
                // 可以选择在这里处理异常或返回错误
                return;
            }
        }
        FileWriter file = new java.io.FileWriter(newPath);

        BufferedWriter finalWriter = null;
        try {
            finalWriter = new BufferedWriter(file);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        BufferedWriter finalWriter1 = finalWriter;

        batchMap.forEach((k,v)->{
            Entity articleCode = null;
            Entity entity = cpgMap.get(v.get(0).getCpgCode().toLowerCase());
            if (entity == null){
                log.info("cpgCode 不存在 : "+v.get(0).getCpgCode());
            }else {
                articleCode = articleMopCode.get(entity.getStr("article_mop_code"));
            }


            String poNo = UUID.fastUUID().toString();
            //输出sql语句
            String insertQuery  = "INSERT INTO gv_voucher_batch (purchase_order_no, issuer_code, voucher_batch_code, article_code" +
                    ", mop_code, booklet_start_no, booklet_end_no, booklet_per_num, booklet_num, voucher_start_no,voucher_end_no,voucher_num,denomination,voucher_effective_date,cpg_code,status) VALUES ("
                    + "'" + poNo + "',"
                    + "'" + v.get(0).getIssuerCode() + "',"
                    + "'" + v.get(0).getVoucherBatchCode() + "',"
                    + "'" + v.get(0).getArticleCodeName() + "',"
                    + "'" + (articleCode != null ? articleCode.getStr("mop_code") : "") + "',"
                    + "'" + v.get(0).getBookletStartNo() + "',"
                    + "'" + v.get(0).getBookletEndNo() + "',"
                    + "'" + v.get(0).getBookletPerNum() + "',"
                    + "'" + v.get(0).getDenomination() + "',"
                    + "'" + v.get(0).getVoucherEffectiveDate() + "',"
                    + "'" + v.get(0).getCpgCode() + "',"
                    + "'" + "1" + "',"
                    + "'" + "DataMigration" + "'"+
                    ");";



            try {
                finalWriter1.write(insertQuery);
                finalWriter1.newLine();

            } catch (IOException e) {
                throw new RuntimeException(e);
            }


        });
        finalWriter1.flush();
    }





    private static void voucherBatch(String path) throws IOException {


        CsvReader reader = CsvUtil.getReader();
        reader.setFieldSeparator(';');
        //读取resources下csv里的文件
        List<csvToSql.Voucher> voucherList = reader.read(ResourceUtil.getUtf8Reader(path), csvToSql.Voucher.class);
        if (CollectionUtils.isEmpty(voucherList)){
            reader.setFieldSeparator(',');
            //读取resources下csv里的文件
            voucherList = reader.read(ResourceUtil.getUtf8Reader(path), csvToSql.Voucher.class);
        }


        //根据批次分组
        Map<String, List<csvToSql.Voucher>> batchMap = voucherList.stream().collect(Collectors.groupingBy(csvToSql.Voucher::getVoucherBatchCode));



        Map<String, Entity> cpgMap = gvCpg.stream().collect(Collectors.toMap(x -> x.getStr("cpg_name").toLowerCase(), x -> x));

        Map<String, Entity> articleMopCode = gvArticleMop.stream().collect(Collectors.toMap(x -> x.getStr("article_mop_code").toLowerCase(), x -> x));
        List<Entity> entities = new ArrayList<>();

        // 创建并检查父目录是否存在，如果不存在则创建
        //String filePath = "D:\\project\\gvcore\\SQL\\src\\main\\resources\\sqlFile\\voucherBatch\\"+path.replace(".csv",".txt");

        String projectPath = System.getProperty("user.dir");
        String filePath = projectPath + "/SQL/src/main/resources/sqlFile/voucherBatch/"+path.replace(".csv",".txt");
        File newPath = new File(filePath);
        File parentDir = newPath.getParentFile();
        if (!parentDir.exists()) {
            boolean mkdirsSuccess = parentDir.mkdirs();
            if (!mkdirsSuccess) {
                System.out.println("Failed to create directories: " + parentDir.getAbsolutePath());
                // 可以选择在这里处理异常或返回错误
                return;
            }
        }
        FileWriter file = new java.io.FileWriter(newPath);





        BufferedWriter finalWriter = null;
        try {
            finalWriter = new BufferedWriter(file);
        } catch (Exception e) {
            throw new RuntimeException(e);

        }



        BufferedWriter finalWriter1 = finalWriter;
        batchMap.forEach((k, v)->{
            Entity entity = cpgMap.get(v.get(0).getCpgCode().toLowerCase());
            Entity articleCode = null;
            if (entity == null){
                log.info("cpgCode 不存在 : "+v.get(0).getCpgCode());
            }else {
                articleCode = articleMopCode.get(entity.getStr("article_mop_code"));
            }

            String poNo = UUID.fastUUID().toString();
            //输出sql语句
            String insertQuery  = "INSERT INTO gv_voucher_batch (purchase_order_no, issuer_code, voucher_batch_code, article_code" +
                    ", mop_code, booklet_start_no, booklet_end_no, booklet_per_num, booklet_num, voucher_start_no,voucher_end_no,voucher_num,denomination,voucher_effective_date,cpg_code,status,create_user) VALUES ("
                    + "'" + poNo + "',"
                    + "'" + v.get(0).getIssuerCode() + "',"
                    + "'" + v.get(0).getVoucherBatchCode() + "',"
                    + "'" + v.get(0).getArticleCodeName() + "',"
                    + "'" + (articleCode != null ? articleCode.getStr("mop_code") : "") + "',"
                    + "'" + v.get(0).getBookletStartNo() + "',"
                    + "'" + v.get(0).getBookletEndNo() + "',"
                    + "'" + v.get(0).getBookletPerNum() + "',"
                    + "'" + String.valueOf(countNum(v.get(0).getBookletStartNo(),v.get(0).getBookletEndNo())) + "',"
                    + "'" + v.get(0).getBatchVoucherStartNo() + "',"
                    + "'" + v.get(0).getBatchVoucherEndNo() + "',"
                    + "'" + String.valueOf(countNum(v.get(0).getBatchVoucherStartNo(),v.get(0).getBatchVoucherEndNo())) + "',"
                    + "'" + v.get(0).getDenomination() + "',"
                    + "'" + v.get(0).getVoucherEffectiveDate() + "',"
                    + "'" + v.get(0).getCpgCode() + "',"
                    + "'" + "sqlFile/1" + "',"
                    + "'" + "DataMigration" + "'" +
                    ");";

            try {
                finalWriter1.write(insertQuery);
                finalWriter1.newLine();

            } catch (IOException e) {
                throw new RuntimeException(e);
            }


        });
        finalWriter1.flush();



    }

    private static void voucher(String path) throws IOException {


        CsvReader reader = CsvUtil.getReader();
        reader.setFieldSeparator(';');
        //读取resources下csv里的文件
        List<csvToSql.Voucher> voucherList = reader.read(ResourceUtil.getUtf8Reader(path), csvToSql.Voucher.class);
        if (CollectionUtils.isEmpty(voucherList)){
            reader.setFieldSeparator(',');
            //读取resources下csv里的文件
            voucherList = reader.read(ResourceUtil.getUtf8Reader(path), csvToSql.Voucher.class);
        }

        //去除掉vouchernumber为空的数据
        voucherList = voucherList.stream().filter(x -> !StringUtils.isEmpty(x.getVoucherNumber())).collect(Collectors.toList());

        List<csvToSql.Voucher> filterList = new ArrayList<>();
        List<Entity> gvVoucher = new CopyOnWriteArrayList<>();

        List<Entity> finalGvVoucher = gvVoucher;

        filterList.addAll( voucherList.stream().filter(x -> finalGvVoucher.stream().noneMatch(entity -> entity.getStr("voucher_code").equals(x.getVoucherNumber()))).collect(Collectors.toList()));




        Map<String, List<Entity>> customerByIssuerCode = gvCustomer.stream().collect(Collectors.groupingBy(x -> x.getStr("issuer_code"), Collectors.toList()));


        Map<String, Entity> cpgMap = gvCpg.stream().collect(Collectors.toMap(x -> x.getStr("cpg_name").toLowerCase(), x -> x));


        Map<String, Entity> articleMopMap = gvArticleMop.stream().collect(Collectors.toMap(x -> x.getStr("article_mop_code").toLowerCase(), x -> x));



        // 创建并检查父目录是否存在，如果不存在则创建
        String projectPath = System.getProperty("user.dir");
        String filePath = projectPath + "/SQL/src/main/resources/sqlFile/voucher/"+path.replace(".csv",".txt");
        File newPath = new File(filePath);
        File parentDir = newPath.getParentFile();
        if (!parentDir.exists()) {
            boolean mkdirsSuccess = parentDir.mkdirs();
            if (!mkdirsSuccess) {
                System.out.println("Failed to create directories: " + parentDir.getAbsolutePath());
                // 可以选择在这里处理异常或返回错误
                return;
            }
        }
        FileWriter file = new java.io.FileWriter(newPath);

        BufferedWriter finalWriter = null;
        try {
            finalWriter = new BufferedWriter(file);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }


        //根据批次分组
        List<Entity> entitys = new ArrayList<>();
        for (csvToSql.Voucher v : filterList) {
            //去除字母
            String originalVoucherNumber = v.getVoucherNumber();
            v.setVoucherCode(originalVoucherNumber);
            v.setVoucherNumber(originalVoucherNumber.replaceAll("[a-zA-Z]",""));

            long index = Long.valueOf(v.getVoucherNumber())%64;
            Entity entity = cpgMap.get(v.getCpgCode().toLowerCase());
            if (null != entity){
                String articleMopCode = entity.getStr("article_mop_code");
                String mopCode = articleMopMap.get(articleMopCode).getStr("mop_code");
                if (cpgMap.containsKey(v.getCpgCode().toLowerCase())){
                    v.setCpgCode(cpgMap.get(v.getCpgCode().toLowerCase()).getStr("cpg_code"));
                    v.setMopCode(mopCode);
                }else {
                    log.info("cpgCode 不存在 : "+v.getCpgCode());
                }
            }else {
                log.info("cpgCode 不存在 : "+v.getCpgCode());
            }


            if (v.getVoucherOwnerType().equals("customer")|| v.getVoucherOwnerType().equals("Customer")){

                if(customerByIssuerCode.containsKey(v.getIssuerCode())){
                    List<Entity> entities = customerByIssuerCode.get(v.getIssuerCode());
                    Map<String, String> customerMap = new HashMap<>();
                    if (entities != null) {
                        customerMap = entities.stream()
                                .filter(x -> x.getStr("customer_name") != null && x.getStr("customer_code") != null)
                                .collect(Collectors.toMap(
                                        x -> x.getStr("customer_name").toLowerCase(),
                                        x -> x.getStr("customer_code"),
                                        (oldValue, newValue) -> oldValue
                                ));
                    }

                    Map<String, String> customerCompanyMap = new HashMap<>();
                    if (entities != null) {
                        customerCompanyMap = entities.stream()
                                .filter(x -> x.getStr("company_name") != null && x.getStr("customer_code") != null)
                                .collect(Collectors.toMap(
                                        x -> x.getStr("company_name").toLowerCase(),
                                        x -> x.getStr("customer_code"),
                                        (oldValue, newValue) -> oldValue
                                ));
                    }

                    if (customerMap.containsKey(v.getVoucherOwnerCode().toLowerCase())){
                        v.setVoucherOwnerCode(customerMap.get(v.getVoucherOwnerCode().toLowerCase()));
                    }else if (customerCompanyMap.containsKey(v.getVoucherOwnerCode().toLowerCase())){
                        v.setVoucherOwnerCode(customerCompanyMap.get(v.getVoucherOwnerCode().toLowerCase()));
                    }else {
                        log.info("customerCode 不存在 : "+v.getVoucherOwnerCode());
                    }


                }
            }else if (v.getVoucherOwnerType().equals("outlet") || v.getVoucherOwnerType().equals("Outlet")){

                if (v.getVoucherOwnerCode().equals("MV01")){
                    v.setVoucherOwnerCode("OU102205191659000007");
                }else if (v.getVoucherOwnerCode().equals("MV03")){
                    v.setVoucherOwnerCode("OU102208081300000029");
                } else if (v.getVoucherOwnerCode().equals("MV04")) {
                    v.setVoucherOwnerCode("OU102209061528000032");
                }else if (v.getVoucherOwnerCode().equals("HO01")) {
                    v.setVoucherOwnerCode("OU102205191658000006");
                    v.setVoucherOwnerType("warehouse");
                }else if (v.getVoucherOwnerCode().equals("WH01")) {
                    v.setVoucherOwnerCode("OU102205191658000005");
                    v.setVoucherOwnerType("warehouse");
                }else if (outletMap.containsKey(v.getVoucherOwnerCode().toLowerCase())){
                    v.setVoucherOwnerCode(outletMap.get(v.getVoucherOwnerCode().toLowerCase()));
                }else {
                    log.info("outletCode 不存在 : "+v.getVoucherOwnerCode());
                }
            }

            //输出sql语句
            String insertQuery  = "INSERT INTO gv_voucher_"+index+" ( issuer_code, voucher_batch_code, booklet_code,booklet_code_num, voucher_code,voucher_code_num" +
                    ",cpg_code, mop_code,denomination,voucher_effective_date,status,voucher_status,circulation_status,voucher_owner_code,voucher_owner_type,create_user) VALUES ("
                    + "'" + v.getIssuerCode() + "',"
                    + "'" + v.getVoucherBatchCode() + "',"
                    + "'" + v.getBookletCode() + "',"
                    + "'" + v.getBookletCode() + "',"
                    + "'" + v.getVoucherCode() + "',"
                    + "'" + v.getVoucherNumber() + "',"
                    + "'" + v.getCpgCode() + "',"
                    + "'" + v.getMopCode() + "',"
                    + "'" + v.getDenomination() + "',"
                    + "'" + v.getVoucherEffectiveDate() + "',"
                    + "'" + v.getStatus()  + "',"
                    + "'" + v.getVoucherStatus()  + "',"
                    + "'" + "3"  + "',"
                    + "'" + v.getVoucherOwnerCode()  + "',"
                    + "'" + v.getVoucherOwnerType()  + "',"
                    + "'" + "SYSTEM_IMPORT"  + "'"+
                    ");";


            try {
                finalWriter.write(insertQuery);
                finalWriter.newLine();

            } catch (IOException e) {
                throw new RuntimeException(e);
            }


        }
        finalWriter.flush();
        log.info("插入成功");


    }

    private static void DigitalVoucher(String path) throws IOException {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("voucher");
        /*ExcelReader read = ExcelUtil.getReader("excel/Voucher Data EGV.xlsx");
        List<Voucher> voucherList = read.readAll(Voucher.class);*/

        /*CsvReader reader = CsvUtil.getReader();
        //读取resources下csv里的文件
        List<Voucher> voucherList = reader.read(ResourceUtil.getUtf8Reader("csv/Voucher Data EGV.csv"), Voucher.class);
*/

        CsvReader reader = CsvUtil.getReader();
        reader.setFieldSeparator(';');
        //读取resources下csv里的文件
        List<csvToSql.Voucher> voucherList = reader.read(ResourceUtil.getUtf8Reader(path), csvToSql.Voucher.class);
        if (CollectionUtils.isEmpty(voucherList)){
            reader.setFieldSeparator(',');
            //读取resources下csv里的文件
            voucherList = reader.read(ResourceUtil.getUtf8Reader(path), csvToSql.Voucher.class);
        }



        //去除掉vouchernumber为空的数据
        voucherList = voucherList.stream().filter(x -> !StringUtils.isEmpty(x.getVoucherNumber())).collect(Collectors.toList());


        List<csvToSql.Voucher> filterList = new ArrayList<>();
        List<Entity> gvVoucher = new CopyOnWriteArrayList<>();

        for (int i = 0; i < 64; i++) {
            //判断是否增量数据
            /*gvVoucher = queryMysql("gv_voucher_"+i);
            log.info("gv_voucher_"+i);*/
        }
        List<Entity> finalGvVoucher = gvVoucher;

        filterList.addAll( voucherList.stream().filter(x -> finalGvVoucher.stream().noneMatch(entity -> entity.getStr("voucher_code").equals(x.getVoucherNumber()))).collect(Collectors.toList()));


        Map<String, String> outletMap = gvOutlet.stream().collect(Collectors.toMap(x -> x.getStr("business_outlet_code").toLowerCase(), x -> x.getStr("outlet_code")));

        Map<String, List<Entity>> customerByIssuerCode = gvCustomer.stream().collect(Collectors.groupingBy(x -> x.getStr("issuer_code"), Collectors.toList()));



        Map<String, Entity> cpgMap = gvCpg.stream().collect(Collectors.toMap(x -> x.getStr("cpg_name").toLowerCase(), x -> x));


        Map<String, Entity> articleMopMap = gvArticleMop.stream().collect(Collectors.toMap(x -> x.getStr("article_mop_code").toLowerCase(), x -> x));


        // 创建并检查父目录是否存在，如果不存在则创建
        //String filePath = "D:\\project\\gvcore\\SQL\\src\\main\\resources\\sqlFile\\voucher\\"+path.replace(".csv",".txt");
        String projectPath = System.getProperty("user.dir");
        String filePath = projectPath + "/SQL/src/main/resources/sqlFile/voucher/"+path.replace(".csv",".txt");
        File newPath = new File(filePath);
        File parentDir = newPath.getParentFile();
        if (!parentDir.exists()) {
            boolean mkdirsSuccess = parentDir.mkdirs();
            if (!mkdirsSuccess) {
                System.out.println("Failed to create directories: " + parentDir.getAbsolutePath());
                // 可以选择在这里处理异常或返回错误
                return;
            }
        }
        FileWriter file = new java.io.FileWriter(newPath);




        BufferedWriter finalWriter = null;
        try {
            finalWriter = new BufferedWriter(file);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }


        //根据批次分组


        ArrayList<Entity> entitys = new ArrayList<>();
        for (csvToSql.Voucher v : filterList) {
            //去除字母
            String originalVoucherNumber = v.getVoucherNumber();
            v.setVoucherCode(originalVoucherNumber);
            v.setVoucherNumber(v.getVoucherNumber().replaceAll("[a-zA-Z]",""));

            long index = Long.valueOf(v.getVoucherNumber())%64;
            Entity entity = cpgMap.get(v.getCpgCode().toLowerCase());
            if (null != entity){
                String articleMopCode = entity.getStr("article_mop_code");
                String mopCode = articleMopMap.get(articleMopCode).getStr("mop_code");
                if (cpgMap.containsKey(v.getCpgCode().toLowerCase())){
                    v.setCpgCode(cpgMap.get(v.getCpgCode().toLowerCase()).getStr("cpg_code"));
                    v.setMopCode(mopCode);
                }else {
                    log.info("cpgCode 不存在 : "+v.getCpgCode());
                }
            }else {
                log.info("cpgCode 不存在 : "+v.getCpgCode());
            }


            if (v.getVoucherOwnerType().equals("customer") || v.getVoucherOwnerType().equals("Customer")){

                if(customerByIssuerCode.containsKey(v.getIssuerCode())){
                    List<Entity> entities = customerByIssuerCode.get(v.getIssuerCode());
                    Map<String, String> customerMap = new HashMap<>();
                    if (entities != null) {
                        customerMap = entities.stream()
                                .filter(x -> StringUtil.isNotEmpty(x.getStr("customer_name")) && StringUtil.isNotEmpty(x.getStr("customer_code")))
                                .collect(Collectors.toMap(
                                        x -> x.getStr("customer_name").toLowerCase(),
                                        x -> x.getStr("customer_code"),
                                        (oldValue, newValue) -> oldValue
                                ));
                    }

                    Map<String, String> customerCompanyMap = new HashMap<>();
                    if (entities != null) {
                        customerCompanyMap = entities.stream()
                                .filter(x -> StringUtil.isNotEmpty(x.getStr("company_name")) && StringUtil.isNotEmpty(x.getStr("customer_code")))
                                .collect(Collectors.toMap(
                                        x -> x.getStr("company_name").toLowerCase(),
                                        x -> x.getStr("customer_code"),
                                        (oldValue, newValue) -> oldValue
                                ));
                    }

                    if (customerMap.containsKey(v.getVoucherOwnerCode().toLowerCase())){
                        v.setVoucherOwnerCode(customerMap.get(v.getVoucherOwnerCode().toLowerCase()));
                    }else if (customerCompanyMap.containsKey(v.getVoucherOwnerCode().toLowerCase())){
                        v.setVoucherOwnerCode(customerCompanyMap.get(v.getVoucherOwnerCode().toLowerCase()));
                    }else {
                        log.info("customerCode 不存在 : "+v.getVoucherOwnerCode());
                    }


                }
            }else if (v.getVoucherOwnerType().equals("outlet") || v.getVoucherOwnerType().equals("Outlet")){
                if (outletMap.containsKey(v.getVoucherOwnerCode().toLowerCase())){
                    v.setVoucherOwnerCode(outletMap.get(v.getVoucherOwnerCode().toLowerCase()));
                }
            }

            //输出sql语句
            String insertQuery  = "INSERT INTO gv_voucher_"+index+" ( issuer_code, voucher_batch_code, booklet_code,booklet_code_num, voucher_code,voucher_code_num" +
                    ",cpg_code, mop_code,denomination,voucher_effective_date,status,voucher_status,circulation_status,voucher_owner_code,voucher_owner_type) VALUES ("
                    + "'" + v.getIssuerCode() + "',"
                    + "'" + v.getVoucherBatchCode() + "',"
                    + "'" + v.getBookletCode() + "',"
                    + "'" + v.getBookletCode() + "',"
                    + "'" + v.getVoucherCode() + "',"
                    + "'" + v.getVoucherNumber() + "',"
                    + "'" + v.getCpgCode() + "',"
                    + "'" + v.getMopCode() + "',"
                    + "'" + v.getDenomination() + "',"
                    + "'" + v.getVoucherEffectiveDate() + "',"
                    + "'" + v.getStatus()  + "',"
                    + "'" + v.getVoucherStatus()  + "',"
                    + "'" + "3"  + "',"
                    + "'" + v.getVoucherOwnerCode()  + "',"
                    + "'" + v.getVoucherOwnerType()  + "'"+
                    ");";

            /*entitys.add(Entity.create("gv_voucher_"+index)
                    .set("issuer_code",v.getIssuerCode())
                    .set("voucher_batch_code",v.getVoucherBatchCode())
                    *//*.set("booklet_code",v.getBookletCode())
                    .set("booklet_code_num",v.getBookletCode())*//*
                    .set("voucher_code",v.getVoucherNumber())
//                    .set("voucher_code_num",v.getVoucherNumber())
                    .set("cpg_code",v.getCpgCode())
                    .set("mop_code",v.getMopCode())
                    .set("denomination",v.getDenomination())
                    .set("voucher_effective_date",v.getVoucherEffectiveDate())
                    .set("status",v.getStatus())
                    .set("voucher_status",v.getVoucherStatus())
                    .set("circulation_status","3")
                    .set("voucher_owner_code",v.getVoucherOwnerCode())
                    .set("voucher_owner_type",v.getVoucherOwnerType())
                    .set("create_user","DataMigration"));

            if (v.getVoucherNumber().equals(filterList.get(filterList.size()-1).getVoucherNumber()) ||entitys.size() == 1000  ){
                try {
                    Db.use().insert(entitys);

                }catch (DuplicateKeyException e){
                    log.info("重复数据："+v.getVoucherNumber());
                }catch (SQLIntegrityConstraintViolationException e){
                    log.info("重复数据："+v.getVoucherNumber());
                } catch (SQLException e) {
                    log.info("插入失败"+e.getMessage());
                }finally {
                    entitys.clear();
                }
            }*/
            try {
                finalWriter.write(insertQuery);
                finalWriter.newLine();

            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            //log.info(insertQuery);

        }
        finalWriter.flush();
        log.info("插入成功");
        stopWatch.stop();

    }



    private static void booklet(String path) throws IOException {
        /*ExcelReader reader = ExcelUtil.getReader(path);
        List<Voucher> voucherList = reader.readAll(Voucher.class);*/

        CsvReader reader = CsvUtil.getReader();
        reader.setFieldSeparator(';');
        //读取resources下csv里的文件
        List<csvToSql.Voucher> voucherList = reader.read(ResourceUtil.getUtf8Reader(path), csvToSql.Voucher.class);
        if (CollectionUtils.isEmpty(voucherList)){
            reader.setFieldSeparator(',');
            //读取resources下csv里的文件
            voucherList = reader.read(ResourceUtil.getUtf8Reader(path), csvToSql.Voucher.class);
        }
        //根据批次分组
        Map<String, List<csvToSql.Voucher>> bookletMap = voucherList.stream().collect(Collectors.groupingBy(csvToSql.Voucher::getBookletCode));
        List<Entity> entitys = new ArrayList<>();

        // 创建并检查父目录是否存在，如果不存在则创建
        //String filePath = "D:\\project\\gvcore\\SQL\\src\\main\\resources\\sqlFile\\voucherBooklet\\"+path.replace(".csv",".txt");
        String projectPath = System.getProperty("user.dir");
        String filePath = projectPath + "/SQL/src/main/resources/sqlFile/voucherBooklet/"+path.replace(".csv",".txt");
        File newPath = new File(filePath);
        File parentDir = newPath.getParentFile();
        if (!parentDir.exists()) {
            boolean mkdirsSuccess = parentDir.mkdirs();
            if (!mkdirsSuccess) {
                System.out.println("Failed to create directories: " + parentDir.getAbsolutePath());
                // 可以选择在这里处理异常或返回错误
                return;
            }
        }
        FileWriter file = new java.io.FileWriter(newPath);

        BufferedWriter finalWriter = null;
        try {
            finalWriter = new BufferedWriter(file);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        BufferedWriter finalWriter1 = finalWriter;
        bookletMap.forEach((k, v)->{
            String poNo = UUID.fastUUID().toString();
            //输出sql语句
            String insertQuery  = "INSERT INTO gv_voucher_booklet ( issuer_code, booklet_code, booklet_barcode" +
                    ", voucher_batch_code,voucher_start_no, voucher_end_no, booklet_per_num,status,create_user) VALUES ("
                    + "'" + v.get(0).getIssuerCode() + "',"
                    + "'" + v.get(0).getBookletCode() + "',"
                    + "'" + v.get(0).getBookletBarcode() + "',"
                    + "'" + v.get(0).getVoucherBatchCode() + "',"
                    + "'" + v.get(0).getBookletVoucherStartNo() + "',"
                    + "'" + v.get(0).getBookletVoucherEndNo() + "',"
                    + "'" + v.get(0).getBookletPerNum() + "',"
                    + "'" + "sqlFile/1" + "',"
                    + "'" + "DataMigration" + "'"+
                    ");";

            try {
                finalWriter1.write(insertQuery);
                finalWriter1.newLine();

            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });
        finalWriter.flush();
    }

}
