package com.example.sql;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @ClassName: FileOutputUtils
 * @Description:
 * @Author: keke
 * @Date: 2021/6/9
 */
public class FileOutputUtils {

    /**
     * <AUTHOR>
     * @Description 持久化字符串
     * @Date 2021/6/9
     * @Param [path, content]
     * @Return void
     **/
    public static void FileWrite(String path, String content) {

        FileOutputStream writer = null;
        OutputStreamWriter out = null;

        try {
            Date date = new Date();
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            String filePath = path + File.separator + format.format(date) + ".txt";
            File writeFile = new File(filePath);
            File parentFile = writeFile.getParentFile();
            if (!parentFile.exists()) {
                parentFile.mkdirs();
            }
            if (!writeFile.exists()) {
                writeFile.createNewFile();
            }
            writer = new FileOutputStream(writeFile, true);
            out = new OutputStreamWriter(writer, StandardCharsets.UTF_8);
			out.write(content + "\n");
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
			try {
				if (out != null) {
					out.close();
				}
				if (writer != null) {
					writer.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
        }
    }

}

