csvToSql 方法顺序执行

        log.info("开始执行Company");
        csvToSql.Company();插入数据库

        log.info("开始执行Merchant");
        csvToSql.Merchant();插入数据库

        log.info("开始执行Outlet");
        csvToSql.Outlet();插入数据库

        log.info("开始执行outletCpg");
        csvToSql.outletCpg();sql文件

        log.info("开始执行pos");
        csvToSql.pos();

        log.info("开始执行posCpg");
        csvToSql.posCpg();sql文件

        log.info("开始执行userAccount");
        csvToSql.userAccount();
        csvToSql.accountRole();

        log.info("开始执行Customer");
        csvToSql.Customer();


        电子券
        importDigitalVoucher();
        M19按电子券处理(没有包)
        importM19PhysicalCoupon();
        实体券
        importPhysicalCoupon();


datapermission Test3 替换用户权限数据